<template>
	<custom-header style="height: 88rpx;" title="任务列表" showBack />
	<view class="noplan" v-if="taskList==''">
		暂无任务
	</view>
	<view class="plan-list" v-else>
		<!-- 搜索框 -->
		<view class="search-box">
			<up-input v-model="searchText" placeholder="请输入任务名称" prefixIcon="search" :clearable="true"
				@change="handleSearchInput" @clear="handleClear" height="80rpx" />
		</view>

		<!-- 任务列表 -->
		<view class="task-list">
			<view v-for="(item, index) in taskList" :key="index" class="task-item" @click="handleSelect(item, index)">
				<view class="task-content">
					<view class="task-info">
						<view class="task_check">
							<view class="task-id">{{item.holeNumber}}</view>
							<!-- <up-icon
				   :name="selectedIndex === index ? 'checkmark-circle-fill' : 'checkmark-circle'"
				   :color="selectedIndex === index ? '#4080FF' : '#C8C9CC'"
				   size="40rpx"
				 /> -->
							<image style="width: 40rpx;height: 40rpx;"
								:src="selectedIndex === index? '/static/cricle_check_active.png':'/static/cricle_check.png'"
								mode=""></image>
						</view>

						<view class="task-details">
							<view class="device-id">设备名称：{{ item.drillName }}</view>
							<view class="create-time">创建时间：{{ item.createdAt }}</view>
						</view>
					</view>

				</view>
			</view>
		</view>

		<!-- 确定按钮 -->
		<view class="bottom-button">
			<button :disabled="selectedIndex === null" :class="{'button-disabled': selectedIndex === null}"
				@click="handleConfirm">确定</button>
		</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue';

	export default {
		components: {
			customHeader
		},
		data() {
			return {
				searchText: '',
				selectedIndex: 1,
				taskList: [],
				sourcePage: '' // 添加来源页面标识
			}
		},
		onShow() {
			// this.debounceSearch = this.debounce(() => {
			// 	this.getTaskList();
			// });
			this.getTaskList();
		},
		onLoad(options) {
			// 获取来源页面标识
			this.sourcePage = options.from || '';
		},
		methods: {
			handleSearchInput(e) {
				// 如果是清空操作，不处理（因为会由handleClear处理）
				if (e === '') return;
				
				// 清除之前的定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}

				// 设置新的定时器，延迟300ms执行搜索
				this.searchTimer = setTimeout(() => {
					this.searchText = e;
					this.taskList = [];
					this.getTaskList();
				}, 300);
			},

			// 处理清空
			handleClear() {
				// 清除可能存在的搜索定时器
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				
				this.searchText = '';
				this.taskList = [];
				this.getTaskList();
			},
			async getTaskList() {
				try {
					let taskdata = {
						keyWord: this.searchText.trim() // 确保去除首尾空格
					}
					const res = await Request.post('/drill/get_task', taskdata)
					if (res.status === 0 && res.data) {
						this.taskList = res.data;
						// 重置选中索引
						this.selectedIndex = 0;
					}
				} catch (error) {
					uni.showToast({
						title: '获取任务列表失败',
						icon: 'none'
					})
				}
			},
			handleSelect(item, index) {
				this.selectedIndex = index;
			},
			handleConfirm() {
				if (this.selectedIndex !== null) {
					const selectedTask = this.taskList[this.selectedIndex];
					const pages = getCurrentPages();
					const prevPage = pages[pages.length - 2];
					
					// 准备要传递的数据
					const taskData = {
						id: selectedTask.id,
						holeNumber: selectedTask.holeNumber,
						drillName: selectedTask.drillName,
						createdAt: selectedTask.createdAt
					};

					// 根据来源页面设置数据
					if (this.sourcePage === 'seal') {
						// 传递给seal页面
						prevPage.$vm.plan = taskData;
					} else if (this.sourcePage === 'shift') {
						// 传递给shift页面
						prevPage.$vm.plan = taskData;
					}

					this.$emit('confirm', selectedTask);
					uni.navigateBack();
				}
			}
		}
	}
</script>

<style scoped>
	.noplan{
		color: rgba(255,255,255,0.85);
		text-align: center;
		padding-top: 300rpx;
	}
	page {
		background: #16171b;
	}
   
	:deep(.uni-input-input) {
		color: rgba(255, 255, 255, 0.65);
	}

	:deep(.u-border) {
		border: none;
	}

	.plan-list {
		/* min-height: 100vh; */
		/* background-color: #000; */
		padding: 32rpx;
		padding-top: 188rpx;
		position: relative;
	}

	.search-box {
		/* padding: 20rpx; */
		background: rgba(255, 255, 255, 0.08);
		border-radius: 16rpx;
		/* border: 0.5px solid rgba(255, 255, 255, 0.0972); */
	}

	.task-list {
		margin-top: 20rpx;
	}

	.task-item {

		background: rgba(255, 255, 255, 0.04);
		border: 0.5px solid rgba(255, 255, 255, 0.0972);
		border-radius: 16rpx;
		padding: 32rpx;
		padding-bottom: 20rpx;
		margin-bottom: 28rpx;
	}

	.task-content {
		width: 100%;
		/* display: flex;
  justify-content: space-between;
  align-items: center; */
	}

	.task-info {
		/* display: flex; */
		/* gap: 20rpx; */
	}

	.task_check {
		width: 100%;
		display: flex;
		align-items: start;
		justify-content: space-between;
	}

	.task-id {
		color: rgba(255, 255, 255, 0.85);
		font-size: 32rpx;
		margin-bottom: 32rpx;
	}

	.task-details {
		color: rgba(255, 255, 255, 0.65);
		/* font-size: 28rpx;
  margin-bottom: 12rpx; */
	}

	.device-id,
	.create-time {
		color: rgba(255, 255, 255, 0.65);
		font-size: 28rpx;
		margin-bottom: 12rpx;
	}

	.bottom-button {
		position: fixed;
		bottom: 100rpx;
		left: 32rpx;
		right: 32rpx;
		/* background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%); */
	}

	.bottom-button button {
		color: rgba(255, 255, 255, 0.85);
		background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	}

	.button-disabled {
		opacity: 0.5;
		background: #666 !important;
	}
</style>