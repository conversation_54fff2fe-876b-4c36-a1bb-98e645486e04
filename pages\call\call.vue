<template>
  <view class="call-page">
    <!-- 来电UI -->
    <view class="incoming-call" v-if="showIncomingModal">
      <view class="incoming-header">
        <text class="incoming-title">来电呼叫</text>
        <view class="device-info">
          <text class="device-id">{{ incomingCallInfo.from }}</text>
          <text class="call-state">邀请你语音通话</text>
        </view>
      </view>
      <view class="incoming-controls">
        <view class="incoming-btn reject" @tap="rejectCall">
          <view class="btn-circle1 reject-btn">
            <image src="/static/hangup.png" />
          </view>
          <text>拒绝</text>
        </view>
        <view class="incoming-btn accept" @tap="acceptCall">
          <view class="btn-circle1 accept-btn">
            <image src="/static/call_accept.png" />
          </view>
          <text>接听</text>
        </view>
      </view>
    </view>

    <!-- 通话UI -->
    <view class="calling-ui" v-if="isInCall">
      <view class="calling-header">
        <text class="call-title">通话中</text>
        <view class="device-info">
          <text class="device-id">{{ incomingCallInfo.from }}</text>
          <text class="call-timer">{{ callDuration }}</text>
        </view>
      </view>
      <view class="calling-controls">
        <view class="control-btn" @tap="toggleSpeaker">
          <view class="btn-circle1">
            <image :src="isSpeakerOn ? '/static/mic_on.png' : '/static/mic_off.png'" />
          </view>
          <text>{{ isSpeakerOn ? '扬声器' : '听筒' }}</text>
        </view>
        <view class="control-btn hangup" @tap="hangUp">
          <view class="btn-circle1 hangup-btn">
            <image src="/static/hangup.png" />
          </view>
          <text>挂断</text>
        </view>
        <view class="control-btn" @tap="toggleMute">
          <view class="btn-circle1" :class="{ 'active-btn': isMuted }">
            <image :src="isMuted ? '/static/speaker_on.png' : '/static/speaker_off.png'" />
          </view>
          <text>{{ isMuted ? '取消静音' : '静音' }}</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
	import NIM from 'nim-web-sdk-ng/dist/v2/NIM_UNIAPP_SDK'

import NERTC from "@/NERtcUniappSDK-JS/lib/index";
// import NertcLocalView from "@/NERtcUniappSDK-JS/nertc-view/NertcLocalView";
// import NertcRemoteView from "@/NERtcUniappSDK-JS/nertc-view/NertcRemoteView";


	import permision from '@/NERtcUniappSDK-JS/permission.js'

	export default {
		name: 'CallPage',

		components: {
			// Remove these components
			// NertcLocalView,
			// NertcRemoteView
		},

		data() {
			return {
				// IM SDK 相关
				nimConnected: false,
				nimStatusText: '未连接',

				// RTC SDK 相关
				engine: null,
				rtcConnected: false,
				rtcStatusText: '未连接',

				// 通话相关
				isInCall: false,
				isMuted: false, // 默认不静音（麦克风打开）
				myAccount: '',
				targetAccount: '',
				callStatus: '未连接',
				callDuration: '',
				durationTimer: null,
				startTime: null,

				// 通话请求相关
				currentCallId: '',
				currentRemoteUserId: '',

				// 配置信息
				config: {
					appKey: '28fa64687a29f211131db2745bb84d14',
					debugLevel: 'debug'
				},
				isSpeakerOn: true, // 修改为默认开启扬声器
				callTimeoutTimer: null,
				hasShownNetworkWarning: false,
				showIncomingModal: true,
				incomingCallInfo: {
					callId: '',
					from: '',
					channelName: ''
				},

				// 添加设备信息
				deviceInfo: {
					id: `设备-${Date.now().toString().slice(-6)}-${Math.floor(Math.random() * 1000)}`, // 生成唯一设备ID
					image: '/static/device_icon.png'
				},

				// 铃声播放器
				ringtonePlayer: null,

				// 添加新属性保存通道名
				currentChannelName: '',

				// 添加重连相关变量
				isReconnecting: false,
				reconnectAttempts: 0,
				maxReconnectAttempts: 3,
				_stateChangeLog: [], // 用于记录状态变化
				_rtcJoinedAt: null, // 添加时间戳标记RTC连接成功时间
				options: {},
				isNavigating: false, // 新增：标记是否正在执行页面导航
				hasRejected: false, // 新增：标记是否已经拒绝过通话
			}
		},

		methods: {
			// NIM 初始化 - 使用v2接口
			initNIM() {
				try {
					console.log('初始化NIM SDK...')

					// 首先检查NIM是否正确导入
					if (!NIM) {
						console.error('NIM 未定义，检查导入语句')
						throw new Error('NIM SDK未定义，请检查导入')
					}

					// 检查全局实例是否已存在
					if (uni.$NIM) {
						console.log('NIM实例已存在于全局')
						return true
					}

					console.log('NIM类型:', typeof NIM)

					// 确认getInstance是可用的函数
					if (typeof NIM.getInstance !== 'function') {
						console.error('NIM.getInstance 不是函数', NIM)
						throw new Error('NIM.getInstance 方法不可用')
					}

					console.log('准备调用NIM.getInstance')

					try {
						// 创建实例并保存到全局
						const nimInstance = NIM.getInstance({
							appkey: this.config.appKey,
							debugLevel: "debug",
							apiVersion: "v2"
						})

						// 确认实例创建成功
						if (!nimInstance) {
							throw new Error('getInstance返回了空值')
						}

						// 输出实例基本信息，帮助调试
						console.log('NIM实例创建成功，类型:', typeof nimInstance)

						// 确认v2服务可用
						if (!nimInstance.V2NIMLoginService) {
							throw new Error('V2 服务不可用，请确认SDK版本支持v2接口')
						}

						// 保存到全局变量
						uni.$NIM = nimInstance

						console.log('NIM SDK初始化完成，已保存到全局 uni.$NIM')
						return true

					} catch (instanceError) {
						console.error('创建NIM实例时出错:', instanceError)
						throw new Error(`创建实例失败: ${instanceError.message || '未知错误'}`)
					}

				} catch (error) {
					console.error('NIM SDK初始化失败:', error)
					uni.showToast({
						title: 'NIM初始化失败: ' + (error.message || '未知错误'),
						icon: 'none',
						duration: 3000
					})
					uni.$NIM = null
					return false
				}
			},

			// 登录 - 使用v2接口
			async calllogin() {
				if (!this.myAccount) {
					uni.showToast({
						title: '请输入您的账号',
						icon: 'none'
					})
					return
				}

				try {
					// 初始化NIM
					if (!uni.$NIM) {
						const initSuccess = this.initNIM()
						if (!initSuccess) {
							throw new Error('NIM初始化失败，无法登录')
						}
					}

					console.log('开始登录NIM...')

					// 从本地缓存获取token
					// const token ='****************************************************************************************************************************'
					const token = uni.getStorageSync('sign') || ''
					if (!token) {
						throw new Error('未找到登录凭证，请先获取token')
					}

					// 使用v2登录接口
					try {
						const params = {
							forceMode: false,
							authType: 1, // 动态token类型
							tokenProvider: function() {
								return Promise.resolve(token)
							}
						}

						// 直接使用V2NIMLoginService的login方法
						await uni.$NIM.V2NIMLoginService.login(this.myAccount, token, params)
						console.log('V2接口登录成功')

						// 登录成功处理
						this.handleLoginSuccess()

					} catch (loginErr) {
						console.error('V2接口登录失败:', loginErr)
						throw loginErr
					}

				} catch (err) {
					console.error('登录失败:', err)

					let errorMsg = '登录失败'
					if (err) {
						if (err.message) {
							errorMsg = err.message
						} else if (err.code) {
							errorMsg = `错误码: ${err.code}`
						}
					}

					uni.showToast({
						title: errorMsg,
						icon: 'none'
					})
				}
			},

			// 处理登录成功
			handleLoginSuccess() {
				this.nimConnected = true
				this.nimStatusText = '已登录'
				this.callStatus = '准备就绪'
                console.log('IM登录成功')
				// 存储账号信息
				uni.setStorageSync('accountId', this.myAccount)

				// 设置事件监听
				this.setupEventListeners()

				// 初始化RTC
				this.initRTC()
			},

			// 设置事件监听 - 使用v2接口
			setupEventListeners() {
				if (!uni.$NIM) return

				try {
					console.log('设置NIM事件监听')

					// 使用V2 Event服务
					const eventService = uni.$NIM.V2NIMEventService
					if (eventService) {
						console.log('使用V2NIMEventService监听连接状态')

						// 监听踢出事件
						eventService.onKickedOutOriginClient(data => {
							console.log('账号在其他设备登录:', data)
							this.nimConnected = false
							this.nimStatusText = '已在其他设备登录'
							this.callStatus = '已在其他设备登录'

							uni.showToast({
								title: '账号已在其他设备登录',
								icon: 'none',
								duration: 3000
							})
						})
					}

					// 监听自定义通知 - 用于语音通话信令
					if (uni.$NIM.V2NIMNotificationService) {
						console.log('使用V2NIMNotificationService监听自定义通知')

						// 监听自定义通知
						uni.$NIM.V2NIMNotificationService.on("onReceiveCustomNotifications", (customNotifications) => {
							if (Array.isArray(customNotifications)) {
								customNotifications.forEach(notification => {
									this.handleCustomNotification(notification)
								})
							}
						})
					} else {
						console.warn('找不到V2通知服务，无法监听消息')
					}
				} catch (error) {
					console.error('设置事件监听失败:', error)
				}
			},

			// 登出 - 使用v2接口
			async logout() {
				if (!uni.$NIM) {
					return
				}

				try {
					// 如果正在通话中，先挂断
					if (this.isInCall) {
						await this.hangUp()
					}

					// 使用V2登出方法
					await uni.$NIM.V2NIMLoginService.logout()

					this.callStatus = '未连接'

					uni.showToast({
						title: '已登出',
						icon: 'none'
					})

				} catch (error) {
					console.error('登出失败:', error)
					uni.showToast({
						title: '登出失败',
						icon: 'none'
					})
				}
			},

			// 通用发送自定义消息方法 - 使用V2接口
			async sendCustomNotification(to, content) {
				try {
					if (!uni.$NIM) {
						throw new Error('NIM未初始化');
					}

					// 确保to参数有效
					if (!to) {
						throw new Error('接收方账号为空');
					}

					// 确保content是字符串
					const contentStr = typeof content === 'string' ? content : JSON.stringify(content);

					console.log(`尝试向 ${to} 发送消息:`, contentStr);

					// 使用V2通知服务
					if (uni.$NIM.V2NIMNotificationService) {
						console.log('使用V2NIMNotificationService发送');

						// 使用与callpage.vue完全相同的会话ID格式
						const conversationId = `${this.myAccount}|1|${to}`;
						
						console.log('使用会话ID:', conversationId);

						// 由于SDK可能不支持Promise，使用直接函数调用
						try {
							// 直接调用函数
							const result = uni.$NIM.V2NIMNotificationService.sendCustomNotification(
								conversationId,
								contentStr,
								{}
							);
							
							console.log('发送结果:', result);
							return result;
						} catch (callError) {
							console.error('直接调用发送函数失败:', callError);
							throw callError;
						}
					} else {
						throw new Error('找不到V2NIMNotificationService');
					}
				} catch (error) {
					console.error('发送消息失败:', error);
					throw error;
				}
			},

			// 初始化RTC引擎
			initRTC() {
				try {
					console.log('初始化RTC引擎 - 纯音频模式...')

					// 确保不重复初始化
					if (this.engine) {
						console.log('RTC引擎已存在，跳过初始化')
						this.rtcStatusText = '已初始化'
						return
					}

					// 使用最严格的设置禁用视频
					this.engine = NERTC.setupEngineWithContext({
						appKey: this.config.appKey,
						logDir: '',
						logLevel: 6, // 修改为最详细的日志级别6
						// 完全禁用视频设置，并指定纯音频模式
						videoConfig: {
							enable: false,     // 完全禁用视频
							quality: 0,        // 设置最低视频质量避免初始化视频
							width: 0,          // 设置0宽度，防止创建视频画布
							height: 0          // 设置0高度，防止创建视频画布
						},
						audioConfig: {
							enable: true       // 只启用音频
						},
						// 特别指定为纯音频
						scene: 'audio_call'  // 明确指定为纯音频场景
					})

					// 仅检查音频权限
					if (uni.getSystemInfoSync().platform === 'android') {
						permision.requestAndroidPermission('android.permission.RECORD_AUDIO')
					}

					// 确保完全禁用视频功能
					if (typeof this.engine.enableLocalVideo === 'function') {
						this.engine.enableLocalVideo({
							enable: false
						})
						console.log('视频已完全禁用')
					}

					// 音频配置
					this.engine.setAudioProfile({
						profile: 0,
						scenario: 0
					})

					// 启用音频
					this.enableAudio()

					// 注册RTC事件监听
					this.setupRTCListeners()

					console.log('RTC引擎初始化完成 - 纯音频模式')
					this.rtcStatusText = '已初始化'

				} catch (error) {
					console.error('RTC引擎初始化失败:', error)
					uni.showToast({
						title: 'RTC初始化失败',
						icon: 'none'
					})
				}
			},

			// 设置RTC事件监听
			setupRTCListeners() {
				// 错误事件
				this.engine.addEventListener('onError', (code, message, extraInfo) => {
					console.error(`RTC错误: code=${code}, message=${message}, extraInfo=${extraInfo}`)
					// 严重错误时结束通话
					if (code < 0) {
						uni.showToast({
							title: '通话发生错误，已断开',
							icon: 'none'
						})
						this.endCall()
					}
				})

				// 加入频道事件
				this.engine.addEventListener('onJoinChannel', (result, channelId, elapsed, userID) => {
					console.log(`加入频道成功: result=${result}, channelId=${channelId}, userID=${userID}`)
					if (result === 0) {
						this.rtcConnected = true
						this.rtcStatusText = '已连接'
						
						// 重要：确保通话状态为true，并添加保护状态的标志
						this.isInCall = true;
						this._rtcJoinedAt = Date.now(); // 保留时间戳记录通话建立时间
						
						// 记录通话建立信息，方便调试
						console.log('通话已成功建立，记录时间戳:', this._rtcJoinedAt, '当前状态:', {
							rtcConnected: this.rtcConnected,
							isInCall: this.isInCall,
							channelId: channelId,
							deviceId: this.deviceInfo.id // 记录设备ID，便于多端区分
						});
						
						this.callStatus = '通话中'
					}
				})

				// 离开频道事件
				this.engine.addEventListener('onLeaveChannel', (result) => {
					console.log(`离开频道: result=${result}`)
					
					// 不要在这里自动设置isInCall=false
					// 只有当调用endCall方法时才应该改变isInCall
					this.rtcConnected = false
					this.rtcStatusText = '未连接'
				})

				// 用户加入事件
				this.engine.addEventListener('onUserJoined', (userID) => {
					console.log(`用户加入: userID=${userID}`)
				})

				// 用户离开事件
				this.engine.addEventListener('onUserLeave', (userID) => {
					console.log(`用户离开: userID=${userID}`)
					if (userID.toString() === this.currentRemoteUserId) {
						console.log('通话对方已离开')
						uni.showToast({
							title: '对方已结束通话',
							icon: 'none'
						})
						// 强制结束本地通话
						this.endCall()
					}
				})

				// 用户音频开始事件
				this.engine.addEventListener('onUserAudioStart', (userID) => {
					console.log(`用户开启音频: userID=${userID}`)
				})

				// 用户音频停止事件
				this.engine.addEventListener('onUserAudioStop', (userID) => {
					console.log(`用户停止音频: userID=${userID}`)
				})

				// 网络状态变化
				this.engine.addEventListener('onConnectionStateChanged', (state) => {
					console.log(`RTC连接状态变化: state=${state}`)
					
					// 数字状态2通常表示已连接
					if (state === 2 || state === 'CONNECTED') {
						this.rtcConnected = true
						this.rtcStatusText = '已连接'
						console.log('RTC已连接到服务器')
					}
					// 只有当状态明确为断开或失败时才处理
					else if ((state === 1 || state === 'DISCONNECTED' || state === 'FAILED') && !this.isReconnecting) {
						console.log('RTC连接已断开或失败')
						
						// 如果正在通话中收到断开，尝试重连
						if (this.isInCall && this.currentChannelName) {
							console.log('通话中断开，尝试重连')
							this.isReconnecting = true
							
							setTimeout(() => {
								if (this.isInCall) { // 确认仍在通话状态
									console.log('尝试重新加入频道:', this.currentChannelName)
									this.joinRTCChannel(this.currentChannelName)
								}
								this.isReconnecting = false
							}, 2000)
						}
						
						// 只有在确定断开且不在通话中才更新状态
						if (!this.isInCall) {
							this.rtcConnected = false
							this.rtcStatusText = '未连接'
						}
					}
				})

				// 添加通话质量监测
				this.engine.addEventListener('onNetworkQuality', (userID, downlinkQuality, uplinkQuality) => {
					console.log(`网络质量: userID=${userID}, 下行=${downlinkQuality}, 上行=${uplinkQuality}`)
					// 网络极差时提示用户
					if (downlinkQuality >= 4 || uplinkQuality >= 4) {
						// 4和5表示网络质量差或很差
						if (!this.hasShownNetworkWarning) {
							this.hasShownNetworkWarning = true
							uni.showToast({
								title: '网络质量不佳',
								icon: 'none',
								duration: 2000
							})
							// 5秒后重置提示标志
							setTimeout(() => {
								this.hasShownNetworkWarning = false
							}, 5000)
						}
					}
				})
			},

			// 添加常量定义
			MULTI_DEVICE_SYNC: {
				CALL_HANDLED: 'call_handled_by_other_device'
			},

			// 修改handleCustomNotification方法
			handleCustomNotification(notification) {
				try {

					const content = typeof notification.content === 'string' ?
						JSON.parse(notification.content) :
						notification.content

					const fromAccount = notification.senderId || notification.fromAccount


					switch (content.type) {
						case 'call_invite':
							this.handleCallInvite(content, fromAccount)
							break
						case 'call_accept':
							this.handleCallAccept(content)
							break
						case 'call_reject':
							this.handleCallReject(content)
							break
						case 'call_cancel':
							this.handleCallCancel(content)
							break
						case 'call_end':
							this.handleCallHangup(content)
							break
						case 'call_handled_by_other_device': // 修改为与PC端一致的类型
							this.handleMultiDeviceSync(content, fromAccount)
							break
						default:
							console.log('未知信令:', content)
					}
				} catch (e) {
					console.error('处理自定义通知失败:', e, notification)
				}
			},
// 重命名并修改多端同步处理方法
			handleMultiDeviceSync(content, fromAccount) {
				// 确认是来自同一账号的其他端
				if (fromAccount === this.myAccount) {
					console.log('收到多端同步消息:', content);
					
					// 日志：当前本地callId和收到的callId
					console.log('本地currentCallId:', this.currentCallId, '收到的callId:', content.callId);
					
					// 添加字段标记消息处理设备
					const handledByThisDevice = content.handledBy === this.deviceInfo.id;
					console.log('是否由本设备处理:', handledByThisDevice, '处理设备:', content.handledBy, '本设备ID:', this.deviceInfo.id);
					
					// 如果消息与当前通话相关
					if (this.currentCallId === content.callId || 
						this.callStatus === '正在呼叫...' || 
						this.isInCall) {
						
						// 如果是本设备处理的，且是接听操作，继续显示通话UI
						if (handledByThisDevice && content.action === 'accept') {
							console.log('本设备已处理接听，继续显示通话UI');
							return; // 不执行后续关闭操作
						}
						
						// 其他情况（其他设备处理或非接听操作）
						this.safeHideModal();
						
						// 如果是其他设备处理的接听操作，不应该在本设备建立RTC连接
						if (!handledByThisDevice && content.action === 'accept') {
							// 标记当前不应该显示通话UI
							this.isInCall = false;
							console.log('通话已被其他设备接听，本设备不显示通话UI');
						}
						
						// 如果当前设备已经在通话中，但接收到其他设备的处理消息，结束本设备的通话
						if (this.isInCall && !handledByThisDevice) {
							this.endCall();
						} else if (!this.isInCall) {
							// 如果当前未在通话中，只重置状态
							this.resetCallState();
						}
						
						// 根据操作类型显示不同的提示
						let message = '通话已在其他端处理';
						if (content.action === 'accept') {
							message = '通话已在其他端接听';
						} else if (content.action === 'reject') {
							message = '通话已在其他端拒绝';
						} else if (content.action === 'cancel') {
							message = '通话已在其他端取消';
						} else if (content.action === 'end' || content.action === 'hangup') {
							message = '通话已在其他端结束';
						}
						
						uni.showToast({
							title: message,
							icon: 'none'
						});
					} else {
						console.log('多端同步消息callId不匹配，忽略');
					}
				}
			},
			// 修改发送多端同步通知的方法
			async sendMultiDeviceSync(action, callId) {
				try {
					const syncData = {
						type: 'call_handled_by_other_device',
						callId: callId,
						action: action,
						handledBy: this.deviceInfo.id, // 使用设备ID标识哪个设备处理了通话
						
						timestamp: Date.now() // 添加时间戳用于判断消息顺序
					}

					console.log('发送多端同步通知:', syncData);
					await this.sendCustomNotification(this.myAccount, syncData);
					return true;
				} catch (error) {
					console.error('发送多端同步通知失败:', error);
					return false;
				}
			},

			// 处理呼叫邀请方法
			handleCallInvite(content, from) {
				try {
					console.log('收到呼叫邀请:', content, '来自:', from);
					
					if (this.isInCall) {
						console.log('当前已在通话中，自动拒绝新呼叫');
						this.sendCustomNotification(from, {
							type: 'call_reject',
							callId: content.callId
						});
						return;
					}

					// 保存关键信息
					this.currentCallId = content.callId;
					this.currentRemoteUserId = from;
					this.currentChannelName = content.channelName || content.callId;
					
					// 保存到本地存储，便于后续恢复
					uni.setStorageSync('lastCallInfo', {
						callId: this.currentCallId,
						remoteUserId: from,
						channelName: this.currentChannelName,
						timestamp: Date.now()
					});
					
					console.log('已保存来电信息:', {
						callId: this.currentCallId,
						remoteUserId: this.currentRemoteUserId,
						channelName: this.currentChannelName
					});

					// 仅被叫弹窗，主叫不弹窗
					if (from !== this.myAccount) {
						// 播放来电铃声
						this.playRingtone();

						// 确保所有字段都有有效值
						this.incomingCallInfo = {
							from: from || '未知用户',
							callId: content.callId || '',
							channelName: content.channelName || content.callId || ''
						};
						
						console.log('设置来电信息:', this.incomingCallInfo);
						
						// 然后再显示弹窗
						this.showIncomingModal = true;

						// 30秒自动拒绝
						this.callTimeoutTimer = setTimeout(() => {
							if (this.showIncomingModal) {
								this.rejectCall(content.callId, from);
								uni.showToast({
									title: '来电超时未接听',
									icon: 'none'
								});
							}
						}, 30000);
					}
				} catch (error) {
					console.error('处理呼叫邀请失败:', error);
				}
			},

			// 处理接受呼叫
			async acceptCall(callId, from, channelName) {
				try {
					console.log('接受呼叫，原始参数:', { callId, from, channelName });
					
					// 处理callId参数 - 可能是对象或字符串
					if (callId && typeof callId === 'object') {
						console.log('callId是对象，尝试提取callId属性:', callId);
						// 如果callId是对象，尝试从中提取callId属性
						if (callId.callId) {
							callId = callId.callId;
						} else if (this.incomingCallInfo && this.incomingCallInfo.callId) {
							// 如果对象中没有callId属性，尝试从incomingCallInfo中获取
							callId = this.incomingCallInfo.callId;
							console.log('从incomingCallInfo获取callId:', callId);
						} else {
							// 如果都没有，使用currentCallId
							callId = this.currentCallId;
							console.log('使用currentCallId:', callId);
						}
					}
					
					// 处理from参数 - 可能为undefined
					if (!from) {
						console.log('from参数为空，尝试从其他来源获取');
						
						// 尝试从incomingCallInfo获取
						if (this.incomingCallInfo && this.incomingCallInfo.from) {
							from = this.incomingCallInfo.from;
							console.log('从incomingCallInfo获取from:', from);
						} 
						// 尝试从currentRemoteUserId获取
						else if (this.currentRemoteUserId) {
							from = this.currentRemoteUserId;
							console.log('从currentRemoteUserId获取from:', from);
						}
						// 尝试从URL参数获取
						else {
							const pages = getCurrentPages();
							const currentPage = pages[pages.length - 1];
							if (currentPage && currentPage.$page && currentPage.$page.options) {
								from = currentPage.$page.options.from;
								console.log('从页面参数获取from:', from);
							}
						}
						
						// 如果仍然无法获取from
						if (!from) {
							// 尝试从本地存储获取最近通话记录
							try {
								const lastCallInfo = uni.getStorageSync('lastCallInfo');
								if (lastCallInfo && lastCallInfo.remoteUserId) {
									from = lastCallInfo.remoteUserId;
									console.log('从本地存储获取from:', from);
								}
							} catch (e) {
								console.error('从本地存储获取from失败:', e);
							}
						}
						
						// 如果仍然无法获取from，抛出错误
						if (!from) {
							throw new Error('无法获取对方账号');
						}
					}

					// 处理channelName参数
					if (!channelName) {
						console.log('channelName为空，尝试从其他来源获取');
						
						// 尝试从incomingCallInfo获取
						if (this.incomingCallInfo && this.incomingCallInfo.channelName) {
							channelName = this.incomingCallInfo.channelName;
							console.log('从incomingCallInfo获取channelName:', channelName);
						}
						// 使用callId作为channelName
						else if (callId) {
							channelName = callId;
							console.log('使用callId作为channelName:', channelName);
						}
					}

					// 记录到本地变量以备后用
					this.currentChannelName = channelName;
					this.currentCallId = callId;
					this.currentRemoteUserId = from;
					
					// 保存到本地存储，便于后续恢复
					uni.setStorageSync('lastCallInfo', {
						callId: callId,
						remoteUserId: from,
						channelName: channelName,
						timestamp: Date.now()
					});
					
					console.log('已保存通话信息:', {
						callId: this.currentCallId,
						from: this.currentRemoteUserId,
						channelName: this.currentChannelName
					});

					// 停止铃声
					this.stopRingtone();

					// 关闭来电弹窗
					this.showIncomingModal = false;

					// 清除超时计时器
					if (this.callTimeoutTimer) {
						clearTimeout(this.callTimeoutTimer);
						this.callTimeoutTimer = null;
					}

					// 设置通话状态
					this.isInCall = true;
					this.callStatus = '通话中';
					
					// 立即发送多端同步通知，表明此设备已接听通话
					try {
						// 使用同步方式标记此设备处理了通话
						const syncSent = await this.sendMultiDeviceSync('accept', this.currentCallId);
						console.log('已发送多端同步接听通知，结果:', syncSent);
					} catch (syncError) {
						console.error('发送多端同步通知失败:', syncError);
						// 即使同步失败，也继续通话流程
					}

					// 使用setTimeout确保UI更新先于RTC操作
					setTimeout(async () => {
						try {
							console.log('准备发送接听通知，参数:', { 
								callId: this.currentCallId, 
								from: this.currentRemoteUserId, 
								channelName: this.currentChannelName 
							});
							
							// 尝试发送接听通知
							let notificationSent = false;
							
							try {
								// 尝试直接使用函数调用方式发送通知
								if (uni.$NIM && uni.$NIM.V2NIMNotificationService) {
									const conversationId = `${this.myAccount}|1|${this.currentRemoteUserId}`;
									const content = JSON.stringify({
										type: 'call_accept',
										callId: this.currentCallId
									});
									
									// 直接调用函数
									uni.$NIM.V2NIMNotificationService.sendCustomNotification(
										conversationId,
										content,
										{}
									);
									
									notificationSent = true;
									console.log('直接调用函数发送通知成功');
								}
							} catch (directError) {
								console.error('直接调用函数发送通知失败:', directError);
							}
							
							// 如果直接调用失败，尝试使用sendCustomNotification方法
							if (!notificationSent) {
								try {
									await this.sendCustomNotification(this.currentRemoteUserId, {
										type: 'call_accept',
										callId: this.currentCallId
									});
									notificationSent = true;
									console.log('使用sendCustomNotification发送通知成功');
								} catch (error) {
									console.error('使用sendCustomNotification发送通知失败:', error);
								}
							}
							
							// 即使通知发送失败，也继续通话流程
							if (!notificationSent) {
								console.warn('所有通知发送方法都失败，但将继续通话流程');
							}
							
							// 初始化音频
							this.prepareAudioBeforeJoin();
							
							// 加入RTC频道
							const joinResult = this.joinRTCChannel(this.currentChannelName);
							
							// 启动计时器
							this.startTimer();
						} catch (error) {
							console.error('接听流程出错:', error);
							// 即使出错也保持通话状态，让用户手动挂断
							// this.isInCall = false; // 注释掉，保持通话状态
						}
					}, 10);
					
				} catch (error) {
					console.error('接受呼叫失败:', error);
					uni.showToast({
						title: '接听失败: ' + (error.message || '未知错误'),
						icon: 'none'
					});
					// this.isInCall = false; // 注释掉，保持通话状态
				}
			},

			// 处理拒绝呼叫
			async rejectCall(callId, from) {
				try {
					console.log('拒绝呼叫，原始参数:', { callId, from });
					
					// 标记已经拒绝过通话，避免后续重复导航
					this.hasRejected = true;
					console.log('设置hasRejected标志为true，防止后续重复导航');
					
					// 处理callId参数
					if (callId && typeof callId === 'object') {
						console.log('callId是对象，尝试提取callId属性:', callId);
						if (callId.callId) {
							callId = callId.callId;
						} else if (this.incomingCallInfo && this.incomingCallInfo.callId) {
							callId = this.incomingCallInfo.callId;
						} else {
							callId = this.currentCallId;
						}
					}
					
					// 处理from参数
					if (!from) {
						console.log('from参数为空，尝试从其他来源获取');
						if (this.incomingCallInfo && this.incomingCallInfo.from) {
							from = this.incomingCallInfo.from;
						} else if (this.currentRemoteUserId) {
							from = this.currentRemoteUserId;
						} else {
							// 尝试从本地存储获取
							try {
								const lastCallInfo = uni.getStorageSync('lastCallInfo');
								if (lastCallInfo && lastCallInfo.remoteUserId) {
									from = lastCallInfo.remoteUserId;
								}
							} catch (e) {
								console.error('从本地存储获取from失败:', e);
							}
						}
						
						if (!from) {
							throw new Error('无法获取对方账号');
						}
					}
					
					console.log('拒绝呼叫，处理后参数:', { callId, from });
					
					// 停止铃声
					this.stopRingtone();

					// 关闭来电弹窗
					this.showIncomingModal = false;

					// 清除超时计时器
					if (this.callTimeoutTimer) {
						clearTimeout(this.callTimeoutTimer);
						this.callTimeoutTimer = null;
					}

					// 立即发送多端同步通知，表明此设备已拒绝通话
					try {
						const syncSent = await this.sendMultiDeviceSync('reject', callId);
						console.log('已发送多端同步拒绝通知，结果:', syncSent);
					} catch (syncError) {
						console.error('发送多端同步通知失败:', syncError);
						// 即使同步失败，也继续流程
					}

					// 尝试发送拒绝通知
					try {
						// 直接调用函数发送通知
						if (uni.$NIM && uni.$NIM.V2NIMNotificationService) {
							const conversationId = `${this.myAccount}|1|${from}`;
							const content = JSON.stringify({
								type: 'call_reject',
								callId: callId
							});
							
							uni.$NIM.V2NIMNotificationService.sendCustomNotification(
								conversationId,
								content,
								{}
							);
							
							console.log('直接调用函数发送拒绝通知成功');
						}
					} catch (directError) {
						console.error('直接调用函数发送拒绝通知失败:', directError);
						
						// 尝试使用sendCustomNotification方法
						try {
							await this.sendCustomNotification(from, {
								type: 'call_reject',
								callId: callId
							});
							console.log('使用sendCustomNotification发送拒绝通知成功');
						} catch (error) {
							console.error('使用sendCustomNotification发送拒绝通知失败:', error);
						}
					}

					// 重置通话状态
					this.resetCallState();
					
					// 在这里执行导航，不在方法开始就设置isNavigating
					console.log('rejectCall: 准备执行页面导航');
					
					// 使用延迟确保状态都已重置后再导航
					setTimeout(() => {
						this.navigateBack();
					}, 100);
					
				} catch (error) {
					console.error('拒绝呼叫失败:', error);
					// 即使失败也重置状态
					this.resetCallState();
					
					// 即使出错也尝试返回上一页，使用同样的延迟
					setTimeout(() => {
						this.navigateBack();
					}, 100);
				}
			},

			// 处理取消呼叫
			handleCallCancel(content) {
				if (content.callId === this.currentCallId) {
					console.log('对方取消呼叫');
					
					// 标记已经处理过导航，避免后续重复导航
					this.hasRejected = true;
					console.log('设置hasRejected标志为true，防止后续重复导航');
					
					// 停止铃声
					this.stopRingtone();
					
					// 关闭来电弹窗
					this.showIncomingModal = false;
					
					// 清除超时计时器
					if (this.callTimeoutTimer) {
						clearTimeout(this.callTimeoutTimer);
						this.callTimeoutTimer = null;
					}
					
					uni.showToast({
						title: '对方取消了通话',
						icon: 'none'
					});
					
					// 立即重置状态
					this.resetCallState();
					
					// 在这里执行导航，不在方法开始就设置isNavigating
					console.log('handleCallCancel: 准备执行页面导航');
					
					// 使用延迟确保状态都已重置后再导航
					setTimeout(() => {
						this.navigateBack();
					}, 100);
				}
			},

			// 处理挂断呼叫
			handleCallHangup(content) {
				console.log('收到挂断通知:', content)
				console.log('本地currentCallId:', this.currentCallId)
				
				// 增加宽松匹配条件，允许在呼叫状态下接收任何挂断信令
				if (content.callId === this.currentCallId || this.isInCall) {
					console.log('对方挂断呼叫，处理中...')

					// 弹窗提示
					uni.showToast({
						title: '对方已挂断通话',
						icon: 'none',
						duration: 2000
					})

					// 立即结束通话 - endCall方法内会处理导航
					this.endCall()
				} else {
					console.log('收到的挂断callId不匹配:', content.callId, this.currentCallId, '但通话状态为:', this.isInCall)
				}
			},

			// 处理接受呼叫通知
			handleCallAccept(content) {
				if (content.callId !== this.currentCallId) {
					console.log('收到的接听callId不匹配:', content.callId, this.currentCallId)
					return
				}
				
				console.log('对方已接受呼叫')
				
				// 清除超时计时器
				if (this.callTimeoutTimer) {
					clearTimeout(this.callTimeoutTimer)
					this.callTimeoutTimer = null
				}
				
				// 确保使用正确的channel
				const channelName = this.currentChannelName || this.currentCallId
				
				// 加入RTC频道
				this.joinRTCChannel(channelName)
				
				// 更新状态
				this.isInCall = true
				this.callStatus = '通话中'
				
				// 开始计时
				this.startTimer()
				
				// 显示提示
				uni.showToast({
					title: '对方已接听',
					icon: 'none',
					duration: 2000
				})
			},

			// 处理拒绝呼叫通知
			handleCallReject(content) {
				if (content.callId !== this.currentCallId) {
					console.log('收到的拒绝callId不匹配:', content.callId, this.currentCallId);
					return;
				}
				
				// 标记已经处理过导航，避免后续重复导航
				this.hasRejected = true;
				console.log('设置hasRejected标志为true，防止后续重复导航');
				
				// 清除超时计时器
				if (this.callTimeoutTimer) {
					clearTimeout(this.callTimeoutTimer);
					this.callTimeoutTimer = null;
				}
				
				// 更新UI状态
				this.callStatus = '对方已拒绝';
				
				uni.showToast({
					title: '对方拒绝了通话',
					icon: 'none',
					duration: 2000
				});
				
				// 重置状态并导航
				setTimeout(() => {
					this.resetCallState();
					
					// 在这里执行导航，不在方法开始就设置isNavigating
					console.log('handleCallReject: 准备执行页面导航');
					this.navigateBack(); // 使用统一的导航方法
				}, 500); // 保留较长时间以便用户看到提示信息，但缩短了原来的2000ms
			},

			// 发起呼叫
			async makeCall() {
				if (!this.targetAccount) {
					uni.showToast({
						title: '请输入目标账号',
						icon: 'none'
					})
					return
				}

				try {
					// 生成唯一呼叫ID
					this.currentCallId = `call_${Date.now()}_${Math.floor(Math.random() * 1000)}`
					this.currentRemoteUserId = this.targetAccount
					this.currentChannelName = this.currentCallId // Set channel name same as callId

					// 发送呼叫邀请
					await this.sendCustomNotification(this.targetAccount, {
						type: 'call_invite',
						callId: this.currentCallId,
						
					})

					this.callStatus = '正在呼叫...'

					// 设置超时处理
					if (this.callTimeoutTimer) {
						clearTimeout(this.callTimeoutTimer)
					}

					this.callTimeoutTimer = setTimeout(() => {
						if (this.callStatus === '正在呼叫...') {
							uni.hideLoading()
							this.callStatus = '对方无应答'
							uni.showToast({
								title: '对方无应答',
								icon: 'none',
								duration: 2000
							})
							this.cancelCall()
						}
					}, 30000) // 30秒超时

				} catch (error) {
					console.error('发起呼叫失败:', error)
					uni.hideLoading()
					uni.showToast({
						title: '呼叫失败',
						icon: 'none'
					})
				}
			},

			// 取消呼叫
			async cancelCall() {
				try {
					// 清除超时定时器
					if (this.callTimeoutTimer) {
						clearTimeout(this.callTimeoutTimer)
						this.callTimeoutTimer = null
					}

					// 立即发送多端同步通知，表明此设备已取消通话
					try {
						const syncSent = await this.sendMultiDeviceSync('cancel', this.currentCallId);
						console.log('已发送多端同步取消通知，结果:', syncSent);
					} catch (syncError) {
						console.error('发送多端同步通知失败:', syncError);
						// 即使同步失败，也继续流程
					}

					if (this.currentRemoteUserId) {
						await this.sendCustomNotification(this.currentRemoteUserId, {
							type: 'call_cancel',
							callId: this.currentCallId
						})
					}

					this.resetCallState()

				} catch (error) {
					console.error('取消呼叫失败:', error)
				}
			},

			// 挂断
			async hangUp() {
				try {
					console.log('开始执行挂断操作');
					
					if (this.callTimeoutTimer) {
						clearTimeout(this.callTimeoutTimer);
						this.callTimeoutTimer = null;
					}

					// 立即发送多端同步通知，表明此设备已挂断通话
					try {
						const syncSent = await this.sendMultiDeviceSync('end', this.currentCallId);
						console.log('已发送多端同步挂断通知，结果:', syncSent);
					} catch (syncError) {
						console.error('发送多端同步通知失败:', syncError);
						// 即使同步失败，也继续流程
					}

					if ((this.isInCall || this.callStatus === '正在呼叫...') && this.currentRemoteUserId) {
						// 尝试使用标准方法发送通知
						let notificationSent = false;
						
						try {
							await this.sendCustomNotification(this.currentRemoteUserId, {
								type: 'call_end',
								callId: this.currentCallId
							});
							notificationSent = true;
							console.log('使用标准方法发送挂断通知成功');
						} catch (notifyError) {
							console.error('标准方法发送挂断通知失败:', notifyError);
							
							// 尝试使用备用方法
							try {
								await this.sendSystemNotification(this.currentRemoteUserId, {
									type: 'call_end',
									callId: this.currentCallId
								});
								notificationSent = true;
								console.log('使用备用方法发送挂断通知成功');
							} catch (backupError) {
								console.error('备用方法也失败:', backupError);
							}
						}
						
						// 即使通知发送失败，也继续流程
						if (!notificationSent) {
							console.warn('无法发送挂断通知，但将继续流程');
						}
					}

					// 确保RTC引擎先停止音频再离开频道
					if (this.engine) {
						try {
							// 先禁用音频
							if (typeof this.engine.enableLocalAudio === 'function') {
								this.engine.enableLocalAudio({
									enable: false
								});
								console.log('挂断前已禁用音频');
							}
						} catch (e) {
							console.error('禁用音频失败:', e);
						}
					}

					// 确保完全挂断 - 移除后面的导航，统一由endCall处理
					console.log('hangUp: 调用endCall处理后续操作');
					this.endCall();
					
				} catch (error) {
					console.error('挂断失败:', error);
					this.endCall();
				}
			},

			// 加入RTC频道
			 async joinRTCChannel(channelName) {
				try {
					console.log('加入RTC频道 (仅音频模式):', channelName)
					
					// 检查参数
					if (!channelName || !this.engine) {
						uni.showToast({
							title: '通话参数错误',
							icon: 'none'
						})
						return false
					}

					// 生成唯一UID
					const uid = parseInt(this.myAccount.replace(/\D/g, '')) || Math.floor(Math.random() * 100000)
					
					try {
						// 设置订阅选项
						if (typeof this.engine.setAutoSubscribe === 'function') {
							this.engine.setAutoSubscribe({
								video: false,
								audio: true
							})
						}
						
						// 确保视频禁用
						if (typeof this.engine.enableLocalVideo === 'function') {
							this.engine.enableLocalVideo({
								enable: false
							})
						}
						
						// 启用音频
						if (typeof this.engine.enableLocalAudio === 'function') {
							this.engine.enableLocalAudio({
								enable: true
							})
						}
						
						// 设置音频参数
						if (typeof this.engine.setAudioProfile === 'function') {
							this.engine.setAudioProfile({
								profile: 0,
								scenario: 1
							})
						}
						
						// 构建参数
						const joinParams = {
							token: '',
							channelName: channelName,
							myUid: uid,
							mediaOptions: {
								audio: true,
								video: false
							}
						}
						
						// 加入频道
						const result = this.engine.joinChannel(joinParams)
						console.log('加入频道结果:', result)
						
						// 重要：再次确保状态正确
						this.isInCall = true
						this.callStatus = '通话中'
						
						// 记录状态
						console.log('加入频道后状态:', {
							isInCall: this.isInCall,
							callStatus: this.callStatus
						})
						
						// 设置扬声器状态 - 默认打开
						if (typeof this.engine.setEnableSpeakerphone === 'function') {
							this.engine.setEnableSpeakerphone(this.isSpeakerOn)
							console.log('扬声器状态设置为:', this.isSpeakerOn ? '开启' : '关闭')
						}
						
						// 确保初始状态为非静音
						this.isMuted = false
						if (typeof this.engine.enableLocalAudio === 'function') {
							// 启用本地音频（非静音）
							this.engine.enableLocalAudio({
								enable: true
							});
							console.log('初始化本地音频为启用状态（非静音）');
						}
						
						return true
					} catch (error) {
						console.error('加入频道异常:', error)
						uni.showToast({
							title: '加入通话失败',
							icon: 'none'
						})
						
						// 即使出错也不改变isInCall状态
						console.log('加入出错后仍保持通话状态:', this.isInCall)
						return false
					}
				} catch (error) {
					console.error('整体加入频道失败:', error)
					return false
				}
			},

			// 结束通话
			endCall() {
				try {
					console.log('结束通话');
					
					// 先记录通话持续时间用于日志
					let callDuration = 0;
					if (this._rtcJoinedAt) {
						callDuration = Date.now() - this._rtcJoinedAt;
						console.log(`通话持续了${callDuration/1000}秒`);
					}
					
					// 先更新状态
					this.isInCall = false;
					this.callStatus = '通话已结束';
					
					// 停止计时
					this.stopTimer();
					
					// 关闭弹窗
					uni.hideToast();
					this.safeHideModal();

					// 处理RTC
					if (this.engine) {
						try {
							// 使用enableLocalAudio替代muteLocalAudioStream
							if (typeof this.engine.enableLocalAudio === 'function') {
								this.engine.enableLocalAudio({
									enable: false
								});
								console.log('结束通话前已禁用音频');
							}
							
							// 离开频道 - 不使用Promise形式
							if (typeof this.engine.leaveChannel === 'function') {
								this.engine.leaveChannel();
								console.log('已离开频道');
							}
						} catch (rtcError) {
							console.error('RTC操作失败:', rtcError);
						}
					}

					// 重置状态 - 使用更安全的方式清理状态，确保不会被意外调用
					setTimeout(() => {						
						// 直接在这里重置关键状态
						this.currentCallId = '';
						this.currentRemoteUserId = '';
						this.currentChannelName = '';
						// 确保通话已结束
						this.rtcConnected = false;
						this.rtcStatusText = '未连接';
						// 重要：清除通话建立时间戳
						this._rtcJoinedAt = null;
						// 其他状态重置
						this.callDuration = '';
						this.showIncomingModal = false;
						this.stopRingtone();
						
						// 只清理计时器，不修改通话状态
						if (this.callTimeoutTimer) {
							clearTimeout(this.callTimeoutTimer);
							this.callTimeoutTimer = null;
						}
						
						this.callStatus = this.nimConnected ? '准备就绪' : '未连接';
						
						// 添加延迟返回上一页，确保状态重置完成后再返回
						// 如果hasRejected为true，则说明已经执行过导航，此处不再执行
						if (!this.hasRejected) {
							console.log('endCall: 准备执行页面导航');
							// 确保在此处设置导航标志，而不是在方法开始
							this.navigateBack(); // 使用统一的导航方法
						} else {
							console.log('已经拒绝过通话，endCall不再重复调用导航');
						}
					}, 500);
				} catch (error) {
					console.error('结束通话失败:', error);
					// 确保状态重置 - 不再调用resetCallState
					this.isInCall = false;
					this.rtcConnected = false;
					this._rtcJoinedAt = null; // 确保也重置这个变量
					this.currentCallId = '';
					this.currentRemoteUserId = '';
					
					// 即使出错也尝试返回上一页
					// 如果hasRejected为true，则说明已经执行过导航，此处不再执行
					if (!this.hasRejected) {
						console.log('endCall错误处理: 准备执行页面导航');
						this.navigateBack(); // 使用统一的导航方法
					} else {
						console.log('已经拒绝过通话，endCall错误处理不再重复调用导航');
					}
				}
			},

			// 重置通话状态
			resetCallState() {
				// ⚠️ 添加保护，防止在通话中意外重置状态
				if (this.isInCall && this.engine && this.rtcConnected) {
					console.warn('检测到在活跃通话中尝试重置状态，已阻止', new Error().stack)
					return; // 阻止在活跃通话中重置
				}
				
				console.log('重置通话状态', new Error().stack)
				
				this.currentCallId = ''
				this.currentRemoteUserId = ''
				this.isInCall = false
				this.callStatus = this.nimConnected ? '准备就绪' : '未连接'
				this.stopTimer()
				this.callDuration = ''
				
				// 清除通话建立时间标记
				this._rtcJoinedAt = null;
				
				this.showIncomingModal = false
				this.stopRingtone()
				
				if (this.callTimeoutTimer) {
					clearTimeout(this.callTimeoutTimer)
					this.callTimeoutTimer = null
				}
				
				// 重要：不重置hasRejected标志，避免影响导航逻辑
				// 仅在页面卸载时重置hasRejected
			},

			// 静音控制
			toggleMute() {
				if (!this.engine) {
					console.error('静音操作失败: RTC引擎未初始化');
					return;
				}

				try {
					// 记录旧状态用于错误恢复
					const oldMuteState = this.isMuted;
					
					// 先更新UI状态
					this.isMuted = !this.isMuted;
					console.log('尝试切换静音状态为:', this.isMuted ? '静音' : '非静音');
					
					// 检查引擎的方法实例
					console.log('RTC引擎方法:', Object.keys(this.engine).filter(key => typeof this.engine[key] === 'function'));
					
					// 尝试多种方式实现静音
					if (typeof this.engine.enableLocalAudio === 'function') {
						console.log('使用enableLocalAudio方法静音，参数:', { enable: !this.isMuted });
						
						// 直接调用方法检查错误
						try {
							// 尝试直接传递布尔值参数
							const result = this.engine.enableLocalAudio(!this.isMuted);
							console.log('enableLocalAudio直接调用结果:', result);
						} catch (directCallError) {
							console.error('直接调用enableLocalAudio失败:', directCallError);
							
							// 尝试使用对象参数格式
							try {
								const result = this.engine.enableLocalAudio({ enable: !this.isMuted });
								console.log('enableLocalAudio使用对象参数调用结果:', result);
							} catch (objectCallError) {
								console.error('使用对象参数调用enableLocalAudio失败:', objectCallError);
								throw new Error('无法正确调用enableLocalAudio方法');
							}
						}

						console.log(`已${this.isMuted ? '静音' : '取消静音'}本地音频`);
						
						// 显示操作成功提示
						uni.showToast({
							title: this.isMuted ? '已静音' : '已取消静音',
							
							icon: 'none',
							duration: 1500
						});
					} 
					else {
						console.error('找不到enableLocalAudio方法');

							uni.showToast({
								title: '静音功能不可用',
								icon: 'none',
								duration: 2000
							});
						
					}
				} catch (error) {
					console.error('切换静音状态失败:', error, '错误堆栈:', error.stack);
					// 还原状态
					this.isMuted = !this.isMuted;
					
					uni.showToast({
						title: '静音操作失败',
						icon: 'none',
						duration: 2000
					});
				}
			},

			// 计时器相关方法
			startTimer() {
				this.startTime = Date.now()
				this.callDuration = '00:00:00'
				this.durationTimer = setInterval(() => {
					const duration = Date.now() - this.startTime
					const hours = Math.floor(duration / 3600000)
					const minutes = Math.floor((duration % 3600000) / 60000)
					const seconds = Math.floor((duration % 60000) / 1000)

					this.callDuration =
						`${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
				}, 1000)
			},

			stopTimer() {
				if (this.durationTimer) {
					clearInterval(this.durationTimer)
					this.durationTimer = null
				}
			},

			// 调试NIM SDK服务
			debugNIMServices() {
				console.log('============ NIM SDK DEBUG ============')
				if (!this.nim) {
					console.log('NIM 实例不存在')
					return
				}

				console.log('NIM 顶级属性:', Object.keys(this.nim))

				// 检查各个服务
				const services = [
					'V2NIMService',
					'V2NIMLoginService',
					'V2NIMNotificationService',
					'V2NIMSystemNotificationService',
					'V2NIMEventService',
					'V2NIMChatService'
				]

				services.forEach(service => {
					if (this.nim[service]) {
						console.log(`${service} 存在，方法:`, Object.getOwnPropertyNames(this.nim[service]).filter(
							item => typeof this.nim[service][item] === 'function'
						))
					} else {
						console.log(`${service} 不存在`)
					}
				})

				console.log('======================================')
			},

			// 扬声器开关
			toggleSpeaker() {
				try {
					// 记录旧状态用于错误恢复
					const oldSpeakerState = this.isSpeakerOn;
					
					// 先更新UI状态
					this.isSpeakerOn = !this.isSpeakerOn
					console.log('尝试切换扬声器状态为:', this.isSpeakerOn ? '开启' : '关闭')
					
					if (this.engine) {
						// 主要方法: setEnableSpeakerphone
						if (typeof this.engine.setEnableSpeakerphone === 'function') {
							this.engine.setEnableSpeakerphone(this.isSpeakerOn)
							console.log('扬声器状态:', this.isSpeakerOn ? '已启用' : '已禁用')
							return;
						}
						
						// 替代方法1: setSpeakerphoneOn
						if (typeof this.engine.setSpeakerphoneOn === 'function') {
							this.engine.setSpeakerphoneOn(this.isSpeakerOn)
							console.log('使用setSpeakerphoneOn切换扬声器状态')
							return;
						}
						
						// 替代方法2: setAudioDevice
						if (typeof this.engine.setAudioDevice === 'function') {
							const deviceType = this.isSpeakerOn ? 'speakerphone' : 'earpiece';
							this.engine.setAudioDevice(deviceType)
							console.log('使用setAudioDevice切换扬声器状态为:', deviceType)
							return;
						}
						
						// 替代方法3: setDefaultAudioRouteToSpeakerphone (可能在某些SDK版本中存在)
						if (typeof this.engine.setDefaultAudioRouteToSpeakerphone === 'function') {
							this.engine.setDefaultAudioRouteToSpeakerphone(this.isSpeakerOn)
							console.log('使用setDefaultAudioRouteToSpeakerphone切换扬声器状态')
							return;
						}
						
						// 网易云信特有方法检查
						if (typeof this.engine.netcall === 'object' && 
							typeof this.engine.netcall.switchAudioOutputDevice === 'function') {
							const deviceId = this.isSpeakerOn ? 'default' : 'communications';
							this.engine.netcall.switchAudioOutputDevice(deviceId)
							console.log('使用netcall.switchAudioOutputDevice切换扬声器状态')
							return;
						}
						
						// 如果都不支持，显示警告并还原状态
						console.warn('找不到可用的扬声器控制方法，请尝试手动切换设备的扬声器')
						// 不要还原UI状态，让用户感知操作已执行，即使底层失败
						// this.isSpeakerOn = oldSpeakerState
						
						// 尝试使用通用Web Audio API方法 (可能仅在浏览器环境有效)
						try {
							if (typeof uni.getSystemInfoSync === 'function') {
								const sysInfo = uni.getSystemInfoSync();
								if (sysInfo.platform === 'web' || sysInfo.platform === 'devtools') {
									console.log('尝试使用Web Audio API切换音频输出...');
									// 这里的代码仅在Web环境下有效
									if (typeof navigator !== 'undefined' && navigator.mediaDevices) {
										console.log('使用Web API通知用户手动切换扬声器');
										uni.showToast({
											title: `已切换至${this.isSpeakerOn ? '扬声器' : '听筒'}模式`,
											icon: 'none'
										});
									}
								} else {
									// 移动端环境，尝试使用uni API
									uni.showToast({
										title: `已尝试切换至${this.isSpeakerOn ? '扬声器' : '听筒'}，请检查效果`,
										icon: 'none'
									});
								}
							}
						} catch (webAudioError) {
							console.error('尝试使用Web Audio API失败:', webAudioError);
						}
					}
				} catch (error) {
					console.error('切换扬声器失败:', error)
					// 还原状态 - 在严重错误时才还原
					this.isSpeakerOn = !this.isSpeakerOn
				}
			},

			// 修复safeHideModal方法，确保所有平台都能关闭弹窗
			safeHideModal() {
				try {
					this.showIncomingModal = false // 关闭自定义弹窗
					// 尝试使用uni.hideModal
					if (typeof uni.hideModal === 'function') {
						uni.hideModal()
					} else {
						// 兼容：强制关闭所有自定义弹窗
						// 1. 触发页面级变量控制弹窗显示（如有）
						if (this.$refs && this.$refs.uniModal) {
							// 如果有ref的modal组件
							this.$refs.uniModal.close && this.$refs.uniModal.close()
						}
						// 2. 触发全局事件关闭弹窗（如有全局事件总线）
						if (this.$emit) {
							this.$emit('close-modal')
						}
						// 3. 兼容自定义弹窗变量
						if (typeof this.showModal !== 'undefined') {
							this.showModal = false
						}
						// 4. 兼容uView等UI库
						if (this.$refs && this.$refs.uModal) {
							this.$refs.uModal.close && this.$refs.uModal.close()
						}
						// 5. 兼容uni.showModal的回调
						// uni.showModal没有返回关闭方法，只能依赖用户操作或页面变量
						// 可以考虑在弹窗回调中加上状态判断
						// 6. 兼容自定义弹窗组件
						// 如果你有自定义弹窗组件，确保有方法可以通过ref关闭
					}
				} catch (e) {
					console.warn('关闭模态框失败:', e)
				}
			},

			// 音频准备与初始化
			prepareAudioBeforeJoin() {
				try {
					// 针对不同平台使用不同的权限请求方法
					const platform = uni.getSystemInfoSync().platform
					
					// Android 平台使用 permision 辅助库
					if (platform === 'android') {
						// 使用已经导入的 permision 模块
						permision.requestAndroidPermission('android.permission.RECORD_AUDIO')
							.then(result => {
								console.log('麦克风权限状态:', result)
								if (result !== 1) { // 注意：Android权限返回1表示已授权
									uni.showToast({
										title: '需要麦克风权限进行通话',
										icon: 'none'
									})
								}
							})
							.catch(err => {
								console.error('请求麦克风权限失败:', err)
							})
					} 
					// iOS 平台
					else if (platform === 'ios') {
						// iOS通常在使用录音相关API时自动请求权限
						try {
							const recorderManager = uni.getRecorderManager()
							// 简单启动并立即停止录音，触发系统权限请求
							recorderManager.onStart(() => {
								console.log('触发录音以请求权限')
								recorderManager.stop()
							})
							recorderManager.onStop(() => {
								console.log('停止录音')
							})
							// 尝试短暂录音以触发权限请求
							recorderManager.start({
								duration: 100, // 极短持续时间
								format: 'mp3'
							})
						} catch (recorderError) {
							console.error('录音管理器初始化失败:', recorderError)
						}
					}
					
					// 初始化音频设置 - 使用数字常量
					if (this.engine) {
						// 设置音频参数
						this.engine.setAudioProfile({
							profile: 0, // 使用数字0替代字符串"DEFAULT"
							scenario: 0  // 使用数字0替代字符串"DEFAULT"
						})
						
						// 启用音频
						this.enableAudio()
					}
				} catch (error) {
					console.error('音频准备失败:', error)
				}
			},

			// 启用音频的独立方法
			enableAudio() {
				try {
					if (this.engine) {
						// 确保本地音频启用
						if (typeof this.engine.enableLocalAudio === 'function') {
							this.engine.enableLocalAudio({
								enable: true
							})
							console.log('本地音频已启用')
						}

						// 设置扬声器模式
						if (typeof this.engine.setEnableSpeakerphone === 'function') {
							this.engine.setEnableSpeakerphone(true)
							this.isSpeakerOn = true
							console.log('扬声器已启用')
						}

						// 取消静音
						if (this.isMuted && typeof this.engine.muteLocalAudioStream === 'function') {
							this.engine.muteLocalAudioStream(false)
							this.isMuted = false
							console.log('取消静音')
						}
					}
				} catch (error) {
					console.error('启用音频失败:', error)
				}
			},

			// 播放铃声
			playRingtone() {
				try {
					const innerAudioContext = uni.createInnerAudioContext()
					innerAudioContext.autoplay = true
					innerAudioContext.loop = true
					innerAudioContext.src = '/static/ringtone.mp3'
					this.ringtonePlayer = innerAudioContext
				} catch (error) {
					console.error('播放铃声失败:', error)
				}
			},

			// 停止铃声
			stopRingtone() {
				if (this.ringtonePlayer) {
					this.ringtonePlayer.stop()
					this.ringtonePlayer.destroy()
					this.ringtonePlayer = null
				}
			},

			// 添加状态变化记录
			logStateChange(action, oldIsInCall, newIsInCall) {
				const entry = {
					timestamp: new Date().toISOString(),
					action,
					oldState: oldIsInCall,
					newState: newIsInCall,
					stack: new Error().stack
				}
				console.log(`状态变化: ${action} - ${oldIsInCall} -> ${newIsInCall}`)
				this._stateChangeLog.push(entry)
			},

			// 添加防止错误重置的保护方法 - 用于检查RTC是否刚刚成功连接
			isRTCRecentlyJoined() {
				if (!this._rtcJoinedAt) return false;
				// 如果RTC连接成功后5秒内，视为刚刚连接，防止重置
				return (Date.now() - this._rtcJoinedAt) < 5000;
			},

			// 新增方法：初始化来电信息
			initIncomingCall() {
				try {
					console.log('初始化来电信息...');
					
					// 1. 尝试从URL参数获取
					let callId, from, channelName;
					const pages = getCurrentPages();
					const currentPage = pages[pages.length - 1];
					
					if (currentPage && currentPage.$page && currentPage.$page.fullPath) {
						const fullPath = currentPage.$page.fullPath;
						console.log('当前页面完整路径:', fullPath);
						
						// 解析URL参数
						const queryString = fullPath.split('?')[1];
						if (queryString) {
							const params = new URLSearchParams(queryString);
							callId = params.get('callId');
							from = params.get('from');
							channelName = params.get('channelName');
							console.log('从URL解析参数:', { callId, from, channelName });
						}
					}
					
					// 2. 尝试从本地存储获取
					if (!callId || !from) {
						try {
							const lastCallInfo = uni.getStorageSync('lastCallInfo');
							if (lastCallInfo && Date.now() - lastCallInfo.timestamp < 60000) { // 只使用1分钟内的数据
								callId = callId || lastCallInfo.callId;
								from = from || lastCallInfo.remoteUserId;
								channelName = channelName || lastCallInfo.channelName;
								console.log('从本地存储获取参数:', lastCallInfo);
							}
						} catch (e) {
							console.error('从本地存储获取参数失败:', e);
						}
					}
					
					// 3. 尝试从全局数据获取
					if (!callId || !from) {
						const app = getApp();
						if (app && app.globalData && app.globalData.callParams) {
							callId = callId || app.globalData.callParams.callId;
							from = from || app.globalData.callParams.from;
							channelName = channelName || app.globalData.callParams.channelName;
							console.log('从全局数据获取参数:', app.globalData.callParams);
						}
					}
					
					// 4. 尝试从事件总线获取
					uni.$once('incoming-call-params', (params) => {
						if (!callId || !from) {
							callId = callId || params.callId;
							from = from || params.from;
							channelName = channelName || params.channelName;
							console.log('从事件总线获取参数:', params);
							
							// 如果从事件总线获取到了参数，立即应用
							this.applyCallParams(callId, from, channelName);
						}
					});
					
					console.log('最终获取到的参数:', { callId, from, channelName });
					
					// 应用获取到的参数
					this.applyCallParams(callId, from, channelName);
					
				} catch (error) {
					console.error('初始化来电信息失败:', error);
				}
			},

			// 新增方法：应用通话参数
			applyCallParams(callId, from, channelName) {
				if (callId && from) {
					this.incomingCallInfo = {
						callId: callId,
						from: from,
						channelName: channelName || callId
					};
					this.currentCallId = callId;
					this.currentRemoteUserId = from;
					this.currentChannelName = channelName || callId;
					
					// 设置界面状态
					this.showIncomingModal = true;
					
					// 播放铃声
					this.playRingtone();
					
					
					
					console.log('已应用通话参数:', { 
						callId: this.currentCallId, 
						from: this.currentRemoteUserId, 
						channelName: this.currentChannelName 
					});
				} else {
					console.error('缺少必要的来电参数');
				}
			},

			onLoad(options) {
				console.log('call.vue onLoad, 参数:', options);
				
				// 保存参数
				if (options) {
					// 如果有callId和from参数，设置incomingCallInfo
					if (options.callId && options.from) {
						this.incomingCallInfo = {
							callId: options.callId,
							from: options.from,
							channelName: options.channelName || options.callId
						};
						
						// 同时设置当前通话信息
						this.currentCallId = options.callId;
						this.currentRemoteUserId = options.from;
						this.currentChannelName = options.channelName || options.callId;
						
						console.log('从onLoad参数设置来电信息:', this.incomingCallInfo);
						
						// 保存到本地存储
						uni.setStorageSync('lastCallInfo', {
							callId: options.callId,
							remoteUserId: options.from,
							channelName: options.channelName || options.callId,
							timestamp: Date.now()
						});
						
						// 显示来电弹窗
						this.showIncomingModal = true;
						
						// 播放来电铃声
						this.playRingtone();
						
						// 设置30秒超时
						this.callTimeoutTimer = setTimeout(() => {
							if (this.showIncomingModal) {
								this.rejectCall(options.callId, options.from);
								uni.showToast({
									title: '来电超时未接听',
									icon: 'none'
								});
							}
						}, 30000);
					}
				}
				
				// 获取用户信息
				const user = uni.getStorageSync('user');
				if (user && user.account) {
					this.myAccount = user.account;
					console.log('设置myAccount:', this.myAccount);
				}
			},

			// 备用消息发送方法 - 使用系统通知
			async sendSystemNotification(to, content) {
				try {
					if (!uni.$NIM) {
						throw new Error('NIM未初始化');
					}

					// 确保to参数有效
					if (!to) {
						throw new Error('接收方账号为空');
					}

					// 确保content是字符串
					const contentStr = typeof content === 'string' ? content : JSON.stringify(content);

					console.log(`尝试使用系统通知向 ${to} 发送消息:`, contentStr);

					// 尝试使用系统通知
					if (uni.$NIM.V2NIMSystemNotificationService &&
						typeof uni.$NIM.V2NIMSystemNotificationService.sendCustomNotification === 'function') {
						
						const result = await uni.$NIM.V2NIMSystemNotificationService.sendCustomNotification({
							receiver: to,
							content: contentStr,
							sendToOnlineUsersOnly: false,
							apnsText: '您有一个新的通话请求'
						});
						
						console.log('系统通知发送成功:', result);
						return result;
					}
					
					// 尝试使用消息服务
					if (uni.$NIM.V2NIMChatService && 
						typeof uni.$NIM.V2NIMChatService.sendMessage === 'function') {
						
						const message = {
							convId: `p2p-${to}`,
							content: contentStr,
							type: 100, // 自定义消息类型
							pushContent: '您有一个新的通话请求'
						};
						
						const result = await uni.$NIM.V2NIMChatService.sendMessage(message);
						console.log('聊天消息发送成功:', result);
						return result;
					}
					
					throw new Error('没有可用的消息发送方法');
					
				} catch (error) {
					console.error('备用消息发送失败:', error);
					throw error;
				}
			},

			// 检查NIM SDK信息
			checkNIMSDKInfo() {
				if (!uni.$NIM) {
					console.error('NIM SDK未初始化');
					return;
				}
				
				console.log('========= NIM SDK 信息 =========');
				
				// 检查版本
				if (uni.$NIM.version) {
					console.log('SDK版本:', uni.$NIM.version);
				} else {
					console.log('无法获取SDK版本');
				}
				
				// 检查可用服务
				const services = [
					'V2NIMLoginService',
					'V2NIMNotificationService',
					'V2NIMSystemNotificationService',
					'V2NIMChatService',
					'V2NIMEventService'
				];
				
				console.log('可用服务:');
				services.forEach(service => {
					if (uni.$NIM[service]) {
						console.log(`- ${service}: 可用`);
						
						try {
							// 检查服务中的方法 - 使用不同的方式获取方法列表
							const methods = [];
							for (const key in uni.$NIM[service]) {
								if (typeof uni.$NIM[service][key] === 'function') {
									methods.push(key);
								}
							}
							console.log(`  方法: ${methods.join(', ') || '无法获取方法列表'}`);
						} catch (e) {
							console.error(`获取${service}方法列表失败:`, e);
						}
					} else {
						console.log(`- ${service}: 不可用`);
					}
				});
				
				// 检查登录状态 - 不使用Promise方式调用
				if (uni.$NIM.V2NIMLoginService && typeof uni.$NIM.V2NIMLoginService.getLoginStatus === 'function') {
					try {
						const status = uni.$NIM.V2NIMLoginService.getLoginStatus();
						console.log('当前登录状态:', status);
					} catch (err) {
						console.error('获取NIM登录状态失败:', err);
					}
				}
				
				console.log('================================');
			},

			// 直接调用NIM API发送消息
			async sendDirectNotification(to, content) {
				try {
					if (!uni.$NIM) {
						throw new Error('NIM未初始化');
					}
					
					console.log('尝试直接调用NIM API发送消息');
					
					// 确保content是字符串
					const contentStr = typeof content === 'string' ? content : JSON.stringify(content);
					
					// 获取原始NIM对象
					const nim = uni.$NIM;
					
					// 尝试获取通知服务的原始实现
					const notificationService = nim.V2NIMNotificationService;
					
					if (notificationService) {
						// 检查服务内部结构
						console.log('通知服务内部结构:', Object.keys(notificationService));
						
						// 尝试获取发送方法
						const sendMethod = notificationService.sendCustomNotification;
						
						if (typeof sendMethod === 'function') {
							// 构造会话ID - 使用与callpage.vue相同的格式
							const conversationId = `${this.myAccount}|1|${to}`;
							
							// 直接调用方法
							console.log('直接调用sendCustomNotification方法');
							const result = sendMethod.call(notificationService, conversationId, contentStr, {});
							console.log('直接调用结果:', result);
							return result;
						} else {
							throw new Error('sendCustomNotification不是一个函数');
						}
					} else {
						throw new Error('无法获取通知服务');
					}
				} catch (error) {
					console.error('直接调用NIM API失败:', error);
					throw error;
				}
			},

			// 创建一个统一的页面导航方法
			navigateBack() {
				// 添加新的调试日志
				console.log('navigateBack被调用，当前状态:', {
					isNavigating: this.isNavigating,
					hasRejected: this.hasRejected,
					isInCall: this.isInCall,
					timestamp: new Date().toISOString()
				});
				
				// 如果已经在导航中，直接返回
				if (this.isNavigating) {
					console.log('===== 导航锁定中，忽略重复的navigateBack调用 =====');
					return;
				}
				
				// 设置导航标志 - 立即设置为true，严格防止重复调用
				this.isNavigating = true;
				
				// 添加安全计时器，保证导航状态会被重置
				const safetyTimer = setTimeout(() => {
					console.log('安全计时器触发: 强制重置导航状态');
					this.isNavigating = false;
				}, 8000); // 比锁定时间长一些，确保其他计时器已经执行完
				
				// 记录导航发起的调用堆栈，方便调试
				console.log('执行页面导航，调用堆栈:', new Error().stack);
				
				// 设置一个锁定计时器，至少5秒内不允许再次导航
				// 这保证了即使在多个setTimeout回调中也只会执行一次导航
				const lockNavigationTime = 5000; // 5秒锁定时间
				
				console.log('执行页面导航，并锁定导航', lockNavigationTime, 'ms');
				try {
					// 获取当前页面栈
					const pages = getCurrentPages();
					console.log('当前页面栈深度:', pages.length);
					
					// 检查是否有上一页可返回
					if (pages.length > 1) {
						// 有上一页，正常返回
						uni.navigateBack({
							delta: 1,
							success: () => {
								console.log('成功返回上一页');
								// 成功导航后清除安全计时器
								clearTimeout(safetyTimer);
							},
							fail: (err) => {
								console.error('返回上一页失败:', err);
								// 返回失败时重定向到首页
								this.navigateToHome();
							},
							complete: () => {
								// 导航完成后延迟重置标志，确保不会在短时间内重复导航
								setTimeout(() => {
									console.log('导航锁定时间结束，重置导航状态');
									this.isNavigating = false;
									// 完成后也清除安全计时器
									clearTimeout(safetyTimer);
								}, lockNavigationTime);
							}
						});
					} else {
						// 没有上一页，直接跳转到首页
						console.log('没有上一页可返回，跳转到首页');
						this.navigateToHome();
					}
				} catch (navError) {
					console.error('执行页面导航时出错:', navError);
					// 发生错误时尝试跳转到首页
					this.navigateToHome();
					
					// 即使发生错误，也要延迟重置导航状态
					setTimeout(() => {
						console.log('导航出错，延迟重置导航状态');
						this.isNavigating = false;
						// 错误处理完成后也清除安全计时器
						clearTimeout(safetyTimer);
					}, lockNavigationTime);
				}
			},

			// 跳转到首页的辅助方法
			navigateToHome() {
				// 添加调试日志
				console.log('navigateToHome被调用，当前状态:', {
					isNavigating: this.isNavigating,
					hasRejected: this.hasRejected,
					isInCall: this.isInCall,
					timestamp: new Date().toISOString()
				});
				
				// 检查是否已经在导航中，避免重复导航
				if (this.isNavigating) {
					console.log('===== 导航锁定中，忽略重复的navigateToHome调用 =====');
					return;
				}
				
				// 设置导航标志
				this.isNavigating = true;
				
				// 添加安全计时器，保证导航状态会被重置
				const safetyTimer = setTimeout(() => {
					console.log('安全计时器触发: 强制重置导航状态');
					this.isNavigating = false;
				}, 8000); // 比锁定时间长一些，确保其他计时器已经执行完
				
				// 记录导航发起的调用堆栈，方便调试
				console.log('执行首页导航，调用堆栈:', new Error().stack);
				
				// 延迟重置导航状态的时间与navigateBack一致
				const lockNavigationTime = 5000;
				
				uni.switchTab({
					url: '/pages/index/index',
					success: () => {
						console.log('成功跳转到首页');
						// 成功导航后清除安全计时器
						clearTimeout(safetyTimer);
					},
					fail: (err) => {
						console.error('switchTab到首页失败，尝试redirectTo:', err);
						// 如果switchTab失败，尝试redirectTo
						uni.redirectTo({
							url: '/pages/index/index',
							success: () => {
								console.log('成功通过redirectTo跳转到首页');
								clearTimeout(safetyTimer);
							},
							fail: (redirectErr) => {
								console.error('redirectTo到首页也失败:', redirectErr);
							}
						});
					},
					complete: () => {
						// 导航完成后延迟重置标志，与navigateBack保持一致
						setTimeout(() => {
							console.log('首页导航完成，延迟重置导航状态');
							this.isNavigating = false;
							// 完成后也清除安全计时器
							clearTimeout(safetyTimer);
						}, lockNavigationTime);
					}
				});
			},

			onUnload() {
				console.log('页面卸载，重置导航状态');
				this.isNavigating = false;
				this.hasRejected = false; // 重置拒绝标志
				
				// 确保通话相关资源已释放
				if (this.isInCall) {
					console.log('页面卸载时通话仍在进行，强制结束');
					this.endCall();
				}
			},

			// 初始化设备ID，确保设备标识一致
			initDeviceId() {
				try {
					// 从本地存储获取设备ID
					const storedDeviceId = uni.getStorageSync('device_id');
					if (storedDeviceId) {
						// 如果存在已保存的设备ID，使用它
						this.deviceInfo.id = storedDeviceId;
						console.log('从本地存储读取设备ID:', storedDeviceId);
					} else {
						// 如果没有保存过设备ID，生成一个新ID并保存
						const newDeviceId = `设备-${Date.now().toString().slice(-6)}-${Math.floor(Math.random() * 1000)}`;
						this.deviceInfo.id = newDeviceId;
						uni.setStorageSync('device_id', newDeviceId);
						console.log('生成并保存新设备ID:', newDeviceId);
					}
				} catch (error) {
					console.error('初始化设备ID出错:', error);
					// 出错时使用默认ID
					this.deviceInfo.id = `设备-${Date.now().toString().slice(-6)}`;
				}
			},
		},

		async mounted() {
			try {
				// 初始化设备ID
				this.initDeviceId();
				console.log('当前设备ID:', this.deviceInfo.id);
				
				// 获取保存的账号信息
				const user = uni.getStorageSync('user');
				console.log('user', user);
				const savedAccount = user && user.account;
				
				if (savedAccount) {
					this.myAccount = savedAccount;
					
					// 自动登录NIM
					console.log('尝试自动登录NIM...');
					await this.calllogin();
					
					// 检查NIM SDK信息
					setTimeout(() => {
						this.checkNIMSDKInfo();
					}, 1000);
				} else {
					console.warn('未找到用户账号信息，无法自动登录');
				}
			} catch (error) {
				console.error('mounted生命周期出错:', error);
			}
			
			// 打印incomingCallInfo
			console.log('初始incomingCallInfo:', this.incomingCallInfo);
			
			// 检查URL参数
			const pages = getCurrentPages();
			const currentPage = pages[pages.length - 1];
			if (currentPage && currentPage.$page) {
				console.log('当前页面参数:', currentPage.$page.options);
				
				// 如果URL中有callId和from参数，直接设置incomingCallInfo
				const options = currentPage.$page.options;
				if (options.callId && options.from) {
					this.incomingCallInfo = {
						callId: options.callId,
						from: options.from,
						channelName: options.channelName || options.callId
					};
					
					// 同时设置当前通话信息
					this.currentCallId = options.callId;
					this.currentRemoteUserId = options.from;
					this.currentChannelName = options.channelName || options.callId;
					
					console.log('从URL参数设置来电信息:', this.incomingCallInfo);
					
					// 显示来电弹窗
					this.showIncomingModal = true;
					
					// 播放来电铃声
					this.playRingtone();
				}
			}
			
			// 检查本地存储的最近通话信息
			try {
				const lastCallInfo = uni.getStorageSync('lastCallInfo');
				if (lastCallInfo && Date.now() - lastCallInfo.timestamp < 60000) { // 只使用1分钟内的数据
					console.log('本地存储的最近通话信息:', lastCallInfo);
				}
			} catch (e) {
				console.error('获取本地存储通话信息失败:', e);
			}
		},

		beforeDestroy() {
			this.stopTimer()

			// 清理RTC引擎
			if (this.engine) {
				this.engine.removeAllEventListener()
				this.engine.destroyEngine()
				this.engine = null
			}

			// 登出NIM (注意：我们不销毁全局实例，只是登出)
			if (uni.$NIM && this.nimConnected) {
				uni.$NIM.V10NIMLoginService.logout().catch(err => {
					console.warn('登出失败:', err)
				})
			}

			// 在销毁前确保通话已结束
			if (this.isInCall) {
				console.log('组件销毁前发现通话仍在进行，尝试挂断')
				this.hangUp()
			}
		},

		watch: {
			isInCall(newVal, oldVal) {
				console.log(`isInCall changed: ${oldVal} -> ${newVal}`)
				this.logStateChange('isInCall changed', oldVal, newVal)
				
				// 如果变为false，记录调用栈以便调试
				if (newVal === false && oldVal === true) {
					console.warn('通话状态从true变为false，调用栈:', new Error().stack)
				}
			}
		}
	}
</script>

<style scoped>
.call-component {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  /* Make sure nothing is hidden */
  pointer-events: auto;
}

/* Ensure incoming call UI is highly visible */
.incoming-call {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(33, 33, 33, 0.98);
  z-index: 10000; /* Higher z-index than other elements */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

/* 通话UI样式 */
.calling-ui {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #151515;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.calling-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 88rpx;
}

.call-title {
  font-size: 34rpx;
  color: rgba(255,255,255,0.85);
}

.device-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.device-image {
  width: 450rpx;
  height: 450rpx;
  margin-bottom: 24rpx;
}

.device-id {
  font-size: 44rpx;
  color: rgba(255,255,255,0.85);
  margin: 16rpx 0;
  margin-top: 140rpx;
}

.call-state {
  font-size: 30rpx;
  color: #bdbdbd;
  margin-bottom: 70rpx;
}

.call-timer {
  font-size: 32rpx;
  color: #aaaaaa;
  margin-top: 40rpx;
}

.calling-controls {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 10vh;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-circle1 {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  background-color: #424242;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.btn-circle1 image {
  width: 100%;
  height: 100%;
}

.active-btn {
  background-color: #0091EA;
}

.hangup-btn {
  background-color: #f44336;
}

.control-btn text {
  font-size: 24rpx;
  color: #e0e0e0;
}

.incoming-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 15vh;
}

.incoming-title {
  font-size: 48rpx;
  color: rgba(255,255,255,0.85);
  margin-bottom: 80rpx;
}

.incoming-controls {
  display: flex;
  justify-content: center;
  gap: 200rpx;
  margin-bottom: 15vh;
}

.incoming-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.reject-btn {
  background-color: #f44336;
}

.accept-btn {
  background-color: #4CAF50;
}

.incoming-btn text {
  font-size: 32rpx;
  color: rgba(255,255,255,0.85);
  margin-top: 20rpx;
}
</style> 