<template>
	<custom-header style="height: 88rpx;" title="日志详情" showBack />
	<view class="equip_content">
		<view class="equip_header">
			<text class="header_text">{{prolist.deviceCode}}</text>
        <view class="headerfl">
			<view class="header_flex">
				<view>
					<view class="flex_label">资源</view>
					<view class="flex_value">{{prolist.deviceName}}</view>
				</view>
	
			</view>
			<view class="header_flex" style="margin-left:120rpx ;">
				<view>
					<view class="flex_label">远端地址</view>
					<view class="flex_value">{{prolist.deviceModel}}</view>
				</view>
				
			</view>
			
			
			</view>
		</view>
		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        :list="list4"
        lineWidth="0"
        lineColor="#177DDC"
        :activeStyle="{
            color: 'rgba(255,255,255,0.85)',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:10rpx 15px; padding-right: 15px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		
		<template v-if="currentTab == 0">
			<view class="content">
			 <view class="cont_flex">
			 	<view class="label">变更字段</view>
				<view class="value">变更前</view>
			 	<view class="value">变更后</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">用户</view>
				<view class="value">空</view>
			 	<view class="value">{{prolist.deviceCode}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">登录ID</view>
				<view class="value">空</view>
			 	<view class="value">{{prolist.deviceModel}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">登录日期</view>
			 	<view class="value">空</view>
			 	<view class="value">{{prolist.configurationState}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">登录方式</view>
			 	<view class="value">空</view>
			 	<view class="value">{{prolist.type}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">动作</view>
			 	<view class="value">空</view>
			 	<view class="value">{{prolist.currentState}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">资源模块</view>
			 	<view class="value">空</view>
			 	<view class="value">{{prolist.creatAt}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">操作内容</view>
			 	<view class="value">空</view>
			 	<view class="value">{{prolist.cont}}</view>
			 </view>
			 </view>
		</template>
		<template v-if="currentTab == 1">
			<!-- <control-cont></control-cont> -->
		</template>
		<template v-if="currentTab == 2">
			<!-- <history-vue></history-vue> -->
		</template>
		<template v-if="currentTab == 3">
			<!-- <keep-vue></keep-vue> -->
		</template>
		<template v-if="currentTab == 4">
			<!-- <lifetrack-vue></lifetrack-vue> -->
		</template>
		<template v-if="currentTab == 5">
			<!-- <transfer-vue></transfer-vue> -->
		</template>
	
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			
		},
		data(){
			return{
				currentTab:0,
				id: null,
			list4:[  
			    { name: '操作日志' },  
			    // { name: '状态监控' },  
			    // { name: '历史记录' },  
			    // { name: '保养计划记录' },  
			    // { name: '设备寿命跟踪' },  
			    // { name: '设备转移记录' }
			],
			prolist:{
				deviceCode: "徐星辰",
				deviceName: "徐星辰(*********)",
				deviceModel: "*********",
				type:'web',
				configurationState: "2024-12-09 09:37:37",
				currentState: "创建",
				creatAt: "打孔管理",
				cont:"修改“打孔管理”"
			}
			}
		},
		onLoad(options) {
			this.id = options.id
			// this.handelDetail()
		},
		onShow(){
			if (this.id) {
				// this.handelDetail()
			}
		},
		methods:{
			handleTabChange(index) {
				// console.log("indexaaa",index.index);
				this.currentTab = index.index; // 更新当前选中的标签索引
				// console.log(this.currentTab);
			},
			async handelDetail() {
			
				try {
			
					const res = await Request.get('/rod/get_info', {
						id: this.id
					})
			
					if (res.status == 0) {
						console.log('返回数据', res);
						this.prolist = res.data;
			
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });
			
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
			
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			
			},
					
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	.equip_content{
		padding:0 32rpx ;
		padding-top: 1rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.tab_cont{
		border-radius: 12rpx;
		// padding: 0 32rpx;
		margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx  12rpx 0 0;
		padding:0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		
	}
	.content{
		border-radius:  0 0 12rpx  12rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		// height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		// justify-content: space-between;
		view{
			flex:1;
		}
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>