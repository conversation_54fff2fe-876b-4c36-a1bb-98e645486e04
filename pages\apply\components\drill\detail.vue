<template>
	<custom-header style="height: 88rpx;" title="钻具详情" showBack />
	<view class="equip_content">
		<view class="equip_header">
			<text class="header_text">{{prolist.title}}</text>
        <view class="headerfl">
			<view class="header_flex">
				<view>
					<view class="flex_label">钻具序列号</view>
					<view class="flex_value">{{prolist.serialNum}}</view>
				</view>
				<view>
					<view class="flex_label">规格类型</view>
					<view class="flex_value">{{prolist.model}}</view>
				</view>
			</view>
			<view class="header_flex" style="margin-left:120rpx ;">
				<view>
					<view class="flex_label">钻具类型</view>
					<view class="flex_value">{{prolist.specs}}</view>
				</view>
				<view>
					<view class="flex_label">使用状态</view>
					<view class="flex_value" v-if="prolist.status==0" style="color:#7a1b2a;">未使用</view>
					<view class="flex_value" v-if="prolist.status==1" style="color: #177DDC;">已使用</view>
				</view>
			</view>
			</view>
		</view>
		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        :list="list4"
        lineWidth="0"
		
        lineColor="#177DDC"
        :activeStyle="{
            color: 'rgba(255,255,255,0.85)',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:10rpx 15px; padding-right: 15px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		
		<template v-if="currentTab == 0">
			<view class="content">
			 <view class="cont_flex">
			 	<view class="label">使用寿命</view>
				<view class="value" style="color: #177DDC;">{{prolist.lifespan}}%</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">制造日期</view>
			 	<view class="value">{{prolist.productionTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">购置日期</view>
			 	<view class="value">{{prolist.purchaseTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">上次使用日期</view>
			 	<view class="value">{{prolist.lastUsedTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">下次维护日期</view>
			 	<view class="value">{{prolist.nextRepaireTime}}</view>
			 </view>
			 </view>
		</template>
		<template v-if="currentTab == 1">
			<!-- <control-cont></control-cont> -->
		</template>
		<template v-if="currentTab == 2">
			<!-- <history-vue></history-vue> -->
		</template>
		<template v-if="currentTab == 3">
			<!-- <keep-vue></keep-vue> -->
		</template>
		<template v-if="currentTab == 4">
			<!-- <lifetrack-vue></lifetrack-vue> -->
		</template>
		<template v-if="currentTab == 5">
			<!-- <transfer-vue></transfer-vue> -->
		</template>
	
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			
		},
		data(){
			return{
				currentTab:0,
				id: null,
			list4:[  
			    { name: '其他信息' },  
			    // { name: '状态监控' },  
			    // { name: '历史记录' },  
			    // { name: '保养计划记录' },  
			    // { name: '设备寿命跟踪' },  
			    // { name: '设备转移记录' }
			],
			prolist:{
				// title:'三牙钻头',
				// serialNum:'ID345678212',
				// specs:'GD323-5',
				// model:'ID5253636626',
				// status:0,
				// lifespan:50,
				// productionTime:"2024-12-09 09:39:37",
				// purchaseTime:"2024-12-09 09:44:31",
				// lastUsedTime:"2024-12-09 09:39:37",
				// nextRepaireTime:"2024-12-09 09:44:31"
			}
			}
		},
		onLoad(options) {
			this.id = options.id
			// this.handelDetail()
		},
		onShow(){
			if (this.id) {
				this.handelDetail()
			}
		},
		methods:{
			handleTabChange(index) {
				// console.log("indexaaa",index.index);
				this.currentTab = index.index; // 更新当前选中的标签索引
				// console.log(this.currentTab);
			},
			async handelDetail() {
			
				try {
			
					const res = await Request.get('/rod/get_info', {
						id: this.id
					})
			
					if (res.status == 0) {
						console.log('返回数据', res);
						this.prolist = res.data;
			
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });
			
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
			
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			
			},
					
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	.equip_content{
		padding:0 32rpx ;
		padding-top: 156rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.tab_cont{
		border-radius: 12rpx;
		// padding: 0 32rpx;
		margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx  12rpx 0 0;
		padding:0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		
	}
	.content{
		border-radius:  0 0 12rpx  12rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>