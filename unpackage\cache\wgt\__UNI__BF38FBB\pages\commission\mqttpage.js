"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var _=Object.create;var l=Object.defineProperty;var g=Object.getOwnPropertyDescriptor;var m=Object.getOwnPropertyNames;var f=Object.getPrototypeOf,d=Object.prototype.hasOwnProperty;var y=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var x=(e,t,o,a)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of m(t))!d.call(e,r)&&r!==o&&l(e,r,{get:()=>t[r],enumerable:!(a=g(t,r))||a.enumerable});return e};var b=(e,t,o)=>(o=e!=null?_(f(e)):{},x(t||!e||!e.__esModule?l(o,"default",{value:e,enumerable:!0}):o,e));var i=y((h,p)=>{p.exports=Vue});var s=b(i());var u=(e,t)=>{let o=e.__vccOpts||e;for(let[a,r]of t)o[a]=r;return o};var v={container:{"":{flex:1,paddingTop:30,paddingRight:30,paddingBottom:30,paddingLeft:30}},"status-text":{"":{fontSize:18,marginBottom:20}},"message-text":{"":{fontSize:16,color:"#333333"}}},w={};function S(e,t){return(0,s.openBlock)(),(0,s.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,s.createElementVNode)("div",{class:"container"},[(0,s.createElementVNode)("u-text",{class:"status-text"},"MQTT\u72B6\u6001: "+(0,s.toDisplayString)(e.connectionStatus),1),e.lastMessage?((0,s.openBlock)(),(0,s.createElementBlock)("u-text",{key:0,class:"message-text"},"\u6700\u65B0\u6D88\u606F: "+(0,s.toDisplayString)(e.lastMessage),1)):(0,s.createCommentVNode)("",!0)])])}var n=u(w,[["render",S],["styles",[v]]]);var c=plus.webview.currentWebview();if(c){let e=parseInt(c.id),t="pages/commission/mqttpage",o={};try{o=JSON.parse(c.__query__)}catch(r){}n.mpType="page";let a=Vue.createPageApp(n,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:t,__pageQuery:o});a.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...n.styles||[]])),a.mount("#root")}})();
