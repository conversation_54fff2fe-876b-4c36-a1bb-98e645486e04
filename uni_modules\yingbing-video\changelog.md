## 1.6.9（2024-10-10）
* 修复动态设置属性无效的问题
* 增加双击屏幕播放/暂停和控制开关属性
* 增加长按屏幕倍速播放和控制开关属性
## 1.6.8（2024-09-19）
* 修复设置部分属性无效的问题
## 1.6.7（2024-08-26）
* 修复设置高度不生效的问题
* 修复主动调用全屏方法不生效的问题
## 1.6.6（2024-06-24）
* 修复初始化传入视频链接无反应的问题
## 1.6.5（2024-06-21）
* 修复vue3app端全屏，时间显示格式异常的问题
* 修复部分视频切换视频加载错误的问题
## 1.6.4（2024-05-11）
* 修复timeShow设为fasle后进度条不走的问题
* 修复barrages改变后弹幕不刷新的问题
* 引入hls.mjs文件
## 1.6.3（2024-04-22）
* 修复vue3和vue2的renderJS环境watcher触发时间差异引起的问题
## 1.6.2（2024-04-22）
* 修复app报错的问题
## 1.6.1（2024-04-18）
* 修复vue3切换页面会显示多个控制栏的问题
## 1.6.0（2024-04-10）
* drawBarrage增加多条弹幕绘制
* ybplayer.js和ybbarrage.js文件使用import引入
## 1.5.9（2024-04-01）
* 升级flv.min.js到1.6.2
* 升级hls.min.js 到1.5.7
* 添加flv.js的监听事件若干
## 1.5.8（2024-03-22）
* 因为许多人反映flv会有闪烁的问题，所以将flv的重连代码删除，改为抛出statisticsinfo事件
* 增加durationChange事件
* 修复播放直播时，能获取到播放时长的问题
## 1.5.7（2024-03-12）
* 修复app端，error事件无法抛出的问题
* 添加barrageChange事件
## 1.5.6（2024-03-08）
* 修复倍速设置初始化时无效的问题
* 修复视频（直播流卡住事件）不起作用的问题
## 1.5.5（2024-03-02）
* 添加vue3兼容（因为vue3不能引入非标准模块的js文件，插件引入js的方法改为script引入，导致h5不能使用history路由模式）
* 修复h5画中画设置按钮不能自动改变状态的问题
## 1.5.4（2024-03-01）
* 增加reload重加载方法
* 增加seizing直播流播放卡住事件
## 1.5.3（2023-12-27）
* 修复动态设置controls无效的问题
## 1.5.2（2023-11-30）
* 修复controls和timeShow同时设为false时，会报错的问题
* 修改css文件引入方式
## 1.5.1（2023-11-24）
* 修改flvConfig中部分配置初始值为空，解决无法自动判断的问题
## 1.5.0（2023-11-07）
* 修复动态变更控制栏静音按钮、设置按钮、全屏按钮后，点击事件无效的问题
## 1.4.9（2023-09-15）
* 解决3.8.12版本或相近版本的编辑器会出现_typeof is not defined的报错的问题
## 1.4.8（2023-09-11）
* 不再通过script引入文件
## 1.4.7（2023-08-18）
* 解决mp4格式视频preload设置无效的问题
## 1.4.6（2023-08-16）
* 解决APP初始化时显示播放图标的问题
## 1.4.5（2023-08-15）
* 解决H5端播放m3u8视频，页面销毁时还继续请求的问题
## 1.4.4（2023-08-15）
* 隐藏APP端视频初始化时的播放图标
* 解决视频初始化时进度条在中间的问题
* 优化进度条样式，使其与上方控制按钮对齐
## 1.4.3（2023-08-10）
* 修复rageChange，和volumeChange事件监听无效的问题
## 1.4.2（2023-08-03）
* 解决H5端退出页面再进入会报错的问题
* 增加页面销毁时自动销毁视频
## 1.4.1（2023-07-17）
* 取消resize事件
* 优化窗口大小改变的监听逻辑
* 优化初始化方法，避免报错
* isLive为true关闭进度条
## 1.4.0（2023-07-08）
* 解决切换src后，插槽点击事件失效的问题
## 1.3.9（2023-07-06）
* 修复设置duration时，loop属性无效的问题
* 优化初始化代码，解决视频组件无法循环遍历的问题
## 1.3.8（2023-07-05）
* 优化组件结构，解决层级问题
## 1.3.7（2023-07-04）
* 修复设置项无法点击的问题
* 优化封面
## 1.3.6（2023-07-04）
* 增加enableBlob属性
* 修复插槽内容会被遮挡的问题
## 1.3.5（2023-06-30）
* 增加播放按钮、播放时间、静音按钮、设置按钮、全屏按钮的显示控制
* 新增duration属性设置播放时长
* 将flvConfig属性hasVideo默认值设为true
* 修复封面闪烁消失的问题
* 不设置高度时，组件自适应高度
## 1.3.4（2023-06-08）
* 修复直接给组件赋值src，会报跨域的问题
## 1.3.3（2023-04-26）
* flvConfig 新增几种属性配置
## 1.3.2（2023-04-24）
* 修复设置progressShow导致控制栏重叠的问题
* 修复initialTime设置不生效的问题
* 新增canplaythrough、loadeddata、loadstart事件
## 1.3.1（2023-04-22）
* 新增crossOrigin属性
* 修复h5可直接播放的视频还会跨域的问题
## 1.3.0（2023-04-20）
* 修复配置progressShow属性不生效的问题
## 1.2.9（2023-04-18）
* 修复objectFit不生效得问题
## 1.2.8（2023-04-08）
* 修复全屏时切换视频，顶部title不显示的问题
## 1.2.7（2023-04-08）
* 优化视频切换
## 1.2.6（2023-04-06）
* 新增title属性，用于全屏时顶部标题展示
* 新增prevBtnShow、nextBtnShow属性，用于显示切换上一个或下一个视频按钮
* 新增prevBtnClick、nextBtnClick事件，用于切换上一个或下一个视频
## 1.2.5（2023-04-01）
* 修复YBPlayer可能会报错的问题
* 修复FLV无法播放的问题
## 1.2.4（2023-03-31）
* 添加controlsChange事件
* 修复拖动进度条抖动的问题
## 1.2.3（2023-03-27）
* 新增一种控制栏插槽，该插槽内容会随控制栏一起消失/显示
## 1.2.2（2023-03-26）
* 修复动态添加视频组件无法播放的问题
## 1.2.1（2023-03-25）
* 修复创建多个视频组件会冲突的问题
## 1.2.0（2023-03-24）
* 优化设置菜单点击反馈异常的问题
## 1.1.9（2023-03-24）
* 将组件js化，方便用于普通html
* 新增截图功能
* 新增设置菜单控制属性
* 取消原本的npm安装模块的方式，现在所需要的库已全部集成
## 1.1.8（2023-03-16）
* 新增stop方法用于注销视频
## 1.1.7（2023-03-16）
* fullscreenChange事件新增type属性根据此属性可判断全屏是否使用css模拟
## 1.1.6（2023-03-16）
* 优化全屏事件
## 1.1.5（2023-03-15）
* 优化模块加载，可以根据需求自己下载相应模块
* canplay事件和loadmetadata事件新增视频宽高信息
* 修复方法名错误的问题
## 1.1.4（2023-03-11）
* 更改switchFullscreen方法名称
## 1.1.3（2023-03-11）
* 重构视频播放器，减少不必要的功能
* 短视频功能取消
* 简化控制栏
* 播放列表功能取消，现在更接近原生VIDEO
* 新增播放flv功能
* 更改部分属性、事件、方法的名称
## 1.1.2（2022-12-17）
* 对于不支持全屏api的系统， 使用css来模拟全屏，该全屏方法限制较多，效果不如全屏api好，如果有更好办法的小伙伴，希望不吝赐教
* 注意：使用css模拟全屏需要关闭原生导航栏，且在tabbar页面全屏时，需要手动关闭原生tabbar
## 1.1.1（2022-10-12）
* 优化app端的全屏模式
* 短视频模式添加全屏按钮
* 注意新的全屏模式需要高版本编辑器和设备支持，我用的是3.4.7的编辑器和安卓7.1.2的模拟器，大家可以以此为参考
## 1.1.0（2022-09-24）
* 修复上次更新的bug
* 去掉高版本编辑器报错
## 1.0.9（2022-09-23）
* 优化判断浏览器是否能播放m3u8格式视频
## 1.0.8（2022-09-23）
* 解决高版本hbuilderX会报错的问题
* 优化不同浏览器的播放功能，实现同步播放
* 解决ios端无法播放的问题
* 修复暂停播放等操作会触发弹幕初始化的问题
## 1.0.7（2022-08-17）
* 修复播放倍率设置不生效的问题
## 1.0.6（2022-08-17）
* 更改内部使用组件的名称(用在自己的项目上才发现组件名冲突了,没有冲突的小伙伴可以不下载)
## 1.0.5（2022-08-17）
* 修复播放m3u8格式视频后，继续播放其它格式视频异常的bug
## 1.0.4（2022-07-22）
* 新增控制弹幕显示上下间距属性
## 1.0.3（2022-07-21）
* 短视频模式新增关闭自定义内容按钮
* 优化一些内容
## 1.0.2（2022-07-21）
* 修复一些bug
## 1.0.1（2022-07-21）
* 短视频模式新增模糊背景显示
* 删除短视频模式下的全屏按钮
## 1.0.0（2022-07-20）
* 发布第一版，使用前请看使用须知
