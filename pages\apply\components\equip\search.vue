<template>
	<custom-header style="height: 88rpx;" title="筛选设备" showBack />
	<view class="content">
		<view>
			<up-action-sheet 
			:show="showCode" 
			:actions="actions" 
			title="请选择设备型号" 
			class="sheet" 
			@close="showCode = false"
				@select="Select">
			</up-action-sheet>
			<up-action-sheet 
			:show="showStatus" 
			:actions="status" 
			title="请选择设备状态" 
			class="sheet"
			@close="showStatus = false" 
			@select="SelectStatus">
			</up-action-sheet>
			<up-action-sheet 
			:show="showSort" 
			:actions="create" 
			title="请选择创建日期排序" 
			class="sheet"
				@close="showSort = false" @select="SelectSort">
			</up-action-sheet>
			<!-- <up-button @click="show = true">打开</up-button> -->
		</view>
		<view class="menu-list">
			<view class="menu-item" @click="showCode = true">
				<view class="menu-item-right">
					<text class="menu-text">设备型号</text>

					<view class="menu-item-content">
						<text class="menu-item-text" v-if="deviceModel==''">请选择设备</text>
						<text class="menu-item-text">{{deviceModel}}</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="showStatus = true">
				<view class="menu-item-right">
					<text class="menu-text">设备状态</text>

					<view class="menu-item-content">
						<text class="menu-item-text" v-if="currentState==0">打开</text>
						<text class="menu-item-text" v-if="currentState==1">关闭</text>
						<text class="menu-item-text" v-if="currentState==2">损坏</text>
						<text class="menu-item-text" v-if="currentState==null">请选择设备状态</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="showSort = true">
				<view class="menu-item-right">
					<text class="menu-text">创建日期排序</text>
					<view class="menu-item-content">
						<text class="menu-item-text" v-if="sort==0">最新创建</text>
						<text class="menu-item-text" v-if="sort==1">最早创建</text>
						<text class="menu-item-text" v-if="sort==null">请选择日期排序</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>

		</view>
		<view class="search_btn">
			<button class="btn1" @click="cancle">取消</button>
			<button class="btn2" @click="confirm">确定</button>
		</view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue';
	import Quene from '@/components/utils/queue';
	import provinceData from '@/static/area/province.json'
	import cityData from '@/static/area/city.json'
	import config from '@/components/utils/config';
	import Request from '@/components/utils/request';
	export default {
		components: {
			customHeader
		},

		data() {
			return {
				showCode: false,
				showStatus: false,
				showSort: false,
				actions: [{
						name: '型号1'
					},
					{
						name: '型号2'
					},
					{
						name: '型号3'
					},
				],
				status: [{
						name: '打开',
						value: 0
					},
					{
						name: '关闭',
						value: 1
					},
					{
						name: '损坏',
						value: 2
					},
				],
				create: [{
						name: '最新创建',
						value: 0
					},
					{
						name: '最早创建',
						value: 1
					},
				],

				deviceModel: '',
				currentState: null,
				sort: null,

			}
		},
		computed: {

		},
		onShow() {


		},
		methods: {

			cancle() {
				uni.navigateBack()
			},
			confirm() {
				// 发送事件通知列表页面更新数据
				uni.$emit('updateEquipList', {
					deviceModel: this.deviceModel,
					sort: this.sort,
					currentState: this.currentState
				});

				// 返回上一页
				uni.navigateBack();
			},
			Select(e) {
				// console.log(e);
				this.deviceModel = e.name
			},
			SelectStatus(e) {
				// console.log(e);
				this.currentState = e.value
			},
			SelectSort(e) {
				// console.log(e);
				this.sort = e.value
			},

		}

	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	.content {
		padding: 0 32rpx;
		padding-top: 32rpx;
		// position: relative;
	}

	.menu-list {
		// background: rgba(255, 255, 255, 0.0362);
		box-sizing: border-box;
		// border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 12rpx;
	}

	.menu-item {
		display: flex;
		background: rgba(255, 255, 255, 0.08);
		margin-bottom: 32rpx;
		border-radius: 8rpx;
		height: 116rpx;
		flex-direction: row;
		align-items: center;
		padding-left: 32rpx;
	}

	.avatar {
		width: 56rpx;
		height: 56rpx;
		border-radius: 8rpx;
	}

	.menu-item-left {
		display: flex;
		align-items: center;
	}

	.menu-item-right {
		flex: 1;
		height: 100%;
		padding-right: 32rpx;
		flex-direction: row;
		text-align: center;
		// padding-top: 32rpx;
		align-items: center;
		display: flex;
		// border-bottom: 1rpx solid rgba(167, 169, 172, 0.15);
		justify-content: space-between;
	}

	.menu-item:last-child .menu-item-right {
		border-bottom: none;
	}

	.menu-item-content {
		flex-direction: row;
		height: 100%;
		display: flex;
		align-items: center;

		text {
			color: rgba(255, 255, 255, 0.65);
			align-items: center;
			text-align: center;
			font-size: 30rpx;
			line-height: 58rpx;
		}
	}

	.menu-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 32rpx;
	}

	.menu-text {
		font-family: PingFang SC;
		font-size: 28rpx;
		letter-spacing: 0px;
		font-weight: normal;
		color: rgba(255, 255, 255, 0.85);
	}

	.arrow-right {
		width: 60rpx;
		height: 60rpx;
	}

	.search_btn {
		position: fixed;
		bottom: 30px;
		display: flex;

		button {
			width: 328rpx;
		}
	}

	:deep(.u-popup__content) {
		background: #16171b !important;

		.u-action-sheet__header__title {
			color: rgba(255, 255, 255, 0.6);
		}

		.u-action-sheet__item-wrap__item__name {
			color: rgba(255, 255, 255, 0.6);
		}

		.u-line {
			border-bottom: 1rpx rgba(214, 215, 217, 0.8) solid !important;
		}
	}

	.btn1 {
		margin-right: 30rpx;
		color: rgba(255, 255, 255, 0.85);
		background: rgba(255, 255, 255, 0.08);
	}

	.btn2 {
		color: rgba(255, 255, 255, 0.85);
		background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	}
</style>