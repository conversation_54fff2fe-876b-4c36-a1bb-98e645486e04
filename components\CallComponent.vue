<template>
  <view class="call-component" v-if="visible">
    <!-- 通话UI -->
    <view class="calling-ui" 
      v-if="isInCall || callStatus === '正在呼叫...'"
      :style="{display: (isInCall || callStatus === '正在呼叫...') ? 'flex' : 'none'}"
    >
      <view class="calling-header">
        <!-- 状态标题 -->
        <text class="call-title">通讯呼叫</text>

        <!-- 设备图标与ID -->
        <view class="device-info">
          <text class="device-id">{{this.myAccount}}</text>
          <text class="call-state">{{isInCall ? '通话中' : '呼叫中...'}}</text>
          <image class="device-image" :src="deviceInfo.image" />
        </view>

        <!-- 通话时长 -->
        <text class="call-timer" v-if="isInCall">{{callDuration}}</text>
      </view>

      <!-- 通话控制按钮 -->
      <view class="calling-controls">
        <view class="control-btn" @tap="toggleSpeaker">
          <view class="btn-circle1" >
            <image style="width: 100%;height: 100%;" :src="isSpeakerOn ? '/static/mic_off.png' : '/static/mic_on.png'" />
          </view>
          <text>{{ !isSpeakerOn ? '听筒' : '扬声器' }}</text>
        </view>

        <view class="control-btn hangup" @tap="hangUp">
          <view class="btn-circle1 hangup-btn">
            <image style="width: 100%;height: 100%;" src="/static/hangup.png" />
          </view>
          <text>挂断</text>
        </view>

        <view class="control-btn" @tap="toggleMute">
          <view class="btn-circle1" :class="{ 'active-btn': isMuted }">
            <image style="width: 100%;height: 100%;" :src="!isMuted ? '/static/speaker_off.png' : '/static/speaker_on.png'" />
          </view>
          <text>{{ isMuted ? '取消静音' : '静音' }}</text>
        </view>
      </view>
    </view>

    <!-- 来电UI弹窗 -->
    <view class="incoming-call" v-if="showIncomingModal">
      <view class="incoming-header">
        <text class="incoming-title">来电呼叫</text>

        <!-- 设备信息 -->
        <view class="device-info">
          <image class="device-image" :src="deviceInfo.image" />
          <text class="device-id">{{incomingCallInfo ? incomingCallInfo.from : '未知设备'}}</text>
          <text class="call-state">邀请你语音通话</text>
        </view>
      </view>

      <!-- 来电操作按钮 -->
      <view class="incoming-controls">
        <view class="incoming-btn reject" @tap="() => rejectCall(incomingCallInfo.callId, incomingCallInfo.from)">
          <view class="btn-circle1 reject-btn">
            <image style="width: 100%;height: 100%;" src="/static/hangup.png" />
          </view>
          <text>拒绝</text>
        </view>

        <view class="incoming-btn accept" 
          @tap="() => acceptCall(incomingCallInfo.callId, incomingCallInfo.from, incomingCallInfo.channelName)">
          <view class="btn-circle1 accept-btn">
            <image style="width: 100%;height: 100%;" src="/static/call_accept.png" />
          </view>
          <text>接听</text>
        </view>
      </view>
    </view>
  </view>
</template>

<script>
import NIM from 'nim-web-sdk-ng/dist/v2/NIM_UNIAPP_SDK'
import NERTC from "@/NERtcUniappSDK-JS/lib/index"
import permision from '@/NERtcUniappSDK-JS/permission.js'

export default {
  name: 'CallComponent',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    targetDeviceId: {
      type: String,
      default: ''
    }
  },
  
  data() {
    return {
      // IM SDK 相关
      nimConnected: false,
      nimStatusText: '未连接',

      // RTC SDK 相关
      engine: null,
      rtcConnected: false,
      rtcStatusText: '未连接',

      // 通话相关
      isInCall: false,
      isMuted: false,
      myAccount: '',
      targetAccount: '',
      callStatus: '未连接',
      callDuration: '',
      durationTimer: null,
      startTime: null,

      // 通话请求相关
      currentCallId: '',
      currentRemoteUserId: '',

      // 配置信息
      config: {
        appKey: '28fa64687a29f211131db2745bb84d14',
        debugLevel: 'debug'
      },
      isSpeakerOn: true,
      callTimeoutTimer: null,
      hasShownNetworkWarning: false,
      showIncomingModal: false,
      incomingCallInfo: {
        callId: '',
        from: '',
        channelName: ''
      },

      // 设备信息
      deviceInfo: {
        id: '设备A-001',
        image: '/static/device_icon.png'
      },

      // 铃声播放器
      ringtonePlayer: null,

      // 添加新属性保存通道名
      currentChannelName: '',

      // 添加重连相关变量
      isReconnecting: false,
      reconnectAttempts: 0,
      maxReconnectAttempts: 3,
      _stateChangeLog: [],
      _rtcJoinedAt: null,
    }
  },
  
  created() {
    // 当组件创建时，尝试初始化和登录
    this.initializeCall()
  },
  
  mounted() {
    // 确保事件监听正确设置
    if (uni.$NIM && !this.nimConnected) {
      this.setupEventListeners()
    }
  },
  
  watch: {
    targetDeviceId: {
      immediate: true, // 确保首次加载也会触发
      handler(newVal) {
        if (newVal) {
          console.log('设置目标账号:', newVal)
          this.targetAccount = newVal
          
          // 确保目标账号设置后打印一条日志以便确认
          setTimeout(() => {
            console.log('目标账号设置完成，当前值:', this.targetAccount)
          }, 0)
        }
      }
    },
    visible(newVal) {
      console.log('CallComponent 可见性变更为:', newVal)
      if (newVal) {
        // 如果变为可见状态，则检查是否需要显示来电UI
        if (this.showIncomingModal) {
          console.log('来电UI应该显示，确保状态正确')
          // 延迟一点时间再确认状态
          setTimeout(() => {
            this.showIncomingModal = true
            this.$forceUpdate()
          }, 100)
        }
        
        // 如果未登录，尝试登录
        if (!this.nimConnected) {
          this.initializeCall()
        }
      }
    },
    showIncomingModal(newVal) {
      console.log('来电UI显示状态变更为:', newVal)
      if (newVal) {
        // 确保组件可见
        this.$emit('show-call-ui')
      }
    },
    isInCall(newVal, oldVal) {
      console.log(`isInCall changed: ${oldVal} -> ${newVal}`)
      this.logStateChange('isInCall changed', oldVal, newVal)
      
      // 如果变为false，记录调用栈以便调试
      if (newVal === false && oldVal === true) {
        console.warn('通话状态从true变为false，调用栈:', new Error().stack)
      }
    }
  },
  
  methods: {
    // 初始化调用方法
    async initializeCall() {
      try {
        // 获取保存的账号信息
        const user = uni.getStorageSync('user')
        const savedAccount = user && user.account
        
        if (savedAccount) {
          this.myAccount = savedAccount
          console.log('正在执行自动登录...')
          await this.calllogin()
        } else {
          this.$emit('login-failed', { message: '未找到用户账号信息' })
        }
      } catch (error) {
        console.error('初始化调用失败:', error)
        this.$emit('login-failed', { error })
      }
    },
    
    // NIM 初始化
    initNIM() {
      try {
        console.log('初始化NIM SDK...')

        // 首先检查NIM是否正确导入
        if (!NIM) {
          console.error('NIM 未定义，检查导入语句')
          throw new Error('NIM SDK未定义，请检查导入')
        }

        // 检查全局实例是否已存在
        if (uni.$NIM) {
          console.log('NIM实例已存在于全局')
          return true
        }

        // 确认getInstance是可用的函数
        if (typeof NIM.getInstance !== 'function') {
          console.error('NIM.getInstance 不是函数', NIM)
          throw new Error('NIM.getInstance 方法不可用')
        }

        try {
          // 创建实例并保存到全局
          const nimInstance = NIM.getInstance({
            appkey: this.config.appKey,
            debugLevel: "debug",
            apiVersion: "v2"
          })

          // 确认实例创建成功
          if (!nimInstance) {
            throw new Error('getInstance返回了空值')
          }

          // 确认v2服务可用
          if (!nimInstance.V2NIMLoginService) {
            throw new Error('V2 服务不可用，请确认SDK版本支持v2接口')
          }

          // 保存到全局变量
          uni.$NIM = nimInstance
          console.log('NIM SDK初始化完成，已保存到全局 uni.$NIM')
          return true

        } catch (instanceError) {
          console.error('创建NIM实例时出错:', instanceError)
          throw new Error(`创建实例失败: ${instanceError.message || '未知错误'}`)
        }
      } catch (error) {
        console.error('NIM SDK初始化失败:', error)
        uni.showToast({
          title: 'NIM初始化失败: ' + (error.message || '未知错误'),
          icon: 'none',
          duration: 3000
        })
        uni.$NIM = null
        return false
      }
    },

    // 登录 - 使用v2接口
    async calllogin() {
      if (!this.myAccount) {
        uni.showToast({
          title: '请输入您的账号',
          icon: 'none'
        })
        return
      }

      try {
        // 初始化NIM
        if (!uni.$NIM) {
          const initSuccess = this.initNIM()
          if (!initSuccess) {
            throw new Error('NIM初始化失败，无法登录')
          }
        }

        console.log('开始登录NIM...')

        // 从本地缓存获取token
        const token = uni.getStorageSync('sign') || ''
        if (!token) {
          throw new Error('未找到登录凭证，请先获取token')
        }

        // 使用v2登录接口
        try {
          const params = {
            forceMode: false,
            authType: 1, // 动态token类型
            tokenProvider: function() {
              return Promise.resolve(token)
            }
          }

          // 直接使用V2NIMLoginService的login方法
          await uni.$NIM.V2NIMLoginService.login(this.myAccount, token, params)
          console.log('V2接口登录成功')

          // 登录成功处理
          this.handleLoginSuccess()
          this.$emit('login-success', { account: this.myAccount })

        } catch (loginErr) {
          console.error('V2接口登录失败:', loginErr)
          this.$emit('login-failed', { error: loginErr })
          throw loginErr
        }
      } catch (err) {
        console.error('登录失败:', err)
        let errorMsg = '登录失败'
        if (err) {
          if (err.message) {
            errorMsg = err.message
          } else if (err.code) {
            errorMsg = `错误码: ${err.code}`
          }
        }

        uni.showToast({
          title: errorMsg,
          icon: 'none'
        })
        
        this.$emit('login-failed', { error: err, message: errorMsg })
      }
    },

    // 处理登录成功
    handleLoginSuccess() {
      this.nimConnected = true
      this.nimStatusText = '已登录'
      this.callStatus = '准备就绪'
      console.log('IM登录成功')
      
      // 存储账号信息
      uni.setStorageSync('accountId', this.myAccount)

      // 设置事件监听
      this.setupEventListeners()

      // 初始化RTC
      this.initRTC()
      
      // 如果有目标账号传入，设置为目标
      if (this.targetDeviceId) {
        this.targetAccount = this.targetDeviceId
      }
    },

    // 设置事件监听
    setupEventListeners() {
      if (!uni.$NIM) return

      try {
        console.log('设置NIM事件监听')

        // 使用V2 Event服务
        const eventService = uni.$NIM.V2NIMEventService
        if (eventService) {
          console.log('使用V2NIMEventService监听连接状态')

          // 监听踢出事件
          eventService.onKickedOutOriginClient(data => {
            console.log('账号在其他设备登录:', data)
            this.nimConnected = false
            this.nimStatusText = '已在其他设备登录'
            this.callStatus = '已在其他设备登录'

            uni.showToast({
              title: '账号已在其他设备登录',
              icon: 'none',
              duration: 3000
            })
            
            this.$emit('kicked-out')
          })
        }

        // 监听自定义通知 - 用于语音通话信令
        if (uni.$NIM.V2NIMNotificationService) {
          console.log('使用V2NIMNotificationService监听自定义通知')

          // 监听自定义通知
          uni.$NIM.V2NIMNotificationService.on("onReceiveCustomNotifications", (customNotifications) => {
            if (Array.isArray(customNotifications)) {
              customNotifications.forEach(notification => {
                this.handleCustomNotification(notification)
              })
            }
          })
        } else {
          console.warn('找不到V2通知服务，无法监听消息')
        }
      } catch (error) {
        console.error('设置事件监听失败:', error)
      }
    },

    // 登出 - 使用v2接口
    async logout() {
      if (!uni.$NIM) {
        return
      }

      try {
        // 如果正在通话中，先挂断
        if (this.isInCall) {
          await this.hangUp()
        }

        // 使用V2登出方法
        await uni.$NIM.V2NIMLoginService.logout()

        this.callStatus = '未连接'

        uni.showToast({
          title: '已登出',
          icon: 'none'
        })

      } catch (error) {
        console.error('登出失败:', error)
        uni.showToast({
          title: '登出失败',
          icon: 'none'
        })
      }
    },

    // 通用发送自定义消息方法 - 使用V2接口
    async sendCustomNotification(to, content) {
      try {
        if (!uni.$NIM) {
          throw new Error('NIM未初始化')
        }

        // 确保content是字符串
        const contentStr = typeof content === 'string' ? content : JSON.stringify(content)

        console.log(`尝试向 ${to} 发送消息:`, contentStr)

        // 使用V2通知服务
        if (uni.$NIM.V2NIMNotificationService &&
          typeof uni.$NIM.V2NIMNotificationService.sendCustomNotification === 'function') {
          console.log('使用V2NIMNotificationService.sendCustomNotification发送')

          // 构造会话ID
          const conversationId = `${this.myAccount}|1|${to}`

          // 发送通知
          return await uni.$NIM.V2NIMNotificationService.sendCustomNotification(
            conversationId,
            contentStr, {} // 空选项
          )
        } else {
          throw new Error('找不到V2消息发送方法')
        }
      } catch (error) {
        console.error('发送消息失败:', error)
        throw error
      }
    },

    // 添加状态变化记录
    logStateChange(action, oldIsInCall, newIsInCall) {
      const entry = {
        timestamp: new Date().toISOString(),
        action,
        oldState: oldIsInCall,
        newState: newIsInCall,
        stack: new Error().stack
      }
      console.log(`状态变化: ${action} - ${oldIsInCall} -> ${newIsInCall}`)
      this._stateChangeLog.push(entry)
    },

    // RTC初始化
    initRTC() {
      try {
        console.log('初始化RTC引擎 - 纯音频模式...')

        // 确保不重复初始化
        if (this.engine) {
          console.log('RTC引擎已存在，跳过初始化')
          this.rtcStatusText = '已初始化'
          return
        }

        // 使用最严格的设置禁用视频
        this.engine = NERTC.setupEngineWithContext({
          appKey: this.config.appKey,
          logDir: '',
          logLevel: 6,
          videoConfig: {
            enable: false,
            quality: 0,
            width: 0,
            height: 0
          },
          audioConfig: {
            enable: true
          },
          scene: 'audio_call'
        })

        // 仅检查音频权限
        if (uni.getSystemInfoSync().platform === 'android') {
          permision.requestAndroidPermission('android.permission.RECORD_AUDIO')
        }

        // 确保完全禁用视频功能
        if (typeof this.engine.enableLocalVideo === 'function') {
          this.engine.enableLocalVideo({
            enable: false
          })
          console.log('视频已完全禁用')
        }

        // 音频配置
        this.engine.setAudioProfile({
          profile: 0,
          scenario: 0
        })

        // 启用音频
        this.enableAudio()

        // 注册RTC事件监听
        this.setupRTCListeners()

        console.log('RTC引擎初始化完成 - 纯音频模式')
        this.rtcStatusText = '已初始化'
        
        this.$emit('rtc-initialized')

      } catch (error) {
        console.error('RTC引擎初始化失败:', error)
        uni.showToast({
          title: 'RTC初始化失败',
          icon: 'none'
        })
        this.$emit('rtc-init-failed', { error })
      }
    },

    // 设置RTC事件监听
    setupRTCListeners() {
      // 错误事件
      this.engine.addEventListener('onError', (code, message, extraInfo) => {
        console.error(`RTC错误: code=${code}, message=${message}, extraInfo=${extraInfo}`)
        // 严重错误时结束通话
        if (code < 0) {
          uni.showToast({
            title: '通话发生错误，已断开',
            icon: 'none'
          })
          this.endCall()
        }
      })

      // 加入频道事件
      this.engine.addEventListener('onJoinChannel', (result, channelId, elapsed, userID) => {
        console.log(`加入频道成功: result=${result}, channelId=${channelId}, userID=${userID}`)
        if (result === 0) {
          this.rtcConnected = true
          this.rtcStatusText = '已连接'
          
          // 重要：确保通话状态为true，并添加保护状态的标志
          this.isInCall = true;
          this._rtcJoinedAt = Date.now(); // 添加时间戳标记RTC连接成功时间
          this.callStatus = '通话中'
          console.log('加入频道事件回调中设置状态:', this.isInCall)
        }
      })

      // 离开频道事件
      this.engine.addEventListener('onLeaveChannel', (result) => {
        console.log(`离开频道: result=${result}`)
        
        // 不要在这里自动设置isInCall=false
        // 只有当调用endCall方法时才应该改变isInCall
        this.rtcConnected = false
        this.rtcStatusText = '未连接'
      })

      // 用户加入事件
      this.engine.addEventListener('onUserJoined', (userID) => {
        console.log(`用户加入: userID=${userID}`)
      })

      // 用户离开事件
      this.engine.addEventListener('onUserLeave', (userID) => {
        console.log(`用户离开: userID=${userID}`)
        if (userID.toString() === this.currentRemoteUserId) {
          console.log('通话对方已离开')
          uni.showToast({
            title: '对方已结束通话',
            icon: 'none'
          })
          // 强制结束本地通话
          this.endCall()
        }
      })

      // 用户音频开始事件
      this.engine.addEventListener('onUserAudioStart', (userID) => {
        console.log(`用户开启音频: userID=${userID}`)
      })

      // 用户音频停止事件
      this.engine.addEventListener('onUserAudioStop', (userID) => {
        console.log(`用户停止音频: userID=${userID}`)
      })

      // 网络状态变化
      this.engine.addEventListener('onConnectionStateChanged', (state) => {
        console.log(`RTC连接状态变化: state=${state}`)
        
        // 数字状态2通常表示已连接
        if (state === 2 || state === 'CONNECTED') {
          this.rtcConnected = true
          this.rtcStatusText = '已连接'
          console.log('RTC已连接到服务器')
        }
        // 只有当状态明确为断开或失败时才处理
        else if ((state === 1 || state === 'DISCONNECTED' || state === 'FAILED') && !this.isReconnecting) {
          console.log('RTC连接已断开或失败')
          
          // 如果正在通话中收到断开，尝试重连
          if (this.isInCall && this.currentChannelName) {
            console.log('通话中断开，尝试重连')
            this.isReconnecting = true
            
            setTimeout(() => {
              if (this.isInCall) { // 确认仍在通话状态
                console.log('尝试重新加入频道:', this.currentChannelName)
                this.joinRTCChannel(this.currentChannelName)
              }
              this.isReconnecting = false
            }, 2000)
          }
          
          // 只有在确定断开且不在通话中才更新状态
          if (!this.isInCall) {
            this.rtcConnected = false
            this.rtcStatusText = '未连接'
          }
        }
      })

      // 添加通话质量监测
      this.engine.addEventListener('onNetworkQuality', (userID, downlinkQuality, uplinkQuality) => {
        console.log(`网络质量: userID=${userID}, 下行=${downlinkQuality}, 上行=${uplinkQuality}`)
        // 网络极差时提示用户
        if (downlinkQuality >= 4 || uplinkQuality >= 4) {
          // 4和5表示网络质量差或很差
          if (!this.hasShownNetworkWarning) {
            this.hasShownNetworkWarning = true
            uni.showToast({
              title: '网络质量不佳',
              icon: 'none',
              duration: 2000
            })
            // 5秒后重置提示标志
            setTimeout(() => {
              this.hasShownNetworkWarning = false
            }, 5000)
          }
        }
      })
    },

    // 添加常量定义
    MULTI_DEVICE_SYNC: {
      CALL_HANDLED: 'call_handled_by_other_device'
    },

    // 处理自定义通知
    handleCustomNotification(notification) {
      try {
        const content = typeof notification.content === 'string' ?
          JSON.parse(notification.content) :
          notification.content

        const fromAccount = notification.senderId || notification.fromAccount
        console.log('收到通知:', content.type, '来自:', fromAccount)

        switch (content.type) {
          case 'call_invite':
            // 先向父组件通报有来电
            this.$emit('incoming-call', {
              callId: content.callId,
              from: fromAccount,
              channelName: content.channelName || content.callId
            })
            
            // 确保组件可见
            this.$emit('show-call-ui')
            
            // 延迟一点时间再处理来电，确保UI更新
            setTimeout(() => {
              this.handleCallInvite(content, fromAccount)
            }, 100)
            break
          
          case 'call_accept':
            this.handleCallAccept(content)
            break
          case 'call_reject':
            this.handleCallReject(content)
            break
          case 'call_cancel':
            this.handleCallCancel(content)
            break
          case 'call_end':
            this.handleCallHangup(content)
            break
          case 'call_handled_by_other_device':
            this.handleMultiDeviceSync(content, fromAccount)
            break
          default:
            console.log('未知信令:', content)
        }
      } catch (e) {
        console.error('\n 处理自定义通知失败:', e, '\n', notification)
        // 尝试基本的来电处理
        if (notification && notification.content) {
          try {
            const simpleContent = typeof notification.content === 'string' ?
              JSON.parse(notification.content) : notification.content
              
            if (simpleContent.type === 'call_invite') {
              // 通知父组件，即使处理失败也至少通知
              this.$emit('incoming-call', {
                callId: simpleContent.callId,
                from: notification.senderId || notification.fromAccount,
                channelName: simpleContent.callId
              })
              this.$emit('show-call-ui')
            }
          } catch (parseError) {
            console.error('尝试基本解析失败:', parseError)
          }
        }
      }
    },

    // 重命名并修改多端同步处理方法
    handleMultiDeviceSync(content, fromAccount) {
      // 确认是来自同一账号的其他端
      if (fromAccount === this.myAccount) {
        console.log('收到多端同步消息:', content)
        // 日志：当前本地callId和收到的callId
        console.log('本地currentCallId:', this.currentCallId, '收到的callId:', content.callId)
        // 允许同步消息处理：只要本地处于呼叫相关状态（正在呼叫、通话中），就响应
        if (
          this.currentCallId === content.callId ||
          this.callStatus === '正在呼叫...' ||
          this.isInCall
        ) {
          this.safeHideModal()
          this.endCall() // 确保彻底挂断并清理状态
          
          // 根据操作类型显示不同的提示
          let message = '通话已在其他端处理'
          if (content.action === 'accept') {
            message = '通话已在其他端接听'
          } else if (content.action === 'reject') {
            message = '通话已在其他端拒绝'
          } else if (content.action === 'cancel') {
            message = '通话已在其他端取消'
          } else if (content.action === 'end' || content.action === 'hangup') {
            message = '通话已在其他端结束'
          }
          
          // uni.showToast({
          //   title: message,
          //   icon: 'none'
          // })
        } else {
          console.log('多端同步消息callId不匹配，忽略')
        }
      }
    },

    // 修改发送多端同步通知的方法
    async sendMultiDeviceSync(action, callId) {
      try {
        const syncData = {
          type: 'call_handled_by_other_device',
          callId: callId,
          action: action,
          handledBy: this.myAccount
        }

        await this.sendCustomNotification(this.myAccount, syncData)
      } catch (error) {
        console.error('发送多端同步通知失败:', error)
      }
    },

    // 处理呼叫邀请方法
    handleCallInvite(content, from) {
      try {
        console.log('收到呼叫邀请，处理中...', content, from)
        
        if (this.isInCall) {
          this.sendCustomNotification(from, {
            type: 'call_reject',
            callId: content.callId
          })
          return
        }

        // 确保from参数有效
        if (!from) {
          console.error('呼叫邀请错误: 缺少发起者ID');
          return;
        }

        this.currentCallId = content.callId
        this.currentRemoteUserId = from
        this.currentChannelName = content.channelName || content.callId
        
        // 保存到本地存储作为备份
        uni.setStorageSync('lastCallInfo', {
          remoteUserId: from,
          callId: content.callId,
          channelName: this.currentChannelName,
          timestamp: Date.now()
        });

        // 仅被叫弹窗，主叫不弹窗
        if (from !== this.myAccount) {
          // 播放来电铃声
          this.playRingtone()

          // 确保所有字段都有有效值
          this.incomingCallInfo = {
            from: from || '未知用户',
            callId: content.callId || '',
            channelName: content.channelName || content.callId || ''
          }
          
          console.log('设置来电信息:', this.incomingCallInfo)
          
          // 确保组件可见
          this.$emit('show-call-ui')
          
          // 显示弹窗 - 确保这里会设置为true
          this.showIncomingModal = true
          console.log('来电弹窗显示状态设置为:', this.showIncomingModal)
          
          // 通知父组件有来电UI已显示
          this.$emit('incoming-call-ui-shown')

          // 手动强制更新组件
          this.$forceUpdate()

          // 30秒自动拒绝
          this.callTimeoutTimer = setTimeout(() => {
            if (this.showIncomingModal) {
              this.rejectCall(content.callId, from)
              uni.showToast({
                title: '来电超时未接听',
                icon: 'none'
              })
            }
          }, 30000)
        }
      } catch (error) {
        console.error('处理呼叫邀请出错:', error)
      }
    },

    // 处理接受呼叫
    async acceptCall(callId, from, channelName) {
      try {
        console.log('接受呼叫，参数:', { callId, from, channelName })
        
        // 检查参数合法性
        if (!from) {
          console.error('接听呼叫错误: 缺少发起者ID');
          uni.showToast({
            title: '接听失败: 无法识别呼叫方',
            icon: 'none'
          });
          throw new Error('缺少呼叫方ID');
        }
        
        // 检查channelName是否有效
        if (!channelName) {
          console.error('通道名为空，使用callId作为channelName')
          channelName = callId // 如果channelName为空，使用callId作为备用
        }

        // 记录到本地变量以备后用 - 确保这些关键信息被正确保存
        this.currentChannelName = channelName
        this.currentCallId = callId
        this.currentRemoteUserId = from
        
        // 保存到本地存储作为备份
        uni.setStorageSync('lastCallInfo', {
          remoteUserId: from,
          callId: callId,
          channelName: channelName,
          timestamp: Date.now()
        });
        
        // 添加日志确认远程用户ID已保存
        console.log('已保存通话信息:', {
          callId: this.currentCallId,
          remoteUserId: this.currentRemoteUserId,
          channelName: this.currentChannelName
        })

        // 停止铃声
        this.stopRingtone()

        // 关闭来电弹窗
        this.showIncomingModal = false

        // 清除超时计时器
        if (this.callTimeoutTimer) {
          clearTimeout(this.callTimeoutTimer)
          this.callTimeoutTimer = null
        }

        // 设置通话状态并强制为true
        this.isInCall = true
        this.callStatus = '通话中'
        
        // 记录当前状态
        console.log('接听前设置通话状态:', {
          isInCall: this.isInCall,
          callStatus: this.callStatus
        })
        
        // 通知父组件通话已开始
        this.$emit('call-started', { 
          targetAccount: from,
          callId: callId
        })

        // 使用setTimeout确保UI更新先于RTC操作
        setTimeout(async () => {
          try {
            // 发送接受通知给对方
            await this.sendCustomNotification(from, {
              type: 'call_accept',
              callId
            })

            // 发送多端同步通知
            await this.sendMultiDeviceSync('accept', callId)
            
            // 再次确认状态
            this.isInCall = true
            console.log('发送通知后再次确认状态:', this.isInCall)

            // 加入RTC频道
            const joinResult = this.joinRTCChannel(this.currentChannelName)
            
            // 无论结果如何，确保状态一致
            this.isInCall = true
            console.log('加入频道后强制确认状态:', this.isInCall)
            
            // 启动计时器
            this.startTimer()
          } catch (error) {
            console.error('接听流程出错:', error)
            
            // 即使出错也保持通话状态，让用户手动挂断
            this.isInCall = true
          }
        }, 10)
        
      } catch (error) {
        console.error('接受呼叫失败:', error)
        uni.showToast({
          title: '接听失败',
          icon: 'none'
        })
        
        // 错误时也保持通话状态，让用户手动挂断
        this.isInCall = true
        console.log('错误处理后强制确认状态:', this.isInCall)
      }
    },

    // 处理拒绝呼叫
    async rejectCall(callId, from) {
      try {
        console.log('拒绝呼叫:', callId, from)
        
        // 停止铃声
        this.stopRingtone()

        // 关闭来电弹窗
        this.showIncomingModal = false

        // 清除超时计时器
        if (this.callTimeoutTimer) {
          clearTimeout(this.callTimeoutTimer)
          this.callTimeoutTimer = null
        }

        // 发送拒绝通知给对方
        await this.sendCustomNotification(from, {
          type: 'call_reject',
          callId: callId,
        })

        // 发送多端同步通知
        await this.sendMultiDeviceSync('reject', callId)

        this.resetCallState()
        
        // 通知父组件通话已结束
        this.$emit('call-ended', { reason: 'rejected' })

      } catch (error) {
        console.error('拒绝呼叫失败:', error)
        // 即使失败也重置状态
        this.resetCallState()
        this.$emit('call-ended', { reason: 'rejected', error })
      }
    },

    // 处理取消呼叫
    handleCallCancel(content) {
      if (content.callId === this.currentCallId) {
        console.log('对方取消呼叫')
        
        // 停止铃声
        this.stopRingtone()
        
        // 关闭来电弹窗
        this.showIncomingModal = false
        
        // 清除超时计时器
        if (this.callTimeoutTimer) {
          clearTimeout(this.callTimeoutTimer)
          this.callTimeoutTimer = null
        }
        
        uni.showToast({
          title: '对方取消了通话',
          icon: 'none'
        })
        
        // 立即重置状态
        this.resetCallState()
        
        // 通知父组件
        this.$emit('call-ended', { reason: 'canceled' })
      }
    },

    // 处理挂断呼叫
    handleCallHangup(content) {
      console.log('收到挂断通知:', content)
      console.log('本地currentCallId:', this.currentCallId)
      
      // 增加宽松匹配条件，允许在呼叫状态下接收任何挂断信令
      if (content.callId === this.currentCallId || this.isInCall) {
        console.log('对方挂断呼叫，处理中...')

        // 弹窗提示
        uni.showToast({
          title: '对方已挂断通话',
          icon: 'none',
          duration: 2000
        })

        // 立即结束通话
        this.endCall()
        
        // 通知父组件
        this.$emit('call-ended', { reason: 'remote-hangup' })
      } else {
        console.log('收到的挂断callId不匹配:', content.callId, this.currentCallId, '但通话状态为:', this.isInCall)
      }
    },

    // 处理接受呼叫通知
    handleCallAccept(content) {
      if (content.callId !== this.currentCallId) {
        console.log('收到的接听callId不匹配:', content.callId, this.currentCallId)
        return
      }
      
      console.log('对方已接受呼叫')
      
      // 清除超时计时器
      if (this.callTimeoutTimer) {
        clearTimeout(this.callTimeoutTimer)
        this.callTimeoutTimer = null
      }
      
      // 确保使用正确的channel
      const channelName = this.currentChannelName || this.currentCallId
      
      // 加入RTC频道
      this.joinRTCChannel(channelName)
      
      // 更新状态
      this.isInCall = true
      this.callStatus = '通话中'
      
      // 开始计时
      this.startTimer()
      
      // 显示提示
      // uni.showToast({
      //   title: '对方已接听',
      //   icon: 'none',
      //   duration: 2000
      // })
      
      // 通知父组件
      this.$emit('call-started', { 
        targetAccount: this.currentRemoteUserId,
        callId: this.currentCallId
      })
    },

    // 处理拒绝呼叫通知
    handleCallReject(content) {
      if (content.callId !== this.currentCallId) {
        console.log('收到的拒绝callId不匹配:', content.callId, this.currentCallId)
        return
      }
      
      // 清除超时计时器
      if (this.callTimeoutTimer) {
        clearTimeout(this.callTimeoutTimer)
        this.callTimeoutTimer = null
      }
      
      // 更新UI状态
      this.callStatus = '对方已拒绝'
      
      uni.showToast({
        title: '对方拒绝了通话',
        icon: 'none',
        duration: 2000
      })
      
      // 重置状态
      setTimeout(() => {
        this.resetCallState()
        
        // 通知父组件
        this.$emit('call-ended', { reason: 'rejected-by-remote' })
      }, 2000)
    },

    // 发起呼叫
    async makeCall() {
      // 先检查是否有有效的目标账号
      if (!this.targetAccount) {
        console.error('未指定目标账号')
        console.log('目标账号状态:', {
          targetDeviceId: this.targetDeviceId,
          targetAccount: this.targetAccount,
          props: this.$props
        })
        uni.showToast({
          title: '请指定呼叫目标',
          icon: 'none'
        })
        return false
      }

      console.log('开始呼叫目标账号:', this.targetAccount)
      
      try {
        // 先确保显示组件
        this.$emit('show-call-ui')
        
        // 生成唯一呼叫ID
        this.currentCallId = `call_${Date.now()}_${Math.floor(Math.random() * 1000)}`
        this.currentRemoteUserId = this.targetAccount
        this.currentChannelName = this.currentCallId
        
        // 保存到本地存储作为备份
        uni.setStorageSync('lastCallInfo', {
          remoteUserId: this.targetAccount,
          callId: this.currentCallId,
          channelName: this.currentChannelName,
          timestamp: Date.now()
        });
        
        // 发送呼叫邀请
        await this.sendCustomNotification(this.targetAccount, {
          type: 'call_invite',
          callId: this.currentCallId,
        })

        this.callStatus = '正在呼叫...'
        
        // 通知父组件呼叫状态
        this.$emit('call-started', { 
          targetAccount: this.targetAccount,
          callId: this.currentCallId
        })

        // 设置超时处理
        if (this.callTimeoutTimer) {
          clearTimeout(this.callTimeoutTimer)
        }

        this.callTimeoutTimer = setTimeout(() => {
          if (this.callStatus === '正在呼叫...') {
            uni.hideLoading()
            this.callStatus = '对方无应答'
            uni.showToast({
              title: '对方无应答',
              icon: 'none',
              duration: 2000
            })
            this.cancelCall()
            this.$emit('call-timeout')
          }
        }, 30000) // 30秒超时
        
        return true

      } catch (error) {
        console.error('发起呼叫失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '呼叫失败',
          icon: 'none'
        })
        this.$emit('call-failed', { error })
        return false
      }
    },

    // 取消呼叫
    async cancelCall() {
      try {
        // 清除超时定时器
        if (this.callTimeoutTimer) {
          clearTimeout(this.callTimeoutTimer)
          this.callTimeoutTimer = null
        }

        if (this.currentRemoteUserId) {
          await this.sendCustomNotification(this.currentRemoteUserId, {
            type: 'call_cancel',
            callId: this.currentCallId
          })
        }

        this.resetCallState()
        
        // 通知父组件
        this.$emit('call-ended', { reason: 'canceled-by-local' })

      } catch (error) {
        console.error('取消呼叫失败:', error)
        this.$emit('call-failed', { error })
      }
    },

    // 挂断
    async hangUp() {
      try {
        if (this.callTimeoutTimer) {
          clearTimeout(this.callTimeoutTimer)
          this.callTimeoutTimer = null
        }

        // 添加详细日志
        console.log('执行挂断操作，当前状态:', {
          isInCall: this.isInCall,
          callStatus: this.callStatus,
          currentRemoteUserId: this.currentRemoteUserId,
          currentCallId: this.currentCallId
        })

        // 检查远程用户ID是否存在
        let remoteUserId = this.currentRemoteUserId;
        
        // 如果远程ID为空，尝试从缓存或本地记录中恢复
        if (!remoteUserId) {
          console.warn('缺少远程用户ID，尝试从其他来源恢复');
          
          // 方法1: 尝试从最近的通话记录中获取
          const cachedCallInfo = uni.getStorageSync('lastCallInfo');
          if (cachedCallInfo && cachedCallInfo.remoteUserId) {
            remoteUserId = cachedCallInfo.remoteUserId;
            console.log('从缓存恢复远程用户ID:', remoteUserId);
          } 
          // 方法2: 使用目标设备ID作为备份
          else if (this.targetAccount) {
            remoteUserId = this.targetAccount;
            console.log('使用目标账号作为远程用户ID:', remoteUserId);
          }
          // 方法3: 如果有设备信息，可以尝试使用
          else if (this.deviceInfo && this.deviceInfo.id) {
            // 假设设备ID可能与账号相关
            remoteUserId = this.deviceInfo.id;
            console.log('使用设备ID作为远程用户ID:', remoteUserId);
          }
        }

        // 增强容错处理 - 即使没有远程ID，也尝试离开频道和重置状态
        if (!remoteUserId) {
          console.error('无法恢复远程用户ID，将跳过发送挂断通知');
        } else {
          console.log('发送挂断通知给:', remoteUserId);
          
          try {
            // 发送挂断通知给对方
            await this.sendCustomNotification(remoteUserId, {
              type: 'call_end',
              callId: this.currentCallId || `fallback_${Date.now()}`
            });
            console.log('挂断通知发送成功');
          } catch (notifyError) {
            console.error('发送挂断通知失败:', notifyError);
          }

          // 发送多端同步通知
          try {
            await this.sendMultiDeviceSync('end', this.currentCallId || `fallback_${Date.now()}`);
            console.log('多端同步通知发送成功');
          } catch (syncError) {
            console.error('发送多端同步失败:', syncError);
          }
          
          // 保存本次通话信息，以备恢复使用
          uni.setStorageSync('lastCallInfo', {
            remoteUserId: remoteUserId,
            callId: this.currentCallId,
            timestamp: Date.now()
          });
        }

        // 确保RTC引擎先停止音频再离开频道
        if (this.engine) {
          try {
            // 先禁用音频
            if (typeof this.engine.enableLocalAudio === 'function') {
              this.engine.enableLocalAudio({
                enable: false
              })
              console.log('挂断前已禁用音频');
            }
          } catch (e) {
            console.error('禁用音频失败:', e);
          }
        }

        // 确保完全挂断
        this.endCall(true) // 使用强制模式确保状态重置
        
        // 通知父组件
        this.$emit('call-ended', { reason: 'local-hangup' })

      } catch (error) {
        console.error('挂断失败:', error)
        this.endCall(true)
        this.$emit('call-ended', { reason: 'local-hangup', error })
      }
    },

    // 结束通话
    endCall(force = false) {
      try {
        console.log('结束通话')

        // 先更新状态
        this.isInCall = false
        this.callStatus = '通话已结束'
        
        // 停止计时
        this.stopTimer()
        
        // 关闭弹窗
        uni.hideToast()
        this.safeHideModal()

        // 处理RTC
        if (this.engine) {
          try {
            // 使用enableLocalAudio替代muteLocalAudioStream
            if (typeof this.engine.enableLocalAudio === 'function') {
              this.engine.enableLocalAudio({
                enable: false
              })
              console.log('结束通话前已禁用音频');
            }
            
            // 离开频道 - 不使用Promise形式
            if (typeof this.engine.leaveChannel === 'function') {
              this.engine.leaveChannel();
              console.log('已离开频道');
            }
          } catch (rtcError) {
            console.error('RTC操作失败:', rtcError)
          }
        }

        // 重置状态 - 使用更安全的方式清理状态，确保不会被意外调用
        setTimeout(() => {     
          // 直接在这里重置关键状态
          this.currentCallId = ''
          this.currentRemoteUserId = ''
          this.currentChannelName = ''
          // 确保通话已结束
          this.rtcConnected = false
          this.rtcStatusText = '未连接'
          // 其他状态重置
          this.callDuration = ''
          this.showIncomingModal = false
          this.stopRingtone()
          
          // 只清理计时器，不修改通话状态
          if (this.callTimeoutTimer) {
            clearTimeout(this.callTimeoutTimer)
            this.callTimeoutTimer = null
          }
          
          this.callStatus = this.nimConnected ? '准备就绪' : '未连接'
          
          // 通知父组件隐藏UI
          this.$emit('hide-call-ui')
        }, 1000)
      } catch (error) {
        console.error('结束通话失败:', error)
        // 确保状态重置 - 不再调用resetCallState
        this.isInCall = false
        this.rtcConnected = false
        this.currentCallId = ''
        this.currentRemoteUserId = '' 
        
        // 通知父组件隐藏UI
        this.$emit('hide-call-ui')
      }
    },

    // 重置通话状态
    resetCallState() {
      // 添加保护，防止在通话中意外重置状态
      if (this.isInCall && this.engine && this.rtcConnected) {
        console.warn('检测到在活跃通话中尝试重置状态，已阻止', new Error().stack)
        return; // 阻止在活跃通话中重置
      }
      
      console.log('重置通话状态', new Error().stack)
      
      this.currentCallId = ''
      this.currentRemoteUserId = ''
      this.isInCall = false
      this.callStatus = this.nimConnected ? '准备就绪' : '未连接'
      this.stopTimer()
      this.callDuration = ''
      
      this.showIncomingModal = false
      this.stopRingtone()
      
      if (this.callTimeoutTimer) {
        clearTimeout(this.callTimeoutTimer)
        this.callTimeoutTimer = null
      }
    },

    // 静音控制
    toggleMute() {
      if (!this.engine) {
        console.error('静音操作失败: RTC引擎未初始化');
        return;
      }

      try {
        // 记录旧状态用于错误恢复
        const oldMuteState = this.isMuted;
        
        // 先更新UI状态
        this.isMuted = !this.isMuted;
        console.log('尝试切换静音状态为:', this.isMuted ? '静音' : '非静音');
        
        // 检查引擎的方法实例
        console.log('RTC引擎方法:', Object.keys(this.engine).filter(key => typeof this.engine[key] === 'function'));
        
        // 尝试多种方式实现静音
        if (typeof this.engine.enableLocalAudio === 'function') {
          console.log('使用enableLocalAudio方法静音，参数:', { enable: !this.isMuted });
          
          // 直接调用方法检查错误
          try {
            // 尝试直接传递布尔值参数
            const result = this.engine.enableLocalAudio(!this.isMuted);
            console.log('enableLocalAudio直接调用结果:', result);
          } catch (directCallError) {
            console.error('直接调用enableLocalAudio失败:', directCallError);
            
            // 尝试使用对象参数格式
            try {
              const result = this.engine.enableLocalAudio({ enable: !this.isMuted });
              console.log('enableLocalAudio使用对象参数调用结果:', result);
            } catch (objectCallError) {
              console.error('使用对象参数调用enableLocalAudio失败:', objectCallError);
              throw new Error('无法正确调用enableLocalAudio方法');
            }
          }

          console.log(`已${this.isMuted ? '静音' : '取消静音'}本地音频`);
          
          // 显示操作成功提示
          // uni.showToast({
          //   title: this.isMuted ? '已静音' : '已取消静音',
          //   icon: 'none',
          //   duration: 1500
          // });
        } 
        else {
          console.error('找不到enableLocalAudio方法');
          uni.showToast({
            title: '静音功能不可用',
            icon: 'none',
            duration: 2000
          });
        }
      } catch (error) {
        console.error('切换静音状态失败:', error, '错误堆栈:', error.stack);
        // 还原状态
        this.isMuted = !this.isMuted;
        
        uni.showToast({
          title: '静音操作失败',
          icon: 'none',
          duration: 2000
        });
      }
    },

    // 扬声器开关
    toggleSpeaker() {
      try {
        // 记录旧状态用于错误恢复
        const oldSpeakerState = this.isSpeakerOn;
        
        // 先更新UI状态
        this.isSpeakerOn = !this.isSpeakerOn
        console.log('尝试切换扬声器状态为:', this.isSpeakerOn ? '开启' : '关闭')
        
        if (this.engine) {
          // 主要方法: setEnableSpeakerphone
          if (typeof this.engine.setEnableSpeakerphone === 'function') {
            this.engine.setEnableSpeakerphone(this.isSpeakerOn)
            console.log('扬声器状态:', this.isSpeakerOn ? '已启用' : '已禁用')
            return;
          }
          
          // 替代方法1: setSpeakerphoneOn
          if (typeof this.engine.setSpeakerphoneOn === 'function') {
            this.engine.setSpeakerphoneOn(this.isSpeakerOn)
            console.log('使用setSpeakerphoneOn切换扬声器状态')
            return;
          }
          
          // 替代方法2: setAudioDevice
          if (typeof this.engine.setAudioDevice === 'function') {
            const deviceType = this.isSpeakerOn ? 'speakerphone' : 'earpiece';
            this.engine.setAudioDevice(deviceType)
            console.log('使用setAudioDevice切换扬声器状态为:', deviceType)
            return;
          }
          
          // 替代方法3: setDefaultAudioRouteToSpeakerphone
          if (typeof this.engine.setDefaultAudioRouteToSpeakerphone === 'function') {
            this.engine.setDefaultAudioRouteToSpeakerphone(this.isSpeakerOn)
            console.log('使用setDefaultAudioRouteToSpeakerphone切换扬声器状态')
            return;
          }
          
          // 网易云信特有方法检查
          if (typeof this.engine.netcall === 'object' && 
            typeof this.engine.netcall.switchAudioOutputDevice === 'function') {
            const deviceId = this.isSpeakerOn ? 'default' : 'communications';
            this.engine.netcall.switchAudioOutputDevice(deviceId)
            console.log('使用netcall.switchAudioOutputDevice切换扬声器状态')
            return;
          }
          
          // 如果都不支持，显示警告并还原状态
          console.warn('找不到可用的扬声器控制方法，请尝试手动切换设备的扬声器')
          
          // 尝试使用通用Web Audio API方法 (可能仅在浏览器环境有效)
          try {
            if (typeof uni.getSystemInfoSync === 'function') {
              const sysInfo = uni.getSystemInfoSync();
              if (sysInfo.platform === 'web' || sysInfo.platform === 'devtools') {
                console.log('尝试使用Web Audio API切换音频输出...');
                // 这里的代码仅在Web环境下有效
                if (typeof navigator !== 'undefined' && navigator.mediaDevices) {
                  console.log('使用Web API通知用户手动切换扬声器');
                  // uni.showToast({
                  //   title: `已切换至${this.isSpeakerOn ? '扬声器' : '听筒'}模式`,
                  //   icon: 'none'
                  // });
                }
              } else {
                // 移动端环境，尝试使用uni API
                // uni.showToast({
                //   title: `已尝试切换至${this.isSpeakerOn ? '扬声器' : '听筒'}，请检查效果`,
                //   icon: 'none'
                // });
              }
            }
          } catch (webAudioError) {
            console.error('尝试使用Web Audio API失败:', webAudioError);
          }
        }
      } catch (error) {
        console.error('切换扬声器失败:', error)
        // 还原状态 - 在严重错误时才还原
        this.isSpeakerOn = !this.isSpeakerOn
      }
    },

    // 播放铃声
    playRingtone() {
      try {
        const innerAudioContext = uni.createInnerAudioContext()
        innerAudioContext.autoplay = true
        innerAudioContext.loop = true
        innerAudioContext.src = '/static/ringtone.mp3'
        this.ringtonePlayer = innerAudioContext
      } catch (error) {
        console.error('播放铃声失败:', error)
      }
    },

    // 停止铃声
    stopRingtone() {
      if (this.ringtonePlayer) {
        this.ringtonePlayer.stop()
        this.ringtonePlayer.destroy()
        this.ringtonePlayer = null
      }
    },

    // 计时器相关方法
    startTimer() {
      this.startTime = Date.now()
      this.callDuration = '00:00:00'
      this.durationTimer = setInterval(() => {
        const duration = Date.now() - this.startTime
        const hours = Math.floor(duration / 3600000)
        const minutes = Math.floor((duration % 3600000) / 60000)
        const seconds = Math.floor((duration % 60000) / 1000)

        this.callDuration =
          `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
      }, 1000)
    },

    stopTimer() {
      if (this.durationTimer) {
        clearInterval(this.durationTimer)
        this.durationTimer = null
      }
    },

    // 加入RTC频道
    joinRTCChannel(channelName) {
      try {
        // 在进入方法时先设置状态，避免异步操作中状态丢失
        this.isInCall = true
        console.log('加入频道前状态:', this.isInCall)
        
        console.log('加入RTC频道 (仅音频模式):', channelName)
        
        // 检查参数
        if (!channelName || !this.engine) {
          uni.showToast({
            title: '通话参数错误',
            icon: 'none'
          })
          // 即使出错也不改变状态
          return false
        }

        // 关闭弹窗
        uni.hideToast()
        this.safeHideModal()

        // 生成唯一UID
        const uid = parseInt(this.myAccount.replace(/\D/g, '')) || Math.floor(Math.random() * 100000)
        
        try {
          // 设置订阅选项
          if (typeof this.engine.setAutoSubscribe === 'function') {
            this.engine.setAutoSubscribe({
              video: false,
              audio: true
            })
          }
          
          // 确保视频禁用
          if (typeof this.engine.enableLocalVideo === 'function') {
            this.engine.enableLocalVideo({
              enable: false
            })
          }
          
          // 启用音频
          if (typeof this.engine.enableLocalAudio === 'function') {
            this.engine.enableLocalAudio({
              enable: true
            })
          }
          
          // 设置音频参数
          if (typeof this.engine.setAudioProfile === 'function') {
            this.engine.setAudioProfile({
              profile: 0,
              scenario: 1
            })
          }
          
          // 构建参数
          const joinParams = {
            token: '',
            channelName: channelName,
            myUid: uid,
            mediaOptions: {
              audio: true,
              video: false
            }
          }
          
          // 加入频道
          const result = this.engine.joinChannel(joinParams)
          console.log('加入频道结果:', result)
          
          // 重要：再次确保状态正确
          this.isInCall = true
          this.callStatus = '通话中'
          
          // 记录状态
          console.log('加入频道后状态:', {
            isInCall: this.isInCall,
            callStatus: this.callStatus
          })
          
          // 设置扬声器状态 - 默认打开
          if (typeof this.engine.setEnableSpeakerphone === 'function') {
            this.engine.setEnableSpeakerphone(this.isSpeakerOn)
            console.log('扬声器状态设置为:', this.isSpeakerOn ? '开启' : '关闭')
          }
          
          // 确保初始状态为非静音
          this.isMuted = false
          if (typeof this.engine.enableLocalAudio === 'function') {
            // 启用本地音频（非静音）
            this.engine.enableLocalAudio({
              enable: true
            });
            console.log('初始化本地音频为启用状态（非静音）');
          }
          
          return true
        } catch (error) {
          console.error('加入频道异常:', error)
          uni.showToast({
            title: '加入通话失败',
            icon: 'none'
          })
          
          // 即使出错也不改变isInCall状态
          console.log('加入出错后仍保持通话状态:', this.isInCall)
          return false
        }
      } catch (error) {
        console.error('整体加入频道失败:', error)
        return false
      }
    },

    // 安全隐藏来电弹窗
    safeHideModal() {
      try {
        this.showIncomingModal = false // 关闭自定义弹窗
        // 尝试使用uni.hideModal
        if (typeof uni.hideModal === 'function') {
          uni.hideModal()
        } else {
          // 兼容：强制关闭所有自定义弹窗
          // 1. 触发页面级变量控制弹窗显示（如有）
          if (this.$refs && this.$refs.uniModal) {
            // 如果有ref的modal组件
            this.$refs.uniModal.close && this.$refs.uniModal.close()
          }
          // 2. 触发全局事件关闭弹窗（如有全局事件总线）
          if (this.$emit) {
            this.$emit('close-modal')
          }
          // 3. 兼容自定义弹窗变量
          if (typeof this.showModal !== 'undefined') {
            this.showModal = false
          }
          // 4. 兼容uView等UI库
          if (this.$refs && this.$refs.uModal) {
            this.$refs.uModal.close && this.$refs.uModal.close()
          }
        }
      } catch (e) {
        console.warn('关闭模态框失败:', e)
      }
    },

    // 音频准备与初始化
    prepareAudioBeforeJoin() {
      try {
        // 针对不同平台使用不同的权限请求方法
        const platform = uni.getSystemInfoSync().platform
        
        // Android 平台使用 permision 辅助库
        if (platform === 'android') {
          // 使用已经导入的 permision 模块
          permision.requestAndroidPermission('android.permission.RECORD_AUDIO')
            .then(result => {
              console.log('麦克风权限状态:', result)
              if (result !== 1) { // 注意：Android权限返回1表示已授权
                uni.showToast({
                  title: '需要麦克风权限进行通话',
                  icon: 'none'
                })
              }
            })
            .catch(err => {
              console.error('请求麦克风权限失败:', err)
            })
        } 
        // iOS 平台
        else if (platform === 'ios') {
          // iOS通常在使用录音相关API时自动请求权限
          try {
            const recorderManager = uni.getRecorderManager()
            // 简单启动并立即停止录音，触发系统权限请求
            recorderManager.onStart(() => {
              console.log('触发录音以请求权限')
              recorderManager.stop()
            })
            recorderManager.onStop(() => {
              console.log('停止录音')
            })
            // 尝试短暂录音以触发权限请求
            recorderManager.start({
              duration: 100, // 极短持续时间
              format: 'mp3'
            })
          } catch (recorderError) {
            console.error('录音管理器初始化失败:', recorderError)
          }
        }
        
        // 初始化音频设置 - 使用数字常量
        if (this.engine) {
          // 设置音频参数
          this.engine.setAudioProfile({
            profile: 0, // 使用数字0替代字符串"DEFAULT"
            scenario: 0  // 使用数字0替代字符串"DEFAULT"
          })
          
          // 启用音频
          this.enableAudio()
        }
      } catch (error) {
        console.error('音频准备失败:', error)
      }
    },

    // 启用音频的独立方法
    enableAudio() {
      try {
        if (this.engine) {
          // 确保本地音频启用
          if (typeof this.engine.enableLocalAudio === 'function') {
            this.engine.enableLocalAudio({
              enable: true
            })
            console.log('本地音频已启用')
          }

          // 设置扬声器模式
          if (typeof this.engine.setEnableSpeakerphone === 'function') {
            this.engine.setEnableSpeakerphone(true)
            this.isSpeakerOn = true
            console.log('扬声器已启用')
          }

          // 取消静音
          if (this.isMuted && typeof this.engine.muteLocalAudioStream === 'function') {
            this.engine.muteLocalAudioStream(false)
            this.isMuted = false
            console.log('取消静音')
          }
        }
      } catch (error) {
        console.error('启用音频失败:', error)
      }
    },

    // 添加防止错误重置的保护方法 - 用于检查RTC是否刚刚成功连接
    isRTCRecentlyJoined() {
      if (!this._rtcJoinedAt) return false;
      // 如果RTC连接成功后5秒内，视为刚刚连接，防止重置
      return (Date.now() - this._rtcJoinedAt) < 5000;
    },

    handleCallInvite(callInfo) {
      console.log('收到呼叫邀请，处理中...', callInfo)
      
      // 触发全局事件
      uni.$emit('incoming-call', {
        type: 'call_invite',
        callId: callInfo.callId,
        from: callInfo.from,
        channelName: callInfo.channelName
      })
      
      // 其他处理逻辑...
    }
  },
  
  beforeDestroy() {
    this.stopTimer()

    // 清理RTC引擎
    if (this.engine) {
      this.engine.removeAllEventListener()
      this.engine.destroyEngine()
      this.engine = null
    }

    // 在销毁前确保通话已结束
    if (this.isInCall) {
      console.log('组件销毁前发现通话仍在进行，尝试挂断')
      this.hangUp()
    }
    
    // 确保停止铃声
    this.stopRingtone()
    
    // 清理计时器
    if (this.callTimeoutTimer) {
      clearTimeout(this.callTimeoutTimer)
      this.callTimeoutTimer = null
    }
  }
}
</script>

<style scoped>
.call-component {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
  /* Make sure nothing is hidden */
  pointer-events: auto;
}

/* Ensure incoming call UI is highly visible */
.incoming-call {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(33, 33, 33, 0.98);
  z-index: 10000; /* Higher z-index than other elements */
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

/* 通话UI样式 */
.calling-ui {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: #151515;
  z-index: 999;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: space-between;
}

.calling-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 88rpx;
}

.call-title {
  font-size: 34rpx;
  color: rgba(255,255,255,0.85);
}

.device-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.device-image {
  width: 450rpx;
  height: 450rpx;
  margin-bottom: 24rpx;
}

.device-id {
  font-size: 44rpx;
  color: rgba(255,255,255,0.85);
  margin: 16rpx 0;
  margin-top: 140rpx;
}

.call-state {
  font-size: 30rpx;
  color: #bdbdbd;
  margin-bottom: 70rpx;
}

.call-timer {
  font-size: 32rpx;
  color: #aaaaaa;
  margin-top: 40rpx;
}

.calling-controls {
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-bottom: 10vh;
}

.control-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.btn-circle1 {
  width: 128rpx;
  height: 128rpx;
  border-radius: 50%;
  background-color: #424242;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16rpx;
}

.btn-circle1 image {
  width: 100%;
  height: 100%;
}

.active-btn {
  background-color: #0091EA;
}

.hangup-btn {
  background-color: #f44336;
}

.control-btn text {
  font-size: 24rpx;
  color: #e0e0e0;
}

.incoming-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 15vh;
}

.incoming-title {
  font-size: 48rpx;
  color: rgba(255,255,255,0.85);
  margin-bottom: 80rpx;
}

.incoming-controls {
  display: flex;
  justify-content: center;
  gap: 200rpx;
  margin-bottom: 15vh;
}

.incoming-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.reject-btn {
  background-color: #f44336;
}

.accept-btn {
  background-color: #4CAF50;
}

.incoming-btn text {
  font-size: 32rpx;
  color: rgba(255,255,255,0.85);
  margin-top: 20rpx;
}
</style> 