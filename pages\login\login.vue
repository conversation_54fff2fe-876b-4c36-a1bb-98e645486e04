<template>
	<!-- <custom-header  style="height: 88rpx;" title="云平台"  /> -->
	<view class="password-container">
		<view class="welcome">
			<text class="wel-text">HI 欢迎铁福来云平台 👋</text>
		</view>
		<!-- 手机号输入框 -->
		<view class="input-group">
			<view class="group-left">
				<text>手机号</text>
				<input type="tel" v-model="phone" placeholder="请输入您的手机号" class="password-input" />
			</view>
			<image class="eye-icon" src="../../static/image/login/tellphone.png" mode=""></image>
		</view>

		<!-- 密码输入框 -->
		<view class="input-group">
			<view class="group-left">
				<text>密码</text>
				<input :type="showpsw ? 'text' : 'password'" v-model="psw" placeholder="请输入您的密码"
					class="password-input" />
			</view>
			<image :src="showpsw ? '/static/image/user/open-eye.png' : '/static/image/user/close-eye.png'"
				class="eye-icon" @click="togglePsw" />
		</view>
		
		<!-- 添加记住密码选项 -->
		<view class="remember-pwd">
			<view class="checkbox" @click="toggleRemember">
				<image :src="isRemember ? '/static/check_active.png' : '/static/check.png'" 
					class="checkbox-icon" />
				<text>记住密码</text>
			</view>
		</view>
		
		<button class="btn" @click="handleConfirm">确定</button>
	</view>
</template>

<script>
	import { encrypt } from '@/components/utils/jsencrypt'
	import Request from '@/components/utils/request'
	import Quene from '@/components/utils/queue'
	import Config from '@/components/utils/config'
	import customHeader from '@/components/page/header.vue'
	import NIM from 'nim-web-sdk-ng/dist/v2/NIM_UNIAPP_SDK'
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				phone: '',
				psw: '',
				showpsw: false,
				publicKey: Config.publicKey, // 用于保存公钥
				user: {},
				registerID: '',
				isRemember: false,
			}
		},
		onShow() {
			// 获取存储的登录信息
			this.getStoredLoginInfo()
		},
		mounted() {
			this.initJPushListener();
		},
		methods: {
			initJPushListener() {
				const jpushModule = uni.requireNativePlugin("JG-JPush");
				
				// 监听连接状态
				uni.$on('jpushConnectStatus', (connectEnable) => {
					console.log('登录页收到极光推送连接状态:', connectEnable);
					if (connectEnable) {
						this.getRegistrationID();
					}
				});
				
				// 直接尝试获取设备ID
				this.getRegistrationID();
			},
			getRegistrationID() {
				const jpushModule = uni.requireNativePlugin("JG-JPush");
				jpushModule.getRegistrationID((result) => {
					const registerID = result.registerID;
					console.log('获取到的设备ID为:', registerID);
					this.registerID = registerID;
				});
			},
			togglePsw() {
				this.showpsw = !this.showpsw;
			},
			toggleRemember() {
				this.isRemember = !this.isRemember
			},
			// 获取存储的登录信息
			getStoredLoginInfo() {
				try {
					const loginInfo = uni.getStorageSync('loginInfo')
					if (loginInfo) {
						this.phone = loginInfo.phone
						this.psw = loginInfo.password
						this.isRemember = true
					}
				} catch (e) {
					console.error('获取存储的登录信息失败:', e)
				}
			},
			async handleConfirm() {
				console.log('Quene Object', Quene);
				const phoneRegex = /^1[3-9]\d{9}$/;
				if (!this.phone.trim()) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					})
					return
				}
				
				if (!phoneRegex.test(this.phone)) {
					uni.showToast({
						title: '请输入正确的手机号格式',
						icon: 'none'
					})
					return
				}

				if (!this.psw.trim()) {
					uni.showToast({
						title: '请输入密码',
						icon: 'none'
					})
					return
				}
				if (!this.publicKey) {
					uni.showToast({
						title: '公钥未加载，请稍后重试',
						icon: 'none'
					})
					return;
				}
				try {
					const encryptedPassword = encrypt(this.psw, this.publicKey);
					console.log("加密后的密码", encryptedPassword)
					
					// 如果勾选了记住密码，保存登录信息
					if (this.isRemember) {
						uni.setStorageSync('loginInfo', {
							phone: this.phone,
							password: this.psw
						})
					} else {
						// 如果取消勾选，删除存储的登录信息
						uni.removeStorageSync('loginInfo')
					}
					
					// 调用登录方法
					
					await this.submitToBackend(encryptedPassword)
					.then(loginResult => {
						console.log('loginResult',loginResult)   // 新添加的log，查看 loginResult的具体结构
						if (loginResult && loginResult.success) {
							//存储用户信息，使用Quene
							Quene.setData('user', loginResult.user)
							Quene.setData('sign',loginResult.user.sign)
							// Quene.setData('userinfo', loginResult.user)
							Quene.setData('Authorization', loginResult.token)
							
							// 保存用户信息到本地存储，供IM登录使用
							uni.setStorageSync('user', loginResult.user);
							uni.setStorageSync('sign', loginResult.user.sign);
							
							// 触发登录成功事件，用于初始化IM
							uni.$emit('loginSuccess', loginResult.user);
							
							// 初始化和登录IM
							this.initAndLoginIM(loginResult.user);
							
							// 登录成功后，立即开始获取待办数量
							const app = getApp();
							// app.initMqttConnection();
							// 跳转
							uni.switchTab({ url: '/pages/project/index' })
							// uni.navigateTo({
							// 	url:'/pages/list/callpage'
							// })
							
						}else{
							uni.showToast({
								title: `${loginResult?.message || '未知错误'}`,
								icon: 'none'
							})
						}
					})
					 .catch(error => {
						console.error(" then catch error", error);   // 打印 then 的 catch 错误信息
						  uni.showToast({
							  title: `登录过程出现异常，${error}`,
							  icon: 'none'
						   })
					})
				} catch (e) {
					console.error("try catch error", e);  // 打印 try...catch 错误信息
					  uni.showToast({
						  title: `登录过程出现异常, ${e}`,
						  icon: 'none'
					  })
				}
			},
			async submitToBackend(encryptedPassword) {
				try {
					let login_data = {
						account: this.phone,
						password: encryptedPassword,
						registrationId:this.registerID
					}
					console.log('login_data',login_data);
					const res = await Request.post('/sys/account_login', login_data);
					if (res.status == 0) {
						return {
							success: true,
							user: res.data.user_info,
							token: res.data.token,
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						})
						console.error('登录失败，后端返回:', res.msg) // 添加错误日志
						return { success: false, message: res.msg || '登录失败' };
					}
				} catch (error) {
					console.error("网络请求错误", error)
					uni.showToast({
						title: '网络不稳定，请稍后再试',
						icon: 'none',
						duration: 2000
					})
					return { success: false, message: "服务异常" } // 网络请求错误
				}

			},
			async handleLogin() {
				try {
					// 原有登录逻辑
					const res = await Request.post('/login', loginParams);
					if (res.status === 0) {
						// 保存token
						uni.setStorageSync('token', res.data.token);
						
						// 绑定极光推送用户别名
						const app = getApp();
						const userId = res.data.userId || 'user_' + new Date().getTime();
						
						// #ifdef APP-PLUS
						try {
							const jPushPlugin = app.globalData.jPushPlugin;
							if (jPushPlugin) {
								// 设置极光推送别名
								jPushPlugin.setAlias({
									alias: userId.toString(),
									success: (result) => {
										console.log('设置极光别名成功:', result);
									},
									fail: (error) => {
										console.error('设置极光别名失败:', error);
									}
								});
								
								// 设置标签
								jPushPlugin.setTags({
									tags: ['app_user', 'role_' + (res.data.role || 'normal')],
									success: (result) => {
										console.log('设置极光标签成功:', result);
									},
									fail: (error) => {
										console.error('设置极光标签失败:', error);
									}
								});
							}
						} catch (error) {
							console.error('设置极光推送用户信息失败:', error);
						}
						// #endif
						
						// 开始轮询获取未处理待办
						app.startTodoPolling();
						
						// 跳转到首页
						uni.switchTab({
							url: '/pages/home/<USER>'
						});
					}
				} catch (error) {
					console.error('登录失败:', error);
				}
			},
			
			// 初始化和登录IM的方法
			async initAndLoginIM(user) {
				try {
					// 检查全局实例是否已存在
					if (uni.$NIM) {
						console.log('NIM实例已存在于全局');
						await this.loginNIM(user);
						return;
					}
					
					// 初始化NIM
					console.log('初始化NIM SDK...');
					try {
						// 创建实例并保存到全局
						const nimInstance = NIM.getInstance({
							appkey: '28fa64687a29f211131db2745bb84d14', // 使用您项目中的appKey
							debugLevel: "debug",
							apiVersion: "v2"
						});
						
						// 确认实例创建成功
						if (!nimInstance) {
							throw new Error('getInstance返回了空值');
						}
						
						// 确认v2服务可用
						if (!nimInstance.V2NIMLoginService) {
							throw new Error('V2 服务不可用，请确认SDK版本支持v2接口');
						}
						
						// 保存到全局变量
						uni.$NIM = nimInstance;
						console.log('NIM SDK初始化完成，已保存到全局 uni.$NIM');
						
						// 初始化成功后登录
						await this.loginNIM(user);
						
					} catch (instanceError) {
						console.error('创建NIM实例时出错:', instanceError);
					}
				} catch (error) {
					console.error('初始化NIM失败:', error);
				}
			},
			
			// 登录NIM的方法
			async loginNIM(user) {
				try {
					if (!uni.$NIM) {
						console.error('NIM未初始化，无法登录');
						return;
					}
					
					const account = user.account;
					const token = user.sign;
					
					if (!account || !token) {
						console.error('登录凭证不完整，无法登录NIM');
						return;
					}
					
					console.log('开始登录NIM...');
					
					// 使用v2登录接口
					try {
						const params = {
							forceMode: false,
							authType: 1, // 动态token类型
							tokenProvider: function() {
								return Promise.resolve(token);
							}
						};
						
						// 直接使用V2NIMLoginService的login方法
						await uni.$NIM.V2NIMLoginService.login(account, token, params);
						console.log('NIM登录成功');
						
						// 设置事件监听
						this.setupNIMEventListeners();
						
						// 登录成功后发送全局事件，让app.vue知道
						uni.$emit('nim-login-success', { account });
						
					} catch (loginErr) {
						console.error('NIM登录失败:', loginErr);
					}
				} catch (err) {
					console.error('NIM登录过程出错:', err);
				}
			},
			
			// 设置NIM事件监听
			setupNIMEventListeners() {
				if (!uni.$NIM) return;
				
				try {
					console.log('设置NIM事件监听');
					
					// 使用V2 Event服务
					const eventService = uni.$NIM.V2NIMEventService;
					if (eventService) {
						console.log('使用V2NIMEventService监听连接状态');
						
						// 监听踢出事件
						eventService.onKickedOutOriginClient(data => {
							console.log('账号在其他设备登录:', data);
							uni.showToast({
								title: '账号已在其他设备登录',
								icon: 'none',
								duration: 3000
							});
						});
					}
					
					// 监听自定义通知 - 用于语音通话信令
					if (uni.$NIM.V2NIMNotificationService) {
						console.log('使用V2NIMNotificationService监听自定义通知');
						
						// 监听自定义通知
						uni.$NIM.V2NIMNotificationService.on("onReceiveCustomNotifications", (customNotifications) => {
							if (Array.isArray(customNotifications)) {
								console.log('收到自定义通知:', customNotifications);
								// 转发通知到全局事件，让app.vue处理
								uni.$emit('nim-custom-notification', customNotifications);
							}
						});
					} else {
						console.warn('找不到V2通知服务，无法监听消息');
					}
				} catch (error) {
					console.error('设置NIM事件监听失败:', error);
				}
			},
			
			beforeDestroy() {
				// 移除监听器
				uni.$off('jpushConnectStatus');
			}
		},
	}
</script>
<style lang="scss" scoped>
	.password-container {
		padding: 0 40rpx;
		padding-top: 156rpx;
		flex:1;
	}

	.input-group {
		margin-top: 48rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		height: 120rpx;
		border-radius: 24rpx;
		padding: 0 32rpx;
		background: rgba(255, 255, 255, 0.13);
		backdrop-filter: blur(20rpx);
	}

	.group-left {
		text {
			font-family: Inter;
			font-size: 24rpx;
			font-weight: normal;
			letter-spacing: 0.4rpx;
			line-height: 34rpx;
			color: rgba(255, 255, 255, 0.45);

		}
	}

	.password-input {
		flex: 1;
		font-size: 28rpx;
		height: 40rpx;
		color: rgba(255, 255, 255, 0.95);
	}

	.eye-icon {
		width: 48rpx;
		height: 48rpx;
	}

	.forward {
		margin-top: 32rpx;

		text {
			font-family: Inter;
			font-size: 28rpx;
			font-weight: normal;
			line-height: 40rpx;
			text-align: right;
			letter-spacing: 0.2px;
			color: rgba(255, 255, 255, 0.25);
		}
	}

	.btn {
		margin-top: 250rpx;
		font-family: Inter;
		font-size: 40rpx;
		font-weight: 500;
		height: 96rpx;
		letter-spacing: 0.4rpx;
		color: #FFFFFF;
		border-radius: 16rpx;
		background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	}

	.welcome {
		margin: 140rpx 0rpx 110rpx 48rpx;
	}

	.wel-text {
		font-family: Outfit;
		font-size: 48rpx;
		font-weight: 500;
		line-height: 30.24px;
		letter-spacing: 0px;
		color: #FFFFFF;
	}

	.remember-pwd {
		margin-top: 32rpx;
		// padding: 0 32rpx;
		
		.checkbox {
			display: flex;
			align-items: center;
			
			.checkbox-icon {
				width: 32rpx;
				height: 32rpx;
				margin-right: 12rpx;
			}
			
			text {
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.65);
			}
		}
	}
</style>