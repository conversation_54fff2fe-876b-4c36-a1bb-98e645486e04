<template>

	<view class="con_content">
		<view class="search-bar">
			<view class="bar">
				
			<input 
			type="text" 
			class="sear_inp" 
			placeholder="请输入变量名称" 
			@input="clearInput"
			v-model="searchText" />
			<icon size="16" type="clear" v-if="showClearIcon" @click="clearIcon"></icon>
			</view>
			<!-- <up-input class="sear_inp" placeholder="钻具名称/钻具ID" prefixIcon="search" v-model="searchText"
				color="rgba(255,255,255,0.8)" prefixIconStyle="font-size: 22px;color: #909399"></up-input> -->
			<button class="con_btn" @click="search">查询</button>
		</view>
		
		
			<view style="height:420rpx;magin:10rpx;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
			<!-- <view> -->
				<zb-table 
				@sort-change="sortChange" 
				:pullUpLoading="pullUpLoading" 
				:isShowLoadMore="true"
				:highlight="true" 
				:show-header="true" 
				:columns="column" 
				:fit="false" 
				:permissionBtn="permissionBtn"
				:stripe="true" 
				row-key="id" 
				@rowClick="rowClick" :border="false"  :data="data"></zb-table>

			</view>
	</view>
</template>

<script>

	import Request from '@/components/utils/request.js'
	let that = null
	export default {
		
		data() {
			const baseData = {
			      id: "BFC001",
			      deviceCode: "ECP002",
			      deviceName: "test钻机",
			      deviceModel: "GD325-5",
			      maintenanceType: 1,
			      maintenanceCycle: 2,
			      nextTime: "2025-01-01 10:12:00",
			      actualTime: "2025-01-01 10:12:00",
				  maintenanceContent:"1.更换机油 2.查看滤芯",
				  maintenanceName:"张三",
				  maintenanceState:1,
				  maintenanceResult:1,
			       remark:"无异常，保养良好",
			      creatAt: "2025-01-01 10:12:00",
			      updateAt: "2025-01-04 10:12:00",
			    };
			      const generateData = ()=>{
					  const repeatedData = [];
			          for (let i =0; i < 1; i++) {
			           repeatedData.push(
			                       { id: Date.now() + i, ...baseData } // 使用 Date.now() + i 生成唯一 id
			                   );
			          }
			        return repeatedData;
					 }
			return {				
				dateStart:'',
				dateEnd:'',
				searchText: '',
				column: [
					// { type:'selection', fixed:true,width:60 },
					{
						name: 'id',
						label: '保养计划ID',
						fixed: true,
						align: 'center',
						emptyString: '--'
					},
					{
						name: 'deviceCode',
						label: '设备序列号',
						// sorter: 'custom',
					},
					{
						name: 'deviceName',
						label: '设备名称',
					},
					{
						name: 'deviceModel',
						label: '设备型号',
					},
					{
						name: 'maintenanceType',
						label: '保养类型',
						fixed: true,
						filters: {
						  0: '日常保养',
						  1: '定期保养'
						},
					},
					{
						name: 'maintenanceCycle',
						label: '保养周期',
						filters: {
						  0: '每日',
						  1: '每月',
						  2: '每季度'
						},
					},
					{
						name: 'nextTime',
						label: '下次保养日期',
					},
					 {
					    name: 'actualTime',
					    label: '实际保养日期',			  
					},
					{
						name: 'maintenanceContent',
						label: '保养内容',
						// sorter: true
					},
					{
						name: 'maintenanceName',
						label: '保养负责人',
						// sorter: true
					},
					{
						name: 'maintenanceState',
						label: '保养状态',
						filters: {
						  0: '未完成',
						  1: '已完成',
						},
					},
					{
						name: 'maintenanceResult',
						label: '保养结果',
						filters: {
						  0: '不正常',
						  1: '正常',
						},
					},
					{
						name:'remark',
						label:'备注'
					},
					{
						name:'creatAt',
						label:'创建时间'
					},
					{
						name:'updateAt',
						label:'更新时间'
					},
					// {
					// 	name: 'operation',
					// 	type: 'operation',
					// 	label: '操作',
					// 	align: 'center',
					// 	renders: [{
					// 			name: '详情',
					// 			class: 'edit',
					// 			type: "primary",
					// 			align: 'center',
					// 			func: 'edit' // func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
					// 		},

					// 	]
					// },
				],
				// data: generateData(),//表格数据
				data:[],
				data1: [],
				flag1: true,
				flag2: true,
				showClearIcon:false,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				isShowLoadMore: true, // 是否显示“加载更多”  
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
			}
		},
		onLoad() {
			// this.handelDrill()
		},
		created() {
			that = this
		},
		mounted() {
			this.handelDrill();
		},
		onLoad(options) {
		       console.log(options);
			   this.dateStart=options.dateStart;
			   this.dateEnd=options.dateEnd;
		   },
		methods: {
			clearInput(event) {
			    this.searchText = event.detail.value;
			    if (event.detail.value.length > 0) {
			        this.showClearIcon = true;
			    } else {
			        this.showClearIcon = false;
			    }
			},
			clearIcon() {
			    this.searchText = '';
			    this.showClearIcon = false;
			},
			search() {
				// console.log(event);
				// this.searchText = event.detail.value;
				console.log('handleSearchInput', this.searchText);
				this.currentPage = 1; // 重置页码
				this.data = []; // 清空数据
				this.isShowLoadMore = true; // 重置加载更多
				this.handelDrill(); // 重新加载数据
				this.searchText=''
			},
			//初始加载调取接口获取数据
			async handelDrill() {

				try {
					let main_data = {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord:this.searchText
					};
					const res = await Request.post('/maintenance/get_ls', main_data)

						if (res.status == 0) {
							// console.log('返回数据', res);
							this.data=res.data.items;
							this.searchText=''
							if (res.data.items.length < this.perPage) {
							             this.isShowLoadMore = false;
							          }
							// 更新成功
							// uni.showToast({
							// 	title: '实名认证成功',
							// 	icon: 'none',
							// 	duration: 2000
							// });

						} else {
							// 失败
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}

				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
				
			},
			
			change(e) {
				console.log("e:", e);
			},
			sortChange(item, sorterMode, index) {

				console.log('触发排序', item, sorterMode, index)
			},
			async pullUpLoading(done) {
				if (!this.flag1) {
					return
				}
				this.currentPage++;  // 页码加1
						try {
				           const res = await Request.post('/maintenance/get_ls', {
				              page: this.currentPage,
				              perPage: this.perPage,
							  keyWord:this.searchText
				            });
				
				           if (res.status == 0) {
							   console.log('加载获取数据',res.data);
							   // this.data.push(res.data.items);
							   console.log('data11111',this.data);
				           		if (res.data.items && res.data.items.length > 0) {
									this.data = this.data.concat(res.data.items);
									console.log('data11111',this.data);
									done(); // 通知 zb-table 加载完成
								}else{
								    this.isShowLoadMore = false;    // 没有更多数据，不再显示加载更多
								   done('ok'); // 通知zb-table 没有更多数据
									this.flag1 = false
									uni.showToast({
										title: '暂无更多数据' ,
										icon: 'none',
										duration: 1000
									})
								}
				
				           } else {
				
						        //    uni.showToast({
								// 	title: '加载数据失败' ,
								// 	icon: 'none',
								// 	duration: 1000
								// })
						        done();       // 结束加载
				           }
						} catch (error) {
						    console.error("加载更多数据失败:", error);
						      // uni.showToast({
					        	// 	title: '加载数据失败' ,
					        	// 	icon: 'none',
					        	// 	duration: 1000
					        	// })
						        done();    //  结束加载
						}
				// setTimeout(() => {
				// 	this.data.push({
				// 		serialNum: 'ZJD0021',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	},
				// 	{
				// 		serialNum: 'ZJD0022',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	})

				// 	this.num++
				// 	if (this.num === 3) {
				// 		done('ok')
				// 		this.flag1 = false
				// 	} else {
				// 		done()
				// 	}
				// }, 2000)
			},
			permissionBtn(row, renders, rowIndex) {
				if (row.id == 2) {
					let arr = renders.filter(item => item.func === 'edit')
					return arr
				}
				return renders
			},
			
			buttonEdit(ite, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '点击编辑'
				// })
				console.log(ite, index)
			},
			

			currentChange(row, index) {
				uni.showToast({
					icon: 'none',
					duration: 3000,
					title: '选中当前一行'
				})
				console.log('单选1111', row, index)
			},
			rowClick(row, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '单击某行'
				// })
				console.log('单击某行', row, index)
			}
		},

	}
</script>

<style scoped lang="scss">
	page {
		background: #16171b;
	}
	::v-deep {
		.uni-card {
			margin: 8px !important;
			padding: 0 !important;

			.uni-card__content {
				padding: 0 !important;
			}
		}
	}

	.filter {
		// margin: 28rpx 0;
		height: 70rpx;
		display: flex;
		// background: #fff;
	}
	
	.sear_inp {
		height: 70rpx;
		// padding: 10rpx 28rpx;
		padding-left: 28rpx;
		font-size: 25rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	
	.select {
		flex: 1;
		border: none;
		// border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		// background: rgba(255, 255, 255, 0.08);
	}
	
	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
	}
	.bar{
		flex:1;
		padding-right: 10rpx;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
		display: flex;
		align-items: center;
	}
	.con_btn {
		height: 65rpx;
		line-height: 65rpx;
		background: #007BFF;
		letter-spacing: 0px;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	
	.sear_inp {
		flex: 1;
		margin-right: 16rpx;
		// background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}
	

	.con_content {
		border-radius:0 0 12rpx 12rpx;
		padding: 0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		height: 600rpx;
		overflow: hidden;
		// background: #fff;
	}
</style>