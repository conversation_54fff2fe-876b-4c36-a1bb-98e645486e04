import { ArgumentMap } from 'eventemitter3';
import { FileProgressObject, UploadFileResult } from './CloudStorageServiceInterface';
import { IMMessage, TMsgAntiSpamInfo, TMsgPushInfo, TMsgScene, TMsgSetting, TMsgTeamSpecializationInfo, TMsgType } from './MsgServiceInterface';
export interface StrObj {
    [key: string]: string;
}
export interface StrAnyObj {
    [key: string]: any;
}
export interface NumAnyObj {
    [key: number]: any;
}
export declare type NIMEStrAnyObj = {
    [key: string]: any;
};
export declare type AnyArr = any[];
export declare type NIMServiceName = 'auth' | 'V1NIMLoginService' | 'V2NIMLoginService' | 'V2NIMLocalConversationService' | 'V2NIMConversationService' | 'V2NIMConversationIdUtil' | 'V2NIMConversationGroupService' | 'V2NIMMessageService' | 'V2NIMClientAntispamUtil' | 'V2NIMMessageLogUtil' | 'V2NIMMessageExtendUtil' | 'V2NIMNotificationService' | 'V2NIMStorageService' | 'V2NIMStorageUtil' | 'V2NIMTeamService' | 'V2NIMUserService' | 'V2NIMFriendService' | 'V2NIMSettingService' | 'V2NIMSyncService' | 'V2NIMAIService' | 'V2NIMSignallingService' | 'V2NIMSubscriptionService' | 'DataStructureConverter' | 'V2NIMMessageConverter' | 'V2NIMMessageCreator' | 'V2NIMMessageAttachmentCreator' | 'V2NIMPassthroughService' | 'YSFService' | 'msg' | 'msgLog' | 'team' | 'superTeam' | 'misc' | 'sync' | 'user' | 'session' | 'systemMessage' | 'friend' | 'event' | 'msgExtend' | 'passThrough' | 'plugin' | 'cloudSession' | 'signaling' | 'cloudStorage' | 'offlinePush' | 'qchatChannel' | 'qchatMedia' | 'qchatMsg' | 'qchatRole' | 'qchatServer';
export declare class NIMEBaseServiceClass<I extends object> {
    on<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    once<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    off<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    removeAllListeners<T extends keyof I>(eventName?: T | undefined): void;
}
/**
 * 基础 service 模块定义
 *
 * 因 typedoc 无法编译 nodeModules 里的 eventEmitter，故而只能己方写个定义
 */
export interface NIMEBaseServiceInterface<I extends object> {
    /**
     * 继承自 eventEmitter3 的监听事件方法
     */
    on<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    /**
     * 继承自 eventEmitter3 的监听事件方法
     */
    once<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    /**
     * 继承自 eventEmitter3 的取消监听方法
     */
    off<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    /**
     * 继承自 eventEmitter3 的移除事件方法
     */
    removeAllListeners<T extends keyof I>(eventName?: T): void;
}
export declare type NIMEBaseListener = {
    [key: string]: [...args: any];
};
export interface IBaseSendMsgOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 场景
     *
     * - 'p2p' (单人聊天)
     * - 'team' (群聊)
     * - 'superTeam' (超大群聊天)
     * @locale
     *
     * @locale en
     * Scenes
     *
     * 'p2p' (one-to-one chat)
     * 'team' (group chat)
     * 'superTeam' (super group chat)
     * @locale
     */
    scene: TMsgScene;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 接收方, 对方帐号或者群id
     * @locale
     *
     * @locale en
     * The recipient: the other party's account or group ID
     * @locale
     */
    to: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 文本消息内容。
     * @locale
     *
     * @locale en
     * The recipient: the other party's account or group ID
     * @locale
     */
    body?: string;
    /**
     * 发送方帐号
     */
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息客户端唯一标识。只在于重发时可以指定
     * @locale
     *
     * @locale en
     * The unique identifier (generated by the SDK) of the message. It can only be specified when resending the message.
     * @locale
     */
    idClient?: string;
    /**
     * 是否为本地，目前如果为 true，这条消息并不会被发出去。
     */
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段。消息发送后，消息体中的 `ext` 属性等于发送消息时设置的 `ext`属性值
     * @locale
     *
     * @locale en
     * Extension field
     * @locale
     */
    ext?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 第三方回调的扩展字段
     * @locale
     *
     * @locale en
     * Extension fields for third-party callbacks
     * @locale
     */
    callbackExt?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息子类型，格式为大于0的整数，开发者可自定义
     * @locale
     *
     * @locale en
     * Message subtype (format: an integer greater than 0)
     * @locale
     */
    subType?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息的杂项设置
     * @locale
     *
     * @locale en
     * Miscellaneous settings for messages
     * @locale
     */
    setting?: TMsgSetting;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 反垃圾相关配置
     * @locale
     *
     * @locale en
     * Anti-spam related configurations
     * @locale
     */
    antiSpamInfo?: TMsgAntiSpamInfo;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 推送相关配置
     * @locale
     *
     * @locale en
     * Push related configurations
     * @locale
     */
    pushInfo?: TMsgPushInfo;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 群组特化的杂项配置。
     *
     * 发送消息时，仅支持设置 `needACK` 属性
     * @locale
     *
     * @locale en
     * Miscellaneous configuration for group specialization
     * @locale
     */
    teamSpecializationInfo?: Pick<TMsgTeamSpecializationInfo, 'needACK'>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 回复消息时，可以设置 `replyMsg`。设置回复消息后，后续可通过消息扩展接口查询消息的回复关系。请参考文档：[消息扩展](https://doc.yunxin.163.com/messaging-enhanced/docs/DYzMDAyMjg?platform=web)
     *
     *
     * @example
     * ```js
     * // 回复消息
     * nim.msg.sendText({
     *   scene: 'p2p',
     *   to: 'test',
     *   text: 'reply hello world',
     *   replyMsg: msg,
     *   done: (e, r) => console.log(e ? '发送消息失败' : '发送消息成功')
     * })
     *
     * // 输入根消息节点，查询该消息所有的回复消息
     * const res = await nim.msgExtend.getThreadMsgs({
     *   "scene": "p2p",
     *   "threadMsgFromAccount": "zk1",
     *   "threadMsgIdServer": rootMsg.idServer,
     *   "threadMsgTime": rootMsg.time,
     *   "threadMsgToAccount": "zk2",
     *   "limit": 100,
     *   "reverse": false
     * })
     * ```
     * @locale
     *
     * @locale en
     * Message body that is replied
     * For example, assume that there is message A, when a user replies to message A, “replyMsg” needs to be passed into message A.
     * It requires the following fields:
     * A ==> from, to, time, idServer, idClient
     * A.threadMessageInfo ==> threadMsgFromAccount, threadMsgToAccount, threadMsgTime, threadMsgIdServer, threadMsgIdClient
     * @locale
     */
    replyMsg?: IMMessage;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送前的回调函数，用于发送前拿到这个消息对象
     *
     * - 此时这个消息对象还没有 idServer 和准确的 time，并且 status 也只是在 sending
     * - 发送失败时，可以用该回调保存消息，然后调用 {@link MsgServiceInterface.resendMsg} 重发
     *
     * @example
     * ```js
     * try {
     *  let tempMsg
     *  await nim.msg.sendTextMsg({
     *    scene: 'p2p',
     *    to: 'test',
     *    body: 'message',
     *    onSendBefore: (msg) => {
     *      // 发送消息前，通过该回调临时存储消息
     *      tempMsg = msg
     *    }
     *   })
     * } catch(err) {
     *    // 发送失败
     *    tempMsg.status = 'sendFailed'
     * }
     *
     * // 如果发送失败，调用 resendMsg 重发消息
     * nim.msg.resendMsg({
     *   msg: tempMsg
     * })
     * ```
     * @locale
     *
     * @locale en
     * The callback function before sending, used to get the message object before sending
     * At this time, the message has no “idServer” and accurate “time”, and the “status” is just “sending”.
     * @locale
     */
    onSendBefore?: (msg: IMMessage) => void;
}
export interface ISendMsgOptions extends IBaseSendMsgOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息类型
     * @locale
     *
     * @locale en
     * Message type
     * @locale
     */
    type: TMsgType;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 附件，可能是文件，也可能是地理位置信息等
     * @locale
     *
     * @locale en
     * Attachments, which can be files, geolocation information, etc.
     * @locale
     */
    attach?: string;
    /**
     * AI自动对话机器人消息
     */
    robotInfo?: {
        /**
         * 指定机器人具体功能，用户填写
         */
        function: string;
        /**
         * 指定话题，用户填写
         */
        topic: string;
        /**
         * 指定自定义内容，用户填写
         */
        customContent: string;
        /**
         * 指定机器人账号，用户填写。(仅在群聊中有效，p2p会被忽略)
         */
        account?: string;
    };
}
export interface TGEO {
    title: string;
    lat: number;
    lng: number;
}
/**
 * @Multi_Lang_Tag
 * @locale cn
 * 发送文本消息的参数
 * @locale
 *
 * @locale en
 * Parameters for sending text messages
 * @locale
 */
export interface ISendTextMsgOptions extends IBaseSendMsgOptions {
    body: string;
    attach?: string;
}
export interface ISendTipMsgOptions extends IBaseSendMsgOptions {
    body: string;
    attach?: string;
}
export interface ISendGeoLocationMsgOptions extends IBaseSendMsgOptions {
    /**
     * 包含三个属性:
     *
     * - lat: 纬度
     * - lng: 经度
     * - title: 名称
     */
    attach: TGEO;
}
export interface ISendCustomMsgOptions extends IBaseSendMsgOptions {
    attach: string;
}
export interface IBaseUploadFileOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * JS 的 File 对象。该属性和 `fileInput`, `filePath`仅需要填一个即可
     *
     * 该属性仅在使用 NIM_BROWSER_SDK.js 时可以使用
     * @locale
     *
     * @locale en
     * File object of JS
     *
     * Browser specific
     * @locale
     */
    file?: File;
    /**
     * @deprecated
     *
     * <br>
     *
     * 请使用 {@link IBaseUploadFileOptions.file} 属性
     *
     * @Multi_Lang_Tag
     * @locale cn
     * 存储文件的 DOM 元素，与上面的 file 只要选填一个就可以了。
     *
     * - 该属性仅在使用 NIM_BROWSER_SDK.js 时可以使用
     * - 如果传字符串，最后会以 document.getElementById('fileInput').files[0] 拿到 File 对象
     * - 如果传的是 DOM 节点，最后以 fileInput.files[0] 拿到 File 对象
     * @locale
     *
     * @locale en
     * The DOM element that stores the file
     *
     * Browser specific, about to be deprecated
     *
     * If you pass a string, you will finally get the File object with document.getElementById('fileInput').files[0]
     * If the DOM node is passed, finally get the File object with fileInput.files[0]
     * @locale
     *
     */
    fileInput?: string | HTMLInputElement;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 临时文件路径
     *
     * - uni-app，小程序等特殊的 JS 运行环境专用（chooseImage 拿到的临时路径）
     * - 仅当使用 NIM_UNIAPP_SDK.js, NIM_MINIAPP_SDK.js 时可以使用
     *
     * @example
     * ```js
     * // 微信小程序选择音频，并发送音频文件消息
     * wx.chooseMedia({
     *  count: 1,
     *  mediaType: ['vedio'],
     *  success: (res) => {
     *    let r = this.nim.msg.sendAudioMsg({
     *      "scene": "p2p",
     *      "to": "cs5",
     *      "filePath": res.tempFiles[0].tempFilePath
     *    })
     *    r.then((res) => {
     *       // 输出结果
     *       console.log('sendAudioMsg 完成：', res)
     *    }).catch((e) => {
     *       console.error('sendAudioMsg 出错：', e)
     *    })
     *  }
     * })
     * ```
     * @locale
     *
     * @locale en
     * Temporary file path
     *
     * uni-app, RN, applet, and other special JS runtime environment dedicated (temporary path obtained by chooseImage)
     * @locale
     */
    filePath?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * maxSize 限制文件大小。
     *
     * 只对浏览器生效。
     *
     * uni-app，小程序等，由于sdk只能得到一个 filePath 临时路径，不能得到整个文件的信息。
     * 所以请开发者自行在选择文件后进行判断，参考那些端的API如 wx.chooseImage，uni.chooseImage
     * @locale
     *
     * @locale en
     * maxSize : maximum file size.
     *
     * Only valid for browsers.
     *
     * For uni-app, applet, etc., because the SDK can only get a temporary filePath path, the information of the entire file cannot be obtained.
     * So please make your determination after selecting the file. Refer to those frame/platform’s APIs such as wx.chooseImage, uni.chooseImage.
     * @locale
     */
    maxSize?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 存储场景，不传默认实例化配置，默认为"im"
     *
     * #### 常见说明
     * - 常用场景为根据资源类型设置存储场景，然后设置定时任务调用 [清理文件](https://doc.yunxin.163.com/messaging/docs/zQyNDM0NzE?platform=server#%E6%B8%85%E7%90%86%E5%B7%B2%E4%B8%8A%E4%BC%A0%E6%96%87%E4%BB%B6) 删除。
     * - IM 默认的存储场景为: 'im'
     * - Chatroom 默认的存储场景为: 'chatroom'
     *
     * @locale
     *
     * @locale en
     * Storage scene (default: instantiation configuration, i.e., "im")
     * @locale
     */
    nosScenes?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 存储有效时间，不传则默认实例化配置
     *
     * 不得小于一天，单位秒
     * @locale
     *
     * @locale en
     * Storage valid time (default: instantiation configuration)
     *
     * Cannot be less than one day (unit: second)
     * @locale
     */
    nosSurvivalTime?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上传进度
     * @locale
     *
     * @locale en
     * Upload progress
     * @locale
     */
    onUploadProgress?: (obj: FileProgressObject) => void;
}
export interface IUploadFileOptions extends IBaseUploadFileOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 文件类型
     * @locale
     *
     * @locale en
     * File type
     * @locale
     */
    type?: 'image' | 'audio' | 'video' | 'file';
}
export interface MixStoreTokenResTag {
    token: string;
    shortUrl: string;
    objectName: string;
    accessKeyId: string;
    secretAccessKey: string;
    sessionToken: string;
    region: string;
    maxRetries: number;
    bucket: string;
}
export interface IBaseSendFileOptions extends IBaseSendMsgOptions, IBaseUploadFileOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 如果通过 {@link CloudStorageServiceInterface.uploadFile} 上传过文件，则直接使用上传结果作为 `attach`即可。
     *
     * @example
     * ```js
     * // 先上传文件，再发送文件消息
     * const file = document.getElementById('file-input').files[0]
     *
     * const uploadFileRes = await nim.cloudStorage.uploadFile({
     *    type: 'audio'
     *    file: file
     * })
     *
     * nim.msg.sendAudioMsg({
     *    scene: 'p2p',
     *    to: 'test',
     *    attach: uploadFileRes
     * })
     * ```
     * @locale
     *
     * @locale en
     * If you have got uploadFileResult via the uploadFile method, the information of uploadFileResult will be directly passed to the “attach” field
     * After that, messages will be directly sent to the IM server, hence it is not required to upload file(s) again.
     * @locale
     */
    attach?: UploadFileResult;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上传前回调事件。可以使用该回调参数的 `abort` 函数取消上传
     *
     * @example
     * ```js
     * // 取消上传
     * const file = document.getElementById('file-input').files[0]
     *
     * nim.msg.sendImageMsg({
     *    scene: 'p2p',
     *    to: 'test',
     *    file: file,
     *    onUploadStart: (task) => {
     *        // 调用 abort 取消上传
     *        task.abort()
     *    }
     * })
     * ```
     * @locale
     *
     * @locale en
     * Upload callback event
     * @locale
     */
    onUploadStart?: (task: {
        abort: () => void;
        [key: string]: any;
    }) => void;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上传完成的回调
     * @locale
     *
     * @locale en
     * Upload the completed callback
     * @locale
     */
    onUploadDone?: (file: UploadFileResult) => void;
}
export interface ISendFileOptions extends IBaseSendFileOptions {
    type: 'image' | 'audio' | 'video' | 'file';
}
export interface V2NIMError extends Error {
    /**
     * 错误码
     *
     * 注: 客户端错误码范围: 190000 ~ 199999
     */
    code: number;
    /**
     * 错误描述
     */
    desc: string;
    /**
     * 错误详情
     */
    detail: {
        /**
         * 可能的详细错误描述
         */
        reason?: string;
        /**
         * 原始错误
         */
        rawError?: Error;
        /**
         * 请求返回的原始数据
         */
        rawData?: string;
        /**
         * 错误发生的时间
         */
        timetag?: number;
        [key: string]: any;
    };
}
/**
 * 易盾反垃圾配置
 *
 * 使用场景: 未使用云信安全通, 直接对接了易盾
 */
export declare type V2NIMAntispamConfig = {
    /**
     * 指定易盾业务ID，而不使用云信后台配置的安全通
     *
     * @example
     * ```
     * '{"textbid":"","picbid":""}'
     * ```
     */
    antispamBusinessId: string;
};
