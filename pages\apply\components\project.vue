<template>
	<custom-header style="height: 88rpx;" title="打孔管理" showBack />
	<view class="con_content">
		<view class="search-bar">
			<view class="bar">

				<input type="text" class="sear_inp" placeholder="请输入钻机名称" @input="clearInput" v-model="searchText" />
				<icon size="16" type="clear" v-if="showClearIcon" @click="clearIcon"></icon>
			</view>
			<!-- <up-input class="sear_inp" placeholder="钻具名称/钻具ID" prefixIcon="search" v-model="searchText"
				color="rgba(255,255,255,0.8)" prefixIconStyle="font-size: 22px;color: #909399"></up-input> -->
			<button class="con_btn" @click="search">查询</button>
		</view>


		<view style="height:calc(100% - 150rpx);magin:10rpx;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
			<!-- <view> -->
			<zb-table @sort-change="sortChange" :pullUpLoading="pullUpLoading" :isShowLoadMore="true" :highlight="true"
				:show-header="true" :columns="column" :fit="false" :permissionBtn="permissionBtn" :stripe="true"
				row-key="id" @rowClick="rowClick" :border="false" :data="data"></zb-table>

		</view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request.js'
	let that = null
	export default {
		components: {
			customHeader,

			// siteCont
		},
		data() {
			const baseData = {
				lanewayId: "HANGLUO001",
				name: "主运输巷",
				type: "运输巷",
				length: "1200米",
				width: "5米",
				height: "3米",
				tiltAngle: "5°",
				startTime: '2022-03-01',
				endTime: '2024-06-30',
				status: 0,
				responsiblePerson: '巷道管理员李四',
				remark: "其他信息",
				noodlesId: 'CAIMIAN001',
				noodlesName: "1号采面"
			};
			const generateData = () => {
				const repeatedData = [];
				for (let i = 0; i < 10; i++) {
					repeatedData.push({
						id: i,
						...baseData
					})
				}
				return repeatedData;
			}
			return {
				dateStart: '',
				dateEnd: '',
				id: null,
				searchText: '',
				column: [
					// { type:'selection', fixed:true,width:60 },
					{
						name: 'drillName',
						label: '钻机名称',
						width: 120
						// fixed: true,
						// align: 'center',
						// emptyString: '--'
					},
					{
						name: 'position',
						label: '施工地点',
						width: 130
						// fixed: true,
						// align: 'center',
						// emptyString: '--'
					},
					{
						name: 'constructionLocation',
						label: '施工位置',
						width: 140

					},
					{
						name: 'team',
						label: '班组',
						// sorter: 'custom',
					},
					{
						name: 'holeNumber',
						label: '孔号',
						// sorter: 'custom',
					},
					{
						name: 'holeDiameter',
						label: '孔径（mm）',
						fixed: true,
						width: 125
					},
					{
						name: 'holeAngle',
						label: '开孔角度（°）',
						width: 140
					},
					{
						name: 'direction',
						label: '方位（°）',
						// sorter: true
					},
					{
						name: 'holeHeight',
						label: '开孔高度（m）',
						width: 140
					},
					{
						name: 'coalDistance',
						label: '见煤距离（m）',
						width: 140
						// sorter: true
					},
					{
						name: 'rockDistance',
						label: '见岩石距离（m）',
						width: 155
					},

					// {
					// 	name: 'holeAngleError',
					// 	label: '开孔角度误差（°）',
					// 	width:160
					// },
					// {
					// 	name: 'holeDirectionError',
					// 	label: '开孔方位误差（°）',
					// 	width:160
					// },
					// {
					// 	name: 'reamingStartDistance',
					// 	label: '扩孔起始距离',
					// 	width:130
					// },
					// {
					// 	name: 'drillingStartDistance',
					// 	label: '打钻起始距离',
					// 	width:130
					// },
					// {
					// 	name: 'estimatedCoalOutput',
					// 	label: '预计出煤量',
					// 	width:120
					// },
					// {
					// 	name: 'holeAngleError',
					// 	label: '开孔角度误差',
					// 	width:130
					// },
					// {
					// 	name: 'holeDirectionError',
					// 	label: '开孔方位误差',
					// 	width:130
					// },
					// {
					// 	name: 'holeHeightError',
					// 	label: '开孔高度误差',
					// 	width:130
					// },
					// {
					// 	name: 'secondaryDrill',
					// 	label: '是否补发',
					// 	type: 'second',
					// 	filters: {
					// 		0: "否",
					// 		1: "是"
					// 	}
					// },
					// 					{
					// 						name: 'operation',
					// 						type: 'operation',
					// 						label: '操作',
					// 						align: 'center',
					// 						renders: [{
					// 								name: '查看钻场',
					// 								class: 'edit',
					// 								type: "primary",
					// 								align: 'center',
					// 								func: 'edit' // func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
					// 							},

					// 						]
					// 					},
				],
				// data: generateData(),
				data: [], //表格数据
				data1: [],
				flag1: true,
				flag2: true,
				showClearIcon: false,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				isShowLoadMore: true, // 是否显示"加载更多"  
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
			}
		},
		onLoad() {
			// this.handelDrill()
		},
		created() {
			that = this
		},
		mounted() {
			this.handelDrill();
		},
		methods: {
			clearInput(event) {
				this.searchText = event.detail.value;
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;

				} else {
					this.showClearIcon = false;
				}
			},
			clearIcon() {
				this.searchText = '';
				this.currentPage = 1; // 重置页码
				this.data = []; // 清空数据

				this.isShowLoadMore = true; // 重置加载更多
				this.handelDrill(); // 重新加载数据
				this.showClearIcon = false;

			},
			search() {
				// console.log(event);
				// this.searchText = event.detail.value;
				console.log('handleSearchInput', this.searchText);
				this.searchText = this.searchText;
				this.currentPage = 1; // 重置页码
				this.data = []; // 清空数据
				this.isShowLoadMore = true; // 重置加载更多
				this.handelDrill(); // 重新加载数据
			},
			//初始加载调取接口获取数据
			async handelDrill() {

				try {
					let main_data = {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord: this.searchText
					};
					const res = await Request.post('/drill/get_ls', main_data)

					if (res.status == 0) {
						// console.log('返回数据', res);
						this.data = res.data.items;
						if (res.data.items.length < this.perPage) {
							this.isShowLoadMore = false;
						}
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });

					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}

				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}

			},

			change(e) {
				console.log("e:", e);
			},
			sortChange(item, sorterMode, index) {

				console.log('触发排序', item, sorterMode, index)
			},
			async pullUpLoading(done) {
				if (!this.flag1) {
					return
				}
				this.currentPage++; // 页码加1
				try {
					const res = await Request.post('/drill/get_ls', {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord: this.searchText
					});

					if (res.status == 0) {
						console.log('加载获取数据', res.data);
						// this.data.push(res.data.items);
						console.log('data11111', this.data);
						if (res.data.items && res.data.items.length > 0) {
							this.data = this.data.concat(res.data.items);
							console.log('data11111', this.data);
							done(); // 通知 zb-table 加载完成
						} else {
							this.isShowLoadMore = false; // 没有更多数据，不再显示加载更多
							done('ok'); // 通知zb-table 没有更多数据
							this.flag1 = false
							uni.showToast({
								title: '暂无更多数据',
								icon: 'none',
								duration: 1000
							})
						}

					} else {

						//    uni.showToast({
						// 	title: '加载数据失败' ,
						// 	icon: 'none',
						// 	duration: 1000
						// })
						done(); // 结束加载
					}
				} catch (error) {
					console.error("加载更多数据失败:", error);
					// uni.showToast({
					// 	title: '加载数据失败' ,
					// 	icon: 'none',
					// 	duration: 1000
					// })
					done(); //  结束加载
				}
				// setTimeout(() => {
				// 	this.data.push({
				// 		serialNum: 'ZJD0021',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	},
				// 	{
				// 		serialNum: 'ZJD0022',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	})

				// 	this.num++
				// 	if (this.num === 3) {
				// 		done('ok')
				// 		this.flag1 = false
				// 	} else {
				// 		done()
				// 	}
				// }, 2000)
			},
			permissionBtn(row, renders, rowIndex) {
				if (row.id == 2) {
					let arr = renders.filter(item => item.func === 'edit')
					return arr
				}
				return renders
			},

			buttonEdit(ite, index) {
				// console.log('点击查看钻场',ite,ite.id);
				this.$emit('switchToSite', ite.id);
			},

			rowClick(row, index) {
				console.log('点击巷道单行', row, row.id);
				uni.navigateTo({
					url: `/pages/apply/components/plan/drilldetail?id=${row.id}`
				})
			}
		},

	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style scoped lang="scss">
	page {
		background: #16171b;
	}

	::v-deep {
		.uni-card {
			margin: 8px !important;
			padding: 0 !important;

			.uni-card__content {
				padding: 0 !important;
			}
		}
	}

	.filter {
		// margin: 28rpx 0;
		height: 70rpx;
		display: flex;
		// background: #fff;
	}

	.sear_inp {
		height: 70rpx;
		// padding: 10rpx 28rpx;
		padding-left: 28rpx;
		font-size: 25rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.select {
		flex: 1;
		border: none;
		// border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		// background: rgba(255, 255, 255, 0.08);
	}

	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
	}

	.bar {
		flex: 1;
		padding-right: 10rpx;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
		display: flex;
		align-items: center;
	}

	.con_btn {
		height: 65rpx;
		line-height: 65rpx;
		background: #007BFF;
		letter-spacing: 0px;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.sear_inp {
		flex: 1;
		margin-right: 16rpx;
		// background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}


	.con_content {
		border-radius: 0 0 12rpx 12rpx;
		padding: 0 32rpx;
		padding-top: 156rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		height: calc(100vh - 150rpx);
		overflow: hidden;
		// background: #fff;
	}
</style>