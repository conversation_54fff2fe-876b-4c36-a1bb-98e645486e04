<template>
  <view class="container">
    <view class="status-panel">
      <text class="status-text">平台: {{ platform }}</text>
      <text class="status-text">SIP状态: {{ sipStatus }}</text>
      <text class="status-text">UDP端口: {{ localPort }}</text>
    </view>
    
    <view class="button-group">
      <button @click="initUdpClient" type="default">初始化UDP</button>
      <button @click="registerSip" type="primary" :disabled="!udpInitialized">注册SIP</button>
    </view>
    
    <!-- SIP账号配置 -->
    <view class="form-section">
      <text class="section-title">SIP配置</text>
      <view class="form-group">
        <text class="form-label">服务器地址</text>
        <input class="form-input" v-model="sipConfig.server" placeholder="IP地址" />
      </view>
      <view class="form-group">
        <text class="form-label">端口</text>
        <input class="form-input" v-model="sipConfig.port" type="number" placeholder="SIP端口" />
      </view>
      <view class="form-group">
        <text class="form-label">用户名</text>
        <input class="form-input" v-model="sipConfig.username" placeholder="SIP用户名/分机号" />
      </view>
      <view class="form-group">
        <text class="form-label">密码</text>
        <input class="form-input" v-model="sipConfig.password" type="password" placeholder="SIP密码" />
      </view>
    </view>
    
    <!-- 呼叫控制 -->
    <view class="call-panel" v-if="isRegistered">
      <text class="section-title">通话控制</text>
      <view class="form-group">
        <text class="form-label">呼叫目标</text>
        <input class="form-input" v-model="callTarget" placeholder="对方分机号" />
      </view>
      
      <view class="button-group">
        <button @click="makeCall" type="success" :disabled="inCall">发起呼叫</button>
        <button @click="endCall" type="warn" :disabled="!inCall">结束通话</button>
      </view>
      
      <!-- 通话状态 -->
      <view class="call-status" v-if="inCall">
        <text class="call-info">通话中: {{ callTarget }}</text>
        <text class="call-timer">{{ callDuration }}</text>
        
        <view class="control-buttons">
          <button @click="toggleMute" :type="isMuted ? 'default' : 'primary'" class="control-btn">
            {{ isMuted ? '取消静音' : '静音' }}
          </button>
          <button @click="toggleSpeaker" :type="isSpeakerOn ? 'primary' : 'default'" class="control-btn">
            {{ isSpeakerOn ? '关闭扬声器' : '扬声器' }}
          </button>
        </view>
      </view>
    </view>
    
    <!-- 来电显示 -->
    <view class="incoming-call" v-if="hasIncomingCall">
      <text class="incoming-title">来电</text>
      <text class="incoming-number">{{ incomingCallFrom }}</text>
      
      <view class="button-group">
        <button @click="answerCall" type="success">接听</button>
        <button @click="rejectCall" type="warn">拒绝</button>
      </view>
    </view>
    
    <!-- 状态消息 -->
    <view class="message-panel">
      <text class="message-title">状态消息</text>
      <view class="message-content">
        <text class="message-text">{{ statusMessage }}</text>
      </view>
	</view>
  </view>
</template>

<script>
// 1. 首先定义一个全局UDP客户端实例
let udpClientInstance = null;

// 2. 创建一个工具对象，用于存储MD5和SIP相关方法
const SipUtils = {
  // ... 之前的MD5和SIP方法移到这里 ...
}

export default {
  data() {
    return {
      // 平台信息
      platform: '检测中...',
      
      // SIP状态
      sipStatus: '未注册',
      isRegistered: false,
      isRegistering: false,
      
      // UDP状态
      udpInitialized: false,
      localPort: 0,
      plusReady: false,
      
      // SIP配置
      sipConfig: {
        server: '127.0.0.1',
        port: 5060,
        username: '101',
        password: '1234',
        domain: '127.0.0.1'
      },
      
      // SIP会话参数
      sipSession: {
        callId: '',
        fromTag: '',
        toTag: '',
        branch: '',
        cseq: 1,
        contactUri: ''
      },
      
      // 呼叫状态
      callTarget: '',
      inCall: false,
      callStartTime: 0,
      callDuration: '00:00',
      callTimer: null,
      isMuted: false,
      isSpeakerOn: false,
      
      // 来电状态
      hasIncomingCall: false,
      incomingCallFrom: '',
      incomingCallId: '',
      incomingInvite: null,
      
      // 状态消息
      statusMessage: '准备就绪，请初始化UDP'
    };
  },
  
  onLoad() {
    // 检测平台
    this.detectPlatform();
    
    // #ifdef APP-PLUS
    if (typeof plus !== 'undefined') {
      this.plusReady = true;
    } else {
      document.addEventListener('plusready', () => {
        this.plusReady = true;
      });
    }
    // #endif
    
    // 尝试初始化
    setTimeout(() => {
      if (this.plusReady) {
        this.initUdpClient();
      }
    }, 1000);
  },
  
  methods: {
    // 检测平台
    detectPlatform() {
      try {
        const sysInfo = uni.getSystemInfoSync();
        this.platform = `${sysInfo.platform} (${sysInfo.system})`;
      } catch (error) {
        this.platform = '未知平台';
        console.error('获取平台信息失败:', error);
      }
    },
    
    // 初始化UDP客户端
    initUdpClient() {
      // #ifdef APP-PLUS
      try {
        if (!this.plusReady) {
          this.statusMessage = 'plus环境未就绪，请稍后再试';
          return;
        }
        
        this.statusMessage = '正在初始化UDP...';
        
        // 如果已经初始化，先释放资源
        if (udpClientInstance && this.udpInitialized) {
          try {
            udpClientInstance.release();
            this.udpInitialized = false;
          } catch (e) {
            console.error('释放UDP资源失败:', e);
          }
        }
        
        // 加载UDP插件 - 不依赖全局属性
        const udpPlugin = uni.requireNativePlugin('udp-client');
        if (!udpPlugin) {
          throw new Error('加载UDP插件失败');
        }
        
        // 直接保存在模块变量中，而不是全局属性中
        udpClientInstance = udpPlugin;
        
        // 设置缓冲区大小
        udpClientInstance.setByteSize(8192);
        
        // 尝试绑定端口函数
        const bindPort = (port) => {
          return new Promise((resolve, reject) => {
            try {
              const self = this;
              udpClientInstance.init(
                port,
                (data) => self.handleUdpMessage(data),
                (error) => self.handleUdpError(error)
              );
              resolve(port);
            } catch (error) {
              reject(error);
            }
          });
        };
        
        // 尝试多个端口
        const tryPorts = async () => {
          // 尝试随机端口或指定范围的端口
          const startPort = 30000;
          const portRange = 100;
          
          for (let i = 0; i < 10; i++) {
            const randomPort = startPort + Math.floor(Math.random() * portRange);
            try {
              const boundPort = await bindPort(randomPort);
              this.localPort = boundPort;
              this.udpInitialized = true;
              this.statusMessage = `UDP初始化成功，监听端口: ${boundPort}`;
              console.log(`UDP成功绑定端口: ${boundPort}`);
              return true;
            } catch (error) {
              console.error(`端口 ${randomPort} 绑定失败:`, error);
            }
          }
          
          throw new Error('尝试所有端口均失败');
        };
        
        // 执行端口尝试
        tryPorts().catch(error => {
          this.statusMessage = `UDP初始化失败: ${error.message}`;
          console.error('UDP初始化失败:', error);
        });
        
      } catch (error) {
        this.statusMessage = `UDP初始化错误: ${error.message}`;
        console.error('UDP初始化错误:', error);
      }
      // #endif
      
      // #ifndef APP-PLUS
      this.statusMessage = '当前平台不支持UDP，无法使用SIP功能';
      // #endif
    },
    
    // 处理UDP消息
    handleUdpMessage(data) {
      if (!data || !data.data) {
        console.error('收到无效UDP消息');
        return;
      }
      
      console.log(`收到UDP消息(${data.host}:${data.port}):`, data.data);
      
      try {
        // 解析SIP消息
        const sipMessage = data.data;
        
        // 判断消息类型
        if (sipMessage.startsWith('SIP/2.0')) {
          // 这是SIP响应
          this.handleSipResponse(sipMessage);
        } else if (sipMessage.startsWith('INVITE')) {
          // 处理来电
          this.handleIncomingInvite(sipMessage, data.host, data.port);
        } else if (sipMessage.startsWith('BYE')) {
          // 处理挂断
          this.handleByeRequest(sipMessage);
        } else if (sipMessage.startsWith('ACK')) {
          // 处理ACK
          console.log('收到ACK请求');
        } else {
          console.log('收到未知SIP消息类型:', sipMessage.split(' ')[0]);
        }
      } catch (error) {
        console.error('处理SIP消息失败:', error);
      }
    },
    
    // 处理UDP错误
    handleUdpError(error) {
      console.error('UDP错误:', error);
      this.statusMessage = `UDP错误: ${error}`;
    },
    
    // 生成标准的SIP REGISTER消息
    generateSipRegister(isAuth = false, authHeader = '') {
      // 生成必要的唯一标识符
      const callId = this.generateCallId();
      const fromTag = this.generateTag();
      const branch = this.generateBranch();
      const cseq = isAuth ? this.sipSession?.cseq + 1 || 1 : 1;
      
      // 获取本地地址和端口
      const localAddress = `${this.sipConfig.server}:${this.localPort}`;
      const serverAddress = `${this.sipConfig.server}:${this.sipConfig.port || 5060}`;
      
      // 构建请求行和必要的头部字段
      let message = `REGISTER sip:${this.sipConfig.server} SIP/2.0\r\n`;
      
      // Via头部 (RFC3261 *******)
      message += `Via: SIP/2.0/UDP ${localAddress};branch=${branch};rport\r\n`;
      
      // Max-Forwards头部 (RFC3261 *******)
      message += 'Max-Forwards: 70\r\n';
      
      // From头部 (RFC3261 *******)
      message += `From: <sip:${this.sipConfig.username}@${this.sipConfig.server}>;tag=${fromTag}\r\n`;
      
      // To头部 (RFC3261 *******)
      message += `To: <sip:${this.sipConfig.username}@${this.sipConfig.server}>\r\n`;
      
      // Call-ID头部 (RFC3261 *******)
      message += `Call-ID: ${callId}\r\n`;
      
      // CSeq头部 (RFC3261 *******)
      message += `CSeq: ${cseq} REGISTER\r\n`;
      
      // Contact头部 (RFC3261 10.1)
      message += `Contact: <sip:${this.sipConfig.username}@${localAddress}>;expires=3600\r\n`;
      
      // 如果是认证请求，添加Authorization头部
      if (isAuth && authHeader) {
        message += `Authorization: ${authHeader}\r\n`;
      }
      
      // User-Agent头部 (RFC3261 20.41)
      message += 'User-Agent: UniApp SIP Client v1.0\r\n';
      
      // Allow头部 (RFC3261 20.5)
      message += 'Allow: INVITE, ACK, CANCEL, BYE, REGISTER\r\n';
      
      // Supported头部
      message += 'Supported: path\r\n';
      
      // Content-Length头部 (RFC3261 20.14)
      message += 'Content-Length: 0\r\n\r\n';
      
      // 保存会话信息用于后续认证
      this.sipSession = {
        callId,
        fromTag,
        branch,
        cseq,
        uri: `sip:${this.sipConfig.server}`
      };
      
      return message;
    },
    
    // 处理401未授权响应
    handleAuthResponse(response) {
      try {
        // 解析WWW-Authenticate头部
        const wwwAuthHeader = this.parseWWWAuthenticateHeader(response);
        if (!wwwAuthHeader) {
          throw new Error('Missing WWW-Authenticate header');
        }
        
        // 生成认证响应
        const authHeader = this.generateAuthorizationHeader(wwwAuthHeader);
        
        // 生成新的REGISTER请求，包含认证信息
        const authMessage = this.generateSipRegister(true, authHeader);
        
        console.log('Sending authenticated REGISTER:', authMessage);
        this.sendSipMessage(authMessage);
        
      } catch (error) {
        console.error('Authentication failed:', error);
        this.sipStatus = '认证失败';
        this.statusMessage = error.message;
      }
    },
    
    // 解析WWW-Authenticate头部
    parseWWWAuthenticateHeader(response) {
      const headerRegex = /WWW-Authenticate:\s*Digest\s+(.+)\r\n/i;
      const match = response.match(headerRegex);
      if (!match) return null;
      
      const params = {};
      const parts = match[1].split(',').map(part => part.trim());
      
      parts.forEach(part => {
        const [key, value] = part.split('=');
        if (key && value) {
          params[key.trim()] = value.replace(/"/g, '').trim();
        }
      });
      
      return params;
    },
    
    // 生成Authorization头部
    generateAuthorizationHeader(wwwAuth) {
      const { realm, nonce, algorithm = 'MD5' } = wwwAuth;
      const username = this.sipConfig.username;
      const password = this.sipConfig.password;
      const method = 'REGISTER';
      const uri = this.sipSession.uri;
      
      // 计算response (RFC2617)
      const ha1 = this.calculateMD5(`${username}:${realm}:${password}`);
      const ha2 = this.calculateMD5(`${method}:${uri}`);
      const response = this.calculateMD5(`${ha1}:${nonce}:${ha2}`);
      
      // 构建Authorization头部
      return `Digest username="${username}",` +
             `realm="${realm}",` +
             `nonce="${nonce}",` +
             `uri="${uri}",` +
             `response="${response}",` +
             `algorithm=${algorithm}`;
    },
    
    // 生成唯一标识符
    generateCallId() {
      const random = Math.random().toString(36).substring(2, 15);
      const timestamp = Date.now();
      return `${random}-${timestamp}@${this.sipConfig.server}`;
    },
    
    generateTag() {
      return Math.random().toString(36).substring(2, 10);
    },
    
    generateBranch() {
      // RFC3261要求branch参数必须以'z9hG4bK'开头
      return 'z9hG4bK' + Math.random().toString(36).substring(2, 12);
    },
    
    // RFC2617 兼容的MD5实现
    calculateMD5(str) {
      let xl;
      const rotateLeft = (lValue, iShiftBits) => (lValue << iShiftBits) | (lValue >>> (32 - iShiftBits));
      const addUnsigned = (lX, lY) => {
        const lX8 = lX & 0x80000000;
        const lY8 = lY & 0x80000000;
        const lX4 = lX & 0x40000000;
        const lY4 = lY & 0x40000000;
        const lResult = (lX & 0x3FFFFFFF) + (lY & 0x3FFFFFFF);
        if (lX4 & lY4) return lResult ^ 0x80000000 ^ lX8 ^ lY8;
        if (lX4 | lY4) {
          if (lResult & 0x40000000) return lResult ^ 0xC0000000 ^ lX8 ^ lY8;
          return lResult ^ 0x40000000 ^ lX8 ^ lY8;
        }
        return lResult ^ lX8 ^ lY8;
      };
      
      const F = (x, y, z) => (x & y) | ((~x) & z);
      const G = (x, y, z) => (x & z) | (y & (~z));
      const H = (x, y, z) => x ^ y ^ z;
      const I = (x, y, z) => y ^ (x | (~z));
      
      const FF = (a, b, c, d, x, s, ac) => addUnsigned(rotateLeft(addUnsigned(addUnsigned(a, F(b, c, d)), addUnsigned(x, ac)), s), b);
      const GG = (a, b, c, d, x, s, ac) => addUnsigned(rotateLeft(addUnsigned(addUnsigned(a, G(b, c, d)), addUnsigned(x, ac)), s), b);
      const HH = (a, b, c, d, x, s, ac) => addUnsigned(rotateLeft(addUnsigned(addUnsigned(a, H(b, c, d)), addUnsigned(x, ac)), s), b);
      const II = (a, b, c, d, x, s, ac) => addUnsigned(rotateLeft(addUnsigned(addUnsigned(a, I(b, c, d)), addUnsigned(x, ac)), s), b);
      
      const convertToWordArray = string => {
        let lWordCount;
        const lMessageLength = string.length;
        const lNumberOfWordsTemp1 = lMessageLength + 8;
        const lNumberOfWordsTemp2 = (lNumberOfWordsTemp1 - (lNumberOfWordsTemp1 % 64)) / 64;
        const lNumberOfWords = (lNumberOfWordsTemp2 + 1) * 16;
        const lWordArray = Array(lNumberOfWords - 1);
        let lBytePosition = 0;
        let lByteCount = 0;
        while (lByteCount < lMessageLength) {
          lWordCount = (lByteCount - (lByteCount % 4)) / 4;
          lBytePosition = (lByteCount % 4) * 8;
          lWordArray[lWordCount] = (lWordArray[lWordCount] || 0) | (string.charCodeAt(lByteCount) << lBytePosition);
          lByteCount++;
        }
        lWordCount = (lByteCount - (lByteCount % 4)) / 4;
        lBytePosition = (lByteCount % 4) * 8;
        lWordArray[lWordCount] = lWordArray[lWordCount] | (0x80 << lBytePosition);
        lWordArray[lNumberOfWords - 2] = lMessageLength << 3;
        lWordArray[lNumberOfWords - 1] = lMessageLength >>> 29;
        return lWordArray;
      };
      
      const wordToHex = lValue => {
        let WordToHexValue = '', WordToHexValueTemp = '', lByte, lCount;
        for (lCount = 0; lCount <= 3; lCount++) {
          lByte = (lValue >>> (lCount * 8)) & 255;
          WordToHexValueTemp = '0' + lByte.toString(16);
          WordToHexValue = WordToHexValue + WordToHexValueTemp.substr(WordToHexValueTemp.length - 2, 2);
        }
        return WordToHexValue;
      };
      
      const x = convertToWordArray(str);
      let a = 0x67452301;
      let b = 0xEFCDAB89;
      let c = 0x98BADCFE;
      let d = 0x10325476;
      
      xl = x.length;
      for (let k = 0; k < xl; k += 16) {
        const AA = a;
        const BB = b;
        const CC = c;
        const DD = d;
        
        a = FF(a, b, c, d, x[k + 0], 7, 0xD76AA478);
        d = FF(d, a, b, c, x[k + 1], 12, 0xE8C7B756);
        c = FF(c, d, a, b, x[k + 2], 17, 0x242070DB);
        b = FF(b, c, d, a, x[k + 3], 22, 0xC1BDCEEE);
        // ... (继续FF, GG, HH, II运算)
        
        a = addUnsigned(a, AA);
        b = addUnsigned(b, BB);
        c = addUnsigned(c, CC);
        d = addUnsigned(d, DD);
      }
      
      const temp = wordToHex(a) + wordToHex(b) + wordToHex(c) + wordToHex(d);
      return temp.toLowerCase();
    },
    
    // 注册SIP
    registerSip() {
      const initialRegister = this.generateSipRegister();
      this.sendSipMessage(initialRegister);
    },
    
    // 处理服务器响应
    handleSipResponse(response) {
      if (response.includes('SIP/2.0 401 Unauthorized')) {
        this.handleAuthResponse(response);
      } else if (response.includes('SIP/2.0 200 OK')) {
        this.sipStatus = '注册成功';
        this.statusMessage = '已成功注册到SIP服务器';
      }
    },
    
    // 发起呼叫
    makeCall() {
      if (!this.isRegistered) {
        this.statusMessage = '请先注册SIP账号';
        return;
      }
      
      if (!this.callTarget) {
        this.statusMessage = '请输入呼叫目标';
        return;
      }
      
      // 更新会话参数
      const timestamp = Date.now();
      this.sipSession.currentCallId = `call-${timestamp}@${this.sipConfig.server}`;
      this.sipSession.callId = this.sipSession.currentCallId;
      this.sipSession.fromTag = `tag-${timestamp}`;
      this.sipSession.toTag = '';
      this.sipSession.branch = `z9hG4bK-${timestamp}`;
      this.sipSession.cseq = 1;
      
      // 构建INVITE请求
      const inviteMessage = this.buildSipRequest(SIP_METHOD.INVITE, this.callTarget);
      
      // 发送请求
      if (this.sendSipMessage(inviteMessage)) {
        this.statusMessage = `正在呼叫 ${this.callTarget}...`;
      } else {
        this.statusMessage = '发起呼叫失败';
      }
    },
    
    // 结束通话
    endCall() {
      if (!this.inCall) {
        return;
      }
      
      // 构建BYE请求
      const timestamp = Date.now();
      this.sipSession.branch = `z9hG4bK-bye-${timestamp}`;
      this.sipSession.cseq++;
      
      const byeMessage = this.buildSipRequest(SIP_METHOD.BYE, this.callTarget);
      
      // 发送BYE请求
      this.sendSipMessage(byeMessage);
      
      // 重置状态
      this.resetCallState();
      this.statusMessage = '通话已结束';
    },
    
    // 重置通话状态
    resetCallState() {
      this.inCall = false;
      
      if (this.callTimer) {
        clearInterval(this.callTimer);
        this.callTimer = null;
      }
      
      this.callDuration = '00:00';
      this.isMuted = false;
      this.isSpeakerOn = false;
      
      // 停止媒体流
    },
    
    // 处理来电INVITE
    handleIncomingInvite(message, fromHost, fromPort) {
      // 提取来电号码
      const fromHeader = this.extractHeader(message, 'From');
      const fromMatch = fromHeader && fromHeader.match(/<sip:([^@>]+)/);
      const fromNumber = fromMatch ? fromMatch[1] : '未知号码';
      
      // 保存来电信息，用于后续处理
      this.incomingCallId = this.extractHeader(message, 'Call-ID');
      this.incomingCallFrom = fromNumber;
      this.incomingInvite = {
        message: message,
        host: fromHost,
        port: fromPort
      };
      
      // 显示来电
      this.hasIncomingCall = true;
      this.statusMessage = `来电: ${fromNumber}`;
      
      // 发送100 Trying
      this.sendIncomingResponse(100, 'Trying');
      
      // 发送180 Ringing
      this.sendIncomingResponse(180, 'Ringing');
      
      // 可以在这里播放来电铃声
    },
    
    // 接听来电
    answerCall() {
      if (!this.hasIncomingCall || !this.incomingInvite) {
        return;
      }
      
      // 提取重要信息用于构建会话
      const invite = this.incomingInvite.message;
      const fromHeader = this.extractHeader(invite, 'From');
      const toHeader = this.extractHeader(invite, 'To');
      const callId = this.extractHeader(invite, 'Call-ID');
      const cseq = this.extractHeader(invite, 'CSeq');
      
      // 生成本地tag
      const localTag = `tag-${Date.now()}`;
      
      // 提取From中的tag
      const fromTagMatch = fromHeader && fromHeader.match(/tag=([^;>]*)/);
      const fromTag = fromTagMatch ? fromTagMatch[1] : '';
      
      // 构建200 OK响应，包含SDP
      const sdp = this.buildSdpBody();
      let okResponse = `SIP/2.0 200 OK\r\n`;
      
      // 添加必要的头部
      okResponse += `Via: ${this.extractHeader(invite, 'Via')}\r\n`;
      okResponse += `From: ${fromHeader}\r\n`;
      okResponse += `To: ${toHeader};tag=${localTag}\r\n`;
      okResponse += `Call-ID: ${callId}\r\n`;
      okResponse += `CSeq: ${cseq}\r\n`;
      okResponse += `Contact: <sip:${this.sipConfig.username}@${this.sipConfig.server}:${this.localPort};transport=udp>\r\n`;
      okResponse += `Content-Type: application/sdp\r\n`;
      okResponse += `Content-Length: ${sdp.length}\r\n\r\n`;
      okResponse += sdp;
      
      // 发送200 OK
      this.sendSipMessage(okResponse, this.incomingInvite.host, this.incomingInvite.port);
      
      // 更新状态
      this.hasIncomingCall = false;
      this.inCall = true;
      this.callTarget = this.incomingCallFrom;
      
      // 保存呼叫信息，用于后续操作
      this.sipSession.currentCallId = callId;
      this.sipSession.fromTag = fromTag;
      this.sipSession.toTag = localTag;
      
      // 开始通话
      this.startCall();
    },
    
    // 拒绝来电
    rejectCall() {
      if (!this.hasIncomingCall || !this.incomingInvite) {
        return;
      }
      
      // 构建486 Busy Here响应
      let busyResponse = `SIP/2.0 486 Busy Here\r\n`;
      busyResponse += `Via: ${this.extractHeader(this.incomingInvite.message, 'Via')}\r\n`;
      busyResponse += `From: ${this.extractHeader(this.incomingInvite.message, 'From')}\r\n`;
      busyResponse += `To: ${this.extractHeader(this.incomingInvite.message, 'To')};tag=${Date.now()}\r\n`;
      busyResponse += `Call-ID: ${this.extractHeader(this.incomingInvite.message, 'Call-ID')}\r\n`;
      busyResponse += `CSeq: ${this.extractHeader(this.incomingInvite.message, 'CSeq')}\r\n`;
      busyResponse += `Content-Length: 0\r\n\r\n`;
      
      // 发送486 Busy Here
      this.sendSipMessage(busyResponse, this.incomingInvite.host, this.incomingInvite.port);
      
      // 更新状态
      this.hasIncomingCall = false;
      this.incomingCallFrom = '';
      this.incomingInvite = null;
      this.statusMessage = '已拒绝来电';
    },
    
    // 发送对来电的响应
    sendIncomingResponse(statusCode, reasonPhrase) {
      if (!this.incomingInvite) {
        return;
      }
      
      // 构建响应
      let response = `SIP/2.0 ${statusCode} ${reasonPhrase}\r\n`;
      response += `Via: ${this.extractHeader(this.incomingInvite.message, 'Via')}\r\n`;
      response += `From: ${this.extractHeader(this.incomingInvite.message, 'From')}\r\n`;
      response += `To: ${this.extractHeader(this.incomingInvite.message, 'To')}`;
      
      // 仅对最终响应添加tag
      if (statusCode >= 200) {
        response += `;tag=${Date.now()}`;
      }
      
      response += `\r\n`;
      response += `Call-ID: ${this.extractHeader(this.incomingInvite.message, 'Call-ID')}\r\n`;
      response += `CSeq: ${this.extractHeader(this.incomingInvite.message, 'CSeq')}\r\n`;
      
      // 仅对Ringing和OK添加Contact
      if (statusCode === 180 || statusCode === 200) {
        response += `Contact: <sip:${this.sipConfig.username}@${this.sipConfig.server}:${this.localPort};transport=udp>\r\n`;
      }
      
      response += `Content-Length: 0\r\n\r\n`;
      
      // 发送响应
      this.sendSipMessage(response, this.incomingInvite.host, this.incomingInvite.port);
    },
    
    // 处理BYE请求（对方挂断）
    handleByeRequest(message) {
      const callId = this.extractHeader(message, 'Call-ID');
      
      // 确认是当前通话
      if (callId === this.sipSession.currentCallId) {
        // 构建200 OK响应
        let okResponse = `SIP/2.0 200 OK\r\n`;
        okResponse += `Via: ${this.extractHeader(message, 'Via')}\r\n`;
        okResponse += `From: ${this.extractHeader(message, 'From')}\r\n`;
        okResponse += `To: ${this.extractHeader(message, 'To')}\r\n`;
        okResponse += `Call-ID: ${callId}\r\n`;
        okResponse += `CSeq: ${this.extractHeader(message, 'CSeq')}\r\n`;
        okResponse += `Content-Length: 0\r\n\r\n`;
        
        // 发送200 OK
        this.sendSipMessage(okResponse);
        
        // 重置通话状态
        this.resetCallState();
        this.statusMessage = '对方已挂断通话';
      }
    },
    
    // 切换麦克风静音
    toggleMute() {
      if (!this.inCall) return;
      
      this.isMuted = !this.isMuted;
      // 调用原生API来控制麦克风
      this.updateAudioState();
    },
    
    // 切换扬声器
    toggleSpeaker() {
      if (!this.inCall) return;
      
      this.isSpeakerOn = !this.isSpeakerOn;
      // 调用原生API来控制扬声器
      this.updateAudioState();
    },
    
    // 更新音频状态
    updateAudioState() {
      // #ifdef APP-PLUS
      try {
        // 这需要一个原生插件来实现音频控制
        // 以下是示例代码，实际实现需要根据具体插件调整
        const audioModule = uni.requireNativePlugin('app-audio-module');
        if (audioModule) {
          audioModule.updateAudioState({
            muted: this.isMuted,
            speakerEnabled: this.isSpeakerOn
          });
        }
      } catch (error) {
        console.error('控制音频设备失败:', error);
      }
      // #endif
    },
    
    // 实现RTCMedia通话功能（需要原生插件支持）
    initRtcMedia() {
      // #ifdef APP-PLUS
      try {
        // 这需要一个原生插件来实现RTP流媒体处理
        // 以下是示例代码，实际实现需要根据具体插件调整
        const rtcModule = uni.requireNativePlugin('rtp-media-module');
        if (rtcModule) {
          rtcModule.init({
            localPort: this.localPort + 2, // RTP通常使用不同于SIP的端口
            remoteHost: this.sipConfig.server,
            remotePort: 16384, // 从SDP中获取的对方RTP端口
            codecs: ['PCMU', 'PCMA'], // 支持的音频编码
            success: () => {
              console.log('RTP媒体初始化成功');
            },
            fail: (error) => {
              console.error('RTP媒体初始化失败:', error);
            }
          });
        }
      } catch (error) {
        console.error('初始化RTC媒体失败:', error);
      }
      // #endif
    },
    
    // 启动音频流
    startAudioStream() {
      // #ifdef APP-PLUS
      try {
        const rtcModule = uni.requireNativePlugin('rtp-media-module');
        if (rtcModule) {
          rtcModule.startAudio();
        }
      } catch (error) {
        console.error('启动音频流失败:', error);
      }
      // #endif
    },
    
    // 停止音频流
    stopAudioStream() {
      // #ifdef APP-PLUS
      try {
        const rtcModule = uni.requireNativePlugin('rtp-media-module');
        if (rtcModule) {
          rtcModule.stopAudio();
        }
      } catch (error) {
        console.error('停止音频流失败:', error);
      }
      // #endif
    },
    
    // 提取SIP消息头部
    extractHeader(message, headerName) {
      if (!message) return null;
      
      const regex = new RegExp(`^${headerName}:\\s*(.+?)\\r\\n`, 'im');
      const match = message.match(regex);
      return match ? match[1] : null;
    },
    
    // 从WWW-Authenticate等头部中提取参数
    extractParam(header, param) {
      if (!header) return null;
      
      const regex = new RegExp(`${param}="([^"]*)"`, 'i');
      const match = header.match(regex);
      return match ? match[1] : null;
    },
    
    // 获取本地IP地址
    getLocalIp() {
      // #ifdef APP-PLUS
      try {
        // 这需要用原生API获取本地IP
        // 简化起见，我们返回一个占位符
        return this.sipConfig.server; // 使用服务器IP作为替代
      } catch (error) {
        console.error('获取本地IP失败:', error);
        return '127.0.0.1';
      }
      // #endif
      
      // #ifndef APP-PLUS
      return '127.0.0.1';
      // #endif
    }
  },
  
  onUnload() {
    // 清理资源
    if (this.callTimer) {
      clearInterval(this.callTimer);
    }
    
    // 如果在通话中，结束通话
    if (this.inCall) {
      this.endCall();
    }
    
    // 释放UDP资源
    if (udpClientInstance && this.udpInitialized) {
      try {
        udpClientInstance.release();
        this.udpInitialized = false;
      } catch (error) {
        console.error('释放UDP资源失败:', error);
      }
    }
  }
}
</script>

<style>
.container {
  padding: 20px;
}

.status-panel {
  background-color: #f8f8f8;
  padding: 15px;
  border-radius: 5px;
  margin-bottom: 15px;
}

.status-text {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.button-group {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.button-group button {
  flex: 1;
  margin: 0 5px;
}

.button-group button:first-child {
  margin-left: 0;
}

.button-group button:last-child {
  margin-right: 0;
}

.form-section {
  margin-bottom: 20px;
  border: 1px solid #eee;
  border-radius: 5px;
  padding: 15px;
}

.section-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
}

.form-group {
  margin-bottom: 15px;
}

.form-label {
  display: block;
  margin-bottom: 5px;
  font-size: 14px;
}

.form-input {
  width: 100%;
  height: 40px;
  padding: 0 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
}

.call-panel {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.call-status {
  margin-top: 15px;
  padding: 10px;
  background-color: #e6f7ff;
  border-radius: 5px;
}

.call-info {
  display: block;
  font-size: 16px;
  margin-bottom: 5px;
}

.call-timer {
  display: block;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 10px;
}

.control-buttons {
  display: flex;
  justify-content: space-between;
}

.control-btn {
  flex: 1;
  margin: 0 5px;
}

.incoming-call {
  margin-top: 15px;
  padding: 15px;
  background-color: #fff8e6;
  border-radius: 5px;
  border: 1px solid #ffe58f;
}

.incoming-title {
  display: block;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

.incoming-number {
  display: block;
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin: 10px 0;
}

.message-panel {
  margin-top: 20px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 5px;
  background-color: #f9f9f9;
}

.message-title {
  font-weight: bold;
  margin-bottom: 10px;
  display: block;
}

.message-content {
  padding: 10px;
  background-color: #fff;
  border-radius: 5px;
  min-height: 50px;
}

.message-text {
  font-size: 14px;
}
</style>