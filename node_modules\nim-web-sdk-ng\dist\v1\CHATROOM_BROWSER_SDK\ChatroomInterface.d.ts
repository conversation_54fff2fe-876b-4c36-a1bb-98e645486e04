import { NIMEModuleParamCloudStorageConfig } from './CloudStorageServiceInterface';
import { Chatroom, ChatroomMember, ChatroomMessage } from './types';
export interface ChatroomInterface {
    /**
     * 实例状态
     *
     * - unconnected: 尚未建立连接(初始化、主动登出、达到最大重连次数)
     * - connecting: 正在建立连接中
     * - connected: 已连接，尚未完成鉴权认证
     * - logined: 已连接, 并且完成了鉴权认证，可以正常开始发送协议
     * - waitReconnect: 等待重连中
     * - destroyed: 实例已经销毁
     */
    status: NIMEChatroomInstanceStatus;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 建立连接，并且登录
     * @locale
     *
     * @locale en
     * Establish a WebSocket persistent connection and log in to the chatroom.
     * @locale
     */
    connect(): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 断开连接。
     *
     * - 退出登录状态，并断开 websocket 连接
     * - disconnect完成后，实例不会被销毁，可再次 connect 方法登录 Chatroom
     * @locale
     *
     * @locale en
     * Disconnect.
     *
     * Log out and disconnect the WebSocket persistent connection.
     * After the disconnect is completed, the chatroom instance will not be destroyed, you can log in to the chatroom again via calling “connect(): Promise<void>”.
     * @locale
     */
    disconnect(): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 更新初始化传入的参数，在初始化完成后使用
     *
     * 请注意！传入的参数会在下一次调用connect或重连生效
     *
     * @locale
     *
     * @locale en
     * Update the parameters for initialization and apply them after initialization is complete
     *
     * Note that the parameters will take effect on the next call to connect or reconnect.
     * @locale
     */
    setOptions(options: ChatroomInitializeOptions): void;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 销毁实例
     *
     * - 销毁当前 Chatroom 实例，同时会退出登录状态，并断开websocket连接
     * - 移除所有监听事件，销毁部分内部变量，并且此实例再也无法调用 connect 恢复 Chatroom 连接
     * @locale
     *
     * @locale en
     * Destroy the chatroom instance.
     *
     * Destroy the current chatroom instance, log out at the same time, and disconnect the WebSocket persistent connection
     * Remove all listening events, destroy some internal variables, and the connection can no longer be restored for the instance by calling “connect(): Promise<void>”.
     * @locale
     */
    destroy(): Promise<void>;
}
/**
 * @Multi_Lang_Tag
 * @locale cn
 * 聊天室实例初始化参数
 *
 * eg. new sdk({ ...ChatroomInitializeOptions })
 * @locale
 *
 * @locale en
 * Chatroom instance initialization parameters
 *
 * eg . new sdk ( { ... ChatroomInitializeOptions })
 * @locale
 */
export interface ChatroomInitializeOptions {
    /**
     * 聊天室账号。如果以匿名模式登录，可以不传
     */
    account: string;
    appkey: string;
    /**
     * 聊天室 登录 token。
     *
     * 注意，若 authType = 1，即鉴权方式为动态 token，则需要在重连时，重新设置 token，防止 token 过期导致鉴权失败
     *
     * ```js
     * chatroom.on('willReconnect', () => {
     *   chatroom.setOptions({
     *      token: 'new token'
     *   })
     * })
     * ```
     */
    token: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室 id。聊天室 ID 通过[创建服务器](https://doc.yunxin.163.com/TM5MzM5Njk/docs/jA0MzQxOTI?platform=server)接口返回
     * @locale
     *
     * @locale en
     * chatroom ID
     * @locale
     */
    chatroomId: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室地址列表
     *
     * - 该地址通过[获取聊天室地址](https://doc.yunxin.163.com/messaging/docs/TMxODkzNjE?platform=server)接口获取
     * @locale
     *
     * @locale en
     * Chatroom address list
     * @locale
     */
    chatroomAddresses: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 日志级别，默认为 off，即不输出任何日志
     *
     * - off: 不输出任何日志
     * - debug: 输出所有日志
     * - log: 输出 log、warn、 error 级别的日志
     * - warn: 输出 warn 和 error 级别的日志
     * - error: 输出 error 级别的日志
     * @locale
     *
     * @locale en
     * Log classification
     *
     * optional value, "off" | "error" | "warn" | "log" | "debug"
     * @locale
     */
    debugLevel?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否需要自动重连，默认为 true
     * @locale
     *
     * @locale en
     * Whether automatic reconnection is required, the default value is true.
     * @locale
     */
    needReconnect?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 自动重连尝试次数
     * @locale
     *
     * @locale en
     * Automatic reconnection attempts
     * @locale
     */
    reconnectionAttempts?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否为游客
     *
     * - 若为 false，以固定成员身份登录聊天室
     * - 若为 true，以游客身份匿名登录聊天室。此时必须填写用户昵称（非匿名方式为选填），建议填写头像。第一次使用匿名方式登录聊天室时（新建实例），无需填写account参数，但需要在登录以后从聊天室实例中获取 SDK 生成的该参数
     * @locale
     *
     * @locale en
     * Whether it a tourist
     * @locale
     */
    isAnonymous?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 标签，可设置多个，仅代表本次登录
     * @locale
     *
     * @locale en
     * Chatroom tag. You can set multiple chatroom tags. The tags that you set are only valid for the current login; if you log in again, you need to set the tags again.
     * @locale
     */
    tags?: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 登录登出等通知目标的标签，是一个标签表达式
     *
     * 表示本次登录以及随后的登出操作产生的进出通知应该广播给哪些标签用户，若缺省则服务器会根据tags自动自动生成一个标签表达式，生成的规则是将tags中的所有标签通过and关键词进行组合，表示只有同时设置了所有tags中的标签的用户能收到我的进出通知
     * @locale
     *
     * @locale en
     * The tag of the target members who need to be notified of events such as the Login and Logout events.
     * @locale
     */
    notifyTargetTags?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 登录 IM 的鉴权方式（默认为 0）：
     *
     * - 0：通过传入静态 token 进行鉴权。静态 token 恒定不变，且默认永久有效，除非主动调用[云信服务端 API](https://doc.yunxin.163.com/messaging/docs/DUxNDQ3NjA?platform=server)刷新token
     * - 1：通过传入动态 token 进行鉴权。动态 token 可设置有效期，因此具备时效性。采用该鉴权方式可有效提升token 破解难度，降低密码泄露风险。动态鉴权生成方式请参考: [登录鉴权](https://doc.yunxin.163.com/messaging/docs/zE2NzA3Mjc?platform=server#%E5%8A%A8%E6%80%81token%E9%89%B4%E6%9D%83)
     * - 2：过云信的第三方回调功能进行鉴权。云信服务端不做 IM 登录鉴权，鉴权工作需由指定的第三方服务器（可以是应用服务器）进行
     *
     * 采用动态 token 鉴权时，需要在重连时，重新设置 token，否则重连时会鉴权失败
     * ```js
     * nim.on('willReconnect', () => {
     *  nim.setOptions({
     *    token: 'new token'
     *  })
     * })
     * ```
     * @locale
     *
     * @locale en
     * Authentication method (default: 0)
     * 0 indicates the authentication method based on the initial loginToken.
     * 1 represents the authentication method based on the token calculated using appSecret.
     * 2 indicates the authentication method based on the token got from third-party callback.
     * @locale
     */
    authType?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 登录自定义字段，用于提交给用户的第三方回调服务进行登录检测
     * @locale
     *
     * @locale en
     * Login custom field, used for submitting to the user's third-party callback service for login authentication.
     * @locale
     */
    loginExt?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否 deviceId 需要固定下来。默认 false。
     *
     * true：sdk 随机对设备生成一个设备标识并存入 localstorage 缓存起来，也就是说一个浏览器来说所有 SDK 实例连接都被认为是共同的设备。
     *
     * false：每一个 sdk 实例连接时，使用随机的字符串作为设备标识，相当于每个实例采用的不同的设备连接上来的。
     *
     * #### 影响范围
     * - 这个参数会影响多端互踢的策略。有关于多端互踢策略的配置可以参见服务器文档。
     * - 这个参数影响离线推送(uniapp)
     * @locale
     *
     * @locale en
     * Whether deviceId is fixed, the default value is false.
     *
     * true：SDK randomly generates a device identifier and stores into the LocalStorage cache, all SDK instances are considered to be common devices for a browser.
     *
     * false：When each SDK instance is connected, the random string is used as a device identifier and different devices are used in each instance.
     *
     * Note: This parameter will affect the strategy of multi-device forced logout. For configurations about multi-device forced logout strategies, see the server documentation.
     * @locale
     */
    isFixedDeviceId?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * Abtest 是否开启，默认 true 开启
     *
     * 注: 打开这个开关，在 sdk 内部会试探某些新功能的开启，建议开发者不要轻易设置它。
     * @locale
     *
     * @locale en
     * Whether Abtest is enabled. The default value is true
     *
     * Note: If you turn on the switch, the SDK attempts to enable certain new features. You are recommended not turn on it.
     * @locale
     */
    isAbtestEnable?: boolean;
    /**
     * AB测试服务器下发地址。一般用户无需关注
     */
    abtestUrl?: string;
    /**
     * 建立连接时的 xhr 请求的超时时间。默认为 8000 ms。
     *
     * xhr 请求是 websocket 连接建立前的步骤。它主要用于和服务器协商连接ID
     */
    xhrConnectTimeout?: number;
    /**
     * 建立 socket 长连接的超时时间。默认为 8000 ms
     */
    socketConnectTimeout?: number;
    /**
     * 私有化环境参数。默认为 false，即不需要兼容私有化环境登录参数。
     *
     * 如果私有化环境未升级到最新版本，需要设置该变量为 true，否则会导致登录失败。
     */
    loginSDKTypeParamCompat?: boolean;
}
export interface ChatroomOtherOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * cloud storage 模块配置
     * @locale
     *
     * @locale en
     * cloud storage config
     * @locale
     */
    cloudStorageConfig?: NIMEModuleParamCloudStorageConfig;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * SDK 上报收集数据的配置
     *
     * 注: v1.0.0 开始支持
     * @locale
     *
     * @locale en
     * Data reporting config
     * @locale
     */
    reporterConfig?: NIMOtherOptionsReporterConfig;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * ABtest 配置
     *
     * 注: v1.0.0 开始支持
     * @locale
     *
     * @locale en
     * ABTest configuration
     * @locale
     */
    abtestConfig?: NIMOtherOptionsAbtestConfig;
    /**
     * 日志模块的配置
     */
    loggerConfig?: NIMOtherOptionsLoggerConfig;
}
export declare type NIMOtherOptionsLoggerConfig = {
    /**
     * 日志等级. 默认 off 关闭日志打印.
     *
     * 注: 分别是关闭日志打印 | 打印 error 日志 | 打印 warn 级别及以上 | 打印 log 级别及以上 | 打印 debug 级别及以上
     */
    debugLevel?: 'off' | 'error' | 'warn' | 'log' | 'debug';
    /**
     * 日志代理函数.
     *
     * 注: 拥有四个等级的日志输出方法. 所有日志经过这些方法代理.
     *
     * 注2: 代理函数的入参包含一个或者多个参数, 参数的类型为基础类型
     */
    logFunc?: {
        debug?: (...args: any) => void;
        log?: (...args: any) => void;
        warn?: (...args: any) => void;
        error?: (...args: any) => void;
    };
    /**
     * 持久日志存储是否打开. 默认 true 打开
     *
     * 注: 只会存储 log 级别及以上的日志
     */
    storageEnable?: boolean;
    /**
     * 持久存储日志的数据库名
     */
    storageName?: string;
};
export declare type NIMOtherOptionsAbtestConfig = {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * ABtest 是否开启，默认 true 开启
     *
     * 注: 打开这个开关，在 sdk 内部会试探某些新功能的开启，建议开发者不要轻易设置它。
     * @locale
     *
     * @locale en
     * Whether Abtest is enabled. The default value is true
     *
     * Note: If you turn on the switch, the SDK attempts to enable certain new features. You are recommended not turn on it.
     * @locale
     */
    isAbtestEnable?: boolean;
    /**
     * abTest 服务器下发地址
     */
    abtestUrl?: string;
};
/**
 * 上报配置
 */
export declare type NIMOtherOptionsReporterConfig = {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 指南针是否开启，默认是 true
     * @locale
     *
     * @locale en
     * Whether compass is enabled. The default value is true
     * @locale
     */
    enableCompass?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 指南针数据默认端点
     * @locale
     *
     * @locale en
     * Default endpoint for compass data
     * @locale
     */
    compassDataEndpoint?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * @deprecated
     * 是否开启数据上报，默认是true
     * @locale
     *
     * @locale en
     * @deprecated
     * Whether data reporting is enabled. The default value is true
     * @locale
     */
    isDataReportEnable?: boolean;
};
/**
 * @Multi_Lang_Tag
 * @locale cn
 * 静态方法
 * @locale
 *
 * @locale en
 * Static method
 * @locale
 */
export interface ChatroomInterfaceStatic {
    /**
     * 构造函数
     */
    new (options?: ChatroomInitializeOptions, otherOptions?: ChatroomOtherOptions): ChatroomInterface;
    /**
     * 单例模式获取实例
     */
    getInstance(_options?: ChatroomInitializeOptions, _otherOptions?: ChatroomOtherOptions): ChatroomInterface;
    /**
     * SDK 版本号-数字格式
     */
    sdkVersion: number;
    /**
     * SDK 版本号-字符串格式
     */
    sdkVersionFormat: string;
}
/**
 * @Multi_Lang_Tag
 * @locale cn
 * 实例的状态标识
 *
 * - unconnected: 尚未建立连接(初始化、主动登出、达到最大重连次数)
 * - connecting: 正在建立连接中
 * - connected: 已连接，尚未完成鉴权认证
 * - logined: 已连接, 并且完成了鉴权认证，可以正常开始发送协议
 * - waitReconnect: 等待重连中
 * - destroyed: 实例已经销毁
 * @locale
 *
 * @locale en
 * Status of the instance
 *
 * - unconnected: no connection has been established(Initialization, active logout, and maximum reconnection times)
 * - connecting: Establishing connection
 * - connected: Connected, authentication has not been completed
 * - logined: Connected and completed the authentication
 * - waitReconnect: Waiting for reconnection
 * - destroyed: The instance has been destroyed
 * @locale
 */
export declare type NIMEChatroomInstanceStatus = 'unconnected' | 'connecting' | 'connected' | 'logined' | 'waitReconnect' | 'destroyed';
/**
 * Example：
 *
 * const instance = new SDK()
 *
 * instance.on('logined', loginResult => { console.log(loginResult) }
 */
export interface ChatroomEventInterface {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 初始化成功登陆
     * @locale
     *
     * @locale en
     * Initialization succeeded and login
     * @locale
     */
    logined: [loginResult: TLoginedResult];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 被踢下线
     * @locale
     *
     * @locale en
     * Be forced to go offline
     * @locale
     */
    kicked: [kickedReason: TKickedReason];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 开始自动重连
     * @locale
     *
     * @locale en
     * Start automatic reconnection
     * @locale
     */
    willReconnect: [params: TWillReconnect];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 连接断开.
     *
     * 注: 此事件不包含被踢而断开的情况<br/>
     * v0.14.0 之前触发条件: 1. 手动断开。 2. 在连接断开后, 自动登录超过重试上限次数。<br/>
     * v0.14.0，包含本版本，更新触发条件: 1. 手动断开。2. 在连接断开后，自动登录超过重试上限次数。3. 登陆保持阶段第一次断开时触发
     * @locale
     *
     * @locale en
     * Disconnect
     * @locale
     */
    disconnect: [];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 收到消息
     * @locale
     *
     * @locale en
     * Received messages
     * @locale
     */
    chatroomMsg: [chatroomMsg: ChatroomMessage];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 当前用户标签变更
     * @locale
     *
     * @locale en
     * Tags of members change
     * @locale
     */
    tagsUpdate: [currentTags: string[]];
}
export interface TLoginedResult {
    chatroom: Chatroom;
    member: ChatroomMember;
}
export interface TKickedReason {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 未知 | 聊天室已经被解散 | 被管理员或踢了 | 多端被踢-不允许同一个帐号在多个地方同时登录 | 被悄悄踢掉, 表示这个链接已经废掉了 | 被拉黑了
     * @locale
     *
     * @locale en
     * Unknown | Chatroom has been disbanded | Removed from chatroom by Administrator | Force offline - the same account is not allowed to log in to multiple devices at the same time | Quietly kicked out, indicating that the link has been abolished | Blocked
     * @locale
     */
    reason: 'unknow' | 'chatroomClosed' | 'managerKick' | 'samePlatformKick' | 'silentlyKick' | 'blacked';
    message: string;
}
export interface TWillReconnect {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 重试次数
     * @locale
     *
     * @locale en
     * number of retries
     * @locale
     */
    retryCount: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 重试间隔
     * @locale
     *
     * @locale en
     * retry interval
     * @locale
     */
    duration: number;
}
