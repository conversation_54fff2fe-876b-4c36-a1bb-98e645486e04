<template>
	<custom-header  style="height: 88rpx;" title="实名认证" showBack />
	<view class="container">
		<view class="idcard-success">
			<image class="idcard-img" src="../../../../static/image/user/idcard.png" mode=""></image>
			<text class="idcard-text">已认证</text>
		</view>
		<view class="content">
			<text class="cont-l">姓名</text>
			<text class="cont-r">{{formatName(userInfo.name)}}</text>
		</view>
		<view class="content">
			<text class="cont-l">身份证号</text>
			<text class="cont-r">{{maskIdCard(userInfo.idNumber)}}</text>
		</view>
	</view>
</template>

<script>
	import Quene from '../../../../components/utils/queue';
	import customHeader from '@/components/page/header.vue'
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				userInfo:{
				// name: '周子', // 姓名
				// idcard: '***************' ,// 身份证号
				}
			}
		},
		async onShow() {
			this.userInfo= Quene.getData('userinfo');
			
			console.log(this.userInfo);
		},
		onLoad(options) {

		},
		methods: {
			//格式化名字
			 formatName(name) {
			            if (!name) return '';
			            const len = name.length;
			            if (len === 2) {
			                return '*'+ name.charAt(1);
			            } else if (len === 3) {
			                return '*'.repeat(len-1) + name.charAt(2);
			            } else if (len > 3) {
			                return '*'.repeat(len-1) + name.charAt(len-1);
			            }
			            return name;
			        },
			// 对身份证号进行掩码处理
			maskIdCard(idCard) {
				if (!idCard) return ''
				if (idCard.length <= 5) return idCard
				return idCard.substring(0, 1) + '*'.repeat(idCard.length - 2) + idCard.substring(idCard.length - 1)
			}

		}
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style lang="scss" scoped>
	.container {
		padding: 0 72rpx;
		padding-top: 156rpx;
	}
.idcard-success{
	display: flex;
	flex-direction: column;
	align-items: center;
	text-align: center;
	/* margin-top: 92rpx; */
	margin-bottom: 72rpx;
}
.idcard-img{
	width: 230rpx;
	height: 174rpx;
	margin: 92rpx auto;
}
.idcard-text{
	font-size: 34rpx;
	font-weight: 500;
	line-height: 56rpx;
	text-align: center;
	letter-spacing: 0.4rpx;
	/* White/85% */
	color: rgba(255, 255, 255, 0.85);
}
.content{
	display: flex;
	flex-direction: row;
	justify-content: space-between;
	margin-bottom: 32rpx;
}
.cont-l{
	font-family: PingFang SC;
	font-size: 32rpx;
	font-weight: normal;
	line-height: 56rpx;
	letter-spacing: 0.4rpx;
	font-variation-settings: "opsz" auto;
	/* White/85% */
	color: rgba(255, 255, 255, 0.85);
}
.cont-r{
	font-family: PingFang SC;
	font-size: 30rpx;
	font-weight: normal;
	line-height: 56rpx;
	text-align: right;
	letter-spacing: 0.4rpx;
	font-variation-settings: "opsz" auto;
	/* White/65% */
	color: rgba(255, 255, 255, 0.65);
}
</style>