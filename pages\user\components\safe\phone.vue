<template>
	<custom-header  style="height: 88rpx;" title="手机号" showBack />
	<view class="content">
		<image class="phone" src="../../../../static/image/user/phone.png" mode=""></image>
		<text class="phone-text">你的手机号：{{formatPhone(phone)}}</text>
		<button class="phone-button" @click="handleSelectClick">更换手机号</button>
		<text class="phone-title">更换手机号码后，登录手机号码和企业通讯录号码均改变。</text>
	</view>
</template>

<script>
	import customHeader  from '@/components/page/header.vue';
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				phone: ''
			}
		},
		 onLoad(options) {
		        // 获取传递过来的手机号
		        if (options.phone) {
		            this.phone = options.phone;
		        }
		    },
		methods: {
			// 格式化手机号
			formatPhone(phone) {
			  if (!phone) return ''
			  return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
			},
			handleSelectClick() {
			  uni.navigateTo({
			    url: `/pages/user/components/safe/updatephone`
			  });
			},
		}
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style lang="scss" scoped>
.content{
	padding: 0 40rpx;
	padding-top: 156rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	margin: auto;
}
.phone{
	height: 280rpx;
	width: 280rpx;
	margin: auto;
	margin-top: 72rpx;
	margin-bottom: 32rpx;
}
.phone-text{
	font-weight: 500;
	font-size: 34rpx;
	font-family: Inter;
	text-align: center;
	color: rgba(255, 255, 255, 0.85);
	margin-bottom: 72rpx;
}
.phone-button{
	width: 100%;
	height: 96rpx;
	font-family: Inter;
	line-height: 96rpx;
	border-radius: 16rpx;
	font-size: 40rpx;
	letter-spacing: 0.4rpx;
	color: #FFFFFF;
	background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	margin-bottom: 32rpx;
}
.phone-title{
	text-align: center;
	font-size: 20rpx;
	letter-spacing: 0.4rpx;
	color: rgba(255, 255, 255, 0.65);
}
</style>
