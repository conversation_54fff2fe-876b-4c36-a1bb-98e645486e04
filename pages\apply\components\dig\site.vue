<template>

	<view class="con_content">
		<view class="search-bar">
			<view class="bar">

				<!-- <input type="text" class="sear_inp" placeholder="请输入巷道名称" @input="clearInput" v-model="searchText" />
				<icon size="16" type="clear" v-if="showClearIcon" @click="clearIcon"></icon> -->
				<zxz-uni-data-select 
				v-model="value" 
				placeholder="请选择巷道"
				:clear="false"
				:localdata="sitelist"
				@change="handleSelectChange"
				></zxz-uni-data-select>
			</view>
			<!-- <up-input class="sear_inp" placeholder="钻具名称/钻具ID" prefixIcon="search" v-model="searchText"
				color="rgba(255,255,255,0.8)" prefixIconStyle="font-size: 22px;color: #909399"></up-input> -->
			<button class="con_btn" @click="search">查询</button>
		</view>


		<view style="height:calc(100% - 150rpx);magin:10rpx;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
			<!-- <view> -->
			<zb-table @sort-change="sortChange" :pullUpLoading="pullUpLoading" :isShowLoadMore="true" :highlight="true"
				:show-header="true" :columns="column" :fit="false" :permissionBtn="permissionBtn" :stripe="true"
				row-key="id" @rowClick="rowClick" :border="false" :data="data"></zb-table>

		</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request.js'
	let that = null
	export default {
		data() {
			const baseData = {
				drillsiteId: "ZHUANCHANG001",
				name: "1号瓦斯抽采钻场",
				location: "西北翼采区",
				type: "瓦斯抽采钻场",
				startTime: "2023-04-01",
				endTime: "2023-05-11",
				number: "50个",
				length: '2000米',
				designDepth: '300米',
				actualDepth: '250米',
				diameter: '200mm',
				responsiblePerson: "钻场负责人王五",
				status: 1,
				remark: '备注',
				lanewayId: 'CAIMIAN001',
				lanewayName: "1号采面"
			};
			const generateData = () => {
				const repeatedData = [];
				for (let i = 0; i < 10; i++) {
					repeatedData.push({
						id: i,
						...baseData
					})
				}
				return repeatedData;
			}
			return {
				dateStart: '',
				dateEnd: '',
				searchText: '',
				id: null,
				value: null,
				isFromRoadway: false,
				sitelist: [],
				column: [
					// { type:'selection', fixed:true,width:60 },
					{
						name: 'drillsiteId',
						label: '钻场编号',
						fixed: true,
						align: 'center',
						emptyString: '--'
					},
					{
						name: 'name',
						label: '钻场名称',
						// sorter: 'custom',
					},
					{
						name: 'location',
						label: '钻场位置',
						fixed: true
					},
					{
						name: 'type',
						label: '钻场类型',
					},
					{
						name: 'startTime',
						label: '施工日期',
						// sorter: true
					},
					{
						name: 'endTime',
						label: '竣工日期'
					},
					{
						name: 'number',
						label: '钻孔数量',
						// sorter: true
					},
					{
						name: 'length',
						label: '钻孔总长度',
					},
					{
						name: 'designDepth',
						label: '设计深度',
					},
					{
						name: 'actualDepth',
						label: '实际深度',
					},
					{
						name: 'diameter',
						label: '钻孔直径',
					},
					{
						name: 'responsiblePerson',
						label: '责任人员',
					},
					{
						name: 'status',
						label: '使用状态',
						filters: {
							0: '未使用',
							1: '使用中'
						},
					},
					{
						name: 'remark',
						label: '备注',
					},	
				],
				// data: generateData(),
				data: [],//表格数据
				data1: [],
				flag1: true,
				flag2: true,
				showClearIcon:false,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				isShowLoadMore: true, // 是否显示"加载更多"  
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
			}
		},
		onLoad() {
			// this.handelDrill()
		},
		created() {
			that = this
		},
		mounted() {
			// 获取巷道列表数据
			this.getLanewayList();
		},
		onLoad(options) {
			console.log('site onLoad options:', options);
			this.dateStart = options.dateStart;
			this.dateEnd = options.dateEnd;
		},
		methods: {
			clearInput(event) {
				this.searchText = event.detail.value;
				if (event.detail.value.length > 0) {
					this.showClearIcon = true;
					this.currentPage = 1; // 重置页码
					this.data = []; // 清空数据
					
					this.isShowLoadMore = true; // 重置加载更多
					this.handelDrill(); // 重新加载数据
				} else {
					this.showClearIcon = false;
				}
			},
			clearIcon() {
				this.searchText = '';
				this.showClearIcon = false;
			},
			search() {
				console.log('handleSearchInput', this.value);
				this.currentPage = 1;
				this.data = [];
				this.isShowLoadMore = true;
				this.handelDrill(this.value);
				this.searchText = '';
			},
			//初始加载调取接口获取数据
			async handelDrill(value) {
				
				try {
					let main_data = {
						page: this.currentPage,
						perPage: this.perPage,
					};
					
					// 如果value不为null且不为空，添加parentId
					if (value !== null && value !== '') {
						main_data.parentId = value;
					}
					
					// 如果是从巷道跳转且有ID，使用跳转时的ID
					if (this.isFromRoadway && this.id) {
						main_data.parentId = this.id;
					}
					
					console.log('请求参数:', main_data);
					const res = await Request.post('/drillsite/get_ls', main_data)
					console.log('钻场数据响应:', res.data);

					if (res.status == 0) {
						if (res.data && Array.isArray(res.data)) {
							// 直接是数组的情况
							this.data = res.data;
							this.isShowLoadMore = false;
						} else if (res.data && res.data.items && Array.isArray(res.data.items)) {
							// 包含items的情况
							this.data = res.data.items;
							this.isShowLoadMore = res.data.items.length < this.perPage;
						} else {
							this.data = [];
							this.isShowLoadMore = false;
						}
					} else {
						this.data = [];
						this.isShowLoadMore = false;
						uni.showToast({
							title: res.msg || '获取数据失败',
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('Error loading drill sites:', error);
					this.data = [];
					this.isShowLoadMore = false;
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},

			change(e) {
				console.log("e:", e);
			},
			sortChange(item, sorterMode, index) {

				console.log('触发排序', item, sorterMode, index)
			},
			async pullUpLoading(done) {
				if (!this.flag1) {
					return
				}
				this.currentPage++; // 页码加1
				try {
					let main_data = {
						page: this.currentPage,
						perPage: this.perPage,
					};
					
					// 如果当前有选中值且不是从巷道跳转，使用选中值
					if (this.value !== null && !this.isFromRoadway) {
						main_data.parentId = this.value;
					}
					
					// 如果是从巷道跳转且有ID，使用跳转时的ID
					if (this.isFromRoadway && this.id) {
						main_data.parentId = this.id;
					}
					
					const res = await Request.post('/drillsite/get_ls', main_data);

					if (res.status == 0) {
						if (res.data.items && res.data.items.length > 0) {
							this.data = this.data.concat(res.data.items);
							done(); // 通知 zb-table 加载完成
						} else {
							this.isShowLoadMore = false; // 没有更多数据，不再显示加载更多
							done('ok'); // 通知zb-table 没有更多数据
							this.flag1 = false
							uni.showToast({
								title: '暂无更多数据',
								icon: 'none',
								duration: 1000
							})
						}
					} else {
						done(); // 结束加载
					}
				} catch (error) {
					console.error("加载更多数据失败:", error);
					done(); //  结束加载
				}
			},
			permissionBtn(row, renders, rowIndex) {
				if (row.id == 2) {
					let arr = renders.filter(item => item.func === 'edit')
					return arr
				}
				return renders
			},

			buttonEdit(ite, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '点击编辑'
				// })

				console.log(ite, index)
			},

			rowClick(row, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '单击某行'
				// })
				console.log('单击某行', row, index)
			},
			// 获取巷道列表
			async getLanewayList() {
				try {
					const res = await Request.get('/laneway/get_all');
					if (res.status === 0 && res.data) {
						// 转换数据格式
						const allOption = {
							text: '全部',
							value: 0
						};
						// 转换数据格式并在开头添加"全部"选项
						this.sitelist = [
							allOption,
							...res.data.map(item => ({
								text: item.name,
								value: item.id
							}))
						];
					} else {
						uni.showToast({
							title: res.msg || '获取巷道列表失败',
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('Error loading laneway list:', error);
					uni.showToast({
						title: '获取巷道列表失败',
						icon: 'none',
						duration: 2000
					});
				}
			},
			// 初始化带 id 的数据
			async initWithId(id) {
				if (!id) {
					console.error('initWithId: id is required');
					return;
				}
				this.isFromRoadway = true;
				this.id = id;
				this.value = id;
				this.data = [];
				this.currentPage = 1;
				this.isShowLoadMore = true;

				// 确保巷道列表数据已加载
				await this.getLanewayList();
				
				this.$nextTick(() => {
					this.handelDrill();
				});
			},
			// 添加下拉框change事件处理
			handleSelectChange(value) {
				console.log('选择的值:', value);
				// 更新选中值
				this.value = value.value;
				// 重置从巷道跳转的标志
				this.isFromRoadway = false;
				// 重置ID
				this.id = value;
				// 立即触发搜索
				// this.search();
			},
		},

	}
</script>

<style scoped lang="scss">
	page {
		background: #16171b;
	}

	::v-deep {
		.uni-card {
			margin: 8px !important;
			padding: 0 !important;

			.uni-card__content {
				padding: 0 !important;
			}
		}
	}

	.filter {
		// margin: 28rpx 0;
		height: 70rpx;
		display: flex;
		// background: #fff;
	}

	.sear_inp {
		height: 70rpx;
		// padding: 10rpx 28rpx;
		padding-left: 28rpx;
		font-size: 25rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.select {
		flex: 1;
		border: none;
		// border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		// background: rgba(255, 255, 255, 0.08);
	}

	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
	}

	.bar {
		flex: 1;
		padding-right: 10rpx;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
		display: flex;
		align-items: center;
		
		:deep(.uni-data-select) {
			flex: 1;
			
			.uni-select {
				border: none !important;
				background: transparent !important;
				
				.uni-select__input-box {
					height: 70rpx;
					border: none !important;
					padding: 0 32rpx;
					background: transparent !important;
					
					.uni-select__input-text {
						color: #FFFFFF !important;
						font-size: 28rpx;
					}
					
					.uni-select__input-placeholder {
						color: rgba(255, 255, 255, 0.45) !important;
						font-size: 28rpx;
					}
				}
			}
			
			.uni-select__selector {
				background-color: #000000 !important;
				border: none !important;
				padding: 0;
				margin-top: 2px;
				border-radius: 0 !important;
				
				&::before {
					display: none !important;
				}
				
				.uni-select__selector-item {
					padding: 20rpx 32rpx;
					color: #FFFFFF !important;
					font-size: 28rpx;
					border: none !important;
					background: transparent !important;
					
					&.active {
						color: #177DDC !important;
					}
					
					&:hover {
						background-color: rgba(255, 255, 255, 0.08) !important;
					}
				}
			}
		}
	}

	.con_btn {
		height: 65rpx;
		line-height: 65rpx;
		background: #007BFF;
		letter-spacing: 0px;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.sear_inp {
		flex: 1;
		margin-right: 16rpx;
		// background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}


	.con_content {
		border-radius: 0 0 12rpx 12rpx;
		padding: 0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		height: calc(100vh - 700rpx);
		overflow: hidden;
		// background: #fff;
	}
</style>