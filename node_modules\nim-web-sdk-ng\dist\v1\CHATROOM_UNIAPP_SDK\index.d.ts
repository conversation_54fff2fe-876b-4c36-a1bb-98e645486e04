import { ChatroomEventInterface, ChatroomInitializeOptions, ChatroomInterface, ChatroomOtherOptions, NIMEChatroomInstanceStatus } from './ChatroomInterface';
import { ChatroomServiceInterface } from './ChatroomServiceInterface';
import { ChatroomMsgServiceInterface } from './ChatroomMsgServiceInterface';
import { ChatroomMemberServiceInterface } from './ChatroomMemberServiceInterface';
import { CloudStorageServiceInterface } from './CloudStorageServiceInterface';
import { EventEmitter } from 'eventemitter3';
export default class Chatroom extends EventEmitter<ChatroomEventInterface> implements ChatroomInterface {
    static instance: Chatroom | null;
    /**
     * SDK 版本号-数字格式
     */
    static sdkVersion: number;
    /**
     * SDK 版本号-字符串格式
     */
    static sdkVersionFormat: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 实例的状态
     * @locale
     *
     * @locale en
     * The state of an instance
     * @locale
     */
    status: NIMEChatroomInstanceStatus;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 账号
     * @locale
     *
     * @locale en
     * Account
     * @locale
     */
    account: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 连接参数
     * @locale
     *
     * @locale en
     * Connection parameters
     * @locale
     */
    options: ChatroomInitializeOptions;
    chatroom: ChatroomServiceInterface;
    chatroomMsg: ChatroomMsgServiceInterface;
    chatroomMember: ChatroomMemberServiceInterface;
    cloudStorage: CloudStorageServiceInterface;
    constructor(options: ChatroomInitializeOptions, _otherOptions?: ChatroomOtherOptions);
    connect(): Promise<void>;
    setOptions(): Promise<void>;
    disconnect(): Promise<void>;
    destroy(): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 单例模式获取实例
     * @locale
     *
     * @locale en
     * Get an instance in singleton mode
     * @locale
     */
    static getInstance(_options?: ChatroomInitializeOptions, _otherOptions?: ChatroomOtherOptions): Chatroom;
}
