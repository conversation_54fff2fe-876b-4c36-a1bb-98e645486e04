<template>
	<custom-header style="height: 88rpx;" title="应急处理详情" showBack />
	<view class="content">
		<view class="detail-card">
			<view class="card-header">
				<text class="header-title">基本信息</text>
			</view>
			<view class="card-content">
				<view class="info-item">
					<text class="label">应急事件编号</text>
					<text class="value">{{detail.emergencyCode}}</text>
				</view>
				<view class="info-item">
					<text class="label">应急事件名称</text>
					<text class="value">{{detail.emergencyName}}</text>
				</view>
				<view class="info-item">
					<text class="label">应急事件类型</text>
					<text class="value">{{getTypeText(detail.emergencyType)}}</text>
				</view>
				<view class="info-item">
					<text class="label">应急响应级别</text>
					<text class="value">{{getLevelText(detail.emergencyLevel)}}</text>
				</view>
				<view class="info-item">
					<text class="label">应急处理步骤</text>
					<text class="value">{{detail.emergencyUrl}}</text>
				</view>
				<view class="info-item">
					<text class="label">报警时间</text>
					<text class="value">{{detail.alarmTime}}</text>
				</view>
			</view>
		</view>
		
		
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request.js'
	
	export default {
		components: {
			customHeader
		},
		props: {
			id: {
				type: [String, Number],
				default: null
			}
		},
		data() {
			return {
				detail: {
					emergencyCode: "EMERGENCY001",
					emergencyName: "瓦斯泄露",
					emergencyType: 0,
					emergencyLevel: 2,
					emergencyUrl: "详见附件",
					alarmTime: "2024-11-11",
				}
			}
		},
		onLoad(options) {
			if (options.id) {
				this.getData(options.id);
			}
		},
		methods: {
			async getData(id) {
				try {
					const res = await Request.get('/emergency/get_info', { id });
					if (res.status === 0) {
						this.detail = res.data;
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('Error fetching detail:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
			getTypeText(sign) {
				if (sign === 0) {
					return '技术事故';
				} else if (sign === 1) {
					return '设备老旧';
				} 
				return '--'; // 默认值
			},
			getLevelText(sign) {
				if (sign === 1) {
					return '1级响应';
				} else if (sign === 2) {
					return '2级响应';
				} else if (sign === 3) {
					return '3级响应';
				} 
				return '--'; // 默认值
			},
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	
	.content {
		padding: 32rpx;
		padding-top: 188rpx;
	}
	
	.detail-card {
		background: rgba(255, 255, 255, 0.04);
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 12rpx;
		margin-bottom: 32rpx;
		
		.card-header {
			padding: 24rpx 32rpx;
			border-bottom: 1rpx solid rgba(255, 255, 255, 0.0972);
			
			.header-title {
				font-size: 32rpx;
				font-weight: 500;
				color: rgba(255, 255, 255, 0.85);
			}
		}
		
		.card-content {
			padding: 32rpx;
		}
	}
	
	.info-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 24rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.label {
			// width: 160rpx;
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.45);
		}
		
		.value {
			// flex: 1;
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.85);
		}
	}
	
	.content-section {
		margin-bottom: 32rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.section-title {
			display: block;
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.45);
			margin-bottom: 16rpx;
		}
		
		.section-text {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.85);
			line-height: 1.6;
			white-space: pre-wrap;
		}
	}
</style>