<template>
	<custom-header style="" title="云平台" />
	<view class="content">
		<!-- 用户信息区域 -->
		<view class="user-info-card" @click="navigateToUserDetail">
			<view class="user-info">
				<view class="avatar-content">
					<image class="avatar" :src="userInfo.profilePic" mode="aspectFill" />
				</view>
				<view class="info-content">
					<view class="content-name">
						<text class="username">{{userInfo.name}}</text>
						<text v-if="userInfo.authentication==1" class="surealname">已实名</text>
					<text v-else class="realname">未实名</text>
					</view>
					<text class="phone">{{formatPhone(userInfo.phoneNumber)}}</text>
				</view>
			</view>
			<image class="arrow-right" src="/static/image/user/right.png" />
		</view>

		<!-- 操作菜单区域 -->
		<view class="menu-list">
			<view v-for="(item, index) in menuItems" :key="index" class="menu-item" @click="handleMenuClick(item)">
				<view class="menu-item-left">
					<image class="menu-icon" :src="item.icon" />
				</view>
				<view class="menu-item-right">
					<text class="menu-text">{{item.text}}</text>
					<image class="arrow-right" src="/static/image/user/right.png" />
				</view>
			</view>
		</view>
		<!-- 设置 -->
		<view class="menu-list">
			<view class="menu-item" @click="setting">
				<view class="menu-item-left">
					<image class="menu-icon" src="/static/image/user/setting.png" />
				</view>
				<view class="menu-item-right">
					<text class="menu-text">设置</text>
					<image class="arrow-right" src="/static/image/user/right.png" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Quene from '../../components/utils/queue'
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue';
	import {
		route
	} from '@/uni_modules/uview-plus';
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				userInfo: {
					// profilePic: '', // 初始化为空字符串，避免初始渲染的问题
					// name: '',
					// account: ''
				},

				menuItems: [{
						icon: '/static/image/user/notification.png',
						text: '通知',
						path: '/pages/user/components/notification'
					},
					{
						icon: '/static/image/user/security.png',
						text: '安全中心',
						path: '/pages/user/components/security'
					},
					{
						icon: '/static/image/user/product.png',
						text: '产品咨询',
						path: '/pages/user/components/product'
					},
					{
						icon: '/static/image/user/feedback.png',
						text: '意见反馈',
						path: '/pages/user/components/feedback'
					}
				]
			}
		},
		async onLoad() {
			await this.loadUserData();
			console.log(this.userInfo);
			
		},
		onShow(){
			this.userInfo = Quene.getData('userinfo');
		},
		methods: {
			//获取用户信息
			async loadUserData() {
				try {
					const res = await Request.get('/personal/get_info');
					if (res.status === 0) {
						//存储用户信息
						Quene.setData('userinfo', res.data);
						this.userInfo = Quene.getData('userinfo');
						// console.log(this.userinfo);
					} else {
						uni.showToast({
							title: '用户信息获取失败',
							icon: 'none',
							duration: 2000
						})
					}
				} catch (error) {
					console.error('Error fetching user info:', error);
					uni.showToast({
						title: '网络不稳定，请稍后再试',
						icon: 'none',
						duration: 2000
					})
				}
			},
			// 格式化手机号
			formatPhone(phone) {
				if (!phone) return ''
				return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
			},
			// 跳转到用户详情页
			navigateToUserDetail() {
				route('/pages/user/components/detail')
			},

			// 处理菜单点击
			handleMenuClick: function(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			setting: function() {
				uni.navigateTo({
					url: '/pages/user/components/setting'
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		height: 100%;
		// -webkit-overflow-scrolling: touch;
		// overscroll-behavior: contain; 
		// overflow: hidden;
		padding: 0 26rpx;
		padding-top: 62rpx;
	}

	.user-info-card {
		padding: 32rpx;
		background: rgba(255, 255, 255, 0.0362);
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		height: 176rpx;
		border-radius: 12rpx;
		flex-direction: row;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.user-info {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.avatar-content {
		width: 104rpx;
		height: 104rpx;
		padding: 4rpx;
		background: rgba(84, 146, 247, 0.6);
		border-radius: 16rpx;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-content: flex-start;
	}

	.avatar {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	.info-content {
		margin-left: 24rpx;
		display: flex;
		flex-direction: column;
	}

	.content-name {
		display: flex;
		flex-direction: row;
		align-items: center;
		text-align: center;
		margin-bottom: 24rpx;
	}

	.username {
		font-size: 40rpx;
		margin-right: 28rpx;
		color: #FFFFFF;
		font-weight: 500;
	}
.surealname{
	padding: 8rpx;
	font-size: 24rpx;
	border-radius: 12rpx;
	background: rgba(22, 119, 255, 0.12);
	color: #1677FF;
	font-weight: normal;
}
	.realname {
		padding: 8rpx;
		font-size: 24rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.12);
		color: rgba(255, 255, 255, 0.85);
		font-weight: normal;
	}

	.phone {
		font-size: 28rpx;
		font-family: PingFang SC;
		color: rgba(255, 255, 255, 0.65);
	}

	.menu-list {
		margin-top: 44rpx;
		background: rgba(255, 255, 255, 0.0362);
		box-sizing: border-box;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 12rpx;
	}

	.menu-item {
		display: flex;
		height: 116rpx;
		flex-direction: row;
		align-items: center;
		padding-left: 32rpx;
	}

	.menu-item-left {
		display: flex;
		align-items: center;
	}

	.menu-item-right {
		flex: 1;
		height: 100%;
		padding-right: 32rpx;
		flex-direction: row;
		align-items: center;
		text-align: center;
		// padding-top: 32rpx;
		display: flex;
		border-bottom: 1rpx solid rgba(167, 169, 172, 0.15);
		justify-content: space-between;
	}

	.menu-item:last-child .menu-item-right {
		border-bottom: none;
	}

	.menu-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 32rpx;
	}

	.menu-text {
		font-family: PingFang SC;
		font-size: 34rpx;
		letter-spacing: 0px;
		font-weight: normal;
		color: #FFFFFF;
	}

	.arrow-right {
		width: 60rpx;
		height: 60rpx;
	}
</style>