<template>
	<custom-header style="height: 88rpx;" title="打孔详情" showBack />
	<view class="equip_content">
		<view class="equip_header">
			<text class="header_text">{{prolist.submitUser}}</text>
        <view class="headerfl">
			<view class="header_flex">
				<view>
					<view class="flex_label">提交人员账号</view>
					<view class="flex_value">{{prolist.submitAccount}}</view>
				</view>
				<view>
					<view class="flex_label">钻机编号</view>
					<view class="flex_value">{{prolist.drillNumber}}</view>
				</view>
				<view>
					<view class="flex_label">班次</view>
					<view class="flex_value">{{prolist.shift}}</view>
				</view>
			</view>
			<view class="header_flex" style="margin-left:120rpx ;">
				<view>
					<view class="flex_label">施工地点</view>
					<view class="flex_value">{{prolist.position}}</view>
				</view>
				<view>
					<view class="flex_label">钻孔类型</view>
					<view class="flex_value">{{prolist.drillingType}}</view>
				</view>
				<view>
					<view class="flex_label">日期</view>
					<view class="flex_value">{{prolist.date}}</view>
				</view>
			</view>
			
			</view>
		</view>
		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        :list="list4"
        lineWidth="0"
		
        lineColor="#177DDC"
        :activeStyle="{
            color: 'rgba(255,255,255,0.85)',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:10rpx 15px; padding-right: 15px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		
		<template v-if="currentTab == 0">
			<view class="content">
			 <view class="cont_flex">
			 	<view class="label">开孔人员名称</view>
				<view class="value">{{prolist.personnel}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">孔号</view>
			 	<view class="value">{{prolist.holeNumber}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">孔深</view>
			 	<view class="value">{{prolist.holeDepth}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">孔径</view>
			 	<view class="value">{{prolist.holeDiameter}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">开孔角度</view>
			 	<view class="value">{{prolist.holeAngle}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">方位</view>
			 	<view class="value">{{prolist.direction}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">开孔高度</view>
			 	<view class="value">{{prolist.holeHeight}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">见煤距离</view>
			 	<view class="value">{{prolist.coalDistance}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">见岩距离</view>
			 	<view class="value">{{prolist.rockDistance}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">扩孔起始距离</view>
			 	<view class="value">{{prolist.reamingStartDistance}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">打钻起始距离</view>
			 	<view class="value">{{prolist.drillingStartDistance}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">预计出煤量</view>
			 	<view class="value">{{prolist.estimatedCoalOutput}}</view>
			 </view>
			 </view>
		</template>
	<template v-if="currentTab == 1">
		<view class="content">
		 <view class="cont_flex">
		 	<view class="label">开孔角度误差</view>
			<view class="value">{{prolist.holeAngleError}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">开孔方位误差</view>
		 	<view class="value">{{prolist.holeDirectionError}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">开孔高度误差</view>
		 	<view class="value">{{prolist.holeHeightError}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">孔深误差</view>
		 	<view class="value">{{prolist.holeDepthError}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">调整后-开孔倾角</view>
		 	<view class="value">{{prolist.startAngle}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">调整后-开孔高度</view>
		 	<view class="value">{{prolist.startHight}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">调整后-方位角</view>
		 	<view class="value">{{prolist.startAzimuth}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">钻后-开孔倾角</view>
		 	<view class="value">{{prolist.finalInclination}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">钻后-开孔高度</view>
		 	<view class="value">{{prolist.finalHoleHeight}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">钻后-方位角</view>
		 	<view class="value">{{prolist.finalAzimuth}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">钻后-孔深</view>
		 	<view class="value">{{prolist.finalHoleDepth}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">钻杆根数</view>
		 	<view class="value">{{prolist.drillPipesNum}}</view>
		 </view>
		 </view>
	</template>
	<template v-if="currentTab == 2">
		<view class="content">
		 <view class="cont_flex">
		 	<view class="label">是否需要轨迹</view>
			<view class="value">{{getTrajectoryRequired(prolist.ifTrajectoryRequired)}}</view>
		 </view>
		 <view class="cont_flex" v-if="prolist.ifTrajectoryRequired === 1">
		 	<view class="label">轨迹数据</view>
		 	<view class="value">{{prolist.trajectory}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">开孔高度误差</view>
		 	<view class="value">{{prolist.holeHeightError}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">申请状态</view>
		 	<view class="value">{{getApproveStatus(prolist.approveStatus)}}</view>
		 </view>
		 <view class="cont_flex" v-if="prolist.approveStatus === 2">
		 	<view class="label">拒绝申请原因</view>
		 	<view class="value">{{prolist.approveReason}}</view>
		 </view>
		 <template v-if="prolist.approveStatus !== 0">
			 <view class="cont_flex">
			 	<view class="label">开孔审批人员名称</view>
			 	<view class="value">{{prolist.approveUser}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">开孔审批人员账号</view>
			 	<view class="value">{{prolist.approveAccount}}</view>
			 </view>
		 </template>
		 <view class="cont_flex">
		 	<view class="label">检查状态</view>
		 	<view class="value">{{getCheckStatus(prolist.checkStatus)}}</view>
		 </view>
		 <view class="cont_flex" v-if="prolist.checkStatus === 2">
		 	<view class="label">不合格原因</view>
		 	<view class="value">{{prolist.checkReason}}</view>
		 </view>
		 <template v-if="prolist.checkStatus !== 0">
			 <view class="cont_flex">
			 	<view class="label">验孔审批人员名称</view>
			 	<view class="value">{{prolist.checkUser}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">验孔审批人员账号</view>
			 	<view class="value">{{prolist.checkAccount}}</view>
			 </view>
		 </template>
		 <view class="cont_flex">
		 	<view class="label">完工申请</view>
		 	<view class="value">{{getStatus(prolist.status)}}</view>
		 </view>
		 <view class="cont_flex" v-if="prolist.status === 2">
		 	<view class="label">未完工原因</view>
		 	<view class="value">{{prolist.reason}}</view>
		 </view>
		 <template v-if="prolist.status !== 0">
			 <view class="cont_flex">
			 	<view class="label">完工审批人员名称</view>
			 	<view class="value">{{prolist.userName}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">完工审批人员账号</view>
			 	<view class="value">{{prolist.userAccount}}</view>
			 </view>
		 </template>
		 <view class="cont_flex">
		 	<view class="label">创建时间</view>
		 	<view class="value">{{prolist.createdAt}}</view>
		 </view>
		 <view class="cont_flex">
		 	<view class="label">修改时间</view>
		 	<view class="value">{{prolist.updatedAt}}</view>
		 </view>
		 </view>
	</template>
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			
		},
		data(){
			return{
				currentTab:0,
				id: null,
			list4:[  
			    { name: '开孔信息' },  
			    { name: '实际信息' },  
			    { name: '申请状态信息' },  
			    // { name: '保养计划记录' },  
			    // { name: '设备寿命跟踪' },  
			    // { name: '设备转移记录' }
			],
			prolist:{
				submitUser: "RART001",
				submitAccount: "齿轮箱",
				date: "DCM-1000",
				position: "设备A的左后方",
				drillNumber: "10000小时",
				drillingType: "8500小时",
				shift: "0.5",
				personnel: 0,
				holeNumber:'每周使用3次',
				holeDepth:'500小时',
				holeDiameter:'诊断报告链接',
				holeAngle:'维护日志链接',
				direction:"5个",
				holeHeight: "2025-01-01 10:12:00",
				coalDistance: "2025-01-04 10:12:00",
				rockDistance: "111",
				reamingStartDistance: "111",
				drillingStartDistance: "111",
				estimatedCoalOutput: "111",
				holeAngleError: "111",
				holeDirectionError: "111",
				holeHeightError: "111",
				holeDepthError: "111",
				ifTrajectoryRequired: "111",
				startAngle: "222",
				startHight: "333",
				startAzimuth:"444",
				finalInclination:"555",
				finalHoleHeight:"666",
				finalAzimuth:"555",
				finalHoleDepth:"11",
				drillPipesNum:"2",
				trajectory:"3",
				approveStatus:0,
				approveReason:"000",
				approveUser:"111",
				approveAccount:"222",
				checkStatus:2,
				checkReason:"333",
				checkUser:"33",
				checkAccount:"55",
				status:0,
				reason:"00",
				userName:"000",
				userAccount:"000",
				createdAt:"000",
				updatedAt:"0000000"
			}
			}
		},
		onLoad(options) {
			this.id = options.id
			this.handelDetail()
		},
		onShow(){
			if (this.id) {
				// this.handelDetail()
			}
		},
		methods:{
			handleTabChange(index) {
				this.currentTab = index.index;
			},
			// 是否需要轨迹显示
			getTrajectoryRequired(status) {
				const statusMap = {
					0: '不需要',
					1: '需要'
				}
				return statusMap[status] || '未知'
			},
			// 申请状态显示
			getApproveStatus(status) {
				const statusMap = {
					0: '待申请',
					1: '同意',
					2: '不同意'
				}
				return statusMap[status] || '未知'
			},
			// 检查状态显示
			getCheckStatus(status) {
				const statusMap = {
					0: '待检查',
					1: '合格',
					2: '不合格'
				}
				return statusMap[status] || '未知'
			},
			// 完工状态显示
			getStatus(status) {
				const statusMap = {
					0: '待申请',
					1: '确认',
					2: '拒绝'
				}
				return statusMap[status] || '未知'
			},
			async handelDetail() {
			
				try {
			
					const res = await Request.get('/drill/get_info', {
						id: this.id
					})
			
					if (res.status == 0) {
						console.log('打孔详情返回数据', res);
						this.prolist = res.data;
			
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });
			
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
			
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			
			},
					
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	.equip_content{
		padding:0 32rpx ;
		padding-top: 1rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.tab_cont{
		border-radius: 12rpx;
		// padding: 0 32rpx;
		margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx 12rpx 0 0;
		padding:0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		height: 840rpx;
		overflow-y: auto;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 0  0 12rpx 12rpx;
	}
	.content{
		// border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		padding-bottom: 10rpx;
		border-radius: 0  0 12rpx 12rpx;
		// height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>