<template>
	<custom-header  style="height: 88rpx;" title="版本" showBack />
	<view class="content">
		<text class="center">{{version}}</text>
	</view>
</template>

<script>
	import customHeader  from '@/components/page/header.vue';
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				version:'云平台0.0.1'
			}
		},
		methods: {
			
		}
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style scoped>
	.content{
		text-align: center;
		padding-top: 426rpx;
	}
.center{
	width: 100%;
	margin: auto;
	font-family: Lato;
	font-size: 32rpx;
	font-weight: normal;
	line-height: 44.8rpx;
	text-align: center;
	letter-spacing: 0px;
	/* Neutrals/Grey */
	color: rgba(255, 255, 255, 0.65);
}
</style>