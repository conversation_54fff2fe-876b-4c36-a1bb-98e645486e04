import { V2NIMChatroomMember, V2NIMChatroomMemberRole } from './V2NIMChatroomMemberService';
import { V2NIMChatroomQueueChangeType, V2NIMChatroomQueueElement } from './V2NIMChatroomQueueService';
import { V2NIMLocationInfo } from './V2NIMChatroomService';
export interface V2NIMChatroomMessageCreator {
    /**
     * 构造文本消息
     *
     * @param text 文本内容. 不允许为空字符串
     *
     * @example
     * ```
     * const message = nim.V2NIMMessageCreator.createTextMessage('hello world')
     * ```
     */
    createTextMessage(text: string): V2NIMChatroomMessage;
    /**
     * 构造图片消息
     *
     * @param imageObj 图片文件. 浏览器环境请传入 File 对象, 小程序及 uniapp 环境请输入临时文件路径
     * @param name 文件显示名称
     * @param sceneName 场景名
     * @param width 图片宽度.
     * @param height 图片高度.
     */
    createImageMessage(imageObj: string | File, name?: string, sceneName?: string, width?: number, height?: number): V2NIMChatroomMessage;
    /**
     * 构造语音消息
     *
     * @param audioObj 语音文件. 浏览器环境请传入 File 对象, 小程序及 uniapp 环境请输入临时文件路径
     * @param name 文件显示名称
     * @param sceneName 场景名
     * @param duration 音频时长
     */
    createAudioMessage(audioObj: string | File, name?: string, sceneName?: string, duration?: number): V2NIMChatroomMessage;
    /**
     * 构造视频消息
     *
     * @param videoObj 视频文件. 浏览器环境请传入 File 对象, 小程序及 uniapp 环境请输入临时文件路径
     * @param name 文件显示名称
     * @param sceneName 场景名
     * @param duration 音频时长
     * @param width 视频宽度
     * @param height 视频高度
     */
    createVideoMessage(videoObj: string | File, name?: string, sceneName?: string, duration?: number, width?: number, height?: number): V2NIMChatroomMessage;
    /**
     * 构造文件消息
     *
     * @param fileObj 文件. 浏览器环境请传入 File 对象, 小程序及 uniapp 环境请输入临时文件路径
     * @param name 文件显示名称
     * @param sceneName 场景名
     */
    createFileMessage(fileObj: string | File, name?: string, sceneName?: string): V2NIMChatroomMessage;
    /**
     * 构造地理位置消息
     *
     * @param latitude 纬度
     * @param longitude 经度
     * @param address 详细位置信息
     */
    createLocationMessage(latitude: number, longitude: number, address: string): V2NIMChatroomMessage;
    /**
     * 构造自定义消息消息
     *
     * @param rawAttachment 自定义的附件内容
     */
    createCustomMessage(rawAttachment: string): V2NIMChatroomMessage;
    /**
     * 构造自定义消息消息. v10.5.0+ 支持
     *
     * @param attachment 自定义附件. 切记一定要有 raw 属性, 且是其他属性的序列化字符串. 示例 { raw: '{"test":123}', test: 123 }
     * @param subType 消息的子类型, 传入的值大于等于0
     */
    createCustomMessageWithAttachment(attachment: V2NIMMessageCustomAttachment, subType?: number): V2NIMChatroomMessage;
    /**
     * 构造转发消息，消息内容与原消息一样
     *
     * @param message 需要转发的消息体
     */
    createForwardMessage(message: V2NIMChatroomMessage): V2NIMChatroomMessage | null;
    /**
     * 构造提示消息
     *
     * @param text 提示文本
     */
    createTipsMessage(text: string): V2NIMChatroomMessage;
}
/**
 * 客户端本地反垃圾工具类
 */
export interface V2NIMClientAntispamUtil {
    /**
     * 对输入的文本进行本地反垃圾检查
     *
     * @param text 文本内容. 不允许为空字符串
     *
     * @example
     * ```
     * const message = nim.V2NIMClientAntispamUtil.checkTextAntispam('hujintao', '***')
     * ```
     */
    checkTextAntispam(text: string, replace?: string): V2NIMClientAntispamResult;
}
export declare const enum V2NIMMessageSendingState {
    /** 未知，如果消息不是从这个端发送的 */
    V2NIM_MESSAGE_SENDING_STATE_UNKNOWN = 0,
    /** 发送成功 */
    V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED = 1,
    /** 发送失败 */
    V2NIM_MESSAGE_SENDING_STATE_FAILED = 2,
    /** 发送中 */
    V2NIM_MESSAGE_SENDING_STATE_SENDING = 3
}
export declare const enum V2NIMMessageAttachmentUploadState {
    /** 未知，不存在附件，或者不需要存储的附件 */
    V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN = 0,
    /** 上传成功 */
    V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS = 1,
    /** 上传失败 */
    V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED = 2,
    /** 上传中 */
    V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING = 3
}
export declare const enum V2NIMMessageType {
    /** 未知，不合法 */
    V2NIM_MESSAGE_TYPE_INVALID = -1,
    /** 0 文本 */
    V2NIM_MESSAGE_TYPE_TEXT = 0,
    /** 1 图片 */
    V2NIM_MESSAGE_TYPE_IMAGE = 1,
    /** 2 语音 */
    V2NIM_MESSAGE_TYPE_AUDIO = 2,
    /** 3 视频 */
    V2NIM_MESSAGE_TYPE_VIDEO = 3,
    /** 4 位置 */
    V2NIM_MESSAGE_TYPE_LOCATION = 4,
    /** 5 通知 */
    V2NIM_MESSAGE_TYPE_NOTIFICATION = 5,
    /** 6 文件 */
    V2NIM_MESSAGE_TYPE_FILE = 6,
    /** 7 音视频通话 */
    V2NIM_MESSAGE_TYPE_AVCHAT = 7,
    /** 10 提示 */
    V2NIM_MESSAGE_TYPE_TIPS = 10,
    /** 11 机器人 */
    V2NIM_MESSAGE_TYPE_ROBOT = 11,
    /** 12 话单 */
    V2NIM_MESSAGE_TYPE_CALL = 12,
    /** 100 自定义 */
    V2NIM_MESSAGE_TYPE_CUSTOM = 100
}
export declare const enum V2NIMQueryDirection {
    /** 按时间戳从大到小查询 */
    V2NIM_QUERY_DIRECTION_DESC = 0,
    /** 按时间戳从小到大查询 */
    V2NIM_QUERY_DIRECTION_ASC = 1
}
/**
 * 消息附件的父类定义
 */
export interface V2NIMMessageAttachmentBase {
    /**
     * 原始的附件内容。只有自定义消息含有 raw 字段
     *
     * 注: 一般是一个 JSON 序列化后的字符串, 但是 SDK 不会校验它.
     */
    raw?: string;
}
/**
 * 文件附件属性
 */
export interface V2NIMMessageFileAttachment extends V2NIMMessageAttachmentBase {
    /**
     * 浏览器上传时，得到 DOM 指向的 File 对象。该参数和 path 二选一
     */
    file?: File;
    /**
     * 小程序上传时，得到的临时文件路径。该参数和 file 二选一
     */
    path?: string;
    /**
     * 文件大小
     */
    size: number;
    /**
     * 文件上传后的 URL 路径
     */
    url: string;
    /**
     * 文件 md5.
     *
     * 注: 仅在浏览器环境支持 File 对象时有效
     */
    md5?: string;
    /**
     * 文件扩展名
     */
    ext: string;
    /**
     * 文件显示名称
     */
    name: string;
    /**
     * 文件存储场景
     */
    sceneName: string;
    /**
     * 附件上传状态
     */
    uploadState: V2NIMMessageAttachmentUploadState;
}
/**
 * 图片附件属性
 */
export interface V2NIMMessageImageAttachment extends V2NIMMessageFileAttachment {
    /**
     * 图片宽度
     */
    width: number;
    /**
     * 图片高度
     */
    height: number;
}
/**
 * 语音附件属性
 */
export interface V2NIMMessageAudioAttachment extends V2NIMMessageFileAttachment {
    /**
     * 音频时长
     */
    duration: number;
}
/**
 * 视频附件属性
 */
export interface V2NIMMessageVideoAttachment extends V2NIMMessageFileAttachment {
    /**
     * 视频时长
     */
    duration: number;
    /**
     * 视频宽度
     */
    width: number;
    /**
     * 视频高度
     */
    height: number;
}
/**
 * 位置附件属性
 */
export interface V2NIMMessageLocationAttachment extends V2NIMMessageAttachmentBase {
    /**
     * 纬度
     */
    latitude: number;
    /**
     * 经度
     */
    longitude: number;
    /**
     * 详细位置信息
     */
    address: string;
}
export declare type V2NIMGenericFileAttachment = V2NIMMessageFileAttachment | V2NIMMessageImageAttachment | V2NIMMessageAudioAttachment | V2NIMMessageVideoAttachment;
export declare const enum V2NIMChatroomMessageNotificationType {
    /**
     * 成员进入聊天室
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_ENTER = 0,
    /**
     * 成员退出聊天室
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_EXIT = 1,
    /**
     * 成员被加入黑名单
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_BLOCK_ADDED = 2,
    /**
     * 成员被移除黑名单
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_BLOCK_REMOVED = 3,
    /**
     * 成员被禁言
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_CHAT_BANNED_ADDED = 4,
    /**
     * 成员取消禁言
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_CHAT_BANNED_REMOVED = 5,
    /**
     * 聊天室信息更新
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_ROOM_INFO_UPDATED = 6,
    /**
     * 成员被踢
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_KICKED = 7,
    /**
     * 成员临时禁言
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_TEMP_CHAT_BANNED_ADDED = 8,
    /**
     * 成员解除临时禁言
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_TEMP_CHAT_BANNED_REMOVED = 9,
    /**
     * 成员信息更新（nick/avatar/extension）
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_INFO_UPDATED = 10,
    /**
     * 麦序队列有变更
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_QUEUE_CHANGE = 11,
    /**
     * 聊天室被禁言. 仅创建者和管理员可以发消息
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_CHAT_BANNED = 12,
    /**
     * 聊天室解除禁言
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_CHAT_BANNED_REMOVED = 13,
    /**
     * 聊天室新增标签禁言，包括的字段是muteDuration、targetTag、operator、opeNick字段
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAG_TEMP_CHAT_BANNED_ADDED = 14,
    /**
     * 聊天室移除标签禁言，包括的字段是muteDuration、targetTag、operator、opeNick字段
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAG_TEMP_CHAT_BANNED_REMOVED = 15,
    /**
     * 聊天室消息撤回，包括的字段是operator、target、msgTime、msgId、ext字段
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MESSAGE_REVOKE = 16,
    /**
     * 聊天室标签更新
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAGS_UPDATE = 17,
    /**
     * 聊天室成员角色更新
     */
    V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_ROLE_UPDATE = 18
}
export interface V2NIMChatroomNotificationAttachment extends V2NIMMessageAttachmentBase {
    /**
     * 通知类型
     */
    type: V2NIMChatroomMessageNotificationType;
    /**
     * 被操作的成员账号列表
     */
    targetIds?: string[];
    /**
     * 被操作成员的昵称列表
     */
    targetNicks?: string;
    /**
     * 被操作的标签
     */
    targetTag?: string;
    /**
     * 操作者
     */
    operatorId?: string;
    /**
     * 操作者昵称
     */
    operatorNick?: string;
    /**
     * 扩展字段
     */
    notificationExtension?: string;
    /**
     * 更新后的标签
     */
    tags?: string[];
}
export interface V2NIMChatroomMessageRevokeNotificationAttachment extends V2NIMChatroomNotificationAttachment {
    /**
     * 撤回的消息 id
     */
    messageClientId?: string;
    /**
     * 撤回的消息的时间
     */
    messageTime?: string;
}
export interface V2NIMChatroomQueueNotificationAttachment extends V2NIMChatroomNotificationAttachment {
    /**
     * 队列变更的内容
     */
    elements?: V2NIMChatroomQueueElement[];
    /**
     * 队列更新类型
     */
    queueChangeType?: V2NIMChatroomQueueChangeType;
}
export interface V2NIMChatroomChatBannedNotificationAttachment extends V2NIMChatroomNotificationAttachment {
    /**
     * 成员是否被禁言
     */
    chatBanned?: boolean;
    /**
     * 成员是否被临时禁言
     */
    tempChatBanned?: boolean;
    /**
     * 临时禁言的时长
     */
    tempChatBannedDuration?: number;
}
export interface V2NIMChatroomMemberRoleUpdateAttachment extends V2NIMChatroomNotificationAttachment {
    /**
     * 之前的角色类型
     */
    previousRole: V2NIMChatroomMemberRole;
    /**
     * 当前的成员信息
     */
    currentMember: V2NIMChatroomMember;
}
export interface V2NIMChatroomMemberEnterNotificationAttachment extends V2NIMChatroomNotificationAttachment {
    /**
     * 成员是否被禁言
     */
    chatBanned?: boolean;
    /**
     * 成员是否被临时禁言
     */
    tempChatBanned?: boolean;
    /**
     * 临时禁言的时长
     */
    tempChatBannedDuration?: number;
}
export interface V2NIMMessageCustomAttachment extends V2NIMMessageAttachmentBase {
    raw: string;
}
/**
 * 自定义消息附件的解析器结构
 *
 * 解析器必须返回一个 {@link V2NIMMessageCustomAttachment | V2NIMMessageCustomAttachment} 类型的对象. SDK 会校验这个对象一定存在属性 "raw".
 *
 * 注: 开发者可以定义 V2NIMMessageCustomAttachment 类型的子类型
 *
 * @param subType 消息的 subType, 参见 {@link V2NIMMessage.subType | V2NIMMessage.subType}. 若消息中不存在 subType, 则默认值为 0.
 * @param attachRaw 消息的附件原始内容, raw 值. 开发者请小心它为空字符串的情况
 * @returns {@link V2NIMMessageCustomAttachment | V2NIMMessageCustomAttachment} 类型的对象. 开发者可以定义 V2NIMMessageCustomAttachment 类型的子类型.
 *
 * 注: 如果返回的对象中 raw 属性一定会被入参的 attachRaw 再次覆盖, 也就是说 raw 作为原始附件内容,  不允许修改.
 */
export declare type V2NIMMessageCustomAttachmentParser = (subType: number, attachRaw: string) => V2NIMMessageCustomAttachment;
export declare type V2NIMChatroomMessageConfig = {
    /**
     * 是否需要在服务端保存历史消息. 默认 true
     */
    historyEnabled?: boolean;
    /**
     * 是否是高优先级消息. 默认 false
     */
    highPriority?: boolean;
};
export declare type V2NIMMessageRouteConfig = {
    /**
     * 是否需要路由消息（抄送）。默认为 true
     */
    routeEnabled?: boolean;
    /**
     * 环境变量，用于指向不同的抄送，第三方回调等配置
     */
    routeEnvironment?: string;
};
export declare type V2NIMMessageAttachment = V2NIMMessageAttachmentBase | V2NIMMessageFileAttachment | V2NIMMessageImageAttachment | V2NIMMessageAudioAttachment | V2NIMMessageVideoAttachment | V2NIMMessageLocationAttachment | V2NIMChatroomNotificationAttachment | V2NIMChatroomMessageRevokeNotificationAttachment | V2NIMChatroomQueueNotificationAttachment | V2NIMChatroomChatBannedNotificationAttachment | V2NIMChatroomMemberEnterNotificationAttachment | V2NIMChatroomMemberRoleUpdateAttachment;
export declare type V2NIMMessageAntispamConfig = {
    /**
     * 指定消息是否需要经过安全通。默认为 true
     *
     * 对于已开通安全通的用户有效，默认消息都会走安全通，如果对单条消息设置 enable 为 false，则此消息不会走安全通
     */
    antispamEnabled?: boolean;
    /**
     * 指定易盾业务id
     */
    antispamBusinessId?: string;
    /**
     * 自定义消息中需要反垃圾的内容，仅当消息类型为自定义消息时有效
     *
     * 内容必须为 json 格式，格式如下:
     * ```js
     * {
     *   // 1 文本 2 图片 3 视频
     *   "type": 1
     *   // 文本内容；图片地址；视频地址
     *   "data": ""
     * }
     * ````
     */
    antispamCustomMessage?: string;
    /**
     * 易盾反作弊，辅助检测数据，json格式
     */
    antispamCheating?: string;
    /**
     * 易盾反垃圾，增强检测数据，json格式
     */
    antispamExtension?: string;
};
export declare type V2NIMChatroomMessage = {
    /**
     * 客户端消息ID。可以根据该字段确定消息是否重复
     */
    messageClientId: string;
    /**
     * 发送者的客户端类型
     */
    senderClientType: number;
    /**
     * 消息创建时间，由服务器返回。在发送成功之前，消息创建时间为发送者本地时间
     */
    createTime: number;
    /**
     * 消息发送者账号
     */
    senderId: string;
    /**
     * 聊天室 ID
     */
    roomId: string;
    /**
     * @computed
     *
     * 消息发送者是否为自己
     */
    isSelf: boolean;
    /**
     * 附件上传状态。
     *
     */
    attachmentUploadState?: V2NIMMessageAttachmentUploadState;
    /**
     * 消息发送状态. 未知(初始状态), 发送成功，发送失败，发送中。
     *
     * Web端只有以下几个状态：
     * - 初始状态，createXXXMessage 之后
     * - 发送成功，sendMessage 返回值
     */
    sendingState: V2NIMMessageSendingState;
    /**
     * 消息类型
     */
    messageType: V2NIMMessageType;
    /**
     * 消息子类型
     */
    subType?: number;
    /**
     * 消息内容
     */
    text?: string;
    /**
     * 消息附属附件
     *
     * 注1: 附件的父级定义是 V2NIMMessageAttachmentBase, 其中包含了一个必定存在的 raw 属性
     *
     * 注2: 根据 {@link V2NIMChatroomMessage.messageType | V2NIMChatroomMessage.messageType} 的类型, 附件属性的定义类型也有区别: <br/>
     * 1. 文件消息: V2NIMChatroomMessageFileAttachment
     * 2. 图片消息: V2NIMChatroomMessageImageAttachment
     * 3. 语音消息: V2NIMChatroomMessageAudioAttachment
     * 4. 视频消息: V2NIMChatroomMessageVideoAttachment
     * 5. 位置消息: V2NIMChatroomMessageLocationAttachment
     * 6. 通知消息: V2NIMChatroomNotificationAttachment
     *   a. 撤回的消息通知: V2NIMChatroomMessageRevokeNotificationAttachment
     *   b. 队列的消息通知: V2NIMChatroomQueueNotificationAttachment
     *   c. 成员禁言的消息通知
     *   d. 成员进入聊天室的消息通知
     *
     * 可以使用 as 类型断言, 或者类型保护处理联合类型时进行类型细化, 举个例子
     * @example
     * ```js
     * function testMessageAttachmentType(message: V2NIMChatroomMessage): void {
     *   if ('url' in message.attachment) {
     *     if ('width' in message.attachment) {
     *       if ('duration' in message.attachment) {
     *         // message.attachment 到这里就会自动推导出是 V2NIMMessageVideoAttachment 类型
     *       }
     *     }
     *   }
     * }
     */
    attachment?: V2NIMMessageAttachment;
    /**
     * 消息服务端扩展
     */
    serverExtension?: string;
    /**
     * 第三方回调扩展字段
     */
    callbackExtension?: string;
    /**
     * 消息相关配置
     */
    messageConfig?: V2NIMChatroomMessageConfig;
    /**
     * 消息路由配置
     */
    routeConfig?: V2NIMMessageRouteConfig;
    /**
     * 反垃圾相关配置
     */
    antispamConfig?: V2NIMMessageAntispamConfig;
    /**
     * 消息的目标标签表达式， 标签表达式
     *
     * 注, 格式参照: https://doc.commsease.com/messaging/docs/TMxOTI0MDA?platform=android#%E6%A0%87%E7%AD%BE%E8%A1%A8%E8%BE%BE%E5%BC%8F
     */
    notifyTargetTags?: string;
    /**
     * 消息发送时的用户信息.
     *
     * 注: 仅回参时存在, 若作为发送接口的入参使用则无效
     */
    userInfoConfig?: V2NIMUserInfoConfig;
    /**
     * 消息空间坐标信息配置
     */
    locationInfo?: V2NIMLocationInfo;
};
export declare type V2NIMUserInfoConfig = {
    /**
     * 消息发送者uinfo的最后更新时间
     */
    userInfoTimestamp?: number;
    /**
     * 发送方昵称
     */
    senderNick?: string;
    /**
     * 发送者头像
     */
    senderAvatar?: string;
    /**
     * 发送者扩展字段
     */
    senderExtension?: string;
};
export declare type V2NIMSendChatroomMessageParams = {
    /**
     * 消息相关配置
     */
    messageConfig?: V2NIMChatroomMessageConfig;
    /**
     * 路由抄送相关配置
     */
    routeConfig?: V2NIMMessageRouteConfig;
    /**
     * 反垃圾相关配置
     */
    antispamConfig?: V2NIMMessageAntispamConfig;
    /**
     * 是否启用本地反垃圾. 默认 false
     *
     * 注: 只针对文本消息生效
     */
    clientAntispamEnabled?: boolean;
    /**
     * 反垃圾命中后替换的文本
     */
    clientAntispamReplace?: string;
    /**
     * 定向消息接收者账号列表
     */
    receiverIds?: string[];
    /**
     * 消息的目标标签表达式， 标签表达式
     *
     * 注, 表达式的文档参照: https://doc.commsease.com/messaging/docs/TMxOTI0MDA?platform=android#%E6%A0%87%E7%AD%BE%E8%A1%A8%E8%BE%BE%E5%BC%8F
     */
    notifyTargetTags?: string;
    /**
     * 消息空间坐标信息配置
     */
    locationInfo?: V2NIMLocationInfo;
};
export declare type V2NIMSendChatroomMessageResult = {
    /**
     * 发送成功后的消息体
     */
    message: V2NIMChatroomMessage;
    /**
     * 云端反垃圾返回的结果
     */
    antispamResult?: string;
    /**
     * 本地反垃圾结果
     */
    clientAntispamResult?: V2NIMClientAntispamResult;
};
export declare const enum V2NIMClientAntispamOperateType {
    /** 无操作 */
    V2NIM_CLIENT_ANTISPAM_OPERATE_NONE = 0,
    /** 命中后，本地替换 */
    V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE = 1,
    /** 命中后，本地屏蔽，拒绝发送此消息 */
    V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD = 2,
    /** 命中后，消息可以发送，服务器屏蔽，即不会转发给其它用户 */
    V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD = 3
}
export interface V2NIMClientAntispamResult {
    /**
     * 客户端反垃圾文本命中后操作类型
     */
    operateType: V2NIMClientAntispamOperateType;
    /**
     * 替换后的文本
     */
    replacedText: string;
}
export declare type V2NIMChatroomMessageListOption = {
    /**
     * 消息查询方向.
     *
     * 注: 默认 V2NIM_QUERY_DIRECTION_DESC, 按时间从大到小的方向查询
     */
    direction?: V2NIMQueryDirection;
    /**
     * 消息类型
     *
     * 注: 不传或者空列表， 则表示查询所有消息类型
     */
    messageTypes?: V2NIMMessageType[];
    /**
     * 消息查询开始时间
     *
     * 注: 默认为 0, 代表不做限制
     */
    beginTime?: number;
    /**
     * 分页条数限制. 默认 100
     */
    limit?: number;
};
export declare type V2NIMChatroomTagMessageOption = {
    /**
     * 查询的tags. 必填且数组长度不能为 0
     */
    tags?: string[];
    /**
     * 根据消息类型查询会话
     */
    messageTypes?: V2NIMMessageType[];
    /**
     * 消息查询开始时间. 默认为 0 代表不限制, 且一定要小于等于 endTime
     */
    beginTime?: number;
    /**
     * 消息查询结束时间. 默认为 0 代表不限制
     */
    endTime?: number;
    /**
     * 每次查询条数，默认100
     */
    limit?: number;
    /**
     * 消息查询方向. 默认 V2NIM_QUERY_DIRECTION_DESC, 按时间从大到小的方向查询
     */
    direction?: V2NIMQueryDirection;
};
export declare type V2NIMChatroomCDNInfo = {
    /**
     * 是否开启 CDN 轮询.
     */
    enabled: boolean;
    /**
     * cdn的轮询地址，每个表示一个CDN的请求url，每个url是一个匹配串，如下：
     *
     * 如：https://chat-msg-activity.netease.im/abcdefg_#time，端侧需要将#time替换成当前时间戳，时间戳需要跟服务器时间戳对齐，防止本地时间不准的情况
     */
    cdnUrls: string[];
    /**
     * 服务器当前时间戳
     */
    timestamp: number;
    /**
     * 轮询间隔. 单位秒
     */
    pollingIntervalSeconds: number;
    /**
     * 加密类型. 0 表示不加密，1 表示 AES(AES/ECB/PKCS7Padding)
     */
    decryptType?: number;
    /**
     * 解密 key
     */
    decryptKey?: string;
    /**
     * 轮询请求的超时时间. 单位毫秒
     */
    pollingTimeoutMillis: number;
};
