<template>
	<custom-header style="height: 88rpx;" title="个人信息" showBack3 />
	<view class="content">
		<view class="menu-list">
			<view class="menu-item" @click="handleAvatarClick">
				<view class="menu-item-right">
					<text class="menu-text">头像</text>
					<view class="menu-item-content">
						<image class="avatar" :src="userInfo.profilePic" mode="aspectFill" />
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="handleEmailClick">
				<view class="menu-item-right">
					<text class="menu-text">邮箱</text>
					<view class="menu-item-content">
						<text class="menu-item-text">{{formatEmail(userInfo.email)}}</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<!-- <view class="menu-item" @click="handleCountryClick"> -->
			<view class="menu-item">
				<view class="menu-item-right">
					<text class="menu-text">国家</text>
					<view class="menu-item-content">
						<!-- <text v-if="userInfo.country=='China'" class="menu-item-text">中国</text> -->
						<text  class="menu-item-text">中国</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="handleAreaClick">
				<view class="menu-item-right">
					<text class="menu-text">地区</text>
					<view class="menu-item-content">
						<text class="menu-item-text">{{formattedArea}}</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="handleAddreaaClick">
				<view class="menu-item-right">
					<text class="menu-text">街道地址</text>
					<view class="menu-item-content">
						<text class="menu-item-text">{{userInfo.address}}</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue';
	import Quene from '@/components/utils/queue';
	import provinceData from '@/static/area/province.json'
	import cityData from '@/static/area/city.json'
	import config from '@/components/utils/config';
	import Request from '@/components/utils/request';
	export default {
		components: {
			customHeader
		},

		data() {
			return {
				provinceData: provinceData,
				cityData: cityData,
				tempFilePaths: [],
				userInfo: {
					// avatar: '/static/image/user/avatar.png',
					// email: '<EMAIL>',
					// country: '中国',
					// region: '河南 郑州',
					// address: '商鼎路01号'
				}
			}
		},
		computed: {
			formattedArea() {
				if (!this.userInfo.province || !this.userInfo.city) {
					return '';
				}


				const provinceInfo = this.provinceData.find(item => item.id === this.userInfo.province)

				if (!provinceInfo) {
					return '';
				}

				const cityInfoArray = this.cityData[this.userInfo.province];
				if (!cityInfoArray || !Array.isArray(cityInfoArray)) {
					return provinceInfo.name
				}
				const cityInfo = cityInfoArray.find(item => item.id === this.userInfo.city)
				if (!cityInfo) {
					console.log('cityInfo-not', cityInfo)
					return provinceInfo.name;
				}

				return `${provinceInfo.name} ${cityInfo.name}`;
			}
		},
		onShow() {
			this.userInfo = Quene.getData('userinfo');

		},
		onLoad() {
			this.userInfo = Quene.getData('userinfo');
			
			// 添加事件监听
			uni.$on('updateUserInfo', (data) => {
				// 更新对应的字段
				Object.assign(this.userInfo, data);
				// 更新缓存
				Quene.setData('userinfo', this.userInfo);
			});
		},
		beforeDestroy() {
			// 移除事件监听
			uni.$off('updateUserInfo');
		},
		methods: {
			// 格式化邮箱显示
			formatEmail(email) {
				if (!email) return '';
				const [localPart, domain] = email.split('@');
				if (localPart.length <= 3) return email;
				return `${localPart.substring(0, 3)}***@${domain}`;
			},
			handleAvatarClick() {
				uni.chooseImage({
					count: 1, // 只能选择一张图片
					success: (res) => {
						this.tempFilePaths = res.tempFilePaths;
						this.previewImage = res.tempFilePaths[0];
						this.uploadImage()
					},
					fail: (error) => {
						console.error("选择图片失败", error);
					}
				});
			},
			async uploadImage() {
				if (this.tempFilePaths.length) {
					let data = {
						file: this.tempFilePaths[0],
						corp_product: 'huashan',
						name_type: 'org',
						dir_name: 'tool'
					}
					uni.uploadFile({
						url: 'https://fileapitest.eykj.cn/file/post_upload',
						data: data,
						header: {
							Authorization: config.token
						},
						filePath: this.tempFilePaths[0],
						name: 'file',
						success: async (uploadFileRes) => {
							if (uploadFileRes.statusCode == 200) {
								let data = JSON.parse(uploadFileRes.data);
								let post_data = {
									profilePic: data.result,
								};
								const res = await Request.post('/personal/post_modify', post_data)
								if (res.status == 0) {
									uni.showToast({
										title: '更改成功',
										duration: 1000,
										icon: 'none'
									})
									// 使用事件通知更新头像
									uni.$emit('updateUserInfo', { profilePic: data.result });
								} else {
									uni.showToast({
										title: res.msg,
										duration: 2000,
										icon: 'none'
									})
								}
							} else {
								uni.showToast({
									title: '上传失败',
									icon: 'none',
									duration: 2000
								})
							}
						},
						fail: (error) => {
							console.error("上传失败:", error);
						}
					});
				} else {
					uni.showToast({
						title: '请选择图片',
						icon: 'none'
					});
				}
			},

			handleEmailClick() {
				uni.navigateTo({
					url: '/pages/user/components/detail/email'
				})
			},
			handleCountryClick() {
				uni.navigateTo({
					url: '/pages/user/components/detail/country'
				})
			},
			handleAreaClick() {
				uni.navigateTo({
					url: '/pages/user/components/detail/area'
				})
			},
			handleAddreaaClick() {
				uni.navigateTo({
					url: '/pages/user/components/detail/address'
				})
			},

		}

	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	.content {
		padding: 0 32rpx;
		padding-top: 188rpx;
	}

	.menu-list {
		background: rgba(255, 255, 255, 0.0362);
		box-sizing: border-box;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 12rpx;
	}

	.menu-item {
		display: flex;
		height: 116rpx;
		flex-direction: row;
		align-items: center;
		padding-left: 32rpx;
	}

	.avatar {
		width: 56rpx;
		height: 56rpx;
		border-radius: 8rpx;
	}

	.menu-item-left {
		display: flex;
		align-items: center;
	}

	.menu-item-right {
		flex: 1;
		height: 100%;
		padding-right: 32rpx;
		flex-direction: row;
		text-align: center;
		// padding-top: 32rpx;
		align-items: center;
		display: flex;
		border-bottom: 1rpx solid rgba(167, 169, 172, 0.15);
		justify-content: space-between;
	}

	.menu-item:last-child .menu-item-right {
		border-bottom: none;
	}

	.menu-item-content {
		flex-direction: row;
		height: 100%;
		display: flex;
		align-items: center;

		text {
			color: rgba(255, 255, 255, 0.65);
			align-items: center;
			text-align: center;
			font-size: 30rpx;
			line-height: 58rpx;
		}
	}

	.menu-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 32rpx;
	}

	.menu-text {
		font-family: PingFang SC;
		font-size: 34rpx;
		letter-spacing: 0px;
		font-weight: normal;
		color: #FFFFFF;
	}

	.arrow-right {
		width: 60rpx;
		height: 60rpx;
	}
</style>