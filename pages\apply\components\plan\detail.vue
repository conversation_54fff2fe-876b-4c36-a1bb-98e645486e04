<template>
	<custom-header style="height: 88rpx;" title="任务详情" showBack />
	<view class="equip_content">
		<view class="equip_header">
			<text class="header_text">{{prolist.taskCode}}</text>
        <view class="headerfl">
			<view class="header_flex">
				<view>
					<view class="flex_label">任务名称</view>
					<view class="flex_value">{{prolist.drillSite}}</view>
				</view>
				<view>
					<view class="flex_label">任务状态</view>
								<view class="flex_value" v-if="prolist.status==0" style=""><span style="padding: 5rpx 10rpx ;color:#D89614 ;background:rgba(216, 150, 20, 0.1);border-radius: 10rpx;font-size: 23rpx;">进行中</span></view>
								<view class="flex_value" v-if="prolist.status==1" style=""><span style="padding: 5rpx 10rpx ;color:#2ea417;
					background: rgba(73, 170, 25, 0.1);font-size: 23rpx;border-radius: 10rpx;">已完成</span></view>
					
				</view>
			</view>
			<view class="header_flex" style="margin-left:60rpx ;">
				<view>
					<view class="flex_label">负责人</view>
					<view class="flex_value">{{prolist.curatorName}}</view>
				</view>
				<view>
					
					<view class="flex_label">创建时间</view>
										<view class="flex_value">{{prolist.createdAt}}</view>
				</view>
			</view>
			</view>
		</view>
		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        ref="upTabs"
        :list="list4"
        lineWidth="30"
        :current="currentTab"
        lineColor="#177DDC"
        :activeStyle="{
            color: '#177DDC',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:10rpx 15px; padding-right: 15px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		
		<!-- <template v-if="currentTab == 0">
			<view class="content">
			 <view class="cont_flex">
			 	<view class="label">采面长度</view>
				<view class="value">{{prolist.length}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">采面宽度</view>
			 	<view class="value">{{prolist.width}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">采面厚度</view>
			 	<view class="value">{{prolist.thickness}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">开采日期</view>
			 	<view class="value">{{prolist.startTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">结束日期</view>
			 	<view class="value">{{prolist.endTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">备注</view>
			 	<view class="value">{{prolist.remark}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">创建时间</view>
			 	<view class="value">{{prolist.createdAt}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">修改时间</view>
			 	<view class="value">{{prolist.updateAt}}</view>
			 </view>
			 </view>
		</template> -->
		<template v-if="currentTab == 0">
			<drill-cont></drill-cont>
		</template>
		<template v-if="currentTab == 1">
			<!-- <site-cont ref="siteCont" :id="id"></site-cont> -->
		</template>
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
	import drillCont from './drill.vue'
	// import siteCont from './site.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			drillCont,
			// siteCont
		},
		data(){
			return{
				currentTab:0,
				id: null,
			list4:[  
			    // { name: '其他信息' },  
			    { name: '打孔管理' },  
			    // { name: '异常记录' },  
			    // { name: '保养计划记录' },  
			    // { name: '设备寿命跟踪' },  
			    // { name: '设备转移记录' }
			],
			prolist:{
				// taskCode: "CAIMIAN001",
				// taskName: "1号采面",				
				// status: 1,				
				// createdAt: '2023-07-01',				
				// curatorName: "张三",				
			}
			}
		},
		onLoad(options) {
			this.id = options.id
			this.handelDetail()
		},
		onShow(){
			if (this.id) {
				// this.handelDetail()
			}
		},
		methods:{
			handleTabChange(index) {
				this.currentTab = index.index;
				// 如果切换到钻场标签，加载全部数据
				if (this.currentTab === 1) {
					this.$nextTick(() => {
						if (this.$refs.siteCont) {
							this.$refs.siteCont.isFromRoadway = false;
							this.$refs.siteCont.id = null;
							this.$refs.siteCont.handelDrill();
						}
					});
				}
			},
			// 切换到钻场标签并加载数据
			// switchToSite(id) {
			// 	console.log('切换到钻场标签，id:', id);
			// 	this.currentTab = 1; // 切换到钻场标签
			// 	// 等待DOM更新后再调用子组件方法
			// 	this.$nextTick(() => {
			// 		console.log('开始调用siteCont的initWithId方法');
			// 		if (this.$refs.siteCont) {
			// 			console.log('siteCont组件存在，调用initWithId');
			// 			this.$refs.siteCont.initWithId(id);
			// 		} else {
			// 			console.error('siteCont组件不存在');
			// 		}
			// 	});
			// },
			async handelDetail() {
			
				try {
			
					const res = await Request.get('/drilltask/get_info', {
						id: this.id
					})
			
					if (res.status == 0) {
						console.log('返回数据', res);
						this.prolist = res.data;
			
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });
			
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
			
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			
			},
					
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	.equip_content{
		padding:0 32rpx ;
		padding-top: 156rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}
.u-tabs__wrapper__nav__line{
	transform: translate(30px) !important;
}
	.tab_cont{
		border-radius: 12rpx;
		// padding: 0 32rpx;
		margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx  12rpx 0 0;
		padding:0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		
	}
	.content{
		border-radius:  0 0 12rpx  12rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		// height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>