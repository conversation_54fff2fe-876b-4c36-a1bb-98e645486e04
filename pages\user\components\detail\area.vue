<template>
	<view class="area-page">
		<custom-header style="height: 88rpx;" title="选择地区" showBack />
		<view class="content">
			<!-- 省份列表 -->
			<view class="area-list">
				<view class="area-item" 
					v-for="item in provinceData" 
					:key="item.id"
					@click="handleProvinceClick(item)"
				>
					<text class="area-name">{{item.name}}</text>
					<image class="arrow-right" src="/static/image/user/right.png" />
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import customHeader from '@/components/page/header.vue'
import provinceData from '@/static/area/province.json'
import Quene from '@/components/utils/queue'

export default {
	components: {
		customHeader
	},
	data() {
		return {
			provinceData: provinceData
		}
	},
	onLoad(options) {
		// 如果需要处理传入的参数，可以在这里处理
		console.log('options:', options)
	},
	methods: {
		handleProvinceClick(item) {
			uni.navigateTo({
				url: `/pages/user/components/detail/city?provinceId=${item.id}&provinceName=${item.name}`
			})
		}
	}
}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style lang="scss" scoped>
.area-page {
	// min-height: 100vh;
	box-sizing: border-box;
}

.content {
	// padding: 32rpx 0;
	padding-top: 156rpx;
}

.area-list {
	background: rgba(255, 255, 255, 0.0362);
	border: 1rpx solid rgba(255, 255, 255, 0.0972);
	border-radius: 12rpx;
	overflow: hidden;
}

.area-item {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 32rpx;
	border-bottom: 1rpx solid rgba(255, 255, 255, 0.0972);
	
	&:last-child {
		border-bottom: none;
	}
	
	.area-name {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.85);
	}
	
	.arrow-right {
		width: 48rpx;
		height: 48rpx;
	}
}
</style>
