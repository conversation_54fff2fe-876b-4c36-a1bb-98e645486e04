# 文档

## 属性说明
| 属性名 | 类型 | 默认值 | 说明 |
| ----- | ---- | ------ |:----:|
| model-value/v-model | String、Number | '' | 开始日期 |
| v-model:end-date | String、Number | '' | 结束日期 |
| start-format | String | yyyy-MM-dd | 开始日期显示格式 |
| start-value-format | String | '' | 开始日期返回值格式，默认与开始日期显示格式相同 |
| end-format | String | yyyy-MM-dd | 结束日期显示格式 |
| end-value-format | String | '' | 结束日期返回值格式，默认与结束日期显示格式相同 |
| start | String、Number | '' | 最小值，可以使用日期的字符串(String)、时间戳(Number) |
| end | String、Number | '' | 最大值，可以使用日期的字符串(String)、时间戳(Number) |
| border | Boolean | true | 是否有边框 |
| range-separator | String | '-' | 开始日期与结束日期之间的分隔符 |
| date-separator | String | '-' | 开始日期、结束日期内部年月日之间的分隔符 |
| start-placeholder | String | '' | 开始日期的占位内容 |
| end-placeholder | String | '' | 结束日期的占位内容 |
| disabled | Boolean | false | 是否不可选择 |
| clear-icon | Boolean | true | 是否显示清除按钮 |
| hide-second | Boolean | false | 是否显示秒，只显示时分 |
| @change | EventHandle |  | 确定日期时间时触发的事件，参数为前选择的周的周一日期和周日日期组成的数组，例如：['2024-12-02', '2024-12-08'] |
| @maskClick | EventHandle |  | 点击遮罩层触发 |
| @show | EventHandle |  | 弹窗弹出时触发 |

## 示例代码
```
<!-- html -->
<lk-week-picker
  start="2009-11-24"
  start-value-format="yyyy-MM-dd 00:00:00"
  range-separator="~"
  date-separator="."
  :end="today"
  :border="false"
  :clear-icon="false"
  :end-date="weekRange[1]"
  v-model="weekRange[0]"
  @change="rangeChange"
></lk-week-picker>
```
```
<!-- js -->
<script setup>
  import { reactive } from 'vue'

  const weekRange = reactive(['2024-12-02', '2024-12-08'])

  // 切换周
  const rangeChange = range => loadData(range)
</script>
```