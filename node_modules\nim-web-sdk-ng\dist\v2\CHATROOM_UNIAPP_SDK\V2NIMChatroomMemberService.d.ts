import { V2NIMChatroomEnterInfo } from './V2NIMChatroomService';
/**
 * V2NIMChatroomMember
 */
export declare type V2NIMChatroomMember = {
    /**
     * 聊天室ID
     */
    roomId: string;
    /**
     * 成员账号 ID
     */
    accountId: string;
    /**
     * 聊天室成员角色类型
     */
    memberRole: V2NIMChatroomMemberRole;
    /**
     * 成员等级
     *
     * 注: 0 表示未设置
     */
    memberLevel?: number;
    /**
     * 进入聊天室后显示的昵称
     */
    roomNick?: string;
    /**
     * 进入聊天室后显示的头像
     */
    roomAvatar?: string;
    /**
     * 成员扩展字段
     */
    serverExtension?: string;
    /**
     * 用户是否在线
     */
    isOnline: boolean;
    /**
     * 是否在黑名单列表中
     */
    blocked: boolean;
    /**
     * 是否禁言
     */
    chatBanned: boolean;
    /**
     * 是否临时禁言
     */
    tempChatBanned: boolean;
    /**
     * 临时禁言的时长. 单位: 秒
     */
    tempChatBannedDuration: number;
    /**
     * 登录标签
     */
    tags?: string;
    /**
     * 登录登出通知标签. 标签表达式参照下面链接:
     *
     * https://doc.commsease.com/messaging/docs/TMxOTI0MDA?platform=android#%E6%A0%87%E7%AD%BE%E8%A1%A8%E8%BE%BE%E5%BC%8F
     */
    notifyTargetTags?: string;
    /**
     * 用户进入聊天室的时间点.
     */
    enterTime?: number;
    /**
     * 更新时间
     */
    updateTime: number;
    /**
     * 是否有效
     */
    valid: boolean;
    /**
     * 是多端登录信息，同一个账号可以登录多个终端，多个终端同时在线
     */
    multiEnterInfo: V2NIMChatroomEnterInfo[];
};
export declare type V2NIMChatroomMemberQueryOption = {
    /**
     * 成员类型
     */
    memberRoles?: V2NIMChatroomMemberRole[];
    /**
     * 是否只返回黑名单成员. 默认为 false
     */
    onlyBlocked?: boolean;
    /**
     * 是否只返回禁言用户. 默认为 false
     */
    onlyChatBanned?: boolean;
    /**
     * 是否只返回在线成员. 默认为 false
     */
    onlyOnline?: boolean;
    /**
     * 分页偏移量。默认为 "", 代表不限制
     */
    pageToken: string;
    /**
     * 分页一页查询数量. 默认为 100
     */
    limit: number;
};
/**
 * 聊天室成员角色类型
 */
export declare const enum V2NIMChatroomMemberRole {
    /**
     * 普通成员
     */
    V2NIM_CHATROOM_MEMBER_ROLE_NORMAL = 0,
    /**
     * 创建者
     */
    V2NIM_CHATROOM_MEMBER_ROLE_CREATOR = 1,
    /**
     * 管理员
     */
    V2NIM_CHATROOM_MEMBER_ROLE_MANAGER = 2,
    /**
     * 普通游客
     */
    V2NIM_CHATROOM_MEMBER_ROLE_NORMAL_GUEST = 3,
    /**
     * 匿名游客
     */
    V2NIM_CHATROOM_MEMBER_ROLE_ANONYMOUS_GUEST = 4,
    /**
     * 虚构用户
     */
    V2NIM_CHATROOM_MEMBER_ROLE_VIRTUAL = 5
}
export declare type V2NIMChatroomMemberListResult = {
    /**
     * 下一次查询的偏移量
     */
    pageToken: string;
    /**
     * 数据是否拉取完毕
     */
    finished: boolean;
    /**
     * 成员列表
     */
    memberList: V2NIMChatroomMember[];
};
export declare type V2NIMChatroomMemberRoleUpdateParams = {
    /**
     * 设置的成员角色
     *
     * 注: <br/>
     * 可以将普通游客， 普通成员设置为管理员. 只能创建者操作 <br/>
     * 可以将普通游客设置为普通成员. 管理员和创建者均可操作 <br/>
     * 可以将普通成员设置为普通游客. 管理员和创建者均可操作 <br/>
     * 可以将管理员设置为普通成员或普通游客. 只能创建者操作 <br/>
     * 不能操作虚构用户与匿名游客, 设置会报错
     */
    memberRole: V2NIMChatroomMemberRole;
    /**
     * 设置用户等级
     */
    memberLevel?: number;
    /**
     * 本次操作生成的通知中的扩展字段
     */
    notificationExtension?: string;
};
export declare type V2NIMChatroomSelfMemberUpdateParams = {
    /**
     * 聊天室显示的昵称
     *
     * 注: 不传此参数表示不更新. 但是传了空串 "", 会返回参数错误
     */
    roomNick?: string;
    /**
     * 聊天室显示的头像
     *
     * 注: 不传此参数表示不更新
     */
    roomAvatar?: string;
    /**
     * 成员扩展字段
     */
    serverExtension?: string;
    /**
     * 是否需要通知. 默认为 true
     */
    notificationEnabled?: boolean;
    /**
     * 本次操作生成的通知中的扩展字段
     */
    notificationExtension?: string;
    /**
     * 是否更新信息持久化存储, 只针对固定成员身份生效. 默认为 false
     */
    persistence?: boolean;
};
export declare type V2NIMChatroomTagMemberOption = {
    /**
     * 查询的tag
     */
    tag: string;
    /**
     * 分页参数
     */
    pageToken?: string;
    /**
     * 每次查询条数. 默认100
     */
    limit?: number;
};
