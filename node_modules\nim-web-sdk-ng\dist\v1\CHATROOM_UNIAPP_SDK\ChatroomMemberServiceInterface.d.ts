import { ChatroomMember, IBaseMarkChatroomMemberOptions, ISetMemberNormalOptions } from './types';
/**
 * 调用方式:
 * ```js
 * chatroom.chatroomMember.updateMyRoomRole(options)
 * ```
 */
export interface ChatroomMemberServiceInterface {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 更新自己在聊天室中的信息
     *
     * #### 影响范围
     * - 更新自己的信息，聊天室成员收到类型为 attach.type = updateMemberInfo 的聊天室通知消息
     *
     * @example
     * ```js
     * chatroom.chatroomMember.updateMyRoomRole({
     *    member: {
     *      nick: 'myNickName'
     *    },
     *    needNotify: true
     * })
     * ```
     * @locale
     *
     * @locale en
     * Update your information in the chatroom
     * @locale
     */
    updateMyRoomRole(options: UpdateMyRoomRoleOptions): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 设置聊天室管理员
     *
     * #### 影响范围
     * - 管理员可以设置聊天室普通成员, 设置聊天室黑名单, 设置聊天室禁言名单, 踢聊天室成员
     * - 添加管理员时，聊天室成员收到 attach.type = addManager 的聊天室通知消息
     * - 移除管理员时，聊天室成员收到 attach.type = removeManager 的聊天室通知消息
     *
     * @example
     * ```js
     * chatroom.chatroomMember.setMemberManager({
     *   account: 'account'
     *   isAdd: true
     * })
     * ```
     * @locale
     *
     * @locale en
     * Set Chatroom Administrator
     *
     * Administrators can set the chatroom common members, chatroom blacklist, chatroom mute list, and remove the chatroom members.
     * @locale
     */
    setMemberManager(options: IBaseMarkChatroomMemberOptions): Promise<ChatroomMember>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 设置聊天室普通成员
     *
     * #### 影响范围
     * - 聊天室成员收到类型为 attach.type = addCommon 的聊天室通知消息
     * - 移除普通成员时，聊天室成员收到类型为 attach.type = removeCommon 的聊天室通知消息
     * @locale
     *
     * @locale en
     * Set up regular members of the chat room.
     * @locale
     */
    setMemberNormal(options: ISetMemberNormalOptions): Promise<ChatroomMember>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 设置聊天室禁言
     *
     * #### 影响范围
     * - 被加入禁言名单的人将不能在该聊天室发送消息
     * - 聊天室成员收到类型为 attach.type = gagMember 的聊天室通知消息
     * - 移除禁言名单时，聊天室成员收到类型为 attach.type = ungagMember 的聊天室通知消息
     * @locale
     *
     * @locale en
     * Mute Chatroom Members
     *
     * People who have been added to the mute list will not be able to send messages in the chatroom.
     * @locale
     */
    setMemberMute(options: IBaseMarkChatroomMemberOptions): Promise<ChatroomMember>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 设置聊天室黑名单账号。
     *
     * #### 影响范围
     * - 被加入黑名单账号无法加入聊天室。移除黑名单后，用户可以重新进入聊天室
     * - 在线用户被加入黑名单后会被踢出聊天室，并收到 {@link ChatroomEventInterface.kicked} 事件通知
     * - 聊天室其它成员收到类型为 attach.type = blackMember 的聊天室通知消息
     * - 移除黑名单时，聊天室其它成员收到类型为 attach.type = unblackMember 的聊天室通知消息
     *
     * @example
     * ```js
     * chatroom.chatroomMember.setMemberBlackList({
     *   account: 'account'
     *   isAdd: true
     * })
     * ```
     * @locale
     *
     * @locale en
     * Set Chatroom Blacklist
     *
     * People who have been added to the blacklist will not be able to enter the chatroom.
     * @locale
     */
    setMemberBlackList(options: IBaseMarkChatroomMemberOptions): Promise<ChatroomMember>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 根据时间戳分页查询聊天室的成员。
     *
     * 共有四种查询模式：
     * - regular: 聊天室固定成员，包括创建者，管理员，普通等级用户，受限用户（禁言+黑名单），有数量上限
     * - temp: 聊天室非固定成员，只有在线时才能在列表中看到，数量无上限
     * - regularOnline: 聊天室在线的固定成员
     * - regularReverse: 聊天室非固定成员（反向查询）
     *
     * 通过 `chatroom.chatroomMember.setMemberNormal` 设置成员为固定成员
     *
     * @example
     * ```js
     * const members = await chatroom.chatroomMember.queryMembers({
     *    type: 'regular',
     *    limit: 100
     * })
     * ```
     * @locale
     *
     * @locale en
     * Get the list of chatroom members.
     * @locale
     */
    queryMembers(options: QueryMembersOptions): Promise<ChatroomMember[]>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 根据 accounts 获取聊天室成员。只有在线成员，或者固定成员才会返回
     *
     * @example
     * ```js
     * const members = await chatroom.chatroomMember.queryMembersByAccounts({
     *   accounts: ['account1', 'account2']
     * })
     * ```
     * @locale
     *
     * @locale en
     * Get chatroom members by accounts.
     * @locale
     */
    queryMembersByAccounts(options: QueryMembersByAccountsOptions): Promise<ChatroomMember[]>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 设置聊天室临时禁言。临时禁言可以以秒为单位设置。
     *
     * 如果需要解除禁言，可以调用 `setMemberTempMute`，并将 `duration` 设置为 0
     *
     * #### 影响范围
     * - 聊天室成员会收到类型为 attach.type = addTempMute 的聊天室通知消息
     * - 解除禁言时，聊天室成员会收到类型为 attach.type = removeTempMute 的聊天室通知消息
     * @locale
     *
     * @locale en
     * Temporarily Mute a Member
     *
     * When someone is temporarily muted from a chatroom, all chatroom members will receive a chatroom notification of the 'addTempMute' type or 'removeTempMute' type.
     * @locale
     */
    setMemberTempMute(options: SetMemberTempMuteOptions): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 踢聊天室成员
     *
     * #### 影响范围
     * - 被踢出成员收到 {@link ChatroomEventInterface.kicked} 事件通知，并退出聊天室
     * - 聊天室其它成员收到类型为 attach.type = kickMember 的聊天室通知消息
     *
     * @example
     * ```js
     * chatroom.chatroomMember.kickMember({
     *    account: 'account'
     * })
     *
     * chatroom.on('kicked', () => {
     *   console.log('kicked')
     * })
     *
     * chatroom.on('chatroomMsg', (msg) => {
     *    // kickMember
     *    console.log(msg.attach.type)
     * })
     * ```
     * @locale
     *
     * @locale en
     * Remove Chatroom Members
     *
     * When someone is removed from a chatroom, all chatroom members will receive a chatroom notification of the 'kickMember' type.
     * @locale
     */
    kickMember(options: KickMemberOptions): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 获取带有某标签的聊天室成员列表
     * @locale
     *
     * @locale en
     * Get the member list of a chatroom with a label.
     * @locale
     */
    queryMembersByTag(options: QueryMembersByTagOptions): Promise<ChatroomMember[]>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 获取带有某标签的在线的聊天室成员数量
     * @locale
     *
     * @locale en
     * Get the number of online members of a chatroom with a label.
     * @locale
     */
    queryMembersCountByTag(options: QueryMembersCountByTagOptions): Promise<number>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 根据tag设置聊天室临时禁言。临时禁言可以以秒为单位设置。
     *
     * 如果需要解除禁言，可以调用 `setMembersTempMuteByTag`，并将 `duration` 设置为 0
     *
     * #### 影响范围
     * - 聊天室成员会收到类型为 attach.type = addTempMuteTag 的聊天室通知消息
     * - 解除禁言时，聊天室成员会收到类型为 attach.type = removeTempMuteTag 的聊天室通知消息
     * @locale
     *
     * @locale en
     * Temporarily Mute Members with a Specific Tag
     *
     * When someone is temporarily muted in a chatroom, all chatroom members will receive a notification of the 'addTempMuteTag' type or 'removeTempMuteTag' type.
     * @locale
     */
    setMembersTempMuteByTag(options: SetMembersTempMuteByTagOptions): Promise<void>;
}
export interface UpdateMyRoomRoleOptions {
    member: {
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 昵称
         * @locale
         *
         * @locale en
         * Nickname
         * @locale
         */
        nick?: string;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 头像
         * @locale
         *
         * @locale en
         * Avatar
         * @locale
         */
        avatar?: string;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 第三方扩展字段
         * @locale
         *
         * @locale en
         * Third-party extension fields
         * @locale
         */
        ext?: string;
    };
    /**
     * @Multi_Lang_Tag
     * @locale cn
     *
     * @locale
     * 是否需要下发对应的通知消息
     * @locale en
     * Whether the corresponding notification message needs to be sent.
     * @locale
     */
    needNotify: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段
     * @locale
     *
     * @locale en
     * Extension Fields
     * @locale
     */
    ext: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 可选，默认false，是否支持 nick, avator 和 ext 字段的持久化，固定成员有效
     * @locale
     *
     * @locale en
     * (Optional) Whether to support the persistence of nick, avatar, and extension fields; the default value is false; valid for the non-visitor members
     * @locale
     */
    needSave: boolean;
}
export interface QueryMembersCountByTagOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 标签，获取带有这个标签的成员数量
     * @locale
     *
     * @locale en
     * Tag. Get the number of members with this tag.
     * @locale
     */
    tag: string;
}
export interface QueryMembersByTagOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 标签，获取带有这个标签的成员
     * @locale
     *
     * @locale en
     * Tag. Get members with this tag.
     * @locale
     */
    tag: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 起始时间，分页用；逆序查询该时间之后的成员列表
     *
     * 第一次不填，默认为0代表当前服务器时间，即获取第一页
     * 第二页及之后，填上一页最后一个成员的enterTime
     * @locale
     *
     * @locale en
     * Start time, used for pagination; query the member list after the current time.
     *
     * Do not fill in the first time; the default value is 0; 0 represents the current server time, that is, to get the first page
     * On the second page and other pages, fill in the enterTime of the last member of the previous page.
     * @locale
     */
    time?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 分页用, 默认 100
     * @locale
     *
     * @locale en
     * For pagination, default value: 100.
     * @locale
     */
    limit?: number;
}
export interface SetMembersTempMuteByTagOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 禁言的tag
     * @locale
     *
     * @locale en
     * Tag for mute
     * @locale
     */
    tag: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 禁言时长，单位秒，传0表示解除禁言
     * @locale
     *
     * @locale en
     * Mute duration (unit: second); input 0 to unmute.
     * @locale
     */
    duration: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否需要下发对应的通知消息
     * @locale
     *
     * @locale en
     * Whether the corresponding notification message needs to be sent.
     * @locale
     */
    needNotify: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 通知消息广播的目标标签，默认是options.tag
     * @locale
     *
     * @locale en
     * The tag of the target members who need to be notified of the broadcast of the system notifications and messages. The default value is “options.tag”.
     * @locale
     */
    notifyTargetTags?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 对应的通知消息的扩展字段
     * @locale
     *
     * @locale en
     * The extension field of the corresponding system notifications and messages.
     * @locale
     */
    ext?: string;
}
export interface QueryMembersByAccountsOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 待查询的账号列表, 每次最多20个
     * @locale
     *
     * @locale en
     * List of accounts to be queried (number of accounts: up to 20 at a time)
     * @locale
     */
    accounts: string[];
}
export interface SetMemberTempMuteOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 账号
     * @locale
     *
     * @locale en
     * Account
     * @locale
     */
    account: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 禁言时长，单位秒，传0表示解除禁言
     * @locale
     *
     * @locale en
     * Mute duration (unit: second); input 0 to unmute.
     * @locale
     */
    duration: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否需要下发对应的通知消息
     * @locale
     *
     * @locale en
     * Whether the corresponding system notifications and messages need to be sent.
     * @locale
     */
    needNotify?: boolean;
    ext?: string;
}
export interface KickMemberOptions {
    account: string;
    ext?: string;
}
export declare enum ChatroomMemberQueryType {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室固定成员，包括创建者，管理员，普通等级用户，受限用户（禁言+黑名单），有数量上限
     * @locale
     *
     * @locale en
     * Fixed members of the chatroom, including creators, administrators, ordinary users, restricted users (muted + blacklist). There’s an upper limit for the number of fixed members.
     * @locale
     */
    regular = 0,
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室非固定成员，只有在线时才能在列表中看到，数量无上限
     * @locale
     *
     * @locale en
     * The non-fixed members of the chatroom can only be seen in the list when they are online, and the number is unlimited.
     * @locale
     */
    temp = 1,
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室在线的固定成员
     * @locale
     *
     * @locale en
     * Online fixed member of chatroom
     * @locale
     */
    regularOnline = 2,
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室非固定成员（反向查询）
     * @locale
     *
     * @locale en
     * Query non-visitor members of chatroom in the reverse chronological order.
     * @locale
     */
    regularReverse = 3
}
export declare type TChatroomMemberQueryType = keyof typeof ChatroomMemberQueryType;
export interface QueryMembersOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * regular: 聊天室固定成员，包括创建者，管理员，普通等级用户，受限用户（禁言+黑名单），有数量上限
     * @locale
     *
     * @locale en
     * regular: Fixed members of the chatroom, including creators, administrators, ordinary users, restricted users (muted + blacklist). There’s an upper limit for the number of fixed members.
     * @locale
     *
     * @Multi_Lang_Tag
     * @locale cn
     * temp: 聊天室非固定成员，只有在线时才能在列表中看到，数量无上限
     * @locale
     *
     * @locale en
     * temp: The non-fixed members of the chatroom can only be seen in the list when they are online, and the number is unlimited.
     * @locale
  
     * @Multi_Lang_Tag
     * @locale cn
     * regularOnline: 聊天室在线的固定成员
     * @locale
     *
     * @locale en
     * regularOnline:  Online fixed member of chatroom
     * @locale
  
     * @Multi_Lang_Tag
     * @locale cn
     * regularReverse: 聊天室非固定成员（反向查询）
     * @locale
     *
     * @locale en
     * regularReverse: Query non-visitor members of chatroom in the reverse chronological order.
     * @locale
     */
    type: TChatroomMemberQueryType;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 分页用, 查找该时间戳之前的成员
     * @locale
     *
     * @locale en
     * For paging; find members before this timestamp.
     * @locale
     */
    time?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 分页用, 默认 100
     * @locale
     *
     * @locale en
     * For pagination; the default value is 100.
     * @locale
     */
    limit?: number;
}
