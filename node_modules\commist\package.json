{"name": "commist", "version": "1.1.0", "description": "Build your commands on minimist!", "main": "index.js", "scripts": {"test": "standard && tape test.js"}, "pre-commit": "test", "repository": {"type": "git", "url": "https://github.com/mcollina/commist.git"}, "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/mcollina/commist/issues"}, "homepage": "https://github.com/mcollina/commist", "dependencies": {"leven": "^2.1.0", "minimist": "^1.1.0"}, "devDependencies": {"minimist": "^1.1.0", "pre-commit": "^1.0.0", "standard": "^12.0.1", "tape": "^4.0.0"}}