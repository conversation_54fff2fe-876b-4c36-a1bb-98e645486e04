<template>
  <view class="container">
    <text>MQTT状态: {{ connectionStatus }}</text>
    
    <button @click="connectMQTT">连接MQTT</button>
    <button @click="disconnectMQTT">断开连接</button>
    <button @click="publishTestMessage">发送测试消息</button>
    
    <!-- 消息列表 -->
    <scroll-view class="message-list" scroll-y>
      <view v-for="(msg, index) in messages" :key="index" class="message-item">
        <text>{{ msg }}</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
// 导入MQTT工具类 - 只保留私有服务器相关函数
import { 
  connectPrivateServer,
  disconnectMqtt, 
  publishMessage, 
  onMessageReceived, 
  isConnected 
} from '@/utils/mqtt-helper.js';

export default {
  data() {
    return {
      connectionStatus: '未连接',
      messages: [],
      activeTopic: '/sys/todo/update',
      client: null,
    }
  },
  
  beforeUnmount() {
    this.disconnectMQTT();
  },
  
  methods: {
    // 连接MQTT
    connectMQTT() {
      this.connectionStatus = '正在连接到私有MQTT服务器...';
      this.messages = []; // 清空消息列表
      
      // 连接到私有服务器
      this.client = connectPrivateServer(this.activeTopic);
      
      // 设置事件处理
      this._setupEventHandlers();
    },
    
    // 设置事件处理器
    _setupEventHandlers() {
      if (!this.client) return;
      
      // 连接成功回调
      this.client.on('connect', () => {
        this.connectionStatus = '已连接';
        this.messages.push('已连接到私有MQTT服务器');
        this.messages.push(`已订阅主题: ${this.activeTopic}`);
      });
      
      // 消息接收回调
      onMessageReceived((topic, message) => {
        const timestamp = new Date().toLocaleTimeString();
        let messageContent;
        
        try {
          // 尝试解析JSON
          const jsonData = JSON.parse(message.toString());
          messageContent = JSON.stringify(jsonData, null, 2);
        } catch (e) {
          // 非JSON消息，直接使用字符串
          messageContent = message.toString();
        }
        
        this.messages.push(`[${timestamp}] 收到消息:
        主题: ${topic}
        内容: ${messageContent}`);
        
        // 限制消息列表长度
        if (this.messages.length > 50) {
          this.messages.shift();
        }
      });
      
      // 错误回调
      this.client.on('error', (error) => {
        this.connectionStatus = `连接错误: ${error.message || '未知错误'}`;
        this.messages.push(`MQTT错误: ${error.message || '未知错误'}`);
      });
      
      // 断开连接回调
      this.client.on('close', () => {
        this.connectionStatus = '已断开';
        this.messages.push('MQTT连接已断开');
      });
      
      // 重连回调
      this.client.on('reconnect', () => {
        this.connectionStatus = '正在重连...';
        this.messages.push('MQTT正在重连...');
      });
    },
    
    disconnectMQTT() {
      disconnectMqtt();
      this.client = null;
      this.connectionStatus = '已断开';
      this.messages.push('MQTT连接已主动断开');
    },
    
    publishTestMessage() {
      if (!isConnected()) {
        this.messages.push('发送失败: MQTT未连接');
        return;
      }
      
      // 创建一个测试消息
      const message = {
        text: '这是一条测试消息',
        timestamp: new Date().toISOString(),
        sender: 'UniApp App'
      };
      
      // 发布消息
      const success = publishMessage(this.activeTopic, message);
      
      if (success) {
        this.messages.push(`已发送消息到主题: ${this.activeTopic}`);
      } else {
        this.messages.push('发送消息失败');
      }
    }
  }
}
</script>

<style>
.container {
  padding: 20rpx;
}

.message-list {
  height: 500rpx;
  margin: 20rpx 0;
  border: 1px solid #eee;
  border-radius: 5rpx;
}

.message-item {
  padding: 10rpx;
  border-bottom: 1px solid #f5f5f5;
  font-size: 28rpx;
}
</style>