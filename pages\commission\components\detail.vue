<template>
	<view class="detail-page">
		<custom-header style="height: 88rpx;" title="待办详情" showBack />
		<view class="content">
			<!-- 基本信息卡片 -->
			<view class="detail-card">
				<view class="card-header">
					<text class="header-title">{{detail.title}}</text>
					<text class="header-text">等待我处理</text>
				</view>
				<view class="card-content" >
					<template v-for="item in parsedDynamicFields">
					<view class="info-item">
						<text class="label">{{item.title}}</text>
						<text class="value">{{item.value}}</text>
					</view>
					</template>
					<view class="info-item">
						<text class="label">创建时间</text>
						<text class="value">{{detail.createdAt}}</text>
					</view>
					<!-- <view class="info-item">
						<text class="label">修改时间</text>
						<text class="value">{{detail.updateAt}}</text>
					</view> -->
					<!-- <view class="info-item">
						<text class="label">施工任务名称</text>
						<text class="value">{{detail.taskName}}</text>
					</view>
					<view class="info-item">
						<text class="label">任务描述</text>
						<text class="value">{{detail.description}}</text>
					</view>
					<view class="info-item">
						<text class="label">施工要求</text>
						<text class="value">{{detail.constructionRequirements}}</text>
					</view>
					<view class="info-item">
						<text class="label">施工图纸</text>
						<text class="value">{{detail.constructionDrawings}}</text>
					</view>
					<view class="info-item">
						<text class="label">任务下发人</text>
						<text class="value">{{detail.taskPerson}}</text>
					</view>
					<view class="info-item">
						<text class="label">任务接收人</text>
						<text class="value">{{detail.taskRecipient}}</text>
					</view> -->
					<!-- <view class="info-item">
						<text class="label">处理状态</text>
						<text class="value" :style="{ color: getStatusColor(detail.status) }">{{getStatusText(detail.status)}}</text>
					</view>
					<view class="info-item" v-if="detail.status === 1 && detail.refuse">
						<text class="label">拒绝原因</text>
						<text class="value" style="color: #D32029;">{{detail.refuse}}</text>
					</view> -->
					<!-- <view class="info-item">
						<text class="label">接受确认日期</text>
						<text class="value">{{detail.receiptDate}}</text>
					</view>
					<view class="info-item">
						<text class="label">任务下发日期</text>
						<text class="value">{{detail.taskDate}}</text>
					</view>
					<view class="info-item">
						<text class="label">计划开始日期</text>
						<text class="value">{{detail.startTime}}</text>
					</view>
					<view class="info-item">
						<text class="label">计划完成日期</text>
						<text class="value">{{detail.EndTime}}</text>
					</view> -->
				</view>
			</view>
			
			<!-- 流程卡片 -->
			<!-- <view class="detail-card">
				<view class="card-header">
					<text class="header-title">流程</text>
				</view>
				<view class="card-content">
					<view class="flow-list">
						<view class="flow-item" v-for="(item, index) in flowList" :key="index">
							<view class="flow-left">
								<view class="avatar-wrapper">
									<view class="avatar">
										<image :src="item.avatar" mode="aspectFill"></image>
									</view>
									<view class="status-icon" :class="item.status">
										<image v-if="item.status === 'success'" src="/static/image/success.png" mode="aspectFill"></image>
										<image v-else-if="item.status === 'waiting'" src="/static/image/waiting.png" mode="aspectFill"></image>
									</view>
									<view class="connect-line" v-if="index !== flowList.length - 1"></view>
								</view>
							</view>
							<view class="flow-right">
								<view class="flow-header">
									<text class="role">{{item.role}}</text>
									<text class="flow-time">{{item.time}}</text>
								</view>
								<view class="flow-name">
									<text class="name">{{item.name}} </text>
									<text v-if="item.status === 'waiting'" class="name"> (审批中)</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view> -->
			
		</view>

	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request.js'
	
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				todoId: null,
				detail: {
					id: 'TODO001',
					title: '钻机检修申请',
					createTime: Date.now(),
					status: 1, // 1: 待处理, 2: 已同意, 3: 已拒绝
					priority: 2, // 1: 普通, 2: 紧急
					projectContent: 'XX项目钻机设备定期检修',
					todoContent: '请审核钻机检修申请，检查设备状态是否符合检修标准'
				},
				rolelist:{
					
				},
				flowList: [
					{
						name: '我',
						role: '发起审批',
						time: '01-22 17:23',
						avatar: '/static/image/user/avatar.png',
						status: 'success'
					},
					{
						name: '徐星辰',
						role: '审批人',
						time: '01-22 17:23',
						avatar: '/static/image/user/avatar.png',
						status: 'success'
					},
					{
						name: '徐星辰',
						role: '抄送人',
						time: '',
						avatar: '/static/image/user/avatar.png',
						status: 'waiting'
					}
				],
				parsedDynamicFields: '',
				showRefusePopup: false,
				refuseReason: ''
			}
		},
		onLoad(options) {
			if (options.id) {
				this.todoId = options.id
				this.getData()
			}
		},
		
		methods: {
			// 获取详情数据
			async getData() {
				try {
					const res = await Request.get('/todo/get_info', { id: this.todoId })
					if (res.status == 0) {
						this.detail = res.data
						// 处理动态字段数据
						if (res.data.dynamicFields) {
							 try {
							            this.parsedDynamicFields = JSON.parse(res.data.dynamicFields)
							            console.log(this.parsedDynamicFields); // 修正为 this.parsedDynamicFields
							          } catch (e) {
							            console.error('解析动态字段失败:', e)
							            this.parsedDynamicFields = []
							          }
						}
					} else {
						uni.showToast({
							title: res.msg || '加载失败',
							icon: 'none'
						})
					}
				} catch (error) {
					console.error('获取待办详情失败:', error)
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none'
					})
				}
			},
			
			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					0: '待进行',
					1: '已处理',
				}
				return statusMap[status] || '--'
			},
			
			// 获取状态颜色
			getStatusColor(status) {
				const colorMap = {
					0: '#177DDC',
					1: '#49AA19',
				}
				return colorMap[status] || 'rgba(255, 255, 255, 0.85)'
			},
			
			
			

		}
	}
</script>

<style lang="scss" scoped>
	.detail-page {
		// min-height: 100vh;
		// background: #16171b;
		// padding-top: 88rpx;
		box-sizing: border-box;
	}
	
	.content {
		padding: 32rpx;
	}
	
	.detail-card {
		background: rgba(255, 255, 255, 0.04);
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 12rpx;
		margin-bottom: 32rpx;
		
		.card-header {
			padding: 24rpx 32rpx 0;
			display: flex;
			flex-direction: column;
			// border-bottom: 1rpx solid rgba(255, 255, 255, 0.0972);
			
			.header-title {
				font-size: 32rpx;
				font-weight: 500;
				color: rgba(255, 255, 255, 0.85);
			}
			.header-text{
				margin-top: 10rpx;
				font-size: 24rpx;
				color: #D89614;
			}
		}
		
		.card-content {
			padding: 32rpx;
		}
	}
	
	.info-item {
		display: flex;
		justify-content: space-between;
		margin-bottom: 24rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.45);
		}
		
		.value {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.85);
		}
	}
	
	.content-section {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 32rpx;
		
		&:last-child {
			margin-bottom: 0;
		}
		
		.section-title {
			display: block;
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.45);
			// margin-bottom: 16rpx;
		}
		
		.section-text {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.85);
			// line-height: 1.6;
			// white-space: pre-wrap;
		}
	}
	
	.action-buttons {
		display: flex;
		gap: 32rpx;
		margin-top: 48rpx;
		
		button {
			flex: 1;
			height: 88rpx;
			line-height: 88rpx;
			font-size: 32rpx;
			border-radius: 12rpx;
			border: none;
			
			&.btn-reject {
				background: rgba(255, 255, 255, 0.04);
				color: rgba(255, 255, 255, 0.85);
				
				&:active {
					opacity: 0.7;
				}
			}
			
			&.btn-approve {
				background: #177DDC;
				color: #fff;
				
				&:active {
					opacity: 0.7;
				}
			}
		}
	}
	
	.refuse-popup {
		width: 600rpx;
		background: #16171b;
		border-radius: 20rpx;
		padding: 32rpx;
		
		.popup-title {
			font-size: 32rpx;
			color: rgba(255, 255, 255, 0.85);
			text-align: center;
			margin-bottom: 32rpx;
		}
		
		.popup-content {
			margin-bottom: 32rpx;
			
			::v-deep .u-textarea {
				background: rgba(255, 255, 255, 0.04);
				border: 1rpx solid rgba(255, 255, 255, 0.1);
				border-radius: 12rpx;
				padding: 16rpx;
				
				.u-textarea__field {
					color: rgba(255, 255, 255, 0.85);
				}
			}
		}
		
		.popup-buttons {
			display: flex;
			gap: 32rpx;
			
			.popup-btn {
				flex: 1;
				height: 88rpx;
				line-height: 88rpx;
				font-size: 32rpx;
				border-radius: 12rpx;
				border: none;
				
				&.cancel {
					background: rgba(255, 255, 255, 0.04);
					color: rgba(255, 255, 255, 0.85);
				}
				
				&.confirm {
					background: #177DDC;
					color: #fff;
				}
				
				&:active {
					opacity: 0.7;
				}
			}
		}
	}
	
	:deep(.u-modal) {
		.u-modal__title {
			padding: 32rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: #333;
		}
		
		.u-modal__content {
			padding: 24rpx;
			padding-right: 0;
			display: block;
			
			.modal-content {
				.u-textarea {
					background: #f8f8f8;
					border-radius: 8rpx;
					padding: 16rpx;
					width: calc(100% - 60rpx);
					min-height: 200rpx;
					
					&__field {
						font-size: 28rpx;
						color: #333;
						line-height: 1.5;
					}
				}
			}
		}
		
		.u-modal__button-group {
			border-top: 1rpx solid #eee;
			
			.u-modal__button {
				font-size: 32rpx;
				color: #666;
				height: 100rpx;
				
				&--primary {
					color: #2979ff;
				}
			}
		}
	}
	
	.flow-list {
		.flow-item {
			display: flex;
			align-items: flex-start;
			margin-bottom: 75rpx;
			position: relative;
			
			&:last-child {
				margin-bottom: 0;
			}
			
			.flow-left {
				margin-right: 20rpx;
				
				.avatar-wrapper {
					position: relative;
					width: 80rpx;
					height: 80rpx;
					
					.avatar {
						width: 100%;
						height: 100%;
						border-radius: 8rpx;
						overflow: hidden;
						background: rgba(255, 255, 255, 0.04);
						
						image {
							width: 100%;
							height: 100%;
						}
					}
					
					.status-icon {
						position: absolute;
						right: -10rpx;
						bottom: -10rpx;
						width: 30rpx;
						height: 30rpx;
						display: flex;
						align-items: center;
						border-radius: 50%;
						z-index: 2;
						
						// &.success {
						// 	background: #D8D8D8;
						// }
						
						// &.waiting {
						// 	background: #D8D8D8;
						// }
						
						image {
							width: 100%;
							height: 100%;
							background: #000;
							border-radius: 50%;
							border:1rpx;
						}
					}
					
					.connect-line {
						position: absolute;
						left: 50%;
						top: calc(100% + 11rpx);
						width: 2rpx;
						height: 54rpx;
						background: rgba(255, 255, 255, 0.08);
						// transform: translateX(-50%);
					}
				}
			}
			
			.flow-right {
				flex: 1;
				// padding-top: 10rpx;
				
				.flow-header {
					display: flex;
					justify-content: space-between;
					align-items: center;
					// margin-bottom: 8rpx;
					
					.role {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.45);
					}
					
					.flow-time {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.3);
					}
				}
				
				.flow-name {
					.name {
						font-size: 24rpx;
						color: rgba(255, 255, 255, 0.85);
					}
				}
			}
		}
	}
</style> 