import configdata from './config'
import cache from './cache';
import queue from './queue'

function config(name) {
	    var info = null;
	    if (name) {
	        var name2 = name.split("."); //字符分割
	        if (name2.length > 1) {
	            info = configdata[name2[0]][name2[1]] || null;
	        } else {
	            info = configdata[name] || null;
	        }
	        if (info == null) {
	            let web_config = cache.get("web_config");
	            if (web_config) {
	                if (name2.length > 1) {
	                    info = web_config[name2[0]][name2[1]] || null;
	                } else {
	                    info = web_config[name] || null;
	                }
	            }
	        }
	    }
	    return info;
	}
	// 通用POST
	function post(url, data, header) {
	    header = queue.getData("Authorization");
		// header=this.config("Authorization");
	    url = this.config("COMMONAPI") + url;
		// console.log(url);
		// console.log(data);
		// console.log(header);
		// uni.showLoading({
		// 	title: '加载中...',
		// 	mask: true,
		// })
	
		//注入通用数据: userLoginname
		// let loginname = '';
		// if (auth.loginStatus()) {
		// 	loginname = queue.getData('user').loginname;
		// }
		// data.userLoginname = loginname;
		
		// data = utils.gethaveSignData(data);
		
	    return new Promise((succ, error) => {
	        uni.request({
	            url: url,
	            data: data,
	            method: "POST",
	            header: {
	                "Authorization": header,
					"serviceType":3
	            },
	            success: function (result) {
	                // if (isTokenExpired(result.data)) {
	                //     handleTokenExpired();
	                //     return;
	                // }
	                succ.call(self, result.data);
	            },
	            fail: function (e) {
	                error.call(self, e)
	            },
				complete: function(){
					// uni.hideLoading();
				}
	        })
	    })
	}
	//上传图片
	function postimg(url, data, header) {
	    header = queue.getData("Authorization");
	    url = 'https://fileapitest.eykj.cn' + url;
		// console.log(url);
		// console.log(data);
		// console.log(header);
		// uni.showLoading({
		// 	title: '加载中...',
		// 	mask: true,
		// })
	
		//注入通用数据: userLoginname
		// let loginname = '';
		// if (auth.loginStatus()) {
		// 	loginname = queue.getData('user').loginname;
		// }
		// data.userLoginname = loginname;
		
		// data = utils.gethaveSignData(data);
		
	    return new Promise((succ, error) => {
	        uni.request({
	            url: url,
	            data: data,
	            method: "POST",
	            header: {
	                "Authorization": header
	            },
	            success: function (result) {
	                // if (isTokenExpired(result.data)) {
	                //     handleTokenExpired();
	                //     return;
	                // }
	                succ.call(self, result.data);
	            },
	            fail: function (e) {
	                error.call(self, e)
	            },
				complete: function(){
					// uni.hideLoading();
				}
	        })
	    })
	}
	//通用GET
	function get(url, data, header) {
	    header = queue.getData("Authorization");
		// header=this.config("Authorization");
	    url = this.config("COMMONAPI") + url;
	    return new Promise((succ, error) => {
	        uni.request({
	            url: url,
	            data: data,
	            method: "GET",
	            header: {
	               "Authorization": header,
	               "serviceType":3
	            },
	            success: function (result) {
	                // if (isTokenExpired(result.data)) {
	                //     handleTokenExpired();
	                //     return;
	                // }
	                succ.call(self, result.data);
	            },
	            fail: function (e) {
	                error.call(self, e)
	            }
	        })
	    })
	}
	
	// // 添加全局处理 token 失效的函数
	// function handleTokenExpired() {
	//   // 正确清除本地存储的 token 和用户信息
	//   uni.removeStorageSync("Authorization"); // 直接使用 uni API 清除存储
	//   // 或者设置为空值
	//   // queue.setData("Authorization", ""); 
	//    uni.removeStorageSync("userinfo");
	//   cache.remove("user");
	  
	//   // 显示提示信息
	//   uni.showToast({
	//     title: '登录已过期，请重新登录',
	//     icon: 'none',
	//     duration: 2000
	//   });
	  
	//   // 延迟跳转到登录页面，给用户时间看提示
	//   setTimeout(() => {
	//     uni.reLaunch({
	//       url: '/pages/login/login'
	//     });
	//   }, 1500);
	// }
	
	// // 检查响应数据是否表明 token 失效
	// function isTokenExpired(responseData) {
	//   return (
	//     (responseData?.status === -1 && responseData.msg === '非法请求头参数值，请先进行登陆！') ||
	//     responseData?.code === 401 || 
	//     responseData?.code === '401' || 
	//     (responseData?.msg && responseData.msg.includes('token失效'))
	//   );
	// }
	
	export default {
	    config: config,
	    get: get, 
	    post: post,
		postimg:postimg,
		// handleTokenExpired: handleTokenExpired,
		// isTokenExpired: isTokenExpired
	};