<template>
	<custom-header  style="height: 88rpx;" title="街道地址" showBack rightContent="提交" rightType="text"  :rightClick="handleSubmit" />
	<view class="address_cont">
		<view class="add_count">
			<input type="text" v-model="address" class="address_text" placeholder="请输入街道地址" />
			<image v-show="resetshow" class="address_img" src="../../../../static/image/user/close.png" mode="" @click="reset"></image>
		</view>
		
	</view>
</template>

<script>
	import customHeader  from '@/components/page/header.vue';
	import Quene from '@/components/utils/queue';
	import Request from '@/components/utils/request';
	export default {
		components:{
			customHeader 
		},
		
		data() {
			return {
				resetshow:false,
				address:'',
				userInfo: {
					// avatar: '/static/image/user/avatar.png',
					// email: '<EMAIL>',
					// country: '中国',
					// region: '河南 郑州',
					// address: '商鼎路01号'
				}
			}
		},
		watch: {
			address(newVal) {
				this.resetshow = newVal.trim() !== '';
			}
		},
		onShow() {
			this.userInfo=Quene.getData('userinfo');
		
		},
		methods: {
			
			async handleSubmit(){
				if(!this.address.trim()){
					uni.showToast({
					    title: '请输入街道地址',
					    icon: 'none'
					});
					return;
				}
				try {
					const res = await Request.post('/personal/post_modify', {
						address: this.address
					});
				
					if (res.status === 0) {
						uni.showToast({
							title: '修改成功',
							icon: 'none',
							duration: 2000
						});
						
						// 使用事件通知更新地址
						uni.$emit('updateUserInfo', {
							address: this.address
						});
						
						// 返回上一页
						uni.navigateBack();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('修改失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
				
			reset(){
				this.address = '';
			}
			
		}

	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>

<style lang="scss" scoped>
	.address_cont{
		padding-top: 188rpx;
	
	}
	.add_count{
		display: flex;
		align-items: center;
		padding:0 32rpx;
		background: rgba(255, 255, 255, 0.03);
		border: 0px solid rgba(255, 255, 255, 0.0972);
	}
	.address_text{
		flex:1;
		height: 108rpx;
		font-size: 34rpx;
		color:rgba(255, 255, 255, 0.65) ;
		// background: rgba(255, 255, 255, 0.03);
		box-sizing: border-box;
		border: 0px solid rgba(255, 255, 255, 0.0972);
	}
	.address_img{
		width: 48rpx;
		height: 48rpx;
	}
</style>