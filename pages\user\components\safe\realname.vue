<template>
	<custom-header  style="height: 88rpx;" title="实名认证" showBack />
		<view class="password-container">
			<!-- 姓名输入框 -->
			<view class="input-group">
				<view class="group-left">
					<text>姓名</text>
					<input 
						v-model="name"
						placeholder="请输入姓名"
						class="password-input"
					/>
				</view>
			</view>
			
		<!-- 身份证号输入框 -->
		        <view class="input-group">
		            <view class="group-left">
		                <text>身份证号</text>
		                <input
		                    type="text"
		                    v-model="idcard"
		                    placeholder="请输入您的身份证号"
		                    class="password-input"
		                   
		                />
		            </view>
				<!-- <image 
					:src="showidCard ? '/static/image/user/open-eye.png' : '/static/image/user/close-eye.png'"
					class="eye-icon"
					@click="toggleidCard"
				/> -->
			</view>
			<button class="btn" @click="handleConfirm">确定</button>
		</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request'
	import Quene from '../../../../components/utils/queue'
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				name: '', // 姓名
				idcard: '', // 身份证号
				showidCard: false, // 控制新密码显示/隐藏
				userInfo:{}
			}
		},
		onLoad(options) {
		       
		    },
			onShow(){
				this.userInfo = Quene.getData('userinfo');
			},
		methods: {
			 // 切换身份证号显示/隐藏
			        toggleidCard() {
			            this.showidCard = !this.showidCard
			        },
			        // 对身份证号进行掩码处理
			        maskIdCard(idCard) {
			            if (!idCard) return ''
			            if (idCard.length <= 5) return idCard
			            return idCard.substring(0, 3) + '*'.repeat(idCard.length - 5) + idCard.substring(idCard.length - 2)
			        },
					//验证身份证号
					validateidNumber() {
						const idNumberReg =  /^[0-9]{17}[0-9X]$/;
						return idNumberReg.test(this.idcard);
					},
					 // 添加确认按钮处理方法
					      async handleConfirm() {
					            if (!this.name.trim()) {
					                uni.showToast({
					                    title: '请输入姓名',
					                    icon: 'none'
					                })
					                return
					            }
					            
					            if(!this.validateidNumber()) {
					            	uni.showToast({
					            		title: '请输入正确的身份证号',
					            		icon: 'none'
					            	});
					            	return;
					            }
					            try {
					            	const res = await Request.post('/personal/post_authenticate', {
					            		name: this.name,
					            		idNumber: this.idcard
					            	});
					            	
					            	if (res.status === 0) {
					            		uni.showToast({
					            			title: '实名认证成功',
					            			icon: 'none',
					            			duration: 2000
					            		});
					            		
					            		// 使用事件通知更新用户信息
					            		uni.$emit('updateUserInfo', {
					            			name: this.name,
					            			idNumber: this.idcard
					            		});
					            		
					            		// 返回上一页
					            		uni.navigateBack();
					            	} else {
					            		uni.showToast({
					            			title: res.msg,
					            			icon: 'none',
					            			duration: 2000
					            		});
					            	}
					            } catch (error) {
					            	console.error('修改失败:', error);
					            	uni.showToast({
					            		title: '网络错误，请稍后重试',
					            		icon: 'none',
					            		duration: 2000
					            	});
					            }
					           
					        }
		}
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style lang="scss" scoped>
.password-container {
	padding: 0 40rpx;
	padding-top: 188rpx;
}
.input-group {
	margin-top: 48rpx;
	display: flex;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	height: 120rpx;
	border-radius: 24rpx;
	padding: 0 32rpx;
	background: rgba(255, 255, 255, 0.13);
	backdrop-filter: blur(20rpx);
}
.group-left{
	text{
		font-family: Inter;
		font-size: 24rpx;
		font-weight: normal;
		letter-spacing: 0.4rpx;
		line-height: 34rpx;
		color: rgba(255, 255, 255, 0.45);

	}
}
.password-input {
	flex: 1;
	font-size: 28rpx;
	height: 40rpx;
	color: rgba(255, 255, 255, 0.95);
}
.eye-icon {
	width: 48rpx;
	height: 48rpx;
}
.btn{
	margin-top: 92rpx;
	font-family: Inter;
	font-size: 40rpx;
	font-weight: 500;
	height: 96rpx;
	letter-spacing: 0.4rpx;
	color: #FFFFFF;
	border-radius: 16rpx;
	background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
}
</style>