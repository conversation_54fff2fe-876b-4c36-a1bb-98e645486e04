<template>
	<custom-header style="height: 88rpx;" title="修改密码" showBack />
	<view class="password-container">
		<!-- 旧密码输入框 -->
		<view class="input-group">
			<view class="group-left">
				<text>旧密码</text>
				<input :type="showOldPassword ? 'text' : 'password'" v-model="oldPassword" placeholder="请输入旧密码"
					class="password-input" />
			</view>
			<image :src="showOldPassword ? '/static/image/user/open-eye.png' : '/static/image/user/close-eye.png'"
				class="eye-icon" @click="toggleOldPassword" />
		</view>

		<!-- 新密码输入框 -->
		<view class="input-group">
			<view class="group-left">
				<text>新密码</text>
				<input :type="showNewPassword ? 'text' : 'password'" v-model="newPassword" placeholder="请输入新密码"
					class="password-input" />
			</view>
			<image :src="showNewPassword ? '/static/image/user/open-eye.png' : '/static/image/user/close-eye.png'"
				class="eye-icon" @click="toggleNewPassword" />
		</view>
		<button class="btn" @click="handleUpdatePassword">确定</button>
		<!-- <view class="password-input" >
				 <password-strength /> 
			</view> -->

	</view>
</template>

<script>
	import Quene from '@/components/utils/queue'
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request'
	import Config from '@/components/utils/config'
	import {
		encrypt
	} from '@/components/utils/jsencrypt'
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				oldPassword: '', // 旧密码
				newPassword: '', // 新密码
				showOldPassword: false, // 控制旧密码显示/隐藏
				showNewPassword: false, // 控制新密码显示/隐藏
				userInfo: {},
				publicKey: Config.publicKey,
			}
		},
		async onShow() {
			this.userInfo = Quene.getData('userinfo');

			// console.log(this.userInfo);
		},
		methods: {
			// 切换旧密码显示/隐藏
			toggleOldPassword() {
				this.showOldPassword = !this.showOldPassword
			},
			// 切换新密码显示/隐藏
			toggleNewPassword() {
				this.showNewPassword = !this.showNewPassword
			},
			// 处理更新密码
			async handleUpdatePassword() {
				// 检查输入是否为空
				if (!this.oldPassword.trim()) {
					uni.showToast({
						title: '旧密码为空',
						icon: 'none',
						duration: 2000
					});
					return;
				}
				if (!this.newPassword.trim()) {
					uni.showToast({
						title: '新密码为空',
						icon: 'none',
						duration: 2000
					});
					return;
				}

				try {
					const res = await Request.post('/personal/post_modify', {
						oldPassword: encrypt(this.oldPassword, this.publicKey),
						newPassword: encrypt(this.newPassword, this.publicKey)
					});

					if (res.status === 0) {
						uni.showToast({
							title: '修改成功',
							icon: 'none',
							duration: 2000
						});
						
						// 使用事件通知更新密码
						uni.$emit('updateUserInfo', {
							password: this.newPassword
						});
						
						// 返回上一页
						uni.navigateBack();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('修改失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			}
		}
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style>
	.password-container {
		padding: 0 40rpx;
		padding-top: 156rpx;
		/* height: 100vh; */
	}

	.input-group {
		margin-top: 48rpx;
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		height: 120rpx;
		border-radius: 24rpx;
		padding: 0 32rpx;
		background: rgba(255, 255, 255, 0.13);
		backdrop-filter: blur(20rpx);
	}

	.group-left {

		font-family: Inter;
		font-size: 24rpx;
		font-weight: normal;
		letter-spacing: 0.4rpx;
		line-height: 34rpx;
		color: rgba(255, 255, 255, 0.45);


	}

	.password-input {
		flex: 1;
		height: 34rpx;
		color: rgba(255, 255, 255, 0.45);
	}

	.eye-icon {
		width: 48rpx;
		height: 48rpx;
	}

	.btn {
		margin-top: 92rpx;
		font-family: Inter;
		font-size: 40rpx;
		font-weight: 500;
		height: 96rpx;
		letter-spacing: 0.4rpx;
		color: #FFFFFF;
		border-radius: 16rpx;
		background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	}
</style>