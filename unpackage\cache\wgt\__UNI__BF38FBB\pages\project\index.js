"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var Me=Object.create;var _=Object.defineProperty,We=Object.defineProperties,Ve=Object.getOwnPropertyDescriptor,Xe=Object.getOwnPropertyDescriptors,Je=Object.getOwnPropertyNames,ge=Object.getOwnPropertySymbols,Ye=Object.getPrototypeOf,be=Object.prototype.hasOwnProperty,Ge=Object.prototype.propertyIsEnumerable;var me=(e,t,o)=>t in e?_(e,t,{enumerable:!0,configurable:!0,writable:!0,value:o}):e[t]=o,n=(e,t)=>{for(var o in t||(t={}))be.call(t,o)&&me(e,o,t[o]);if(ge)for(var o of ge(t))Ge.call(t,o)&&me(e,o,t[o]);return e},q=(e,t)=>We(e,Xe(t));var Ke=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var Ze=(e,t,o,i)=>{if(t&&typeof t=="object"||typeof t=="function")for(let r of Je(t))!be.call(e,r)&&r!==o&&_(e,r,{get:()=>t[r],enumerable:!(i=Ve(t,r))||i.enumerable});return e};var $=(e,t,o)=>(o=e!=null?Me(Ye(e)):{},Ze(t||!e||!e.__esModule?_(o,"default",{value:e,enumerable:!0}):o,e));var v=(e,t,o)=>new Promise((i,r)=>{var l=s=>{try{h(o.next(s))}catch(b){r(b)}},c=s=>{try{h(o.throw(s))}catch(b){r(b)}},h=s=>s.done?i(s.value):Promise.resolve(s.value).then(l,c);h((o=o.apply(e,t)).next())});var R=Ke((sr,Ae)=>{Ae.exports=Vue});var cr=$(R());function F(e){return weex.requireModule(e)}function y(e,t,...o){uni.__log__?uni.__log__(e,t,...o):console[e].apply(console,[...o,t])}function M(e,t){return typeof e=="string"?t:e}var g=$(R());var T=(e,t)=>{let o=e.__vccOpts||e;for(let[i,r]of t)o[i]=r;return o};var _e={"uicon-level":"\uE693","uicon-column-line":"\uE68E","uicon-checkbox-mark":"\uE807","uicon-folder":"\uE7F5","uicon-movie":"\uE7F6","uicon-star-fill":"\uE669","uicon-star":"\uE65F","uicon-phone-fill":"\uE64F","uicon-phone":"\uE622","uicon-apple-fill":"\uE881","uicon-chrome-circle-fill":"\uE885","uicon-backspace":"\uE67B","uicon-attach":"\uE632","uicon-cut":"\uE948","uicon-empty-car":"\uE602","uicon-empty-coupon":"\uE682","uicon-empty-address":"\uE646","uicon-empty-favor":"\uE67C","uicon-empty-permission":"\uE686","uicon-empty-news":"\uE687","uicon-empty-search":"\uE664","uicon-github-circle-fill":"\uE887","uicon-rmb":"\uE608","uicon-person-delete-fill":"\uE66A","uicon-reload":"\uE788","uicon-order":"\uE68F","uicon-server-man":"\uE6BC","uicon-search":"\uE62A","uicon-fingerprint":"\uE955","uicon-more-dot-fill":"\uE630","uicon-scan":"\uE662","uicon-share-square":"\uE60B","uicon-map":"\uE61D","uicon-map-fill":"\uE64E","uicon-tags":"\uE629","uicon-tags-fill":"\uE651","uicon-bookmark-fill":"\uE63B","uicon-bookmark":"\uE60A","uicon-eye":"\uE613","uicon-eye-fill":"\uE641","uicon-mic":"\uE64A","uicon-mic-off":"\uE649","uicon-calendar":"\uE66E","uicon-calendar-fill":"\uE634","uicon-trash":"\uE623","uicon-trash-fill":"\uE658","uicon-play-left":"\uE66D","uicon-play-right":"\uE610","uicon-minus":"\uE618","uicon-plus":"\uE62D","uicon-info":"\uE653","uicon-info-circle":"\uE7D2","uicon-info-circle-fill":"\uE64B","uicon-question":"\uE715","uicon-error":"\uE6D3","uicon-close":"\uE685","uicon-checkmark":"\uE6A8","uicon-android-circle-fill":"\uE67E","uicon-android-fill":"\uE67D","uicon-ie":"\uE87B","uicon-IE-circle-fill":"\uE889","uicon-google":"\uE87A","uicon-google-circle-fill":"\uE88A","uicon-setting-fill":"\uE872","uicon-setting":"\uE61F","uicon-minus-square-fill":"\uE855","uicon-plus-square-fill":"\uE856","uicon-heart":"\uE7DF","uicon-heart-fill":"\uE851","uicon-camera":"\uE7D7","uicon-camera-fill":"\uE870","uicon-more-circle":"\uE63E","uicon-more-circle-fill":"\uE645","uicon-chat":"\uE620","uicon-chat-fill":"\uE61E","uicon-bag-fill":"\uE617","uicon-bag":"\uE619","uicon-error-circle-fill":"\uE62C","uicon-error-circle":"\uE624","uicon-close-circle":"\uE63F","uicon-close-circle-fill":"\uE637","uicon-checkmark-circle":"\uE63D","uicon-checkmark-circle-fill":"\uE635","uicon-question-circle-fill":"\uE666","uicon-question-circle":"\uE625","uicon-share":"\uE631","uicon-share-fill":"\uE65E","uicon-shopping-cart":"\uE621","uicon-shopping-cart-fill":"\uE65D","uicon-bell":"\uE609","uicon-bell-fill":"\uE640","uicon-list":"\uE650","uicon-list-dot":"\uE616","uicon-zhihu":"\uE6BA","uicon-zhihu-circle-fill":"\uE709","uicon-zhifubao":"\uE6B9","uicon-zhifubao-circle-fill":"\uE6B8","uicon-weixin-circle-fill":"\uE6B1","uicon-weixin-fill":"\uE6B2","uicon-twitter-circle-fill":"\uE6AB","uicon-twitter":"\uE6AA","uicon-taobao-circle-fill":"\uE6A7","uicon-taobao":"\uE6A6","uicon-weibo-circle-fill":"\uE6A5","uicon-weibo":"\uE6A4","uicon-qq-fill":"\uE6A1","uicon-qq-circle-fill":"\uE6A0","uicon-moments-circel-fill":"\uE69A","uicon-moments":"\uE69B","uicon-qzone":"\uE695","uicon-qzone-circle-fill":"\uE696","uicon-baidu-circle-fill":"\uE680","uicon-baidu":"\uE681","uicon-facebook-circle-fill":"\uE68A","uicon-facebook":"\uE689","uicon-car":"\uE60C","uicon-car-fill":"\uE636","uicon-warning-fill":"\uE64D","uicon-warning":"\uE694","uicon-clock-fill":"\uE638","uicon-clock":"\uE60F","uicon-edit-pen":"\uE612","uicon-edit-pen-fill":"\uE66B","uicon-email":"\uE611","uicon-email-fill":"\uE642","uicon-minus-circle":"\uE61B","uicon-minus-circle-fill":"\uE652","uicon-plus-circle":"\uE62E","uicon-plus-circle-fill":"\uE661","uicon-file-text":"\uE663","uicon-file-text-fill":"\uE665","uicon-pushpin":"\uE7E3","uicon-pushpin-fill":"\uE86E","uicon-grid":"\uE673","uicon-grid-fill":"\uE678","uicon-play-circle":"\uE647","uicon-play-circle-fill":"\uE655","uicon-pause-circle-fill":"\uE654","uicon-pause":"\uE8FA","uicon-pause-circle":"\uE643","uicon-eye-off":"\uE648","uicon-eye-off-outline":"\uE62B","uicon-gift-fill":"\uE65C","uicon-gift":"\uE65B","uicon-rmb-circle-fill":"\uE657","uicon-rmb-circle":"\uE677","uicon-kefu-ermai":"\uE656","uicon-server-fill":"\uE751","uicon-coupon-fill":"\uE8C4","uicon-coupon":"\uE8AE","uicon-integral":"\uE704","uicon-integral-fill":"\uE703","uicon-home-fill":"\uE964","uicon-home":"\uE965","uicon-hourglass-half-fill":"\uE966","uicon-hourglass":"\uE967","uicon-account":"\uE628","uicon-plus-people-fill":"\uE626","uicon-minus-people-fill":"\uE615","uicon-account-fill":"\uE614","uicon-thumb-down-fill":"\uE726","uicon-thumb-down":"\uE727","uicon-thumb-up":"\uE733","uicon-thumb-up-fill":"\uE72F","uicon-lock-fill":"\uE979","uicon-lock-open":"\uE973","uicon-lock-opened-fill":"\uE974","uicon-lock":"\uE97A","uicon-red-packet-fill":"\uE690","uicon-photo-fill":"\uE98B","uicon-photo":"\uE98D","uicon-volume-off-fill":"\uE659","uicon-volume-off":"\uE644","uicon-volume-fill":"\uE670","uicon-volume":"\uE633","uicon-red-packet":"\uE691","uicon-download":"\uE63C","uicon-arrow-up-fill":"\uE6B0","uicon-arrow-down-fill":"\uE600","uicon-play-left-fill":"\uE675","uicon-play-right-fill":"\uE676","uicon-rewind-left-fill":"\uE679","uicon-rewind-right-fill":"\uE67A","uicon-arrow-downward":"\uE604","uicon-arrow-leftward":"\uE601","uicon-arrow-rightward":"\uE603","uicon-arrow-upward":"\uE607","uicon-arrow-down":"\uE60D","uicon-arrow-right":"\uE605","uicon-arrow-left":"\uE60E","uicon-arrow-up":"\uE606","uicon-skip-back-left":"\uE674","uicon-skip-forward-right":"\uE672","uicon-rewind-right":"\uE66F","uicon-rewind-left":"\uE671","uicon-arrow-right-double":"\uE68D","uicon-arrow-left-double":"\uE68C","uicon-wifi-off":"\uE668","uicon-wifi":"\uE667","uicon-empty-data":"\uE62F","uicon-empty-history":"\uE684","uicon-empty-list":"\uE68B","uicon-empty-page":"\uE627","uicon-empty-order":"\uE639","uicon-man":"\uE697","uicon-woman":"\uE69C","uicon-man-add":"\uE61C","uicon-man-add-fill":"\uE64C","uicon-man-delete":"\uE61A","uicon-man-delete-fill":"\uE66A","uicon-zh":"\uE70A","uicon-en":"\uE692"},P=e=>e,ye="3",I={v:ye,version:ye,type:["primary","success","info","error","warning"],color:{"u-primary":"#2979ff","u-warning":"#ff9900","u-success":"#19be6b","u-error":"#fa3534","u-info":"#909399","u-main-color":"#303133","u-content-color":"#606266","u-tips-color":"#909399","u-light-color":"#c0c4cc","up-primary":"#2979ff","up-warning":"#ff9900","up-success":"#19be6b","up-error":"#fa3534","up-info":"#909399","up-main-color":"#303133","up-content-color":"#606266","up-tips-color":"#909399","up-light-color":"#c0c4cc"},unit:"px",interceptor:{navbarLeftClick:null}},$e={toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},Qe={primary:"#3c9cff",info:"#909399",default:"#909399",warning:"#f9ae3d",error:"#f56c6c",success:"#5ac725",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},{toString:Ie}=Object.prototype;function Te(e){return Ie.call(e)==="[object Array]"}function et(e){return e!==null&&typeof e=="object"}function tt(e){return Ie.call(e)==="[object Date]"}function ot(e){return typeof URLSearchParams!="undefined"&&e instanceof URLSearchParams}function ee(e,t){if(!(e===null||typeof e=="undefined"))if(typeof e!="object"&&(e=[e]),Te(e))for(let o=0,i=e.length;o<i;o++)t.call(null,e[o],o,e);else for(let o in e)Object.prototype.hasOwnProperty.call(e,o)&&t.call(null,e[o],o,e)}function it(e){return Object.prototype.toString.call(e)==="[object Object]"}function te(){let e={};function t(o,i){typeof e[i]=="object"&&typeof o=="object"?e[i]=te(e[i],o):typeof o=="object"?e[i]=te({},o):e[i]=o}for(let o=0,i=arguments.length;o<i;o++)ee(arguments[o],t);return e}function S(e){return typeof e=="undefined"}function we(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function rt(e,t){if(!t)return e;let o;if(ot(t))o=t.toString();else{let i=[];ee(t,(r,l)=>{r===null||typeof r=="undefined"||(Te(r)?l=`${l}[]`:r=[r],ee(r,c=>{tt(c)?c=c.toISOString():et(c)&&(c=JSON.stringify(c)),i.push(`${we(l)}=${we(c)}`)}))}),o=i.join("&")}if(o){let i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}function nt(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function at(e,t){return t?`${e.replace(/\/+$/,"")}/${t.replace(/^\/+/,"")}`:e}function lt(e,t){return e&&!nt(t)?at(e,t):t}function st(e,t,o){let{validateStatus:i}=o.config,r=o.statusCode;r&&(!i||i(r))?e(o):t(o)}var xe=(e,t)=>{let o={};return e.forEach(i=>{S(t[i])||(o[i]=t[i])}),o},ct=e=>new Promise((t,o)=>{let i=rt(lt(e.baseURL,e.url),e.params),r={url:i,header:e.header,complete:c=>{e.fullPath=i,c.config=e;try{typeof c.data=="string"&&(c.data=JSON.parse(c.data))}catch(h){}st(t,o,c)}},l;if(e.method==="UPLOAD"){delete r.header["content-type"],delete r.header["Content-Type"];let c={filePath:e.filePath,name:e.name},h=["files","timeout","formData"];l=uni.uploadFile(n(n(n({},r),c),xe(h,e)))}else if(e.method==="DOWNLOAD")S(e.timeout)||(r.timeout=e.timeout),l=uni.downloadFile(r);else{let c=["data","method","timeout","dataType","responseType","sslVerify","firstIpv4"];l=uni.request(n(n({},r),xe(c,e)))}e.getTask&&e.getTask(l,e)}),ut=e=>ct(e);function L(){this.handlers=[]}L.prototype.use=function(t,o){return this.handlers.push({fulfilled:t,rejected:o}),this.handlers.length-1};L.prototype.eject=function(t){this.handlers[t]&&(this.handlers[t]=null)};L.prototype.forEach=function(t){this.handlers.forEach(o=>{o!==null&&t(o)})};var Be=(e,t,o)=>{let i={};return e.forEach(r=>{S(o[r])?S(t[r])||(i[r]=t[r]):i[r]=o[r]}),i},ft=(e,t={})=>{let o=t.method||e.method||"GET",i={baseURL:e.baseURL||"",method:o,url:t.url||"",params:t.params||{},custom:n(n({},e.custom||{}),t.custom||{}),header:te(e.header||{},t.header||{})},r=["getTask","validateStatus"];if(i=n(n({},i),Be(r,e,t)),o==="DOWNLOAD")S(t.timeout)?S(e.timeout)||(i.timeout=e.timeout):i.timeout=t.timeout;else if(o==="UPLOAD")delete i.header["content-type"],delete i.header["Content-Type"],["files","filePath","name","timeout","formData"].forEach(c=>{S(t[c])||(i[c]=t[c])}),S(i.timeout)&&!S(e.timeout)&&(i.timeout=e.timeout);else{let l=["data","timeout","dataType","responseType","sslVerify","firstIpv4"];i=n(n({},i),Be(l,e,t))}return i},dt={baseURL:"",header:{},method:"GET",dataType:"json",responseType:"text",custom:{},timeout:6e4,sslVerify:!0,firstIpv4:!1,validateStatus:function(t){return t>=200&&t<300}},pt=function(){function e(d,m){return m!=null&&d instanceof m}var t;try{t=Map}catch(d){t=function(){}}var o;try{o=Set}catch(d){o=function(){}}var i;try{i=Promise}catch(d){i=function(){}}function r(d,m,C,O,K){typeof m=="object"&&(C=m.depth,O=m.prototype,K=m.includeNonEnumerable,m=m.circular);var ue=[],fe=[],Ue=typeof Buffer!="undefined";typeof m=="undefined"&&(m=!0),typeof C=="undefined"&&(C=1/0);function x(f,B){if(f===null)return null;if(B===0)return f;var p,Z;if(typeof f!="object")return f;if(e(f,t))p=new t;else if(e(f,o))p=new o;else if(e(f,i))p=new i(function(E,j){f.then(function(z){E(x(z,B-1))},function(z){j(x(z,B-1))})});else if(r.__isArray(f))p=[];else if(r.__isRegExp(f))p=new RegExp(f.source,b(f)),f.lastIndex&&(p.lastIndex=f.lastIndex);else if(r.__isDate(f))p=new Date(f.getTime());else{if(Ue&&Buffer.isBuffer(f))return Buffer.from?p=Buffer.from(f):(p=new Buffer(f.length),f.copy(p)),p;e(f,Error)?p=Object.create(f):typeof O=="undefined"?(Z=Object.getPrototypeOf(f),p=Object.create(Z)):(p=Object.create(O),Z=O)}if(m){var de=ue.indexOf(f);if(de!=-1)return fe[de];ue.push(f),fe.push(p)}e(f,t)&&f.forEach(function(E,j){var z=x(j,B-1),Re=x(E,B-1);p.set(z,Re)}),e(f,o)&&f.forEach(function(E){var j=x(E,B-1);p.add(j)});for(var A in f){var He=Object.getOwnPropertyDescriptor(f,A);He&&(p[A]=x(f[A],B-1));try{var qe=Object.getOwnPropertyDescriptor(f,A);if(qe.set==="undefined")continue;p[A]=x(f[A],B-1)}catch(E){if(E instanceof TypeError)continue;if(E instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols)for(var pe=Object.getOwnPropertySymbols(f),A=0;A<pe.length;A++){var U=pe[A],Q=Object.getOwnPropertyDescriptor(f,U);Q&&!Q.enumerable&&!K||(p[U]=x(f[U],B-1),Object.defineProperty(p,U,Q))}if(K)for(var he=Object.getOwnPropertyNames(f),A=0;A<he.length;A++){var H=he[A],Q=Object.getOwnPropertyDescriptor(f,H);Q&&Q.enumerable||(p[H]=x(f[H],B-1),Object.defineProperty(p,H,Q))}return p}return x(d,C)}r.clonePrototype=function(m){if(m===null)return null;var C=function(){};return C.prototype=m,new C};function l(d){return Object.prototype.toString.call(d)}r.__objToStr=l;function c(d){return typeof d=="object"&&l(d)==="[object Date]"}r.__isDate=c;function h(d){return typeof d=="object"&&l(d)==="[object Array]"}r.__isArray=h;function s(d){return typeof d=="object"&&l(d)==="[object RegExp]"}r.__isRegExp=s;function b(d){var m="";return d.global&&(m+="g"),d.ignoreCase&&(m+="i"),d.multiline&&(m+="m"),m}return r.__getRegExpFlags=b,r}(),oe=class{constructor(t={}){it(t)||(t={},y("warn","at uni_modules/uview-plus/libs/luch-request/core/Request.js:40","\u8BBE\u7F6E\u5168\u5C40\u53C2\u6570\u5FC5\u987B\u63A5\u6536\u4E00\u4E2AObject")),this.config=pt(n(n({},dt),t)),this.interceptors={request:new L,response:new L}}setConfig(t){this.config=t(this.config)}middleware(t){t=ft(this.config,t);let o=[ut,void 0],i=Promise.resolve(t);for(this.interceptors.request.forEach(r=>{o.unshift(r.fulfilled,r.rejected)}),this.interceptors.response.forEach(r=>{o.push(r.fulfilled,r.rejected)});o.length;)i=i.then(o.shift(),o.shift());return i}request(t={}){return this.middleware(t)}get(t,o={}){return this.middleware(n({url:t,method:"GET"},o))}post(t,o,i={}){return this.middleware(n({url:t,data:o,method:"POST"},i))}put(t,o,i={}){return this.middleware(n({url:t,data:o,method:"PUT"},i))}delete(t,o,i={}){return this.middleware(n({url:t,data:o,method:"DELETE"},i))}options(t,o,i={}){return this.middleware(n({url:t,data:o,method:"OPTIONS"},i))}upload(t,o={}){return o.url=t,o.method="UPLOAD",this.middleware(o)}download(t,o={}){return o.url=t,o.method="DOWNLOAD",this.middleware(o)}},ht=new oe;function gt(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)}function mt(e){return/^1[23456789]\d{9}$/.test(e)}function bt(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)}function At(e){if(!e)return!1;if(typeof e=="number")return e.toString().length!==10&&e.toString().length!==13?!1:!isNaN(new Date(e).getTime());if(typeof e=="string"){let t=Number(e);if(!isNaN(t)&&(t.toString().length===10||t.toString().length===13))return!isNaN(new Date(t).getTime());if(e.length<10||e.length>19||!/^\d{4}[-\/]\d{2}[-\/]\d{2}( \d{1,2}:\d{2}(:\d{2})?)?$/.test(e))return!1;let i=new Date(e);return!isNaN(i.getTime())}return!1}function yt(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)}function V(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function wt(e){return typeof e=="string"}function xt(e){return/^\d+$/.test(e)}function Bt(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)}function vt(e){let t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,o=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return e.length===7?o.test(e):e.length===8?t.test(e):!1}function St(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)}function Et(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)}function Ct(e){return/^[a-zA-Z]*$/.test(e)}function Qt(e){return/^[0-9a-zA-Z]*$/g.test(e)}function It(e,t){return e.indexOf(t)>=0}function Tt(e,t){return e>=t[0]&&e<=t[1]}function Pt(e,t){return e.length>=t[0]&&e.length<=t[1]}function kt(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)}function ie(e){switch(typeof e){case"undefined":return!0;case"string":if(e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length==0)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(e===0||isNaN(e))return!0;break;case"object":if(e===null||e.length===0)return!0;for(let t in e)return!1;return!0}return!1}function jt(e){if(typeof e=="string")try{let t=JSON.parse(e);return!!(typeof t=="object"&&t)}catch(t){return!1}return!1}function Pe(e){return typeof Array.isArray=="function"?Array.isArray(e):Object.prototype.toString.call(e)==="[object Array]"}function ke(e){return Object.prototype.toString.call(e)==="[object Object]"}function zt(e,t=6){return new RegExp(`^\\d{${t}}$`).test(e)}function re(e){return typeof e=="function"}function Ft(e){return ke(e)&&re(e.then)&&re(e.catch)}function Nt(e){let t=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(t)}function Lt(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}function Dt(e){return e&&Object.prototype.toString.call(e)==="[object RegExp]"}var Ot={email:gt,mobile:mt,url:bt,date:At,dateISO:yt,number:V,digits:xt,idCard:Bt,carNo:vt,amount:St,chinese:Et,letter:Ct,enOrNum:Qt,contains:It,range:Tt,rangeLength:Pt,empty:ie,isEmpty:ie,jsonString:jt,landline:kt,object:ke,array:Pe,code:zt,func:re,promise:Ft,video:Lt,image:Nt,regExp:Dt,string:wt};function je(e,t=!1){return V(e)?t?`${e}px`:Number(e):/(rpx|upx)$/.test(e)?t?`${uni.upx2px(parseInt(e))}px`:Number(uni.upx2px(parseInt(e))):t?`${parseInt(e)}px`:parseInt(e)}function ae(e=30){return new Promise(t=>{setTimeout(()=>{t()},e)})}function ze(){let e={};return e=uni.getWindowInfo(),e}function Ut(e=void 0){let t=this.$parent;for(;t;)if(e=e.replace(/up-([a-zA-Z0-9-_]+)/g,"u-$1"),t.$options&&t.$options.name!==e)t=t.$parent;else return t;return!1}function k(e,t="object"){if(ie(e)||typeof e=="object"&&t==="object"||t==="string"&&typeof e=="string")return e;if(t==="object"){e=W(e);let i=e.split(";"),r={};for(let l=0;l<i.length;l++)if(i[l]){let c=i[l].split(":");r[W(c[0])]=W(c[1])}return r}let o="";return typeof e=="object"&&e.forEach((i,r)=>{let l=r.replace(/([A-Z])/g,"-$1").toLowerCase();o+=`${l}:${i};`}),W(o)}function w(e="auto",t=""){return t||(t=I.unit||"px"),t=="rpx"&&V(String(e))&&(e=e*2),e=String(e),V(e)?`${e}${t}`:e}function Fe(e){if([null,void 0,NaN,!1].includes(e)||typeof e!="object"&&typeof e!="function")return e;let t=Pe(e)?[]:{};for(let o in e)e.hasOwnProperty(o)&&(t[o]=typeof e[o]=="object"?Fe(e[o]):e[o]);return t}function D(e={},t={}){let o=Fe(e);if(typeof o!="object"||typeof t!="object")return!1;for(let i in t)t.hasOwnProperty(i)&&(i in o?t[i]==null||typeof o[i]!="object"||typeof t[i]!="object"?o[i]=t[i]:o[i].concat&&t[i].concat?o[i]=o[i].concat(t[i]):o[i]=D(o[i],t[i]):o[i]=t[i]);return o}function N(e,t={}){if(typeof e!="object"||typeof t!="object")return!1;for(let o in t)t.hasOwnProperty(o)&&(o in e?t[o]==null||typeof e[o]!="object"||typeof t[o]!="object"?e[o]=t[o]:e[o].concat&&t[o].concat?e[o]=e[o].concat(t[o]):e[o]=N(e[o],t[o]):e[o]=t[o]);return e}String.prototype.padStart||(String.prototype.padStart=function(e,t=" "){if(Object.prototype.toString.call(t)!=="[object String]")throw new TypeError("fillString must be String");let o=this;if(o.length>=e)return String(o);let i=e-o.length,r=Math.ceil(i/t.length);for(;r>>=1;)t+=t,r===1&&(t+=t);return t.slice(0,i)+o});function W(e,t="both"){return e=String(e),t=="both"?e.replace(/^\s+|\s+$/g,""):t=="left"?e.replace(/^\s*/,""):t=="right"?e.replace(/(\s*$)/g,""):t=="all"?e.replace(/\s+/g,""):e}function ve(e={},t=!0,o="brackets"){let i=t?"?":"",r=[];["indices","brackets","repeat","comma"].indexOf(o)==-1&&(o="brackets");for(let l in e){let c=e[l];if(!(["",void 0,null].indexOf(c)>=0))if(c.constructor===Array)switch(o){case"indices":for(let s=0;s<c.length;s++)r.push(`${l}[${s}]=${c[s]}`);break;case"brackets":c.forEach(s=>{r.push(`${l}[]=${s}`)});break;case"repeat":c.forEach(s=>{r.push(`${l}=${s}`)});break;case"comma":let h="";c.forEach(s=>{h+=(h?",":"")+s}),r.push(`${l}=${h}`);break;default:c.forEach(s=>{r.push(`${l}[]=${s}`)})}else r.push(`${l}=${c}`)}return r.length?i+r.join("&"):""}function Ht(){let e=getCurrentPages();return`/${e[e.length-1].route||""}`}var qt={actionSheet:{show:!1,title:"",description:"",actions:[],index:"",cancelText:"",closeOnClickAction:!0,safeAreaInsetBottom:!0,openType:"",closeOnClickOverlay:!0,round:0,wrapMaxHeight:"600px"}},Rt={album:{urls:[],keyName:"",singleSize:180,multipleSize:70,space:6,singleMode:"scaleToFill",multipleMode:"aspectFill",maxCount:9,previewFullImage:!0,rowCount:3,showMore:!0,autoWrap:!1,unit:"px",stop:!0}},Mt={alert:{title:"",type:"warning",description:"",closable:!1,showIcon:!1,effect:"light",center:!1,fontSize:14}},Wt={avatar:{src:"",shape:"circle",size:40,mode:"scaleToFill",text:"",bgColor:"#c0c4cc",color:"#ffffff",fontSize:18,icon:"",mpAvatar:!1,randomBgColor:!1,defaultUrl:"",colorIndex:"",name:""}},Vt={avatarGroup:{urls:[],maxCount:5,shape:"circle",mode:"scaleToFill",showMore:!0,size:40,keyName:"",gap:.5,extraValue:0}},Xt={backtop:{mode:"circle",icon:"arrow-upward",text:"",duration:100,scrollTop:0,top:400,bottom:100,right:20,zIndex:9,iconStyle:{color:"#909399",fontSize:"19px"}}},Jt={badge:{isDot:!1,value:"",show:!0,max:999,type:"error",showZero:!1,bgColor:null,color:null,shape:"circle",numberType:"overflow",offset:[],inverted:!1,absolute:!1}},Yt={button:{hairline:!1,type:"info",size:"normal",shape:"square",plain:!1,disabled:!1,loading:!1,loadingText:"",loadingMode:"spinner",loadingSize:15,openType:"",formType:"",appParameter:"",hoverStopPropagation:!0,lang:"en",sessionFrom:"",sendMessageTitle:"",sendMessagePath:"",sendMessageImg:"",showMessageCard:!1,dataName:"",throttleTime:0,hoverStartTime:0,hoverStayTime:200,text:"",icon:"",iconColor:"",color:"",stop:!0}},Gt={calendar:{title:"\u65E5\u671F\u9009\u62E9",showTitle:!0,showSubtitle:!0,mode:"single",startText:"\u5F00\u59CB",endText:"\u7ED3\u675F",customList:[],color:"#3c9cff",minDate:0,maxDate:0,defaultDate:null,maxCount:Number.MAX_SAFE_INTEGER,rowHeight:56,formatter:null,showLunar:!1,showMark:!0,confirmText:"\u786E\u5B9A",confirmDisabledText:"\u786E\u5B9A",show:!1,closeOnClickOverlay:!1,readonly:!1,showConfirm:!0,maxRange:Number.MAX_SAFE_INTEGER,rangePrompt:"",showRangePrompt:!0,allowSameDay:!1,round:0,monthNum:3,weekText:["\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D","\u65E5"],forbidDays:[],forbidDaysToast:"\u8BE5\u65E5\u671F\u5DF2\u7981\u7528"}},Kt={carKeyboard:{random:!1}},Zt={cell:{customClass:"",title:"",label:"",value:"",icon:"",disabled:!1,border:!0,center:!1,url:"",linkType:"navigateTo",clickable:!1,isLink:!1,required:!1,arrowDirection:"",iconStyle:{},rightIconStyle:{},rightIcon:"arrow-right",titleStyle:{},size:"",stop:!0,name:""}},_t={cellGroup:{title:"",border:!0,customStyle:{}}},$t={checkbox:{name:"",shape:"",size:"",checkbox:!1,disabled:"",activeColor:"",inactiveColor:"",iconSize:"",iconColor:"",label:"",labelSize:"",labelColor:"",labelDisabled:""}},eo={checkboxGroup:{name:"",value:[],shape:"square",disabled:!1,activeColor:"#2979ff",inactiveColor:"#c8c9cc",size:18,placement:"row",labelSize:14,labelColor:"#303133",labelDisabled:!1,iconColor:"#ffffff",iconSize:12,iconPlacement:"left",borderBottom:!1}},to={circleProgress:{percentage:30}},oo={code:{seconds:60,startText:"\u83B7\u53D6\u9A8C\u8BC1\u7801",changeText:"X\u79D2\u91CD\u65B0\u83B7\u53D6",endText:"\u91CD\u65B0\u83B7\u53D6",keepRunning:!1,uniqueKey:""}},io={codeInput:{adjustPosition:!0,maxlength:6,dot:!1,mode:"box",hairline:!1,space:10,value:"",focus:!1,bold:!1,color:"#606266",fontSize:18,size:35,disabledKeyboard:!1,borderColor:"#c9cacc",disabledDot:!0}},ro={col:{span:12,offset:0,justify:"start",align:"stretch",textAlign:"left"}},no={collapse:{value:null,accordion:!1,border:!0}},ao={collapseItem:{title:"",value:"",label:"",disabled:!1,isLink:!0,clickable:!0,border:!0,align:"left",name:"",icon:"",duration:300,showRight:!0,titleStyle:{},iconStyle:{},rightIconStyle:{},cellCustomStyle:{},cellCustomClass:""}},lo={columnNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80,step:!1,duration:1500,disableTouch:!0,justifyContent:"flex-start"}},so={countDown:{time:0,format:"HH:mm:ss",autoStart:!0,millisecond:!1}},co={countTo:{startVal:0,endVal:0,duration:2e3,autoplay:!0,decimals:0,useEasing:!0,decimal:".",color:"#606266",fontSize:22,bold:!1,separator:""}},uo={datetimePicker:{show:!1,popupMode:"bottom",showToolbar:!0,value:"",title:"",mode:"datetime",maxDate:new Date(new Date().getFullYear()+10,0,1).getTime(),minDate:new Date(new Date().getFullYear()-10,0,1).getTime(),minHour:0,maxHour:23,minMinute:0,maxMinute:59,filter:null,formatter:null,loading:!1,itemHeight:44,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u8BA4",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,closeOnClickOverlay:!1,defaultIndex:[]}},fo={divider:{dashed:!1,hairline:!0,dot:!1,textPosition:"center",text:"",textSize:14,textColor:"#909399",lineColor:"#dcdfe6"}},po={empty:{icon:"",text:"",textColor:"#c0c4cc",textSize:14,iconColor:"#c0c4cc",iconSize:90,mode:"data",width:160,height:160,show:!0,marginTop:0}},ho={form:{model:{},rules:{},errorType:"message",borderBottom:!0,labelPosition:"left",labelWidth:45,labelAlign:"left",labelStyle:{}}},go={formItem:{label:"",prop:"",rules:[],borderBottom:"",labelPosition:"",labelWidth:"",rightIcon:"",leftIcon:"",required:!1,leftIconStyle:""}},mo={gap:{bgColor:"transparent",height:20,marginTop:0,marginBottom:0,customStyle:{}}},bo={grid:{col:3,border:!1,align:"left"}},Ao={gridItem:{name:null,bgColor:"transparent"}},{color:Se}=I,yo={icon:{name:"",color:Se["u-content-color"],size:"16px",bold:!1,index:"",hoverClass:"",customPrefix:"uicon",label:"",labelPos:"right",labelSize:"15px",labelColor:Se["u-content-color"],space:"3px",imgMode:"",width:"",height:"",top:0,stop:!1}},wo={image:{src:"",mode:"aspectFill",width:"300",height:"225",shape:"square",radius:0,lazyLoad:!0,showMenuByLongpress:!0,loadingIcon:"photo",errorIcon:"error-circle",showLoading:!0,showError:!0,fade:!0,webp:!1,duration:500,bgColor:"#f3f4f6"}},xo={indexAnchor:{text:"",color:"#606266",size:14,bgColor:"#dedede",height:32}},Bo={indexList:{inactiveColor:"#606266",activeColor:"#5677fc",indexList:[],sticky:!0,customNavHeight:0,safeBottomFix:!1}},vo={input:{value:"",type:"text",fixed:!1,disabled:!1,disabledColor:"#f5f7fa",clearable:!1,password:!1,maxlength:140,placeholder:null,placeholderClass:"input-placeholder",placeholderStyle:"color: #c0c4cc",showWordLimit:!1,confirmType:"done",confirmHold:!1,holdKeyboard:!1,focus:!1,autoBlur:!1,disableDefaultPadding:!1,cursor:-1,cursorSpacing:30,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,inputAlign:"left",fontSize:"15px",color:"#303133",prefixIcon:"",prefixIconStyle:"",suffixIcon:"",suffixIconStyle:"",border:"surround",readonly:!1,shape:"square",formatter:null}},So={keyboard:{mode:"number",dotDisabled:!1,tooltip:!0,showTips:!0,tips:"",showCancel:!0,showConfirm:!0,random:!1,safeAreaInsetBottom:!0,closeOnClickOverlay:!0,show:!1,overlay:!0,zIndex:10075,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u5B9A",autoChange:!1}},Eo={line:{color:"#d6d7d9",length:"100%",direction:"row",hairline:!0,margin:0,dashed:!1}},Co={lineProgress:{activeColor:"#19be6b",inactiveColor:"#ececec",percentage:0,showText:!0,height:12}},{color:Qo}=I,Io={link:{color:Qo["u-primary"],fontSize:15,underLine:!1,href:"",mpTips:"\u94FE\u63A5\u5DF2\u590D\u5236\uFF0C\u8BF7\u5728\u6D4F\u89C8\u5668\u6253\u5F00",lineColor:"",text:""}},To={list:{showScrollbar:!1,lowerThreshold:50,upperThreshold:0,scrollTop:0,offsetAccuracy:10,enableFlex:!1,pagingEnabled:!1,scrollable:!0,scrollIntoView:"",scrollWithAnimation:!1,enableBackToTop:!1,height:0,width:0,preLoadScreen:1}},Po={listItem:{anchor:""}},{color:Ee}=I,ko={loadingIcon:{show:!0,color:Ee["u-tips-color"],textColor:Ee["u-tips-color"],vertical:!1,mode:"spinner",size:24,textSize:15,text:"",timingFunction:"ease-in-out",duration:1200,inactiveColor:""}},jo={loadingPage:{loadingText:"\u6B63\u5728\u52A0\u8F7D",image:"",loadingMode:"circle",loading:!1,bgColor:"#ffffff",color:"#C8C8C8",fontSize:19,iconSize:28,loadingColor:"#C8C8C8",zIndex:10}},zo={loadmore:{status:"loadmore",bgColor:"transparent",icon:!0,fontSize:14,iconSize:17,color:"#606266",loadingIcon:"spinner",loadmoreText:"\u52A0\u8F7D\u66F4\u591A",loadingText:"\u6B63\u5728\u52A0\u8F7D...",nomoreText:"\u6CA1\u6709\u66F4\u591A\u4E86",isDot:!1,iconColor:"#b7b7b7",marginTop:10,marginBottom:10,height:"auto",line:!1,lineColor:"#E6E8EB",dashed:!1}},Fo={modal:{show:!1,title:"",content:"",confirmText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",showConfirmButton:!0,showCancelButton:!1,confirmColor:"#2979ff",cancelColor:"#606266",buttonReverse:!1,zoom:!0,asyncClose:!1,closeOnClickOverlay:!1,negativeTop:0,width:"650rpx",confirmButtonShape:"",contentTextAlign:"left",asyncCloseTip:"\u64CD\u4F5C\u4E2D...",asyncCancelClose:!1}},No={navbar:{safeAreaInsetTop:!0,placeholder:!1,fixed:!0,border:!1,leftIcon:"arrow-left",leftText:"",rightText:"",rightIcon:"",title:"",titleColor:"",bgColor:"#ffffff",titleWidth:"400rpx",height:"44px",leftIconSize:20,leftIconColor:Qe.mainColor,autoBack:!1,titleStyle:""}},Lo={noNetwork:{tips:"\u54CE\u5440\uFF0C\u7F51\u7EDC\u4FE1\u53F7\u4E22\u5931",zIndex:"",image:"data:image/png;base64,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"}},Do={noticeBar:{text:[],direction:"row",step:!1,icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",speed:80,fontSize:14,duration:2e3,disableTouch:!0,url:"",linkType:"navigateTo",justifyContent:"flex-start"}},Oo={notify:{top:0,type:"primary",color:"#ffffff",bgColor:"",message:"",duration:3e3,fontSize:15,safeAreaInsetTop:!1}},Uo={numberBox:{name:"",value:0,min:1,max:Number.MAX_SAFE_INTEGER,step:1,integer:!1,disabled:!1,disabledInput:!1,asyncChange:!1,inputWidth:35,showMinus:!0,showPlus:!0,decimalLength:null,longPress:!0,color:"#323233",buttonWidth:30,buttonSize:30,buttonRadius:"0px",bgColor:"#EBECEE",inputBgColor:"#EBECEE",cursorSpacing:100,disableMinus:!1,disablePlus:!1,iconStyle:"",miniMode:!1}},Ho={numberKeyboard:{mode:"number",dotDisabled:!1,random:!1}},qo={overlay:{show:!1,zIndex:10070,duration:300,opacity:.5}},Ro={parse:{copyLink:!0,errorImg:"",lazyLoad:!1,loadingImg:"",pauseVideo:!0,previewImg:!0,setTitle:!0,showImgMenu:!0}},Mo={picker:{show:!1,popupMode:"bottom",showToolbar:!0,title:"",columns:[],loading:!1,itemHeight:44,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u5B9A",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,keyName:"text",closeOnClickOverlay:!1,defaultIndex:[],immediateChange:!0,zIndex:10076}},Wo={popup:{show:!1,overlay:!0,mode:"bottom",duration:300,closeable:!1,overlayStyle:{},closeOnClickOverlay:!0,zIndex:10075,safeAreaInsetBottom:!0,safeAreaInsetTop:!1,closeIconPos:"top-right",round:0,zoom:!0,bgColor:"",overlayOpacity:.5}},Vo={radio:{name:"",shape:"",disabled:"",labelDisabled:"",activeColor:"",inactiveColor:"",iconSize:"",labelSize:"",label:"",labelColor:"",size:"",iconColor:"",placement:""}},Xo={radioGroup:{value:"",disabled:!1,shape:"circle",activeColor:"#2979ff",inactiveColor:"#c8c9cc",name:"",size:18,placement:"row",label:"",labelColor:"#303133",labelSize:14,labelDisabled:!1,iconColor:"#ffffff",iconSize:12,borderBottom:!1,iconPlacement:"left",gap:"10px"}},Jo={rate:{value:1,count:5,disabled:!1,size:18,inactiveColor:"#b2b2b2",activeColor:"#FA3534",gutter:4,minCount:1,allowHalf:!1,activeIcon:"star-fill",inactiveIcon:"star",touchable:!0}},Yo={readMore:{showHeight:400,toggle:!1,closeText:"\u5C55\u5F00\u9605\u8BFB\u5168\u6587",openText:"\u6536\u8D77",color:"#2979ff",fontSize:14,textIndent:"2em",name:""}},Go={row:{gutter:0,justify:"start",align:"center"}},Ko={rowNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80}},Zo={scrollList:{indicatorWidth:50,indicatorBarWidth:20,indicator:!0,indicatorColor:"#f2f2f2",indicatorActiveColor:"#3c9cff",indicatorStyle:""}},_o={search:{shape:"round",bgColor:"#f2f2f2",placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57",clearabled:!0,focus:!1,showAction:!0,actionStyle:{},actionText:"\u641C\u7D22",inputAlign:"left",inputStyle:{},disabled:!1,borderColor:"transparent",searchIconColor:"#909399",searchIconSize:22,color:"#606266",placeholderColor:"#909399",searchIcon:"search",margin:"0",animation:!1,value:"",maxlength:"-1",height:32,label:null}},$o={section:{title:"",subTitle:"\u66F4\u591A",right:!0,fontSize:15,bold:!0,color:"#303133",subColor:"#909399",showLine:!0,lineColor:"",arrow:!0}},ei={skeleton:{loading:!0,animate:!0,rows:0,rowsWidth:"100%",rowsHeight:18,title:!0,titleWidth:"50%",titleHeight:18,avatar:!1,avatarSize:32,avatarShape:"circle"}},ti={slider:{value:0,blockSize:18,min:0,max:100,step:1,activeColor:"#2979ff",inactiveColor:"#c0c4cc",blockColor:"#ffffff",showValue:!1,disabled:!1,blockStyle:{},useNative:!1,height:"2px"}},oi={statusBar:{bgColor:"transparent"}},ii={steps:{direction:"row",current:0,activeColor:"#3c9cff",inactiveColor:"#969799",activeIcon:"",inactiveIcon:"",dot:!1}},ri={stepsItem:{title:"",desc:"",iconSize:17,error:!1}},ni={sticky:{offsetTop:0,customNavHeight:0,disabled:!1,bgColor:"transparent",zIndex:"",index:""}},ai={subsection:{list:[],current:0,activeColor:"#3c9cff",inactiveColor:"#303133",mode:"button",fontSize:12,bold:!0,bgColor:"#eeeeef",keyName:"name"}},li={swipeAction:{autoClose:!0}},si={swipeActionItem:{show:!1,closeOnClick:!0,name:"",disabled:!1,threshold:20,autoClose:!0,options:[],duration:300}},ci={swiper:{list:[],indicator:!1,indicatorActiveColor:"#FFFFFF",indicatorInactiveColor:"rgba(255, 255, 255, 0.35)",indicatorStyle:"",indicatorMode:"line",autoplay:!0,current:0,currentItemId:"",interval:3e3,duration:300,circular:!1,previousMargin:0,nextMargin:0,acceleration:!1,displayMultipleItems:1,easingFunction:"default",keyName:"url",imgMode:"aspectFill",height:130,bgColor:"#f3f4f6",radius:4,loading:!1,showTitle:!1}},ui={swiperIndicator:{length:0,current:0,indicatorActiveColor:"",indicatorInactiveColor:"",indicatorMode:"line"}},fi={switch:{loading:!1,disabled:!1,size:25,activeColor:"#2979ff",inactiveColor:"#ffffff",value:!1,activeValue:!0,inactiveValue:!1,asyncChange:!1,space:0}},di={tabbar:{value:null,safeAreaInsetBottom:!0,border:!0,zIndex:1,activeColor:"#1989fa",inactiveColor:"#7d7e80",fixed:!0,placeholder:!0}},pi={tabbarItem:{name:null,icon:"",badge:null,dot:!1,text:"",badgeStyle:"top: 6px;right:2px;"}},hi={tabs:{duration:300,list:[],lineColor:"#3c9cff",activeStyle:{color:"#303133"},inactiveStyle:{color:"#606266"},lineWidth:20,lineHeight:3,lineBgSize:"cover",itemStyle:{height:"44px"},scrollable:!0,current:0,keyName:"name",iconStyle:{}}},gi={tag:{type:"primary",disabled:!1,size:"medium",shape:"square",text:"",bgColor:"",color:"",borderColor:"",closeColor:"#C6C7CB",name:"",plainFill:!1,plain:!1,closable:!1,show:!0,icon:"",iconColor:""}},mi={text:{type:"",show:!0,text:"",prefixIcon:"",suffixIcon:"",mode:"",href:"",format:"",call:!1,openType:"",bold:!1,block:!1,lines:"",color:"#303133",size:15,iconStyle:{fontSize:"15px"},decoration:"none",margin:0,lineHeight:"",align:"left",wordWrap:"normal",flex1:!0}},bi={textarea:{value:"",placeholder:"",placeholderClass:"textarea-placeholder",placeholderStyle:"color: #c0c4cc",height:70,confirmType:"done",disabled:!1,count:!1,focus:!1,autoHeight:!1,fixed:!1,cursorSpacing:0,cursor:"",showConfirmBar:!0,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,disableDefaultPadding:!1,holdKeyboard:!1,maxlength:140,border:"surround",formatter:null}},Ai={toast:{zIndex:10090,loading:!1,message:"",icon:"",type:"",loadingMode:"",show:"",overlay:!1,position:"center",params:{},duration:2e3,isTab:!1,url:"",callback:null,back:!1}},yi={toolbar:{show:!0,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u8BA4",cancelColor:"#909193",confirmColor:"#3c9cff",title:""}},wi={tooltip:{text:"",copyText:"",size:14,color:"#606266",bgColor:"transparent",direction:"top",zIndex:10071,showCopy:!0,buttons:[],overlay:!0,showToast:!0}},xi={transition:{show:!1,mode:"fade",duration:"300",timingFunction:"ease-out"}},Bi={upload:{accept:"image",extension:[],capture:["album","camera"],compressed:!0,camera:"back",maxDuration:60,uploadIcon:"camera-fill",uploadIconColor:"#D3D4D6",useBeforeRead:!1,previewFullImage:!0,maxCount:52,disabled:!1,imageMode:"aspectFill",name:"",sizeType:["original","compressed"],multiple:!1,deletable:!0,maxSize:Number.MAX_VALUE,fileList:[],uploadText:"",width:80,height:80,previewImage:!0}},u=n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n(n({},qt),Rt),Mt),Wt),Vt),Xt),Jt),Yt),Gt),Kt),Zt),_t),$t),eo),to),oo),io),ro),no),ao),lo),so),co),uo),fo),po),ho),go),mo),bo),Ao),yo),wo),xo),Bo),vo),So),Eo),Co),Io),To),Po),ko),jo),zo),Fo),No),Lo),Do),Oo),Uo),Ho),qo),Ro),Mo),Wo),Vo),Xo),Jo),Yo),Go),Ko),Zo),_o),$o),ei),ti),oi),ii),ri),ni),ai),li),si),ci),ui),fi),di),pi),hi),gi),mi),bi),Ai),yi),wi),xi),Bi);function vi(e){N(I,e.config||{}),N(u,e.props||{}),N(Qe,e.color||{}),N($e,e.zIndex||{})}if(uni&&uni.upuiParams){y("log","at uni_modules/uview-plus/libs/config/props.js:204","setting uview-plus");let e=uni.upuiParams();e.httpIns&&e.httpIns(ht),e.options&&vi(e.options)}var Si=P({props:{name:{type:String,default:()=>u.icon.name},color:{type:String,default:()=>u.icon.color},size:{type:[String,Number],default:()=>u.icon.size},bold:{type:Boolean,default:()=>u.icon.bold},index:{type:[String,Number],default:()=>u.icon.index},hoverClass:{type:String,default:()=>u.icon.hoverClass},customPrefix:{type:String,default:()=>u.icon.customPrefix},label:{type:[String,Number],default:()=>u.icon.label},labelPos:{type:String,default:()=>u.icon.labelPos},labelSize:{type:[String,Number],default:()=>u.icon.labelSize},labelColor:{type:String,default:()=>u.icon.labelColor},space:{type:[String,Number],default:()=>u.icon.space},imgMode:{type:String,default:()=>u.icon.imgMode},width:{type:[String,Number],default:()=>u.icon.width},height:{type:[String,Number],default:()=>u.icon.height},top:{type:[String,Number],default:()=>u.icon.top},stop:{type:Boolean,default:()=>u.icon.stop}}}),X=P({}),ne=class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(t){return t[0]==="/"?t:`/${t}`}mixinParam(t,o){t=t&&this.addRootPath(t);let i="";return/.*\/.*\?.*=.*/.test(t)?(i=ve(o,!1),t+=`&${i}`):(i=ve(o),t+=i)}route(){return v(this,arguments,function*(t={},o={}){let i={};typeof t=="string"?(i.url=this.mixinParam(t,o),i.type="navigateTo"):(i=D(this.config,t),i.url=this.mixinParam(t.url,t.params)),i.url!==Ht()&&(o.intercept&&(this.config.intercept=o.intercept),i.params=o,i=D(this.config,i),typeof uni.$u.routeIntercept=="function"?(yield new Promise((l,c)=>{uni.$u.routeIntercept(i,l)}))&&this.openPage(i):this.openPage(i))})}openPage(t){let{url:o,type:i,delta:r,animationType:l,animationDuration:c}=t;(t.type=="navigateTo"||t.type=="to")&&uni.navigateTo({url:o,animationType:l,animationDuration:c}),(t.type=="redirectTo"||t.type=="redirect")&&uni.redirectTo({url:o}),(t.type=="switchTab"||t.type=="tab")&&uni.switchTab({url:o}),(t.type=="reLaunch"||t.type=="launch")&&uni.reLaunch({url:o}),(t.type=="navigateBack"||t.type=="back")&&uni.navigateBack({delta:r})}},Ce=new ne().route,Ei=F("dom"),J=P({props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data(){return{}},onLoad(){this.$u.getRect=this.$uGetRect},created(){this.$u.getRect=this.$uGetRect},computed:{$u(){return uni.$u},bem(){return function(e,t,o){let i=`u-${e}--`,r={};return t&&t.map(l=>{r[i+this[l]]=!0}),o&&o.map(l=>{this[l]?r[i+l]=this[l]:delete r[i+l]}),Object.keys(r)}}},methods:{openPage(e="url"){let t=this[e];t&&Ce({type:this.linkType,url:t})},navTo(e="",t="navigateTo"){Ce({type:this.linkType,url:e})},$uGetRect(e,t){return new Promise(o=>{ae(30).then(()=>{let i=e.substring(1),r=this.$refs[i];r||o({with:0,height:0,left:0,right:0,top:0,bottom:0}),Ei.getComponentRect(r,l=>{o(l.size)})})})},getParentData(e=""){this.parent||(this.parent={}),this.parent=Ut.call(this,e),this.parent.children&&this.parent.children.indexOf(this)===-1&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map(t=>{this.parentData[t]=this.parent[t]})},preventEvent(e){e&&typeof e.stopPropagation=="function"&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){uni.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&Ot.array(this.parent.children)){let e=this.parent.children;e.map((t,o)=>{t===this&&e.splice(o,1)})}}}),Ci={"u-icon":{"":{alignItems:"center"}},"u-icon--left":{"":{flexDirection:"row-reverse",alignItems:"center"}},"u-icon--right":{"":{flexDirection:"row",alignItems:"center"}},"u-icon--top":{"":{flexDirection:"column-reverse",justifyContent:"center"}},"u-icon--bottom":{"":{flexDirection:"column",justifyContent:"center"}},"u-icon__icon":{"":{fontFamily:"uicon-iconfont",position:"relative",flexDirection:"row",alignItems:"center"}},"u-icon__icon--primary":{"":{color:"#3c9cff"}},"u-icon__icon--success":{"":{color:"#5ac725"}},"u-icon__icon--error":{"":{color:"#f56c6c"}},"u-icon__icon--warning":{"":{color:"#f9ae3d"}},"u-icon__icon--info":{"":{color:"#909399"}}},Qi="https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf",Ne=weex.requireModule("dom");Ne.addRule("fontFace",{fontFamily:"uicon-iconfont",src:`url('${Qi}')`});var Ii={name:"u-icon",beforeCreate(){this.customFontFamily&&Ne.addRule("fontFace",{fontFamily:`${this.customPrefix}-${this.customFontFamily}`,src:`url('${this.customFontUrl}')`})},data(){return{}},emits:["click"],mixins:[X,J,Si],computed:{uClasses(){let e=[];return e.push(this.customPrefix+"-"+this.name),this.customPrefix=="uicon"?e.push("u-iconfont"):e.push(this.customPrefix),this.color&&I.type.includes(this.color)&&e.push("u-icon__icon--"+this.color),e},iconStyle(){let e={};return e={fontSize:w(this.size),lineHeight:w(this.size),fontWeight:this.bold?"bold":"normal",top:w(this.top)},this.color&&!I.type.includes(this.color)&&(e.color=this.color),e},isImg(){return this.name.indexOf("/")!==-1},imgStyle(){let e={};return e.width=this.width?w(this.width):w(this.size),e.height=this.height?w(this.height):w(this.size),e},icon(){return this.customPrefix!=="uicon"?this.customIcons[this.customPrefix+"-"+this.name]||this.name:_e["uicon-"+this.name]||this.name}},methods:{addStyle:k,addUnit:w,clickHandler(e){this.$emit("click",this.index,e),this.stop&&this.preventEvent(e)}}};function Ti(e,t,o,i,r,l){return(0,g.openBlock)(),(0,g.createElementBlock)("view",{class:(0,g.normalizeClass)(["u-icon",["u-icon--"+e.labelPos]]),onClick:t[0]||(t[0]=(...c)=>l.clickHandler&&l.clickHandler(...c)),renderWhole:!0},[l.isImg?((0,g.openBlock)(),(0,g.createElementBlock)("u-image",{key:0,class:"u-icon__img",src:e.name,mode:e.imgMode,style:(0,g.normalizeStyle)([l.imgStyle,l.addStyle(e.customStyle)])},null,12,["src","mode"])):((0,g.openBlock)(),(0,g.createElementBlock)("u-text",{key:1,class:(0,g.normalizeClass)(["u-icon__icon",l.uClasses]),style:(0,g.normalizeStyle)([l.iconStyle,l.addStyle(e.customStyle)]),hoverClass:e.hoverClass},(0,g.toDisplayString)(l.icon),15,["hoverClass"])),e.label!==""?((0,g.openBlock)(),(0,g.createElementBlock)("u-text",{key:2,class:"u-icon__label",style:(0,g.normalizeStyle)({color:e.labelColor,fontSize:l.addUnit(e.labelSize),marginLeft:e.labelPos=="right"?l.addUnit(e.space):0,marginTop:e.labelPos=="bottom"?l.addUnit(e.space):0,marginRight:e.labelPos=="left"?l.addUnit(e.space):0,marginBottom:e.labelPos=="top"?l.addUnit(e.space):0})},(0,g.toDisplayString)(e.label),5)):(0,g.createCommentVNode)("",!0)],2)}var Le=T(Ii,[["render",Ti],["styles",[Ci]]]);var a=$(R());var Pi="https://diytflservtest.eykj.cn",ki="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb3JwX3Byb2R1Y3QiOiJjYW55aW4iLCJ0eXBlcyI6ImlzdiIsImNvcnBpZCI6ImRpbmdiOTYxNGRmOTQzNDJmNTcwYTEzMjBkY2IyNWU5MTM1MSIsImNvcnBfbmFtZSI6Ilx1NGUwMFx1NGUwMFx1NzlkMVx1NjI4MFx1NTE4NVx1OTBlOFx1NWYwMFx1NTNkMVx1NWU3M1x1NTNmMCIsInVzZXJpZCI6IjMzMzYzNTMzMjEyNDIxMDY2NCIsIm5hbWUiOiJcdTVmMjBcdTYzMmZcdTUzMTcxIiwic3RhZmZfbmFtZSI6Ilx1NWYyMFx1NjMyZlx1NTMxNzEiLCJzdGFmZmlkIjoiMzMzNjM1MzMyMTI0MjEwNjY0IiwiZGVwdF9pZF9saXN0IjoiWzI5OTkzOTAxNywgNDg1Mzg5NTMwLCA0ODYzNzAwMTgsIDY2NDk0NjI5OV0iLCJwYXJlbnRfaWRfbGlzdCI6WzEsMjk5OTM5MDE3XSwiZGluaW5naGFsbF9pZCI6OTMsImRpbmluZ2hhbGxfdGl0bGUiOiJcdTk5MTBcdTUzODUtXHU2YmI1IiwiYWRtaW4iOjF9.mLy2KnEIjCrp7Y-UHA8SrtBjfbdR7W2EvQtBhlE44bk",ji="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBKPfRCrkJ367IiaaxUjR0xIyO68pMwPEVWL/OqI78dtFPPW+zQQ1wb7YGk+pMJV2oa64cC5xZCLzPNHV6LaZe4hIxlxrcGed3aB1cLLNz0ujj1OplHD8PA2Hwlcz1bOo6U7VsQA2tYaXS9xGlBGgqNoGD3KHtDNYWiApA8FyJYwIDAQAB",De={COMMONAPI:Pi,publicKey:ji,token:ki},Y="_mallStore";function zi(e,t,o){uni.setStorageSync(e,t);var i=parseInt(o);if(i>0){var r=Date.parse(new Date);r=r/1e3+i,uni.setStorageSync(e+Y,r+"")}else uni.removeStorageSync(e+Y)}function Fi(e,t){var o=parseInt(uni.getStorageSync(e+Y));if(o&&parseInt(o)<Date.parse(new Date)/1e3)return t||!1;var i=uni.getStorageSync(e);return i||((t==null||t=="")&&(t=!1),t)}function Ni(e){uni.removeStorageSync(e),uni.removeStorageSync(e+Y)}function Li(){uni.clearStorageSync()}var Di={put:zi,get:Fi,remove:Ni,clear:Li};function Oi(e,t){try{uni.setStorageSync(e,t)}catch(o){}}function Ui(e){try{let t=uni.getStorageSync(e);if(t)return t}catch(t){}}var le={setData:Oi,getData:Ui};function Hi(e){var t=null;if(e){var o=e.split(".");if(o.length>1?t=De[o[0]][o[1]]||null:t=De[e]||null,t==null){let i=Di.get("web_config");i&&(o.length>1?t=i[o[0]][o[1]]||null:t=i[e]||null)}}return t}function qi(e,t,o){return o=le.getData("Authorization"),e=this.config("COMMONAPI")+e,new Promise((i,r)=>{uni.request({url:e,data:t,method:"POST",header:{Authorization:o,serviceType:3},success:function(l){i.call(self,l.data)},fail:function(l){r.call(self,l)},complete:function(){}})})}function Ri(e,t,o){return o=le.getData("Authorization"),e="https://fileapitest.eykj.cn"+e,new Promise((i,r)=>{uni.request({url:e,data:t,method:"POST",header:{Authorization:o},success:function(l){i.call(self,l.data)},fail:function(l){r.call(self,l)},complete:function(){}})})}function Mi(e,t,o){return o=le.getData("Authorization"),e=this.config("COMMONAPI")+e,new Promise((i,r)=>{uni.request({url:e,data:t,method:"GET",header:{Authorization:o,serviceType:3},success:function(l){i.call(self,l.data)},fail:function(l){r.call(self,l)}})})}var se={config:Hi,get:Mi,post:qi,postimg:Ri};var Oe="/static/image/BG.png";var Wi=P({props:{isDot:{type:Boolean,default:()=>u.badge.isDot},value:{type:[Number,String],default:()=>u.badge.value},modelValue:{type:[Number,String],default:()=>u.badge.modelValue},show:{type:Boolean,default:()=>u.badge.show},max:{type:[Number,String],default:()=>u.badge.max},type:{type:String,default:()=>u.badge.type},showZero:{type:Boolean,default:()=>u.badge.showZero},bgColor:{type:[String,null],default:()=>u.badge.bgColor},color:{type:[String,null],default:()=>u.badge.color},shape:{type:String,default:()=>u.badge.shape},numberType:{type:String,default:()=>u.badge.numberType},offset:{type:Array,default:()=>u.badge.offset},inverted:{type:Boolean,default:()=>u.badge.inverted},absolute:{type:Boolean,default:()=>u.badge.absolute}}}),Vi={"u-badge":{"":{borderTopRightRadius:100,borderTopLeftRadius:100,borderBottomLeftRadius:100,borderBottomRightRadius:100,flexDirection:"row",lineHeight:11,textAlign:"center",fontSize:11,color:"#FFFFFF"}},"u-badge--dot":{"":{height:8,width:8}},"u-badge--inverted":{"":{fontSize:13}},"u-badge--not-dot":{"":{paddingTop:2,paddingRight:5,paddingBottom:2,paddingLeft:5}},"u-badge--horn":{"":{borderBottomLeftRadius:0}},"u-badge--primary":{"":{backgroundColor:"#3c9cff"}},"u-badge--primary--inverted":{"":{color:"#3c9cff"}},"u-badge--error":{"":{backgroundColor:"#f56c6c"}},"u-badge--error--inverted":{"":{color:"#f56c6c"}},"u-badge--success":{"":{backgroundColor:"#5ac725"}},"u-badge--success--inverted":{"":{color:"#5ac725"}},"u-badge--info":{"":{backgroundColor:"#909399"}},"u-badge--info--inverted":{"":{color:"#909399"}},"u-badge--warning":{"":{backgroundColor:"#f9ae3d"}},"u-badge--warning--inverted":{"":{color:"#f9ae3d"}}},Xi={name:"u-badge",mixins:[X,Wi,J],computed:{boxStyle(){return{}},badgeStyle(){let e={};if(this.color&&(e.color=this.color),this.bgColor&&!this.inverted&&(e.backgroundColor=this.bgColor),this.absolute&&(e.position="absolute",this.offset.length)){let t=this.offset[0],o=this.offset[1]||t;e.top=w(t),e.right=w(o)}return e},showValue(){switch(this.numberType){case"overflow":return Number(this.value)>Number(this.max)?this.max+"+":this.value;case"ellipsis":return Number(this.value)>Number(this.max)?"...":this.value;case"limit":return Number(this.value)>999?Number(this.value)>=9999?Math.floor(this.value/1e4*100)/100+"w":Math.floor(this.value/1e3*100)/100+"k":this.value;default:return Number(this.value)}}},methods:{addStyle:k}};function Ji(e,t,o,i,r,l){return e.show&&(Number(e.value)!==0||e.showZero||e.isDot)?((0,a.openBlock)(),(0,a.createElementBlock)("u-text",{key:0,class:(0,a.normalizeClass)([[e.isDot?"u-badge--dot":"u-badge--not-dot",e.inverted&&"u-badge--inverted",e.shape==="horn"&&"u-badge--horn",`u-badge--${e.type}${e.inverted?"--inverted":""}`],"u-badge"]),style:(0,a.normalizeStyle)([l.addStyle(e.customStyle),l.badgeStyle])},(0,a.toDisplayString)(e.isDot?"":l.showValue),7)):(0,a.createCommentVNode)("",!0)}var Yi=T(Xi,[["render",Ji],["styles",[Vi]]]),Gi=P({props:{duration:{type:Number,default:()=>u.tabs.duration},list:{type:Array,default:()=>u.tabs.list},lineColor:{type:String,default:()=>u.tabs.lineColor},activeStyle:{type:[String,Object],default:()=>u.tabs.activeStyle},inactiveStyle:{type:[String,Object],default:()=>u.tabs.inactiveStyle},lineWidth:{type:[String,Number],default:()=>u.tabs.lineWidth},lineHeight:{type:[String,Number],default:()=>u.tabs.lineHeight},lineBgSize:{type:String,default:()=>u.tabs.lineBgSize},itemStyle:{type:[String,Object],default:()=>u.tabs.itemStyle},scrollable:{type:Boolean,default:()=>u.tabs.scrollable},current:{type:[Number,String],default:()=>u.tabs.current},keyName:{type:String,default:()=>u.tabs.keyName},iconStyle:{type:[String,Object],default:()=>u.tabs.iconStyle}}}),Ki={"u-tabs__wrapper":{"":{flexDirection:"row",alignItems:"center"}},"u-tabs__wrapper__scroll-view-wrapper":{"":{flex:1}},"u-tabs__wrapper__scroll-view":{"":{flexDirection:"row",flex:1}},"u-tabs__wrapper__nav":{"":{flexDirection:"row",position:"relative"}},"u-tabs__wrapper__nav__item":{"":{paddingTop:0,paddingRight:11,paddingBottom:0,paddingLeft:11,flexDirection:"row",alignItems:"center",justifyContent:"center"}},"u-tabs__wrapper__nav__item__text":{"":{fontSize:15,color:"#606266","!whiteSpace":"nowrap"}},"u-tabs__wrapper__nav__item__text--disabled":{"":{"!color":"#c8c9cc"}},"u-tabs__wrapper__nav__line":{"":{height:3,backgroundColor:"#3c9cff",width:30,position:"absolute",bottom:2,borderRadius:100,transitionProperty:"transform",transitionDuration:300}},"@TRANSITION":{"u-tabs__wrapper__nav__line":{property:"transform",duration:300}}},Zi=F("animation"),_i=F("dom"),$i={name:"u-tabs",mixins:[X,J,Gi],data(){return{firstTime:!0,scrollLeft:0,scrollViewWidth:0,lineOffsetLeft:0,tabsRect:{left:0},innerCurrent:0,moving:!1}},watch:{current:{immediate:!0,handler(e,t){e!==this.innerCurrent&&(typeof e=="string"?this.innerCurrent=parseInt(e):this.innerCurrent=e,this.$nextTick(()=>{this.resize()}))}},list(){this.$nextTick(()=>{this.resize()})}},computed:{textStyle(){return e=>{let t={},o=e==this.innerCurrent?k(this.activeStyle):k(this.inactiveStyle);return this.list[e].disabled&&(t.color="#c8c9cc"),D(o,t)}},propsBadge(){return u.badge}},mounted(){return v(this,null,function*(){this.init()})},emits:["click","longPress","change","update:current"],methods:{addStyle:k,addUnit:w,setLineLeft(){let e=this.list[this.innerCurrent];if(!e)return;let t=this.list.slice(0,this.innerCurrent).reduce((i,r)=>i+r.rect.width,0),o=je(this.lineWidth);this.lineOffsetLeft=t+(e.rect.width-o)/2,this.animation(this.lineOffsetLeft,this.firstTime?0:parseInt(this.duration)),this.firstTime&&setTimeout(()=>{this.firstTime=!1},10)},animation(e,t=0){let o=this.$refs["u-tabs__wrapper__nav__line"];Zi.transition(o,{styles:{transform:`translateX(${e}px)`},duration:t})},clickHandler(e,t){this.$emit("click",q(n({},e),{index:t}),t),!e.disabled&&(this.innerCurrent=t,this.resize(),this.$emit("update:current",t),this.$emit("change",q(n({},e),{index:t}),t))},longPressHandler(e,t){this.$emit("longPress",q(n({},e),{index:t}))},init(){ae().then(()=>{this.resize()})},setScrollLeft(){this.innerCurrent<0&&(this.innerCurrent=0);let e=this.list[this.innerCurrent],t=this.list.slice(0,this.innerCurrent).reduce((r,l)=>r+l.rect.width,0),o=ze().windowWidth,i=t-(this.tabsRect.width-e.rect.width)/2-(o-this.tabsRect.right)/2+this.tabsRect.left/2;i=Math.min(i,this.scrollViewWidth-this.tabsRect.width),this.scrollLeft=Math.max(0,i)},resize(){this.list.length!==0&&Promise.all([this.getTabsRect(),this.getAllItemRect()]).then(([e,t=[]])=>{e.left>e.width&&(e.right=e.right-Math.floor(e.left/e.width)*e.width,e.left=e.left%e.width),this.tabsRect=e,this.scrollViewWidth=0,t.map((o,i)=>{this.scrollViewWidth+=o.width,this.list[i].rect=o}),this.setLineLeft(),this.setScrollLeft()})},getTabsRect(){return new Promise(e=>{this.queryRect("u-tabs__wrapper__scroll-view").then(t=>e(t))})},getAllItemRect(){return new Promise(e=>{let t=this.list.map((o,i)=>this.queryRect(`u-tabs__wrapper__nav__item-${i}`,!0));Promise.all(t).then(o=>e(o))})},queryRect(e,t){return new Promise(o=>{_i.getComponentRect(t?this.$refs[e][0]:this.$refs[e],i=>{o(i.size)})})}}};function er(e,t,o,i,r,l){let c=M((0,a.resolveDynamicComponent)("up-icon"),Le),h=M((0,a.resolveDynamicComponent)("u-badge"),Yi);return(0,a.openBlock)(),(0,a.createElementBlock)("view",{class:(0,a.normalizeClass)(["u-tabs",[e.customClass]]),renderWhole:!0},[(0,a.createElementVNode)("view",{class:"u-tabs__wrapper"},[(0,a.renderSlot)(e.$slots,"left"),(0,a.createElementVNode)("view",{class:"u-tabs__wrapper__scroll-view-wrapper"},[(0,a.createElementVNode)("scroll-view",{scrollX:e.scrollable,scrollLeft:r.scrollLeft,scrollWithAnimation:"",class:"u-tabs__wrapper__scroll-view",showScrollbar:!1,ref:"u-tabs__wrapper__scroll-view"},[(0,a.createElementVNode)("view",{class:"u-tabs__wrapper__nav",ref:"u-tabs__wrapper__nav"},[((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(e.list,(s,b)=>((0,a.openBlock)(),(0,a.createElementBlock)("view",{class:(0,a.normalizeClass)(["u-tabs__wrapper__nav__item",[`u-tabs__wrapper__nav__item-${b}`,s.disabled&&"u-tabs__wrapper__nav__item--disabled",r.innerCurrent==b?"u-tabs__wrapper__nav__item-active":""]]),key:b,onClick:d=>l.clickHandler(s,b),onLongpress:d=>l.longPressHandler(s,b),ref_for:!0,ref:`u-tabs__wrapper__nav__item-${b}`,style:(0,a.normalizeStyle)([l.addStyle(e.itemStyle),{flex:e.scrollable?"":1}])},[e.$slots.icon?(0,a.renderSlot)(e.$slots,"icon",{key:0,item:s,keyName:e.keyName,index:b}):((0,a.openBlock)(),(0,a.createElementBlock)(a.Fragment,{key:1},[s.icon?((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:0,class:"u-tabs__wrapper__nav__item__prefix-icon"},[(0,a.createVNode)(c,{name:s.icon,customStyle:l.addStyle(e.iconStyle)},null,8,["name","customStyle"])])):(0,a.createCommentVNode)("",!0)],64)),e.$slots.content?(0,a.renderSlot)(e.$slots,"content",{key:2,item:s,keyName:e.keyName,index:b}):!e.$slots.content&&(e.$slots.default||e.$slots.$default)?(0,a.renderSlot)(e.$slots,"default",{key:3,item:s,keyName:e.keyName,index:b}):((0,a.openBlock)(),(0,a.createElementBlock)("u-text",{key:4,class:(0,a.normalizeClass)([[s.disabled&&"u-tabs__wrapper__nav__item__text--disabled"],"u-tabs__wrapper__nav__item__text"]),style:(0,a.normalizeStyle)([l.textStyle(b)])},(0,a.toDisplayString)(s[e.keyName]),7)),(0,a.createVNode)(h,{show:!!(s.badge&&(s.badge.show||s.badge.isDot||s.badge.value)),isDot:s.badge&&s.badge.isDot||l.propsBadge.isDot,value:s.badge&&s.badge.value||l.propsBadge.value,max:s.badge&&s.badge.max||l.propsBadge.max,type:s.badge&&s.badge.type||l.propsBadge.type,showZero:s.badge&&s.badge.showZero||l.propsBadge.showZero,bgColor:s.badge&&s.badge.bgColor||l.propsBadge.bgColor,color:s.badge&&s.badge.color||l.propsBadge.color,shape:s.badge&&s.badge.shape||l.propsBadge.shape,numberType:s.badge&&s.badge.numberType||l.propsBadge.numberType,inverted:s.badge&&s.badge.inverted||l.propsBadge.inverted,customStyle:"margin-left: 4px;"},null,8,["show","isDot","value","max","type","showZero","bgColor","color","shape","numberType","inverted"])],46,["onClick","onLongpress"]))),128)),(0,a.createElementVNode)("view",{class:"u-tabs__wrapper__nav__line",ref:"u-tabs__wrapper__nav__line",style:(0,a.normalizeStyle)([{width:l.addUnit(e.lineWidth),height:l.addUnit(e.lineHeight),background:e.lineColor,backgroundSize:e.lineBgSize}])},null,4)],512)],8,["scrollX","scrollLeft"])]),(0,a.renderSlot)(e.$slots,"right")])],2)}var tr=T($i,[["render",er],["styles",[Ki]]]),or="/static/image/apply/element.png",ir={},rr={container:{"":{flex:1,position:"relative",backgroundColor:"#16171b"}},"background-image":{"":{position:"absolute",top:0,left:0}},"content-wrapper":{"":{position:"absolute",top:0,left:0,right:0,bottom:0,flex:1}},cont:{"":{display:"flex",justifyContent:"center"}},de_con:{"":{paddingTop:"16rpx",paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx"}},header:{"":{marginTop:"88rpx",paddingLeft:"32rpx",paddingRight:"32rpx",paddingBottom:"32rpx",flexDirection:"row",justifyContent:"space-between",alignItems:"center"}},"header-left":{"":{flexDirection:"row",alignItems:"center"}},"header-right":{"":{paddingTop:"4rpx",paddingRight:"4rpx",paddingBottom:"4rpx",paddingLeft:"4rpx",height:"80rpx",width:"80rpx",borderRadius:"12rpx",backgroundColor:"#5492F7"}},"hi-text":{"":{fontSize:"36rpx",color:"rgba(255,255,255,0.46)"}},"name-text":{"":{fontSize:"36rpx",color:"rgba(255,255,255,0.46)",marginLeft:"8rpx"}},avatar:{"":{width:"72rpx",height:"72rpx",borderRadius:"12rpx"}},"u-tabs__wrapper":{"":{flex:1,width:100},".tabs-container ":{width:100}},"u-tabs":{"":{width:100}},"tabs-container":{"":{width:100}},"u-tabs__wrapper__nav__item":{".tabs-container ":{paddingTop:0,paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx"}},"tab-list":{"":{flexDirection:"row",paddingTop:"16rpx",paddingRight:"32rpx",paddingBottom:"16rpx",paddingLeft:"32rpx"}},"tab-content":{"":{flexDirection:"row"}},"tab-item":{"":{paddingTop:"16rpx",paddingRight:"32rpx",paddingBottom:"16rpx",paddingLeft:"32rpx",marginRight:"16rpx",borderRadius:"8rpx",backgroundColor:"rgba(255,255,255,0.04)"}},"tab-item-active":{"":{backgroundColor:"rgba(255,255,255,0.1)"}},"tab-text":{"":{fontSize:"28rpx",color:"rgba(255,255,255,0.6)"}},"tab-text-active":{"":{color:"rgba(255,255,255,0.85)"}},content:{"":{paddingTop:"32rpx",paddingLeft:"32rpx",paddingRight:"32rpx"}},"uni-scroll-view-refresher":{".content ":{"!width":100,height:20,backgroundColor:"#16171b",display:"flex",justifyContent:"center",alignItems:"center"}},"uni-scroll-view-refresher__indicator":{".content .uni-scroll-view-refresher ":{"content::before":'"\u52A0\u8F7D\u4E2D"',"color::before":"rgba(255,255,255,0.8)","fontSize::before":14}},"refresh-text":{".content ":{position:"absolute",top:0,left:0,right:0,height:30,display:"flex",alignItems:"center",justifyContent:"center",color:"rgba(255,255,255,0.8)",fontSize:14,backgroundColor:"#16171b",zIndex:100}},"uni-scroll-view-refresher__indicator-box":{".content .uni-scroll-view-refresher ":{opacity:0}},phone_cont:{"":{paddingTop:"32rpx",paddingRight:"32rpx",paddingBottom:"32rpx",paddingLeft:"32rpx",borderRadius:"12rpx",backgroundColor:"rgba(255,255,255,0.04)",borderWidth:.5,borderStyle:"solid",borderColor:"rgba(255,255,255,0.0972)"}},phone_bet:{"":{display:"flex",flexDirection:"row",justifyContent:"space-between",alignItems:"center"}},phone_title:{"":{color:"rgba(255,255,255,0.85)",fontSize:"34rpx",lineHeight:"44rpx"}},phone_value:{"":{color:"rgba(255,255,255,0.85)",fontSize:"30rpx",lineHeight:"44rpx"}},phone_btn:{"":{marginTop:"32rpx",borderWidth:1,borderStyle:"solid",borderColor:"rgba(255,255,255,0.2)",backgroundColor:"rgba(255,255,255,0)",color:"rgba(255,255,255,0.85)",display:"flex",alignItems:"center",justifyContent:"center",fontSize:"28rpx",paddingTop:"16rpx",paddingRight:"16rpx",paddingBottom:"16rpx",paddingLeft:"16rpx"}},phone_btn_image:{"":{marginRight:"10rpx",width:"40rpx",height:"40rpx"}},phone_green:{"":{fontSize:"28rpx",borderRadius:"12rpx",paddingTop:"12rpx",paddingRight:"20rpx",paddingBottom:"12rpx",paddingLeft:"20rpx"},".normal":{color:"#00B042",backgroundColor:"rgba(0,176,66,0.12)"},".abnormal":{color:"#FF4D4F",backgroundColor:"rgba(255,77,79,0.12)"}},"no-data":{"":{textAlign:"center",color:"rgba(255,255,255,0.6)",fontSize:"32rpx",paddingTop:"64rpx",paddingRight:0,paddingBottom:"64rpx",paddingLeft:0}},"no-more":{"":{textAlign:"center",color:"rgba(255,255,255,0.6)",fontSize:"28rpx",paddingTop:"32rpx",paddingRight:0,paddingBottom:"32rpx",paddingLeft:0}},"welcome-container":{"":{paddingTop:"0rpx",paddingRight:"32rpx",paddingBottom:"60rpx",paddingLeft:"32rpx"}},"welcome-text":{"":{fontSize:"52rpx",color:"rgba(255,255,255,0.85)"}},null_device:{"":{marginTop:"80rpx",color:"rgba(255,255,255,0.65)",fontSize:"32rpx",textAlign:"center"}},"device-list":{"":{flex:1}},"device-grid":{"":{flexDirection:"row",flexWrap:"wrap",justifyContent:"space-between"}},"device-item":{"":{width:"340rpx",backgroundColor:"rgba(255,255,255,0.04)",boxShadow:"0px 4px 10px 0px rgba(0, 0, 0, 0.1)",borderRadius:"20rpx",marginTop:"20rpx",marginRight:"32rpx",marginBottom:"20rpx",marginLeft:"32rpx",paddingTop:"20rpx",paddingRight:"20rpx",paddingBottom:"20rpx",paddingLeft:"20rpx"}},"device-image":{"":{width:"200rpx",height:"92rpx"}},"device-name":{"":{fontSize:"34rpx",color:"rgba(255,255,255,0.85)",marginTop:"28rpx",marginRight:0,marginBottom:"28rpx",marginLeft:0}},"device-info":{"":{flexDirection:"row",justifyContent:"space-between",alignItems:"center",marginBottom:"8rpx"}},"info-label":{"":{fontSize:"24rpx",color:"rgba(255,255,255,0.45)"}},"info-value":{"":{fontSize:"24rpx",color:"rgba(255,255,255,0.65)"}},"avatar-container":{"":{width:100,height:100}},"avatar-placeholder":{"":{width:100,height:100,display:"flex",alignItems:"center",justifyContent:"center"}},"avatar-text":{"":{color:"#FFFFFF",fontSize:"28rpx"}}},nr={inheritAttrs:!1,data(){return{deviceData:[],page:1,perPage:5,screenWidth:0,screenHeight:0,isRefreshing:!1,loading:!1,hasMore:!0,userNameSuffix:"",userAvatar:"",currentTab:0,tabsList:[],statusBarHeight:20,modelData:[]}},created(){return v(this,null,function*(){let e=uni.getSystemInfoSync();this.screenWidth=e.windowWidth,this.screenHeight=e.windowHeight,this.statusBarHeight=e.statusBarHeight,this.getUserInfo(),yield this.getModelList()})},onLoad(){},onShow(){this.getDeviceList()},methods:{handleDevice(e){uni.navigateTo({url:`/pages/project/detail/index?deviceCode=${e.deviceCode}`})},initData(){return v(this,null,function*(){yield this.getModelList(),this.getDeviceList()})},getDeviceList(){return v(this,arguments,function*(e={}){if(!this.loading){this.loading=!0;try{let t={page:this.page,perPage:this.perPage,offlineState:1},o=n(n({},t),e);y("log","at pages/project/index.nvue:160","\u6700\u7EC8\u8BF7\u6C42\u53C2\u6570:",o);let i=yield se.post("/device/get_ls",o);i.status===0?(this.deviceData=i.data.items||[],this.hasMore=i.data.hasMore||!1):y("error","at pages/project/index.nvue:167","\u83B7\u53D6\u8BBE\u5907\u5217\u8868\u5931\u8D25:",i.message)}catch(t){y("error","at pages/project/index.nvue:170","\u8BF7\u6C42\u51FA\u9519:",t)}finally{this.loading=!1}}})},onRefresh(){return v(this,null,function*(){this.isRefreshing=!0;try{yield Promise.all([this.getModelList(),this.getDeviceList()]),uni.showToast({title:"\u5237\u65B0\u6210\u529F",icon:"none",duration:1e3})}catch(e){y("error","at pages/project/index.nvue:192","\u5237\u65B0\u5931\u8D25:",e),uni.showToast({title:"\u5237\u65B0\u5931\u8D25",icon:"none",duration:1e3})}finally{this.isRefreshing=!1}})},getModelList(){return v(this,null,function*(){try{let e=yield se.get("/model/attribute/get_all_model");if(y("log","at pages/project/index.nvue:206","model\u6570\u636E:",e.data),e.status===0&&Array.isArray(e.data)){let t={name:"\u5168\u90E8\u8BBE\u5907",id:0},o=e.data.map(i=>typeof i=="object"?{name:i.model||"",id:i.id||""}:{name:i,id:i}).filter(i=>i.name);this.tabsList=[t,...o],y("log","at pages/project/index.nvue:228","\u5904\u7406\u540E\u7684tabs\u6570\u636E:",this.tabsList)}}catch(e){y("error","at pages/project/index.nvue:231","\u83B7\u53D6\u8BBE\u5907\u578B\u53F7\u5217\u8868\u5931\u8D25:",e),uni.showToast({title:"\u83B7\u53D6\u8BBE\u5907\u578B\u53F7\u5931\u8D25",icon:"none"})}})},handleTabClick(e){y("log","at pages/project/index.nvue:239","\u70B9\u51FB\u7684tab\u9879:",e);let t=this.tabsList.findIndex(o=>o.name===e.name);if(t>-1){this.currentTab=t;let o={page:1,perPage:this.perPage,offlineState:1};t>0&&e.id&&(o.deviceModelID=e.id),y("log","at pages/project/index.nvue:256","\u8BF7\u6C42\u53C2\u6570:",o),this.getDeviceList(o)}},getUserInfo(){try{let e=uni.getStorageSync("user");e&&(this.userNameSuffix=e.name.slice(-2),this.userAvatar=e.profilePic)}catch(e){y("error","at pages/project/index.nvue:269","\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25:",e)}}}};function ar(e,t,o,i,r,l){let c=M((0,a.resolveDynamicComponent)("u-tabs"),tr);return(0,a.openBlock)(),(0,a.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,a.createElementVNode)("view",{class:"container"},[(0,a.createElementVNode)("u-image",{class:"background-image",src:Oe,mode:"aspectFill",style:(0,a.normalizeStyle)({width:r.screenWidth+"px",height:r.screenHeight+"px"})},null,4),(0,a.createElementVNode)("view",{class:"content-wrapper"},[(0,a.createElementVNode)("view",{class:"header",style:(0,a.normalizeStyle)({paddingTop:r.statusBarHeight+"px"})},[(0,a.createElementVNode)("view",{class:"header-left"},[(0,a.createElementVNode)("u-text",{class:"hi-text"},"Hi, "),(0,a.createElementVNode)("u-text",{class:"name-text"},(0,a.toDisplayString)(r.userNameSuffix),1)]),(0,a.createElementVNode)("view",{class:"header-right"},[r.userAvatar?((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:0,class:"avatar-container"},[(0,a.createElementVNode)("u-image",{class:"avatar",src:r.userAvatar,mode:"aspectFill"},null,8,["src"])])):((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:1,class:"avatar-placeholder"},[(0,a.createElementVNode)("u-text",{class:"avatar-text"},(0,a.toDisplayString)(r.userNameSuffix),1)]))])],4),(0,a.createElementVNode)("view",{class:"welcome-container"},[(0,a.createElementVNode)("u-text",{class:"welcome-text"},"\u6B22\u8FCE\u74E6\u65AF\u6CBB\u7406\u4FE1\u606F\u5316\u7CFB\u7EDF")]),(0,a.createElementVNode)("view",{class:"tabs-container"},[r.tabsList.length>0?((0,a.openBlock)(),(0,a.createBlock)(c,{key:0,list:r.tabsList,current:r.currentTab,onClick:l.handleTabClick,itemStyle:"height: 80rpx; padding: 0 32rpx;",lineColor:"rgba(255, 255, 255, 0.85)",activeStyle:{color:"rgba(255, 255, 255, 0.85)",fontWeight:"bold"},inactiveStyle:{color:"rgba(255, 255, 255, 0.65)"},scrollable:!0},null,8,["list","current","onClick","activeStyle","inactiveStyle"])):(0,a.createCommentVNode)("",!0)]),r.deviceData==""?((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:0,class:"null_device"},[(0,a.createElementVNode)("u-text",{class:"null_device"},"\u6682\u65E0\u8BBE\u5907")])):((0,a.openBlock)(),(0,a.createElementBlock)("scroll-view",{key:1,class:"device-list",scrollY:"true",refresherEnabled:!0,onRefresherrefresh:t[0]||(t[0]=(...h)=>l.onRefresh&&l.onRefresh(...h)),refresherTriggered:r.isRefreshing},[(0,a.createElementVNode)("view",{class:"device-grid"},[((0,a.openBlock)(!0),(0,a.createElementBlock)(a.Fragment,null,(0,a.renderList)(r.deviceData,(h,s)=>((0,a.openBlock)(),(0,a.createElementBlock)("view",{key:s,class:"device-item",onClick:b=>l.handleDevice(h)},[(0,a.createElementVNode)("view",{style:{display:"flex","flex-direction":"row","justify-content":"end","margin-bottom":"8rpx"}},[(0,a.createElementVNode)("u-text",{style:{"text-align":"right"},class:"info-value"},(0,a.toDisplayString)(h.deviceModel),1)]),(0,a.createElementVNode)("u-image",{class:"device-image",src:or,mode:"aspectFit"}),(0,a.createElementVNode)("u-text",{class:"device-name"},(0,a.toDisplayString)(h.deviceName),1),(0,a.createElementVNode)("view",{class:"device-info"},[(0,a.createElementVNode)("u-text",{class:"info-label"},"\u8FD0\u884C\u52A8\u6001\uFF1A"),(0,a.createElementVNode)("u-text",{class:"info-value"},(0,a.toDisplayString)(h.workState===1?"\u5F00\u673A":"\u5173\u673A"),1)]),(0,a.createElementVNode)("view",{class:"device-info"},[(0,a.createElementVNode)("u-text",{class:"info-label"},"\u5DE5\u4F5C\u6A21\u5F0F\uFF1A"),(0,a.createElementVNode)("u-text",{class:"info-value"},(0,a.toDisplayString)(h.workMode===0?"\u7A7A\u95F2":"\u5DE5\u4F5C\u4E2D"),1)])],8,["onClick"]))),128))])],40,["refresherTriggered"]))])])])}var G=T(nr,[["render",ar],["styles",[ir,rr]]]);var ce=plus.webview.currentWebview();if(ce){let e=parseInt(ce.id),t="pages/project/index",o={};try{o=JSON.parse(ce.__query__)}catch(r){}G.mpType="page";let i=Vue.createPageApp(G,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:t,__pageQuery:o});i.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...G.styles||[]])),i.mount("#root")}})();
