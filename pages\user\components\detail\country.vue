<template>
	<view class="">
	<custom-header  style="height: 88rpx;" title="选择国家" showBack />
  <view class="country-picker">
    <!-- <view class="search-bar">
      <input type="text" placeholder="搜索国家/地区" v-model="searchText" />
    </view> -->
	
    <scroll-view scroll-y class="scroll-area">
      <!-- <view
        v-for="country in filteredCountries"
        :key="country.code"
        class="country-item"
        @click="selectCountry(country)"
      >
        {{ country.cn }}
      </view> -->
	  <view class="country-item" @click="selectCountry(country)">
	  	中国
	  </view>
    </scroll-view>
  </view>
  </view>
</template>

<script>
import countryData from '../../../../static/area/country.json'; 
import Request from '@/components/utils/request'; // 引入 request
import customHeader  from '@/components/page/header.vue';
import Quene from '@/components/utils/queue';
export default {
	components:{
		customHeader 
	},
  data() {
    return {
      searchText: '',
      allCountries: [],
      currentPage: null,
      eventChannel: null,
    };
  },
  computed: {
    filteredCountries() {
      return this.allCountries.filter((country) =>
        country.cn.toLowerCase().includes(this.searchText.toLowerCase())
      );
    },
  },
  methods: {
    loadCountries() {
      this.allCountries = countryData; /* 从 JSON 里读取数据 */
    },
    async selectCountry(country) {
      try {
        const res = await Request.post('/personal/post_modify', {
          country: '中国'
        });
        
        if (res.status === 0) {
          // 使用事件通知更新国家信息
          uni.$emit('updateUserInfo', {
            country: '中国'
          });
          
          // 返回上一页
          uni.navigateBack();
        } else {
          this.showErrorToast(res.msg || '修改失败');
        }
      } catch (error) {
        this.showErrorToast('网络错误，请稍后重试');
        console.error('修改失败:', error);
      }
    },
    getCurrentRoute() {
      const pages = getCurrentPages(); // 获取加载的页面
      const currentPage = pages[pages.length - 1]; // 获取当前页面
      this.currentPage = currentPage;
      return currentPage; // 返回当前页面
    },
      showErrorToast(title) {
        uni.showToast({
          title: title,
          icon: 'none',
        });
      },
  },
  mounted() {
    this.loadCountries();
    this.getCurrentRoute();
    this.eventChannel = this.currentPage.eventChannel;
  },
};
</script>

<style scoped>
.header{
	height: 186rpx;
	/* color: #fff; */
}
.country-picker {
  display: flex;
  flex-direction: column;
  /* height: 100vh; */
  background: rgba(255, 255, 255, 0.05);
}
.search-bar {
  padding: 10px;
  /* background-color: rgba(255, 255, 255, 0.13); */
}
.search-bar input {
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 5px;
  width: 100%;
  font-size: 16px;
  color: rgba(255, 255, 255, 0.65);
}
.scroll-area {
  flex: 1;
  overflow-y: auto;
}
.country-item {
	color: rgba(255, 255, 255, 0.65);
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.0972);
  cursor: pointer;
}
.country-item:last-child{
	border: none;
}
</style>
