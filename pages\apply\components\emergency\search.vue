<template>
	<custom-header style="height: 88rpx;" title="筛选" showBack />
	<view class="content">
		<view>
			<up-datetime-picker		            
				:show="show1"
				v-model="value1"
				mode="date"
				@confirm="handleConfirm" 
				@cancel="handleCancel" 
			></up-datetime-picker>
			<up-datetime-picker
				:show="show2"
				v-model="value2"
				mode="date"
				:minDate="value1"
				@confirm="handleConfirm2" 
				@cancel="handleCancel2" 
			></up-datetime-picker>
		</view>
		<view class="menu-list">
			<view class="menu-item" @click="show1 = true">
				<view class="menu-item-right">
					<text class="menu-text">开始日期</text>
					<view class="menu-item-content">
						<text class="menu-item-text">{{formatDate(value1)}}</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="show2 = true">
				<view class="menu-item-right">
					<text class="menu-text">结束日期</text>
					<view class="menu-item-content">
						<text class="menu-item-text">{{formatDate(value2)}}</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			
			<!-- <view class="menu-item">
				<view class="menu-item-right">
					<text class="menu-text">预案类型</text>
					<view class="menu-item-content">
						<up-input class="type-input" v-model="planType" placeholder="请输入预案类型" 
							placeholder-style="color: rgba(255,255,255,0.3)"></up-input>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			
			<view class="menu-item">
				<view class="menu-item-right">
					<text class="menu-text">预案等级</text>
					<view class="menu-item-content">
						<up-input class="type-input" v-model="planLevel" placeholder="请输入预案等级" 
							placeholder-style="color: rgba(255,255,255,0.3)"></up-input>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view> -->
		</view>
		
		<view class="search_btn">
			<button class="btn1" @click="cancle">取消</button>
			<button class="btn2" @click="confirm">确定</button>
		</view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue';
	
	export default {
		components: {
			customHeader
		},
		data() {
			const today = new Date();
			const formattedDate = this.formatDate(today);
			
			return {
				value1: today.getTime(),
				value2: today.getTime(),
				show1: false,
				show2: false,
				time1: formattedDate,
				time2: formattedDate,
				planType: '',
				planLevel: ''
			}
		},
		methods: {
			formatDate(date) {
				const d = new Date(date);
				const year = d.getFullYear();
				const month = String(d.getMonth() + 1).padStart(2, '0');
				const day = String(d.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			},
			
			handleConfirm(e) {
				const date = new Date(e.value);
				this.value1 = e.value;
				this.time1 = this.formatDate(date);
				this.show1 = false;
			},
			
			handleConfirm2(e) {
				const date = new Date(e.value);
				this.value2 = e.value;
				this.time2 = this.formatDate(date);
				this.show2 = false;
			},
			
			handleCancel2() {
				this.show2 = false;
			},
			
			handleCancel() {
				this.show1 = false;
			},
			
			cancle() {
				uni.navigateBack()
			},
			
			confirm() {
				uni.$emit('updateEmergencyList', {
					dateStart: this.time1,
					dateEnd: this.time2
				});
				
				uni.navigateBack();
			}
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	.content {
		padding: 0 32rpx;
		padding-top: 32rpx;
	}

	.menu-list {
		border-radius: 12rpx;
	}

	.menu-item {
		display: flex;
		background: rgba(255, 255, 255, 0.08);
		margin-bottom: 32rpx;
		border-radius: 8rpx;
		height: 116rpx;
		flex-direction: row;
		align-items: center;
		padding-left: 32rpx;
	}

	.menu-item-right {
		flex: 1;
		height: 100%;
		padding-right: 32rpx;
		flex-direction: row;
		text-align: center;
		align-items: center;
		display: flex;
		justify-content: space-between;
	}

	.menu-item-content {
		flex-direction: row;
		height: 100%;
		display: flex;
		align-items: center;

		text {
			color: rgba(255, 255, 255, 0.65);
			align-items: center;
			text-align: center;
			font-size: 30rpx;
			line-height: 58rpx;
		}
	}

	.menu-text {
		font-family: PingFang SC;
		font-size: 28rpx;
		letter-spacing: 0px;
		font-weight: normal;
		color: rgba(255, 255, 255, 0.85);
	}

	.arrow-right {
		width: 60rpx;
		height: 60rpx;
	}
	
	.type-input {
		width: 300rpx;
		background: transparent;
		color: rgba(255, 255, 255, 0.65);
	}
	
	.search_btn {
		position: fixed;
		bottom: 30px;
		display: flex;
		
		button {
			width: 328rpx;
		}
	}
	
	.btn1 {		
		margin-right: 30rpx;
		color: rgba(255,255,255,0.85);
		background: rgba(255, 255, 255, 0.08);
	}
	
	.btn2 {
		color: rgba(255,255,255,0.85);
		background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	}
</style>