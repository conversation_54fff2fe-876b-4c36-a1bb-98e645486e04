<template>
	<custom-header  style="height: 88rpx;" title="云平台" showBack />
	<view class="container">
		<view class="menu" @click="navigateToVer">
			<view class="menu-left">
				<text>关于系统</text>
			</view>
			<view class="menu-right">
				<text>{{version}}</text>
				<image src="../../../static/image/user/right.png" mode=""></image>
			</view>
		</view>
		<view class="menu menu_outlogin"  @click="outLogin">
			<view class="menu_outlogin">
				<text>退出登录</text>
			</view>
			
		</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
import customHeader from '@/components/page/header.vue'
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				version:'云平台0.0.1' //版本号
			}
		},
		methods: {
			navigateToVer(){
      uni.navigateTo({
        url: '/pages/user/components/setting/version'
      })
    },
    async outLogin(){
      try {
        // 先触发logout事件，断开IM连接
        uni.$emit('logout');
        
        await Request.get('/sys/logOut');
        // 清除本地存储的信息
        uni.removeStorageSync('Authorization');
        uni.removeStorageSync('userinfo');
        uni.removeStorageSync('user');
        uni.removeStorageSync('sign');
        uni.removeStorageSync('loginInfo');
        
        // 跳转到登录页面
        uni.reLaunch({
          url: '/pages/login/login'
        });
      } catch (error) {
        uni.showToast({
          title: '退出登录失败',
          icon: 'none'
        });
      }
    }
		}
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style lang="scss" scoped>
	.container{
		padding: 32rpx;
		padding-top: 188rpx
		}
		.menu{
	height: 124rpx;
	padding: 0 32rpx;
	border-radius: 12rpx;
	opacity: 1;
	background: rgba(255, 255, 255, 0.0362);
	box-sizing: border-box;
	border: 1rpx solid rgba(255, 255, 255, 0.0972);
	display: flex;
	flex-direction:row;
	align-items: center;
	justify-content: space-between;
	margin-bottom: 28rpx;
}
.menu-left{
	font-family: Lato;
	font-size: 34rpx;
	font-weight: normal;
	line-height: 44.8rpx;
	letter-spacing: 0rpx;
	/* Neutrals/White */
	color: #FFFFFF;
}
.menu_outlogin{
	height: 96rpx;
	align-items: center;
	line-height: 96rpx;
	text-align: center;
	font-size: 32rpx;
	justify-content: center;
	color: rgba(255, 255, 255, 0.85);
}
.menu-right{
	height: 100%;
	display: flex;
	flex-direction: row;
	align-items: center;
	text{
		font-family: Lato;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 58rpx;
		text-align: right;
		letter-spacing: 0rpx;
		/* White/65% */
		color: rgba(255, 255, 255, 0.65);
	}
	image{
		width: 60rpx;
		height: 60rpx;
	}
}
</style>