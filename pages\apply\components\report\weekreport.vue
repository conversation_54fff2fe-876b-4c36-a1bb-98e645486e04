<template>
	
	<scroll-view 
			class="content" 
			scroll-y 
			@refresherrefresh="onRefresh"
			@refresherpulling="onPulling"
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			refresher-background="#16171b"
			:refresher-threshold="30"
			refresher-default-style="black"
		>
		<view class="search-bar">
			<lk-week-picker
			  start="2009-11-24"
			  start-value-format="yyyy-MM-dd"
			  range-separator="~"
			  date-separator="."
			  :end="today"
			  :border="false"
			  :clear-icon="false"
			  :end-date="weekRange[1]"
			  v-model="weekRange[0]"
			  @change="rangeChange"
			></lk-week-picker>
			<!-- <zxz-uni-datetime-picker
				class="date-picker"
				type="week"
				v-model="selectedDate"
				@change="dateChange"
				@confirm="weekConfirm"
				format="yyyy-MM-dd"
				:border="false"
				:clear-icon="false"
				:placeholder="'选择周'"
			>
			</zxz-uni-datetime-picker> -->
			<!-- <view class="filter_icon" @click="handelSearch">
				<image src="@/static/image/apply/search.png" mode=""></image>
			</view> -->
		</view>
		
			<view style="height:600px;magin:10rpx;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
			<!-- <view> -->
				<zb-table 
				@sort-change="sortChange" 
				:pullUpLoading="pullUpLoading" 
				:isShowLoadMore="true"
				:highlight="true" 
				:show-header="true" 
				:columns="column" 
				:fit="false" 
				:permissionBtn="permissionBtn"
				:stripe="true" 
				row-key="id" 
				v-bind="$attrs"
				@rowClick="rowClick" 
				:border="false"  @edit="buttonEdit"
				 :data="data"></zb-table>

			</view>

	</scroll-view>
</template>

<script>

	import Request from '@/components/utils/request.js'
	let that = null
	export default {
		inheritAttrs: false,
		
		data() {
			const baseData = {
			      serialNum: "ZJD00111111111111111",
			      title: "三牙钻头",
			      model: "钻头",
			      specs: "GD325-5",
			      productionTime: "2023-12-23 10:12:00",
			      purchaseTime: "2023-12-23 10:12:00",
			      lifespan: "20",
			      status: 0,
			       remark:"有拉满取作府红议少认越物政但采队因改。业有习极具部回率认按表价路。直非权则提片却次",
			      lastUsedTime: "2025-01-01 10:12:00",
			      nextRepaireTime: "2025-01-04 10:12:00",
			    };
			      const generateData = ()=>{
					  const repeatedData = [];
			          for (let i =0; i < 20; i++) {
			           repeatedData.push(
			                {id:i,...baseData}
			               )
			          }
			        return repeatedData;
					 }
			// 获取当前日期
			const today = new Date();
			const year = today.getFullYear();
			const month = today.getMonth() + 1;
			const day = today.getDate();
			
			// 计算本周的周一和周日
			const currentDay = today.getDay() || 7;
			const monday = new Date(today);
			monday.setDate(today.getDate() - (currentDay - 1));
			
			const sunday = new Date(monday);
			sunday.setDate(monday.getDate() + 6);
			
			// 格式化日期函数
			const formatDate = (date) => {
				const year = date.getFullYear();
				const month = String(date.getMonth() + 1).padStart(2, '0');
				const day = String(date.getDate()).padStart(2, '0');
				return `${year}-${month}-${day}`;
			};

			return {				
				dateStart: formatDate(monday),
				dateEnd: formatDate(sunday),
				searchText: '',
				show: true,
								mode: 'week',
				column: [
					// { type:'selection', fixed:true,width:60 },
					{
						name: 'name',
						label: '人员',
						fixed: true,
						// align: 'center',
						emptyString: '--'
					},
					
					{
						name: 'effectiveHour',
						label: '有效工作',
					},
					{
						name: 'useElectricity',
						label: '用电量',
					},
					{
						name: 'useWater',
						label: '用水量',
						fixed: true
					},
					{
						name: 'sealingDepth',
						label: '封孔深度',
					},
					{
						name: 'cementContent',
						label: '水泥量',
					},
					{
						name: 'footageNum',
						label: '累计钻孔米',
						width: 120,
					},
					{
						name: 'drillPipeNum',
						label: '退钻钻杆根数',
						// sorter: true,
						width: 130,
						// type: 'progress'
					},
					{
						name: 'finishNum',
						label: '终孔数量',
					},
					//  {
					//           name: 'status',
					//           label: '施工进度',
					// 		  type:'plan',
					//           filters: {
					//             0: '未完成',
					//             1: '已完成',
					// 			2:'已废孔'
					//           },
							  
					// }
				],
				data: [],//表格数据
				data1: [],
				flag1: true,
				flag2: true,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				isShowLoadMore: true, // 是否显示"加载更多"  
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
				isRefreshing: false,  // 是否正在刷新
				isPulling: false, // 添加下拉状态
				showDatePicker: false,
				dateDisplay: '',
				minDate: `${year - 5}-01-01`, // 现在 year 变量已定义
				maxDate: `${year + 5}-12-31`, // 现在 year 变量已定义
				defaultDate: '', // 不设置默认日期，避免自动选中
				monthNum: 1, // 只显示当前月份
				selectedDate: '',
				weekDates: [], // 添加这个数组来存储日期范围
				weekRange: [formatDate(monday), formatDate(sunday)], // 设置默认值为当前周
				today: new Date().toISOString().split('T')[0], // Add this for the end date limit
			}
		},
		onLoad() {
			this.handelDrill();
			
			// 添加筛选更新事件监听
			uni.$on('updateDrillList', ({dateStart, dateEnd}) => {
				this.dateStart = dateStart;
				this.dateEnd = dateEnd;
				this.currentPage = 1;
				this.data = [];
				this.handelDrill();
			});
		},
		created() {
			that = this
		},
		mounted() {
			this.handelDrill();
			// 监听控制台输出
			const originalLog = console.log;
			console.log = (...args) => {
				// 检查是否是日期数组输出
				if (args[0] instanceof Array && args[0].length === 2 && 
					typeof args[0][0] === 'string' && typeof args[0][1] === 'string') {
					this.updateWeekDates(args[0]);
				}
				originalLog.apply(console, args);
			};
		},
		methods: {
			 handleSearchInput(e) {
			 	// console.log('搜索输入值:', e);
			 	// 清除之前的定时器
			 	if(this.searchTimer) {
			 		clearTimeout(this.searchTimer);
			 	}
			 	
			 	// 设置新的定时器，延迟300ms执行搜索
			 	this.searchTimer = setTimeout(() => {
			 		this.searchText = e; // 更新搜索文本
			 		this.currentPage = 1; // 重置页码
			 		this.data = []; // 清空数据
			 		this.isShowLoadMore = true; // 重置加载更多
			 		this.handelDrill(); // 重新加载数据
			 	}, 300);
			 },
			 
			 // 处理清空
			 handleClear() {
			 	this.searchText = '';
			 	this.currentPage = 1;
			 	this.data = [];
			 	this.isShowLoadMore = true;
			 	this.handelDrill();
			 },
			//初始加载调取接口获取数据
			async handelDrill() {

				try {
					let drill_data = {
						page: this.currentPage,
						perPage: this.perPage,
						dateStart:this.dateStart,
						 dateEnd: this.dateEnd
					};
					const res = await Request.post('/report/get_week_report', drill_data)

						if (res.status == 0) {
							console.log('返回数据', res);
							this.data=res.data.items;
							if (res.data.items.length < this.perPage) {
							             this.isShowLoadMore = false;
							          }
							// 更新成功
							// uni.showToast({
							// 	title: '实名认证成功',
							// 	icon: 'none',
							// 	duration: 2000
							// });

						} else {
							// 失败
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}

				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
				
			},
			
			change(e) {
				console.log("e:", e);
			},
			sortChange(item, sorterMode, index) {

				console.log('触发排序', item, sorterMode, index)
			},
			//筛选
			handelSearch(){
				uni.navigateTo({
					// url: '/pages/apply/components/drill/search'
				})
			},
			cellStyle({
				row,
				column,
				rowIndex,
				columnIndex
			}) {
				// console.log('row, column, rowIndex, columnIndex')
				if ((columnIndex % 2) != 0) {
					return {
						background: 'red'
					}
				}
			},
			async pullUpLoading(done) {
				if (!this.flag1) {
					return
				}
				this.currentPage++;  // 页码加1
						try {
				           const res = await Request.post('/report/get_week_report', {
				              page: this.currentPage,
				              perPage: this.perPage,
							 dateEnd: this.dateEnd,
							 dateStart:this.dateStart,
							
				            });
				
				           if (res.status == 0) {
							   console.log('加载获取数据',res.data);
							   // this.data.push(res.data.items);
							   console.log('data11111',this.data);
				           		if (res.data.items && res.data.items.length > 0) {
				           			this.data = this.data.concat(res.data.items);
				           			console.log('data11111',this.data);
				           			done(); // 通知 zb-table 加载完成
				           			
				           		}else{
				           		   done('ok'); // 通知zb-table 没有更多数据
				           		   this.flag1 = false
				           		   uni.showToast({
				           		   	title: '暂无更多数据' ,
				           		   	icon: 'none',
				           		   	duration: 1000
				           		   })
				           		}
				
				           } else {
				
						        //    uni.showToast({
								// 	title: '加载数据失败' ,
								// 	icon: 'none',
								// 	duration: 1000
								// })
						        done();       // 结束加载
				           }
						} catch (error) {
						    console.error("加载更多数据失败:", error);
						      // uni.showToast({
					        	// 	title: '加载数据失败' ,
					        	// 	icon: 'none',
					        	// 	duration: 1000
					        	// })
						        done();    //  结束加载
						}
				// setTimeout(() => {
				// 	this.data.push({
				// 		serialNum: 'ZJD0021',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	},
				// 	{
				// 		serialNum: 'ZJD0022',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	})

				// 	this.num++
				// 	if (this.num === 3) {
				// 		done('ok')
				// 		this.flag1 = false
				// 	} else {
				// 		done()
				// 	}
				// }, 2000)
			},
			permissionBtn(row, renders, rowIndex) {
				if (row.id == 2) {
					let arr = renders.filter(item => item.func === 'edit')
					return arr
				}
				return renders
			},
			
			buttonEdit(ite, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '点击编辑'
				// })
				// console.log(ite, index),
				uni.navigateTo({
					// url:`/pages/apply/components/drill/detail?id=${ite.id}`
				})
			},

			rowClick(row, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '单击某行'
				// })
				// console.log('单击某行', row, index)
				uni.navigateTo({
					// url: `/pages/apply/components/drill/detail?id=${row.id}`
				})
			},
			// 添加下拉刷新方法
						async onRefresh() {
							this.isPulling = false;  // 开始刷新时关闭下拉状态
							this.isRefreshing = true;
							
							try {
								// 重置页码和数据
								this.currentPage = 1;
								this.flag1 = true;
								
								// 重新加载数据
								await this.handelDrill();
								
								// 提示刷新成功
								uni.showToast({
									title: '刷新成功',
									icon: 'none',
									duration: 1000
								});
							} catch (error) {
								console.error('刷新失败:', error);
								uni.showToast({
									title: '刷新失败',
									icon: 'none',
									duration: 1000
								});
							} finally {
								// 停止刷新动画
								this.isRefreshing = false;
							}
						},
						
						// 添加下拉事件处理
						onPulling(e) {
							this.isPulling = true;
						},
			// 修改日期变更处理方法
			dateChange(weeks) {
				console.log('选择的周:', weeks);
				// 在这里我们可以捕获 zxz-util.js 输出的日期数组
				// 使用 nextTick 确保在下一个 DOM 更新周期中获取数组
				this.$nextTick(() => {
					// 这里的 weekDates 就是 zxz-util.js 输出的数组
					if (this.weekDates && this.weekDates.length === 2) {
						this.dateStart = this.weekDates[0];
						this.dateEnd = this.weekDates[1];
						this.handelDrill();
					}
				});
			},
			
			// 修改确认事件处理器
			weekConfirm(e) {
				// 这里的 e 应该包含日期数组
				console.log('周选择确认:', e);
				
				// 检查是否有日期数组
				if (Array.isArray(e) && e.length === 2) {
					this.dateStart = e[0];
					this.dateEnd = e[1];
				} else if (e && e.detail && Array.isArray(e.detail) && e.detail.length === 2) {
					// 某些情况下日期数组可能在 detail 属性中
					this.dateStart = e.detail[0];
					this.dateEnd = e.detail[1];
				}
				
				// 重置分页和数据
				this.currentPage = 1;
				this.data = [];
				this.isShowLoadMore = true;
				
				// 调用API获取新日期的数据
				this.handelDrill();
			},
			// 添加一个方法来更新日期范围
			updateWeekDates(dates) {
				this.weekDates = dates;
			},
			// Add this method to handle week range changes
			rangeChange(dates) {
				console.log('Selected week range:', dates);
				// Update the date range for API calls
				this.dateStart = dates[0];
				this.dateEnd = dates[1];
				
				// Reset pagination
				this.currentPage = 1;
				this.data = [];
				this.isShowLoadMore = true;
				
				// Fetch new data with selected date range
				this.handelDrill();
			},
		},

		// 添加组件销毁时的清理
		beforeDestroy() {
			// 移除事件监听
			uni.$off('updateDrillList');
			// 恢复原始的 console.log
			console.log = console.log;
		},
		watch: {
			selectedDate: {
				handler(newVal) {
					console.log('newVal', newVal);
					// 使用 nextTick 等待日期数组更新
					this.$nextTick(() => {
						// 获取控制台输出的日期数组
						const consoleOutput = console.log.toString();
						const match = consoleOutput.match(/\['(.*?)', '(.*?)'\]/);
						if (match) {
							this.dateStart = match[1];
							this.dateEnd = match[2];
							
							// 重置分页和数据
							this.currentPage = 1;
							this.data = [];
							this.isShowLoadMore = true;
							
							// 调用API获取新日期的数据
							this.handelDrill();
						}
					});
				},
				immediate: true
			}
		}
	}
</script>

<style>
	page {
		background: #16171b;
	}
</style>
<style scoped lang="scss">
	page {
		background: #16171b;
	}

	.filter {
		margin: 28rpx 0;
		height: 70rpx;
		display: flex;
		// background: #fff;
	}

	.select zxz-uni-data-select {
		border: none;
	}
	:deep(.uni-date__x-input){
		//background: #f00 !important;
		color:rgba(255,255,255,0.45);
	}
:deep(.uni-date-x){
	background: #26272b !important;
}
	uni-icons {
		width: 20rpx;
		height: 20rpx;
		color: #fff;
	}
.u-popup{
	// display: none;
}
	.sear_inp {
		width: 100%;
		border:none;
		padding: 12rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	  background: #26272b !important;
	}

	.select {
		flex: 1;
		border: none;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
	}
	.filter_icon{
		height: 42rpx;
		width: 28rpx;
		 padding: 14rpx;
		 border-radius: 10rpx;
		background: rgba(255, 255, 255, 0.08);
	}
	.filter_icon image {
		width: 28rpx;
		height: 28rpx;
		
	}

	::v-deep {
		.uni-card {
			margin: 8px !important;
			padding: 0 !important;

			.uni-card__content {
				padding: 0 !important;
			}
		}
	}

	text {
		color: #fff;
	}

	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		// display: flex;
		color: rgba(255, 255, 255, 0.8);
	}

	.sear_inp {
		flex:1;
		margin-right: 10rpx;
		// background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}

	.content {
			padding: 0 34rpx;
			// height: calc(100vh - 88rpx);
			box-sizing: border-box;
			background: #16171b;
			position: relative;
			
			/* 自定义下拉刷新样式 */
			:deep(.uni-scroll-view-refresher) {
				width: 100% !important;
				height: 20px;
				background: #16171b;
				display: flex;
				justify-content: center;
				align-items: center;
				
				/* 隐藏默认图标 */
				.uni-scroll-view-refresher__indicator-box {
					display: none;
				}
				
				/* 自定义文本 */
				.uni-scroll-view-refresher__indicator {
					&::before {
						content: '加载中';
						color: rgba(255, 255, 255, 0.8);
						font-size: 14px;
					}
				}
			}
			
			/* 自定义刷新文本样式 */
			.refresh-text {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 30px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: rgba(255, 255, 255, 0.8);
				font-size: 14px;
				background: #16171b;
				z-index: 100;
			}
			
			/* 隐藏默认的刷新图标 */
			:deep(.uni-scroll-view-refresher) {
				.uni-scroll-view-refresher__indicator-box {
					opacity: 0;
				}
			}
		}

		/* 自定义日历样式 */
		:deep(.u-calendar) {
			background-color: #26272b !important;
			
			.u-calendar__header {
				background-color: #26272b !important;
				color: rgba(255, 255, 255, 0.8) !important;
			}
			
			.u-calendar__content {
				background-color: #26272b !important;
			}
			
			.u-calendar__action {
				background-color: #26272b !important;
			}
			
			/* 确保所有日期都可见和可选 */
			.u-calendar__day {
				opacity: 1 !important;
				pointer-events: auto !important;
			}
			
			/* 突出显示可选日期 */
			.u-calendar__day-text {
				color: rgba(255, 255, 255, 0.8) !important;
			}
		}
	
	/* 日期选择器样式 */
	.date-picker {
		width: 100%;
	}
	
	/* 设置日期选择器的暗色主题 */
	:deep(.uni-date) {
		width: 100%;
		
		.uni-date__icon-clear {
			display: none;
		}
	}
	
	:deep(.uni-date-x--popup) {
		background-color: #26272b !important;
	}
	
	:deep(.uni-calendar-item__weeks-day) {
		color: rgba(255, 255, 255, 0.8) !important;
	}
	
	:deep(.uni-calendar-item__weeks-day-text) {
		color: rgba(255, 255, 255, 0.8) !important;
	}
	
	:deep(.uni-calender__body-date-item) {
		background-color: #3c9cff !important;
		color: #fff !important;
	}
	
</style>