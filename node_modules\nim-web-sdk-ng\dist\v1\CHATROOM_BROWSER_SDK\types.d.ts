export interface StrAnyObj {
    [key: string]: any;
}
export interface StrObj {
    [key: string]: string;
}
export declare enum EClientType {
    Android = 1,
    iOS = 2,
    PC = 4,
    WindowsPhone = 8,
    Web = 16,
    Server = 32,
    Mac = 64,
    HarmonyOS = 65
}
export declare type TClientType = keyof typeof EClientType;
export declare enum EMsgType {
    text = 0,
    image = 1,
    audio = 2,
    video = 3,
    geo = 4,
    notification = 5,
    file = 6,
    tip = 10,
    robot = 11,
    g2 = 12,
    custom = 100
}
export declare type TMsgType = keyof typeof EMsgType;
export interface Chatroom {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室 id
     * @locale
     *
     * @locale en
     * Chat room ID
     * @locale
     */
    id: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 名字
     * @locale
     *
     * @locale en
     * Chat room name
     * @locale
     */
    name: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室公告
     * @locale
     *
     * @locale en
     * Chat room Announcement
     * @locale
     */
    announcement: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 广播 url 地址
     * @locale
     *
     * @locale en
     * broadcast URL addres
     * @locale
     */
    broadcastUrl: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段
     * @locale
     *
     * @locale en
     * extension field
     * @locale
     */
    ext: string;
    createTime: number;
    updateTime: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 创建者
     * @locale
     *
     * @locale en
     * The creator of a chat room
     * @locale
     */
    creator: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否全体禁言
     * @locale
     *
     * @locale en
     * Whether a chat room is muted
     * @locale
     */
    mute: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 在线成员数量
     * @locale
     *
     * @locale en
     * The number of online members
     * @locale
     */
    onlineMemberNum: number;
}
export interface ChatroomMember {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 账号ID
     * @locale
     *
     * @locale en
     * account ID
     * @locale
     */
    account: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 昵称
     * @locale
     *
     * @locale en
     * nickname
     * @locale
     */
    nick: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 头像
     * @locale
     *
     * @locale en
     * avatar
     * @locale
     */
    avatar: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 成员类型
     * @locale
     *
     * @locale en
     * member type
     * @locale
     */
    type: 'unset' | 'restricted' | 'common' | 'owner' | 'manager' | 'guest' | 'anonymous';
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否是游客
     * @locale
     *
     * @locale en
     * Whether the member is a guest
     * @locale
     */
    guest?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否被拉黑
     * @locale
     *
     * @locale en
     * Whether the member is blocked
     * @locale
     */
    blacked?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否被禁言
     * @locale
     *
     * @locale en
     * Whether the member is muted
     * @locale
     */
    muted?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 级别
     * @locale
     *
     * @locale en
     * level
     * @locale
     */
    level?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否在线，只有固定成员才能离线, 对游客而言只能是在线
     * @locale
     *
     * @locale en
     * Whether the member is online. Only permanent members can be displayed offline. Guests have only the online state.
     * @locale
     */
    online: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 进入聊天室的时间, 如果离线, 无该字段
     * @locale
     *
     * @locale en
     * The time when a member joins a chatroom. The field is not displayed for offline members.
     * @locale
     */
    enterTime?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段
     * @locale
     *
     * @locale en
     * extension field
     * @locale
     */
    ext?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 更新时间
     * @locale
     * update time
     * @locale en
     * Update time
     * @locale
     */
    updateTime: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否被临时禁言
     * @locale
     *
     * @locale en
     * Whether the member is temporarily muted
     * @locale
     */
    tempMuted: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 临时禁言剩余时长
     * @locale
     *
     * @locale en
     * Remaining time of a temporary mute
     * @locale
     */
    tempMuteDuration: number;
}
export interface ConnectResult {
    chatroom: Chatroom;
    member: ChatroomMember;
}
export interface ChatroomMessage {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 聊天室的 id
     * @locale
     *
     * @locale en
     * Chat room ID
     * @locale
     */
    chatroomId: string;
    status: 'success' | 'fail' | 'sending';
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息发送方, 帐号
     * @locale
     *
     * @locale en
     * Message sender and account
     * @locale
     */
    from: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送方的设备类型
     * @locale
     *
     * @locale en
     * Device type of a sender
     * @locale
     */
    fromClientType?: TClientType;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息发送方的昵称
     * @locale
     *
     * @locale en
     * The nickname of a sender
     * @locale
     */
    fromNick?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息发送方的头像
     * @locale
     *
     * @locale en
     * The avatar of the sender
     * @locale
     */
    fromAvatar?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息发送方的扩展字段
     * @locale
     *
     * @locale en
     * The extension field for a sender
     * @locale
     */
    fromExt?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息时间戳
     * @locale
     *
     * @locale en
     * Message timestamp
     * @locale
     */
    time: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息类型
     * @locale
     *
     * @locale en
     * Message type
     * @locale
     */
    type: TMsgType;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息的文本内容
     * @locale
     *
     * @locale en
     * Text content of a message
     * @locale
     */
    body: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     *  消息的流向
     * 'in'表示此消息是收到的消息
     * 'out'表示此消息是发出的消息
     * @locale
     *
     * @locale en
     * The direction (received or sent) of messages
     * 'in' indicates a received message
     * 'out' indicates a sent message
     * @locale
     */
    flow: 'in' | 'out';
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否跳过存云端历史, false:不跳过,true:跳过存历史,默认false
     * @locale
     *
     * @locale en
     * Whether a message is stored in message history in the cloud:
     * False: store the message, True: does not store the message.
     * The default value is False.
     * @locale
     */
    skipHistory: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 附加内容，自定义，文件，geo 等类型都会附带这个
     * @locale
     *
     * @locale en
     * Attachment of a custom message, file message, or location message:
     * @locale
     */
    attach?: StrAnyObj;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * SDK端生成的消息id
     * @locale
     *
     * @locale en
     * The message ID generated by the SDK
     * @locale
     */
    idClient: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段
     * @locale
     *
     * @locale en
     * Extension field
     * @locale
     */
    ext?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否为重发的消息
     * @locale
     *
     * @locale en
     * Whether a message is resent
     * @locale
     */
    resend?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 服务器第三方回调的扩展字段
     * @locale
     *
     * @locale en
     * Extension field for webhooks
     * @locale
     */
    callbackExt?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 开发者自定义的消息子类型，格式为大于0的整数
     * @locale
     *
     * @locale en
     * Custom message subtype. an integer greater than 0.
     * @locale
     */
    subType?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 环境变量，用于指向不同的抄送、第三方回调等配置
     * @locale
     *
     * @locale en
     * Environment variable used for configurations, such as message routing and webhooks.
     * @locale
     */
    env?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 若有开启易盾反垃圾，并且反垃圾结果命中，通过这个参数返回
     * @locale
     *
     * @locale en
     * If GuardEase moderation is enabled, spam detection will be returned via this parameter.
     * @locale
     */
    yidunAntiSpamRes?: string;
}
export interface IBaseUploadFileOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * JS 的 File 对象。
     *
     * 浏览器专用
     * @locale
     *
     * @locale en
     * JavaScript (JS) File object.
     *
     * Browser specific
     * @locale
     */
    file?: File;
    /**
     * @deprecated Use {@link IBaseUploadFileOptions.file} instead.
     *
     * @Multi_Lang_Tag
     * @locale cn
     * 存储文件的 DOM 元素，与上面的 file 只要选填一个就可以了。
     *
     * 浏览器专用
     *
     * 如果传字符串，最后会以 document.getElementById('fileInput').files[0] 拿到 File 对象
     * 如果传的是 DOM 节点，最后以 fileInput.files[0] 拿到 File 对象
     * @locale
     *
     * @locale en
     * The DOM element that stores the file. You only need to specify the DOM element or the above-mentioned File.
     *
     * Browser specific
     *
     * If you input a string, you will get the File object using document.getElementById (' fileInput ').files[0].
     * If you input a DOM node, get the File object using fileInput.files [0].
     * @locale
     */
    fileInput?: string | HTMLInputElement;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 临时文件路径
     *
     * uni-app，RN，小程序等特殊的 JS 运行环境专用（chooseImage 拿到的临时路径）
     * @locale
     *
     * @locale en
     * Path of temporary files
     *
     * Dedicated for special JS running environments such as uni-app, React Native (RN), WeChat mini-program (temporary path obtained by chooseImage).
     * @locale
     */
    filePath?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * maxSize 限制文件大小。
     *
     * 只对浏览器生效。
     *
     * uni-app，小程序等，由于sdk只能得到一个 filePath 临时路径，不能得到整个文件的信息。
     * 所以请开发者自行在选择文件后进行判断，参考那些端的API如 wx.chooseImage，uni.chooseImage
     * @locale
     *
     * @locale en
     * maxSize limits file size.
     *
     * Only valid for browsers.
     *
     * For running environments such as uni-app and WeChat mini-programs, you cannot get the entire information of the file, because the SDK can only get one temporary file path.
     * So please make your judgment after selecting the file and refer to APIs such as “wx.chooseImage” and “uni.chooseImage”.
     * @locale
     */
    maxSize?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 存储场景，不传默认实例化配置，默认为"im"
     * @locale
     *
     * @locale en
     * Storage scene. Default value: "im "（the instantiation configuration）.
     * @locale
     */
    nosScenes?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 存储有效时间，不传则默认实例化配置
     *
     * 不得小于一天，单位秒
     * @locale
     *
     * @locale en
     * Storage validity period. Default value: the instantiation configuration.
     *
     * Cannot be less than one day (unit: second)
     * @locale
     */
    nosSurvivalTime?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上传进度
     * @locale
     *
     * @locale en
     * Upload progress
     * @locale
     */
    onUploadProgress?: (obj: FileProgressObject) => void;
}
/**
 * @Multi_Lang_Tag
 * @locale cn
 * 上传文件参数
 * @locale
 *
 * @locale en
 * Upload file parameters
 * @locale
 */
export interface IUploadFileOptions extends IBaseUploadFileOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 文件类型
     * @locale
     *
     * @locale en
     * File type
     * @locale
     */
    type?: 'image' | 'audio' | 'video' | 'file';
}
export interface IBaseSendMsgOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 本条消息的目标标签，符合该标签条件的成员才会收到消息通知；若缺失，则使用发送者登录时设置的notifyTargetTags，若仍缺失，则消息会发送给聊天室内的所有人
     * @locale
     *
     * @locale en
     * The notification-target tag of the current message. only members who meet the tag conditions will receive the message; if the notification-target tag of the current message is missing, the notifyTargetTags set by the sender when logging in will be used. If the latter is also missing, the message will be sent to everyone in the chatroom.
     * @locale
     */
    notifyTargetTags?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段
     * @locale
     *
     * @locale en
     * Extension field
     * @locale
     */
    ext?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否过客户端反垃圾
     * @locale
     *
     * @locale en
     * Whether to enable client anti-spam
     * @locale
     */
    clientAntiSpam?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息子类型，格式为大于0的整数，开发者可自定义
     * @locale
     *
     * @locale en
     * Message subtype. Format requirement: integer greater than 0
     * @locale
     */
    subType?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 易盾反作弊字段，长度限制1024，JSON字符串格式。如："{"email":"<EMAIL>","phone":"12345678901","token":"1234","extension":"hello"}"
     * @locale
     *
     * @locale en
     * GuardEase moderation field.  The value should be a JSON string not longer than 1’024 bytes. Example: "{"email":"<EMAIL>","phone":"12345678901","token":"1234","extension":"hello"}"
     * @locale
     */
    yidunAntiCheating?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 环境变量，用于指向不同的抄送、第三方回调等配置
     * @locale
     *
     * @locale en
     * Environment variable used for configurations such as data synchronization and third-party callbacks.
     * @locale
     */
    env?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 易盾反作弊扩展字段字段，2021-08-09 追加。限制 JSON 格式字符串，长度上限 1024
     * @locale
     *
     * @locale en
     * GuardEase moderation extension field, added on 2021-08-09. The value should be a JSON string not longer than 1024 bytes.
     * @locale
     */
    yidunAntiSpamExt?: string;
}
export interface ISendTextMsgOptions extends IBaseSendMsgOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息内容
     * @locale
     *
     * @locale en
     * Body of a message
     * @locale
     */
    body: string;
}
export interface ISendGeoLocationMsgOptions extends IBaseSendMsgOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息内容
     * @locale
     *
     * @locale en
     * Body of a message
     * @locale
     */
    body: {
        lng: number;
        lat: number;
        title: string;
    };
}
export interface ISendTipMsgOptions extends IBaseSendMsgOptions {
    body: string;
}
export interface ISendCustomMsgOptions extends IBaseSendMsgOptions {
    body: string;
}
export interface UploadFileResult {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 文件名
     * @locale
     *
     * @locale en
     * Name of a file
     * @locale
     */
    name: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 文件 url
     * @locale
     *
     * @locale en
     * URL to a file
     * @locale
     */
    url: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 文件后缀
     * @locale
     *
     * @locale en
     * File extension
     * @locale
     */
    ext: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 文件大小，单位字节
     * @locale
     *
     * @locale en
     * Size of a file in bytes
     * @locale
     */
    size?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 宽度
     * @locale
     *
     * @locale en
     * Width
     * @locale
     */
    w?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 高度
     * @locale
     *
     * @locale en
     * Height
     * @locale
     */
    h?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 音频/视频 文件的时长
     * @locale
     *
     * @locale en
     * The duration of an audio/video file
     * @locale
     */
    dur?: number;
    md5?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 图片的转向
     * @locale
     *
     * @locale en
     * The orientation of an image
     * @locale
     */
    orientation?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 音频解码格式
     * @locale
     *
     * @locale en
     * Audio decoding format
     * @locale
     */
    audioCodec?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 视频解码格式
     * @locale
     *
     * @locale en
     * Video decoding format
     * @locale
     */
    videoCodec?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 音视频文件的容器
     * @locale
     *
     * @locale en
     * Container for audio and video files
     * @locale
     */
    container?: string;
}
export interface FileProgressObject {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 总大小
     * @locale
     *
     * @locale en
     * Total size
     * @locale
     */
    total: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 已上传大小
     * @locale
     *
     * @locale en
     * Size of uploaded data
     * @locale
     */
    loaded: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 已上传进度
     * @locale
     *
     * @locale en
     * Upload progress
     * @locale
     */
    percentage: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 已上传进度的文本描述
     * @locale
     *
     * @locale en
     * Text description of upload progress
     * @locale
     */
    percentageText: string;
}
export interface IBaseSendFileOptions extends IBaseSendMsgOptions, IBaseUploadFileOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 如果之前已经上传好了文件，那么直接传入 body 字段
     * 接下来不会再上传它，而是直接发往 IM 服务器
     * @locale
     *
     * @locale en
     * If the uploadResult field has been got via the uploadFile API, the uploadResult field will be directly passed into the body field.
     * In this case, the uploadResult field will not be uploaded again. It will be sent directly to the IM server.
     * @locale
     */
    body?: UploadFileResult;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上传前回调事件
     * @locale
     *
     * @locale en
     * Callback before upload
     * @locale
     */
    onUploadStart?: (task: {
        abort: () => void;
        [key: string]: any;
    }) => void;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上传完成的回调
     * @locale
     *
     * @locale en
     * Callback upon upload completion.
     * @locale
     */
    onUploadDone?: (file: UploadFileResult) => void;
}
export interface ISendFileOptions extends IBaseSendFileOptions {
    type: 'image' | 'audio' | 'video' | 'file';
}
export interface IBaseMarkChatroomMemberOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 待设置的账号
     * @locale
     *
     * @locale en
     * Account to be set up
     * @locale
     */
    account: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * true表示添加, false表示移除
     *
     * 当有人被加为管理员时, 所有聊天室成员会收到类型为'addManager'的聊天室通知消息。
     * 当有人被移除管理员时, 所有聊天室成员会收到类型为'removeManager'的聊天室通知消息。
     *
     * 当有人被加为普通成员时, 所有聊天室成员会收到类型为'addCommon'的聊天室通知消息。
     * 当有人被移除普通成员时, 所有聊天室成员会收到类型为'removeCommon'的聊天室通知消息。
     *
     * 当有人被加入禁言名单时, 所有聊天室成员会收到类型为'gagMember'的聊天室通知消息。
     * 当有人被移除禁言名单时, 所有聊天室成员会收到类型为'ungagMember'的聊天室通知消息。
     *
     * 当有人被加入黑名单时, 所有聊天室成员会收到类型为'blackMember'的聊天室通知消息。
     * 当有人被移除黑名单时, 所有聊天室成员会收到类型为'blackMember'的聊天室通知消息。
     * @locale
     *
     * @locale en
     * true: assign a member administrator, false: unassign a member administrator
     *
     * If a member is assigned administrator, all members will receive a notification message of ' addManager ' type.
     * If a member is unassigned administrator, all members will receive a notification message of ' removeManager ' type.
     *
     * If a member is assigned regular member, all members members will receive a notification message of ' addCommon ' type.
     * If a member is unassigned regular member, all members members will receive a notification message of ' removeCommon ' type.
     *
     * If a member is muted, all members will receive a notification message of ' gagMember ' type.
     * If a member is unmuted, all members will receive a notification message of ' ungagMember ' type.
     *
     * If a member is added to the blocklist, all members will receive a notification message of ' blackMember ' type.
     * If a member is removed from the blocklist, all members will receive a notification message of ' unblackMember ' type.
     * @locale
     */
    isAdd: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段, 如果填了, 那么其它聊天室成员收到的聊天室通知消息的attach.custom的值为此字段
     *
     * 推荐使用 JSON 格式的字符串构建
     * @locale
     *
     * @locale en
     * Extension field. If the field value is filled in, the value of attach.custom of the chatroom notification messages received by other chatroom members is the filled-in value.
     *
     * It is recommended that you use JSON strings to construct the extension field.
     * @locale
     */
    ext: string;
}
export interface IMarkChatroomMemberOptions extends IBaseMarkChatroomMemberOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 等级
     * @locale
     *
     * @locale en
     * grade
     * @locale
     */
    level?: number;
    type: number;
}
export interface ISetMemberNormalOptions extends IBaseMarkChatroomMemberOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 等级
     * @locale
     *
     * @locale en
     * Level
     * @locale
     */
    level?: number;
}
