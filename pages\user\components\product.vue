<template>
	<custom-header  style="height: 88rpx;" title="产品咨询" showBack />
	<view class="content">
		<text class="center">{{title}}</text>
	</view>
</template>

<script>
	import customHeader  from '@/components/page/header.vue';
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				title:'正在开发中...'
			}
		},
		methods: {
			
		}
	}
</script>

<style scoped>
	.content{
		text-align: center;
		margin-top: 426rpx;
	}
.center{
	width: 100%;
	margin: auto;
	font-family: Lato;
	font-size: 32rpx;
	font-weight: normal;
	line-height: 44.8rpx;
	text-align: center;
	letter-spacing: 0px;
	/* Neutrals/Grey */
	color: rgba(255, 255, 255, 0.65);
}
</style>