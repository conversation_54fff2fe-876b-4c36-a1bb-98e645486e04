<template>
	<custom-header style="height: 88rpx;" title="施工详情" showBack />
	<view class="equip_content">
		<view class="equip_header">
			<text class="header_text">{{prolist.constructionName}}</text>
        <view class="headerfl">
			<view class="header_flex">
				<view>
					<view class="flex_label">施工任务编号</view>
					<view class="flex_value">{{prolist.constructionNumber}}</view>
				</view>
				<view>
					<view class="flex_label">施工任务负责人</view>
					<view class="flex_value">{{prolist.constructionPerson}}</view>
				</view>

			</view>
			<view class="header_flex" style="margin-left:120rpx ;">
				<view>
					<view class="flex_label">施工任务单位名称</view>
					<view class="flex_value">{{prolist.constructionUnit}}</view>
				</view>
				<view>
					<view class="flex_label">施工人员</view>
					<view class="flex_value">{{prolist.constructionCrews}}</view>
				</view>
			</view>
			</view>
		</view>
		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        :list="list4"
        lineWidth="0"
		
        lineColor="#177DDC"
        :activeStyle="{
            color: 'rgba(255,255,255,0.85)',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:10rpx 15px; padding-right: 15px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		
		<template v-if="currentTab == 0">
			<view class="content">
			 <view class="cont_flex">
			 	<view class="label">计划开始时间</view>
				<view class="value">{{prolist.startDate}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">计划结束时间</view>
			 	<view class="value">{{prolist.endDate}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">实际开始时间</view>
			 	<view class="value">{{prolist.actualStarttime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">实际结束时间</view>
			 	<view class="value">{{prolist.lastUsedTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">任务完成度</view>
			 	<view class="value">{{prolist.constructionProgress}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">验收任务负责人</view>
			 	<view class="value">{{prolist.acceptancePersonnel}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">验收时间</view>
			 	<view class="value">{{prolist.acceptanceDate}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">验收结果</view>
			 	<view class="value">{{getResText(prolist.results)}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">备注</view>
			 	<view class="value">{{prolist.remark}}</view>
			 </view>
			
			 </view>
		</template>
		
	
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			
		},
		data(){
			return{
				currentTab:0,
				id: null,
			list4:[  
			    { name: '数据记录' },  
			    // { name: '状态监控' },  
			    // { name: '历史记录' },  
			    // { name: '保养计划记录' },  
			    // { name: '设备寿命跟踪' },  
			    // { name: '设备转移记录' }
			],
			prolist:{
				constructionNumber: "CONSTRUC001",
				constructionName: "井筒施工",
				constructionUnit: "XXX矿工施工队",
				constructionPerson: "赵六",
				constructionCrews: "张三、李四、王五",
				startDate: "2023-12-23",
				endDate: "2024-12-26",
				actualStarttime:"2024-12-26",
				actualEndtime:"2024-12-26",
				constructionProgress:80,
				acceptancePersonnel:"陈琦",
				acceptanceDate:"2024-12-26",
				results:1,
				remark:"请注意施工安全"
			}
			}
		},
		onLoad(options) {
			this.id = options.id
			this.handelDetail()
		},
		onShow(){
			if (this.id) {
				// this.handelDetail()
			}
		},
		methods:{
			handleTabChange(index) {
				// console.log("indexaaa",index.index);
				this.currentTab = index.index; // 更新当前选中的标签索引
				// console.log(this.currentTab);
			},
			getResText(res){
				if(res===0){
					return '不通过'
				}else if(res===1){
					return '通过'
				}
			},
			async handelDetail() {
			
				try {
			
					const res = await Request.get('/construction/get_info', {
						id: this.id
					})
			
					if (res.status == 0) {
						console.log('返回数据', res);
						this.prolist = res.data;
			
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });
			
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
			
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			
			},
					
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	.equip_content{
		padding:0 32rpx ;
		padding-top: 1rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.tab_cont{
		// padding: 0 32rpx;
		margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx  12rpx 0 0;
		padding:0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		
	}
	.content{
		border-radius:  0 0 12rpx  12rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		// height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>