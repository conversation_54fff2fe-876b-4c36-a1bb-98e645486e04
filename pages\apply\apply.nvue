<template>
	<view class="container">
		<image 
			class="background-image" 
			src="/static/image/BG.png" 
			mode="aspectFill" 
			:style="{ width: screenWidth + 'px', height: screenHeight + 'px' }"
		/>
		<view class="content-wrapper">
			<view 
				 class="header"
				background="transparent"
				style="color: #fff;"
			> <text style=" font-size: 34rpx;color: rgba(255, 255, 255, 0.85);"></text></view>
		<scroll-view class="content" >
			<!-- 施工管理 -->
			<view class="menu-list">
				<text class="menu-header">施工管理</text>
				<view class="grid-list">		
					<view v-for="(item, index) in menuItems" :key="index" class="grid-item" @click="handleMenuClick(item)">
						<view class="icon-wrapper">
							<image class="icon" :src="item.icon" mode="aspectFit" />
						</view>
						<text class="item-text">{{item.text}}</text>
					</view>
				</view>
			</view>
			<!-- 资产管理 -->
			<view class="menu-list">
				<text class="menu-header">资产管理</text>
				<view class="grid-list">		
					<view v-for="(item, index) in proItems" :key="index" class="grid-item" @click="handleproClick(item)">
						<view class="icon-wrapper">
							<image class="icon" :src="item.icon" mode="aspectFit" />
						</view>
						<text class="item-text">{{item.text}}</text>
					</view>
				</view>
			</view>
			<!-- 施工管理 -->
			<view class="menu-list">
				<text class="menu-header">矿区信息</text>
				<view class="grid-list">		
					<view v-for="(item, index) in applyItems" :key="index" class="grid-item" @click="handleapplyClick(item)">
						<view class="icon-wrapper">
							<image class="icon" :src="item.icon" mode="aspectFit" />
						</view>
						<text class="item-text">{{item.text}}</text>
					</view>
				</view>
			</view>
			<!-- 其他 -->
			<view class="menu-list">
				<text class="menu-header">其他</text>
				<view class="grid-list">		
					<view v-for="(item, index) in Items" :key="index" class="grid-item" @click="handleClick(item)">
						<view class="icon-wrapper">
							<image class="icon" :src="item.icon" mode="aspectFit" />
						</view>
						<text class="item-text">{{item.text}}</text>
					</view>
				</view>
			</view>
		</scroll-view>
	</view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue';
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				screenWidth: 0,
				screenHeight: 0,
				menuItems: [
					
					{
						icon: '/static/image/apply/plan.png',
						text: '下发任务',
						path: '/pages/apply/components/plan'
					},
					// {
					// 	icon: '/static/image/apply/project.png',
					// 	text: '施工列表',
					// 	path: '/pages/apply/components/project'
					// },
					{
							icon: '/static/image/apply/slot.png',
							text: '打孔管理',
							path: '/pages/apply/components/project'
					},
					{
						icon: '/static/image/apply/slots.png',
						text: '施工记录',
						path: '/pages/apply/components/slot'
					},
					{
						icon: '/static/image/apply/commition.png',
						text: '信息上报',
						path: '/pages/apply/components/message'
					},
					
				],
				proItems: [{
						icon: '/static/image/apply/equip.png',
						text: '设备管理',
						path: '/pages/apply/components/equip'
					},
					{
						icon: '/static/image/apply/drill.png',
						text: '钻具管理',
						path: '/pages/apply/components/drill'
					},
					
				],
				applyItems: [
					{
						icon: '/static/image/apply/noodles.png',
						text: '采面管理',
						path: '/pages/apply/components/noodles'
					},
					{
						icon: '/static/image/apply/laneway.png',
						text: '巷道管理',
						path: '/pages/apply/components/laneway'
					},
					{
						icon: '/static/image/apply/drillsite.png',
						text: '钻场管理',
						path: '/pages/apply/components/drillsite'
					},
					// {
					// 	icon: '/static/image/apply/dig.png',
					// 	text: '矿区概括',
					// 	path: '/pages/apply/components/dig'
					// }
				],
				Items: [{
						icon: '/static/image/apply/conputer.png',
						text: '运维中心',
						path: '/pages/apply/components/computer'
					},
					{
						icon: '/static/image/apply/energency.png',
						text: '应急处理',
						path: '/pages/apply/components/emergency'
					},
					{
						icon: '/static/image/apply/log.png',
						text: '报表',
						path: '/pages/apply/components/report'
					},
					// {
					// 	icon: '/static/image/apply/phone.png',
					// 	text: '通讯呼叫',
					// 	path: '/pages/apply/components/phone'
					// }
				]
			}
		},
		created() {
			// 获取屏幕尺寸
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.windowWidth
			this.screenHeight = systemInfo.windowHeight
		},
		methods: {
			handleMenuClick(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			handleproClick(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			handleapplyClick(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			handleClick(item) {
				uni.navigateTo({
					url: item.path
				})
			}
		}
	}
</script>

<style>
	.container {
		flex: 1;
		position: relative;
	}
	
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
	}
	
	.content-wrapper {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		flex: 1;
	}
	
	
	.header {
		padding-top: 50rpx;
		/* height: 160rpx; */
		font-size: 34rpx;
		font-weight: 600;
	    text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		
		/* background-color: rgba(255,255,255,0.03); */
	}
	

	.content {
		padding-top: 52rpx;

			padding-left: 26rpx;

			padding-right: 26rpx;
	}
	
	.menu-list {
		margin-top: 44rpx;
		background-color: rgba(255, 255, 255, 0.0362);
		padding: 32rpx;
		border-radius: 12rpx;
	}
	
	.menu-header {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.7);
		margin-bottom: 20rpx;
	}
	
	.grid-list {
		flex-direction: row;
		flex-wrap: nowrap;
		justify-content: flex-start;
	}
	
	.grid-item {
		width: 160rpx;
		align-items: center;
		padding-top: 26rpx;
	}
	
	.icon-wrapper {
		width: 84rpx;
		height: 84rpx;
		background-color: rgba(255, 255, 255, 0.1);
		border-radius: 32rpx;
		align-items: center;
		justify-content: center;
		margin-bottom: 14rpx;
	}
	
	.icon {
		width: 36rpx;
		height: 36rpx;
	}
	
	.item-text {
		font-size: 21rpx;
		color: rgba(255, 255, 255, 0.85);
		text-align: center;
	}
</style>
