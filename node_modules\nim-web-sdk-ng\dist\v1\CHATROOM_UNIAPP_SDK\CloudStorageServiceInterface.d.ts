import { IUploadFileOptions, UploadFileResult } from './types';
/**
 * 调用方式:
 * ```js
 * chatroom.cloudStorage.uploadFile(options)
 * ```
 */
export interface CloudStorageServiceInterface {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上传文件
     * @locale
     *
     * @locale en
     * Upload files
     * @locale
     */
    uploadFile(options: IUploadFileOptions): Promise<UploadFileResult>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 若开启了融合存储，则默认返回文件的短链地址。若需要返回文件的原始链接，可以通过该接口获取
     *
     * ```js
     * const url = await chatroom.cloudStorage.getOriginUrl(shortLinkUrl)
     *
     * console.log('origin url is: ${url}')
     * ```
     * @locale
     *
     * @locale en
     * convert a short link to a long link
     * @locale
     */
    getOriginUrl(options: string): Promise<string>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 获取文件鉴权的token。
     *
     * 在云信后台可以配置上传文件是否需要鉴权。如果需要鉴权，可以通过这个接口获取token。
     *
     * - 若 type = 2，表示设置的为带有过期时间的全局 token
     * - 若 type = 3，表示设置的为文件级别的 url 鉴权
     *
     * ```js
     * const res = await chatroom.cloudStorage.getFileToken({
     *   type: 2
     * })
     *
     * console.log('url is: ${url}?token=${res.token}')
     * ```
     * @locale
     * @locale en
     * Get the token for the authorization for file access.
     * @locale
     */
    getFileToken(options: GetFileTokenOptions): Promise<GetFileTokenResult | void>;
}
export interface GetFileTokenOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 类型 2表示带过期时间的全局token鉴权，3表示文件级别的url鉴权
     * @locale
     *
     * @locale en
     * authentication type, 2: token-based authentication with a validity period, 3: URL-based authentication for access files
     * @locale
     */
    type: 2 | 3;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 如果type=3,是url鉴权，需要传url数组
     * @locale
     *
     * @locale en
     * If type=3, an array of URLs is required
     * @locale
     */
    urls?: string[] | string;
}
export interface GetFileTokenResult {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 类型 2表示带过期时间的全局token鉴权，3表示文件级别的url鉴权
     * @locale
     *
     * @locale en
     * authentication type, 2: token-based authentication with a validity period, 3: URL-based authentication for access files
     * @locale
     */
    type: 2 | 3;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 如果是url鉴权，就返回url数组对应的tokens
     * @locale
     *
     * @locale en
     * For type 3, tokens associated with the array of URLs are returned
     * @locale
     */
    tokens?: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 基于过期时间鉴权的token
     * @locale
     *
     * @locale en
     * token-based authentication
     * @locale
     */
    token?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * token的过期时间，单位s
     * @locale
     *
     * @locale en
     * the validity period of a token
     * @locale
     */
    ttl: number;
}
export interface NIMEModuleParamCloudStorageConfig {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * NOS上传地址（直传）
     * 小程序、UniApp上传地址
     * @locale
     *
     * @locale en
     * Address of NOS upload (direct transfer)
     * MiniApp, UniApp upload address
     * @locale
     */
    commonUploadHost?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 小程序、UniApp上传地址备用域名
     * @locale
     *
     * @locale en
     * MiniApp, UniApp upload backup address array
     * @locale
     */
    commonUploadHostBackupList?: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * NOS上传地址（分片）
     * @locale
     *
     * @locale en
     * NOS upload address (chunks)
     * @locale
     */
    chunkUploadHost?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送文件消息中文件的url的通配符地址，例：'https://{host}/{object}'
     * @locale
     *
     * @locale en
     * Wildcard address of the file URL in the file message, for example: 'https://{host}/{object}'.
     * @locale
     */
    uploadReplaceFormat?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 接收到文件消息的替换模版
     * 这个是用来接到消息后，要按一定模式替换掉文件链接的。给予一个安全下载链接。
     * 例：'https://{bucket}-nosdn.netease.im/{object}'
     * @locale
     *
     * @locale en
     * The template for the URL of the received file of a file message
     * If a file messages is received, the URL of a file is replaced with a specified patten for a secured download URL
     * Example: 'https://{bucket}-nosdn.netease.im/{object}'
     * @locale
     */
    downloadUrl?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 收到哪些host地址，需要替换成downloadUrl，例：收到nos.netease.com/{bucket}/{obj}
     * @locale
     *
     * @locale en
     * The received addresses to be replaced with downloadUrl, for example: nos.netease.com/{bucket}/{obj}.
     * @locale
     */
    downloadHostList?: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 服务器下发的域名存在，并且对象前缀匹配成功，那么强行替换为`${protocol}${serverCdnDomain}/${decodePath.slice(prefixIndex)}`
     * @locale
     *
     * @locale en
     * If the domain name issued by the server exists and the object prefix matches, it will be forcibly replaced with `${protocol}${serverCdnDomain}/${decodePath.slice(prefixIndex)}`
     * @locale
     */
    nosCdnEnable?: boolean;
    /**
     * NOS 上传专用的 cdn 配置
     */
    cdn?: {
        /**
         * 默认的下载域名
         */
        defaultCdnDomain?: string;
        /**
         * 下载域名
         */
        cdnDomain?: string;
        /**
         * 桶名, 一般 NOS 默认为 "nim"
         */
        bucket?: string;
        /**
         * 路径前缀，一般不需要填写
         */
        objectNamePrefix?: string;
    };
    /**
     * amazon aws s3 sdk
     *
     * 注：若传入 s3 sdk 后，本 SDK 根据融合存储策略配置，可能会 new 创建出它的实例并使用它的实例方法进行上传/存储。
     */
    s3?: any;
    /**
     * localStorage 缓存的云存储配置的键名的前缀。默认叫 NIMClient
     *
     * 注: 举个例子，根据默认配置，策略缓存的键叫 'NIMClient-AllGrayscaleConfig'。
     */
    storageKeyPrefix?: string;
    /**
     * 是否需要开启融合存储整个策略。默认为 true
     *
     * 注: 为 false 则不会进行 lbs 灰度开关和策略获取，直接退化到老的 nos 上传逻辑。
     */
    isNeedToGetUploadPolicyFromServer?: boolean;
}
