<template>
	<custom-header  style="height: 88rpx;" title="邮箱" showBack rightContent="提交" rightType="text"  :rightClick="handleSubmit" />
	<view class="address_cont">
		<view class="add_count">
			<input type="text" v-model="email" class="address_text" placeholder="请输入邮箱" />
			<image v-show="resetshow" class="address_img" src="../../../../static/image/user/close.png" mode="" @click="reset"></image>
		</view>
		
	</view>
</template>

<script>
	import customHeader  from '@/components/page/header.vue';
	import Quene from '@/components/utils/queue';
	import Request from '@/components/utils/request';
	export default {
		components:{
			customHeader 
		},
		
		data() {
			return {
				resetshow:false,
				email:'',
				userInfo: {
					// avatar: '/static/image/user/avatar.png',
					// email: '<EMAIL>',
					// country: '中国',
					// region: '河南 郑州',
					// address: '商鼎路01号'
				}
			}
		},

		watch: {
			email(newVal) {
				this.resetshow = newVal.trim() !== '';
			}
		},

		onShow() {
			this.userInfo=Quene.getData('userinfo');
		},
		methods: {
			
			async handleSubmit(){
				if(!this.email.trim()){
					uni.showToast({
					    title: '请输入邮箱',
						icon: 'none'
					});
					return;
				}
				
				// 验证邮箱
				if(!this.validateEmail()) {
					uni.showToast({
						title: '请输入正确的邮箱',
						icon: 'none'
					});
					return;
				}
				
				try {
					const res = await Request.post('/personal/post_modify', {
						email: this.email
					});
				
					if (res.status === 0) {
						uni.showToast({
							title: '修改成功',
							icon: 'none',
							duration: 2000
						});
						
						// 使用事件通知更新邮箱
						uni.$emit('updateUserInfo', {
							email: this.email
						});
						
						// 返回上一页
						uni.navigateBack();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('修改失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
			
			reset(){
				this.email = '';
			
			},
			
			validateEmail(){
				const emailReg = /^[a-zA-Z0-9._-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,4}$/;
				return emailReg.test(this.email);
			}
		}

	}
</script>

<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	.address_cont{
		padding-top: 188rpx;
	
	}
	.add_count{
		display: flex;
		align-items: center;
		padding:0 32rpx;
		background: rgba(255, 255, 255, 0.03);
		border: 0px solid rgba(255, 255, 255, 0.0972);
	}
	.address_text{
		flex:1;
		height: 108rpx;
		font-size: 34rpx;
		color:rgba(255, 255, 255, 0.65) ;
		// background: rgba(255, 255, 255, 0.03);
		box-sizing: border-box;
		border: 0px solid rgba(255, 255, 255, 0.0972);
	}
	.address_img{
		width: 48rpx;
		height: 48rpx;
	}
</style>