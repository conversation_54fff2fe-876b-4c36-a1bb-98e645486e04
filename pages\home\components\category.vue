<template>
	<view style="height: 750rpx">
		<l-echart ref="chart"></l-echart>
	</view>
</template>

<script>
	import * as echarts from 'echarts';
	export default {
		props: {
			cateData: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {
			}
		},
		mounted() {
			this.$refs.chart.init(echarts, chart => {
				let option = {
					legend: {
						// orient: 'vertical',
						// top:'top',
						bottom: 'bottom',
						textStyle:{
							color:'rgba(255,255,255,0.85)',
						}
					},
					tooltip: {},
					dataset: {
						source: [
							['product', '用电量', '用水量'],
							...this.cateData  // 使用传入的cateData数据
						]
					},
					label:{
						color:'rgba(255,255,255,0.85)',
					},
					xAxis: {
						type: 'category',
						axisLabel:{
							color:'rgba(255,255,255,0.85)'
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
							}
						}
					},
					yAxis: {
						axisLabel:{
							color:'rgba(255,255,255,0.85)'
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color: '#4f4f4f'
							}
						}
					},
					// Declare several bar series, each will be mapped
					// to a column of dataset.source by default.
					series: [
						{
							type: 'bar'
						},
						{
							type: 'bar'
						},

					]
				}
				chart.setOption(option);

			});
		}
	}
</script>