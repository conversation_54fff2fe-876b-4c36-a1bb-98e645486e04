<template>
	<custom-header  style="height: 88rpx;" title="手机号" showBack />
		<view class="container">
			<!-- 手机号输入框 -->
			<view class="input-group">
				<view class="input-left">
					<text>手机号</text>
					<input type="number"
						v-model="phone"
						maxlength="11"
						placeholder="请输入手机号"
						
						class="input-box"/>
				</view>
				<text class="verify-btn" v-if="!isSendSms" @click="sendSms">获取验证码</text>
				<text class="verify-btn" v-if="isSendSms">{{sms_seconds}}秒后重发</text>
					
			</view>
			
			<!-- 验证码输入框和获取验证码按钮 -->
			<view class="input-group">
				<view class="input-left">
					<text>验证码</text>
					<input type="number"
						v-model="verifyCode"
						maxlength="6"
						placeholder="请输入验证码"
						class="input-box verify-input"/>
				</view>
							
			</view>
			<button class="btn"  @click="handleUpdatePhone">确定</button>
		</view>
</template>

<script>
	import Quene from '@/components/utils/queue';
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request';
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				phone: '', // 手机号
				verifyCode: '', // 验证码
				isSendSms: false, //是否已发送验证码
				sms_seconds: 60, //验证码倒计时秒数
				sms_interval_id: '', //验证码倒计时ID
				userInfo:{}
			}
		},
		onLoad(options) {
			// 获取传递过来的手机号
			if (options.phone) {
				this.phone = options.phone;
			}
		},
		onShow(){
			this.userInfo = Quene.getData('userinfo');
		},
		onHide() {
			let that = this;
			
			clearInterval(that.sms_interval_id);
		},
		onUnload() {
			let that = this;
			
			clearInterval(that.sms_interval_id);
		},
		methods: {
			// 获取验证码
			async sendSms() {
				if (!this.phone.trim()) {
				    uni.showToast({
				        title: '请输入手机号',
				        icon: 'none'
				    })
				    return
				}
				// 验证手机号
				if(!this.validatePhone()) {
					uni.showToast({
						title: '请输入正确的手机号',
						icon: 'none'
					});
					return;
				}
				
				try {
					var that = this;
					
					let post_data = {
						phoneNumber: that.phone,
					};
					const res = await Request.post('/sms/post_send_sms', post_data)

						if (res.status == 0) {
							that.isSendSms = true;
							uni.showToast({
								title: '验证码已发送',
								icon: 'none',
								duration: 2000
							})
							that.sms_interval_id = setInterval(()=>{
								if (that.sms_seconds > 0) {
									that.sms_seconds = that.sms_seconds - 1;
								} else {
									that.isSendSms = false;
									clearInterval(that.sms_interval_id);
									that.sms_seconds = 60;
								}
							}, 1000);
						} else {
						// 失败
						uni.showToast({
							title: '获取验证码失败，请稍后重试',
							icon: 'none',
							duration: 2000
						});
					}
					
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
			
			// 更换手机号
			async handleUpdatePhone() {
				if (!this.phone.trim()) {
					uni.showToast({
						title: '请输入手机号',
						icon: 'none'
					});
					return;
				}
				if (!this.verifyCode.trim()) {
					uni.showToast({
						title: '请输入验证码',
						icon: 'none'
					});
					return;
				}
				
				try {
					const res = await Request.post('/personal/post_modify', {
						phone: this.phone,
						verifyCode: this.verifyCode
					});
					
					if (res.status === 0) {
						uni.showToast({
							title: '修改成功',
							icon: 'none',
							duration: 2000
						});
						
						// 使用事件通知更新手机号
						uni.$emit('updateUserInfo', {
							phone: this.phone
						});
						
						// 返回上一页
						uni.navigateBack();
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('修改失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
			
			// 验证手机号
			validatePhone() {
				const phoneReg = /^1[3-9]\d{9}$/;
				return phoneReg.test(this.phone);
			}
		}
		
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style lang="scss" scoped>
.container {
	padding:0 40rpx;
	padding-top: 156rpx;
}

.input-group {
	display: flex;
	height: 120rpx;
	flex-direction: row;
	padding: 0 32rpx;
	align-items: center;
	justify-content: space-between;
	margin-top: 48rpx;
	border-radius: 24rpx;
	background: rgba(255, 255, 255, 0.13);
	box-sizing: border-box;
	//border: 2rpx solid;
	//border-image: linear-gradient(259deg, rgba(109, 150, 255, 0.44) 2%, rgba(214, 125, 255, 0.53) 96%) 1;
	backdrop-filter: blur(20rpx);
}
.input-left{
	text{
		letter-spacing: 0.4rpx;
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.45);
	}
}
.input-box {
	flex: 1;
	line-height: 40rpx;
	border-radius: 4px;
	font-size: 28rpx;
	margin-top: 4rpx;
	letter-spacing: 0.4rpx;
	color: rgba(255, 255, 255, 0.95);
}

.verify-btn {
	width: 240rpx;
	line-height: 40rpx;
	font-size: 24rpx;
	color: #fff;
	letter-spacing: 0.4rpx;
	background-color: rgba(0, 0, 0, 0);
	border: none;
	text-align: right;
	border-radius: 4px;
	padding: 0;
}
.btn{
	margin-top: 92rpx;
	font-family: Inter;
	font-size: 40rpx;
	font-weight: 500;
	height: 96rpx;
	letter-spacing: 0.4rpx;
	color: #FFFFFF;
	border-radius: 16rpx;
	background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
}
.verify-btn.counting-down {
	background-color: #999;
}
</style>