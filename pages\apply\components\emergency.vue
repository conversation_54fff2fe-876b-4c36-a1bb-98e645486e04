<template>
	<custom-header style="height: 88rpx;" title="应急处理" showBack2 />
	<view class="" style="padding-top: 156rpx;">
	<scroll-view 
		class="content" 
		scroll-y 
		@refresherrefresh="onRefresh"
		@refresherpulling="onPulling"
		:refresher-enabled="true"
		:refresher-triggered="isRefreshing"
		refresher-background="#16171b"
		:refresher-threshold="30"
		refresher-default-style="black"
	>
		<view class="search-bar">
			<up-input 
			class="sear_inp" 
			placeholder="应急事件名称" 
			prefixIcon="search" 
			v-model="searchText"
			:clearable="true"
			@change="handleSearchInput"
			@clear="handleClear"
			color="rgba(255,255,255,0.8)" prefixIconStyle="font-size: 22px;color: #909399"></up-input>
			<!-- <view class="filter_icon" @click="handelSearch">
				<image src="@/static/image/apply/search.png" mode=""></image>
			</view> -->
		</view>
		
		<view style="height:600px;magin:10rpx;magin-bottom:0;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
			<zb-table 
				@sort-change="sortChange" 
				:pullUpLoading="pullUpLoading" 
				:isShowLoadMore="true"
				:highlight="true" 
				:show-header="true" 
				:columns="column" 
				:fit="false" 
				:permissionBtn="permissionBtn"
				:stripe="true" 
				row-key="id" 
				@rowClick="rowClick" 
				:border="false"  
				@edit="buttonEdit"
				:data="data"
				v-bind="$attrs"
			></zb-table>
		</view>
	</scroll-view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request.js'
	
	export default {
		inheritAttrs: false,
		components: {
			customHeader
		},
		data() {
			const baseData = {
				emergencyCode: "EMERGENCY001",
				emergencyName: "瓦斯泄露",
				emergencyType: 0,
				emergencyLevel: 2,
				emergencyUrl: "详见附件",
				alarmTime: "2024-11-11",
				creatAt:"2024-10-10",
				updateAt:"2024-11-21"
			};
			
			const generateData = () => {
				const repeatedData = [];
				for (let i = 0; i < 10; i++) {
					repeatedData.push({
						id: i,
						...baseData
					})
				}
				return repeatedData;
			}
			
			return {
				searchText: '',
				column: [
					{
						name: 'emergencyCode',
						label: '应急事件编号',
						fixed: true,
						width: 150,
						align: 'center',
						emptyString: '--'
					},
					{
						name: 'emergencyName',
						label: '应急事件名称',
						width:130
					},
					{
						name: 'emergencyType',
						label: '应急事件类型',
						width:130,
						filters: {
						  0: '技术事故',
						  1: '设备老旧'
						},
					},
					{
						name: 'emergencyLevel',
						label: '应急响应级别',
						// sorter: true,
						width:130,
						filters: {
						  0: '1级',
						  1: '2级',
						  2: '3级'
						},
					},
					{
						name: 'emergencyUrl',
						label: '应急处理步骤',
						width:130
					},
					{
						name: 'alarmTime',
						label: '报警时间',
						width:130
					},
					{
						name: 'creatAt',
						label: '创建时间',
						width:117
					},
					{
						name: 'creatAt',
						label: '修改时间',
						width:117
					},
					{
						name: 'operation',
						type: 'operation',
						label: '操作',
						// width: 120,
						align: 'center',
						renders: [{
							name: '详情',
							class: 'edit',
							type: "primary",
							align: 'center',
							func: 'edit'
						}]
					},
				],
				// data: generateData(),
				data:[],
				flag1: true,
				currentPage: 1,
				perPage: 10,
				isRefreshing: false,
				isPulling: false,
			}
		},
		onLoad() {
			this.handelDrill();
			
			// 添加筛选更新事件监听
			uni.$on('updateEmergencyList', ({dateStart, dateEnd}) => {
				this.dateStart = dateStart;
				this.dateEnd = dateEnd;
				this.currentPage = 1;
				this.data = [];
				this.handelDrill();
			});
		},
		methods: {
			handleSearchInput(e) {
				// console.log('搜索输入值:', e);
				// 清除之前的定时器
				if(this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				
				// 设置新的定时器，延迟300ms执行搜索
				this.searchTimer = setTimeout(() => {
					this.searchText = e; // 更新搜索文本
					this.currentPage = 1; // 重置页码
					this.data = []; // 清空数据
					this.isShowLoadMore = true; // 重置加载更多
					this.handelDrill(); // 重新加载数据
				}, 300);
			},
			
			// 处理清空
			handleClear() {
				this.searchText = '';
				this.currentPage = 1;
				this.data = [];
				this.isShowLoadMore = true;
				this.handelDrill();
			},
			
			async handelDrill() {
				try {
					const params = {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord: this.searchText
					};
					
					const res = await Request.post('/emergency/get_ls', params);
					
					if (res.status == 0) {
						if (this.currentPage === 1) {
							this.data = res.data.items;
						} else {
							this.data = this.data.concat(res.data.items);
						}
						
						if (res.data.items.length < this.perPage) {
							this.isShowLoadMore = false;
						}
					} else {
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('Error loading data:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			},
			async pullUpLoading(done) {
				if (!this.flag1) {
					return
				}
				this.currentPage++;  // 页码加1
						try {
				           const res = await Request.post('/rod/get_ls', {
				              page: this.currentPage,
				              perPage: this.perPage,
							 keyWord:this.searchText,
							 dateStart:this.dateStart,
							 dateEnd:this.dateEnd
				            });
				
				           if (res.status == 0) {
							   console.log('加载获取数据',res.data);
							   // this.data.push(res.data.items);
							   console.log('data11111',this.data);
				           		if (res.data.items && res.data.items.length > 0) {
				           			this.data = this.data.concat(res.data.items);
				           			console.log('data11111',this.data);
				           			done(); // 通知 zb-table 加载完成
				           			
				           		}else{
				           		   done('ok'); // 通知zb-table 没有更多数据
				           		   this.flag1 = false
				           		   uni.showToast({
				           		   	title: '暂无更多数据' ,
				           		   	icon: 'none',
				           		   	duration: 1000
				           		   })
				           		}
				
				           } else {
				
						        //    uni.showToast({
								// 	title: '加载数据失败' ,
								// 	icon: 'none',
								// 	duration: 1000
								// })
						        done();       // 结束加载
				           }
						} catch (error) {
						    console.error("加载更多数据失败:", error);
						      // uni.showToast({
					        	// 	title: '加载数据失败' ,
					        	// 	icon: 'none',
					        	// 	duration: 1000
					        	// })
						        done();    //  结束加载
						}
				
			},
			permissionBtn(row, renders, rowIndex) {
				if (row.id == 2) {
					let arr = renders.filter(item => item.func === 'edit')
					return arr
				}
				return renders
			},
			sortChange(item, sorterMode, index) {
				console.log('触发排序', item, sorterMode, index);
			},
			
			handelSearch() {
				uni.navigateTo({
					url: '/pages/apply/components/emergency/search'
				})
			},
			
			buttonEdit(item, index) {
				uni.navigateTo({
					url: `/pages/apply/components/emergency/detail?id=${item.id}`
				})
			},
			
			rowClick(row, index) {
				uni.navigateTo({
					url: `/pages/apply/components/emergency/detail?id=${row.id}`
				})
			},
			
			async onRefresh() {
				this.isPulling = false;
				this.isRefreshing = true;
				
				try {
					this.currentPage = 1;
					await this.handelDrill();
					
					uni.showToast({
						title: '刷新成功',
						icon: 'none',
						duration: 1000
					});
				} catch (error) {
					console.error('刷新失败:', error);
					uni.showToast({
						title: '刷新失败',
						icon: 'none',
						duration: 1000
					});
				} finally {
					this.isRefreshing = false;
				}
			},
			
			onPulling(e) {
				this.isPulling = true;
			},
		},
		// 添加组件销毁时的清理
		beforeDestroy() {
			// 移除事件监听
			uni.$off('updateEmergencyList');
		},
	}
</script>

<style>
	page {
		background: #16171b;
	}
</style>
<style scoped lang="scss">
	page {
		background: #16171b;
	}

	.content {
		padding: 0 34rpx;
		box-sizing: border-box;
		background: #16171b;
		position: relative;
		
		:deep(.uni-scroll-view-refresher) {
			width: 100% !important;
			height: 20px;
			background: #16171b;
			display: flex;
			justify-content: center;
			align-items: center;
			
			.uni-scroll-view-refresher__indicator-box {
				display: none;
			}
			
			.uni-scroll-view-refresher__indicator {
				&::before {
					content: '加载中';
					color: rgba(255, 255, 255, 0.8);
					font-size: 14px;
				}
			}
		}
	}

	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
	}

	.sear_inp {
		flex: 1;
		margin-right: 10rpx;
		background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
		border:none;
	}

	.filter_icon {
		display: flex;
		align-items: center;
		height: 42rpx;
		width: 28rpx;
		padding: 14rpx;
		border-radius: 10rpx;
		background: rgba(255, 255, 255, 0.08);
		
		image {
			width: 28rpx;
			height: 28rpx;
		}
	}
</style>