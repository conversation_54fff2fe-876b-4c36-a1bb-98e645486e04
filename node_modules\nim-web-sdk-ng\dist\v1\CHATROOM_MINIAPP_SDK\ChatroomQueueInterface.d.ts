/**
 * 调用方式:
 * ```js
 * chatroom.chatroomQueue.update(options)
 * ```
 */
export interface ChatroomQueueInterface {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 初始化聊天室队列
     *
     * [聊天室队列](https://doc.yunxin.163.com/messaging-enhanced/docs/jEzNDIyNjY?platform=web#%E5%88%9D%E5%A7%8B%E5%8C%96%E8%81%8A%E5%A4%A9%E5%AE%A4%E9%98%9F%E5%88%97)指聊天室（房间）中由多个元素（key-value 键值对）构成的队列，应用于直播间中的连麦场景和礼物队列展示等场景。
     *
     * @example
     * ```js
     * chatroom.chatroomQueue.init({
     *     maxItemCount: 100
     * })
     * ```
     * @locale
     *
     * @locale en
     * initialize the chat room queue
     * @locale
     */
    init(options: QueueInitOptions): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 加入或者更新队列元素
     *
     * @example
     * ```js
     * chatroom.chatroomQueue.update({
     *  elementKey: 'uniKey',
     *  // 元素内容，长度限制4096字节
     *  elementValue: 'uniValue',
     *  // 可选参数，不传默认false，当提交这个新元素的用户从聊天室掉线或退出的时候，是否需要删除这个元素。
     *  transient: false,
     *  // 可选参数，队列元素所属账号，默认不传表示队列元素属于当前操作人，管理员可以指定队列元素归属于其他合法账号
     *  account: 'account'
     * })
     * ```
     * @locale
     *
     * @locale en
     * add or update the queue
     * @locale
     */
    update(options: QueueUpdateOptions): Promise<void>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 将指定元素（不传入参默认取出第一个元素）从聊天室队列中踢除
     *
     * @example
     * ```js
     * chatroom.chatroomQueue.poll({
     *    key: 'uniKey'
     * })
     * ```
     * @locale
     *
     * @locale en
     * Get the specified or first element of the queue
     * @locale
     */
    poll(options: QueuePollOptions): Promise<ChatroomQueue>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 批量更新至多 100 个聊天室队列的元素
     *
     * @example
     * ```js
     * chatroom.chatroomQueue.batchUpdate({
     *    // 批量更新元素的key-value对，key/value分别是elementKey和elementValue（elementKey 限制 128, elementValue 限制 4096 字节），一次最多更新100个
     *    itemList: {
     *      'uniKey1': 'uniValue1',
     *      'uniKey2': 'uniValue2'
     *    },
     *    // 可选参数，不传默认false，当设置为 true 时，所有聊天室成员会收到类型为'updateQueue'的聊天室通知消息。
     *    needNotify: false,
     *    // 通知中的自定义字段，长度限制2048
     *    notifyExtension: ''
     * })
     * ```
     * @locale
     *
     * @locale en
     * Update multiple elements in the queue at a time
     * @locale
     */
    batchUpdate(options: QueueBatchUpdateOptions): Promise<ChatroomQueue[]>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 排序列出所有元素
     *
     * @example
     * ```js
     * const elements = await chatroom.chatroomQueue.fetch()
     * ```
     * @locale
     *
     * @locale en
     * sort all elements in a queue
     * @locale
     */
    fetch(): Promise<ChatroomQueue[]>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 查看聊天室队列中的第一个元素
     * @locale
     *
     * @locale en
     * View the first element of the queue
     * @locale
     */
    pickHeader(): Promise<ChatroomQueue>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 清空队列
     * @locale
     *
     * @locale en
     * clear queues
     * @locale
     */
    clear(): Promise<void>;
}
export interface QueueInitOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 新建队列的长度限制(0~1000),可以对现有队列做此操作，修改现有队列的长度上限；当前队列如果已经超过了新的限制，元素不会减少，但是新元素无法增加
     * @locale
     *
     * @locale en
     * The limit of a new queue (0 ~ 1000). You can perform this operation for an existing queue to modify the length of the queue. If the current queue has exceeded the new limit, you cannot add more elements will not be added, but the existing elements cannot be removed.
     * @locale
     */
    maxItemCount: number;
}
export interface QueueUpdateOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 新元素的UniqKey，长度限制128字节
     * @locale
     *
     * @locale en
     * The UniqKey of the new element. Its length is limited to 128 bytes.
     * @locale
     */
    elementKey: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 新元素内容，长度限制4096字节
     * @locale
     *
     * @locale en
     * The content of the new element. Its length is limited to 4096 bytes.
     * @locale
     */
    elementValue: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 可选参数，不传默认false，当提交这个新元素的用户从聊天室掉线或退出的时候，是否需要删除这个元素。
     * @locale
     *
     * @locale en
     * (Optional parameter) When the user who submits this new element is offline or exits the chatroom, whether to delete this element. Default value: false.
     * @locale
     */
    transient?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 可选参数，队列元素所属账号，默认不传表示队列元素属于当前操作人，管理员可以指定队列元素归属于其他合法账号
     * @locale
     *
     * @locale en
     * (Optional parameter) The account to which the queue element belongs. By default, it means that the queue element belongs to the current operator. The administrator can specify that the queue element belongs to other valid accounts.
     * @locale
     */
    account?: boolean;
}
export interface QueuePollOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 需要取出的元素的 UniqKey, 传空传表示取出第一个元素
     * @locale
     *
     * @locale en
     * UniqKey of the element that needs to be taken out from the chatroom queue. If it is null, the first element will be taken out.
     * @locale
     */
    key?: string;
}
export interface QueueBatchUpdateOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 批量更新元素的key-value对，key/value分别是elementKey和elementValue（elementKey 限制 128, elementValue 限制 4096 字节），一次最多更新100个
     * @locale
     *
     * @locale en
     * Batch update key-value pairs of elements. The key is elementKey and the value is elementValue ( elementKey is limited to 128 bytes, elementValue is limited to 4096 bytes), and a maximum of 100 key-value pairs are updated at a time
     * @locale
     */
    itemList: {
        [key: string]: string;
    };
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否需要发送广播通知，可选参数，不传默认false，当设置为 true 时，所有聊天室成员会收到类型为'updateQueue'的聊天室通知消息。
     * @locale
     *
     * @locale en
     * (Optional parameter) Whether to send broadcast notifications. Default value: false. When the parameter is set to true, all chatroom members will receive notification messages of the 'updateQueue' type.
     * @locale
     */
    needNotify?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 通知中的自定义字段，长度限制2048
     * @locale
     *
     * @locale en
     * Custom fields in notifications. Length limit: 2048 bytes.
     * @locale
     */
    notifyExtension?: string;
}
export interface ChatroomQueue {
    [key: string]: string;
}
