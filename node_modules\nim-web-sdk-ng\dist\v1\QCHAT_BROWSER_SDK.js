/**
 * 
 *   Version: 10.8.30
 * 
 *   Git Hash: 53d9f639411d85eb37f1039804197c286cc2e303
 * 
 *   Created At: 4/24/2025, 7:32:47 AM
 * 
 *   Target: QCHAT_BROWSER_SDK.js
 *   
 */

!function(t,a){"object"==typeof exports&&"undefined"!=typeof module?module.exports=a():"function"==typeof define&&define.amd?define(a):(t="undefined"!=typeof globalThis?globalThis:t||self).QChat=a()}(this,(function(){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function createCommonjsModule(t){var a={exports:{}};return t(a,a.exports),a.exports}var a,u,h=getDefaultExportFromCjs(createCommonjsModule((function(t){t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports}))),check=function(t){return t&&t.Math==Math&&t},m=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof t&&t)||function(){return this}()||Function("return this")(),fails=function(t){try{return!!t()}catch(t){return!0}},g=!fails((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),E=Function.prototype,I=E.apply,M=E.call,S="object"==typeof Reflect&&Reflect.apply||(g?M.bind(I):function(){return M.apply(I,arguments)}),C=Function.prototype,T=C.bind,b=C.call,R=g&&T.bind(b,b),A=g?function(t){return t&&R(t)}:function(t){return t&&function(){return b.apply(t,arguments)}},isCallable=function(t){return"function"==typeof t},N=!fails((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),w=Function.prototype.call,O=g?w.bind(w):function(){return w.apply(w,arguments)},x={}.propertyIsEnumerable,k=Object.getOwnPropertyDescriptor,P={f:k&&!x.call({1:2},1)?function propertyIsEnumerable(t){var a=k(this,t);return!!a&&a.enumerable}:x},createPropertyDescriptor=function(t,a){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:a}},L=A({}.toString),q=A("".slice),classofRaw=function(t){return q(L(t),8,-1)},D=Object,V=A("".split),U=fails((function(){return!D("z").propertyIsEnumerable(0)}))?function(t){return"String"==classofRaw(t)?V(t,""):D(t)}:D,isNullOrUndefined=function(t){return null==t},G=TypeError,requireObjectCoercible=function(t){if(isNullOrUndefined(t))throw G("Can't call method on "+t);return t},toIndexedObject=function(t){return U(requireObjectCoercible(t))},B="object"==typeof document&&document.all,j=void 0===B&&void 0!==B?function(t){return"object"==typeof t?null!==t:isCallable(t)||t===B}:function(t){return"object"==typeof t?null!==t:isCallable(t)},W={},aFunction=function(t){return isCallable(t)?t:void 0},getBuiltIn=function(t,a){return arguments.length<2?aFunction(W[t])||aFunction(m[t]):W[t]&&W[t][a]||m[t]&&m[t][a]},Y=A({}.isPrototypeOf),$=getBuiltIn("navigator","userAgent")||"",H=m.process,Q=m.Deno,K=H&&H.versions||Q&&Q.version,z=K&&K.v8;z&&(u=(a=z.split("."))[0]>0&&a[0]<4?1:+(a[0]+a[1])),!u&&$&&(!(a=$.match(/Edge\/(\d+)/))||a[1]>=74)&&(a=$.match(/Chrome\/(\d+)/))&&(u=+a[1]);var J,X=u,Z=!!Object.getOwnPropertySymbols&&!fails((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&X&&X<41})),ee=Z&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,te=Object,re=ee?function(t){return"symbol"==typeof t}:function(t){var a=getBuiltIn("Symbol");return isCallable(a)&&Y(a.prototype,te(t))},ne=String,tryToString=function(t){try{return ne(t)}catch(t){return"Object"}},ae=TypeError,aCallable=function(t){if(isCallable(t))return t;throw ae(tryToString(t)+" is not a function")},getMethod=function(t,a){var u=t[a];return isNullOrUndefined(u)?void 0:aCallable(u)},oe=TypeError,ie=Object.defineProperty,se="__core-js_shared__",ce=m[se]||function(t,a){try{ie(m,t,{value:a,configurable:!0,writable:!0})}catch(u){m[t]=a}return a}(se,{}),le=createCommonjsModule((function(t){(t.exports=function(t,a){return ce[t]||(ce[t]=void 0!==a?a:{})})("versions",[]).push({version:"3.25.0",mode:"pure",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.0/LICENSE",source:"https://github.com/zloirock/core-js"})})),ue=Object,toObject=function(t){return ue(requireObjectCoercible(t))},de=A({}.hasOwnProperty),pe=Object.hasOwn||function hasOwn(t,a){return de(toObject(t),a)},he=0,me=Math.random(),fe=A(1..toString),uid=function(t){return"Symbol("+(void 0===t?"":t)+")_"+fe(++he+me,36)},ge=le("wks"),ve=m.Symbol,_e=ve&&ve.for,ye=ee?ve:ve&&ve.withoutSetter||uid,wellKnownSymbol=function(t){if(!pe(ge,t)||!Z&&"string"!=typeof ge[t]){var a="Symbol."+t;Z&&pe(ve,t)?ge[t]=ve[t]:ge[t]=ee&&_e?_e(a):ye(a)}return ge[t]},Ee=TypeError,Ie=wellKnownSymbol("toPrimitive"),toPrimitive=function(t,a){if(!j(t)||re(t))return t;var u,h=getMethod(t,Ie);if(h){if(void 0===a&&(a="default"),u=O(h,t,a),!j(u)||re(u))return u;throw Ee("Can't convert object to primitive value")}return void 0===a&&(a="number"),function(t,a){var u,h;if("string"===a&&isCallable(u=t.toString)&&!j(h=O(u,t)))return h;if(isCallable(u=t.valueOf)&&!j(h=O(u,t)))return h;if("string"!==a&&isCallable(u=t.toString)&&!j(h=O(u,t)))return h;throw oe("Can't convert object to primitive value")}(t,a)},toPropertyKey=function(t){var a=toPrimitive(t,"string");return re(a)?a:a+""},Me=m.document,Se=j(Me)&&j(Me.createElement),documentCreateElement=function(t){return Se?Me.createElement(t):{}},Ce=!N&&!fails((function(){return 7!=Object.defineProperty(documentCreateElement("div"),"a",{get:function(){return 7}}).a})),Te=Object.getOwnPropertyDescriptor,be={f:N?Te:function getOwnPropertyDescriptor(t,a){if(t=toIndexedObject(t),a=toPropertyKey(a),Ce)try{return Te(t,a)}catch(t){}if(pe(t,a))return createPropertyDescriptor(!O(P.f,t,a),t[a])}},Re=/#|\.prototype\./,isForced=function(t,a){var u=Ne[Ae(t)];return u==Oe||u!=we&&(isCallable(a)?fails(a):!!a)},Ae=isForced.normalize=function(t){return String(t).replace(Re,".").toLowerCase()},Ne=isForced.data={},we=isForced.NATIVE="N",Oe=isForced.POLYFILL="P",xe=isForced,ke=A(A.bind),functionBindContext=function(t,a){return aCallable(t),void 0===a?t:g?ke(t,a):function(){return t.apply(a,arguments)}},Pe=N&&fails((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),Le=String,qe=TypeError,anObject=function(t){if(j(t))return t;throw qe(Le(t)+" is not an object")},De=TypeError,Ve=Object.defineProperty,Ue=Object.getOwnPropertyDescriptor,Ge="enumerable",Fe="configurable",Be="writable",je={f:N?Pe?function defineProperty(t,a,u){if(anObject(t),a=toPropertyKey(a),anObject(u),"function"==typeof t&&"prototype"===a&&"value"in u&&Be in u&&!u.writable){var h=Ue(t,a);h&&h.writable&&(t[a]=u.value,u={configurable:Fe in u?u.configurable:h.configurable,enumerable:Ge in u?u.enumerable:h.enumerable,writable:!1})}return Ve(t,a,u)}:Ve:function defineProperty(t,a,u){if(anObject(t),a=toPropertyKey(a),anObject(u),Ce)try{return Ve(t,a,u)}catch(t){}if("get"in u||"set"in u)throw De("Accessors not supported");return"value"in u&&(t[a]=u.value),t}},We=N?function(t,a,u){return je.f(t,a,createPropertyDescriptor(1,u))}:function(t,a,u){return t[a]=u,t},Ye=be.f,wrapConstructor=function(t){var Wrapper=function(a,u,h){if(this instanceof Wrapper){switch(arguments.length){case 0:return new t;case 1:return new t(a);case 2:return new t(a,u)}return new t(a,u,h)}return S(t,this,arguments)};return Wrapper.prototype=t.prototype,Wrapper},_export=function(t,a){var u,h,g,E,I,M,S,C,T=t.target,b=t.global,R=t.stat,N=t.proto,w=b?m:R?m[T]:(m[T]||{}).prototype,O=b?W:W[T]||We(W,T,{})[T],x=O.prototype;for(g in a)u=!xe(b?g:T+(R?".":"#")+g,t.forced)&&w&&pe(w,g),I=O[g],u&&(M=t.dontCallGetSet?(C=Ye(w,g))&&C.value:w[g]),E=u&&M?M:a[g],u&&typeof I==typeof E||(S=t.bind&&u?functionBindContext(E,m):t.wrap&&u?wrapConstructor(E):N&&isCallable(E)?A(E):E,(t.sham||E&&E.sham||I&&I.sham)&&We(S,"sham",!0),We(O,g,S),N&&(pe(W,h=T+"Prototype")||We(W,h,{}),We(W[h],g,E),t.real&&x&&!x[g]&&We(x,g,E)))},$e=Math.ceil,He=Math.floor,Qe=Math.trunc||function trunc(t){var a=+t;return(a>0?He:$e)(a)},toIntegerOrInfinity=function(t){var a=+t;return a!=a||0===a?0:Qe(a)},Ke=Math.max,ze=Math.min,toAbsoluteIndex=function(t,a){var u=toIntegerOrInfinity(t);return u<0?Ke(u+a,0):ze(u,a)},Je=Math.min,lengthOfArrayLike=function(t){return(a=t.length)>0?Je(toIntegerOrInfinity(a),****************):0;var a},createMethod$4=function(t){return function(a,u,h){var m,g=toIndexedObject(a),E=lengthOfArrayLike(g),I=toAbsoluteIndex(h,E);if(t&&u!=u){for(;E>I;)if((m=g[I++])!=m)return!0}else for(;E>I;I++)if((t||I in g)&&g[I]===u)return t||I||0;return!t&&-1}},Xe={includes:createMethod$4(!0),indexOf:createMethod$4(!1)},Ze={},et=Xe.indexOf,tt=A([].push),objectKeysInternal=function(t,a){var u,h=toIndexedObject(t),m=0,g=[];for(u in h)!pe(Ze,u)&&pe(h,u)&&tt(g,u);for(;a.length>m;)pe(h,u=a[m++])&&(~et(g,u)||tt(g,u));return g},rt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],nt=Object.keys||function keys(t){return objectKeysInternal(t,rt)},at=N&&!Pe?Object.defineProperties:function defineProperties(t,a){anObject(t);for(var u,h=toIndexedObject(a),m=nt(a),g=m.length,E=0;g>E;)je.f(t,u=m[E++],h[u]);return t},ot={f:at},it=getBuiltIn("document","documentElement"),st=le("keys"),sharedKey=function(t){return st[t]||(st[t]=uid(t))},ct=sharedKey("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<script>"+t+"</"+"script>"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag("")),t.close();var a=t.parentWindow.Object;return t=null,a},NullProtoObject=function(){try{J=new ActiveXObject("htmlfile")}catch(t){}var t,a;NullProtoObject="undefined"!=typeof document?document.domain&&J?NullProtoObjectViaActiveX(J):((a=documentCreateElement("iframe")).style.display="none",it.appendChild(a),a.src=String("javascript:"),(t=a.contentWindow.document).open(),t.write(scriptTag("document.F=Object")),t.close(),t.F):NullProtoObjectViaActiveX(J);for(var u=rt.length;u--;)delete NullProtoObject.prototype[rt[u]];return NullProtoObject()};Ze[ct]=!0;var lt=Object.create||function create(t,a){var u;return null!==t?(EmptyConstructor.prototype=anObject(t),u=new EmptyConstructor,EmptyConstructor.prototype=null,u[ct]=t):u=NullProtoObject(),void 0===a?u:ot.f(u,a)};_export({target:"Object",stat:!0,sham:!N},{create:lt});var ut=W.Object,dt=function create(t,a){return ut.create(t,a)},pt=String,ht=TypeError,mt=Object.setPrototypeOf||("__proto__"in{}?function(){var t,a=!1,u={};try{(t=A(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(u,[]),a=u instanceof Array}catch(t){}return function setPrototypeOf(u,h){return anObject(u),function(t){if("object"==typeof t||isCallable(t))return t;throw ht("Can't set "+pt(t)+" as a prototype")}(h),a?t(u,h):u.__proto__=h,u}}():void 0);_export({target:"Object",stat:!0},{setPrototypeOf:mt});var ft=W.Object.setPrototypeOf,gt=A([].slice),vt=Function,_t=A([].concat),yt=A([].join),Et={},construct$8=function(t,a,u){if(!pe(Et,a)){for(var h=[],m=0;m<a;m++)h[m]="a["+m+"]";Et[a]=vt("C,a","return new C("+yt(h,",")+")")}return Et[a](t,u)},It=g?vt.bind:function bind(t){var a=aCallable(this),u=a.prototype,h=gt(arguments,1),m=function bound(){var u=_t(h,gt(arguments));return this instanceof m?construct$8(a,u.length,u):a.apply(t,u)};return j(u)&&(m.prototype=u),m};_export({target:"Function",proto:!0,forced:Function.bind!==It},{bind:It});var entryVirtual=function(t){return W[t+"Prototype"]},Mt=entryVirtual("Function").bind,St=Function.prototype,bind$1=function(t){var a=t.bind;return t===St||Y(St,t)&&a===St.bind?Mt:a},Ct=createCommonjsModule((function(t){function _setPrototypeOf(a,u){var h;return t.exports=_setPrototypeOf=ft?bind$1(h=ft).call(h):function _setPrototypeOf(t,a){return t.__proto__=a,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(a,u)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports})),Tt=getDefaultExportFromCjs(createCommonjsModule((function(t){t.exports=function _inheritsLoose(t,a){t.prototype=dt(a.prototype),t.prototype.constructor=t,Ct(t,a)},t.exports.__esModule=!0,t.exports.default=t.exports})));_export({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MAX_SAFE_INTEGER:****************});var bt={f:Object.getOwnPropertySymbols},Rt=Object.assign,At=Object.defineProperty,Nt=A([].concat),wt=!Rt||fails((function(){if(N&&1!==Rt({b:1},Rt(At({},"a",{enumerable:!0,get:function(){At(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},a={},u=Symbol(),h="abcdefghijklmnopqrst";return t[u]=7,h.split("").forEach((function(t){a[t]=t})),7!=Rt({},t)[u]||nt(Rt({},a)).join("")!=h}))?function assign(t,a){for(var u=toObject(t),h=arguments.length,m=1,g=bt.f,E=P.f;h>m;)for(var I,M=U(arguments[m++]),S=g?Nt(nt(M),g(M)):nt(M),C=S.length,T=0;C>T;)I=S[T++],N&&!O(E,M,I)||(u[I]=M[I]);return u}:Rt;_export({target:"Object",stat:!0,arity:2,forced:Object.assign!==wt},{assign:wt});var Ot=W.Object.assign,xt=!fails((function(){function F(){}return F.prototype.constructor=null,Object.getPrototypeOf(new F)!==F.prototype})),kt=sharedKey("IE_PROTO"),Pt=Object,Lt=Pt.prototype,qt=xt?Pt.getPrototypeOf:function(t){var a=toObject(t);if(pe(a,kt))return a[kt];var u=a.constructor;return isCallable(u)&&a instanceof u?u.prototype:a instanceof Pt?Lt:null},Dt=rt.concat("length","prototype"),Vt={f:Object.getOwnPropertyNames||function getOwnPropertyNames(t){return objectKeysInternal(t,Dt)}},Ut=A([].concat),Gt=getBuiltIn("Reflect","ownKeys")||function ownKeys(t){var a=Vt.f(anObject(t)),u=bt.f;return u?Ut(a,u(t)):a},Ft=Error,Bt=A("".replace),jt=String(Ft("zxcasd").stack),Wt=/\n\s*at [^:]*:[^\n]*/,Yt=Wt.test(jt),errorStackClear=function(t,a){if(Yt&&"string"==typeof t&&!Ft.prepareStackTrace)for(;a--;)t=Bt(t,Wt,"");return t},installErrorCause=function(t,a){j(a)&&"cause"in a&&We(t,"cause",a.cause)},$t={},Ht=wellKnownSymbol("iterator"),Qt=Array.prototype,isArrayIteratorMethod=function(t){return void 0!==t&&($t.Array===t||Qt[Ht]===t)},Kt={};Kt[wellKnownSymbol("toStringTag")]="z";var zt="[object z]"===String(Kt),Jt=wellKnownSymbol("toStringTag"),Xt=Object,Zt="Arguments"==classofRaw(function(){return arguments}()),er=zt?classofRaw:function(t){var a,u,h;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(u=function(t,a){try{return t[a]}catch(t){}}(a=Xt(t),Jt))?u:Zt?classofRaw(a):"Object"==(h=classofRaw(a))&&isCallable(a.callee)?"Arguments":h},tr=wellKnownSymbol("iterator"),getIteratorMethod$5=function(t){if(!isNullOrUndefined(t))return getMethod(t,tr)||getMethod(t,"@@iterator")||$t[er(t)]},rr=TypeError,getIterator=function(t,a){var u=arguments.length<2?getIteratorMethod$5(t):a;if(aCallable(u))return anObject(O(u,t));throw rr(tryToString(t)+" is not iterable")},iteratorClose=function(t,a,u){var h,m;anObject(t);try{if(!(h=getMethod(t,"return"))){if("throw"===a)throw u;return u}h=O(h,t)}catch(t){m=!0,h=t}if("throw"===a)throw u;if(m)throw h;return anObject(h),u},nr=TypeError,Result=function(t,a){this.stopped=t,this.result=a},ar=Result.prototype,iterate=function(t,a,u){var h,m,g,E,I,M,S,C=u&&u.that,T=!(!u||!u.AS_ENTRIES),b=!(!u||!u.IS_RECORD),R=!(!u||!u.IS_ITERATOR),A=!(!u||!u.INTERRUPTED),N=functionBindContext(a,C),stop=function(t){return h&&iteratorClose(h,"normal",t),new Result(!0,t)},callFn=function(t){return T?(anObject(t),A?N(t[0],t[1],stop):N(t[0],t[1])):A?N(t,stop):N(t)};if(b)h=t.iterator;else if(R)h=t;else{if(!(m=getIteratorMethod$5(t)))throw nr(tryToString(t)+" is not iterable");if(isArrayIteratorMethod(m)){for(g=0,E=lengthOfArrayLike(t);E>g;g++)if((I=callFn(t[g]))&&Y(ar,I))return I;return new Result(!1)}h=getIterator(t,m)}for(M=b?t.next:h.next;!(S=O(M,h)).done;){try{I=callFn(S.value)}catch(t){iteratorClose(h,"throw",t)}if("object"==typeof I&&I&&Y(ar,I))return I}return new Result(!1)},or=String,toString=function(t){if("Symbol"===er(t))throw TypeError("Cannot convert a Symbol value to a string");return or(t)},normalizeStringArgument=function(t,a){return void 0===t?arguments.length<2?"":a:toString(t)},ir=!fails((function(){var t=Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",createPropertyDescriptor(1,7)),7!==t.stack)})),sr=wellKnownSymbol("toStringTag"),cr=Error,lr=[].push,ur=function AggregateError(t,a){var u,h=arguments.length>2?arguments[2]:void 0,m=Y(dr,this);mt?u=mt(cr(),m?qt(this):dr):(u=m?this:lt(dr),We(u,sr,"Error")),void 0!==a&&We(u,"message",normalizeStringArgument(a)),ir&&We(u,"stack",errorStackClear(u.stack,1)),installErrorCause(u,h);var g=[];return iterate(t,lr,{that:g}),We(u,"errors",g),u};mt?mt(ur,cr):function(t,a,u){for(var h=Gt(a),m=je.f,g=be.f,E=0;E<h.length;E++){var I=h[E];pe(t,I)||u&&pe(u,I)||m(t,I,g(a,I))}}(ur,cr,{name:!0});var dr=ur.prototype=lt(cr.prototype,{constructor:createPropertyDescriptor(1,ur),message:createPropertyDescriptor(1,""),name:createPropertyDescriptor(1,"AggregateError")});_export({global:!0,constructor:!0,arity:2},{AggregateError:ur});var pr,hr,mr,fr=m.WeakMap,gr=isCallable(fr)&&/native code/.test(String(fr)),vr="Object already initialized",_r=m.TypeError,yr=m.WeakMap;if(gr||ce.state){var Er=ce.state||(ce.state=new yr),Ir=A(Er.get),Mr=A(Er.has),Sr=A(Er.set);pr=function(t,a){if(Mr(Er,t))throw _r(vr);return a.facade=t,Sr(Er,t,a),a},hr=function(t){return Ir(Er,t)||{}},mr=function(t){return Mr(Er,t)}}else{var Cr=sharedKey("state");Ze[Cr]=!0,pr=function(t,a){if(pe(t,Cr))throw _r(vr);return a.facade=t,We(t,Cr,a),a},hr=function(t){return pe(t,Cr)?t[Cr]:{}},mr=function(t){return pe(t,Cr)}}var Tr,br,Rr,Ar={set:pr,get:hr,has:mr,enforce:function(t){return mr(t)?hr(t):pr(t,{})},getterFor:function(t){return function(a){var u;if(!j(a)||(u=hr(a)).type!==t)throw _r("Incompatible receiver, "+t+" required");return u}}},Nr=Function.prototype,wr=N&&Object.getOwnPropertyDescriptor,Or=pe(Nr,"name"),xr={EXISTS:Or,PROPER:Or&&"something"===function something(){}.name,CONFIGURABLE:Or&&(!N||N&&wr(Nr,"name").configurable)},defineBuiltIn=function(t,a,u,h){return h&&h.enumerable?t[a]=u:We(t,a,u),t},kr=wellKnownSymbol("iterator"),Pr=!1;[].keys&&("next"in(Rr=[].keys())?(br=qt(qt(Rr)))!==Object.prototype&&(Tr=br):Pr=!0);var Lr=!j(Tr)||fails((function(){var t={};return Tr[kr].call(t)!==t}));Tr=Lr?{}:lt(Tr),isCallable(Tr[kr])||defineBuiltIn(Tr,kr,(function(){return this}));var qr={IteratorPrototype:Tr,BUGGY_SAFARI_ITERATORS:Pr},Dr=zt?{}.toString:function toString(){return"[object "+er(this)+"]"},Vr=je.f,Ur=wellKnownSymbol("toStringTag"),setToStringTag=function(t,a,u,h){if(t){var m=u?t:t.prototype;pe(m,Ur)||Vr(m,Ur,{configurable:!0,value:a}),h&&!zt&&We(m,"toString",Dr)}},Gr=qr.IteratorPrototype,returnThis$1=function(){return this},Fr=xr.PROPER,Br=qr.BUGGY_SAFARI_ITERATORS,jr=wellKnownSymbol("iterator"),Wr="keys",Yr="values",$r="entries",returnThis=function(){return this},iteratorDefine=function(t,a,u,h,m,g,E){!function(t,a,u,h){var m=a+" Iterator";t.prototype=lt(Gr,{next:createPropertyDescriptor(+!h,u)}),setToStringTag(t,m,!1,!0),$t[m]=returnThis$1}(u,a,h);var I,M,S,getIterationMethod=function(t){if(t===m&&A)return A;if(!Br&&t in b)return b[t];switch(t){case Wr:return function keys(){return new u(this,t)};case Yr:return function values(){return new u(this,t)};case $r:return function entries(){return new u(this,t)}}return function(){return new u(this)}},C=a+" Iterator",T=!1,b=t.prototype,R=b[jr]||b["@@iterator"]||m&&b[m],A=!Br&&R||getIterationMethod(m),N="Array"==a&&b.entries||R;if(N&&(I=qt(N.call(new t)))!==Object.prototype&&I.next&&(setToStringTag(I,C,!0,!0),$t[C]=returnThis),Fr&&m==Yr&&R&&R.name!==Yr&&(T=!0,A=function values(){return O(R,this)}),m)if(M={values:getIterationMethod(Yr),keys:g?A:getIterationMethod(Wr),entries:getIterationMethod($r)},E)for(S in M)(Br||T||!(S in b))&&defineBuiltIn(b,S,M[S]);else _export({target:a,proto:!0,forced:Br||T},M);return E&&b[jr]!==A&&defineBuiltIn(b,jr,A,{name:m}),$t[a]=A,M};je.f;var Hr="Array Iterator",Qr=Ar.set,Kr=Ar.getterFor(Hr);iteratorDefine(Array,"Array",(function(t,a){Qr(this,{type:Hr,target:toIndexedObject(t),index:0,kind:a})}),(function(){var t=Kr(this),a=t.target,u=t.kind,h=t.index++;return!a||h>=a.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==u?{value:h,done:!1}:"values"==u?{value:a[h],done:!1}:{value:[h,a[h]],done:!1}}),"values"),$t.Arguments=$t.Array;var zr="process"==classofRaw(m.process),Jr=wellKnownSymbol("species"),setSpecies=function(t){var a=getBuiltIn(t),u=je.f;N&&a&&!a[Jr]&&u(a,Jr,{configurable:!0,get:function(){return this}})},Xr=TypeError,anInstance=function(t,a){if(Y(a,t))return t;throw Xr("Incorrect invocation")},Zr=A(Function.toString);isCallable(ce.inspectSource)||(ce.inspectSource=function(t){return Zr(t)});var en=ce.inspectSource,noop=function(){},tn=[],rn=getBuiltIn("Reflect","construct"),nn=/^\s*(?:class|function)\b/,an=A(nn.exec),sn=!nn.exec(noop),cn=function isConstructor(t){if(!isCallable(t))return!1;try{return rn(noop,tn,t),!0}catch(t){return!1}},ln=function isConstructor(t){if(!isCallable(t))return!1;switch(er(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return sn||!!an(nn,en(t))}catch(t){return!0}};ln.sham=!0;var un,dn,pn,hn,mn=!rn||fails((function(){var t;return cn(cn.call)||!cn(Object)||!cn((function(){t=!0}))||t}))?ln:cn,fn=TypeError,aConstructor=function(t){if(mn(t))return t;throw fn(tryToString(t)+" is not a constructor")},gn=wellKnownSymbol("species"),speciesConstructor=function(t,a){var u,h=anObject(t).constructor;return void 0===h||isNullOrUndefined(u=anObject(h)[gn])?a:aConstructor(u)},vn=TypeError,validateArgumentsLength=function(t,a){if(t<a)throw vn("Not enough arguments");return t},_n=/(?:ipad|iphone|ipod).*applewebkit/i.test($),yn=m.setImmediate,En=m.clearImmediate,In=m.process,Mn=m.Dispatch,Sn=m.Function,Cn=m.MessageChannel,Tn=m.String,bn=0,Rn={},An="onreadystatechange";try{un=m.location}catch(t){}var run=function(t){if(pe(Rn,t)){var a=Rn[t];delete Rn[t],a()}},runner=function(t){return function(){run(t)}},listener=function(t){run(t.data)},post=function(t){m.postMessage(Tn(t),un.protocol+"//"+un.host)};yn&&En||(yn=function setImmediate(t){validateArgumentsLength(arguments.length,1);var a=isCallable(t)?t:Sn(t),u=gt(arguments,1);return Rn[++bn]=function(){S(a,void 0,u)},dn(bn),bn},En=function clearImmediate(t){delete Rn[t]},zr?dn=function(t){In.nextTick(runner(t))}:Mn&&Mn.now?dn=function(t){Mn.now(runner(t))}:Cn&&!_n?(hn=(pn=new Cn).port2,pn.port1.onmessage=listener,dn=functionBindContext(hn.postMessage,hn)):m.addEventListener&&isCallable(m.postMessage)&&!m.importScripts&&un&&"file:"!==un.protocol&&!fails(post)?(dn=post,m.addEventListener("message",listener,!1)):dn=An in documentCreateElement("script")?function(t){it.appendChild(documentCreateElement("script")).onreadystatechange=function(){it.removeChild(this),run(t)}}:function(t){setTimeout(runner(t),0)});var Nn,wn,On,xn,kn,Pn,Ln,qn,Dn={set:yn,clear:En},Vn=/ipad|iphone|ipod/i.test($)&&void 0!==m.Pebble,Un=/web0s(?!.*chrome)/i.test($),Gn=be.f,Fn=Dn.set,Bn=m.MutationObserver||m.WebKitMutationObserver,jn=m.document,Wn=m.process,Yn=m.Promise,$n=Gn(m,"queueMicrotask"),Hn=$n&&$n.value;Hn||(Nn=function(){var t,a;for(zr&&(t=Wn.domain)&&t.exit();wn;){a=wn.fn,wn=wn.next;try{a()}catch(t){throw wn?xn():On=void 0,t}}On=void 0,t&&t.enter()},_n||zr||Un||!Bn||!jn?!Vn&&Yn&&Yn.resolve?((Ln=Yn.resolve(void 0)).constructor=Yn,qn=functionBindContext(Ln.then,Ln),xn=function(){qn(Nn)}):zr?xn=function(){Wn.nextTick(Nn)}:(Fn=functionBindContext(Fn,m),xn=function(){Fn(Nn)}):(kn=!0,Pn=jn.createTextNode(""),new Bn(Nn).observe(Pn,{characterData:!0}),xn=function(){Pn.data=kn=!kn}));var Qn=Hn||function(t){var a={fn:t,next:void 0};On&&(On.next=a),wn||(wn=a,xn()),On=a},perform=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},Queue=function(){this.head=null,this.tail=null};Queue.prototype={add:function(t){var a={item:t,next:null};this.head?this.tail.next=a:this.head=a,this.tail=a},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}};var Kn,zn,Jn=Queue,Xn=m.Promise,Zn="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,ea=!Zn&&!zr&&"object"==typeof window&&"object"==typeof document,ta=Xn&&Xn.prototype,ra=wellKnownSymbol("species"),na=!1,aa=isCallable(m.PromiseRejectionEvent),oa=xe("Promise",(function(){var t=en(Xn),a=t!==String(Xn);if(!a&&66===X)return!0;if(!ta.catch||!ta.finally)return!0;if(!X||X<51||!/native code/.test(t)){var u=new Xn((function(t){t(1)})),FakePromise=function(t){t((function(){}),(function(){}))};if((u.constructor={})[ra]=FakePromise,!(na=u.then((function(){}))instanceof FakePromise))return!0}return!a&&(ea||Zn)&&!aa})),ia={CONSTRUCTOR:oa,REJECTION_EVENT:aa,SUBCLASSING:na},sa=TypeError,PromiseCapability=function(t){var a,u;this.promise=new t((function(t,h){if(void 0!==a||void 0!==u)throw sa("Bad Promise constructor");a=t,u=h})),this.resolve=aCallable(a),this.reject=aCallable(u)},ca={f:function(t){return new PromiseCapability(t)}},la=Dn.set,ua="Promise",da=ia.CONSTRUCTOR,pa=ia.REJECTION_EVENT,ha=Ar.getterFor(ua),ma=Ar.set,fa=Xn&&Xn.prototype,ga=Xn,va=fa,_a=m.TypeError,ya=m.document,Ea=m.process,Ia=ca.f,Ma=Ia,Sa=!!(ya&&ya.createEvent&&m.dispatchEvent),Ca="unhandledrejection",isThenable=function(t){var a;return!(!j(t)||!isCallable(a=t.then))&&a},callReaction=function(t,a){var u,h,m,g=a.value,E=1==a.state,I=E?t.ok:t.fail,M=t.resolve,S=t.reject,C=t.domain;try{I?(E||(2===a.rejection&&onHandleUnhandled(a),a.rejection=1),!0===I?u=g:(C&&C.enter(),u=I(g),C&&(C.exit(),m=!0)),u===t.promise?S(_a("Promise-chain cycle")):(h=isThenable(u))?O(h,u,M,S):M(u)):S(g)}catch(t){C&&!m&&C.exit(),S(t)}},notify=function(t,a){t.notified||(t.notified=!0,Qn((function(){for(var u,h=t.reactions;u=h.get();)callReaction(u,t);t.notified=!1,a&&!t.rejection&&onUnhandled(t)})))},dispatchEvent=function(t,a,u){var h,g;Sa?((h=ya.createEvent("Event")).promise=a,h.reason=u,h.initEvent(t,!1,!0),m.dispatchEvent(h)):h={promise:a,reason:u},!pa&&(g=m["on"+t])?g(h):t===Ca&&function(t,a){var u=m.console;u&&u.error&&(1==arguments.length?u.error(t):u.error(t,a))}("Unhandled promise rejection",u)},onUnhandled=function(t){O(la,m,(function(){var a,u=t.facade,h=t.value;if(isUnhandled(t)&&(a=perform((function(){zr?Ea.emit("unhandledRejection",h,u):dispatchEvent(Ca,u,h)})),t.rejection=zr||isUnhandled(t)?2:1,a.error))throw a.value}))},isUnhandled=function(t){return 1!==t.rejection&&!t.parent},onHandleUnhandled=function(t){O(la,m,(function(){var a=t.facade;zr?Ea.emit("rejectionHandled",a):dispatchEvent("rejectionhandled",a,t.value)}))},bind=function(t,a,u){return function(h){t(a,h,u)}},internalReject=function(t,a,u){t.done||(t.done=!0,u&&(t=u),t.value=a,t.state=2,notify(t,!0))},internalResolve=function(t,a,u){if(!t.done){t.done=!0,u&&(t=u);try{if(t.facade===a)throw _a("Promise can't be resolved itself");var h=isThenable(a);h?Qn((function(){var u={done:!1};try{O(h,a,bind(internalResolve,u,t),bind(internalReject,u,t))}catch(a){internalReject(u,a,t)}})):(t.value=a,t.state=1,notify(t,!1))}catch(a){internalReject({done:!1},a,t)}}};da&&(va=(ga=function Promise(t){anInstance(this,va),aCallable(t),O(Kn,this);var a=ha(this);try{t(bind(internalResolve,a),bind(internalReject,a))}catch(t){internalReject(a,t)}}).prototype,(Kn=function Promise(t){ma(this,{type:ua,done:!1,notified:!1,parent:!1,reactions:new Jn,rejection:!1,state:0,value:void 0})}).prototype=defineBuiltIn(va,"then",(function then(t,a){var u=ha(this),h=Ia(speciesConstructor(this,ga));return u.parent=!0,h.ok=!isCallable(t)||t,h.fail=isCallable(a)&&a,h.domain=zr?Ea.domain:void 0,0==u.state?u.reactions.add(h):Qn((function(){callReaction(h,u)})),h.promise})),zn=function(){var t=new Kn,a=ha(t);this.promise=t,this.resolve=bind(internalResolve,a),this.reject=bind(internalReject,a)},ca.f=Ia=function(t){return t===ga||undefined===t?new zn(t):Ma(t)}),_export({global:!0,constructor:!0,wrap:!0,forced:da},{Promise:ga}),setToStringTag(ga,ua,!1,!0),setSpecies(ua);var Ta=wellKnownSymbol("iterator"),ba=!1;try{var Ra=0,Aa={next:function(){return{done:!!Ra++}},return:function(){ba=!0}};Aa[Ta]=function(){return this},Array.from(Aa,(function(){throw 2}))}catch(t){}var checkCorrectnessOfIteration=function(t,a){if(!a&&!ba)return!1;var u=!1;try{var h={};h[Ta]=function(){return{next:function(){return{done:u=!0}}}},t(h)}catch(t){}return u},Na=ia.CONSTRUCTOR||!checkCorrectnessOfIteration((function(t){Xn.all(t).then(void 0,(function(){}))}));_export({target:"Promise",stat:!0,forced:Na},{all:function all(t){var a=this,u=ca.f(a),h=u.resolve,m=u.reject,g=perform((function(){var u=aCallable(a.resolve),g=[],E=0,I=1;iterate(t,(function(t){var M=E++,S=!1;I++,O(u,a,t).then((function(t){S||(S=!0,g[M]=t,--I||h(g))}),m)})),--I||h(g)}));return g.error&&m(g.value),u.promise}});var wa=ia.CONSTRUCTOR;Xn&&Xn.prototype,_export({target:"Promise",proto:!0,forced:wa,real:!0},{catch:function(t){return this.then(void 0,t)}}),_export({target:"Promise",stat:!0,forced:Na},{race:function race(t){var a=this,u=ca.f(a),h=u.reject,m=perform((function(){var m=aCallable(a.resolve);iterate(t,(function(t){O(m,a,t).then(u.resolve,h)}))}));return m.error&&h(m.value),u.promise}}),_export({target:"Promise",stat:!0,forced:ia.CONSTRUCTOR},{reject:function reject(t){var a=ca.f(this);return O(a.reject,void 0,t),a.promise}});var promiseResolve=function(t,a){if(anObject(t),j(a)&&a.constructor===t)return a;var u=ca.f(t);return(0,u.resolve)(a),u.promise},Oa=ia.CONSTRUCTOR,xa=getBuiltIn("Promise"),ka=!Oa;_export({target:"Promise",stat:!0,forced:!0},{resolve:function resolve(t){return promiseResolve(ka&&this===xa?Xn:this,t)}}),_export({target:"Promise",stat:!0},{allSettled:function allSettled(t){var a=this,u=ca.f(a),h=u.resolve,m=u.reject,g=perform((function(){var u=aCallable(a.resolve),m=[],g=0,E=1;iterate(t,(function(t){var I=g++,M=!1;E++,O(u,a,t).then((function(t){M||(M=!0,m[I]={status:"fulfilled",value:t},--E||h(m))}),(function(t){M||(M=!0,m[I]={status:"rejected",reason:t},--E||h(m))}))})),--E||h(m)}));return g.error&&m(g.value),u.promise}});var Pa="No one promise resolved";_export({target:"Promise",stat:!0},{any:function any(t){var a=this,u=getBuiltIn("AggregateError"),h=ca.f(a),m=h.resolve,g=h.reject,E=perform((function(){var h=aCallable(a.resolve),E=[],I=0,M=1,S=!1;iterate(t,(function(t){var C=I++,T=!1;M++,O(h,a,t).then((function(t){T||S||(S=!0,m(t))}),(function(t){T||S||(T=!0,E[C]=t,--M||g(new u(E,Pa)))}))})),--M||g(new u(E,Pa))}));return E.error&&g(E.value),h.promise}});var La=Xn&&Xn.prototype,qa=!!Xn&&fails((function(){La.finally.call({then:function(){}},(function(){}))}));_export({target:"Promise",proto:!0,real:!0,forced:qa},{finally:function(t){var a=speciesConstructor(this,getBuiltIn("Promise")),u=isCallable(t);return this.then(u?function(u){return promiseResolve(a,t()).then((function(){return u}))}:t,u?function(u){return promiseResolve(a,t()).then((function(){throw u}))}:t)}});var Da=A("".charAt),Va=A("".charCodeAt),Ua=A("".slice),createMethod$3=function(t){return function(a,u){var h,m,g=toString(requireObjectCoercible(a)),E=toIntegerOrInfinity(u),I=g.length;return E<0||E>=I?t?"":void 0:(h=Va(g,E))<55296||h>56319||E+1===I||(m=Va(g,E+1))<56320||m>57343?t?Da(g,E):h:t?Ua(g,E,E+2):m-56320+(h-55296<<10)+65536}},Ga={codeAt:createMethod$3(!1),charAt:createMethod$3(!0)}.charAt,Fa="String Iterator",Ba=Ar.set,ja=Ar.getterFor(Fa);iteratorDefine(String,"String",(function(t){Ba(this,{type:Fa,string:toString(t),index:0})}),(function next(){var t,a=ja(this),u=a.string,h=a.index;return h>=u.length?{value:void 0,done:!0}:(t=Ga(u,h),a.index+=t.length,{value:t,done:!1})}));var Wa=W.Promise,Ya=wellKnownSymbol("toStringTag");for(var $a in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var Ha=m[$a],Qa=Ha&&Ha.prototype;Qa&&er(Qa)!==Ya&&We(Qa,Ya,$a),$t[$a]=$t.Array}var Ka=Wa;_export({target:"Promise",stat:!0,forced:!0},{try:function(t){var a=ca.f(this),u=perform(t);return(u.error?a.reject:a.resolve)(u.value),a.promise}});var za=Ka,Ja=Date,Xa=A(Ja.prototype.getTime);_export({target:"Date",stat:!0},{now:function now(){return Xa(new Ja)}});var Za=W.Date.now,eo=Array.isArray||function isArray(t){return"Array"==classofRaw(t)},to=TypeError,doesNotExceedSafeInteger=function(t){if(t>****************)throw to("Maximum allowed index exceeded");return t},createProperty=function(t,a,u){var h=toPropertyKey(a);h in t?je.f(t,h,createPropertyDescriptor(0,u)):t[h]=u},ro=wellKnownSymbol("species"),no=Array,arraySpeciesCreate=function(t,a){return new(function(t){var a;return eo(t)&&(a=t.constructor,(mn(a)&&(a===no||eo(a.prototype))||j(a)&&null===(a=a[ro]))&&(a=void 0)),void 0===a?no:a}(t))(0===a?0:a)},ao=wellKnownSymbol("species"),arrayMethodHasSpeciesSupport=function(t){return X>=51||!fails((function(){var a=[];return(a.constructor={})[ao]=function(){return{foo:1}},1!==a[t](Boolean).foo}))},oo=wellKnownSymbol("isConcatSpreadable"),io=X>=51||!fails((function(){var t=[];return t[oo]=!1,t.concat()[0]!==t})),so=arrayMethodHasSpeciesSupport("concat"),isConcatSpreadable=function(t){if(!j(t))return!1;var a=t[oo];return void 0!==a?!!a:eo(t)};_export({target:"Array",proto:!0,arity:1,forced:!io||!so},{concat:function concat(t){var a,u,h,m,g,E=toObject(this),I=arraySpeciesCreate(E,0),M=0;for(a=-1,h=arguments.length;a<h;a++)if(isConcatSpreadable(g=-1===a?E:arguments[a]))for(m=lengthOfArrayLike(g),doesNotExceedSafeInteger(M+m),u=0;u<m;u++,M++)u in g&&createProperty(I,M,g[u]);else doesNotExceedSafeInteger(M+1),createProperty(I,M++,g);return I.length=M,I}});var co=entryVirtual("Array").concat,lo=Array.prototype,concat=function(t){var a=t.concat;return t===lo||Y(lo,t)&&a===lo.concat?co:a},uo=/MSIE .\./.test($),po=m.Function,wrap$1=function(t){return uo?function(a,u){var h=validateArgumentsLength(arguments.length,1)>2,m=isCallable(a)?a:po(a),g=h?gt(arguments,2):void 0;return t(h?function(){S(m,this,g)}:m,u)}:t},ho={setTimeout:wrap$1(m.setTimeout),setInterval:wrap$1(m.setInterval)},mo=ho.setInterval;_export({global:!0,bind:!0,forced:m.setInterval!==mo},{setInterval:mo});var fo=ho.setTimeout;_export({global:!0,bind:!0,forced:m.setTimeout!==fo},{setTimeout:fo});var go=W.setTimeout,vo=createCommonjsModule((function(t){var a=Object.prototype.hasOwnProperty,u="~";function Events(){}function EE(t,a,u){this.fn=t,this.context=a,this.once=u||!1}function addListener(t,a,h,m,g){if("function"!=typeof h)throw new TypeError("The listener must be a function");var E=new EE(h,m||t,g),I=u?u+a:a;return t._events[I]?t._events[I].fn?t._events[I]=[t._events[I],E]:t._events[I].push(E):(t._events[I]=E,t._eventsCount++),t}function clearEvent(t,a){0==--t._eventsCount?t._events=new Events:delete t._events[a]}function EventEmitter(){this._events=new Events,this._eventsCount=0}Object.create&&(Events.prototype=Object.create(null),(new Events).__proto__||(u=!1)),EventEmitter.prototype.eventNames=function eventNames(){var t,h,m=[];if(0===this._eventsCount)return m;for(h in t=this._events)a.call(t,h)&&m.push(u?h.slice(1):h);return Object.getOwnPropertySymbols?m.concat(Object.getOwnPropertySymbols(t)):m},EventEmitter.prototype.listeners=function listeners(t){var a=u?u+t:t,h=this._events[a];if(!h)return[];if(h.fn)return[h.fn];for(var m=0,g=h.length,E=new Array(g);m<g;m++)E[m]=h[m].fn;return E},EventEmitter.prototype.listenerCount=function listenerCount(t){var a=u?u+t:t,h=this._events[a];return h?h.fn?1:h.length:0},EventEmitter.prototype.emit=function emit(t,a,h,m,g,E){var I=u?u+t:t;if(!this._events[I])return!1;var M,S,C=this._events[I],T=arguments.length;if(C.fn){switch(C.once&&this.removeListener(t,C.fn,void 0,!0),T){case 1:return C.fn.call(C.context),!0;case 2:return C.fn.call(C.context,a),!0;case 3:return C.fn.call(C.context,a,h),!0;case 4:return C.fn.call(C.context,a,h,m),!0;case 5:return C.fn.call(C.context,a,h,m,g),!0;case 6:return C.fn.call(C.context,a,h,m,g,E),!0}for(S=1,M=new Array(T-1);S<T;S++)M[S-1]=arguments[S];C.fn.apply(C.context,M)}else{var b,R=C.length;for(S=0;S<R;S++)switch(C[S].once&&this.removeListener(t,C[S].fn,void 0,!0),T){case 1:C[S].fn.call(C[S].context);break;case 2:C[S].fn.call(C[S].context,a);break;case 3:C[S].fn.call(C[S].context,a,h);break;case 4:C[S].fn.call(C[S].context,a,h,m);break;default:if(!M)for(b=1,M=new Array(T-1);b<T;b++)M[b-1]=arguments[b];C[S].fn.apply(C[S].context,M)}}return!0},EventEmitter.prototype.on=function on(t,a,u){return addListener(this,t,a,u,!1)},EventEmitter.prototype.once=function once(t,a,u){return addListener(this,t,a,u,!0)},EventEmitter.prototype.removeListener=function removeListener(t,a,h,m){var g=u?u+t:t;if(!this._events[g])return this;if(!a)return clearEvent(this,g),this;var E=this._events[g];if(E.fn)E.fn!==a||m&&!E.once||h&&E.context!==h||clearEvent(this,g);else{for(var I=0,M=[],S=E.length;I<S;I++)(E[I].fn!==a||m&&!E[I].once||h&&E[I].context!==h)&&M.push(E[I]);M.length?this._events[g]=1===M.length?M[0]:M:clearEvent(this,g)}return this},EventEmitter.prototype.removeAllListeners=function removeAllListeners(t){var a;return t?(a=u?u+t:t,this._events[a]&&clearEvent(this,a)):(this._events=new Events,this._eventsCount=0),this},EventEmitter.prototype.off=EventEmitter.prototype.removeListener,EventEmitter.prototype.addListener=EventEmitter.prototype.on,EventEmitter.prefixed=u,EventEmitter.EventEmitter=EventEmitter,t.exports=EventEmitter}));_export({global:!0},{globalThis:m});var _o=Array,yo=Math.max,arraySliceSimple=function(t,a,u){for(var h=lengthOfArrayLike(t),m=toAbsoluteIndex(a,h),g=toAbsoluteIndex(void 0===u?h:u,h),E=_o(yo(g-m,0)),I=0;m<g;m++,I++)createProperty(E,I,t[m]);return E.length=I,E},Eo=Vt.f,Io="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],Mo={f:function getOwnPropertyNames(t){return Io&&"Window"==classofRaw(t)?function(t){try{return Eo(t)}catch(t){return arraySliceSimple(Io)}}(t):Eo(toIndexedObject(t))}},So={f:wellKnownSymbol},Co=je.f,wellKnownSymbolDefine=function(t){var a=W.Symbol||(W.Symbol={});pe(a,t)||Co(a,t,{value:So.f(t)})},symbolDefineToPrimitive=function(){var t=getBuiltIn("Symbol"),a=t&&t.prototype,u=a&&a.valueOf,h=wellKnownSymbol("toPrimitive");a&&!a[h]&&defineBuiltIn(a,h,(function(t){return O(u,this)}),{arity:1})},To=A([].push),createMethod$2=function(t){var a=1==t,u=2==t,h=3==t,m=4==t,g=6==t,E=7==t,I=5==t||g;return function(M,S,C,T){for(var b,R,A=toObject(M),N=U(A),w=functionBindContext(S,C),O=lengthOfArrayLike(N),x=0,k=T||arraySpeciesCreate,P=a?k(M,O):u||E?k(M,0):void 0;O>x;x++)if((I||x in N)&&(R=w(b=N[x],x,A),t))if(a)P[x]=R;else if(R)switch(t){case 3:return!0;case 5:return b;case 6:return x;case 2:To(P,b)}else switch(t){case 4:return!1;case 7:To(P,b)}return g?-1:h||m?m:P}},bo={forEach:createMethod$2(0),map:createMethod$2(1),filter:createMethod$2(2),some:createMethod$2(3),every:createMethod$2(4),find:createMethod$2(5),findIndex:createMethod$2(6),filterReject:createMethod$2(7)},Ro=bo.forEach,Ao=sharedKey("hidden"),No="Symbol",wo=Ar.set,Oo=Ar.getterFor(No),xo=Object.prototype,ko=m.Symbol,Po=ko&&ko.prototype,Lo=m.TypeError,qo=m.QObject,Do=be.f,Vo=je.f,Uo=Mo.f,Go=P.f,Fo=A([].push),Bo=le("symbols"),jo=le("op-symbols"),Wo=le("wks"),Yo=!qo||!qo.prototype||!qo.prototype.findChild,$o=N&&fails((function(){return 7!=lt(Vo({},"a",{get:function(){return Vo(this,"a",{value:7}).a}})).a}))?function(t,a,u){var h=Do(xo,a);h&&delete xo[a],Vo(t,a,u),h&&t!==xo&&Vo(xo,a,h)}:Vo,wrap=function(t,a){var u=Bo[t]=lt(Po);return wo(u,{type:No,tag:t,description:a}),N||(u.description=a),u},Ho=function defineProperty(t,a,u){t===xo&&Ho(jo,a,u),anObject(t);var h=toPropertyKey(a);return anObject(u),pe(Bo,h)?(u.enumerable?(pe(t,Ao)&&t[Ao][h]&&(t[Ao][h]=!1),u=lt(u,{enumerable:createPropertyDescriptor(0,!1)})):(pe(t,Ao)||Vo(t,Ao,createPropertyDescriptor(1,{})),t[Ao][h]=!0),$o(t,h,u)):Vo(t,h,u)},Qo=function defineProperties(t,a){anObject(t);var u=toIndexedObject(a),h=nt(u).concat($getOwnPropertySymbols(u));return Ro(h,(function(a){N&&!O(Ko,u,a)||Ho(t,a,u[a])})),t},Ko=function propertyIsEnumerable(t){var a=toPropertyKey(t),u=O(Go,this,a);return!(this===xo&&pe(Bo,a)&&!pe(jo,a))&&(!(u||!pe(this,a)||!pe(Bo,a)||pe(this,Ao)&&this[Ao][a])||u)},zo=function getOwnPropertyDescriptor(t,a){var u=toIndexedObject(t),h=toPropertyKey(a);if(u!==xo||!pe(Bo,h)||pe(jo,h)){var m=Do(u,h);return!m||!pe(Bo,h)||pe(u,Ao)&&u[Ao][h]||(m.enumerable=!0),m}},Jo=function getOwnPropertyNames(t){var a=Uo(toIndexedObject(t)),u=[];return Ro(a,(function(t){pe(Bo,t)||pe(Ze,t)||Fo(u,t)})),u},$getOwnPropertySymbols=function(t){var a=t===xo,u=Uo(a?jo:toIndexedObject(t)),h=[];return Ro(u,(function(t){!pe(Bo,t)||a&&!pe(xo,t)||Fo(h,Bo[t])})),h};Z||(ko=function Symbol(){if(Y(Po,this))throw Lo("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?toString(arguments[0]):void 0,a=uid(t),setter=function(t){this===xo&&O(setter,jo,t),pe(this,Ao)&&pe(this[Ao],a)&&(this[Ao][a]=!1),$o(this,a,createPropertyDescriptor(1,t))};return N&&Yo&&$o(xo,a,{configurable:!0,set:setter}),wrap(a,t)},Po=ko.prototype,defineBuiltIn(Po,"toString",(function toString(){return Oo(this).tag})),defineBuiltIn(ko,"withoutSetter",(function(t){return wrap(uid(t),t)})),P.f=Ko,je.f=Ho,ot.f=Qo,be.f=zo,Vt.f=Mo.f=Jo,bt.f=$getOwnPropertySymbols,So.f=function(t){return wrap(wellKnownSymbol(t),t)},N&&Vo(Po,"description",{configurable:!0,get:function description(){return Oo(this).description}})),_export({global:!0,constructor:!0,wrap:!0,forced:!Z,sham:!Z},{Symbol:ko}),Ro(nt(Wo),(function(t){wellKnownSymbolDefine(t)})),_export({target:No,stat:!0,forced:!Z},{useSetter:function(){Yo=!0},useSimple:function(){Yo=!1}}),_export({target:"Object",stat:!0,forced:!Z,sham:!N},{create:function create(t,a){return void 0===a?lt(t):Qo(lt(t),a)},defineProperty:Ho,defineProperties:Qo,getOwnPropertyDescriptor:zo}),_export({target:"Object",stat:!0,forced:!Z},{getOwnPropertyNames:Jo}),symbolDefineToPrimitive(),setToStringTag(ko,No),Ze[Ao]=!0;var Xo=Z&&!!Symbol.for&&!!Symbol.keyFor,Zo=le("string-to-symbol-registry"),ei=le("symbol-to-string-registry");_export({target:"Symbol",stat:!0,forced:!Xo},{for:function(t){var a=toString(t);if(pe(Zo,a))return Zo[a];var u=getBuiltIn("Symbol")(a);return Zo[a]=u,ei[u]=a,u}});var ti=le("symbol-to-string-registry");_export({target:"Symbol",stat:!0,forced:!Xo},{keyFor:function keyFor(t){if(!re(t))throw TypeError(tryToString(t)+" is not a symbol");if(pe(ti,t))return ti[t]}});var ri=getBuiltIn("JSON","stringify"),ni=A(/./.exec),ai=A("".charAt),oi=A("".charCodeAt),ii=A("".replace),si=A(1..toString),ci=/[\uD800-\uDFFF]/g,li=/^[\uD800-\uDBFF]$/,ui=/^[\uDC00-\uDFFF]$/,di=!Z||fails((function(){var t=getBuiltIn("Symbol")();return"[null]"!=ri([t])||"{}"!=ri({a:t})||"{}"!=ri(Object(t))})),pi=fails((function(){return'"\\udf06\\ud834"'!==ri("\udf06\ud834")||'"\\udead"'!==ri("\udead")})),stringifyWithSymbolsFix=function(t,a){var u=gt(arguments),h=a;if((j(a)||void 0!==t)&&!re(t))return eo(a)||(a=function(t,a){if(isCallable(h)&&(a=O(h,this,t,a)),!re(a))return a}),u[1]=a,S(ri,null,u)},fixIllFormed=function(t,a,u){var h=ai(u,a-1),m=ai(u,a+1);return ni(li,t)&&!ni(ui,m)||ni(ui,t)&&!ni(li,h)?"\\u"+si(oi(t,0),16):t};ri&&_export({target:"JSON",stat:!0,arity:3,forced:di||pi},{stringify:function stringify(t,a,u){var h=gt(arguments),m=S(di?stringifyWithSymbolsFix:ri,null,h);return pi&&"string"==typeof m?ii(m,ci,fixIllFormed):m}});var hi=!Z||fails((function(){bt.f(1)}));_export({target:"Object",stat:!0,forced:hi},{getOwnPropertySymbols:function getOwnPropertySymbols(t){var a=bt.f;return a?a(toObject(t)):[]}}),wellKnownSymbolDefine("asyncIterator"),wellKnownSymbolDefine("hasInstance"),wellKnownSymbolDefine("isConcatSpreadable"),wellKnownSymbolDefine("iterator"),wellKnownSymbolDefine("match"),wellKnownSymbolDefine("matchAll"),wellKnownSymbolDefine("replace"),wellKnownSymbolDefine("search"),wellKnownSymbolDefine("species"),wellKnownSymbolDefine("split"),wellKnownSymbolDefine("toPrimitive"),symbolDefineToPrimitive(),wellKnownSymbolDefine("toStringTag"),setToStringTag(getBuiltIn("Symbol"),"Symbol"),wellKnownSymbolDefine("unscopables"),setToStringTag(m.JSON,"JSON",!0);var mi=W.Symbol;wellKnownSymbolDefine("asyncDispose"),wellKnownSymbolDefine("dispose"),wellKnownSymbolDefine("matcher"),wellKnownSymbolDefine("metadataKey"),wellKnownSymbolDefine("observable"),wellKnownSymbolDefine("metadata"),wellKnownSymbolDefine("patternMatch"),wellKnownSymbolDefine("replaceAll");var fi=mi,gi=je.f;_export({target:"Object",stat:!0,forced:Object.defineProperty!==gi,sham:!N},{defineProperty:gi});var vi=createCommonjsModule((function(t){var a=W.Object,u=t.exports=function defineProperty(t,u,h){return a.defineProperty(t,u,h)};a.defineProperty.sham&&(u.sham=!0)})),_i=vi,yi=fails((function(){qt(1)}));_export({target:"Object",stat:!0,forced:yi,sham:!xt},{getPrototypeOf:function getPrototypeOf(t){return qt(toObject(t))}});var Ei=W.Object.getPrototypeOf,arrayMethodIsStrict=function(t,a){var u=[][t];return!!u&&fails((function(){u.call(null,a||function(){return 1},1)}))},Ii=bo.forEach,Mi=arrayMethodIsStrict("forEach")?[].forEach:function forEach(t){return Ii(this,t,arguments.length>1?arguments[1]:void 0)};_export({target:"Array",proto:!0,forced:[].forEach!=Mi},{forEach:Mi});var Si=entryVirtual("Array").forEach,Ci=Array.prototype,Ti={DOMTokenList:!0,NodeList:!0},forEach$1=function(t){var a=t.forEach;return t===Ci||Y(Ci,t)&&a===Ci.forEach||pe(Ti,er(t))?Si:a},bi=A([].reverse),Ri=[1,2];_export({target:"Array",proto:!0,forced:String(Ri)===String(Ri.reverse())},{reverse:function reverse(){return eo(this)&&(this.length=this.length),bi(this)}});var Ai=entryVirtual("Array").reverse,Ni=Array.prototype,reverse=function(t){var a=t.reverse;return t===Ni||Y(Ni,t)&&a===Ni.reverse?Ai:a},wi=arrayMethodHasSpeciesSupport("slice"),Oi=wellKnownSymbol("species"),xi=Array,ki=Math.max;_export({target:"Array",proto:!0,forced:!wi},{slice:function slice(t,a){var u,h,m,g=toIndexedObject(this),E=lengthOfArrayLike(g),I=toAbsoluteIndex(t,E),M=toAbsoluteIndex(void 0===a?E:a,E);if(eo(g)&&(u=g.constructor,(mn(u)&&(u===xi||eo(u.prototype))||j(u)&&null===(u=u[Oi]))&&(u=void 0),u===xi||void 0===u))return gt(g,I,M);for(h=new(void 0===u?xi:u)(ki(M-I,0)),m=0;I<M;I++,m++)I in g&&createProperty(h,m,g[I]);return h.length=m,h}});var Pi=entryVirtual("Array").slice,Li=Array.prototype,slice=function(t){var a=t.slice;return t===Li||Y(Li,t)&&a===Li.slice?Pi:a},qi=So.f("iterator"),Di=Xe.includes,Vi=fails((function(){return!Array(1).includes()}));_export({target:"Array",proto:!0,forced:Vi},{includes:function includes(t){return Di(this,t,arguments.length>1?arguments[1]:void 0)}});var Ui=entryVirtual("Array").includes,Gi=wellKnownSymbol("match"),Fi=TypeError,notARegexp=function(t){if(function(t){var a;return j(t)&&(void 0!==(a=t[Gi])?!!a:"RegExp"==classofRaw(t))}(t))throw Fi("The method doesn't accept regular expressions");return t},Bi=wellKnownSymbol("match"),ji=A("".indexOf);_export({target:"String",proto:!0,forced:!function(t){var a=/./;try{"/./"[t](a)}catch(u){try{return a[Bi]=!1,"/./"[t](a)}catch(t){}}return!1}("includes")},{includes:function includes(t){return!!~ji(toString(requireObjectCoercible(this)),toString(notARegexp(t)),arguments.length>1?arguments[1]:void 0)}});var Wi=entryVirtual("String").includes,Yi=Array.prototype,$i=String.prototype,includes=function(t){var a=t.includes;return t===Yi||Y(Yi,t)&&a===Yi.includes?Ui:"string"==typeof t||t===$i||Y($i,t)&&a===$i.includes?Wi:a},Hi=fails((function(){nt(1)}));_export({target:"Object",stat:!0,forced:Hi},{keys:function keys(t){return nt(toObject(t))}});var Qi=W.Object.keys,Ki=bo.map,zi=arrayMethodHasSpeciesSupport("map");_export({target:"Array",proto:!0,forced:!zi},{map:function map(t){return Ki(this,t,arguments.length>1?arguments[1]:void 0)}});var Ji=entryVirtual("Array").map,Xi=Array.prototype,map$6=function(t){var a=t.map;return t===Xi||Y(Xi,t)&&a===Xi.map?Ji:a},Zi=bo.filter,es=arrayMethodHasSpeciesSupport("filter");_export({target:"Array",proto:!0,forced:!es},{filter:function filter(t){return Zi(this,t,arguments.length>1?arguments[1]:void 0)}});var ts=entryVirtual("Array").filter,rs=Array.prototype,filter=function(t){var a=t.filter;return t===rs||Y(rs,t)&&a===rs.filter?ts:a},ns=createCommonjsModule((function(t,a){t.exports=function(){function _regeneratorRuntime(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
_regeneratorRuntime=function _regeneratorRuntime(){return t};var t={},a=Object.prototype,u=a.hasOwnProperty,h="function"==typeof fi?fi:{},m=h.iterator||"@@iterator",g=h.asyncIterator||"@@asyncIterator",E=h.toStringTag||"@@toStringTag";function define(t,a,u){return _i(t,a,{value:u,enumerable:!0,configurable:!0,writable:!0}),t[a]}try{define({},"")}catch(t){define=function define(t,a,u){return t[a]=u}}function wrap(t,a,u,h){var m=a&&a.prototype instanceof Generator?a:Generator,g=dt(m.prototype),E=new Context(h||[]);return g._invoke=function(t,a,u){var h="suspendedStart";return function(m,g){if("executing"===h)throw new Error("Generator is already running");if("completed"===h){if("throw"===m)throw g;return doneResult()}for(u.method=m,u.arg=g;;){var E=u.delegate;if(E){var M=maybeInvokeDelegate(E,u);if(M){if(M===I)continue;return M}}if("next"===u.method)u.sent=u._sent=u.arg;else if("throw"===u.method){if("suspendedStart"===h)throw h="completed",u.arg;u.dispatchException(u.arg)}else"return"===u.method&&u.abrupt("return",u.arg);h="executing";var S=tryCatch(t,a,u);if("normal"===S.type){if(h=u.done?"completed":"suspendedYield",S.arg===I)continue;return{value:S.arg,done:u.done}}"throw"===S.type&&(h="completed",u.method="throw",u.arg=S.arg)}}}(t,u,E),g}function tryCatch(t,a,u){try{return{type:"normal",arg:t.call(a,u)}}catch(t){return{type:"throw",arg:t}}}t.wrap=wrap;var I={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var M={};define(M,m,(function(){return this}));var S=Ei&&Ei(Ei(values([])));S&&S!==a&&u.call(S,m)&&(M=S);var C=GeneratorFunctionPrototype.prototype=Generator.prototype=dt(M);function defineIteratorMethods(t){var a;forEach$1(a=["next","throw","return"]).call(a,(function(a){define(t,a,(function(t){return this._invoke(a,t)}))}))}function AsyncIterator(t,a){function invoke(h,m,g,E){var I=tryCatch(t[h],t,m);if("throw"!==I.type){var M=I.arg,S=M.value;return S&&"object"==typeof S&&u.call(S,"__await")?a.resolve(S.__await).then((function(t){invoke("next",t,g,E)}),(function(t){invoke("throw",t,g,E)})):a.resolve(S).then((function(t){M.value=t,g(M)}),(function(t){return invoke("throw",t,g,E)}))}E(I.arg)}var h;this._invoke=function(t,u){function callInvokeWithMethodAndArg(){return new a((function(a,h){invoke(t,u,a,h)}))}return h=h?h.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}}function maybeInvokeDelegate(t,a){var u=t.iterator[a.method];if(void 0===u){if(a.delegate=null,"throw"===a.method){if(t.iterator.return&&(a.method="return",a.arg=void 0,maybeInvokeDelegate(t,a),"throw"===a.method))return I;a.method="throw",a.arg=new TypeError("The iterator does not provide a 'throw' method")}return I}var h=tryCatch(u,t.iterator,a.arg);if("throw"===h.type)return a.method="throw",a.arg=h.arg,a.delegate=null,I;var m=h.arg;return m?m.done?(a[t.resultName]=m.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=void 0),a.delegate=null,I):m:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,I)}function pushTryEntry(t){var a={tryLoc:t[0]};1 in t&&(a.catchLoc=t[1]),2 in t&&(a.finallyLoc=t[2],a.afterLoc=t[3]),this.tryEntries.push(a)}function resetTryEntry(t){var a=t.completion||{};a.type="normal",delete a.arg,t.completion=a}function Context(t){this.tryEntries=[{tryLoc:"root"}],forEach$1(t).call(t,pushTryEntry,this),this.reset(!0)}function values(t){if(t){var a=t[m];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var h=-1,g=function next(){for(;++h<t.length;)if(u.call(t,h))return next.value=t[h],next.done=!1,next;return next.value=void 0,next.done=!0,next};return g.next=g}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,define(C,"constructor",GeneratorFunctionPrototype),define(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,E,"GeneratorFunction"),t.isGeneratorFunction=function(t){var a="function"==typeof t&&t.constructor;return!!a&&(a===GeneratorFunction||"GeneratorFunction"===(a.displayName||a.name))},t.mark=function(t){return ft?ft(t,GeneratorFunctionPrototype):(t.__proto__=GeneratorFunctionPrototype,define(t,E,"GeneratorFunction")),t.prototype=dt(C),t},t.awrap=function(t){return{__await:t}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,g,(function(){return this})),t.AsyncIterator=AsyncIterator,t.async=function(a,u,h,m,g){void 0===g&&(g=za);var E=new AsyncIterator(wrap(a,u,h,m),g);return t.isGeneratorFunction(u)?E:E.next().then((function(t){return t.done?t.value:E.next()}))},defineIteratorMethods(C),define(C,E,"Generator"),define(C,m,(function(){return this})),define(C,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var a=[];for(var u in t)a.push(u);return reverse(a).call(a),function next(){for(;a.length;){var u=a.pop();if(u in t)return next.value=u,next.done=!1,next}return next.done=!0,next}},t.values=values,Context.prototype={constructor:Context,reset:function reset(t){var a;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,forEach$1(a=this.tryEntries).call(a,resetTryEntry),!t)for(var h in this)"t"===h.charAt(0)&&u.call(this,h)&&!isNaN(+slice(h).call(h,1))&&(this[h]=void 0)},stop:function stop(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function dispatchException(t){if(this.done)throw t;var a=this;function handle(u,h){return g.type="throw",g.arg=t,a.next=u,h&&(a.method="next",a.arg=void 0),!!h}for(var h=this.tryEntries.length-1;h>=0;--h){var m=this.tryEntries[h],g=m.completion;if("root"===m.tryLoc)return handle("end");if(m.tryLoc<=this.prev){var E=u.call(m,"catchLoc"),I=u.call(m,"finallyLoc");if(E&&I){if(this.prev<m.catchLoc)return handle(m.catchLoc,!0);if(this.prev<m.finallyLoc)return handle(m.finallyLoc)}else if(E){if(this.prev<m.catchLoc)return handle(m.catchLoc,!0)}else{if(!I)throw new Error("try statement without catch or finally");if(this.prev<m.finallyLoc)return handle(m.finallyLoc)}}}},abrupt:function abrupt(t,a){for(var h=this.tryEntries.length-1;h>=0;--h){var m=this.tryEntries[h];if(m.tryLoc<=this.prev&&u.call(m,"finallyLoc")&&this.prev<m.finallyLoc){var g=m;break}}g&&("break"===t||"continue"===t)&&g.tryLoc<=a&&a<=g.finallyLoc&&(g=null);var E=g?g.completion:{};return E.type=t,E.arg=a,g?(this.method="next",this.next=g.finallyLoc,I):this.complete(E)},complete:function complete(t,a){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&a&&(this.next=a),I},finish:function finish(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.finallyLoc===t)return this.complete(u.completion,u.afterLoc),resetTryEntry(u),I}},catch:function _catch(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.tryLoc===t){var h=u.completion;if("throw"===h.type){var m=h.arg;resetTryEntry(u)}return m}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(t,a,u){return this.delegate={iterator:values(t),resultName:a,nextLoc:u},"next"===this.method&&(this.arg=void 0),I}},t}function _typeof(t){return _typeof="function"==typeof fi&&"symbol"==typeof qi?function(t){return typeof t}:function(t){return t&&"function"==typeof fi&&t.constructor===fi&&t!==fi.prototype?"symbol":typeof t},_typeof(t)}function _classCallCheck(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,a){for(var u=0;u<a.length;u++){var h=a[u];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),_i(t,h.key,h)}}function _createClass(t,a,u){return a&&_defineProperties(t.prototype,a),u&&_defineProperties(t,u),_i(t,"prototype",{writable:!1}),t}function __awaiter(t,a,u,h){function adopt(t){return t instanceof u?t:new u((function(a){a(t)}))}return new(u||(u=za))((function(u,m){function fulfilled(t){try{step(h.next(t))}catch(t){m(t)}}function rejected(t){try{step(h.throw(t))}catch(t){m(t)}}function step(t){t.done?u(t.value):adopt(t.value).then(fulfilled,rejected)}step((h=h.apply(t,a||[])).next())}))}var t={isDataReportEnable:!0,maxSize:100,msgListMaxSize:1e3,cacheMaxSize:1e3,maxDelay:3e5,maxInterval:3e4,minInterval:1e4,timeout:5e3,autoStart:!0,loginFailIgnoreInterval:72e5},a=12,u=8e3,h=function emptyFn(){},m=function(){function Reporter(a){_classCallCheck(this,Reporter),this.isUploadEnable=!0,this.serverAllowUpload=!1,this.initConfigLoaded=!1,this.loading=!1,this.isDestroyed=!1,this.reportConfig=t,this.configPath="dispatcher/req",this.dataReportPath="statics/report/common/form",this.traceMsgCache={},this.reqRetryCount=0,this.highPriorityMsgList=[],this.msgList=[],this.lowPriorityMsgList=[],this.cacheMsgList=[],this.lastReportTime=Za(),this.timer=null,this.endedAsyncMsgByModule={},this.lastFailLogin={},this.setConfig(a),this.reportConfig.isDataReportEnable&&this.reportConfig.autoStart&&this.initUploadConfig()}return _createClass(Reporter,[{key:"setConfig",value:function setConfig(t){var a=Ot({},this.reportConfig.common,t.common);this.reportConfig=Ot({},this.reportConfig,t),this.reportConfig.common=a,this.reportConfig.common.sdk_type||(this.reportConfig.common.sdk_type="im")}},{key:"reportImmediately",value:function reportImmediately(t,a){var u=this;this.reportConfig.isDataReportEnable&&this.reportConfig.request(t,Ot({dataType:"json",method:"POST",timeout:this.reportConfig.timeout},a)).catch((function(t){var a,h;null===(h=null===(a=u.reportConfig)||void 0===a?void 0:a.logger)||void 0===h||h.warn("Reporter immediately upload failed",t)}))}},{key:"report",value:function report(a,u){var h=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(h.priority||(h.priority=this.getEventPriority(a,u)),this.reportConfig.isDataReportEnable&&a){if("login"===a&&!1===u.succeed&&u.process_id){var m=this.lastFailLogin[u.process_id]||0;if(u.start_time-m<t.loginFailIgnoreInterval)return;this.lastFailLogin[u.process_id]=u.start_time}var g=Za();"HIGH"===h.priority?this.highPriorityMsgList.push({module:a,msg:u,createTime:g}):"NORMAL"===h.priority?this.msgList.push({module:a,msg:u,createTime:g}):"LOW"===h.priority&&this.lowPriorityMsgList.push({module:a,msg:u,createTime:g}),this.highPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.highPriorityMsgList.shift(),this.msgList.length>this.reportConfig.msgListMaxSize&&this.msgList.shift(),this.lowPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.lowPriorityMsgList.shift(),this.doReport()}}},{key:"reportTraceStart",value:function reportTraceStart(t,a){if(this.reportConfig.isDataReportEnable&&t&&!this.traceMsgCache[t]){var u=Ot(Ot({start_time:Za()},a),{extension:[]});this.traceMsgCache[t]=u}}},{key:"reportTraceUpdate",value:function reportTraceUpdate(t){}},{key:"reportTraceUpdateV2",value:function reportTraceUpdateV2(t,a,u){var h,m=this;if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[t]){var g=this.traceMsgCache[t].extension,E=g.length,I=(new Date).getTime();0===E?a.duration=I-this.traceMsgCache[t].start_time:g[E-1].end_time?a.duration=I-g[E-1].end_time:a.duration=I-this.traceMsgCache[t].start_time,g.push(Ot({end_time:I},a));var M=g.length-1;(null==u?void 0:u.asyncParams)&&((h=this.traceMsgCache[t]).asyncPromiseArray||(h.asyncPromiseArray=[]),this.traceMsgCache[t].asyncPromiseArray.push(u.asyncParams.then((function(a){m.traceMsgCache[t]&&m.traceMsgCache[t].extension[M]&&Ot(m.traceMsgCache[t].extension[M],a)}))))}}},{key:"reportTraceEnd",value:function reportTraceEnd(t){var a,u,h=this,m=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[t])if("nos"!==t||!1===m){"boolean"==typeof m?this.traceMsgCache[t].succeed=!!m:this.traceMsgCache[t].state=m,this.traceMsgCache[t].duration=Za()-this.traceMsgCache[t].start_time,forEach$1(a=this.traceMsgCache[t].extension).call(a,(function(t){delete t.end_time}));var g=this.traceMsgCache[t];if(this.traceMsgCache[t]=null,g.asyncPromiseArray){(u=this.endedAsyncMsgByModule)[t]||(u[t]=[]),this.endedAsyncMsgByModule[t].push(g);var E=function asyncCallback(){var a;h.endedAsyncMsgByModule[t]&&includes(a=h.endedAsyncMsgByModule[t]).call(a,g)&&(delete g.asyncPromiseArray,h.report(t,g,{priority:h.getEventPriority(t,g)}))};za.all(g.asyncPromiseArray).then(E).catch(E)}else this.report(t,g,{priority:this.getEventPriority(t,g)})}else this.traceMsgCache[t]=null}},{key:"getEventPriority",value:function getEventPriority(t,a){if("exceptions"===t){if(0===a.action)return"HIGH";if(2===a.action)return"HIGH";if(1===a.action&&0!==a.exception_service)return"HIGH"}else{if("msgReceive"===t)return"LOW";if("nim_api_trace"===t)return"LOW"}return"NORMAL"}},{key:"reportTraceCancel",value:function reportTraceCancel(t){this.reportConfig.isDataReportEnable&&(this.endedAsyncMsgByModule[t]=[],this.traceMsgCache[t]=null)}},{key:"pause",value:function pause(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!1)}},{key:"restore",value:function restore(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!0,this.initConfigLoaded||this.initUploadConfig())}},{key:"destroy",value:function destroy(){var t,a=this;this.reportConfig.isDataReportEnable&&(forEach$1(t=Qi(this.traceMsgCache)).call(t,(function(t){a.reportTraceEnd(t,1)})),null!==this.timer&&clearTimeout(this.timer),this.setConfig=h,this.report=h,this.reportTraceStart=h,this.reportTraceUpdate=h,this.reportTraceEnd=h,this.pause=h,this.restore=h,this.destroy=h,this.reqRetryCount=0,this.cacheMsgList=[],this.traceMsgCache={},this.lowPriorityMsgList=[],this.msgList=[],this.highPriorityMsgList=[],this.reportConfig={},this.isDestroyed=!0)}},{key:"initUploadConfig",value:function initUploadConfig(){var t,h;return __awaiter(this,void 0,void 0,_regeneratorRuntime().mark((function _callee(){var m,g,E,I,M,S=this;return _regeneratorRuntime().wrap((function _callee$(C){for(var T;;)switch(C.prev=C.next){case 0:if(!this.loading){C.next=2;break}return C.abrupt("return");case 2:this.loading=!0,m=this.reportConfig.common||{},g=map$6(T=this.reportConfig.compassDataEndpoint.split(",")).call(T,(function(t){var a;return concat(a="".concat(t,"/")).call(a,S.configPath)})),E=_regeneratorRuntime().mark((function _loop(E){return _regeneratorRuntime().wrap((function _loop$(I){for(;;)switch(I.prev=I.next){case 0:if(!S.initConfigLoaded&&!S.isDestroyed){I.next=2;break}return I.abrupt("return","break");case 2:return I.prev=2,I.next=5,S.reportConfig.request(g[E],{method:"GET",dataType:"json",params:{deviceId:m.dev_id,sdkVer:m.sdk_ver,platform:m.platform,appkey:m.app_key},timeout:S.reportConfig.timeout}).then((function(t){var a,u;if(!S.isDestroyed){if(200===t.status&&t.data&&200===t.data.code){S.initConfigLoaded=!0;var h=t.data.data||{};S.reportConfig.maxSize=h.maxSize>1e3?1e3:h.maxSize,S.reportConfig.maxInterval=h.maxInterval>1e4?1e4:h.maxInterval,S.reportConfig.maxInterval=h.maxInterval<10?10:h.maxInterval,S.reportConfig.minInterval=h.minInterval<2?2:h.minInterval,S.reportConfig.maxDelay=h.maxDelay||300,S.reportConfig.maxInterval=1e3*S.reportConfig.maxInterval,S.reportConfig.minInterval=1e3*S.reportConfig.minInterval,S.reportConfig.maxDelay=1e3*S.reportConfig.maxDelay,h.endpoint?S.dataReportEndpoint=h.endpoint:S.dataReportEndpoint=g[E],S.serverAllowUpload=!0,S.loading=!1,S.reportHeartBeat()}else 200===t.status&&(S.initConfigLoaded=!0);null===(u=null===(a=S.reportConfig)||void 0===a?void 0:a.logger)||void 0===u||u.log("Get reporter upload config success")}})).catch((function(t){var h,m;S.isDestroyed||(S.loading=!1,null===(m=null===(h=S.reportConfig)||void 0===h?void 0:h.logger)||void 0===m||m.error("Get reporter upload config failed",t),S.reqRetryCount<a&&(S.reqRetryCount++,go((function(){S.isDestroyed||S.initUploadConfig()}),u)))}));case 5:I.next=14;break;case 7:if(I.prev=7,I.t0=I.catch(2),!S.isDestroyed){I.next=11;break}return I.abrupt("return",{v:void 0});case 11:S.loading=!1,null===(h=null===(t=S.reportConfig)||void 0===t?void 0:t.logger)||void 0===h||h.error("Exec reporter request failed",I.t0),S.reqRetryCount<a&&(S.reqRetryCount++,go((function(){S.isDestroyed||S.initUploadConfig()}),u));case 14:case"end":return I.stop()}}),_loop,null,[[2,7]])})),I=0;case 7:if(!(I<g.length)){C.next=17;break}return C.delegateYield(E(I),"t0",9);case 9:if("break"!==(M=C.t0)){C.next=12;break}return C.abrupt("break",17);case 12:if("object"!==_typeof(M)){C.next=14;break}return C.abrupt("return",M.v);case 14:I++,C.next=7;break;case 17:case"end":return C.stop()}}),_callee,this)})))}},{key:"reportHeartBeat",value:function reportHeartBeat(){var t=this;this.isDestroyed||(this.timer=go((function(){t.reportHeartBeat()}),this.reportConfig.minInterval),this.doReport())}},{key:"doReport",value:function doReport(){if(!this.isDestroyed){var t=this.highPriorityMsgList.length+this.msgList.length+this.lowPriorityMsgList.length+this.cacheMsgList.length>2*this.reportConfig.maxSize?this.reportConfig.minInterval:this.reportConfig.maxInterval;Za()-this.lastReportTime>=t&&this.upload()}}},{key:"getUploadMsg",value:function getUploadMsg(){var t,a,u,h,m,g,E=this,I={},M=Za();this.highPriorityMsgList=filter(t=this.highPriorityMsgList).call(t,(function(t){return M-t.createTime<E.reportConfig.maxDelay})),this.msgList=filter(a=this.msgList).call(a,(function(t){return M-t.createTime<E.reportConfig.maxDelay})),this.lowPriorityMsgList=filter(u=this.lowPriorityMsgList).call(u,(function(t){return M-t.createTime<E.reportConfig.maxDelay})),this.cacheMsgList=filter(h=this.cacheMsgList).call(h,(function(t){return M-t.createTime<E.reportConfig.maxDelay}));var S=slice(m=this.highPriorityMsgList).call(m,0,this.reportConfig.maxSize);if(this.highPriorityMsgList=slice(g=this.highPriorityMsgList).call(g,S.length),S.length<this.reportConfig.maxSize){var C,T,b=this.reportConfig.maxSize-S.length;S=concat(S).call(S,slice(C=this.msgList).call(C,0,b)),this.msgList=slice(T=this.msgList).call(T,b)}if(S.length<this.reportConfig.maxSize){var R,A,N=this.reportConfig.maxSize-S.length;S=concat(S).call(S,slice(R=this.lowPriorityMsgList).call(R,0,N)),this.lowPriorityMsgList=slice(A=this.lowPriorityMsgList).call(A,N)}if(S.length<this.reportConfig.maxSize){var w,O,x=this.reportConfig.maxSize-S.length;S=concat(S).call(S,slice(w=this.cacheMsgList).call(w,0,x)),this.cacheMsgList=slice(O=this.cacheMsgList).call(O,x)}return forEach$1(S).call(S,(function(t){I[t.module]?I[t.module].push(t.msg):I[t.module]=[t.msg]})),{uploadMsgArr:S,uploadMsg:I}}},{key:"upload",value:function upload(){var t,a,u=this;if(this.isUploadEnable&&this.serverAllowUpload&&!(this.lastReportTime&&Za()-this.lastReportTime<this.reportConfig.minInterval)){var h=this.getUploadMsg(),m=h.uploadMsgArr,g=h.uploadMsg;if(m.length){this.lastReportTime=Za();try{var E,I=concat(E="".concat(this.dataReportEndpoint,"/")).call(E,this.dataReportPath);this.reportConfig.request(I,{dataType:"json",method:"POST",data:{common:this.reportConfig.common,event:g},headers:{sdktype:"im"},timeout:this.reportConfig.timeout}).catch((function(t){var a,h,g,E;u.cacheMsgList=slice(a=concat(h=u.cacheMsgList).call(h,m)).call(a,0,u.reportConfig.cacheMaxSize),null===(E=null===(g=u.reportConfig)||void 0===g?void 0:g.logger)||void 0===E||E.warn("Reporter upload failed",t)}))}catch(u){null===(a=null===(t=this.reportConfig)||void 0===t?void 0:t.logger)||void 0===a||a.warn("Exec reporter request failed",u)}clearTimeout(this.timer),this.reportHeartBeat()}}}}]),Reporter}();return m}()})),as=getDefaultExportFromCjs(createCommonjsModule((function(t){function _defineProperties(t,a){for(var u=0;u<a.length;u++){var h=a[u];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),_i(t,h.key,h)}}t.exports=function _createClass(t,a,u){return a&&_defineProperties(t.prototype,a),u&&_defineProperties(t,u),_i(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports}))),os=createCommonjsModule((function(t){function _typeof(a){return t.exports=_typeof="function"==typeof fi&&"symbol"==typeof qi?function(t){return typeof t}:function(t){return t&&"function"==typeof fi&&t.constructor===fi&&t!==fi.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(a)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports})),is=createCommonjsModule((function(t){var a=os.default;function _regeneratorRuntime(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
t.exports=_regeneratorRuntime=function _regeneratorRuntime(){return u},t.exports.__esModule=!0,t.exports.default=t.exports;var u={},h=Object.prototype,m=h.hasOwnProperty,g="function"==typeof fi?fi:{},E=g.iterator||"@@iterator",I=g.asyncIterator||"@@asyncIterator",M=g.toStringTag||"@@toStringTag";function define(t,a,u){return _i(t,a,{value:u,enumerable:!0,configurable:!0,writable:!0}),t[a]}try{define({},"")}catch(t){define=function define(t,a,u){return t[a]=u}}function wrap(t,a,u,h){var m=a&&a.prototype instanceof Generator?a:Generator,g=dt(m.prototype),E=new Context(h||[]);return g._invoke=function(t,a,u){var h="suspendedStart";return function(m,g){if("executing"===h)throw new Error("Generator is already running");if("completed"===h){if("throw"===m)throw g;return doneResult()}for(u.method=m,u.arg=g;;){var E=u.delegate;if(E){var I=maybeInvokeDelegate(E,u);if(I){if(I===S)continue;return I}}if("next"===u.method)u.sent=u._sent=u.arg;else if("throw"===u.method){if("suspendedStart"===h)throw h="completed",u.arg;u.dispatchException(u.arg)}else"return"===u.method&&u.abrupt("return",u.arg);h="executing";var M=tryCatch(t,a,u);if("normal"===M.type){if(h=u.done?"completed":"suspendedYield",M.arg===S)continue;return{value:M.arg,done:u.done}}"throw"===M.type&&(h="completed",u.method="throw",u.arg=M.arg)}}}(t,u,E),g}function tryCatch(t,a,u){try{return{type:"normal",arg:t.call(a,u)}}catch(t){return{type:"throw",arg:t}}}u.wrap=wrap;var S={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var C={};define(C,E,(function(){return this}));var T=Ei&&Ei(Ei(values([])));T&&T!==h&&m.call(T,E)&&(C=T);var b=GeneratorFunctionPrototype.prototype=Generator.prototype=dt(C);function defineIteratorMethods(t){var a;forEach$1(a=["next","throw","return"]).call(a,(function(a){define(t,a,(function(t){return this._invoke(a,t)}))}))}function AsyncIterator(t,u){function invoke(h,g,E,I){var M=tryCatch(t[h],t,g);if("throw"!==M.type){var S=M.arg,C=S.value;return C&&"object"==a(C)&&m.call(C,"__await")?u.resolve(C.__await).then((function(t){invoke("next",t,E,I)}),(function(t){invoke("throw",t,E,I)})):u.resolve(C).then((function(t){S.value=t,E(S)}),(function(t){return invoke("throw",t,E,I)}))}I(M.arg)}var h;this._invoke=function(t,a){function callInvokeWithMethodAndArg(){return new u((function(u,h){invoke(t,a,u,h)}))}return h=h?h.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}}function maybeInvokeDelegate(t,a){var u=t.iterator[a.method];if(void 0===u){if(a.delegate=null,"throw"===a.method){if(t.iterator.return&&(a.method="return",a.arg=void 0,maybeInvokeDelegate(t,a),"throw"===a.method))return S;a.method="throw",a.arg=new TypeError("The iterator does not provide a 'throw' method")}return S}var h=tryCatch(u,t.iterator,a.arg);if("throw"===h.type)return a.method="throw",a.arg=h.arg,a.delegate=null,S;var m=h.arg;return m?m.done?(a[t.resultName]=m.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=void 0),a.delegate=null,S):m:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,S)}function pushTryEntry(t){var a={tryLoc:t[0]};1 in t&&(a.catchLoc=t[1]),2 in t&&(a.finallyLoc=t[2],a.afterLoc=t[3]),this.tryEntries.push(a)}function resetTryEntry(t){var a=t.completion||{};a.type="normal",delete a.arg,t.completion=a}function Context(t){this.tryEntries=[{tryLoc:"root"}],forEach$1(t).call(t,pushTryEntry,this),this.reset(!0)}function values(t){if(t){var a=t[E];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var u=-1,h=function next(){for(;++u<t.length;)if(m.call(t,u))return next.value=t[u],next.done=!1,next;return next.value=void 0,next.done=!0,next};return h.next=h}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,define(b,"constructor",GeneratorFunctionPrototype),define(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,M,"GeneratorFunction"),u.isGeneratorFunction=function(t){var a="function"==typeof t&&t.constructor;return!!a&&(a===GeneratorFunction||"GeneratorFunction"===(a.displayName||a.name))},u.mark=function(t){return ft?ft(t,GeneratorFunctionPrototype):(t.__proto__=GeneratorFunctionPrototype,define(t,M,"GeneratorFunction")),t.prototype=dt(b),t},u.awrap=function(t){return{__await:t}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,I,(function(){return this})),u.AsyncIterator=AsyncIterator,u.async=function(t,a,h,m,g){void 0===g&&(g=za);var E=new AsyncIterator(wrap(t,a,h,m),g);return u.isGeneratorFunction(a)?E:E.next().then((function(t){return t.done?t.value:E.next()}))},defineIteratorMethods(b),define(b,M,"Generator"),define(b,E,(function(){return this})),define(b,"toString",(function(){return"[object Generator]"})),u.keys=function(t){var a=[];for(var u in t)a.push(u);return reverse(a).call(a),function next(){for(;a.length;){var u=a.pop();if(u in t)return next.value=u,next.done=!1,next}return next.done=!0,next}},u.values=values,Context.prototype={constructor:Context,reset:function reset(t){var a;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,forEach$1(a=this.tryEntries).call(a,resetTryEntry),!t)for(var u in this)"t"===u.charAt(0)&&m.call(this,u)&&!isNaN(+slice(u).call(u,1))&&(this[u]=void 0)},stop:function stop(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function dispatchException(t){if(this.done)throw t;var a=this;function handle(u,h){return g.type="throw",g.arg=t,a.next=u,h&&(a.method="next",a.arg=void 0),!!h}for(var u=this.tryEntries.length-1;u>=0;--u){var h=this.tryEntries[u],g=h.completion;if("root"===h.tryLoc)return handle("end");if(h.tryLoc<=this.prev){var E=m.call(h,"catchLoc"),I=m.call(h,"finallyLoc");if(E&&I){if(this.prev<h.catchLoc)return handle(h.catchLoc,!0);if(this.prev<h.finallyLoc)return handle(h.finallyLoc)}else if(E){if(this.prev<h.catchLoc)return handle(h.catchLoc,!0)}else{if(!I)throw new Error("try statement without catch or finally");if(this.prev<h.finallyLoc)return handle(h.finallyLoc)}}}},abrupt:function abrupt(t,a){for(var u=this.tryEntries.length-1;u>=0;--u){var h=this.tryEntries[u];if(h.tryLoc<=this.prev&&m.call(h,"finallyLoc")&&this.prev<h.finallyLoc){var g=h;break}}g&&("break"===t||"continue"===t)&&g.tryLoc<=a&&a<=g.finallyLoc&&(g=null);var E=g?g.completion:{};return E.type=t,E.arg=a,g?(this.method="next",this.next=g.finallyLoc,S):this.complete(E)},complete:function complete(t,a){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&a&&(this.next=a),S},finish:function finish(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.finallyLoc===t)return this.complete(u.completion,u.afterLoc),resetTryEntry(u),S}},catch:function _catch(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.tryLoc===t){var h=u.completion;if("throw"===h.type){var m=h.arg;resetTryEntry(u)}return m}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(t,a,u){return this.delegate={iterator:values(t),resultName:a,nextLoc:u},"next"===this.method&&(this.arg=void 0),S}},u}t.exports=_regeneratorRuntime,t.exports.__esModule=!0,t.exports.default=t.exports})),ss=is(),cs=ss;try{regeneratorRuntime=ss}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=ss:Function("r","regeneratorRuntime = r")(ss)}var ls=Xe.indexOf,us=A([].indexOf),ds=!!us&&1/us([1],1,-0)<0,ps=arrayMethodIsStrict("indexOf");_export({target:"Array",proto:!0,forced:ds||!ps},{indexOf:function indexOf(t){var a=arguments.length>1?arguments[1]:void 0;return ds?us(this,t,a)||0:ls(this,t,a)}});var hs=entryVirtual("Array").indexOf,ms=Array.prototype,indexOf=function(t){var a=t.indexOf;return t===ms||Y(ms,t)&&a===ms.indexOf?hs:a};W.JSON||(W.JSON={stringify:JSON.stringify});var fs=function stringify(t,a,u){return S(W.JSON.stringify,null,arguments)},gs=fs,vs=bo.some,_s=arrayMethodIsStrict("some");_export({target:"Array",proto:!0,forced:!_s},{some:function some(t){return vs(this,t,arguments.length>1?arguments[1]:void 0)}});var ys=entryVirtual("Array").some,Es=Array.prototype,some=function(t){var a=t.some;return t===Es||Y(Es,t)&&a===Es.some?ys:a};function __rest(t,a){var u={};for(var h in t)Object.prototype.hasOwnProperty.call(t,h)&&a.indexOf(h)<0&&(u[h]=t[h]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var m=0;for(h=Object.getOwnPropertySymbols(t);m<h.length;m++)a.indexOf(h[m])<0&&Object.prototype.propertyIsEnumerable.call(t,h[m])&&(u[h[m]]=t[h[m]])}return u}function __awaiter(t,a,u,h){return new(u||(u=Promise))((function(m,g){function fulfilled(t){try{step(h.next(t))}catch(t){g(t)}}function rejected(t){try{step(h.throw(t))}catch(t){g(t)}}function step(t){t.done?m(t.value):function adopt(t){return t instanceof u?t:new u((function(a){a(t)}))}(t.value).then(fulfilled,rejected)}step((h=h.apply(t,a||[])).next())}))}var Is=Math.min,Ms=[].lastIndexOf,Ss=!!Ms&&1/[1].lastIndexOf(1,-0)<0,Cs=arrayMethodIsStrict("lastIndexOf"),Ts=Ss||!Cs?function lastIndexOf(t){if(Ss)return S(Ms,this,arguments)||0;var a=toIndexedObject(this),u=lengthOfArrayLike(a),h=u-1;for(arguments.length>1&&(h=Is(h,toIntegerOrInfinity(arguments[1]))),h<0&&(h=u+h);h>=0;h--)if(h in a&&a[h]===t)return h||0;return-1}:Ms;_export({target:"Array",proto:!0,forced:Ts!==[].lastIndexOf},{lastIndexOf:Ts});var bs,Rs=entryVirtual("Array").lastIndexOf,As=Array.prototype,lastIndexOf=function(t){var a=t.lastIndexOf;return t===As||Y(As,t)&&a===As.lastIndexOf?Rs:a},Ns="\t\n\v\f\r                　\u2028\u2029\ufeff",ws=A("".replace),Os="["+Ns+"]",xs=RegExp("^"+Os+Os+"*"),ks=RegExp(Os+Os+"*$"),createMethod$1=function(t){return function(a){var u=toString(requireObjectCoercible(a));return 1&t&&(u=ws(u,xs,"")),2&t&&(u=ws(u,ks,"")),u}},Ps={start:createMethod$1(1),end:createMethod$1(2),trim:createMethod$1(3)},Ls=xr.PROPER,qs=Ps.trim;_export({target:"String",proto:!0,forced:(bs="trim",fails((function(){return!!Ns[bs]()||"​᠎"!=="​᠎"[bs]()||Ls&&Ns[bs].name!==bs})))},{trim:function trim(){return qs(this)}}),entryVirtual("String").trim;var Ds=bo.findIndex,Vs="findIndex",Us=!0;Vs in[]&&Array(1).findIndex((function(){Us=!1})),_export({target:"Array",proto:!0,forced:Us},{findIndex:function findIndex(t){return Ds(this,t,arguments.length>1?arguments[1]:void 0)}});var Gs=entryVirtual("Array").findIndex,Fs=Array.prototype,findIndex=function(t){var a=t.findIndex;return t===Fs||Y(Fs,t)&&a===Fs.findIndex?Gs:a},Bs=fails((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),js=Object.isExtensible,Ws=fails((function(){js(1)}))||Bs?function isExtensible(t){return!!j(t)&&((!Bs||"ArrayBuffer"!=classofRaw(t))&&(!js||js(t)))}:js,Ys=!fails((function(){return Object.isExtensible(Object.preventExtensions({}))})),$s=createCommonjsModule((function(t){var a=je.f,u=!1,h=uid("meta"),m=0,setMetadata=function(t){a(t,h,{value:{objectID:"O"+m++,weakData:{}}})},g=t.exports={enable:function(){g.enable=function(){},u=!0;var t=Vt.f,a=A([].splice),m={};m[h]=1,t(m).length&&(Vt.f=function(u){for(var m=t(u),g=0,E=m.length;g<E;g++)if(m[g]===h){a(m,g,1);break}return m},_export({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:Mo.f}))},fastKey:function(t,a){if(!j(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!pe(t,h)){if(!Ws(t))return"F";if(!a)return"E";setMetadata(t)}return t[h].objectID},getWeakData:function(t,a){if(!pe(t,h)){if(!Ws(t))return!0;if(!a)return!1;setMetadata(t)}return t[h].weakData},onFreeze:function(t){return Ys&&u&&Ws(t)&&!pe(t,h)&&setMetadata(t),t}};Ze[h]=!0})),Hs=je.f,Qs=bo.forEach,Ks=Ar.set,zs=Ar.getterFor,collection=function(t,a,u){var h,g=-1!==t.indexOf("Map"),E=-1!==t.indexOf("Weak"),I=g?"set":"add",M=m[t],S=M&&M.prototype,C={};if(N&&isCallable(M)&&(E||S.forEach&&!fails((function(){(new M).entries().next()})))){var T=(h=a((function(a,u){Ks(anInstance(a,T),{type:t,collection:new M}),null!=u&&iterate(u,a[I],{that:a,AS_ENTRIES:g})}))).prototype,b=zs(t);Qs(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(t){var a="add"==t||"set"==t;!(t in S)||E&&"clear"==t||We(T,t,(function(u,h){var m=b(this).collection;if(!a&&E&&!j(u))return"get"==t&&void 0;var g=m[t](0===u?0:u,h);return a?this:g}))})),E||Hs(T,"size",{configurable:!0,get:function(){return b(this).collection.size}})}else h=u.getConstructor(a,t,g,I),$s.enable();return setToStringTag(h,t,!1,!0),C[t]=h,_export({global:!0,forced:!0},C),E||u.setStrong(h,t,g),h},defineBuiltIns=function(t,a,u){for(var h in a)u&&u.unsafe&&t[h]?t[h]=a[h]:defineBuiltIn(t,h,a[h],u);return t},Js=je.f,Xs=$s.fastKey,Zs=Ar.set,ec=Ar.getterFor,tc={getConstructor:function(t,a,u,h){var m=t((function(t,m){anInstance(t,g),Zs(t,{type:a,index:lt(null),first:void 0,last:void 0,size:0}),N||(t.size=0),isNullOrUndefined(m)||iterate(m,t[h],{that:t,AS_ENTRIES:u})})),g=m.prototype,E=ec(a),define=function(t,a,u){var h,m,g=E(t),I=getEntry(t,a);return I?I.value=u:(g.last=I={index:m=Xs(a,!0),key:a,value:u,previous:h=g.last,next:void 0,removed:!1},g.first||(g.first=I),h&&(h.next=I),N?g.size++:t.size++,"F"!==m&&(g.index[m]=I)),t},getEntry=function(t,a){var u,h=E(t),m=Xs(a);if("F"!==m)return h.index[m];for(u=h.first;u;u=u.next)if(u.key==a)return u};return defineBuiltIns(g,{clear:function clear(){for(var t=E(this),a=t.index,u=t.first;u;)u.removed=!0,u.previous&&(u.previous=u.previous.next=void 0),delete a[u.index],u=u.next;t.first=t.last=void 0,N?t.size=0:this.size=0},delete:function(t){var a=this,u=E(a),h=getEntry(a,t);if(h){var m=h.next,g=h.previous;delete u.index[h.index],h.removed=!0,g&&(g.next=m),m&&(m.previous=g),u.first==h&&(u.first=m),u.last==h&&(u.last=g),N?u.size--:a.size--}return!!h},forEach:function forEach(t){for(var a,u=E(this),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);a=a?a.next:u.first;)for(h(a.value,a.key,this);a&&a.removed;)a=a.previous},has:function has(t){return!!getEntry(this,t)}}),defineBuiltIns(g,u?{get:function get(t){var a=getEntry(this,t);return a&&a.value},set:function set(t,a){return define(this,0===t?0:t,a)}}:{add:function add(t){return define(this,t=0===t?0:t,t)}}),N&&Js(g,"size",{get:function(){return E(this).size}}),m},setStrong:function(t,a,u){var h=a+" Iterator",m=ec(a),g=ec(h);iteratorDefine(t,a,(function(t,a){Zs(this,{type:h,target:t,state:m(t),kind:a,last:void 0})}),(function(){for(var t=g(this),a=t.kind,u=t.last;u&&u.removed;)u=u.previous;return t.target&&(t.last=u=u?u.next:t.state.first)?"keys"==a?{value:u.key,done:!1}:"values"==a?{value:u.value,done:!1}:{value:[u.key,u.value],done:!1}:(t.target=void 0,{value:void 0,done:!0})}),u?"entries":"values",!u,!0),setSpecies(a)}};collection("Map",(function(t){return function Map(){return t(this,arguments.length?arguments[0]:void 0)}}),tc);var rc=W.Map,nc=[].push,ac=function from(t){var a,u,h,m,g=arguments.length,E=g>1?arguments[1]:void 0;return aConstructor(this),(a=void 0!==E)&&aCallable(E),isNullOrUndefined(t)?new this:(u=[],a?(h=0,m=functionBindContext(E,g>2?arguments[2]:void 0),iterate(t,(function(t){O(nc,u,m(t,h++))}))):iterate(t,nc,{that:u}),new this(u))};_export({target:"Map",stat:!0,forced:!0},{from:ac});var oc=function of(){return new this(gt(arguments))};_export({target:"Map",stat:!0,forced:!0},{of:oc});var ic=function deleteAll(){for(var t,a=anObject(this),u=aCallable(a.delete),h=!0,m=0,g=arguments.length;m<g;m++)t=O(u,a,arguments[m]),h=h&&t;return!!h};_export({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:ic});_export({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function emplace(t,a){var u,h,m=anObject(this),g=aCallable(m.get),E=aCallable(m.has),I=aCallable(m.set);return O(E,m,t)?(u=O(g,m,t),"update"in a&&(u=a.update(u,t,m),O(I,m,t,u)),u):(h=a.insert(t,m),O(I,m,t,h),h)}});var sc=getIterator;_export({target:"Map",proto:!0,real:!0,forced:!0},{every:function every(t){var a=anObject(this),u=sc(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return!iterate(u,(function(t,u,m){if(!h(u,t,a))return m()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{filter:function filter(t){var a=anObject(this),u=sc(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0),m=new(speciesConstructor(a,getBuiltIn("Map"))),g=aCallable(m.set);return iterate(u,(function(t,u){h(u,t,a)&&O(g,m,t,u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),m}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{find:function find(t){var a=anObject(this),u=sc(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,m){if(h(u,t,a))return m(u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function findKey(t){var a=anObject(this),u=sc(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,m){if(h(u,t,a))return m(t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var cc=A([].push);_export({target:"Map",stat:!0,forced:!0},{groupBy:function groupBy(t,a){aCallable(a);var u=getIterator(t),h=new this,m=aCallable(h.has),g=aCallable(h.get),E=aCallable(h.set);return iterate(u,(function(t){var u=a(t);O(m,h,u)?cc(O(g,h,u),t):O(E,h,u,[t])}),{IS_ITERATOR:!0}),h}});_export({target:"Map",proto:!0,real:!0,forced:!0},{includes:function includes(t){return iterate(sc(anObject(this)),(function(a,u,h){if((m=u)===(g=t)||m!=m&&g!=g)return h();var m,g}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Map",stat:!0,forced:!0},{keyBy:function keyBy(t,a){var u=new this;aCallable(a);var h=aCallable(u.set);return iterate(t,(function(t){O(h,u,a(t),t)})),u}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function keyOf(t){return iterate(sc(anObject(this)),(function(a,u,h){if(u===t)return h(a)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function mapKeys(t){var a=anObject(this),u=sc(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0),m=new(speciesConstructor(a,getBuiltIn("Map"))),g=aCallable(m.set);return iterate(u,(function(t,u){O(g,m,h(u,t,a),u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),m}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function mapValues(t){var a=anObject(this),u=sc(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0),m=new(speciesConstructor(a,getBuiltIn("Map"))),g=aCallable(m.set);return iterate(u,(function(t,u){O(g,m,t,h(u,t,a))}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),m}}),_export({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function merge(t){for(var a=anObject(this),u=aCallable(a.set),h=arguments.length,m=0;m<h;)iterate(arguments[m++],u,{that:a,AS_ENTRIES:!0});return a}});var lc=TypeError;_export({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function reduce(t){var a=anObject(this),u=sc(a),h=arguments.length<2,m=h?void 0:arguments[1];if(aCallable(t),iterate(u,(function(u,g){h?(h=!1,m=g):m=t(m,g,u,a)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),h)throw lc("Reduce of empty map with no initial value");return m}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{some:function some(t){var a=anObject(this),u=sc(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,m){if(h(u,t,a))return m()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var uc=TypeError;_export({target:"Map",proto:!0,real:!0,forced:!0},{update:function update(t,a){var u=anObject(this),h=aCallable(u.get),m=aCallable(u.has),g=aCallable(u.set),E=arguments.length;aCallable(a);var I=O(m,u,t);if(!I&&E<3)throw uc("Updating absent value");var M=I?O(h,u,t):aCallable(E>2?arguments[2]:void 0)(t,u);return O(g,u,t,a(M,t,u)),u}});var dc=TypeError,pc=function upsert(t,a){var u,h=anObject(this),m=aCallable(h.get),g=aCallable(h.has),E=aCallable(h.set),I=arguments.length>2?arguments[2]:void 0;if(!isCallable(a)&&!isCallable(I))throw dc("At least one callback required");return O(g,h,t)?(u=O(m,h,t),isCallable(a)&&(u=a(u),O(E,h,t,u))):isCallable(I)&&(u=I(),O(E,h,t,u)),u};_export({target:"Map",proto:!0,real:!0,forced:!0},{upsert:pc}),_export({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:pc});var hc=rc,mc=createCommonjsModule((function(t){function _getPrototypeOf(a){var u;return t.exports=_getPrototypeOf=ft?bind$1(u=Ei).call(u):function _getPrototypeOf(t){return t.__proto__||Ei(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(a)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports})),fc=createCommonjsModule((function(t){t.exports=function _isNativeFunction(t){var a;return-1!==indexOf(a=Function.toString.call(t)).call(a,"[native code]")},t.exports.__esModule=!0,t.exports.default=t.exports})),gc=getBuiltIn("Reflect","construct"),vc=Object.prototype,_c=[].push,yc=fails((function(){function F(){}return!(gc((function(){}),[],F)instanceof F)})),Ec=!fails((function(){gc((function(){}))})),Ic=yc||Ec;_export({target:"Reflect",stat:!0,forced:Ic,sham:Ic},{construct:function construct(t,a){aConstructor(t),anObject(a);var u=arguments.length<3?t:aConstructor(arguments[2]);if(Ec&&!yc)return gc(t,a,u);if(t==u){switch(a.length){case 0:return new t;case 1:return new t(a[0]);case 2:return new t(a[0],a[1]);case 3:return new t(a[0],a[1],a[2]);case 4:return new t(a[0],a[1],a[2],a[3])}var h=[null];return S(_c,h,a),new(S(It,t,h))}var m=u.prototype,g=lt(j(m)?m:vc),E=S(t,g,a);return j(E)?E:g}});var Mc=W.Reflect.construct,Sc=createCommonjsModule((function(t){t.exports=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Mc)return!1;if(Mc.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Mc(Boolean,[],(function(){}))),!0}catch(t){return!1}},t.exports.__esModule=!0,t.exports.default=t.exports})),Cc=createCommonjsModule((function(t){function _construct(a,u,h){var m;Sc()?(t.exports=_construct=bind$1(m=Mc).call(m),t.exports.__esModule=!0,t.exports.default=t.exports):(t.exports=_construct=function _construct(t,a,u){var h=[null];h.push.apply(h,a);var m=new(bind$1(Function).apply(t,h));return u&&Ct(m,u.prototype),m},t.exports.__esModule=!0,t.exports.default=t.exports);return _construct.apply(null,arguments)}t.exports=_construct,t.exports.__esModule=!0,t.exports.default=t.exports})),Tc=createCommonjsModule((function(t){function _wrapNativeSuper(a){var u="function"==typeof hc?new hc:void 0;return t.exports=_wrapNativeSuper=function _wrapNativeSuper(t){if(null===t||!fc(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==u){if(u.has(t))return u.get(t);u.set(t,Wrapper)}function Wrapper(){return Cc(t,arguments,mc(this).constructor)}return Wrapper.prototype=dt(t.prototype,{constructor:{value:Wrapper,enumerable:!1,writable:!0,configurable:!0}}),Ct(Wrapper,t)},t.exports.__esModule=!0,t.exports.default=t.exports,_wrapNativeSuper(a)}t.exports=_wrapNativeSuper,t.exports.__esModule=!0,t.exports.default=t.exports})),bc=getDefaultExportFromCjs(Tc),Rc=TypeError,createMethod=function(t){return function(a,u,h,m){aCallable(u);var g=toObject(a),E=U(g),I=lengthOfArrayLike(g),M=t?I-1:0,S=t?-1:1;if(h<2)for(;;){if(M in E){m=E[M],M+=S;break}if(M+=S,t?M<0:I<=M)throw Rc("Reduce of empty array with no initial value")}for(;t?M>=0:I>M;M+=S)M in E&&(m=u(m,E[M],M,g));return m}},Ac={left:createMethod(!1),right:createMethod(!0)}.left,Nc=arrayMethodIsStrict("reduce");_export({target:"Array",proto:!0,forced:!Nc||!zr&&X>79&&X<83},{reduce:function reduce(t){var a=arguments.length;return Ac(this,t,a,a>1?arguments[1]:void 0)}});var wc,Oc,xc,kc,Pc,Lc,qc,Dc,Vc,Uc,Gc,Fc,Bc,jc,Wc,Yc,$c,Hc,Qc,Kc,zc,Jc,Xc,Zc,el,tl,rl,nl,al,ol,il,sl,cl,ll,ul,dl,pl,hl,ml,fl,gl,vl,_l,yl,El,Il,Ml,Sl,Cl,Tl,bl,Rl,Al,Nl=entryVirtual("Array").reduce,wl=Array.prototype,reduce=function(t){var a=t.reduce;return t===wl||Y(wl,t)&&a===wl.reduce?Nl:a};!function(t){t[t.V2NIM_DATA_SYNC_TYPE_LEVEL_FULL=0]="V2NIM_DATA_SYNC_TYPE_LEVEL_FULL",t[t.V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC=1]="V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC"}(wc||(wc={})),function(t){t[t.V2NIM_DATA_SYNC_TYPE_MAIN=1]="V2NIM_DATA_SYNC_TYPE_MAIN",t[t.V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER=2]="V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER",t[t.V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER=3]="V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER"}(Oc||(Oc={})),function(t){t[t.V2NIM_DATA_SYNC_STATE_WAITING=1]="V2NIM_DATA_SYNC_STATE_WAITING",t[t.V2NIM_DATA_SYNC_STATE_SYNCING=2]="V2NIM_DATA_SYNC_STATE_SYNCING",t[t.V2NIM_DATA_SYNC_STATE_COMPLETED=3]="V2NIM_DATA_SYNC_STATE_COMPLETED"}(xc||(xc={})),function(t){t[t.V2NIM_CONVERSATION_TYPE_UNKNOWN=0]="V2NIM_CONVERSATION_TYPE_UNKNOWN",t[t.V2NIM_CONVERSATION_TYPE_P2P=1]="V2NIM_CONVERSATION_TYPE_P2P",t[t.V2NIM_CONVERSATION_TYPE_TEAM=2]="V2NIM_CONVERSATION_TYPE_TEAM",t[t.V2NIM_CONVERSATION_TYPE_SUPER_TEAM=3]="V2NIM_CONVERSATION_TYPE_SUPER_TEAM"}(kc||(kc={})),function(t){t[t.V2NIM_MESSAGE_STATUS_DEFAULT=0]="V2NIM_MESSAGE_STATUS_DEFAULT",t[t.V2NIM_MESSAGE_STATUS_REVOKE=1]="V2NIM_MESSAGE_STATUS_REVOKE",t[t.V2NIM_MESSAGE_STATUS_BACKFILL=2]="V2NIM_MESSAGE_STATUS_BACKFILL"}(Pc||(Pc={})),function(t){t[t.V2NIM_FRIEND_MODE_TYPE_ADD=1]="V2NIM_FRIEND_MODE_TYPE_ADD",t[t.V2NIM_FRIEND_MODE_TYPE_APPLY=2]="V2NIM_FRIEND_MODE_TYPE_APPLY"}(Lc||(Lc={})),function(t){t[t.V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED=1]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED",t[t.V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED"}(qc||(qc={})),function(t){t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT=0]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED=1]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED=3]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD=4]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD"}(Dc||(Dc={})),function(t){t[t.V2NIM_FRIEND_DELETION_TYPE_BY_SELF=1]="V2NIM_FRIEND_DELETION_TYPE_BY_SELF",t[t.V2NIM_FRIEND_DELETION_TYPE_BY_PEER=2]="V2NIM_FRIEND_DELETION_TYPE_BY_PEER"}(Vc||(Vc={})),function(t){t[t.V2NIM_FRIEND_VERIFY_TYPE_ADD=1]="V2NIM_FRIEND_VERIFY_TYPE_ADD",t[t.V2NIM_FRIEND_VERIFY_TYPE_APPLY=2]="V2NIM_FRIEND_VERIFY_TYPE_APPLY",t[t.V2NIM_FRIEND_VERIFY_TYPE_ACCEPT=3]="V2NIM_FRIEND_VERIFY_TYPE_ACCEPT",t[t.V2NIM_FRIEND_VERIFY_TYPE_REJECT=4]="V2NIM_FRIEND_VERIFY_TYPE_REJECT"}(Uc||(Uc={})),function(t){t[t.V2NIM_LOGIN_AUTH_TYPE_DEFAULT=0]="V2NIM_LOGIN_AUTH_TYPE_DEFAULT",t[t.V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN=1]="V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN",t[t.V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY=2]="V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY"}(Gc||(Gc={})),function(t){t[t.V2NIM_LOGIN_STATUS_LOGOUT=0]="V2NIM_LOGIN_STATUS_LOGOUT",t[t.V2NIM_LOGIN_STATUS_LOGINED=1]="V2NIM_LOGIN_STATUS_LOGINED",t[t.V2NIM_LOGIN_STATUS_LOGINING=2]="V2NIM_LOGIN_STATUS_LOGINING",t[t.V2NIM_LOGIN_STATUS_UNLOGIN=3]="V2NIM_LOGIN_STATUS_UNLOGIN"}(Fc||(Fc={})),function(t){t[t.V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN=0]="V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN",t[t.V2NIM_LOGIN_CLIENT_TYPE_ANDROID=1]="V2NIM_LOGIN_CLIENT_TYPE_ANDROID",t[t.V2NIM_LOGIN_CLIENT_TYPE_IOS=2]="V2NIM_LOGIN_CLIENT_TYPE_IOS",t[t.V2NIM_LOGIN_CLIENT_TYPE_PC=4]="V2NIM_LOGIN_CLIENT_TYPE_PC",t[t.V2NIM_LOGIN_CLIENT_TYPE_WP=8]="V2NIM_LOGIN_CLIENT_TYPE_WP",t[t.V2NIM_LOGIN_CLIENT_TYPE_WEB=16]="V2NIM_LOGIN_CLIENT_TYPE_WEB",t[t.V2NIM_LOGIN_CLIENT_TYPE_RESTFUL=32]="V2NIM_LOGIN_CLIENT_TYPE_RESTFUL",t[t.V2NIM_LOGIN_CLIENT_TYPE_MAC_OS=64]="V2NIM_LOGIN_CLIENT_TYPE_MAC_OS",t[t.V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS=65]="V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS"}(Bc||(Bc={})),function(t){t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE=1]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE",t[t.V2NIM_KICKED_OFFLINE_REASON_SERVER=2]="V2NIM_KICKED_OFFLINE_REASON_SERVER",t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT=3]="V2NIM_KICKED_OFFLINE_REASON_CLIENT",t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY=4]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY"}(jc||(jc={})),function(t){t[t.V2NIM_LOGIN_CLIENT_CHANGE_LIST=1]="V2NIM_LOGIN_CLIENT_CHANGE_LIST",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGIN=2]="V2NIM_LOGIN_CLIENT_CHANGE_LOGIN",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT=3]="V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT"}(Wc||(Wc={})),function(t){t[t.V2NIM_CONNECT_STATUS_DISCONNECTED=0]="V2NIM_CONNECT_STATUS_DISCONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTED=1]="V2NIM_CONNECT_STATUS_CONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTING=2]="V2NIM_CONNECT_STATUS_CONNECTING",t[t.V2NIM_CONNECT_STATUS_WAITING=3]="V2NIM_CONNECT_STATUS_WAITING"}(Yc||(Yc={})),function(t){t[t.NIM_MESSAGE_AI_STREAM_STATUS_STREAMING=-1]="NIM_MESSAGE_AI_STREAM_STATUS_STREAMING",t[t.NIM_MESSAGE_AI_STREAM_STATUS_NONE=0]="NIM_MESSAGE_AI_STREAM_STATUS_NONE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER=1]="NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER",t[t.NIM_MESSAGE_AI_STREAM_STATUS_CANCEL=2]="NIM_MESSAGE_AI_STREAM_STATUS_CANCEL",t[t.NIM_MESSAGE_AI_STREAM_STATUS_UPDATE=3]="NIM_MESSAGE_AI_STREAM_STATUS_UPDATE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE=4]="NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION=5]="NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION"}($c||($c={})),function(t){t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT=0]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT",t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE=1]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE",t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE=2]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE"}(Hc||(Hc={})),function(t){t[t.V2NIM_MESSAGE_AI_REGEN_OP_UPDATE=1]="V2NIM_MESSAGE_AI_REGEN_OP_UPDATE",t[t.V2NIM_MESSAGE_AI_REGEN_OP_NEW=2]="V2NIM_MESSAGE_AI_REGEN_OP_NEW"}(Qc||(Qc={})),function(t){t[t.V2NIM_MESSAGE_AI_STATUS_UNKNOW=0]="V2NIM_MESSAGE_AI_STATUS_UNKNOW",t[t.V2NIM_MESSAGE_AI_STATUS_AT=1]="V2NIM_MESSAGE_AI_STATUS_AT",t[t.V2NIM_MESSAGE_AI_STATUS_RESPONSE=2]="V2NIM_MESSAGE_AI_STATUS_RESPONSE"}(Kc||(Kc={})),function(t){t[t.V2NIM_MESSAGE_TYPE_INVALID=-1]="V2NIM_MESSAGE_TYPE_INVALID",t[t.V2NIM_MESSAGE_TYPE_TEXT=0]="V2NIM_MESSAGE_TYPE_TEXT",t[t.V2NIM_MESSAGE_TYPE_IMAGE=1]="V2NIM_MESSAGE_TYPE_IMAGE",t[t.V2NIM_MESSAGE_TYPE_AUDIO=2]="V2NIM_MESSAGE_TYPE_AUDIO",t[t.V2NIM_MESSAGE_TYPE_VIDEO=3]="V2NIM_MESSAGE_TYPE_VIDEO",t[t.V2NIM_MESSAGE_TYPE_LOCATION=4]="V2NIM_MESSAGE_TYPE_LOCATION",t[t.V2NIM_MESSAGE_TYPE_NOTIFICATION=5]="V2NIM_MESSAGE_TYPE_NOTIFICATION",t[t.V2NIM_MESSAGE_TYPE_FILE=6]="V2NIM_MESSAGE_TYPE_FILE",t[t.V2NIM_MESSAGE_TYPE_AVCHAT=7]="V2NIM_MESSAGE_TYPE_AVCHAT",t[t.V2NIM_MESSAGE_TYPE_TIPS=10]="V2NIM_MESSAGE_TYPE_TIPS",t[t.V2NIM_MESSAGE_TYPE_ROBOT=11]="V2NIM_MESSAGE_TYPE_ROBOT",t[t.V2NIM_MESSAGE_TYPE_CALL=12]="V2NIM_MESSAGE_TYPE_CALL",t[t.V2NIM_MESSAGE_TYPE_CUSTOM=100]="V2NIM_MESSAGE_TYPE_CUSTOM"}(zc||(zc={})),function(t){t[t.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR=0]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR",t[t.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND=1]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND"}(Jc||(Jc={})),function(t){t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED=-1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE=0]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK=1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE=2]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO=3]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS=4]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS=5]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER=6]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER=7]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER=8]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT=9]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER=10]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE=401]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK=402]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE=403]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO=404]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS=405]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS=410]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER=406]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER=407]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER=408]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT=411]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER=409]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER"}(Xc||(Xc={})),function(t){t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN=0]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS=1]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED=2]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING=3]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING"}(Zc||(Zc={})),function(t){t[t.V2NIM_MESSAGE_SENDING_STATE_UNKNOWN=0]="V2NIM_MESSAGE_SENDING_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED=1]="V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED",t[t.V2NIM_MESSAGE_SENDING_STATE_FAILED=2]="V2NIM_MESSAGE_SENDING_STATE_FAILED",t[t.V2NIM_MESSAGE_SENDING_STATE_SENDING=3]="V2NIM_MESSAGE_SENDING_STATE_SENDING"}(el||(el={})),function(t){t[t.V2NIM_QUERY_DIRECTION_DESC=0]="V2NIM_QUERY_DIRECTION_DESC",t[t.V2NIM_QUERY_DIRECTION_ASC=1]="V2NIM_QUERY_DIRECTION_ASC"}(tl||(tl={})),function(t){t[t.V2NIM_CLEAR_HISTORY_MODE_ALL=0]="V2NIM_CLEAR_HISTORY_MODE_ALL",t[t.V2NIM_CLEAR_HISTORY_MODE_LOCAL=1]="V2NIM_CLEAR_HISTORY_MODE_LOCAL"}(rl||(rl={})),function(t){t[t.V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED=0]="V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED",t[t.V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY=1]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY=2]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY=3]="V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY=4]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY=5]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY"}(nl||(nl={})),function(t){t[t.V2NIM_MESSAGE_PIN_STATE_NOT_PINNED=0]="V2NIM_MESSAGE_PIN_STATE_NOT_PINNED",t[t.V2NIM_MESSAGE_PIN_STATE_PINNED=1]="V2NIM_MESSAGE_PIN_STATE_PINNED",t[t.V2NIM_MESSAGE_PIN_STATE_UPDATED=2]="V2NIM_MESSAGE_PIN_STATE_UPDATED"}(al||(al={})),function(t){t[t.V2NIM_QUICK_COMMENT_STATE_ADD=1]="V2NIM_QUICK_COMMENT_STATE_ADD",t[t.V2NIM_QUICK_COMMENT_STATE_REMOVE=2]="V2NIM_QUICK_COMMENT_STATE_REMOVE"}(ol||(ol={})),function(t){t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_NONE=0]="V2NIM_CLIENT_ANTISPAM_OPERATE_NONE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE=1]="V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD=2]="V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD=3]="V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD"}(il||(il={})),function(t){t[t.V2NIM_SORT_ORDER_DESC=0]="V2NIM_SORT_ORDER_DESC",t[t.V2NIM_SORT_ORDER_ASC=1]="V2NIM_SORT_ORDER_ASC"}(sl||(sl={})),function(t){t[t.P2P_DELETE_MSG=7]="P2P_DELETE_MSG",t[t.TEAM_DELETE_MSG=8]="TEAM_DELETE_MSG",t[t.SUPERTEAM_DELETE_MSG=12]="SUPERTEAM_DELETE_MSG",t[t.P2P_ONE_WAY_DELETE_MSG=13]="P2P_ONE_WAY_DELETE_MSG",t[t.TEAM_ONE_WAY_DELETE_MSG=14]="TEAM_ONE_WAY_DELETE_MSG",t[t.CUSTOM_P2P_MSG=100]="CUSTOM_P2P_MSG",t[t.CUSTOM_TEAM_MSG=101]="CUSTOM_TEAM_MSG",t[t.CUSTOM_SUPERTEAM_MSG=103]="CUSTOM_SUPERTEAM_MSG"}(cl||(cl={})),function(t){t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF",t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_ON=1]="V2NIM_TEAM_MESSAGE_MUTE_MODE_ON",t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON=2]="V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON"}(ll||(ll={})),function(t){t[t.V2NIM_P2P_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_P2P_MESSAGE_MUTE_MODE_OFF",t[t.V2NIM_P2P_MESSAGE_MUTE_MODE_ON=1]="V2NIM_P2P_MESSAGE_MUTE_MODE_ON"}(ul||(ul={})),function(t){t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL=0]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL",t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL=1]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL",t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER"}(dl||(dl={})),function(t){t[t.V2NIM_TEAM_TYPE_INVALID=0]="V2NIM_TEAM_TYPE_INVALID",t[t.V2NIM_TEAM_TYPE_ADVANCED=1]="V2NIM_TEAM_TYPE_ADVANCED",t[t.V2NIM_TEAM_TYPE_SUPER=2]="V2NIM_TEAM_TYPE_SUPER"}(pl||(pl={})),function(t){t[t.V2NIM_TEAM_JOIN_MODE_FREE=0]="V2NIM_TEAM_JOIN_MODE_FREE",t[t.V2NIM_TEAM_JOIN_MODE_APPLY=1]="V2NIM_TEAM_JOIN_MODE_APPLY",t[t.V2NIM_TEAM_JOIN_MODE_INVITE=2]="V2NIM_TEAM_JOIN_MODE_INVITE"}(hl||(hl={})),function(t){t[t.V2NIM_TEAM_AGREE_MODE_AUTH=0]="V2NIM_TEAM_AGREE_MODE_AUTH",t[t.V2NIM_TEAM_AGREE_MODE_NO_AUTH=1]="V2NIM_TEAM_AGREE_MODE_NO_AUTH"}(ml||(ml={})),function(t){t[t.V2NIM_TEAM_INVITE_MODE_MANAGER=0]="V2NIM_TEAM_INVITE_MODE_MANAGER",t[t.V2NIM_TEAM_INVITE_MODE_ALL=1]="V2NIM_TEAM_INVITE_MODE_ALL"}(fl||(fl={})),function(t){t[t.V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER",t[t.V2NIM_TEAM_UPDATE_INFO_MODE_ALL=1]="V2NIM_TEAM_UPDATE_INFO_MODE_ALL"}(gl||(gl={})),function(t){t[t.V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN=0]="V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN",t[t.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL=1]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL",t[t.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL=3]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL"}(vl||(vl={})),function(t){t[t.V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER",t[t.V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL=1]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL"}(_l||(_l={})),function(t){t[t.V2NIM_TEAM_MEMBER_ROLE_NORMAL=0]="V2NIM_TEAM_MEMBER_ROLE_NORMAL",t[t.V2NIM_TEAM_MEMBER_ROLE_OWNER=1]="V2NIM_TEAM_MEMBER_ROLE_OWNER",t[t.V2NIM_TEAM_MEMBER_ROLE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_MANAGER"}(yl||(yl={})),function(t){t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION=0]="V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION=1]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION=2]="V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION=3]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION"}(El||(El={})),function(t){t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_INIT=0]="V2NIM_TEAM_JOIN_ACTION_STATUS_INIT",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED=1]="V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED=2]="V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED=3]="V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED"}(Il||(Il={})),function(t){t[t.teamApply=0]="teamApply",t[t.teamApplyReject=1]="teamApplyReject",t[t.teamInvite=2]="teamInvite",t[t.teamInviteReject=3]="teamInviteReject",t[t.tlistUpdate=4]="tlistUpdate",t[t.superTeamApply=15]="superTeamApply",t[t.superTeamApplyReject=16]="superTeamApplyReject",t[t.superTeamInvite=17]="superTeamInvite",t[t.superTeamInviteReject=18]="superTeamInviteReject"}(Ml||(Ml={})),function(t){t[t.V2NIM_AI_MODEL_TYPE_UNKNOW=0]="V2NIM_AI_MODEL_TYPE_UNKNOW",t[t.V2NIM_AI_MODEL_TYPE_QWEN=1]="V2NIM_AI_MODEL_TYPE_QWEN",t[t.V2NIM_AI_MODEL_TYPE_AZURE=2]="V2NIM_AI_MODEL_TYPE_AZURE",t[t.V2NIM_AI_MODEL_TYPE_PRIVATE=3]="V2NIM_AI_MODEL_TYPE_PRIVATE"}(Sl||(Sl={})),function(t){t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE=0]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL=2]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE=4]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION=5]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION"}(Cl||(Cl={})),function(t){t.V2NIM_AI_MODEL_ROLE_TYPE_SYSTEM="system",t.V2NIM_AI_MODEL_ROLE_TYPE_USER="user",t.V2NIM_AI_MODEL_ROLE_TYPE_ASSISTANT="assistant"}(Tl||(Tl={})),function(t){t[t.V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN=0]="V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CLOSE=1]="V2NIM_SIGNALLING_EVENT_TYPE_CLOSE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_JOIN=2]="V2NIM_SIGNALLING_EVENT_TYPE_JOIN",t[t.V2NIM_SIGNALLING_EVENT_TYPE_INVITE=3]="V2NIM_SIGNALLING_EVENT_TYPE_INVITE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE=4]="V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_REJECT=5]="V2NIM_SIGNALLING_EVENT_TYPE_REJECT",t[t.V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT=6]="V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT",t[t.V2NIM_SIGNALLING_EVENT_TYPE_LEAVE=7]="V2NIM_SIGNALLING_EVENT_TYPE_LEAVE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CONTROL=8]="V2NIM_SIGNALLING_EVENT_TYPE_CONTROL"}(bl||(bl={})),function(t){t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO=1]="V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO",t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO=2]="V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO",t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM=3]="V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM"}(Rl||(Rl={})),function(t){t[t.V2NIM_USER_STATUS_TYPE_UNKNOWN=0]="V2NIM_USER_STATUS_TYPE_UNKNOWN",t[t.V2NIM_USER_STATUS_TYPE_LOGIN=1]="V2NIM_USER_STATUS_TYPE_LOGIN",t[t.V2NIM_USER_STATUS_TYPE_LOGOUT=2]="V2NIM_USER_STATUS_TYPE_LOGOUT",t[t.V2NIM_USER_STATUS_TYPE_DISCONNECT=3]="V2NIM_USER_STATUS_TYPE_DISCONNECT"}(Al||(Al={}));var Ol={V2NIM_ERROR_CODE_UNKNOWN:{code:0,message:"unknown error"},V2NIM_ERROR_CODE_SUCCESS:{code:200,message:"success"},V2NIM_ERROR_CODE_HANDSHAKE:{code:201,message:"handshake error"},V2NIM_ERROR_CODE_REQUEST_TEMPERARY_FORBIDDEN:{code:398,message:"request temprary forbidden"},V2NIM_ERROR_CODE_SERVER_UNIT_ERROR:{code:399,message:"server unit error"},V2NIM_ERROR_CODE_FORBIDDEN:{code:403,message:"forbidden"},V2NIM_ERROR_CODE_NOT_FOUND:{code:404,message:"not found"},V2NIM_ERROR_CODE_PARAMETER_ERROR:{code:414,message:"parameter error"},V2NIM_ERROR_CODE_RATE_LIMIT_REACHED:{code:416,message:"rate limit reached"},V2NIM_ERROR_CODE_MULTI_LOGIN_FORBIDDEN:{code:417,message:"multi login forbidden"},V2NIM_ERROR_CODE_SERVER_INTERNAL_ERROR:{code:500,message:"server internal error"},V2NIM_ERROR_CODE_SERVER_BUSY:{code:503,message:"server busy"},V2NIM_ERROR_CODE_APP_UNREACHABLE:{code:511,message:"app server unreachable"},V2NIM_ERROR_CODE_SERVICE_UNAVAILABLE:{code:514,message:"service unavailable"},V2NIM_ERROR_CODE_PROTOCOL_BLACKHOLE_FILTERED:{code:599,message:"protocol filtered by blackhole rule"},V2NIM_ERROR_CODE_NO_PERMISSION:{code:997,message:"appid has no permission to call the protocol"},V2NIM_ERROR_CODE_UNPACK_ERROR:{code:998,message:"unpack error"},V2NIM_ERROR_CODE_PACK_ERROR:{code:999,message:"pack error"},V2NIM_ERROR_CODE_IM_DISABLED:{code:101301,message:"IM disabled"},V2NIM_ERROR_CODE_SERVICE_ADDRESS_INVALID:{code:101302,message:"service address invalid"},V2NIM_ERROR_CODE_APPKEY_NOT_EXIST:{code:101303,message:"appkey not exist"},V2NIM_ERROR_CODE_BUNDLEID_CHECK_FAILED:{code:101304,message:"bundleid check failed"},V2NIM_ERROR_CODE_APPKEY_BLOCKED:{code:101403,message:"appkey blocked"},V2NIM_ERROR_CODE_INVALID_TOKEN:{code:102302,message:"invalid token"},V2NIM_ERROR_CODE_ROBOT_NOT_ALLOWED:{code:102303,message:"robot not allowed"},V2NIM_ERROR_CODE_ACCOUNT_NOT_EXIST:{code:102404,message:"account not exist"},V2NIM_ERROR_CODE_ACCOUNT_CHAT_BANNED:{code:102421,message:"account chat banned"},V2NIM_ERROR_CODE_ACCOUNT_BANNED:{code:102422,message:"account banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_BLOCK_LIST:{code:102426,message:"account in block list"},V2NIM_ERROR_CODE_USER_PROFILE_NOT_EXIST:{code:103404,message:"user profile not exist"},V2NIM_ERROR_CODE_USER_PROFILE_HIT_ANTISPAM:{code:103451,message:"user profile hit antispam"},V2NIM_ERROR_CODE_PEER_FRIEND_LIMIT:{code:104301,message:"peer friend limit"},V2NIM_ERROR_CODE_FRIEND_APPLICATION_NOT_EXIST:{code:104302,message:"friend application not exist"},V2NIM_ERROR_CODE_FRIEND_NOT_EXIST:{code:104404,message:"friend not exist"},V2NIM_ERROR_CODE_FRIEND_ALREADY_EXIST:{code:104405,message:"friend already exist"},V2NIM_ERROR_CODE_SELF_FRIEND_OPERATION_NOT_ALLOWED:{code:104429,message:"self friend operation not allowed"},V2NIM_ERROR_CODE_FRIEND_LIMIT:{code:104435,message:"friend limit"},V2NIM_ERROR_CODE_FRIEND_OPERATION_RATE_LIMIT:{code:104449,message:"friend operation rate limit"},V2NIM_ERROR_CODE_FRIEND_HIT_ANTISPAM:{code:104451,message:"friend hit antispam"},V2NIM_ERROR_CODE_SELF_MUTE_OPERATION_NOT_ALLOWED:{code:105429,message:"self mute operation not allowed"},V2NIM_ERROR_CODE_MUTE_LIST_LIMIT:{code:105435,message:"mute list limit"},V2NIM_ERROR_CODE_SELF_BLOCK_LIST_OPERATION_NOT_ALLOWED:{code:106429,message:"self block list operation not allowed"},V2NIM_ERROR_CODE_BLOCK_LIST_LIMIT:{code:106435,message:"block list limit"},V2NIM_ERROR_CODE_REVOKE_THIRD_PARTY_MESSAGE_NOT_ALLOWED:{code:107301,message:"revoke third party message not allowed"},V2NIM_ERROR_CODE_SHORT_TO_LONG_URL_FAILED:{code:107307,message:"short to long URL failed"},V2NIM_ERROR_CODE_URL_INVALID:{code:107308,message:"URL invalid"},V2NIM_ERROR_CODE_DURATION_OUT_OF_RANGE:{code:107309,message:"duration out of range"},V2NIM_ERROR_CODE_GET_FILE_META_INFO_FAILED:{code:107310,message:"get file meta info failed"},V2NIM_ERROR_CODE_AUDIO_FILE_SIZE_LIMIT:{code:107311,message:"audio file size limit"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_TIMEOUT:{code:107312,message:"voice to text timeout"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FAILED:{code:107313,message:"voice to text failed"},V2NIM_ERROR_CODE_REVOKE_EXCEED_TIME_LIMIT:{code:107314,message:"revoke message exceed time limit"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_NOT_ALLOWED:{code:107315,message:"revoke specific message not allowed"},V2NIM_ERROR_CODE_FORCE_PUSH_LIST_LIMIT:{code:107316,message:"force push list limit"},V2NIM_ERROR_CODE_TEAM_MESSAGE_RECEIPT_RATE_LIMIT:{code:107317,message:"team message receipt rate limit"},V2NIM_ERROR_CODE_SNAPSHOT_NOT_EXIST:{code:107318,message:"snapshot not exist"},V2NIM_ERROR_CODE_PIN_LIMIT:{code:107319,message:"pin limit"},V2NIM_ERROR_CODE_PIN_NOT_EXIST:{code:107320,message:"pin not exist"},V2NIM_ERROR_CODE_QUICK_COMMENT_LIMIT:{code:107321,message:"quick comment limit"},V2NIM_ERROR_CODE_PIN_ALREADY_EXIST:{code:107322,message:"pin already exist"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FUNCTION_DISABLED:{code:107333,message:"voice to text function disabled"},V2NIM_ERROR_CODE_CLOUD_SEARCH_FUNCTION_DISABLED:{code:107334,message:"cloud search function disabled"},V2NIM_ERROR_CODE_ONE_WAY_DELETE_FUNCTION_DISABLED:{code:107335,message:"one-way delete function disabled"},V2NIM_ERRPR_CODE_ONEWAY_DELETION_NOT_ALLOW_FOR_TARGET_MESSAGES:{code:107338,message:"one-way deletion is not allowed for target messages"},V2NIM_ERRPR_CODE_SENDER_CANNOT_INCLUDED_IN_TARGET_LIST:{code:107339,message:"The message sender cannot be included in the target list"},V2NIM_ERROR_CODE_ROBOT_CANNOT_SEND_TARGET_MESSAGE:{code:107340,message:"Robot can not send target message"},V2NIM_ERROR_CODE_PIN_TARGET_MESSAGE_NOT_ALLOWED:{code:107345,message:"Pin target message is not allowed"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_REPLY:{code:107346,message:"Target message not allowed reply"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_QUICK_COMMENT:{code:107347,message:"Target message not allowed quick comment"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_TO_SELF_NOT_ALLOWED:{code:107429,message:"revoke message to self not allowed"},V2NIM_ERROR_CODE_APP_CHAT_BANNED:{code:107410,message:"app chat banned"},V2NIM_ERROR_CODE_QUICK_COMMENT_FUNCTION_DISABLED:{code:107326,message:"quick comment function disabled"},V2NIM_ERROR_CODE_PIN_FUNCTION_DISABLED:{code:107327,message:"PIN function disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_FUNCTION_DISABLED:{code:107324,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_P2P_READ_RECEIPT_FUNCTION_DISABLED:{code:107325,message:"read receipt for p2p messages function disabled"},V2NIM_ERROR_CODE_RATE_LIMIT_FOR_MESSAGING_REACHED:{code:107323,message:"rate limit for messaging reached"},V2NIM_ERROR_CODE_MESSAGE_HIT_ANTISPAM:{code:107451,message:"message hit antispam"},V2NIM_ERROR_CODE_MESSAGE_NOT_EXIST:{code:107404,message:"message not exist"},V2NIM_ERROR_CODE_UNSENDING_MESSAGE_EXPIRED:{code:107406,message:"unsending message expired"},V2NIM_ERROR_CODE_TEAM_MARK_READ_FAILED:{code:107302,message:"sending message failed for marking message read failed for too many team members"},V2NIM_ERROR_CODE_SENDER_OR_MANAGER_PERMISSION_ONLY_REVOKE:{code:107303,message:"only sender or manager can revoke message"},V2NIM_ERROR_CODE_DELETE_SELF_MESSAGE_NOT_ALLOWED:{code:107328,message:"delete self message not allowed"},V2NIM_ERROR_CODE_NOT_CHATBOT_ACCOUNT:{code:107329,message:"is not chatbot account"},V2NIM_ERROR_CODE_MESSAGE_SENSE_REQUIRED:{code:107330,message:"sender or receiver must sense message"},V2NIM_ERROR_CODE_HIGH_PRIORITY_MESSAGE_RATE_LIMIT:{code:107304,message:"rate limit of high-priority messages exceeded"},ACK_MESSAGE_BE_HIGH_PRIORITY:{code:107305,message:"ack message should be high-priority"},V2NIM_ERROR_CODE_DUPLICATE_CLIENT_MESSAGE_ID:{code:107306,message:"duplicate client message ID"},V2NIM_ERROR_CODE_INVALID_TIME_RANGE:{code:107439,message:"invalid time range"},V2NIM_ERROR_CODE_NOT_ADVANCED_TEAM:{code:108302,message:"not advanced team"},V2NIM_ERROR_CODE_TEAM_MANAGER_LIMIT:{code:108303,message:"team manager limit"},V2NIM_ERROR_CODE_JOINED_TEAM_LIMIT:{code:108305,message:"joined team limit"},V2NIM_ERROR_CODE_TEAM_NORMAL_MEMBER_CHAT_BANNED:{code:108306,message:"team normal member chat banned"},V2NIM_ERROR_CODE_INVITED_ACCOUNT_NOT_FRIEND:{code:108307,message:"invited account not friend"},V2NIM_ERROR_CODE_REJECT_ALL_TEAM_APPLICATIONS:{code:108308,message:"reject all team applications"},V2NIM_ERROR_CODE_TARGETING_MESSAGE_FOR_TEAM_DISABLED:{code:108318,message:"Targeting messages for group chat is disabled"},V2NIM_ERROR_CODE_INCLUSIVE_AS_FALSE_NOT_ALLOWED_FOR_SUPER_TEAM:{code:108319,message:'Setting "inclusive" to false for super teams is not allowed'},V2NIM_ERROR_CODE_CANNOT_MAKE_SUPER_TEAM_MESSAGE_VISIBLE_TO_NEW_MEMBERS:{code:108320,message:"Cannot make super team targeted messages visible to new members"},V2NIM_ERROR_CODE_CANNOT_ALLOW_TARGETED_MESSAGES_INCLUSIVE_TO_NEW_MEMBERS:{code:108321,message:"Cannot allow targeted messages inclusive to new members"},V2NIM_ERROR_CODE_TEAM_NOT_EXIST:{code:108404,message:"team not exist"},V2NIM_ERROR_CODE_TEAM_ALREADY_CHAT_BANNED:{code:108420,message:"team already chat banned"},V2NIM_ERROR_CODE_ALL_TEAM_MEMBER_CHAT_BANNED:{code:108423,message:"all team member chat banned"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT:{code:108434,message:"extended super team limit"},V2NIM_ERROR_CODE_CREATED_TEAM_LIMIT:{code:108435,message:"created team limit"},V2NIM_ERROR_CODE_TEAM_INVITATION_LIMIT:{code:108437,message:"team invitation limit"},V2NIM_ERROR_CODE_TEAM_HIT_ANTISPAM:{code:108451,message:"team hit antispam"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT_NOT_CONFIGURED:{code:108304,message:"extended super team limit not configured"},V2NIM_ERROR_CODE_SUPER_TEAM_SERVICE_DISABLED:{code:108311,message:"super team service disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_RECORD_NOT_FOUND:{code:108301,message:"read receipt record for the team message not found"},V2NIM_ERROR_CODE_NOT_MANAGER:{code:108430,message:"unable to assign owner manager"},V2NIM_ERROR_CODE_ONLINE_MEMBER_COUNT_DISABLED:{code:108406,message:"number of online users service disabled"},V2NIM_ERROR_CODE_TRANSFER_DISABLED:{code:108310,message:"unable to transfer the ownership to owner"},V2NIM_ERROR_CODE_CREATE_TEAM_DISABLED:{code:108309,message:"unable to create team with more than %s people"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_CREATE_FAILED:{code:108313,message:"/ extended super team creation failed，use open api to create the team"},V2NIM_ERROR_CODE_TEAM_MESSAGE_READ_RECEIPT_DISABLED:{code:108312,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_RETRY:{code:108449,message:"an error occurred, try again"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_NOT_TEAM_MEMBER:{code:109301,message:"list of chat banned users contains non team members"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_OPERATOR:{code:109303,message:"list of chat banned users contains the operator"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_TEAM_OWNER:{code:109304,message:"list of chat banned users contains the team owner"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_MANAGER_NOT_ALLOWED:{code:109305,message:"operation on team manager not allowed"},V2NIM_ERROR_CODE_NO_TEAM_INVITE_PERMISSION:{code:109306,message:"no team invite permission"},V2NIM_ERROR_CODE_TEAM_OWNER_QUIT_NOT_ALLOWED:{code:109307,message:"team owner quit not allowed"},V2NIM_ERROR_CODE_TEAM_OWNER_IN_KICK_LIST:{code:109308,message:"list of kicked user contains the team owner"},V2NIM_ERROR_CODE_INVITE_ROBOT_ACCOUNT_NOT_ALLOWED:{code:109309,message:"invite robot account not allowed"},V2NIM_ERROR_CODE_KICK_OPERATOR_NOT_ALLOWED:{code:109310,message:"kick operator not allowed"},V2NIM_ERROR_CODE_TEAM_MEMBER_ALREADY_EXIST:{code:109311,message:"team member already exist"},V2NIM_ERROR_CODE_TEAM_INVITATION_OR_APPLICATION_NOT_EXIST:{code:109313,message:"team invitation or application not exist"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_OWNER_NOT_ALLOWED:{code:109314,message:"operation on team owner not allowed"},V2NIM_ERROR_CODE_FORCED_PUSH_LIST_INCLUDES_NON_TARGETED_ACCOUNTS:{code:109318,message:"The forced push list includes non-targeted accounts"},V2NIM_ERROR_CODE_TEAM_MEMBER_NOT_EXIST:{code:109404,message:"team member not exist"},V2NIM_ERROR_CODE_TEAM_MEMBER_CHAT_BANNED:{code:109424,message:"team member chat banned"},V2NIM_ERROR_CODE_TEAM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:109427,message:"team owner operation permission required"},V2NIM_ERROR_CODE_TEAM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:109432,message:"team owner or manager operation permission required"},V2NIM_ERROR_CODE_TEAM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:109449,message:"team member concurrent operation failed"},V2NIM_ERROR_CODE_TEAM_MEMBER_HIT_ANTISPAM:{code:109451,message:"team member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_AND_ACCOUNT_MISMATCH:{code:110302,message:"conversation and account mismatch"},V2NIM_ERROR_CODE_CONVERSATION_STICK_TOP_LIMIT:{code:110303,message:"conversation stick top limit"},V2NIM_ERROR_CODE_CONVERSATION_BELONGED_GROUP_LIMIT:{code:110304,message:"conversation belonged group limit"},V2NIM_ERROR_CODE_CONVERSATION_IS_NOT_STICK_TOP:{code:110305,message:"conversation is not stick top"},V2NIM_ERROR_CODE_STICK_TOP_DISABLED:{code:110306,message:"conversation stick top disabled"},V2NIM_ERROR_CODE_CONVERSATION_NOT_EXIST:{code:110404,message:"conversation not exist"},V2NIM_ERROR_CODE_CHATROOM_LINK_UNAVAILABLE:{code:113304,message:"chatroom link unavailable"},V2NIM_ERROR_CODE_IM_CONNECTION_ABNORMAL:{code:113305,message:"IM connection abnormal"},V2NIM_ERROR_CODE_CHATROOM_NOT_EXIST:{code:113404,message:"chatroom not exist"},V2NIM_ERROR_CODE_CHATROOM_CLOSED:{code:113406,message:"chatroom closed"},V2NIM_ERROR_CODE_CHATROOM_REPEATED_OPERATION:{code:113409,message:"chatroom repeated operation"},V2NIM_ERROR_CODE_CHATROOM_DISABLED:{code:113410,message:"chatroom disabled"},V2NIM_ERROR_CODE_ALL_CHATROOM_MEMBER_CHAT_BANNED:{code:113423,message:"all chatroom member chat banned"},V2NIM_ERROR_CODE_CHATROOM_HIT_ANTISPAM:{code:113451,message:"chatroom hit antispam"},V2NIM_ERROR_CODE_ANONYMOUS_MEMBER_FORBIDDEN:{code:114303,message:"anonymous member forbidden"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_NOT_EXIST:{code:114404,message:"chatroom member not exist"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_REPEATED_OPERATION:{code:114405,message:"chatroom member repeated operation"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CHAT_BANNED:{code:114421,message:"chatroom member chat banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_CHATROOM_BLOCK_LIST:{code:114426,message:"account in chatroom block list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:114427,message:"chatroom owner operation permission required"},V2NIM_ERROR_CODE_SELF_IN_CHATROOM_MEMBER_OPERATION_LIST:{code:114429,message:"self in chatroom member operation list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:114432,message:"chatroom owner or manager operation permission required"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_LIMIT:{code:114437,message:"chatroom member limit"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:114449,message:"chatroom member concurrent operation failed"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_HIT_ANTISPAM:{code:114451,message:"chatroom member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_NOT_EXIST:{code:116404,message:"conversation group not exist"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_LIMIT:{code:116435,message:"conversation group limit"},V2NIM_ERROR_CODE_CONVERSATIONS_IN_GROUP_LIMIT:{code:116437,message:"conversations in group limit"},V2NIM_ERROR_CODE_COLLECTION_LIMIT:{code:189301,message:"collection limit"},V2NIM_ERROR_CODE_COLLECTION_NOT_EXIST:{code:189302,message:"collection not exist"},V2NIM_ERROR_CODE_COLLECTION_CONCURRENT_OPERATION_FAILED:{code:189449,message:"collection concurrent operation failed"},V2NIM_ERROR_CODE_INTERNAL:{code:190001,message:"internal error"},V2NIM_ERROR_CODE_ILLEGAL_STATE:{code:190002,message:"illegal state"},V2NIM_ERROR_CODE_MISUSE:{code:191001,message:"misuse"},V2NIM_ERROR_CODE_CANCELLED:{code:191002,message:"operation cancelled"},V2NIM_ERROR_CODE_CALLBACK_FAILED:{code:191003,message:"callback failed"},V2NIM_ERROR_CODE_INVALID_PARAMETER:{code:191004,message:"invalid parameter"},V2NIM_ERROR_CODE_TIMEOUT:{code:191005,message:"timeout"},V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST:{code:191006,message:"resource not exist"},V2NIM_ERROR_CODE_RESOURCE_ALREADY_EXIST:{code:191007,message:"resource already exist"},V2NIM_ERROR_CODE_CONNECT_FAILED:{code:192001,message:"connect failed"},V2NIM_ERROR_CODE_CONNECT_TIMEOUT:{code:192002,message:"connect timeout"},V2NIM_ERROR_CODE_DISCONNECT:{code:192003,message:"disconnect"},V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT:{code:192004,message:"protocol timeout"},V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED:{code:192005,message:"protocol send failed"},V2NIM_ERROR_CODE_REQUEST_FAILED:{code:192006,message:"request failed"},V2NIM_ERROR_CODE_FILE_NOT_FOUND:{code:194001,message:"file not found"},V2NIM_ERROR_CODE_FILE_CREATE_FAILED:{code:194002,message:"file create failed"},V2NIM_ERROR_CODE_FILE_OPEN_FAILED:{code:194003,message:"file open failed"},V2NIM_ERROR_CODE_FILE_WRITE_FAILED:{code:194004,message:"file write failed"},V2NIM_ERROR_CODE_FILE_READ_FAILED:{code:194005,message:"file read failed"},V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:{code:194006,message:"file upload failed"},V2NIM_ERROR_CODE_FILE_DOWNLOAD_FAILED:{code:194007,message:"file download failed"},V2NIM_ERROR_CODE_CLIENT_ANTISPAM:{code:195001,message:"client anti-spam"},V2NIM_ERROR_CODE_SERVER_ANTISPAM:{code:195002,message:"server anti-spam"},V2NIM_ERROR_CODE_STREAM_OUTPUT_STOPPED:{code:189318,message:"Streaming text response stopped"},V2NIM_ERROR_CODE_STREAM_OUTPUT_GENERATED:{code:189319,message:"Streaming text response generated"},V2NIM_ERROR_CODE_STREAM_OUTPUT_ABORTED:{code:189320,message:"Streaming text response aborted due to exception"},V2NIM_ERROR_CODE_INTERRUPTION_REJECTED:{code:189321,message:"Non-streaming messages cannot be interrupted"}},xl=Qi(Ol),kl=reduce(xl).call(xl,(function(t,a){var u=Ol[a];return t[a]=u.code,t}),{}),Pl=reduce(xl).call(xl,(function(t,a){var u=Ol[a];return t[u.code]=u.message,t}),{}),Ll=function(t){function V2NIMErrorImpl(a){var u;return(u=t.call(this,a.desc)||this).name="V2NIMError",u.code=a.code||0,u.desc=a.desc||Pl[u.code]||Fl[u.code]||"",u.message=u.desc,u.detail=a.detail||{},u}return Tt(V2NIMErrorImpl,t),V2NIMErrorImpl.prototype.toString=function toString(){var t,a=this.name+"\n code: "+this.code+'\n message: "'+this.message+'"\n detail: '+(this.detail?gs(this.detail):"");return(null===(t=null==this?void 0:this.detail)||void 0===t?void 0:t.rawError)&&(a+="\n rawError: "+this.detail.rawError.message),a},V2NIMErrorImpl}(bc(Error));var ql=function(t){function ValidateError(a,u,h){var m;return void 0===u&&(u={}),(m=t.call(this,{code:kl.V2NIM_ERROR_CODE_PARAMETER_ERROR,detail:{reason:a,rules:h,data:u}})||this).name="validateError",m.message=m.message+"\n"+gs(m.detail,null,2),m.data=u,m.rules=h,m}return Tt(ValidateError,t),ValidateError}(Ll),Dl=function(t){function ValidateErrorV2(a){var u,h,m,g;return(u=t.call(this,{code:kl.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:null===(h=a.detail)||void 0===h?void 0:h.reason,rules:null===(m=a.detail)||void 0===m?void 0:m.rules,data:null===(g=a.detail)||void 0===g?void 0:g.data}})||this).name="ValidateErrorV2",u}return Tt(ValidateErrorV2,t),ValidateErrorV2}(Ll),Vl=function(t){function FormatError(a,u,h){var m;return(m=t.call(this,{code:kl.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:a,key:u,rules:h}})||this).name="formatError",m}return Tt(FormatError,t),FormatError}(Ll),Ul=function(t){function UploadError(a){var u;return(u=t.call(this,Ot({code:400},a))||this).desc=u.desc||"upload file error",u.message=u.desc,u.name="uploadError",u}return Tt(UploadError,t),UploadError}(Ll),Gl=function(t){function CustomError(a,u,h){var m;return void 0===u&&(u={}),void 0===h&&(h=400),(m=t.call(this,{code:h,desc:a,detail:u})||this).name="customError",m.data=u,m}return Tt(CustomError,t),CustomError}(Ll),Fl={200:null,406:null,808:null,810:null,302:"The user name or password is incorrect.",405:"Parameter length too long",408:"Client request timed out",415:"Client network unavailable",422:"Account disabled",508:"Expiration date",509:"Invalid",7101:"Be pulled black",700:"Partial failure of batch operation",801:"The number of people in the team has reached the upper limit",802:"No permission",803:"The team does not exist or has not changed",804:"The user is not in the team",805:"Team type mismatch",806:"The number of teams created has reached the limit",807:"Team member not valid",809:"Already in the team",811:"The number of accounts in the forced push list exceeds the limit",812:"The team is muted",813:"Due to the limited number of team, some pull people successfully",814:"Disable team message read service",815:"Maximum number of team administrators",816:"Batch operation partial failure",9102:"Channel failure",9103:"This call has been answered / rejected at another end",10201:"Signaling: target NIM client is offline",10202:"Signaling: push is unreachable",10404:"Signaling: channel not exists",10405:"Signaling: channel already exists",10406:"Signaling: member of channel not exists",10407:"Signaling: member of channel already exists",10408:"Signaling: the invitation request does not exist or has expired",10409:"Signaling: the invitation request has been rejected",10410:"Signaling: the invitation request has been accepted",10414:"Signaling: request parameter error",10417:"Signaling: uid conflict",10419:"Signaling: the number of members of channel exceed the limit",10420:"Signaling: member is already in the channel on other client",10700:"Signaling: phased success",13002:"Abnormal chatroom status",13003:"In the blacklist",13004:"In the mute list",13006:"All members are muted, and only the administrator can speak"};var Bl=Ps.trim,jl=m.parseInt,Wl=m.Symbol,Yl=Wl&&Wl.iterator,$l=/^[+-]?0x/i,Hl=A($l.exec),Ql=8!==jl(Ns+"08")||22!==jl(Ns+"0x16")||Yl&&!fails((function(){jl(Object(Yl))}))?function parseInt(t,a){var u=Bl(toString(t));return jl(u,a>>>0||(Hl($l,u)?16:10))}:jl;_export({global:!0,forced:parseInt!=Ql},{parseInt:Ql});var Kl=W.parseInt;_export({target:"Array",stat:!0},{isArray:eo});var zl=W.Array.isArray;function get(t,a){if("object"!=typeof t||null===t)return t;for(var u=(a=a||"").split("."),h=0;h<u.length;h++){var m=u[h],g=t[m],E=indexOf(m).call(m,"["),I=indexOf(m).call(m,"]");if(-1!==E&&-1!==I&&E<I){var M=slice(m).call(m,0,E),S=Kl(slice(m).call(m,E+1,I));g=t[M],g=zl(g)?g[S]:void 0}if(null==g)return g;t=g}return t}var Jl,Xl=(Jl=function _s4(){return(65536*(1+Math.random())|0).toString(16).substring(1)},function(){return Jl()+Jl()+Jl()+Jl()+Jl()+Jl()+Jl()+Jl()});function getEnumKeys(t){var a;return filter(a=Qi(t)).call(a,(function(t){return!(+t>=0)}))}function getEnumKeyByEnumValue(t,a){var u,h=filter(u=Qi(t)).call(u,(function(u){return t[u]==a}));return h.length>0?h[0]:void 0}function assignOptions(t,a){return function assignWith(t,a,u,h){for(var m in t=t||{},u=u||{},h=h||function(){},a=a||{}){var g=h(t[m],a[m]);t[m]=void 0===g?a[m]:g}for(var E in u){var I=h(t[E],u[E]);t[E]=void 0===I?u[E]:I}return t}({},t,a,(function(t,a){return void 0===a?t:a}))}function emptyFuncWithPromise(){return za.resolve()}var Zl,eu,tu={file:{md5:"$(Etag)",size:"$(ObjectSize)"},image:{md5:"$(Etag)",size:"$(ObjectSize)",w:"$(ImageInfo.Width)",h:"$(ImageInfo.Height)",orientation:"$(ImageInfo.Orientation)"},audio:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Audio.Duration)"},video:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Video.Duration)",w:"$(AVinfo.Video.Width)",h:"$(AVinfo.Video.Height)"}},ru={accessKeyId:"",secretAccessKey:"",sessionToken:"",region:"",maxRetries:0,bucket:"",objectName:"",token:"",shortUrl:""};function getUploadResponseFormat(t){return void 0===t&&(t="file"),gs(tu[t]||{}).replace(/"/gi,'\\"')}!function(t){t[t.nos=1]="nos",t[t.s3=2]="s3"}(Zl||(Zl={})),function(t){t[t.dontNeed=-1]="dontNeed",t[t.time=2]="time",t[t.urls=3]="urls"}(eu||(eu={}));var nu={chunkUploadHost:"https://wannos-web.127.net",chunkUploadHostBackupList:["https://fileup.chatnos.com","https://oss.chatnos.com"],commonUploadHost:"https://fileup.chatnos.com",commonUploadHostBackupList:["https://oss.chatnos.com"],chunkMaxSize:4194304e4,commonMaxSize:104857600,uploadReplaceFormat:"https://{host}/{object}",cdn:{defaultCdnDomain:"nim-nosdn.netease.im",cdnDomain:"",bucket:"",objectNamePrefix:""},downloadUrl:"https://{bucket}-nosdn.netease.im/{object}",downloadHostList:["nos.netease.com"],nosCdnEnable:!0,isNeedToGetUploadPolicyFromServer:!0};function isPlainObject(t){return null!=t&&"object"==typeof t&&Ei(t)==Object.prototype}function merge$1(t,a){var u=isPlainObject(t)||zl(t),h=isPlainObject(a)||zl(a);if(u&&h){for(var m in a){var g=merge$1(t[m],a[m]);void 0!==g&&(t[m]=g)}return t}return a}var au={setLogger:function setLogger(t){throw new Error("setLogger not implemented.")},platform:"",WebSocket:function(){function AdapterSocket(t,a){throw this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this.binaryType="",new Error("Method not implemented.")}var t=AdapterSocket.prototype;return t.close=function close(t,a){throw new Error("Method not implemented.")},t.send=function send(t){throw new Error("Method not implemented.")},t.onclose=function onclose(t){throw new Error("Method not implemented.")},t.onerror=function onerror(t){throw new Error("Method not implemented.")},t.onmessage=function onmessage(t){throw new Error("Method not implemented.")},t.onopen=function onopen(t){throw new Error("Method not implemented.")},AdapterSocket}(),localStorage:{},request:function request(t,a){throw new Error("request not implemented.")},uploadFile:function uploadFile(t){throw new Error("uploadFile not implemented.")},getSystemInfo:function getSystemInfo(){throw new Error("getSystemInfo not implemented.")},getFileUploadInformation:function getFileUploadInformation(t){throw new Error("getFileUploadInformation not implemented.")},envPayload:{},net:{getNetworkStatus:function getNetworkStatus(){return za.resolve({net_type:0,net_connect:!0})},onNetworkStatusChange:function onNetworkStatusChange(t){},offNetworkStatusChange:function offNetworkStatusChange(){}},logStorage:function(){function AdapterLogStorageImpl(t){}var t=AdapterLogStorageImpl.prototype;return t.open=function open(){return za.resolve()},t.close=function close(){},t.addLogs=function addLogs(t){return za.resolve()},t.extractLogs=function extractLogs(){return za.resolve()},AdapterLogStorageImpl}()};function pickBy(t,a){t=t||{},a=a||function(){return!0};var u={};for(var h in t)a(t[h])&&(u[h]=t[h]);return u}var ou=function(){function NOS(t,a){this.nosCdnHostTimer=0,this.nosErrorCount=0,this.core=t,this.cloudStorage=a}var t=NOS.prototype;return t.reset=function reset(){this.nosErrorCount=0},t.getNosAccessToken=function getNosAccessToken(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a,u,h;return cs.wrap((function _callee$(m){for(;;)switch(m.prev=m.next){case 0:return m.next=2,this.core.sendCmd("getNosAccessToken",{tag:t});case 2:return a=m.sent,u=get(a,"content.nosAccessTokenTag.token"),h=t.url,m.abrupt("return",{token:u,url:-1!==indexOf(h).call(h,"?")?h+"&token="+u:h+"?token="+u});case 6:case"end":return m.stop()}}),_callee,this)})))},t.deleteNosAccessToken=function deleteNosAccessToken(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){return cs.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("deleteNosAccessToken",{tag:t});case 2:case"end":return a.stop()}}),_callee2,this)})))},t.nosUpload=function nosUpload(t,a){var u,h,m,g,E,I,M,S;return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var C,T,b,R,A,N,w,O,x,k,P,L,q,D,V,U,G,B,j;return cs.wrap((function _callee3$(W){for(;;)switch(W.prev=W.next){case 0:if(C=get(this.core,"config.cdn.bucket"),T={tag:t.nosScenes||C||"nim"},t.nosSurvivalTime&&(T.expireSec=t.nosSurvivalTime),b=this.core.adapters.getFileUploadInformation(t),!(!a&&!b)){W.next=18;break}return W.prev=6,W.next=9,this.core.sendCmd("getNosToken",{responseBody:getUploadResponseFormat(t.type),nosToken:T});case 9:R=W.sent,W.next=18;break;case 12:if(W.prev=12,W.t0=W.catch(6),this.core.logger.error("uploadFile:: getNosToken error",W.t0),!(W.t0 instanceof Ll)){W.next=17;break}throw W.t0;case 17:throw new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getNosToken error",rawError:W.t0,curProvider:1}});case 18:return A=this.config.uploadReplaceFormat.replace("{host}",this.config.cdn.cdnDomain||this.config.cdn.defaultCdnDomain).replace("{object}",b?null===(u=b.uploadInfo)||void 0===u?void 0:u.objectName:a?null==a?void 0:a.objectName:R.content.nosToken.objectName),N="",a&&a.shortUrl&&(N=a.shortUrl),(null===(g=null===(m=null===(h=null==b?void 0:b.uploadInfo)||void 0===h?void 0:h.payload)||void 0===m?void 0:m.mixStoreToken)||void 0===g?void 0:g.shortUrl)&&(N=b.uploadInfo.payload.mixStoreToken.shortUrl),w=N||A,W.prev=23,x=b?{token:null===(E=null==b?void 0:b.uploadInfo)||void 0===E?void 0:E.token,bucket:null===(I=null==b?void 0:b.uploadInfo)||void 0===I?void 0:I.bucketName,objectName:null===(M=null==b?void 0:b.uploadInfo)||void 0===M?void 0:M.objectName}:a||R.content.nosToken,this.core.logger.log("uploadFile:: uploadFile params",{nosToken:x,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,platform:au.platform}),k="BROWSER"===au.platform?this.config.chunkUploadHost:this.config.commonUploadHost+"/"+(x&&x.bucket),this.core.reporterHookCloudStorage.update({remote_addr:k,operation_type:a?2:0}),W.next=30,this.core.adapters.uploadFile(Ot(Ot(Ot({},t),{nosToken:x,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,maxSize:t.maxSize||this.config.chunkMaxSize}),a?{payload:{mixStoreToken:a}}:{}));case 30:O=W.sent,W.next=65;break;case 33:if(W.prev=33,W.t1=W.catch(23),this.core.logger.error("uploadFile::nos uploadFile error:",W.t1),P="v2"===get(this.core,"options.apiVersion"),W.t1.code!==kl.V2NIM_ERROR_CODE_CANCELLED&&10499!==W.t1.errCode){W.next=39;break}throw new Ul({code:P?kl.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:get(W.t1,"message")||"Request abort",rawError:W.t1,curProvider:1}});case 39:if(!P||W.t1.errCode!==kl.V2NIM_ERROR_CODE_FILE_OPEN_FAILED){W.next=41;break}throw new Ll({code:kl.V2NIM_ERROR_CODE_FILE_OPEN_FAILED,detail:{reason:get(W.t1,"message")||"Read file failed",rawError:W.t1,curProvider:1}});case 41:return W.next=43,au.net.getNetworkStatus();case 43:if(L=W.sent,!1!==L.net_connect){W.next=47;break}throw new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:W.t1,curProvider:1}});case 47:if(!a){W.next=64;break}if(!(this.nosErrorCount<=0)){W.next=60;break}W.prev=49,this.cloudStorage.mixStorage._addCircuitTimer(),W.next=56;break;case 53:throw W.prev=53,W.t2=W.catch(49),new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:W.t2,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.cloudStorage.mixStorage.mixStorePolicy,file:t.file||t.filePath}});case 56:return this.nosErrorCount=get(this.cloudStorage,"mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry"),W.abrupt("return",this.cloudStorage._uploadFile(t));case 60:return this.nosErrorCount--,W.abrupt("return",this.nosUpload(t,a));case 62:W.next=65;break;case 64:throw new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"NOS attempts failed",rawError:W.t1,curProvider:1}});case 65:if(q=null==O?void 0:O.type,(D=q&&indexOf(q).call(q,"/")>-1?slice(q).call(q,0,indexOf(q).call(q,"/")):"")||(D=t.type||""),(V={image:"imageInfo",video:"vinfo",audio:"vinfo"})[D]){W.next=71;break}return W.abrupt("return",Ot({url:w},O));case 71:return W.prev=71,W.next=74,this.core.adapters.request(A+"?"+V[D],{method:"GET",dataType:"json",timeout:5e3},{exception_service:3});case 74:U=W.sent,W.next=81;break;case 77:return W.prev=77,W.t3=W.catch(71),this.core.logger.error("uploadFile:: fetch file info error",W.t3),W.abrupt("return",Ot({url:w},O));case 81:if(!U){W.next=88;break}return G=U.data,B="imageInfo"===V[D]?G:null===(S=null==G?void 0:G.GetVideoInfo)||void 0===S?void 0:S.VideoInfo,j={url:w,name:O.name,size:O.size,ext:O.ext,w:null==B?void 0:B.Width,h:null==B?void 0:B.Height,orientation:null==B?void 0:B.Orientation,dur:null==B?void 0:B.Duration,audioCodec:null==B?void 0:B.AudioCodec,videoCodec:null==B?void 0:B.VideoCodec,container:null==B?void 0:B.Container},W.abrupt("return",pickBy(j,(function(t){return void 0!==t})));case 88:return W.abrupt("return",Ot({url:w},O));case 89:case"end":return W.stop()}}),_callee3,this,[[6,12],[23,33],[49,53],[71,77]])})))},t._getNosCdnHost=function _getNosCdnHost(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a,u,h,m=this;return cs.wrap((function _callee4$(g){for(;;)switch(g.prev=g.next){case 0:return g.prev=0,g.next=3,this.core.sendCmd("getNosCdnHost");case 3:a=g.sent,g.next=10;break;case 6:return g.prev=6,g.t0=g.catch(0),this.core.logger.error("getNosCdnHost::error",g.t0),g.abrupt("return");case 10:if(a){g.next=12;break}return g.abrupt("return");case 12:u=null===(t=null==a?void 0:a.content)||void 0===t?void 0:t.nosConfigTag,0!==(h=Kl(null==u?void 0:u.expire))&&u.cdnDomain?-1===h?(this.config.cdn.bucket=u.bucket,this.config.cdn.cdnDomain=u.cdnDomain,this.config.cdn.objectNamePrefix=u.objectNamePrefix):(this.config.cdn.bucket=u.bucket,this.config.cdn.cdnDomain=u.cdnDomain,this.config.cdn.objectNamePrefix=u.objectNamePrefix,this.nosCdnHostTimer=this.core.timerManager.addTimer((function(){m._getNosCdnHost()}),1e3*h)):(this.config.cdn.bucket="",this.config.cdn.cdnDomain="",this.config.cdn.objectNamePrefix="");case 15:case"end":return g.stop()}}),_callee4,this,[[0,6]])})))},as(NOS,[{key:"config",get:function get(){return this.cloudStorage.config}}]),NOS}(),iu=entryVirtual("Array").values,su=Array.prototype,cu={DOMTokenList:!0,NodeList:!0},values=function(t){var a=t.values;return t===su||Y(su,t)&&a===su.values||pe(cu,er(t))?iu:a},lu=bo.every,uu=arrayMethodIsStrict("every");_export({target:"Array",proto:!0,forced:!uu},{every:function every(t){return lu(this,t,arguments.length>1?arguments[1]:void 0)}});var du=entryVirtual("Array").every,pu=Array.prototype,every=function(t){var a=t.every;return t===pu||Y(pu,t)&&a===pu.every?du:a};function difference(t,a){return a=a||[],filter(t=t||[]).call(t,(function(t){return-1===indexOf(a).call(a,t)}))}function replacer(t,a){return a instanceof RegExp?"__REGEXP "+a.toString():a}function validate(t,a,u,h){var m;void 0===a&&(a={}),void 0===h&&(h=!1);var g={};return forEach$1(m=Qi(t)).call(m,(function(m){var E=t[m].type,I=u?"In "+u+", ":"";if(null==a){var M=I+"param is null or undefined";throw h?new Dl({detail:{reason:M,data:{key:m},rules:"required"}}):new ql(M,{key:m},"required")}if(void 0===a[m]){if(!1===t[m].required)return void(g[m]=a[m]);var S=I+"param '"+m+"' is required";throw h?new Dl({detail:{reason:S,data:{key:m},rules:"required"}}):new ql(S,{key:m},"required")}var C=hu[E];if(C&&!C(a,m,t[m],h)){var T=I+"param '"+m+"' unexpected",b={key:m,value:a[m]};throw h?new Dl({detail:{reason:T,data:b,rules:gs(t[m],replacer)}}):new ql(T,b,gs(t[m],replacer))}g[m]=a[m]})),g}var hu={string:function string(t,a,u){var h=u.allowEmpty,m=u.max,g=u.min,E=u.regExp,I=t[a];return"string"==typeof I&&((!1!==h||""!==I)&&(!("number"==typeof m&&I.length>m)&&(!("number"==typeof g&&I.length<g)&&!(function isRegExp(t){return"[object RegExp]"===Object.prototype.toString.call(t)}(E)&&!E.test(I)))))},number:function number(t,a,u){var h=u.min,m=u.max,g=t[a];return"number"==typeof g&&(!("number"==typeof h&&g<h)&&!("number"==typeof m&&g>m))},boolean:function boolean(t,a){return"boolean"==typeof t[a]},file:function file(t,a){return!0},enum:function _enum(t,a,u){var h=values(u),m=t[a];return!h||indexOf(h).call(h,m)>-1},jsonstr:function jsonstr(t,a){try{var u=JSON.parse(t[a]);return"object"==typeof u&&null!==u}catch(t){return!1}},func:function func(t,a){return"function"==typeof t[a]},array:function array(t,a,u,h){void 0===h&&(h=!1);var m=u.itemType,g=u.itemRules,E=u.rules,I=u.min,M=u.max,S=values(u),C=t[a];if(!zl(C))return!1;if("number"==typeof M&&C.length>M)return!1;if("number"==typeof I&&C.length<I)return!1;if(g)forEach$1(C).call(C,(function(t,u){var m,E;validate(((m={})[u]=g,m),((E={})[u]=t,E),a+"["+u+"]",h)}));else if(E)forEach$1(C).call(C,(function(t,u){return validate(E,t,a+"["+u+"]",h)}));else if("enum"===m){if(S&&difference(C,S).length)return!1}else if(m&&!every(C).call(C,(function(t){return typeof t===m})))return!1;return!0},object:function object(t,a,u,h){void 0===h&&(h=!1);var m=u.rules,g=u.allowEmpty,E=t[a];if("object"!=typeof E||null===E)return!1;if(m){var I=Qi(m),M=Qi(E),S=filter(M).call(M,(function(t){return indexOf(I).call(I,t)>-1}));if(!1===g&&0===S.length)return!1;validate(m,E,a,h)}return!0}},callWithSafeIterationClosing=function(t,a,u,h){try{return h?a(anObject(u)[0],u[1]):a(u)}catch(a){iteratorClose(t,"throw",a)}},mu=Array,fu=!checkCorrectnessOfIteration((function(t){Array.from(t)}));_export({target:"Array",stat:!0,forced:fu},{from:function from(t){var a=toObject(t),u=mn(this),h=arguments.length,m=h>1?arguments[1]:void 0,g=void 0!==m;g&&(m=functionBindContext(m,h>2?arguments[2]:void 0));var E,I,M,S,C,T,b=getIteratorMethod$5(a),R=0;if(!b||this===mu&&isArrayIteratorMethod(b))for(E=lengthOfArrayLike(a),I=u?new this(E):mu(E);E>R;R++)T=g?m(a[R],R):a[R],createProperty(I,R,T);else for(C=(S=getIterator(a,b)).next,I=u?new this:[];!(M=O(C,S)).done;R++)T=g?callWithSafeIterationClosing(S,m,[M.value,R],!0):M.value,createProperty(I,R,T);return I.length=R,I}});var gu=W.Array.from,vu=getIteratorMethod$5;function getPromiseWithAbort(t){var a={},u=new za((function(t,u){a.abort=u}));return a.promise=za.race([t,u]),a}za.reject;var _u=function(){function PromiseManager(){this.abortFns=[]}var t=PromiseManager.prototype;return t.add=function add(t){var a=getPromiseWithAbort(t);return this.abortFns.push(a.abort),a.promise},t.clear=function clear(t){var a;forEach$1(a=this.abortFns).call(a,(function(a){return a(t||new Ll({code:kl.V2NIM_ERROR_CODE_CANCELLED,detail:{reason:"Aborted"}}))})),this.abortFns=[]},t.destroy=function destroy(){this.clear()},PromiseManager}(),yu={tolerantRTT:3e3,bestRTT:100,maxChances:5,enable:!0},Eu={timestamp:0,rtt:0,baseClock:0,baseTime:0},Iu=function(){function TimeOrigin(t,a,u){void 0===u&&(u="getServerTime"),this.serverOrigin=Eu,this.config=yu,this.isSettingNTP=!1,this.currentChance=0,this.failedDelay=2e3,this.successDelay=3e5,this.timer=0,this.cmdName="getServerTime",this.core=t,this.logger=t.logger,this.promiseManager=new _u,this.cmdName=u,a&&this.setOptions(a)}var t=TimeOrigin.prototype;return t.setOptions=function setOptions(t){this.config=Ot({},yu,this.config,t)},t.reset=function reset(){this.timer&&clearTimeout(this.timer),this.promiseManager.clear(),this.serverOrigin=Eu,this.currentChance=0},t.setOriginTimetick=function setOriginTimetick(){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var t,a,u,h,m,g,E,I,M,S;return cs.wrap((function _callee$(C){for(;;)switch(C.prev=C.next){case 0:if(this.config.enable){C.next=2;break}return C.abrupt("return");case 2:if(!this.isSettingNTP){C.next=4;break}return C.abrupt("return");case 4:if(!(this.currentChance>=this.config.maxChances)){C.next=6;break}return C.abrupt("return");case 6:if(t=get(this.core,"auth.status"),a=get(this.core,"status"),u=get(this.core,"V2NIMLoginService.lifeCycle.loginStatus"),"logined"===t||"logined"===a||1===u){C.next=11;break}return C.abrupt("return");case 11:return this.isSettingNTP=!0,this.currentChance++,this.timer&&clearTimeout(this.timer),this.timer=0,h="TimeOrigin::setOriginTimetick:",m=Za(),this.core.logger.debug(h+" getServerTime start, times "+this.currentChance),C.prev=18,C.next=21,this.promiseManager.add(this.core.sendCmd(this.cmdName));case 21:E=C.sent,g=get(E,"content.time"),this.isSettingNTP=!1,C.next=33;break;case 26:return C.prev=26,C.t0=C.catch(18),I=C.t0,this.isSettingNTP=!1,this.logger.warn(h+" Calculate Delay time, getServerTime error",I),I.code!==kl.V2NIM_ERROR_CODE_CANCELLED&&(clearTimeout(this.timer),this.timer=go(bind$1(M=this.setOriginTimetick).call(M,this),this.failedDelay)),C.abrupt("return");case 33:if(g){C.next=37;break}return this.core.logger.warn(h+" Calculate Delay time incorrect format"),this.config.enable=!1,C.abrupt("return");case 37:S=Za()-m,this.doSet(g,S);case 39:case"end":return C.stop()}}),_callee,this,[[18,26]])})))},t.doSet=function doSet(t,a){var u,h="TimeOrigin::setOriginTimetick:";if(a>this.config.tolerantRTT)this.logger.warn(h+" denied RTT:"+a),clearTimeout(this.timer),this.timer=go(bind$1(u=this.setOriginTimetick).call(u,this),this.failedDelay);else if(a>this.config.bestRTT){var m;this.serverOrigin.rtt&&a>=this.serverOrigin.rtt?this.logger.warn(h+" ignore RTT:"+a):(this.setServerOrigin(a,t),this.logger.log(h+" accept reluctantly RTT:"+a)),clearTimeout(this.timer),this.timer=go(bind$1(m=this.setOriginTimetick).call(m,this),this.failedDelay)}else{var g;this.setServerOrigin(a,t),this.logger.debug(h+" accept best RTT:"+a),this.currentChance=0,clearTimeout(this.timer),this.timer=go(bind$1(g=this.setOriginTimetick).call(g,this),this.successDelay)}},t.getNTPTime=function getNTPTime(t){if(void 0===t&&(t=this.getTimeNode()),this.checkNodeReliable(t)){var a=Math.floor(t.time-this.serverOrigin.baseTime);return this.serverOrigin.timestamp+a}return Za()},t.checkNodeReliable=function checkNodeReliable(t){if(void 0===t&&(t=this.getTimeNode()),this.serverOrigin.timestamp){if(0===this.serverOrigin.baseClock)return!0;var a=t.clock-this.serverOrigin.baseClock,u=t.time-this.serverOrigin.baseTime;return Math.abs(u-a)<500}return!1},t.checkPerformance=function checkPerformance(){return"BROWSER"===au.platform&&!("undefined"==typeof performance||!performance.now)},TimeOrigin.checkPerformance=function checkPerformance(){return"BROWSER"===au.platform&&!("undefined"==typeof performance||!performance.now)},t.getTimeNode=function getTimeNode(){return{clock:this.checkPerformance()?performance.now():0,time:Za()}},TimeOrigin.getTimeNode=function getTimeNode(){return{clock:TimeOrigin.checkPerformance()?performance.now():0,time:Za()}},t.setServerOrigin=function setServerOrigin(t,a){this.serverOrigin={timestamp:a+Math.floor(t/2),rtt:t,baseClock:this.checkPerformance()?performance.now():0,baseTime:Za()}},TimeOrigin}();function _createForOfIteratorHelperLoose$4(t,a){var u,h=void 0!==fi&&vu(t)||t["@@iterator"];if(h)return bind$1(u=(h=h.call(t)).next).call(u,h);if(zl(t)||(h=function _unsupportedIterableToArray$4(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$4(t,a);var h=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===h&&t.constructor&&(h=t.constructor.name),"Map"===h||"Set"===h?gu(t):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray$4(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){h&&(t=h);var m=0;return function(){return m>=t.length?{done:!0}:{done:!1,value:t[m++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$4(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,h=Array(a);u<a;u++)h[u]=t[u];return h}var Mu={},Su={};function parseEachCmd(t,a,u,h,m){var g,E={cmd:u,raw:t,error:null,service:null==a?void 0:a.service,content:{},__receiveTimeNode:Iu.getTimeNode()};if(!u||!a)return E.notFound=!0,E;(18===a.sid||a.sid>=26&&a.sid<100)&&(t.code=function toReadableCode(t){if("number"!=typeof t||t!=t)throw new Ll({code:kl.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"Read code failed",rawData:""+t}});if(t<0||t>=0&&t<1e3||t>=2e4&&t<=20099)return t;var a=(65535&t)>>9;a-=a<=38?1:2;return 1e5+1e3*a+(511&t)}(t.code));var I,M=function genCmdError(t,a){var u=Pl[t],h=Fl[t];return null===h?null:new Ll({code:t,desc:u||h||t,detail:{cmd:a,timetag:Za()}})}(t.code,u);if(E.error=M,E.error){if(E.error.detail.cmd=u,!(null===(g=null==a?void 0:a.ignoreErrCodes)||void 0===g?void 0:includes(g).call(g,t.code)))return E;m.warn("parseCmd:: ignore error ",E.error),E.error.detail.ignore=!0}a.response&&forEach$1(I=a.response).call(I,(function(t,a){var u=h[a],m=t.type,g=t.name,I=t.reflectMapper;if(void 0!==u)switch(m){case"Property":E.content[g]=I?deserialize(u,I):u;break;case"PropertyArray":E.content[g]=map$6(u).call(u,(function(t){return I?deserialize(t,I):t}));break;case"Int":case"Long":case"Byte":E.content[g]=+u;break;case"Bool":E.content[g]="true"===u||!0===u||1===u;break;default:E.content[g]=u}}));return E}function serialize(t,a,u){var h={};for(var m in t=function flattenObjByMapper(t,a){var u={};for(var h in a){for(var m,g=a[h],E="number"==typeof g?h:g.access?g.access:h,I=t,M=_createForOfIteratorHelperLoose$4(E.split("."));!(m=M()).done;){var S=m.value;if(void 0===I[S]||null===I[S]){I=void 0;break}I=I[S]}void 0!==I&&(u[E]=I)}return u}(t,a),a){var g=a[m],E="number"==typeof g?m:g.access?g.access:m;if(!u||includes(u).call(u,m))if(E in t){if("number"==typeof g)h[g]=t[E];else if("object"==typeof g)if(g.converter){var I=g.converter(t[E],t);void 0!==I&&(h[g.id]=I)}else h[g.id]=t[E]}else"object"==typeof g&&g.def&&("function"==typeof g.def?h[g.id]=g.def(t):h[g.id]=g.def)}return h}function deserialize(t,a){var u={};for(var h in t){var m=a[h];if("string"==typeof m)u[m]=t[h];else if("object"==typeof m&&"prop"in m){var g=m.access?m.access:m.prop;if(m.converter){var E=m.converter(t[h],t);void 0!==E&&(u[g]=E)}else m.type&&"number"===m.type?u[g]=+t[h]:m.type&&"boolean"===m.type?u[g]=!("0"===t[h]||!t[h]):u[g]=t[h]}}for(var I in a){var M=a[I];if(M&&void 0!==M.def){var S=M.access?M.access:M.prop;S in u||("function"==typeof M.def?u[S]=M.def(t):u[S]=M.def)}}return u=function unflattenObj(t){var a={},u=function _loop(u){var h=u.split(".");reduce(h).call(h,(function(a,m,g){return a[m]||(a[m]=isNaN(Number(h[g+1]))?h.length-1==g?t[u]:{}:[])}),a)};for(var h in t)u(h);return a}(u),u}function registerParser(t){for(var a in Ot(Mu,t.cmdConfig),t.cmdMap){var u=t.cmdMap[a],h=t.cmdConfig[u];if(h)if(zl(Su[a])){for(var m,g=!1,E=_createForOfIteratorHelperLoose$4(Su[a]);!(m=E()).done;){var I=m.value;if(I.cmd===u&&I.config.service===h.service){g=!0;break}}g||Su[a].push({config:h,cmd:u})}else Su[a]=[{config:h,cmd:u}]}}function invertSerializeMap(t){var a,u={};return forEach$1(a=Qi(t)).call(a,(function(a){u[a]=function invertSerializeItem(t){var a={};for(var u in t){var h=t[u];"number"==typeof h?a[h]=u:"object"==typeof h&&(a[h.id]={prop:u,type:h.retType,access:h.retAccess?h.retAccess:h.access?h.access:u,def:h.retDef,converter:h.retConverter})}return a}(t[a])})),u}function invert(t){t=t||{};var a={};for(var u in t)a[t[u]]=u;return a}var Cu={"6_2":"getNosToken","6_22":"getOriginUrl","6_24":"getNosAccessToken","6_25":"deleteNosAccessToken","6_26":"getNosCdnHost","6_27":"getGrayscaleConfig","6_28":"getMixStorePolicy","6_29":"getMixStoreToken","6_30":"getFileAuthToken"},Tu={nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},mixStoreTokenReqTag:{provider:0,tokenCount:1,nosSurvivalTime:2,tag:3,returnBody:4,policyVersion:5},nosConfigTag:{bucket:1,cdnDomain:2,expire:3,objectNamePrefix:4},grayConfigTag:{config:0,ttl:1},mixStorePolicyTag:{providers:0,ttl:1,mixEnable:2,nosPolicy:3,s3Policy:4,policyVersion:5},mixStoreTokenResTag:{provider:0,accessKeyId:1,secretAccessKey:2,sessionToken:3,token:4,expireTime:5,bucket:6,objectName:7,fileExpireSec:8,tag:9,shortUrl:10,region:11},nosSafeUrlTag:{safeUrl:0,originUrl:1},mixStoreAuthTokenReqTag:{type:1,urls:2},mixStoreAuthTokenResTag:{type:1,tokens:2,token:3,ttl:4},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3}},bu={getNosToken:{sid:6,cid:2,service:"cloudStorage",params:[{type:"String",name:"responseBody"},{type:"Property",name:"nosToken",entity:"nosToken",reflectMapper:Tu.nosToken}],response:[{type:"Property",name:"nosToken",reflectMapper:invert(Tu.nosToken)}]},getOriginUrl:{sid:6,cid:22,service:"cloudStorage",params:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:Tu.nosSafeUrlTag}],response:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:invert(Tu.nosSafeUrlTag)}]},getNosCdnHost:{sid:6,cid:26,service:"cloudStorage",response:[{type:"Property",name:"nosConfigTag",reflectMapper:invert(Tu.nosConfigTag)}]},getGrayscaleConfig:{sid:6,cid:27,service:"cloudStorage",params:[{type:"Property",name:"config"}],response:[{type:"Property",name:"grayConfigTag",reflectMapper:invert(Tu.grayConfigTag)}]},getMixStorePolicy:{sid:6,cid:28,service:"cloudStorage",params:[{type:"LongArray",name:"supportType"}],response:[{type:"Property",name:"mixStorePolicyTag",reflectMapper:invert(Tu.mixStorePolicyTag)}]},getMixStoreToken:{sid:6,cid:29,service:"cloudStorage",params:[{type:"Property",name:"mixStoreTokenReqTag",reflectMapper:Tu.mixStoreTokenReqTag}],response:[{type:"Property",name:"mixStoreTokenResTag",reflectMapper:invert(Tu.mixStoreTokenResTag)}]},getFileAuthToken:{sid:6,cid:30,service:"cloudStorage",params:[{type:"Property",name:"mixStoreAuthTokenReqTag",reflectMapper:Tu.mixStoreAuthTokenReqTag}],response:[{type:"Property",name:"mixStoreAuthTokenResTag",reflectMapper:invert(Tu.mixStoreAuthTokenResTag)}]},getNosAccessToken:{sid:6,cid:24,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:Tu.nosAccessTokenTag}],response:[{type:"Property",name:"tag",reflectMapper:invert(Tu.nosAccessTokenTag)}]},deleteNosAccessToken:{sid:6,cid:25,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:Tu.nosAccessTokenTag}]}},Ru=function(){function MixStorage(t,a){this.GRAYKEY="AllGrayscaleConfig",this.MIXSTOREKEY="AllMixStorePolicy",this.grayConfig={mixStoreEnable:!1,timeStamp:0,ttl:0},this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10,this.circuitTimer=0,this.core=t,this.cloudStorage=a,this.logger=t.logger}var t=MixStorage.prototype;return t.reset=function reset(){this.grayConfig=null,this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10},t.getGrayscaleConfig=function getGrayscaleConfig(t,a){var u;return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var h,m;return cs.wrap((function _callee$(g){for(;;)switch(g.prev=g.next){case 0:if(au.localStorage)try{au.localStorage.getItem&&au.localStorage.getItem(this.GRAYKEY)&&(this.grayConfig=JSON.parse(au.localStorage.getItem(this.GRAYKEY))[t])}catch(t){au.localStorage.getItem(this.GRAYKEY)&&this.core.logger.error("uploadFile:: JSON.parse grayscaleConfig error ",t)}if(this.grayConfig&&!(this.grayConfig.timeStamp+1e3*this.grayConfig.ttl<a)){g.next=17;break}return g.next=4,this.core.sendCmd("getGrayscaleConfig",{config:{}});case 4:if(!(h=g.sent).content||!h.content.grayConfigTag){g.next=16;break}this.logger.log("uploadFile::getAppGrayConfigRequest success ");try{this.grayConfig=JSON.parse(h.content.grayConfigTag.config),this.grayConfig.ttl=JSON.parse(h.content.grayConfigTag.ttl)}catch(t){this.logger.error("getGrayscaleConfig error",t)}if(this.grayConfig){g.next=10;break}return g.abrupt("return");case 10:m=au.localStorage.getItem(this.GRAYKEY)?JSON.parse(au.localStorage.getItem(this.GRAYKEY)):{},this.grayConfig.timeStamp=(new Date).getTime(),m[t]=this.grayConfig,au.localStorage.setItem(this.GRAYKEY,gs(m)),g.next=17;break;case 16:this.logger.log("uploadFile:: result grayConfig:",h.content);case 17:if(!(null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable)){g.next=20;break}return g.next=20,this._getMixStorePolicy(t);case 20:case"end":return g.stop()}}),_callee,this)})))},t._getMixStorePolicy=function _getMixStorePolicy(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a,u,h,m,g,E,I;return cs.wrap((function _callee2$(M){for(;;)switch(M.prev=M.next){case 0:if(a=(new Date).getTime(),au.localStorage)try{this.mixStorePolicy=JSON.parse(au.localStorage.getItem(this.MIXSTOREKEY))[t],this.curProvider=Kl(this.mixStorePolicy.providers[0]),this.mixStorePolicy.timeStamp&&this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl>a&&(h=this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl-a,this.core.timerManager.addTimer(bind$1(u=this._getMixStorePolicy).call(u,this,t),h))}catch(a){au.localStorage.getItem(this.MIXSTOREKEY)&&JSON.parse(au.localStorage.getItem(this.MIXSTOREKEY))[t]&&this.core.logger.error("uploadFile:: JSON.parse mixStorePolicy error ",a)}if(this.mixStorePolicy&&!(this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl<=a)){M.next=31;break}return M.prev=3,M.next=6,this.core.sendCmd("getMixStorePolicy",{supportType:this.cloudStorage.aws.s3?[1,2]:[1]});case 6:g=M.sent,E=g.content.mixStorePolicyTag,this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.mixStorePolicy.policyVersion=E.policyVersion,this.mixStorePolicy.ttl=Number(E.ttl),this.mixStorePolicy.providers=E.providers.split(","),this.circuitTimer&&this.core.timerManager.deleteTimer(this.circuitTimer),this.curProvider=Kl(this.mixStorePolicy.providers[0]),this.mixStorePolicy.nosPolicy=E.nosPolicy?JSON.parse(E.nosPolicy):null,this.mixStorePolicy.s3Policy=E.s3Policy?JSON.parse(E.s3Policy):null,null===this.mixStorePolicy.s3Policy?this.mixStorePolicy.providers=["1"]:null===this.mixStorePolicy.nosPolicy?this.mixStorePolicy.providers=["2"]:this.mixStorePolicy.providers=this.mixStorePolicy.s3Policy.priority<this.mixStorePolicy.nosPolicy.priority?["2","1"]:["1","2"],this.core.timerManager.addTimer(bind$1(m=this._getMixStorePolicy).call(m,this,t),1e3*this.mixStorePolicy.ttl),I=au.localStorage.getItem(this.MIXSTOREKEY)?JSON.parse(au.localStorage.getItem(this.MIXSTOREKEY)):{},this.mixStorePolicy.timeStamp=(new Date).getTime(),I[t]=this.mixStorePolicy,au.localStorage.setItem(this.MIXSTOREKEY,gs(I)),M.next=31;break;case 24:if(M.prev=24,M.t0=M.catch(3),this.logger.error("getMixStorePolicy error",M.t0),0!==this.mixStoreErrorCount){M.next=29;break}throw new Error("getMixStorePolicy all count error");case 29:this._getMixStorePolicy(t),this.mixStoreErrorCount--;case 31:this.mixStorePolicy.nosPolicy&&(this.cloudStorage.nos.nosErrorCount=this.mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry);case 32:case"end":return M.stop()}}),_callee2,this,[[3,24]])})))},t._addCircuitTimer=function _addCircuitTimer(){var t=this,a=this.mixStorePolicy.providers,u=a[(indexOf(a).call(a,String(this.curProvider))+1)%a.length];if(!u)throw new Error("uploadFile nextProvider error");if(u===a[0])throw new Error("uploadFile all policy fail");if(this.logger.log("uploadFile:: upload policy will change,now policy:"+this.curProvider+" nextProvider:"+u),this.curProvider=Kl(u),this.mixStorePolicy.nosPolicy&&this.mixStorePolicy.s3Policy){var h=this.mixStorePolicy[1===this.curProvider?"nosPolicy":"s3Policy"].uploadConfig.retryPolicy.circuit;if(!h||0===h)throw new Error("uploadFile circuit error");this.circuitTimer=this.core.timerManager.addTimer((function(){t.logger.log("uploadFile:: upload policy will change,now policy:"+t.curProvider+" nextProvider:"+Kl(t.mixStorePolicy.providers[0])),t.curProvider=Kl(t.mixStorePolicy.providers[0]),t.core.timerManager.deleteTimer(t.circuitTimer)}),1e3*h)}throw new Error("uploadFile will not retry again")},t.getFileAuthToken=function getFileAuthToken(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var a;return cs.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,this.core.sendCmd("getFileAuthToken",{mixStoreAuthTokenReqTag:t});case 2:return a=u.sent,u.abrupt("return",a.content.mixStoreAuthTokenResTag);case 4:case"end":return u.stop()}}),_callee3,this)})))},MixStorage}(),Au=Ps.trim,Nu=A("".charAt),wu=m.parseFloat,Ou=m.Symbol,xu=Ou&&Ou.iterator,ku=1/wu(Ns+"-0")!=-1/0||xu&&!fails((function(){wu(Object(xu))}))?function parseFloat(t){var a=Au(toString(t)),u=wu(a);return 0===u&&"-"==Nu(a,0)?-0:u}:wu;_export({global:!0,forced:parseFloat!=ku},{parseFloat:ku});var Pu=W.parseFloat,Lu=-1,qu=function(){function AWS(t,a){this.s3=null,this.core=t,this.cloudStorage=a,this.logger=t.logger}var t=AWS.prototype;return t.s3Upload=function s3Upload(t,a){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var u,h,m,g,E,I,M,S,C,T,b,R,A,N,w=this;return cs.wrap((function _callee2$(O){for(;;)switch(O.prev=O.next){case 0:if(Lu+=1,!t.file){O.next=5;break}u=t.file,O.next=20;break;case 5:if("string"!=typeof t.fileInput){O.next=15;break}if(this.logger.warn("fileInput will abandon,Please use file or filepath"),!((h=document.getElementById(t.fileInput))&&h.files&&h.files[0])){O.next=12;break}u=h.files[0],O.next=13;break;case 12:throw new Error("Can not get file from fileInput");case 13:O.next=20;break;case 15:if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0])){O.next=19;break}u=t.fileInput.files[0],O.next=20;break;case 19:throw new Error("Can not get file from fileInput "+t.fileInput);case 20:if(this.mixStorePolicy.s3Policy){O.next=22;break}throw new Error("dont get s3 policy");case 22:return m={accessKeyId:a.accessKeyId,secretAccessKey:a.secretAccessKey,sessionToken:a.sessionToken,region:a.region,maxRetries:this.mixStorePolicy.s3Policy.uploadConfig.retryPolicy.retry},g=this.s3,E=decodeURIComponent(a.bucket),I=decodeURIComponent(a.objectName),M=u,S="https://"+E+".s3.amazonaws.com/"+I,C={},(T=this.mixStorePolicy.s3Policy)&&T.uploadConfig&&zl(T.uploadConfig.uploadUrl)&&T.uploadConfig.uploadUrl.length>0&&(b=T.uploadConfig.uploadUrl.length,Lu%=b,C.endpoint=T.uploadConfig.uploadUrl[Lu],C.s3ForcePathStyle=!0,S=C.endpoint+"/"+E+"/"+I),this.core.reporterHookCloudStorage.update({remote_addr:S,operation_type:1}),(R=new g(C)).config.update(m),A={Bucket:E,Key:I,Body:M,Metadata:{token:a.token},ContentType:M.type||"application/octet-stream"},this.core.logger.log("uploadFile:: s3 upload params:",A),(N=R.upload(A)).on("httpUploadProgress",(function(a){var u=Pu((a.loaded/a.total).toFixed(2));t.onUploadProgress&&t.onUploadProgress({total:a.total,loaded:a.loaded,percentage:u,percentageText:Math.round(100*u)+"%"})})),O.abrupt("return",new za((function(u,h){var m=(new Date).getTime();N.send((function(g,S){return __awaiter(w,void 0,void 0,cs.mark((function _callee(){var C,T,b,R,A,N,w,O;return cs.wrap((function _callee$(x){for(;;)switch(x.prev=x.next){case 0:if(!g||"RequestAbortedError"!==g.code){x.next=5;break}this.logger.error("uploadFile:","api::s3:upload file abort.",g),h(new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:"S3RequestAbortedError",rawError:g,curProvider:2}})),x.next=41;break;case 5:if(!g){x.next=26;break}return this.logger.error("uploadFile:","api::s3:upload file failed.",g),this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(T=null===(C=this.core)||void 0===C?void 0:C.auth)||void 0===T?void 0:T.account),trace_id:null===(b=this.core.clientSocket.socket)||void 0===b?void 0:b.sessionId,start_time:m,action:1,exception_service:4}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof g.status?g.status:"number"==typeof g.code?g.code:0,description:g.message||""+g.code,operation_type:1,target:gs({bucket:E,object:I})},{asyncParams:au.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),x.next=12,au.net.getNetworkStatus();case 12:if(R=x.sent,!1!==R.net_connect){x.next=16;break}return x.abrupt("return",h(new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:g,curProvider:this.cloudStorage.mixStorage.curProvider}})));case 16:x.prev=16,this.cloudStorage.mixStorage._addCircuitTimer(),x.next=23;break;case 20:return x.prev=20,x.t0=x.catch(16),x.abrupt("return",h(new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:x.t0,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.mixStorePolicy,file:t.file||t.filePath}})));case 23:u(this.cloudStorage._uploadFile(t)),x.next=41;break;case 26:if(A=(A=(A=this.mixStorePolicy.s3Policy.cdnSchema).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn)).replace("{objectName}",S.Key),N={size:M.size,name:M.name,url:a.shortUrl?a.shortUrl:A,ext:M.name.split(".")[1]||"unknown"},w=t.type||"",(O={image:"imageInfo"})[w]){x.next=36;break}return x.abrupt("return",u(N));case 36:return x.t1=u,x.next=39,this.getS3FileInfo({url:A,infoSuffix:O[w],s3Result:N});case 39:return x.t2=x.sent,x.abrupt("return",(0,x.t1)(x.t2));case 41:case"end":return x.stop()}}),_callee,this,[[16,20]])})))})),t.onUploadStart&&t.onUploadStart(N)})));case 39:case"end":return O.stop()}}),_callee2,this)})))},t.getS3FileInfo=function getS3FileInfo(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var u,h,m,g,E,I,M;return cs.wrap((function _callee3$(S){for(;;)switch(S.prev=S.next){case 0:return u=t.url,h=t.infoSuffix,m=t.s3Result,S.prev=1,S.next=4,this.core.adapters.request(u+"?"+h,{method:"GET",dataType:"text",timeout:5e3},{exception_service:3});case 4:g=S.sent,S.next=11;break;case 7:return S.prev=7,S.t0=S.catch(1),this.core.logger.error("uploadFile:: fetch file info error",S.t0),S.abrupt("return",m);case 11:if(!g){S.next=18;break}return E=g.data,I="imageInfo"===h?E:null===(a=null==E?void 0:E.GetVideoInfo)||void 0===a?void 0:a.VideoInfo,M=Ot(Ot({},m),{w:null==I?void 0:I.Width,h:null==I?void 0:I.Height,orientation:null==I?void 0:I.Orientation,dur:null==I?void 0:I.Duration,audioCodec:null==I?void 0:I.AudioCodec,videoCodec:null==I?void 0:I.VideoCodec,container:null==I?void 0:I.Container}),S.abrupt("return",pickBy(M,(function(t){return void 0!==t})));case 18:return this.core.logger.error("uploadFile:: fetch s3 file info no result",u+"?"+h),S.abrupt("return",m);case 20:case"end":return S.stop()}}),_callee3,this,[[1,7]])})))},as(AWS,[{key:"mixStorePolicy",get:function get(){return this.cloudStorage.mixStorage.mixStorePolicy}}]),AWS}(),Du=function(){function CloudStorageService(t,a){void 0===a&&(a={}),this.config={},this.uploadTaskMap={},this.name="cloudStorage",this.logger=t.logger,this.core=t,this.nos=new ou(t,this),this.mixStorage=new Ru(t,this),this.aws=new qu(t,this),registerParser({cmdMap:Cu,cmdConfig:bu}),this.setOptions(a),this.setListeners()}var t=CloudStorageService.prototype;return t.setOptions=function setOptions(t){void 0===t&&(t={});var a=t.storageKeyPrefix||"NIMClient";this.mixStorage.GRAYKEY=a+"-AllGrayscaleConfig",this.mixStorage.MIXSTOREKEY=a+"-AllMixStorePolicy";var u=t.s3,h=__rest(t,["s3"]),m=Ot({},nu,this.config);if(h&&Object.prototype.hasOwnProperty.call(h,"cdn")){var g=Ot(Ot({},m.cdn),h.cdn);this.config=Ot({},m,h),this.config.cdn=g}else this.config=Ot({},m,h);u&&(this.aws.s3=u)},t.setListeners=function setListeners(){var t,a,u,h;this.core.eventBus.on("kicked",bind$1(t=this._clearUnCompleteTask).call(t,this)),this.core.eventBus.on("disconnect",bind$1(a=this._clearUnCompleteTask).call(a,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLogout",bind$1(u=this._clearUnCompleteTask).call(u,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleKicked",bind$1(h=this._clearUnCompleteTask).call(h,this))},t._clearUnCompleteTask=function _clearUnCompleteTask(){var t,a=this;forEach$1(t=Qi(this.uploadTaskMap)).call(t,(function(t){var u=a.uploadTaskMap[t];u&&u.abort&&u.abort()})),this.uploadTaskMap={}},t.init=function init(t){return void 0===t&&(t=Za()),__awaiter(this,void 0,void 0,cs.mark((function _callee(){return cs.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.mixStorage.reset(),this.nos.reset(),!this.config.isNeedToGetUploadPolicyFromServer){a.next=5;break}return a.next=5,this.mixStorage.getGrayscaleConfig(this.core.options.appkey,t);case 5:return a.next=7,this.nos._getNosCdnHost();case 7:case"end":return a.stop()}}),_callee,this)})))},t.processCallback=function processCallback(t,a){var u=this,h=t.onUploadProgress,m=t.onUploadDone,g=t.onUploadStart;return{onUploadStart:"function"==typeof g?function(t){u.uploadTaskMap[a]=t;try{g(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadStart execute error",t)}}:function(t){u.uploadTaskMap[a]=t},onUploadProgress:"function"==typeof h?function(t){u.core.reporterHookCloudStorage.update({transferred_size:t.loaded,full_size:t.total});try{h(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadProgress execute error",t)}}:function(t){u.core.reporterHookCloudStorage.update({transferred_size:t.loaded,full_size:t.total})},onUploadDone:"function"==typeof m?function(t){u.core.reporterHookCloudStorage.end(0);try{m(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadDone execute error",t)}}:function(){u.core.reporterHookCloudStorage.end(0)},taskKey:a}},t.uploadFile=function uploadFile(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a,u,h,m,g,E,I,M;return cs.wrap((function _callee2$(S){for(;;)switch(S.prev=S.next){case 0:if(validate({maxSize:{type:"number",required:!1},type:{type:"enum",values:["file","image","audio","video"]}},t),t.fileInput||t.file||t.filePath){S.next=3;break}throw new Error("uploadFile needs target file object or a filePath");case 3:if(!t.type||"file"===t.type){S.next=7;break}if(!(a=get(t,"file.type"))||"string"!=typeof a||-1!==indexOf(a).call(a,t.type)){S.next=7;break}throw new Error('The meta type "'+a+'" does not match "'+t.type+'"');case 7:return this.core.reporterHookCloudStorage.start(),t.file?this.core.reporterHookCloudStorage.update({full_size:t.file.size}):"string"==typeof t.fileInput?(u=document.getElementById(t.fileInput))&&u.files&&u.files[0]&&this.core.reporterHookCloudStorage.update({full_size:u.files[0].size}):t.fileInput&&t.fileInput.files&&t.fileInput.files[0]&&this.core.reporterHookCloudStorage.update({full_size:t.fileInput.files[0].size}),h=Xl(),m=this.processCallback(t,h),g=m.onUploadStart,E=m.onUploadProgress,I=m.onUploadDone,t.onUploadStart=g,t.onUploadProgress=E,t.onUploadDone=I,M=null,S.prev=15,S.next=18,this._uploadFile(t);case 18:M=S.sent,t.md5&&(M.md5=t.md5),delete this.uploadTaskMap[h],S.next=28;break;case 23:throw S.prev=23,S.t0=S.catch(15),delete this.uploadTaskMap[h],this.core.reporterHookCloudStorage.end((S.t0&&S.t0.code)===kl.V2NIM_ERROR_CODE_CANCELLED?3:1),S.t0;case 28:return M&&(M.size=void 0===M.size?void 0:Number(M.size),M.w=void 0===M.w?void 0:Number(M.w),M.h=void 0===M.h?void 0:Number(M.h),M.dur=void 0===M.dur?void 0:Number(M.dur)),M.url=decodeURIComponent(M.url),t.onUploadDone({size:M.size,name:M.name,url:M.url,ext:M.name.split(".")[1]||"unknown"}),S.abrupt("return",M);case 32:case"end":return S.stop()}}),_callee2,this,[[15,23]])})))},t._uploadFile=function _uploadFile(t){var a,u;return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var h,m,g,E;return cs.wrap((function _callee3$(I){for(;;)switch(I.prev=I.next){case 0:if(get(this.mixStorage,"grayConfig.mixStoreEnable")&&get(this.mixStorage,"mixStorePolicy.providers.length")){I.next=3;break}return this.logger.log("uploadFile:: uploadFile begin, use old nos"),I.abrupt("return",this.nos.nosUpload(t));case 3:if(this.logger.log("uploadFile::_uploadFile, grayConfig enable:"+get(this.mixStorage,"grayConfig.mixStoreEnable")+" curProvider:"+get(this.mixStorage,"curProvider")),h=this.core.adapters.getFileUploadInformation(t),m=!0,h?!1===h.complete&&2===this.mixStorage.curProvider&&(m=!1):m=!1,this.aws.s3||(this.mixStorage.curProvider=1),g=ru,m){I.next=23;break}return I.prev=10,I.next=13,this.core.sendCmd("getMixStoreToken",{mixStoreTokenReqTag:{provider:this.mixStorage.curProvider,tokenCount:1,tag:"qchat",nosSurvivalTime:t.nosSurvivalTime,returnBody:getUploadResponseFormat(t.type),policyVersion:this.mixStorage.mixStorePolicy.policyVersion}});case 13:E=I.sent,g=E.content.mixStoreTokenResTag,I.next=23;break;case 17:if(I.prev=17,I.t0=I.catch(10),this.core.logger.error("uploadFile:: getMixStoreToken error",I.t0),!(I.t0 instanceof Ll)){I.next=22;break}throw I.t0;case 22:throw new Ul({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getMixStoreToken error",rawError:I.t0,curProvider:this.mixStorage.curProvider,mixStorePolicy:this.mixStorage.mixStorePolicy}});case 23:if(m){I.next=27;break}return I.abrupt("return",2===this.mixStorage.curProvider?this.aws.s3Upload(t,g):this.nos.nosUpload(t,g));case 27:return I.abrupt("return",this.nos.nosUpload(t,null===(u=null===(a=null==h?void 0:h.uploadInfo)||void 0===a?void 0:a.payload)||void 0===u?void 0:u.mixStoreToken));case 28:case"end":return I.stop()}}),_callee3,this,[[10,17]])})))},t.getThumbUrl=function getThumbUrl(t,a){var u,h,m,g,E;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),t;var I=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);I[0],I[1],I[2],I[3],I[4];var M=I[5];if(I[6],I[7],null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable){var S=this._getUrlType(t);if(2===S&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.imagethumb"))return(null===(m=null===(h=this.mixStorePolicy.s3Policy)||void 0===h?void 0:h.thumbPolicy)||void 0===m?void 0:m.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",M).replace("{x}",a.width.toString()).replace("{y}",a.height.toString());if(1===S&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.imagethumb"))return(null===(E=null===(g=this.mixStorePolicy.nosPolicy)||void 0===g?void 0:g.thumbPolicy)||void 0===E?void 0:E.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",M).replace("{x}",a.width.toString()).replace("{y}",a.height.toString())}return includes(t).call(t,"?")?t+"&imageView&thumbnail="+a.width+"x"+a.height:t+"?imageView&thumbnail="+a.width+"x"+a.height},t.getVideoCoverUrl=function getVideoCoverUrl(t,a){var u,h,m,g,E;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),t;var I=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);I[0],I[1],I[2],I[3],I[4];var M=I[5];if(I[6],I[7],null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable){var S=this._getUrlType(t);if(2===S&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.vframe"))return(null===(m=null===(h=this.mixStorePolicy.s3Policy)||void 0===h?void 0:h.thumbPolicy)||void 0===m?void 0:m.vframe).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",M).replace("{x}",a.width.toString()).replace("{y}",a.height.toString()).replace("{offset}","0").replace("{type}","png");if(1===S&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.vframe"))return(null===(E=null===(g=this.mixStorePolicy.nosPolicy)||void 0===g?void 0:g.thumbPolicy)||void 0===E?void 0:E.vframe).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",M).replace("{x}",a.width.toString()).replace("{y}",a.height.toString()).replace("{offset}","0").replace("{type}","png")}return includes(t).call(t,"?")?t+"&vframe&offset=0&resize="+a.width+"x"+a.height+"&type=png":t+"?vframe&offset=0&resize="+a.width+"x"+a.height+"&type=png"},t.getPrivateUrl=function getPrivateUrl(t){var a;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),"";var u=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);u[0];var h=u[1];u[2];var m=u[3];u[4];var g=u[5];if(u[6],u[7],null===(a=this.grayConfig)||void 0===a?void 0:a.mixStoreEnable){var E=this._getUrlType(t);return 2===E&&this.mixStorePolicy.s3Policy&&(t=this.mixStorePolicy.s3Policy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",g)),1===E&&this.mixStorePolicy.nosPolicy&&(t=this.mixStorePolicy.nosPolicy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",g)),t}var I=this.config,M=I.downloadUrl,S=I.downloadHostList,C=I.nosCdnEnable,T=this.config.cdn.cdnDomain,b=this.config.cdn.objectNamePrefix?decodeURIComponent(this.config.cdn.objectNamePrefix):"",R=decodeURIComponent(g),A=indexOf(R).call(R,b);if(T&&A>-1&&C)return""+h+T+"/"+slice(R).call(R,A);if(includes(S).call(S,m)&&includes(g).call(g,"/")){var N=indexOf(g).call(g,"/"),w=g.substring(0,N),O=g.substring(N+1);return M.replace("{bucket}",w).replace("{object}",O)}var x=filter(S).call(S,(function(t){return"string"==typeof m&&includes(m).call(m,t)}))[0],k=x?m.replace(x,"").replace(/\W/g,""):null;return k?M.replace("{bucket}",k).replace("{object}",g):t},t.getOriginUrl=function getOriginUrl(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a;return cs.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:if("string"==typeof t&&includes(t).call(t,"_im_url=1")){u.next=2;break}return u.abrupt("return",t);case 2:return u.next=4,this.core.sendCmd("getOriginUrl",{nosSafeUrlTag:{safeUrl:t}});case 4:return a=u.sent,u.abrupt("return",a.content.nosSafeUrlTag.originUrl);case 6:case"end":return u.stop()}}),_callee4,this)})))},t.getFileToken=function getFileToken(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var a,u,h,m,g,E=this;return cs.wrap((function _callee5$(I){for(;;)switch(I.prev=I.next){case 0:if(validate({type:{type:"number",min:2,max:3},urls:{type:"array",required:!1,itemType:"string"}},t),a=this.mixStorePolicy.nosPolicy?this.mixStorePolicy.nosPolicy.authPolicy.policyType:null,u=this.mixStorePolicy.s3Policy?this.mixStorePolicy.s3Policy.authPolicy.policyType:null,a!==String(-1)||u!==String(-1)){I.next=8;break}throw this.logger.error("don't need token"),new Error("don't need token");case 8:if(2!==t.type){I.next=17;break}if(!(a&&indexOf(a).call(a,String(2))>=0||u&&indexOf(u).call(u,String(2))>0)){I.next=13;break}return I.abrupt("return",this.mixStorage.getFileAuthToken(t));case 13:throw this.logger.error("don't support time token "),new Error("don't support type time token ");case 15:I.next=33;break;case 17:if(t.urls&&t.urls.length){I.next=20;break}throw this.logger.error("urls is required when urls token"),new Error("urls is required when urls token");case 20:if(m=[],g=[],forEach$1(h=t.urls).call(h,(function(t){var a=E._getUrlType(t);1===a&&g.push(t),2===a&&m.push(t)})),(!u||0!==m.length&&indexOf(u).call(u,String(3))<0)&&(this.logger.warn("s3 url don't support url token"),m=[]),(!a||0!==g.length&&indexOf(a).call(a,String(3))<0)&&(this.logger.warn("nos url don't support url token"),g=[]),0!==m.length||0!==g.length){I.next=30;break}throw this.logger.error("not support urls"),new Error("not support urls");case 30:if(0!==m.length&&0!==g.length){I.next=33;break}return t.urls=gs(t.urls),I.abrupt("return",this.mixStorage.getFileAuthToken(t));case 33:case"end":return I.stop()}}),_callee5,this)})))},t._getUrlType=function _getUrlType(t){var a,u;return this.mixStorePolicy.nosPolicy&&some(a=this.mixStorePolicy.nosPolicy.dlcdns).call(a,(function(a){return indexOf(t).call(t,a)>=0}))?1:this.mixStorePolicy.s3Policy&&some(u=this.mixStorePolicy.s3Policy.dlcdns).call(u,(function(a){return indexOf(t).call(t,a)>=0}))?2:null},t.getNosAccessToken=function getNosAccessToken(t){return validate({url:{type:"string",allowEmpty:!1}},t),this.nos.getNosAccessToken(t)},t.deleteNosAccessToken=function deleteNosAccessToken(t){return validate({token:{type:"string",allowEmpty:!1}},t),this.nos.deleteNosAccessToken(t)},t.process=function process(t){var a=get(t,"error.detail.ignore");return t.error&&!a?za.reject(t.error):za.resolve(t)},as(CloudStorageService,[{key:"grayConfig",get:function get(){return this.mixStorage.grayConfig}},{key:"mixStorePolicy",get:function get(){return this.mixStorage.mixStorePolicy}}]),CloudStorageService}(),Vu=TypeError,deletePropertyOrThrow=function(t,a){if(!delete t[a])throw Vu("Cannot delete property "+tryToString(a)+" of "+tryToString(t))},Uu=Math.floor,mergeSort=function(t,a){var u=t.length,h=Uu(u/2);return u<8?insertionSort(t,a):merge(t,mergeSort(arraySliceSimple(t,0,h),a),mergeSort(arraySliceSimple(t,h),a),a)},insertionSort=function(t,a){for(var u,h,m=t.length,g=1;g<m;){for(h=g,u=t[g];h&&a(t[h-1],u)>0;)t[h]=t[--h];h!==g++&&(t[h]=u)}return t},merge=function(t,a,u,h){for(var m=a.length,g=u.length,E=0,I=0;E<m||I<g;)t[E+I]=E<m&&I<g?h(a[E],u[I])<=0?a[E++]:u[I++]:E<m?a[E++]:u[I++];return t},Gu=mergeSort,Fu=$.match(/firefox\/(\d+)/i),Bu=!!Fu&&+Fu[1],ju=/MSIE|Trident/.test($),Wu=$.match(/AppleWebKit\/(\d+)\./),Yu=!!Wu&&+Wu[1],$u=[],Hu=A($u.sort),Qu=A($u.push),Ku=fails((function(){$u.sort(void 0)})),zu=fails((function(){$u.sort(null)})),Ju=arrayMethodIsStrict("sort"),Xu=!fails((function(){if(X)return X<70;if(!(Bu&&Bu>3)){if(ju)return!0;if(Yu)return Yu<603;var t,a,u,h,m="";for(t=65;t<76;t++){switch(a=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:u=3;break;case 68:case 71:u=4;break;default:u=2}for(h=0;h<47;h++)$u.push({k:a+h,v:u})}for($u.sort((function(t,a){return a.v-t.v})),h=0;h<$u.length;h++)a=$u[h].k.charAt(0),m.charAt(m.length-1)!==a&&(m+=a);return"DGBEFHACIJK"!==m}}));_export({target:"Array",proto:!0,forced:Ku||!zu||!Ju||!Xu},{sort:function sort(t){void 0!==t&&aCallable(t);var a=toObject(this);if(Xu)return void 0===t?Hu(a):Hu(a,t);var u,h,m=[],g=lengthOfArrayLike(a);for(h=0;h<g;h++)h in a&&Qu(m,a[h]);for(Gu(m,function(t){return function(a,u){return void 0===u?-1:void 0===a?1:void 0!==t?+t(a,u)||0:toString(a)>toString(u)?1:-1}}(t)),u=lengthOfArrayLike(m),h=0;h<u;)a[h]=m[h++];for(;h<g;)deletePropertyOrThrow(a,h++);return a}});var Zu,ed,td,rd=entryVirtual("Array").sort,nd=Array.prototype,sort=function(t){var a=t.sort;return t===nd||Y(nd,t)&&a===nd.sort?rd:a};!function(t){t[t.ASC=1]="ASC",t[t.DESC=2]="DESC"}(Zu||(Zu={})),function(t){t[t.reorderWeight=0]="reorderWeight",t[t.createTime=1]="createTime",t[t.totalMember=2]="totalMember"}(ed||(ed={})),function(t){t[t.square=1]="square",t[t.person=2]="person"}(td||(td={}));var ad=function(t){function Service(a,u){var h;return(h=t.call(this)||this).name=a,h.core=u,h.name=a,h.logger=u.logger,h.core=u,h}Tt(Service,t);var a=Service.prototype;return a.emit=function emit(a){var u=this;try{for(var h,m,g=arguments.length,E=new Array(g>1?g-1:0),I=1;I<g;I++)E[I-1]=arguments[I];var M=(h=t.prototype.emit).call.apply(h,concat(m=[this,a]).call(m,E));return M}catch(t){return go((function(){throw u.logger.error(u.name+"::emit throw error in setTimeout. event: "+a.toString()+". Error",t),t}),0),!1}},a.process=function process(t){var a=this[t.cmd+"Handler"];if("function"==typeof a)return a.call(this,t);var u=get(t,"error.detail.ignore");return t.error&&!u?za.reject(t.error):za.resolve(t)},Service}(vo),od={"24_15":"qchatSubscribe","24_27":"qchatGetUnreadInfo","24_48":"qchatCreateChannel","24_49":"qchatDeleteChannel","24_50":"qchatUpdateChannel","24_51":"qchatGetChannels","24_52":"qchatGetChannelsByPage","24_53":"qchatGetMembersByPage","24_54":"qchatUpdateWhiteBlackRole","24_55":"qchatGetWhiteBlackRolesPage","24_56":"qchatUpdateWhiteBlackMembers","24_57":"qchatGetWhiteBlackMembersPage","24_58":"qchatGetExistingWhiteBlackRoles","24_59":"qchatGetExistingWhiteBlackMembers","24_60":"qchatUpdateCategoryInfoOfChannel","24_109":"qchatCreateChannelCategory","24_110":"qchatRemoveChannelCategory","24_111":"qchatUpdateChannelCategory","24_112":"qchatGetChannelCategoriesByID","24_113":"qchatUpdateChannelCategoryWhiteBlackRole","24_114":"qchatGetChannelCategoryWhiteBlackRolesPage","24_115":"qchatUpdateChannelCategoryWhiteBlackMembers","24_116":"qchatGetChannelCategoryWhiteBlackMembersPage","24_117":"qchatGetChannelCategoryWhiteBlackRoles","24_118":"qchatGetChannelCategoryWhiteBlackMembers","24_119":"qchatGetChannelCategoriesPage","24_120":"qchatGetChannelCategoryChannelsPage","24_93":"qchatGetChannelSearchByPage","24_95":"qchatChannelMemberSearch","25_8":"qchatGetRoleIdsByServerId","25_9":"qchatSubscribeAsVisitor","25_12":"qchatAutoSubscribe","25_13":"qchatAutoSubscribeNotification"},id={serverId:1,channelId:2,ackTimestamp:3,unreadCount:4,mentionedCount:5,maxCount:6,lastMsgTime:7},sd={channelInfo:{channelId:1,serverId:2,name:4,topic:5,ext:6,type:7,validFlag:8,createTime:9,updateTime:10,owner:11,viewMode:12,categoryId:13,syncMode:14,reorderWeight:15,visitorMode:16},antispamTag:{antiSpamBusinessId:1},memberInfo:{serverId:1,accid:3,nick:4,avatar:5,ext:6,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},serverRole:{serverId:1,roleId:2,name:3,icon:4,ext:5,auths:6,type:7,memberCount:8,priority:9,createTime:10,updateTime:11},qchatSubReqTag:{type:1,opeType:2},qchatChannelIdInfoTag:{serverId:1,channelId:2},unreadInfo:id,qchatGetChannelListPageTag:{serverId:1,timetag:2,limit:3},qchatGetMembersByPageTag:{serverId:1,channelId:2,timetag:3,limit:4},qchatUpdateWhiteBlackRoleTag:{serverId:1,channelId:2,type:3,opeType:4,roleId:5},qchatGetWhiteBlackRolesPageTag:{serverId:1,channelId:2,type:3,timetag:4,limit:5},qchatUpdateWhiteBlackMembersTag:{serverId:1,channelId:2,type:3,opeType:4,toAccids:5},qchatGetWhiteBlackMembersPageTag:{serverId:1,channelId:2,type:3,timetag:4,limit:5},qchatGetExistingWhiteBlackRolesTag:{serverId:1,channelId:2,type:3,roleIds:4},qchatGetExistingWhiteBlackMembersTag:{serverId:1,channelId:2,type:3,accids:4},QChatChannelCategoryInfo:{categoryId:1,serverId:2,name:4,ext:5,owner:6,viewMode:7,validFlag:8,createTime:9,updateTime:10,channelNumber:11},qchatUpdateChannelCategoryWhiteBlackRoleTag:{serverId:1,categoryId:2,type:3,opeType:4,roleId:5},qchatGetChannelCategoryWhiteBlackRolesPageTag:{serverId:1,categoryId:2,type:3,timetag:4,limit:5},qchatUpdateChannelCategoryWhiteBlackMembersTag:{serverId:1,categoryId:2,type:3,opeType:4,toAccids:5},qchatGetChannelCategoryWhiteBlackMembersPageTag:{serverId:1,categoryId:2,type:3,timetag:4,limit:5},qchatGetChannelCategoryWhiteBlackRolesTag:{serverId:1,categoryId:2,type:3,roleIds:4},qchatGetChannelCategoryWhiteBlackMembersTag:{serverId:1,categoryId:2,type:3,accids:4},qchatGetChannelCategoriesPageTag:{serverId:1,timetag:2,limit:3},qchatGetChannelCategoryChannelsPageTag:{serverId:1,categoryId:2,timetag:3,limit:4},qchatGetChannelSearchByPageTag:{keyword:1,startTime:2,endTime:3,order:4,limit:5,serverId:6,sort:7,cursor:8},qchatUpdateChannelTag:{channelId:1,name:4,topic:5,ext:6,viewMode:12,visitorMode:16},serverRoles:{serverId:1,roleIds:2,timeTag:3},qchatChannelMemberSearchTag:{serverId:1,channelId:2,keyword:3,limit:4},qchatChannelMemberInfo:{serverId:1,channelId:2,avatar:3,accid:4,nick:5,createTime:6,updateTime:7}},cd=function getDeserializeTag(){return invertSerializeMap(sd)},ld=function getCmdConfig(){var t=cd();return{qchatCreateChannel:{sid:24,cid:48,service:"qchatChannel",params:[{type:"Property",name:"channelInfo",reflectMapper:sd.channelInfo},{type:"Property",name:"antispamTag",reflectMapper:sd.antispamTag}],response:[{type:"Property",name:"channelInfo",reflectMapper:t.channelInfo}]},qchatDeleteChannel:{sid:24,cid:49,service:"qchatChannel",params:[{type:"Long",name:"channelId"}]},qchatUpdateChannel:{sid:24,cid:50,service:"qchatChannel",params:[{type:"Property",name:"channelInfo",reflectMapper:sd.qchatUpdateChannelTag},{type:"Property",name:"antispamTag",reflectMapper:sd.antispamTag}],response:[{type:"Property",name:"channelInfo",reflectMapper:t.channelInfo}]},qchatGetChannels:{sid:24,cid:51,service:"qchatChannel",params:[{type:"LongArray",name:"channelIds"}],response:[{type:"PropertyArray",name:"channelList",reflectMapper:t.channelInfo}]},qchatGetChannelsByPage:{sid:24,cid:52,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelListPageTag",reflectMapper:sd.qchatGetChannelListPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.channelInfo}]},qchatGetMembersByPage:{sid:24,cid:53,service:"qchatChannel",params:[{type:"Property",name:"qchatGetMembersByPageTag",reflectMapper:sd.qchatGetMembersByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.memberInfo}]},qchatUpdateWhiteBlackRole:{sid:24,cid:54,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateWhiteBlackRoleTag",reflectMapper:sd.qchatUpdateWhiteBlackRoleTag}]},qchatGetWhiteBlackRolesPage:{sid:24,cid:55,service:"qchatChannel",params:[{type:"Property",name:"qchatGetWhiteBlackRolesPageTag",reflectMapper:sd.qchatGetWhiteBlackRolesPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverRole}]},qchatUpdateWhiteBlackMembers:{sid:24,cid:56,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateWhiteBlackMembersTag",reflectMapper:sd.qchatUpdateWhiteBlackMembersTag}]},qchatGetWhiteBlackMembersPage:{sid:24,cid:57,service:"qchatChannel",params:[{type:"Property",name:"qchatGetWhiteBlackMembersPageTag",reflectMapper:sd.qchatGetWhiteBlackMembersPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.memberInfo}]},qchatGetExistingWhiteBlackRoles:{sid:24,cid:58,service:"qchatChannel",params:[{type:"Property",name:"qchatGetExistingWhiteBlackRolesTag",reflectMapper:sd.qchatGetExistingWhiteBlackRolesTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:t.serverRole}]},qchatGetExistingWhiteBlackMembers:{sid:24,cid:59,service:"qchatChannel",params:[{type:"Property",name:"qchatGetExistingWhiteBlackMembersTag",reflectMapper:sd.qchatGetExistingWhiteBlackMembersTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:t.memberInfo}]},qchatUpdateCategoryInfoOfChannel:{sid:24,cid:60,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateCategoryInfoOfChannelTag",reflectMapper:sd.channelInfo}],response:[{type:"Property",name:"channelInfo",reflectMapper:t.channelInfo}]},qchatCreateChannelCategory:{sid:24,cid:109,service:"qchatChannel",params:[{type:"Property",name:"qchatCreateChannelCategoryTag",reflectMapper:sd.QChatChannelCategoryInfo}],response:[{type:"Property",name:"QChatChannelCategoryInfo",reflectMapper:t.QChatChannelCategoryInfo}]},qchatRemoveChannelCategory:{sid:24,cid:110,service:"qchatChannel",params:[{type:"Long",name:"categoryId"}]},qchatUpdateChannelCategory:{sid:24,cid:111,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateChannelCategoryTag",reflectMapper:sd.QChatChannelCategoryInfo}],response:[{type:"Property",name:"QChatChannelCategoryInfo",reflectMapper:t.QChatChannelCategoryInfo}]},qchatGetChannelCategoriesByID:{sid:24,cid:112,service:"qchatChannel",params:[{type:"LongArray",name:"categoryIds"}],response:[{type:"PropertyArray",name:"channelCategoryList",reflectMapper:t.QChatChannelCategoryInfo}]},qchatUpdateChannelCategoryWhiteBlackRole:{sid:24,cid:113,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateChannelCategoryWhiteBlackRoleTag",reflectMapper:sd.qchatUpdateChannelCategoryWhiteBlackRoleTag}]},qchatGetChannelCategoryWhiteBlackRolesPage:{sid:24,cid:114,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackRolesPageTag",reflectMapper:sd.qchatGetChannelCategoryWhiteBlackRolesPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverRole}]},qchatUpdateChannelCategoryWhiteBlackMembers:{sid:24,cid:115,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateChannelCategoryWhiteBlackMembersTag",reflectMapper:sd.qchatUpdateChannelCategoryWhiteBlackMembersTag}]},qchatGetChannelCategoryWhiteBlackMembersPage:{sid:24,cid:116,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackMembersPageTag",reflectMapper:sd.qchatGetChannelCategoryWhiteBlackMembersPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.memberInfo}]},qchatGetChannelCategoryWhiteBlackRoles:{sid:24,cid:117,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackRolesTag",reflectMapper:sd.qchatGetChannelCategoryWhiteBlackRolesTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:t.serverRole}]},qchatGetChannelCategoryWhiteBlackMembers:{sid:24,cid:118,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackMembersTag",reflectMapper:sd.qchatGetChannelCategoryWhiteBlackMembersTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:t.memberInfo}]},qchatGetChannelCategoriesPage:{sid:24,cid:119,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoriesPageTag",reflectMapper:sd.qchatGetChannelCategoriesPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.QChatChannelCategoryInfo}]},qchatGetChannelCategoryChannelsPage:{sid:24,cid:120,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryChannelsPageTag",reflectMapper:sd.qchatGetChannelCategoryChannelsPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.channelInfo}]},qchatSubscribe:{sid:24,cid:15,service:"qchatChannel",params:[{type:"Property",name:"qchatSubReqTag",reflectMapper:sd.qchatSubReqTag},{type:"PropertyArray",name:"channels",reflectMapper:sd.qchatChannelIdInfoTag}],response:[{type:"PropertyArray",name:"unreadInfos",reflectMapper:t.unreadInfo},{type:"PropertyArray",name:"failedChannels",reflectMapper:t.qchatChannelIdInfoTag}]},qchatAutoSubscribe:{sid:25,cid:12,service:"qchatChannel",hasPacketTimer:!1,params:[{type:"Property",name:"qchatAutoSubReqTag"}],response:[{type:"Property",name:"qchatAutoSubInfo",reflectMapper:{1:"time"}}]},qchatAutoSubscribeNotification:{service:"qchatChannel",sid:25,cid:13,response:[{type:"PropertyArray",name:"serverIds",reflectMapper:t.unreadInfo},{type:"PropertyArray",name:"unreadInfos",reflectMapper:t.unreadInfo}]},qchatGetUnreadInfo:{sid:24,cid:27,service:"qchatChannel",params:[{type:"PropertyArray",name:"channels",reflectMapper:sd.qchatChannelIdInfoTag}],response:[{type:"PropertyArray",name:"unreadInfos",reflectMapper:t.unreadInfo}]},qchatGetChannelSearchByPage:{sid:24,cid:93,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelSearchByPageTag",reflectMapper:sd.qchatGetChannelSearchByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag",3:"cursor"}},{type:"PropertyArray",name:"datas",reflectMapper:t.channelInfo}]},qchatGetRoleIdsByServerId:{sid:25,cid:8,service:"qchatChannel",params:[{type:"Property",name:"qchatGetRoleIdsByServerIdTag",reflectMapper:{serverIdTimeTags:1}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:t.serverRoles},{type:"String",name:"failServerIds"}]},qchatChannelMemberSearch:{sid:24,cid:95,service:"qchatChannel",params:[{type:"Property",name:"qchatChannelMemberSearchTag",reflectMapper:sd.qchatChannelMemberSearchTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:t.qchatChannelMemberInfo}]},qchatSubscribeAsVisitor:{sid:25,cid:9,service:"qchatChannel",params:[{type:"Property",name:"tag",reflectMapper:sd.qchatSubReqTag},{type:"PropertyArray",name:"datas",reflectMapper:sd.qchatChannelIdInfoTag}],response:[{type:"PropertyArray",name:"failedArr",reflectMapper:t.qchatChannelIdInfoTag}]}}},ud={"24_31":"qchatCreateServer","24_32":"qchatDeleteServer","24_33":"qchatUpdateServer","24_34":"qchatGetServers","24_35":"qchatGetServersByPage","24_36":"qchatInviteServerMembers","24_37":"qchatAcceptServerInvite","24_38":"qchatRejectInviteServer","24_39":"qchatApplyServerJoin","24_40":"qchatAcceptServerApply","24_41":"qchatRejectServerApply","24_42":"qchatKickServerMembers","24_43":"qchatLeaveServer","24_44":"qchatUpdateMyMemberInfo","24_45":"qchatUpdateServerMemberInfo","24_46":"qchatGetServerMembers","24_47":"qchatGetServerMembersByPage","24_104":"qchatUpdateServerMemberBan","24_105":"qchatGetBannedMembersByPage","24_91":"qchatServerSearchByPage","24_92":"qchatServerMemberSearchByPage","24_122":"qchatGenerateInviteCode","24_123":"qchatJoinByInviteCode","24_124":"qchatGetInviteApplyRecordOfServer","24_125":"qchatGetInviteApplyRecordOfSelf","25_5":"qchatClearServersUnread","25_7":"qchatSubscribeChannelsByServers","25_10":"qchatEnterAsVisitor","25_11":"qchatLeaveAsVisitor"},dd={serverInfo:{serverId:1,name:3,icon:4,ext:5,owner:6,memberNumber:7,inviteMode:8,applyMode:9,validFlag:10,createTime:11,updateTime:12,channelNumber:13,categoryNumber:14,searchType:15,searchEnable:16,reorderWeight:17},antispamTag:{antiSpamBusinessId:1},memberInfo:{serverId:1,accid:3,nick:4,avatar:5,ext:6,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},otherMemberInfo:{serverId:1,accid:3,nick:4,avatar:5,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},getServerListPageTag:{timestamp:1,limit:2},getServerMemberListPageTag:{serverId:1,timetag:2,limit:3},updateServerMemberBanTag:{serverId:1,opeType:2,accid:3,ext:4},getBannedMembersByPageTag:{serverId:1,timetag:2,limit:3},serverMemberBanInfo:{serverId:1,appid:2,accid:3,ext:4,banTime:5,validFlag:6,createTime:7,updateTime:8},serverSearchByPageTag:{keyword:1,startTime:2,endTime:3,order:4,limit:5,serverType:6,searchType:7,sort:8,cursor:9},serverMemberSearchByPageTag:{serverId:1,keyword:3,limit:4},inviteRespParamTag:{requestId:1},applyRespParamTag:{requestId:1,expireTime:2},inviteApplyRecord:{accid:1,type:2,serverId:3,status:4,requestId:5,createTime:6,updateTime:7,expireTime:8,data:9,recordId:10},clearServersUnreadTag:{successServerIds:1,failServerIds:2,ackTimestamp:3},unreadInfo:id},pd=function getDeserializeTag(){return invertSerializeMap(dd)},hd=function getCmdConfig(){var t=pd();return{qchatCreateServer:{sid:24,cid:31,service:"qchatServer",params:[{type:"Property",name:"serverInfo",reflectMapper:dd.serverInfo},{type:"Property",name:"antispamTag",reflectMapper:dd.antispamTag}],response:[{type:"Property",name:"serverInfo",reflectMapper:t.serverInfo}]},qchatDeleteServer:{sid:24,cid:32,service:"qchatServer",params:[{type:"Long",name:"serverId"}]},qchatUpdateServer:{sid:24,cid:33,service:"qchatServer",params:[{type:"Property",name:"serverInfo",reflectMapper:dd.serverInfo},{type:"Property",name:"antispamTag",reflectMapper:dd.antispamTag}],response:[{type:"Property",name:"serverInfo",reflectMapper:t.serverInfo}]},qchatGetServers:{sid:24,cid:34,service:"qchatServer",params:[{type:"LongArray",name:"serverIds"}],response:[{type:"PropertyArray",name:"serverList",reflectMapper:t.serverInfo}]},qchatGetServersByPage:{sid:24,cid:35,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:dd.getServerListPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverInfo}]},qchatInviteServerMembers:{sid:24,cid:36,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"StrArray",name:"accids"},{type:"String",name:"ps"},{type:"Property",name:"params",reflectMapper:{ttl:1}}],response:[{type:"StrArray",name:"failByOverAccids"},{type:"StrArray",name:"failByBanAccids"},{type:"Property",name:"record",reflectMapper:t.applyRespParamTag}]},qchatAcceptServerInvite:{sid:24,cid:37,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"Property",name:"recordInfo",reflectMapper:dd.inviteRespParamTag}]},qchatRejectInviteServer:{sid:24,cid:38,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"String",name:"ps"},{type:"Property",name:"recordInfo",reflectMapper:dd.inviteRespParamTag}]},qchatApplyServerJoin:{sid:24,cid:39,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"ps"},{type:"Property",name:"params",reflectMapper:{ttl:1}}],response:[{type:"Property",name:"data",reflectMapper:t.applyRespParamTag}]},qchatAcceptServerApply:{sid:24,cid:40,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"Property",name:"recordInfo",reflectMapper:dd.applyRespParamTag}]},qchatRejectServerApply:{sid:24,cid:41,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"String",name:"ps"},{type:"Property",name:"recordInfo",reflectMapper:dd.applyRespParamTag}]},qchatKickServerMembers:{sid:24,cid:42,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"StrArray",name:"accids"}]},qchatLeaveServer:{sid:24,cid:43,service:"qchatServer",params:[{type:"Long",name:"serverId"}]},qchatUpdateMyMemberInfo:{sid:24,cid:44,service:"qchatServer",params:[{type:"Property",name:"memberInfo",reflectMapper:dd.memberInfo},{type:"Property",name:"antispamTag",reflectMapper:dd.antispamTag}],response:[{type:"Property",name:"memberInfo",reflectMapper:t.memberInfo}]},qchatUpdateServerMemberInfo:{sid:24,cid:45,service:"qchatServer",params:[{type:"Property",name:"memberInfo",reflectMapper:dd.otherMemberInfo},{type:"Property",name:"antispamTag",reflectMapper:dd.antispamTag}],response:[{type:"Property",name:"memberInfo",reflectMapper:t.memberInfo}]},qchatGetServerMembers:{sid:24,cid:46,service:"qchatServer",params:[{type:"StrArray",name:"accids"}],response:[{type:"PropertyArray",name:"accidList",reflectMapper:t.memberInfo}]},qchatGetServerMembersByPage:{sid:24,cid:47,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:dd.getServerMemberListPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.memberInfo}]},qchatUpdateServerMemberBan:{sid:24,cid:104,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:dd.updateServerMemberBanTag}]},qchatGetBannedMembersByPage:{sid:24,cid:105,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:dd.getBannedMembersByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverMemberBanInfo}]},qchatServerSearchByPage:{sid:24,cid:91,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:dd.serverSearchByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag",3:"cursor"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverInfo}]},qchatServerMemberSearchByPage:{sid:24,cid:92,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:dd.serverMemberSearchByPageTag}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.memberInfo}]},qchatGenerateInviteCode:{sid:24,cid:122,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,ttl:2}}],response:[{type:"Property",name:"data",reflectMapper:{1:"serverId",2:"requestId",3:"inviteCode",4:"expireTime"}}]},qchatJoinByInviteCode:{sid:24,cid:123,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,inviteCode:2,ps:3}}]},qchatGetInviteApplyRecordOfServer:{sid:24,cid:124,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,fromTime:2,toTime:3,reverse:4,limit:5,cursor:6}}],response:[{type:"PropertyArray",name:"data",reflectMapper:t.inviteApplyRecord}]},qchatGetInviteApplyRecordOfSelf:{sid:24,cid:125,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{fromTime:1,toTime:2,reverse:3,limit:4,cursor:5}}],response:[{type:"PropertyArray",name:"data",reflectMapper:t.inviteApplyRecord}]},qchatClearServersUnread:{sid:25,cid:5,service:"qchatServer",params:[{type:"LongArray",name:"serverIds"}],response:[{type:"Property",name:"clearServersUnreadTag",reflectMapper:t.clearServersUnreadTag}]},qchatSubscribeChannelsByServers:{sid:25,cid:7,service:"qchatServer",params:[{type:"Int",name:"type"},{type:"LongArray",name:"serverIds"}],response:[{type:"PropertyArray",name:"unreadInfos",reflectMapper:t.unreadInfo},{type:"String",name:"failServerIds"}]},qchatEnterAsVisitor:{sid:25,cid:10,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverIds:1}}],response:[{type:"String",name:"failServerIds"}]},qchatLeaveAsVisitor:{sid:25,cid:11,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverIds:1}}],response:[{type:"String",name:"failServerIds"}]}}};function format(t,a){if(!isPlainObject(a))return{};var u=JSON.parse(gs(a)),h=doFormat(t,u);return JSON.parse(gs(Ot(Ot({},u),h)))}function doFormat(t,a){if(!isPlainObject(a))return{};var u={},h=Qi(t);return forEach$1(h).call(h,(function(h){var m=t[h].type;if("string"!=typeof m){var g=doFormat(t[h],a);Qi(g).length>0&&(u[h]=g)}else{var E=t[h],I=E.rawKey||h,M=md[m](a,I,E);void 0!==M&&(a[I]=void 0,u[h]=M)}})),u}var md={number:function number(t,a){if(void 0!==t[a])return+t[a]},string:function string(t,a){if(void 0!==t[a])return t[a]},boolean:function boolean(t,a){return+t[a]>0||0!=+t[a]&&void 0},enum:function _enum(t,a,u){return values(u)[t[a]]},object:function object(t,a){if(void 0!==t[a])try{return JSON.parse(t[a])}catch(t){return{}}}};function formatReverse(t,a){if(!isPlainObject(a))return{};var u=JSON.parse(gs(a)),h=doFormatReverse(t,u);return JSON.parse(gs(Ot(Ot({},u),h)))}function doFormatReverse(t,a){var u;if(!isPlainObject(a))return reduce(u=Qi(t)).call(u,(function(a,u){return a[t[u].rawKey||u]=void 0,a}),{});var h={},m=Qi(t);return forEach$1(m).call(m,(function(u){var m=t[u].type;if("string"!=typeof m){var g=doFormatReverse(t[u],a[u]);return Ot(h,g),void(a[u]=void 0)}var E=t[u],I=E.rawKey||u,M=fd[m](a,u,E);a[I]=void 0,h[I]=M})),h}var fd={number:function number(t,a){return t[a]},string:function string(t,a){return t[a]},boolean:function boolean(t,a){return!0===t[a]?1:!1===t[a]?0:void 0},enum:function _enum(t,a,u){return values(u)[t[a]]},object:function object(t,a){if(void 0!==t[a])try{return gs(t[a])}catch(t){return""}}},gd=fd,vd={serverId:{type:"string"},name:{type:"string"},icon:{type:"string"},ext:{type:"string"},owner:{type:"string"},memberNumber:{type:"number"},inviteMode:{type:"number"},applyMode:{type:"number"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},channelNumber:{type:"number"},categoryNumber:{type:"number"},searchType:{type:"number"},searchEnable:{type:"boolean"},reorderWeight:{type:"string"}},_d={serverId:{type:"string"},uid:{type:"string"},accid:{type:"string"},nick:{type:"string"},avatar:{type:"string"},ext:{type:"string"},type:{type:"number"},joinTime:{type:"number"},inviter:{type:"string"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"}},yd={serverId:{type:"string"},appid:{type:"string"},accid:{type:"string"},ext:{type:"string"},banTime:{type:"number"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"}};function formatServer(t){return format(vd,t)}function formatServers(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return formatServer(t)})):[]}function formatMember$1(t){return format(_d,t)}function formatMembers$1(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return formatMember$1(t)})):[]}function formatServerMemberBanInfos(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return function formatServerMemberBanInfo(t){return format(yd,t)}(t)})):[]}function generateAntispamTag(t){var a,u,h;if(!t.antispamTag)return{};var m=Ot({},t.antispamTag);return(null===(a=t.antispamTag)||void 0===a?void 0:a.antiSpamBusinessId)&&"string"!=typeof(null===(u=t.antispamTag)||void 0===u?void 0:u.antiSpamBusinessId)&&(m.antiSpamBusinessId=gs(null===(h=t.antispamTag)||void 0===h?void 0:h.antiSpamBusinessId)),m}var Ed={accid:{type:"string"},type:{type:"number"},serverId:{type:"string"},status:{type:"number"},requestId:{type:"string"},createTime:{type:"number"},updateTime:{type:"number"},expireTime:{type:"number"},data:{type:"object"}};function formatInviteApplyRecord(t){return format(Ed,t)}function formatInviteApplyRecords(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return formatInviteApplyRecord(t)})):[]}var Id,Md,Sd,Cd,Td={successServerIds:{type:"object"},failServerIds:{type:"object"},ackTimestamp:{type:"number"}};function formatClearServersUnread(t){return format(Td,t)}!function(t){t[t.reorderWeight=0]="reorderWeight",t[t.createTime=1]="createTime"}(Id||(Id={})),function(t){t[t.white=1]="white",t[t.black=2]="black"}(Md||(Md={})),function(t){t[t.add=1]="add",t[t.remove=2]="remove"}(Sd||(Sd={})),function(t){t[t.message=0]="message",t[t.media=1]="media",t[t.ext=100]="ext"}(Cd||(Cd={}));var bd={channelId:{type:"string"},serverId:{type:"string"},name:{type:"string"},topic:{type:"string"},ext:{type:"string"},type:{type:"enum",values:Cd},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},viewMode:{type:"number"},categoryId:{type:"string"},syncMode:{type:"number"},visitorMode:{type:"number"},reorderWeight:{type:"string"}},Rd={serverId:{type:"string"},channelId:{type:"string"},ackTimestamp:{type:"number"},unreadCount:{type:"number"},mentionedCount:{type:"number"},maxCount:{type:"number"},lastMsgTime:{type:"number"}},Ad={serverId:{type:"string"},channelId:{type:"string"},type:{type:"enum",values:Md},opeType:{type:"enum",values:Sd},toAccids:{type:"object"}},Nd={serverId:{type:"string"},channelId:{type:"string"},type:{type:"enum",values:Md},opeType:{type:"enum",values:Sd},roleId:{type:"string"}},wd={categoryId:{type:"string"},serverId:{type:"string"},name:{type:"string"},ext:{type:"string"},owner:{type:"string"},viewMode:{type:"number"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},channelNumber:{type:"number"}},Od={serverId:{type:"string"},channelId:{type:"string"},accid:{type:"string"},avatar:{type:"string"},nick:{type:"string"},createTime:{type:"number"},updateTime:{type:"number"}};function formatUpdateWhiteBlackRole(t){return format(Nd,t)}function formatChannel(t){return format(bd,t)}function formatChannels(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return formatChannel(t)})):[]}function formatUnreadInfo(t){return format(Rd,t)}function formatUnreadInfos(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return formatUnreadInfo(t)})):[]}function formatChannelCategory(t){return format(wd,t)}function formatChannelCategorys(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return formatChannelCategory(t)})):[]}function formatChannelMembers(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return function formatChannelMember(t){return format(Od,t)}(t)})):[]}function formatFailServerIds(t){try{return JSON.parse(t)}catch(t){return[]}}function _createForOfIteratorHelperLoose$3(t,a){var u,h=void 0!==fi&&vu(t)||t["@@iterator"];if(h)return bind$1(u=(h=h.call(t)).next).call(u,h);if(zl(t)||(h=function _unsupportedIterableToArray$3(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$3(t,a);var h=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===h&&t.constructor&&(h=t.constructor.name),"Map"===h||"Set"===h?gu(t):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray$3(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){h&&(t=h);var m=0;return function(){return m>=t.length?{done:!0}:{done:!1,value:t[m++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$3(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,h=Array(a);u<a;u++)h[u]=t[u];return h}var xd,kd,Pd,Ld,qd=function(t){function QChatServerService(a){var u;return(u=t.call(this,"qchatServer",a)||this).core=a,registerParser({cmdMap:ud,cmdConfig:hd()}),u}Tt(QChatServerService,t);var a=QChatServerService.prototype;return a.createServer=function createServer(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a;return cs.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return validate({icon:{type:"string",required:!1},name:{type:"string",required:!1},ext:{type:"string",required:!1},searchType:{type:"number",required:!1,min:0},searchEnable:{type:"boolean",required:!1},inviteMode:{type:"number",min:0,max:1,required:!1},applyMode:{type:"number",min:0,max:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatCreateServer",{serverInfo:Ot(Ot({},t),{searchEnable:!1===t.searchEnable?0:1}),antispamTag:generateAntispamTag(t)});case 3:return a=u.sent,u.abrupt("return",formatServer(a.content.serverInfo));case 5:case"end":return u.stop()}}),_callee,this)})))},a.deleteServer=function deleteServer(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){return cs.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1}},t),a.next=3,this.core.sendCmd("qchatDeleteServer",t);case 3:case"end":return a.stop()}}),_callee2,this)})))},a.updateServer=function updateServer(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var a;return cs.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",min:1},icon:{type:"string",required:!1},name:{type:"string",required:!1},ext:{type:"string",required:!1},searchType:{type:"number",required:!1,min:0},searchEnable:{type:"boolean",required:!1},inviteMode:{type:"number",min:0,max:1,required:!1},applyMode:{type:"number",min:0,max:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateServer",{serverInfo:Ot(Ot({},t),{searchEnable:!1===t.searchEnable?0:1}),antispamTag:generateAntispamTag(t)});case 3:return a=u.sent,u.abrupt("return",formatServer(a.content.serverInfo));case 5:case"end":return u.stop()}}),_callee3,this)})))},a.getServers=function getServers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a;return cs.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverIds:{type:"array",itemType:"string",min:1}},t),u.next=3,this.core.sendCmd("qchatGetServers",t);case 3:return a=u.sent,u.abrupt("return",formatServers(a.content.serverList));case 5:case"end":return u.stop()}}),_callee4,this)})))},a.getServersByPage=function getServersByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var a,u,h,m;return cs.wrap((function _callee5$(g){for(;;)switch(g.prev=g.next){case 0:return validate({timestamp:{type:"number"},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetServersByPage",{tag:t});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatServers(h)});case 6:case"end":return g.stop()}}),_callee5,this)})))},a.inviteServerMembers=function inviteServerMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){var a,u;return cs.wrap((function _callee6$(h){for(;;)switch(h.prev=h.next){case 0:return validate({serverId:{type:"string",min:1},accids:{type:"array",itemType:"string",min:1},ps:{type:"string"},params:{type:"object",rules:{ttl:{type:"number"}},required:!1}},t),h.next=3,this.core.sendCmd("qchatInviteServerMembers",t);case 3:return a=h.sent,u=a.content.record||{},h.abrupt("return",{failByOverAccids:a.content.failByOverAccids,failByBanAccids:a.content.failByBanAccids,recordInfo:formatInviteApplyRecord(u)});case 6:case"end":return h.stop()}}),_callee6,this)})))},a.acceptServerInvite=function acceptServerInvite(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){return cs.wrap((function _callee7$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},t),a.next=3,this.core.sendCmd("qchatAcceptServerInvite",t);case 3:case"end":return a.stop()}}),_callee7,this)})))},a.rejectInviteServer=function rejectInviteServer(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){return cs.wrap((function _callee8$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},ps:{type:"string"},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},t),a.next=3,this.core.sendCmd("qchatRejectInviteServer",t);case 3:case"end":return a.stop()}}),_callee8,this)})))},a.applyServerJoin=function applyServerJoin(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee9(){var a;return cs.wrap((function _callee9$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",min:1},ps:{type:"string"},params:{type:"object",rules:{ttl:{type:"number"}},required:!1}},t),u.next=3,this.core.sendCmd("qchatApplyServerJoin",t);case 3:return a=u.sent,u.abrupt("return",formatInviteApplyRecord(a.content.data));case 5:case"end":return u.stop()}}),_callee9,this)})))},a.acceptServerApply=function acceptServerApply(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee10(){return cs.wrap((function _callee10$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},t),a.next=3,this.core.sendCmd("qchatAcceptServerApply",t);case 3:case"end":return a.stop()}}),_callee10,this)})))},a.rejectServerApply=function rejectServerApply(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee11(){return cs.wrap((function _callee11$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},ps:{type:"string"},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},t),a.next=3,this.core.sendCmd("qchatRejectServerApply",t);case 3:case"end":return a.stop()}}),_callee11,this)})))},a.kickServerMembers=function kickServerMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee12(){return cs.wrap((function _callee12$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1},accids:{type:"array",itemType:"string",min:1}},t),a.next=3,this.core.sendCmd("qchatKickServerMembers",t);case 3:case"end":return a.stop()}}),_callee12,this)})))},a.leaveServer=function leaveServer(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee13(){return cs.wrap((function _callee13$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1}},t),a.next=3,this.core.sendCmd("qchatLeaveServer",t);case 3:case"end":return a.stop()}}),_callee13,this)})))},a.updateMyMemberInfo=function updateMyMemberInfo(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee14(){var a;return cs.wrap((function _callee14$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",min:1},accid:{type:"string",required:!1},nick:{type:"string",allowEmpty:!0,required:!1},avatar:{type:"string",required:!1},ext:{type:"string",required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateMyMemberInfo",{memberInfo:t,antispamTag:generateAntispamTag(t)});case 3:return a=u.sent,u.abrupt("return",formatMember$1(a.content.memberInfo));case 5:case"end":return u.stop()}}),_callee14,this)})))},a.updateServerMemberInfo=function updateServerMemberInfo(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee15(){var a;return cs.wrap((function _callee15$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},nick:{type:"string",required:!1},avatar:{type:"string",required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateServerMemberInfo",{memberInfo:t,antispamTag:generateAntispamTag(t)});case 3:return a=u.sent,u.abrupt("return",formatMember$1(a.content.memberInfo));case 5:case"end":return u.stop()}}),_callee15,this)})))},a.getServerMembers=function getServerMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee16(){var a,u,h,m,g;return cs.wrap((function _callee16$(E){for(;;)switch(E.prev=E.next){case 0:if(validate({accids:{type:"array"}},t),a=[],t.accids.length)for(u=_createForOfIteratorHelperLoose$3(t.accids);!(h=u()).done;)m=h.value,a.push(m.serverId+"|"+m.accid);return E.next=5,this.core.sendCmd("qchatGetServerMembers",{accids:a});case 5:return g=E.sent,E.abrupt("return",formatMembers$1(g.content.accidList));case 7:case"end":return E.stop()}}),_callee16,this)})))},a.getServerMembersByPage=function getServerMembersByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee17(){var a,u,h,m;return cs.wrap((function _callee17$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",min:1},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetServerMembersByPage",{tag:t});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatMembers$1(h)});case 6:case"end":return g.stop()}}),_callee17,this)})))},a.subscribeServer=function subscribeServer(t){var a,u,h,m;return __awaiter(this,void 0,void 0,cs.mark((function _callee18(){var g,E,I,M;return cs.wrap((function _callee18$(S){for(;;)switch(S.prev=S.next){case 0:if(validate({type:{type:"number",min:4,max:4,required:!1},opeType:{type:"number",min:1,max:2}},t),!(null===(u=null===(a=this.core.qchatChannel)||void 0===a?void 0:a.config)||void 0===u?void 0:u.autoSubscribe)||t.isInternalTrigger){S.next=3;break}throw new Gl("subscribe server failed, manual subscribe is not allowed in auto subscribe mode",{},403);case 3:if(t.type||(t.type=4),!t.servers.length){S.next=12;break}return E=Ot(Ot({},t),{channels:map$6(g=t.servers).call(g,(function(t){return{serverId:t.serverId,channelId:""}}))}),S.next=8,this.core.qchatChannel.subscribeModuleService.subscribe(E);case 8:return I=S.sent,M=I.failedChannels,(null===(m=null===(h=this.core.qchatChannel)||void 0===h?void 0:h.config)||void 0===m?void 0:m.autoSubscribe)?this.core.qchatChannel.subscribeModuleService.cacheAutoSubscribe(E):this.core.qchatChannel.subscribeModuleService.cacheSubscribe(E),S.abrupt("return",{failedServers:M});case 12:case"end":return S.stop()}}),_callee18,this)})))},a.banServerMember=function banServerMember(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee19(){return cs.wrap((function _callee19$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},ext:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("qchatUpdateServerMemberBan",{tag:Ot(Ot({},t),{opeType:1})});case 3:case"end":return a.stop()}}),_callee19,this)})))},a.unbanServerMember=function unbanServerMember(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee20(){return cs.wrap((function _callee20$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},ext:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("qchatUpdateServerMemberBan",{tag:Ot(Ot({},t),{opeType:2})});case 3:case"end":return a.stop()}}),_callee20,this)})))},a.getBannedMembersByPage=function getBannedMembersByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee21(){var a,u,h,m;return cs.wrap((function _callee21$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetBannedMembersByPage",{tag:t});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatServerMemberBanInfos(h)});case 6:case"end":return g.stop()}}),_callee21,this)})))},a.serverSearchByPage=function serverSearchByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee22(){var a,u,h,m;return cs.wrap((function _callee22$(g){for(;;)switch(g.prev=g.next){case 0:if(validate({keyword:{type:"string",allowEmpty:!1},startTime:{type:"number",min:0,required:!1},endTime:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1},serverType:{type:"array",itemType:"number",min:0,required:!1},order:{type:"enum",values:getEnumKeys(Zu),required:!1},searchType:{type:"enum",values:getEnumKeys(td)},sort:{type:"enum",values:getEnumKeys(ed),required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},t),!(t.startTime&&t.endTime&&t.startTime>=t.endTime)){g.next=3;break}throw new ql("startTime more than endTime",t,"timeRule");case 3:return g.next=5,this.core.sendCmd("qchatServerSearchByPage",{tag:Ot(Ot({},t),{order:t.order&&Zu[t.order],searchType:td[t.searchType],sort:sort(t)&&ed[sort(t)],serverType:gs(t.serverType)})});case 5:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag),cursor:m.cursor},datas:formatServers(h)});case 8:case"end":return g.stop()}}),_callee22,this)})))},a.serverMemberSearchByPage=function serverMemberSearchByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee23(){var a;return cs.wrap((function _callee23$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},keyword:{type:"string",allowEmpty:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatServerMemberSearchByPage",{tag:t});case 3:return a=u.sent,u.abrupt("return",formatMembers$1(a.content.members));case 5:case"end":return u.stop()}}),_callee23,this)})))},a.generateInviteCode=function generateInviteCode(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee24(){var a;return cs.wrap((function _callee24$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},ttl:{type:"number",min:0,required:!1}},t),u.next=3,this.core.sendCmd("qchatGenerateInviteCode",{tag:t});case 3:return a=u.sent,u.abrupt("return",format({expireTime:{type:"number"}},a.content.data));case 5:case"end":return u.stop()}}),_callee24,this)})))},a.joinByInviteCode=function joinByInviteCode(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee25(){return cs.wrap((function _callee25$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},inviteCode:{type:"string",allowEmpty:!1},ps:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("qchatJoinByInviteCode",{tag:t});case 3:case"end":return a.stop()}}),_callee25,this)})))},a.getInviteApplyRecordOfServer=function getInviteApplyRecordOfServer(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee26(){var a;return cs.wrap((function _callee26$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},fromTime:{type:"number",min:0},toTime:{type:"number",min:0},reverse:{type:"boolean",required:!1},limit:{type:"number",min:1,required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetInviteApplyRecordOfServer",{tag:Ot(Ot({},t),{reverse:reverse(t)?1:0})});case 3:return a=u.sent,u.abrupt("return",formatInviteApplyRecords(a.content.data));case 5:case"end":return u.stop()}}),_callee26,this)})))},a.getInviteApplyRecordOfSelf=function getInviteApplyRecordOfSelf(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee27(){var a;return cs.wrap((function _callee27$(u){for(;;)switch(u.prev=u.next){case 0:return validate({fromTime:{type:"number",min:0},toTime:{type:"number",min:0},reverse:{type:"boolean",required:!1},limit:{type:"number",min:1,required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetInviteApplyRecordOfSelf",{tag:Ot(Ot({},t),{reverse:reverse(t)?1:0})});case 3:return a=u.sent,u.abrupt("return",formatInviteApplyRecords(a.content.data));case 5:case"end":return u.stop()}}),_callee27,this)})))},a.markRead=function markRead(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee28(){var a,u,h,m;return cs.wrap((function _callee28$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverIds:{type:"array",itemType:"string",required:!0}},t),g.next=3,this.core.sendCmd("qchatClearServersUnread",t);case 3:return a=g.sent,u=formatClearServersUnread(a.content.clearServersUnreadTag),h=u.successServerIds,m=u.ackTimestamp,this.logger.log("qchatServer::clearServersUnread:: begin auto clear servers unreadInfo"),this.core.eventBus.emit("qchatChannel/clearUnreadCountByServers",h,m),g.abrupt("return",u);case 9:case"end":return g.stop()}}),_callee28,this)})))},a.subscribeAllChannel=function subscribeAllChannel(t){var a,u;return __awaiter(this,void 0,void 0,cs.mark((function _callee29(){var h,m,g;return cs.wrap((function _callee29$(E){for(;;)switch(E.prev=E.next){case 0:if(validate({type:{type:"number",required:!0},serverIds:{type:"array",itemType:"string",required:!0,max:10}},t),!(null===(u=null===(a=this.core.qchatChannel)||void 0===a?void 0:a.config)||void 0===u?void 0:u.autoSubscribe)){E.next=3;break}throw new Gl("subscribe channel failed, manual subscribe is not allowed in auto subscribe mode.",{},403);case 3:return E.next=5,this.core.sendCmd("qchatSubscribeChannelsByServers",t,{timeout:3e4});case 5:return(m=E.sent).content.unreadInfos=formatUnreadInfos(m.content.unreadInfos),m.content.failServerIds=formatFailServerIds(m.content.failServerIds),g={type:t.type,opeType:1,channels:map$6(h=m.content.unreadInfos).call(h,(function(t){return{serverId:t.serverId,channelId:t.channelId}}))},this.core.eventBus.emit("qchatChannel/cacheSubscribe",g),this.core.eventBus.emit("qchatChannel/getRoleIdsByServerId",t.serverIds),this.core.eventBus.emit("qchatChannel/updateUnreads",m.content.unreadInfos),E.abrupt("return",m.content);case 13:case"end":return E.stop()}}),_callee29,this)})))},a.enterAsVisitor=function enterAsVisitor(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee30(){var a,u,h;return cs.wrap((function _callee30$(m){for(;;)switch(m.prev=m.next){case 0:return validate({serverIds:{type:"array",itemType:"string",min:1}},t),m.next=3,this.core.sendCmd("qchatEnterAsVisitor",{tag:{serverIds:gs(t.serverIds)}});case 3:return a=m.sent,u=formatFailServerIds(a.content.failServerIds),h=difference(t.serverIds,u),this.core.qchatChannel.subscribeForVisitorService.cacheServer(h),m.abrupt("return",{failedServers:u});case 8:case"end":return m.stop()}}),_callee30,this)})))},a.leaveAsVisitor=function leaveAsVisitor(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee31(){var a,u,h,m=this;return cs.wrap((function _callee31$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverIds:{type:"array",itemType:"string",min:1}},t),g.next=3,this.core.sendCmd("qchatLeaveAsVisitor",{tag:{serverIds:gs(t.serverIds)}});case 3:return a=g.sent,u=formatFailServerIds(a.content.failServerIds),h=difference(t.serverIds,u),forEach$1(h).call(h,(function(t){m.core.qchatChannel.subscribeForVisitorService.deleteServer(t)})),g.abrupt("return",{failedServers:u});case 8:case"end":return g.stop()}}),_callee31,this)})))},a.subscribeAsVisitor=function subscribeAsVisitor(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee32(){return cs.wrap((function _callee32$(a){for(;;)switch(a.prev=a.next){case 0:if(validate({opeType:{type:"number",min:1,max:2},type:{type:"number",required:!1},serverIds:{type:"array",itemType:"string",min:1}},t),!t.serverIds.length){a.next=5;break}return a.next=4,this.core.qchatChannel.subscribeForVisitorService.subscribeServerAsVisitor(t);case 4:return a.abrupt("return",a.sent);case 5:return a.abrupt("return",{failedServers:[]});case 6:case"end":return a.stop()}}),_callee32,this)})))},QChatServerService}(ad);!function(t){t[t.everyone=1]="everyone",t[t.custom=2]="custom"}(xd||(xd={})),function(t){t[t.normal=0]="normal",t[t.owner=1]="owner"}(kd||(kd={})),function(t){t[t.ignore=0]="ignore",t[t.deny=-1]="deny",t[t.allow=1]="allow"}(Pd||(Pd={})),function(t){t[t.manageServer=1]="manageServer",t[t.manageChannel=2]="manageChannel",t[t.manageRole=3]="manageRole",t[t.sendMsg=4]="sendMsg",t[t.accountInfoSelf=5]="accountInfoSelf",t[t.inviteServer=6]="inviteServer",t[t.kickServer=7]="kickServer",t[t.accountInfoOther=8]="accountInfoOther",t[t.recallMsg=9]="recallMsg",t[t.deleteMsg=10]="deleteMsg",t[t.remindOther=11]="remindOther",t[t.remindEveryone=12]="remindEveryone",t[t.manageBlackWhiteList=13]="manageBlackWhiteList",t[t.banServerMember=14]="banServerMember",t[t.RTCChannelConnect=15]="RTCChannelConnect",t[t.RTCChannelDisconnectOther=16]="RTCChannelDisconnectOther",t[t.RTCChannelOpenMicrophone=17]="RTCChannelOpenMicrophone",t[t.RTCChannelOpenCamera=18]="RTCChannelOpenCamera",t[t.RTCChannelOpenCloseOtherMicrophone=19]="RTCChannelOpenCloseOtherMicrophone",t[t.RTCChannelOpenCloseOtherCamera=20]="RTCChannelOpenCloseOtherCamera",t[t.RTCChannelOpenCloseEveryoneMicrophone=21]="RTCChannelOpenCloseEveryoneMicrophone",t[t.RTCChannelOpenCloseEveryoneCamera=22]="RTCChannelOpenCloseEveryoneCamera",t[t.RTCChannelOpenShareScreen=23]="RTCChannelOpenShareScreen",t[t.RTCChannelCloseOtherShareScreen=24]="RTCChannelCloseOtherShareScreen",t[t.manageInviteApply=25]="manageInviteApply",t[t.manageInviteApplyHistory=26]="manageInviteApplyHistory",t[t.mentionedRole=27]="mentionedRole"}(Ld||(Ld={}));var Dd={type:{type:"enum",values:xd},memberType:{type:"enum",values:kd},createTime:{type:"number"},updateTime:{type:"number"},priority:{type:"number"},memberCount:{type:"number"},joinTime:{type:"number"}},Vd={roleId:{type:"string"},categoryId:{type:"string"},serverId:{type:"string"},type:{type:"enum",values:xd},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},name:{type:"string"},icon:{type:"string"},ext:{type:"string"}},Ud={id:{type:"string"},accid:{type:"string"},categoryId:{type:"string"},serverId:{type:"string"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},nick:{type:"string"},avatar:{type:"string"},ext:{type:"string"},memberType:{type:"enum",values:kd},joinTime:{type:"number"},inviter:{type:"string"}};function generatorRoleForCmd(t){var a=Ot({},t),u=formatReverse(Dd,a);return u.auths&&(u.auths=function generateRoleAuthsForCmd(t){var a,u=reduce(a=Qi(t)).call(a,(function(a,u){var h=Ld[u];return h?a[h]=Pd[t[u]]:a[u]=Pd[t[u]],a}),{});return gs(u)}(u.auths)),u}function formatRoleAuths(t){var a;return reduce(a=Qi(t)).call(a,(function(a,u){var h=getEnumKeyByEnumValue(Ld,u);h?a[h]=Pd[t[u]]:a[Kl(u)]=Pd[t[u]];return a}),{})}function formatRole(t){var a=format(Dd,t);return t.auths&&(a.auths=formatRoleAuths(JSON.parse(a.auths))),a.isMember&&delete a.isMember,a}function formatRoles(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return formatRole(t)})):[]}function formatChannelCategoryRole(t){return t.auths&&(t.auths=formatRoleAuths(JSON.parse(t.auths))),format(Vd,t)}function formatChannelCategoryMemberRole(t){return t.auths&&(t.auths=formatRoleAuths(JSON.parse(t.auths))),format(Ud,t)}collection("Set",(function(t){return function Set(){return t(this,arguments.length?arguments[0]:void 0)}}),tc);var Gd=W.Set;_export({target:"Set",stat:!0,forced:!0},{from:ac}),_export({target:"Set",stat:!0,forced:!0},{of:oc});_export({target:"Set",proto:!0,real:!0,forced:!0},{addAll:function addAll(){for(var t=anObject(this),a=aCallable(t.add),u=0,h=arguments.length;u<h;u++)O(a,t,arguments[u]);return t}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{deleteAll:ic});var Fd=getIterator;_export({target:"Set",proto:!0,real:!0,forced:!0},{every:function every(t){var a=anObject(this),u=Fd(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return!iterate(u,(function(t,u){if(!h(t,t,a))return u()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{difference:function difference(t){var a=anObject(this),u=new(speciesConstructor(a,getBuiltIn("Set")))(a),h=aCallable(u.delete);return iterate(t,(function(t){O(h,u,t)})),u}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{filter:function filter(t){var a=anObject(this),u=Fd(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0),m=new(speciesConstructor(a,getBuiltIn("Set"))),g=aCallable(m.add);return iterate(u,(function(t){h(t,t,a)&&O(g,m,t)}),{IS_ITERATOR:!0}),m}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{find:function find(t){var a=anObject(this),u=Fd(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u){if(h(t,t,a))return u(t)}),{IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{intersection:function intersection(t){var a=anObject(this),u=new(speciesConstructor(a,getBuiltIn("Set"))),h=aCallable(a.has),m=aCallable(u.add);return iterate(t,(function(t){O(h,a,t)&&O(m,u,t)})),u}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{isDisjointFrom:function isDisjointFrom(t){var a=anObject(this),u=aCallable(a.has);return!iterate(t,(function(t,h){if(!0===O(u,a,t))return h()}),{INTERRUPTED:!0}).stopped}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{isSubsetOf:function isSubsetOf(t){var a=getIterator(this),u=anObject(t),h=u.has;return isCallable(h)||(u=new(getBuiltIn("Set"))(t),h=aCallable(u.has)),!iterate(a,(function(t,a){if(!1===O(h,u,t))return a()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{isSupersetOf:function isSupersetOf(t){var a=anObject(this),u=aCallable(a.has);return!iterate(t,(function(t,h){if(!1===O(u,a,t))return h()}),{INTERRUPTED:!0}).stopped}});var Bd=A([].join),jd=[].push;_export({target:"Set",proto:!0,real:!0,forced:!0},{join:function join(t){var a=anObject(this),u=Fd(a),h=void 0===t?",":toString(t),m=[];return iterate(u,jd,{that:m,IS_ITERATOR:!0}),Bd(m,h)}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{map:function map(t){var a=anObject(this),u=Fd(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0),m=new(speciesConstructor(a,getBuiltIn("Set"))),g=aCallable(m.add);return iterate(u,(function(t){O(g,m,h(t,t,a))}),{IS_ITERATOR:!0}),m}});var Wd=TypeError;_export({target:"Set",proto:!0,real:!0,forced:!0},{reduce:function reduce(t){var a=anObject(this),u=Fd(a),h=arguments.length<2,m=h?void 0:arguments[1];if(aCallable(t),iterate(u,(function(u){h?(h=!1,m=u):m=t(m,u,u,a)}),{IS_ITERATOR:!0}),h)throw Wd("Reduce of empty set with no initial value");return m}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{some:function some(t){var a=anObject(this),u=Fd(a),h=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u){if(h(t,t,a))return u()}),{IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{symmetricDifference:function symmetricDifference(t){var a=anObject(this),u=new(speciesConstructor(a,getBuiltIn("Set")))(a),h=aCallable(u.delete),m=aCallable(u.add);return iterate(t,(function(t){O(h,u,t)||O(m,u,t)})),u}}),_export({target:"Set",proto:!0,real:!0,forced:!0},{union:function union(t){var a=anObject(this),u=new(speciesConstructor(a,getBuiltIn("Set")))(a);return iterate(t,aCallable(u.add),{that:u}),u}});var Yd,$d,Hd,Qd,Kd,zd,Jd,Xd,Zd=Gd;function isObject(t){var a=typeof t;return null!=t&&("object"==a||"function"==a)}!function(t){t[t.default=1]="default",t[t.sync=2]="sync"}(Yd||(Yd={})),function(t){t[t.sendTime=1]="sendTime"}($d||($d={})),function(t){t[t.text=0]="text",t[t.image=1]="image",t[t.audio=2]="audio",t[t.video=3]="video",t[t.geo=4]="geo",t[t.notification=5]="notification",t[t.file=6]="file",t[t.tip=10]="tip",t[t.robot=11]="robot",t[t.g2=12]="g2",t[t.custom=100]="custom"}(Hd||(Hd={})),function(t){t[t.sending=1]="sending",t[t.success=2]="success",t[t.failed=3]="failed"}(Qd||(Qd={})),function(t){t[t.notifyAll=1]="notifyAll",t[t.notifySubscribe=2]="notifySubscribe"}(Kd||(Kd={})),function(t){t[t.unknown=0]="unknown",t[t.server=1]="server",t[t.channel=2]="channel",t[t.serverAccids=3]="serverAccids",t[t.channelAccids=4]="channelAccids",t[t.accids=5]="accids"}(zd||(zd={})),function(t){t[t.serverMemberInvite=1]="serverMemberInvite",t[t.serverMemberInviteReject=2]="serverMemberInviteReject",t[t.serverMemberApply=3]="serverMemberApply",t[t.serverMemberApplyReject=4]="serverMemberApplyReject",t[t.serverCreate=5]="serverCreate",t[t.serverRemove=6]="serverRemove",t[t.serverUpdate=7]="serverUpdate",t[t.serverMemberInviteDone=8]="serverMemberInviteDone",t[t.serverMemberInviteAccept=9]="serverMemberInviteAccept",t[t.serverMemberApplyDone=10]="serverMemberApplyDone",t[t.serverMemberApplyAccept=11]="serverMemberApplyAccept",t[t.serverMemberKick=12]="serverMemberKick",t[t.serverMemberLeave=13]="serverMemberLeave",t[t.serverMemberUpdate=14]="serverMemberUpdate",t[t.channelCreate=15]="channelCreate",t[t.channelRemove=16]="channelRemove",t[t.channelUpdate=17]="channelUpdate",t[t.channelUpdateWhiteBlackIdentify=18]="channelUpdateWhiteBlackIdentify",t[t.channelUpdateWhiteBlackIdentifyUser=19]="channelUpdateWhiteBlackIdentifyUser",t[t.updateQuickComment=20]="updateQuickComment",t[t.channelCategoryCreate=21]="channelCategoryCreate",t[t.channelCategoryRemove=22]="channelCategoryRemove",t[t.channelCategoryUpdate=23]="channelCategoryUpdate",t[t.channelCategoryUpdateWhiteBlackIdentify=24]="channelCategoryUpdateWhiteBlackIdentify",t[t.channelCategoryUpdateWhiteBlackIdentifyUser=25]="channelCategoryUpdateWhiteBlackIdentifyUser",t[t.serverIdentifyAdd=26]="serverIdentifyAdd",t[t.serverIdentifyRemove=27]="serverIdentifyRemove",t[t.serverIdentifyUpdate=28]="serverIdentifyUpdate",t[t.channelIdentifyUpdate=29]="channelIdentifyUpdate",t[t.userIdentifyUpdate=30]="userIdentifyUpdate",t[t.channelVisibilityUpdate=31]="channelVisibilityUpdate",t[t.serverEnterLeave=32]="serverEnterLeave",t[t.serverMemberJoinByInviteCode=33]="serverMemberJoinByInviteCode",t[t.channelVisibilityToVisitorUpdate=34]="channelVisibilityToVisitorUpdate",t[t.myMemberInfoUpdated=35]="myMemberInfoUpdated",t[t.custom=100]="custom",t[t.msgTyping=101]="msgTyping"}(Jd||(Jd={})),function(t){t[t.reply=1]="reply",t[t.thread=2]="thread",t[t.all=3]="all"}(Xd||(Xd={}));var ep=Math.max,tp=Math.min;function debounce(t,a,u){var h,m,g,E,I,M,S=0,C=!1,T=!1,b=!0;if("function"!=typeof t)throw new TypeError("Expected a function");function invokeFunc(a){var u=h,g=m;return h=m=void 0,S=a,E=t.apply(g,u)}function leadingEdge(t){return S=t,I=go(timerExpired,a),C?invokeFunc(t):E}function shouldInvoke(t){var u=t-M;return void 0===M||u>=a||u<0||T&&t-S>=g}function timerExpired(){var t=Za();if(shouldInvoke(t))return trailingEdge(t);I=go(timerExpired,function remainingWait(t){var u=a-(t-M);return T?tp(u,g-(t-S)):u}(t))}function trailingEdge(t){return I=void 0,b&&h?invokeFunc(t):(h=m=void 0,E)}function debounced(){var t=Za(),u=shouldInvoke(t);if(h=arguments,m=this,M=t,u){if(void 0===I)return leadingEdge(M);if(T)return clearTimeout(I),I=go(timerExpired,a),invokeFunc(M)}return void 0===I&&(I=go(timerExpired,a)),E}return a=Number(a)||0,isObject(u)&&(C=!!u.leading,g=(T="maxWait"in u)?ep(Number(u.maxWait)||0,a):g,b="trailing"in u?!!u.trailing:b),debounced.cancel=function cancel(){void 0!==I&&clearTimeout(I),S=0,h=M=m=I=void 0},debounced.flush=function flush(){return void 0===I?E:trailingEdge(Za())},debounced}var rp;function throttle(t,a,u){var h=!0,m=!0;if("function"!=typeof t)throw new TypeError("Expected a function");return isObject(u)&&(h="leading"in u?!!u.leading:h,m="trailing"in u?!!u.trailing:m),debounce(t,a,{leading:h,maxWait:a,trailing:m})}function chunk(t,a){t=t||[],a=a||1,a=Math.max(Math.floor(a),1);for(var u=[],h=0;h<t.length;h+=a)u.push(slice(t).call(t,h,h+a));return u}!function(t){t[t.sub=1]="sub",t[t.unSub=2]="unSub"}(rp||(rp={}));var np=function(){function UnreadInfoModuleService(t){var a=this;this.unreadCount={},this.manualSubscribeUnreadMap={},this.manualSubscribeServerMap={},this.manualSubscribeTypingMap={},this.unreadServerCount={},this.serverRoleIdsMap={},this.autoSubscribeUnreadMap={},this.autoSubscribeServerMap={},this._serverUnreadInfo=throttle((function(t){a.core.emit("serverUnreadInfo",a.getServerUnreadInfo(t)),get(a.core,"qchatServer.emit")&&a.core.qchatServer.emit("serverUnreadInfo",a.getServerUnreadInfo(t))}),100),this.core=t}var t=UnreadInfoModuleService.prototype;return t.changeCacheServerRoleIds=function changeCacheServerRoleIds(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a,u,h,m,g,E,I,M=this;return cs.wrap((function _callee$(S){for(;;)switch(S.prev=S.next){case 0:if(u=t.attach.serverIdentifyInfo,h=u.serverId,m=u.roleId,this.serverRoleIdsMap[h]){S.next=4;break}return S.next=4,this.getRoleIdsByServerId([h]);case 4:if(g=this.serverRoleIdsMap[h],!(t.time<g.timeTag)){S.next=7;break}return S.abrupt("return");case 7:if(t.type===Jd[Jd.serverIdentifyAdd]?g.roleIds.add(m):g.roleIds.delete(m),this.core.logger.debug("QChatChannel::qchatChannel/serverIdentifyChange::now "+h+" roleIds is",concat(a=[]).call(a,g.roleIds)),E=this.unreadServerCount[h]){S.next=12;break}return S.abrupt("return");case 12:I=chunk(Qi(E),100),forEach$1(I).call(I,(function(t){M.core.qchatChannel.getChannelUnreadInfos({channels:map$6(t).call(t,(function(t){return{serverId:h,channelId:t}}))})}));case 14:case"end":return S.stop()}}),_callee,this)})))},t.getRoleIdsByServerId=function getRoleIdsByServerId(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a,u,h,m,g=this;return cs.wrap((function _callee2$(E){for(;;)switch(E.prev=E.next){case 0:if((u=filter(t).call(t,(function(t){return!g.serverRoleIdsMap[t]}))).length){E.next=3;break}return E.abrupt("return");case 3:return h={serverIdTimeTags:u},E.next=6,this.core.sendCmd("qchatGetRoleIdsByServerId",{qchatGetRoleIdsByServerIdTag:h});case 6:m=E.sent,this.core.logger.debug("QChatChannel:: getRoleIdsByServerId, params is ",h,"result is",m),forEach$1(a=m.content.serverRoles).call(a,(function(t){try{t.roleIds=JSON.parse(t.roleIds)}catch(t){g.core.logger.error("QChatChannel:: getRoleIdsByServerId JSON parse roleIds error",t)}g.serverRoleIdsMap[t.serverId]={roleIds:new Zd(t.roleIds),timeTag:t.timeTag}}));case 9:case"end":return E.stop()}}),_callee2,this)})))},t._unSubscribeChannel=function _unSubscribeChannel(t,a){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var u,h,m,g,E;return cs.wrap((function _callee3$(I){for(;;)switch(I.prev=I.next){case 0:if(u=t+"_"+a,h=[],this.manualSubscribeUnreadMap[u]?h.push(this.manualSubscribeUnreadMap[u]):this.autoSubscribeUnreadMap[u]&&h.push(this.autoSubscribeUnreadMap[u]),this.manualSubscribeTypingMap[u]&&h.push(this.manualSubscribeTypingMap[u]),0!==h.length){I.next=6;break}return I.abrupt("return");case 6:for(m=0,g=h;m<g.length;m++)E=g[m],this.subscribe({opeType:rp.unSub,type:E,channels:[{serverId:t,channelId:a}]});this.manualSubscribeUnreadMap[u]?this.manualSubscribeUnreadMap[u]=void 0:this.autoSubscribeUnreadMap[u]&&(this.autoSubscribeUnreadMap[u]=void 0),this.manualSubscribeTypingMap[u]&&(this.manualSubscribeTypingMap[u]=void 0);case 9:case"end":return I.stop()}}),_callee3,this)})))},t._unSubscribeServer=function _unSubscribeServer(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a,u;return cs.wrap((function _callee4$(h){for(;;)switch(h.prev=h.next){case 0:if(a=t,u=this.manualSubscribeServerMap[a]||this.autoSubscribeServerMap[a]){h.next=4;break}return h.abrupt("return");case 4:this.subscribe({opeType:rp.unSub,type:u,channels:[{serverId:t,channelId:""}]}),this.manualSubscribeServerMap[a]?this.manualSubscribeServerMap[a]=void 0:this.autoSubscribeServerMap[a]&&(this.autoSubscribeServerMap[a]=void 0);case 6:case"end":return h.stop()}}),_callee4,this)})))},t.subscribe=function subscribe(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){var a,u,h,m,g=this;return cs.wrap((function _callee6$(E){for(;;)switch(E.prev=E.next){case 0:return validate({type:{type:"number",min:1,max:5},opeType:{type:"number",min:1,max:2}},t),E.next=3,this.core.sendCmd("qchatSubscribe",{qchatSubReqTag:{type:t.type,opeType:t.opeType},channels:t.channels});case 3:return u=E.sent,h=new Zd,forEach$1(a=t.channels).call(a,(function(t){return __awaiter(g,void 0,void 0,cs.mark((function _callee5(){return cs.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:this.serverRoleIdsMap[t.serverId]||h.add(t.serverId);case 1:case"end":return a.stop()}}),_callee5,this)})))})),m=chunk(gu(h),10),forEach$1(m).call(m,(function(t){g.getRoleIdsByServerId(t)})),E.abrupt("return",u.content);case 9:case"end":return E.stop()}}),_callee6,this)})))},t.autoSubscribe=function autoSubscribe(){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){return cs.wrap((function _callee7$(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.core.sendCmd("qchatAutoSubscribe",{qchatAutoSubReqTag:{}});case 2:case"end":return t.stop()}}),_callee7,this)})))},t.resumeSubscribe=function resumeSubscribe(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee10(){var a,u,h,m,g,E,I,M=this;return cs.wrap((function _callee10$(S){for(;;)switch(S.prev=S.next){case 0:if(this.serverRoleIdsMap={},g=100,t){S.next=6;break}return this.unreadServerCount={},this.unreadCount={},S.abrupt("return");case 6:return E={},forEach$1(a=Qi(this.manualSubscribeUnreadMap)).call(a,(function(t){var a=M.manualSubscribeUnreadMap[t];if(a){var u=t.split("_"),h={serverId:u[0],channelId:u[1]};E[a]=E[a]||[],E[a].push(h)}})),E[5]=[],forEach$1(u=Qi(this.manualSubscribeTypingMap)).call(u,(function(t){return __awaiter(M,void 0,void 0,cs.mark((function _callee8(){var a,u,h,m;return cs.wrap((function _callee8$(g){for(;;)switch(g.prev=g.next){case 0:this.manualSubscribeTypingMap[t]&&(a=t.split("_"),u=a[0],h=a[1],m={serverId:u,channelId:h},E[5].push(m));case 2:case"end":return g.stop()}}),_callee8,this)})))})),E[4]=[],forEach$1(h=Qi(this.manualSubscribeServerMap)).call(h,(function(t){return __awaiter(M,void 0,void 0,cs.mark((function _callee9(){var a;return cs.wrap((function _callee9$(u){for(;;)switch(u.prev=u.next){case 0:this.manualSubscribeServerMap[t]&&(a={serverId:t},E[4].push(a));case 2:case"end":return u.stop()}}),_callee9,this)})))})),I=[],forEach$1(m=Qi(E)).call(m,(function(t){var a=E[t];if(a&&0===a.length)return za.resolve();if(a.length>g){var u=chunk(a,100);return forEach$1(u).call(u,(function(a){I.push(M.createSubscribePromise(a,t))}))}I.push(M.createSubscribePromise(a,t))})),S.next=16,za.all(I);case 16:case"end":return S.stop()}}),_callee10,this)})))},t.createSubscribePromise=function createSubscribePromise(t,a){var u=this,h={type:Kl(a),opeType:1,channels:t};return this.core.logger.debug("qchatChannel:: autoSubscribeUnread params",h),this.subscribe(h).then((function(t){var a=t.unreadInfos;a&&a.length>0&&(a=formatUnreadInfos(a),u.updateUnreads(a))})).catch((function(t){u.core.logger.error("qchatChannel:: autoSubscribeUnread error ",t)}))},t.cacheSubscribe=function cacheSubscribe(t){var a,u=this;forEach$1(a=t.channels).call(a,(function(a){var h=a.channelId?a.serverId+"_"+a.channelId:""+a.serverId,m=5===t.type?u.manualSubscribeTypingMap:4===t.type?u.manualSubscribeServerMap:u.manualSubscribeUnreadMap;t.opeType===rp.sub?m[h]=t.type:t.opeType===rp.unSub&&(m[h]=void 0)}))},t.cacheAutoSubscribe=function cacheAutoSubscribe(t){var a,u=this;forEach$1(a=t.channels).call(a,(function(a){var h=a.channelId?a.serverId+"_"+a.channelId:""+a.serverId,m=4===t.type?u.autoSubscribeServerMap:u.autoSubscribeUnreadMap;t.opeType===rp.sub?m[h]=t.type:t.opeType===rp.unSub&&(m[h]=void 0)}))},t.cacheUnreadCount=function cacheUnreadCount(t){var a=this;forEach$1(t).call(t,(function(t){var u=t.serverId+"_"+t.channelId;a.unreadCount[u]=Ot({},t),a.unreadServerCount[t.serverId]||(a.unreadServerCount[t.serverId]={}),a.unreadServerCount[t.serverId][t.channelId]=!0}))},t.getServerUnreadInfo=function getServerUnreadInfo(t){var a,u=this,h={serverId:t,unreadCount:0,mentionedCount:0,maxCount:0},m=this.unreadServerCount[t];m&&(forEach$1(a=Qi(m)).call(a,(function(a){var m=t+"_"+a,g=u.unreadCount[m];h.unreadCount+=g.unreadCount,h.mentionedCount+=g.mentionedCount,void 0!==g.maxCount&&(h.maxCount=g.maxCount)})),h.unreadCount=h.unreadCount>h.maxCount?h.maxCount:h.unreadCount,h.mentionedCount=h.mentionedCount>h.maxCount?h.maxCount:h.mentionedCount);return h},t.getUnreadInfo=function getUnreadInfo(t){validate({serverId:{type:"string"},channelId:{type:"string"}},t);var a=t.serverId+"_"+t.channelId;return this.unreadCount[a]?this.unreadCount[a]:null},t.shouldChangeUnread=function shouldChangeUnread(t,a){if(!t.serverId||!t.channelId)return!1;if(this.core.qchatChannel.subscribeForVisitorService.isInSubscribeChannels(t.serverId,t.channelId))return!1;if(!1===t.historyEnable)return!1;if(!1===t.needBadge)return!1;if(t.fromAccount===this.core.account)return!1;if(a&&2!==(null==t?void 0:t.status))return!1;if(t.notifyReason&&t.notifyReason===getEnumKeyByEnumValue(Kd,Kd.notifySubscribe)){if(!t.serverId||!t.channelId)return!1;var u=this.getUnreadInfo({serverId:t.serverId,channelId:t.channelId}),h=a?"ackTimestamp":"lastMsgTime";if(!u)return!1;if(t.time&&u[h]&&u[h]>t.time)return!1}return!0},t.shouldChangeMentionedUnread=function shouldChangeMentionedUnread(t,a){return void 0===a&&(a=!1),__awaiter(this,void 0,void 0,cs.mark((function _callee11(){var u,h;return cs.wrap((function _callee11$(m){for(;;)switch(m.prev=m.next){case 0:if(!t.accidsOfMentionedRoles||!includes(u=t.accidsOfMentionedRoles).call(u,this.core.account)){m.next=2;break}return m.abrupt("return",!0);case 2:if(!t.mentionRoleIds||!t.serverId){m.next=16;break}if(this.serverRoleIdsMap[t.serverId]){m.next=9;break}if(a){m.next=7;break}return this.handledRoleMsg(t),m.abrupt("return",!1);case 7:return m.next=9,this.getRoleIdsByServerId([t.serverId]);case 9:h=0;case 10:if(!(h<t.mentionRoleIds.length)){m.next=16;break}if(!this.serverRoleIdsMap[t.serverId].roleIds.has(t.mentionRoleIds[h])){m.next=13;break}return m.abrupt("return",!0);case 13:h++,m.next=10;break;case 16:return m.abrupt("return",!1);case 17:case"end":return m.stop()}}),_callee11,this)})))},t.handledRoleMsg=function handledRoleMsg(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee12(){var a,u,h,m,g,E=this;return cs.wrap((function _callee12$(I){for(;;)switch(I.prev=I.next){case 0:if(t.mentionRoleIds){I.next=2;break}return I.abrupt("return");case 2:if(this.serverRoleIdsMap[t.serverId]){I.next=5;break}return I.next=5,this.getRoleIdsByServerId([t.serverId]);case 5:if(this.serverRoleIdsMap[t.serverId]){I.next=8;break}return this.core.logger.warn("QChatChannel::handledRoleMsg::can not get serverRoleIds,server id",t.serverId),I.abrupt("return");case 8:if(u=!1,forEach$1(a=t.mentionRoleIds).call(a,(function(a){E.serverRoleIdsMap[t.serverId].roleIds.has(a)&&(u=!0,E.core.logger.debug("QChatChannel::handledRoleMsg::will update message mentionedCount，message is ",t))})),u){I.next=12;break}return I.abrupt("return");case 12:if(h=this.unreadCount[t.serverId+"_"+t.channelId],m=2===(null==t?void 0:t.status),h.mentionedCount=m?h.mentionedCount?h.mentionedCount-1:0:h.mentionedCount?h.mentionedCount+1:1,"number"!=typeof h.maxCount){I.next=25;break}if(!((g=(g=h.mentionedCount)>h.maxCount?h.maxCount:g)>h.maxCount)){I.next=21;break}return this.core.logger.debug("QChatChannel::handledRoleMsg::tempUnreadCount more than maxCount"),I.abrupt("return");case 21:this.core.emit("unreadInfo",Ot(Ot({},h),{mentionedCount:g})),this.core.emit("unreadInfos",[Ot(Ot({},h),{mentionedCount:g})]),this.core.qchatChannel.emit("unreadInfos",[Ot(Ot({},h),{mentionedCount:g})]),this._serverUnreadInfo(t.serverId);case 25:case"end":return I.stop()}}),_callee12,this)})))},t.getMentionedFlag=function getMentionedFlag(t,a){return void 0===a&&(a=!1),__awaiter(this,void 0,void 0,cs.mark((function _callee13(){var u;return cs.wrap((function _callee13$(h){for(;;)switch(h.prev=h.next){case 0:if(!t.mentionAll){h.next=4;break}return h.abrupt("return",!0);case 4:if(!t.mentionRoleIds&&!t.accidsOfMentionedRoles){h.next=10;break}return h.next=7,this.shouldChangeMentionedUnread(t,a);case 7:return h.abrupt("return",h.sent);case 10:if(!t.mentionAccids||!includes(u=t.mentionAccids).call(u,this.core.account)){h.next=12;break}return h.abrupt("return",!0);case 12:return h.abrupt("return",!1);case 13:case"end":return h.stop()}}),_callee13,this)})))},t.changeUnread=function changeUnread(t,a){return __awaiter(this,void 0,void 0,cs.mark((function _callee14(){var u,h,m,g,E,I,M;return cs.wrap((function _callee14$(S){for(;;)switch(S.prev=S.next){case 0:if(u=t.serverId,h=t.channelId,m=u+"_"+h,this.shouldChangeUnread(t,a)){S.next=6;break}return this.core.logger.debug("changeUnread: "+m+" does not need to update unreadInfo"),S.abrupt("return");case 6:if(this.unreadCount[m]){S.next=11;break}return S.next=9,this.core.qchatChannel.getChannelUnreadInfos({channels:[{serverId:u,channelId:h}]});case 9:return!this.serverRoleIdsMap[u]&&t.mentionRoleIds&&this.getRoleIdsByServerId([u]),S.abrupt("return");case 11:return g=this.unreadCount[m],E=2===(null==t?void 0:t.status),S.next=15,this.getMentionedFlag(t);case 15:if(I=S.sent,E?(g.unreadCount=g.unreadCount?g.unreadCount-1:0,I&&(g.mentionedCount=g.mentionedCount?g.mentionedCount-1:0),delete g.lastMsgTime,this.core.logger.debug("changeUnread: "+m+" unread reduce ",g)):(g.unreadCount=g.unreadCount?g.unreadCount+1:1,I&&(g.mentionedCount=g.mentionedCount?g.mentionedCount+1:1),t.time&&(g.lastMsgTime=t.time),this.core.logger.debug("changeUnread: "+m+" unread add ",g)),M="number"==typeof g.maxCount?g.maxCount:100,!(g.unreadCount>M)){S.next=21;break}return this.core.logger.debug("QChatChannel::subscribe::tempUnreadCount more than maxCount"),S.abrupt("return");case 21:this.core.logger.warn("unreadInfo event will abandon,please use unreadInfos"),this.core.emit("unreadInfo",Ot(Ot({},g),{mentionedCount:g.mentionedCount>M?M:g.mentionedCount,unreadCount:g.unreadCount>M?M:g.unreadCount})),this.core.emit("unreadInfos",[Ot(Ot({},g),{mentionedCount:g.mentionedCount>M?M:g.mentionedCount,unreadCount:g.unreadCount>M?M:g.unreadCount})]),this.core.qchatChannel.emit("unreadInfos",[Ot(Ot({},g),{mentionedCount:g.mentionedCount>M?M:g.mentionedCount,unreadCount:g.unreadCount>M?M:g.unreadCount})]),this._serverUnreadInfo(u);case 26:case"end":return S.stop()}}),_callee14,this)})))},t.updateUnreads=function updateUnreads(t){var a,u,h=this;if(t&&t.length>0){var m=[],g=[],E=map$6(a=filter(t).call(t,(function(t){var a=t.serverId+"_"+t.channelId,u=h.unreadCount[a],m=get(u,"ackTimestamp"),g=get(t,"ackTimestamp");return!(m&&g&&g<m)&&(get(u,"unreadCount")!==get(t,"unreadCount")||get(u,"mentionedCount")!==get(t,"mentionedCount"))}))).call(a,(function(t){m.push(h.getServerUnreadInfo(t.serverId)),g.push(h.unreadServerCount[t.serverId]);var a=t.serverId,u=t.channelId,E=a+"_"+u;return h.core.logger.debug("qchat channel updateUnread: ",t),h.unreadCount[E]=Ot({},t),h.unreadServerCount[a]||(h.unreadServerCount[a]={}),h.unreadServerCount[a][u]=!0,t}));if(0!==E.length){this.core.emit("unreadInfos",E),this.core.qchatChannel.emit("unreadInfos",E);var I={};forEach$1(E).call(E,(function(t,a){h.core.emit("unreadInfo",t);var u=m[a],E=g[a],M=h.getServerUnreadInfo(t.serverId),S=h.unreadServerCount[t.serverId],C=u.unreadCount!==M.unreadCount,T=!E||0===Qi(E).length,b=S&&Qi(S).length>0,R=u.mentionedCount!==M.mentionedCount;(C||R||T&&b)&&(I[t.serverId]=!0)})),forEach$1(u=Qi(I)).call(u,(function(t){h.core.emit("serverUnreadInfo",h.getServerUnreadInfo(t)),get(h.core,"qchatServer.emit")&&h.core.qchatServer.emit("serverUnreadInfo",h.getServerUnreadInfo(t))}))}}},t.clearUnreadCountByServers=function clearUnreadCountByServers(t,a){var u=this,h=[];forEach$1(t).call(t,(function(t){var a;u.unreadServerCount[t]&&forEach$1(a=Qi(u.unreadServerCount[t])).call(a,(function(a){h.push(t+"_"+a)}))}));var m=[];forEach$1(h).call(h,(function(t){m.push(Ot(Ot({},u.unreadCount[t]),{unreadCount:0,mentionedCount:0,ackTimestamp:a}))})),this.updateUnreads(m)},UnreadInfoModuleService}(),ap=function(){function SubscribeForVisitorService(t){this.limitForBatchEnter=10,this.limitForBatchSubscribe=100,this.autoServers=new Zd,this.autoVisitorSubscribeServer=new Zd,this.autoVisitorSubscribeChannel=new Zd,this.core=t}var t=SubscribeForVisitorService.prototype;return t.subscribeChannelAsVisitor=function subscribeChannelAsVisitor(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a,u,h,m,g=this;return cs.wrap((function _callee$(E){for(;;)switch(E.prev=E.next){case 0:return E.next=2,this.core.sendCmd("qchatSubscribeAsVisitor",{tag:{type:t.type||6,opeType:t.opeType},datas:t.channels});case 2:return u=E.sent,h=u.content.failedArr,m=filter(a=t.channels).call(a,(function(t){return!some(h).call(h,(function(a){return a.channelId===t.channelId&&a.serverId===t.serverId}))})),forEach$1(m).call(m,(function(a){1===t.opeType?g.autoVisitorSubscribeChannel.add(a.serverId+"&"+a.channelId):g.autoVisitorSubscribeChannel.delete(a.serverId+"&"+a.channelId)})),E.abrupt("return",{failedChannels:h});case 7:case"end":return E.stop()}}),_callee,this)})))},t.subscribeServerAsVisitor=function subscribeServerAsVisitor(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a,u,h,m,g,E,I=this;return cs.wrap((function _callee2$(M){for(;;)switch(M.prev=M.next){case 0:return M.next=2,this.core.sendCmd("qchatSubscribeAsVisitor",{tag:{type:t.type||7,opeType:t.opeType},datas:map$6(a=t.serverIds).call(a,(function(t){return{serverId:t}}))});case 2:return m=M.sent,g=m.content.failedArr,E=filter(u=t.serverIds).call(u,(function(t){return!some(g).call(g,(function(a){return a.serverId===t}))})),forEach$1(E).call(E,(function(a){1===t.opeType?I.autoVisitorSubscribeServer.add(a):I.autoVisitorSubscribeServer.delete(a)})),M.abrupt("return",{failedServers:map$6(h=m.content.failedArr).call(h,(function(t){return t.serverId}))});case 7:case"end":return M.stop()}}),_callee2,this)})))},t.deleteServer=function deleteServer(t){var a;this.autoServers.delete(t),this.autoVisitorSubscribeServer.has(t)&&this.subscribeServerAsVisitor({opeType:2,serverIds:[t]});var u=[];forEach$1(a=this.autoVisitorSubscribeChannel).call(a,(function(a){if(0===indexOf(a).call(a,t)){var h=a.replace(t+"&","");u.push({serverId:t,channelId:h})}})),u.length>0&&this.subscribeChannelAsVisitor({opeType:2,channels:u})},t.deleteAutoSetInServerId=function deleteAutoSetInServerId(t){var a,u=this;this.autoServers.delete(t),this.autoVisitorSubscribeServer.delete(t),forEach$1(a=this.autoVisitorSubscribeChannel).call(a,(function(a){0===indexOf(a).call(a,t)&&u.autoVisitorSubscribeChannel.delete(a)}))},t.deleteAutoSetInChannel=function deleteAutoSetInChannel(t,a){this.autoVisitorSubscribeChannel.delete(t+"&"+a)},t.unSubscribeChannel=function unSubscribeChannel(t,a){this.subscribeChannelAsVisitor({opeType:2,channels:[{serverId:t,channelId:a}]})},t.isInSubscribeChannels=function isInSubscribeChannels(t,a){return this.autoVisitorSubscribeChannel.has(t+"&"+a)},t.isInAutoServers=function isInAutoServers(t){return this.autoServers.has(t)},t.doAutoSubscribe=function doAutoSubscribe(){var t=this,a=[],u=chunk(gu(this.autoVisitorSubscribeServer),this.limitForBatchSubscribe);forEach$1(u).call(u,(function(u){a.push(t.subscribeServerAsVisitor({opeType:1,serverIds:u}))}));var h=chunk(gu(this.autoVisitorSubscribeChannel),this.limitForBatchSubscribe);return forEach$1(h).call(h,(function(u){a.push(t.subscribeChannelAsVisitor({opeType:1,channels:map$6(u).call(u,(function(t){var a=indexOf(t).call(t,"&");return{serverId:slice(t).call(t,0,a),channelId:slice(t).call(t,a+1)}}))}))})),za.all(a)},t.doAutoEnterServer=function doAutoEnterServer(){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var t,a,u,h=this;return cs.wrap((function _callee3$(m){for(;;)switch(m.prev=m.next){case 0:return t=chunk(gu(this.autoServers),this.limitForBatchEnter),a=[],forEach$1(t).call(t,(function(t){a.push(h.core.qchatServer.enterAsVisitor({serverIds:t}))})),m.next=5,za.all(a);case 5:u=m.sent,forEach$1(u).call(u,(function(t){var a;forEach$1(a=t.failedServers).call(a,(function(t){h.deleteAutoSetInServerId(t)}))}));case 7:case"end":return m.stop()}}),_callee3,this)})))},t.resumeSubscribe=function resumeSubscribe(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){return cs.wrap((function _callee4$(a){for(;;)switch(a.prev=a.next){case 0:if(!t){a.next=7;break}return a.next=3,this.doAutoEnterServer();case 3:return a.next=5,this.doAutoSubscribe();case 5:a.next=10;break;case 7:this.autoServers.clear(),this.autoVisitorSubscribeChannel.clear(),this.autoVisitorSubscribeServer.clear();case 10:case"end":return a.stop()}}),_callee4,this)})))},t.cacheServer=function cacheServer(t){var a=this;forEach$1(t).call(t,(function(t){return a.autoServers.add(t)}))},SubscribeForVisitorService}();function _createForOfIteratorHelperLoose$2(t,a){var u,h=void 0!==fi&&vu(t)||t["@@iterator"];if(h)return bind$1(u=(h=h.call(t)).next).call(u,h);if(zl(t)||(h=function _unsupportedIterableToArray$2(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$2(t,a);var h=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===h&&t.constructor&&(h=t.constructor.name),"Map"===h||"Set"===h?gu(t):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray$2(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){h&&(t=h);var m=0;return function(){return m>=t.length?{done:!0}:{done:!1,value:t[m++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$2(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,h=Array(a);u<a;u++)h[u]=t[u];return h}var op,ip,sp=function(t){function QChatChannelService(a,u){var h;return(h=t.call(this,"qchatChannel",a)||this).config={autoSubscribe:!1},h.core=a,registerParser({cmdMap:od,cmdConfig:ld()}),u&&h.setOptions(u),h.subscribeModuleService=new np(a),h.subscribeForVisitorService=new ap(a),h.setListener(),h}Tt(QChatChannelService,t);var a=QChatChannelService.prototype;return a.setOptions=function setOptions(t){t&&(this.config=Ot(this.config,t))},a.setListener=function setListener(){var t,a,u,h,m,g=this;this.core.eventBus.on("logined",(function(t){g.subscribeModuleService.resumeSubscribe(t.isAutoReconnect),g.subscribeForVisitorService.resumeSubscribe(t.isAutoReconnect),g.config.autoSubscribe&&(g.subscribeModuleService.autoSubscribeServerMap={},g.subscribeModuleService.autoSubscribeUnreadMap={},g.subscribeModuleService.autoSubscribe())})),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLoginSucc",(function(t){g.subscribeModuleService.resumeSubscribe(t.isReconnect),g.subscribeForVisitorService.resumeSubscribe(t.isReconnect),g.config.autoSubscribe&&(g.subscribeModuleService.autoSubscribeServerMap={},g.subscribeModuleService.autoSubscribeUnreadMap={},g.subscribeModuleService.autoSubscribe())})),this.core.eventBus.on("qchatChannel/changeUnread",bind$1(t=this.subscribeModuleService.changeUnread).call(t,this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/updateUnreads",bind$1(a=this.subscribeModuleService.updateUnreads).call(a,this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/cacheSubscribe",bind$1(u=this.subscribeModuleService.cacheSubscribe).call(u,this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/clearUnreadCountByServers",bind$1(h=this.subscribeModuleService.clearUnreadCountByServers).call(h,this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/getRoleIdsByServerId",bind$1(m=this.subscribeModuleService.getRoleIdsByServerId).call(m,this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/autoUnSubscribe",(function(t){if(g.logger.log("QChatChannel::enter autoUnSubscribe sysMsg is",t),t.type===Jd[Jd.channelVisibilityUpdate])g.logger.log("QChatChannel::begin autoUnSubscribe channel key is",t.serverId+"_"+t.channelId),g.subscribeModuleService._unSubscribeChannel(t.serverId,t.channelId),g.logger.log("QChatChannel::autoUnSubscribe channel done key is",t.serverId+"_"+t.channelId);else if(t.type===Jd[Jd.serverEnterLeave]){var a;(g.subscribeModuleService.manualSubscribeServerMap[t.serverId]||g.subscribeModuleService.autoSubscribeServerMap[t.serverId])&&(g.logger.log("QChatChannel::begin autoUnSubscribe server key is",t.serverId),g.subscribeModuleService._unSubscribeServer(t.serverId),g.logger.log("QChatChannel::autoUnSubscribe server done key is",t.serverId));var u=g.subscribeModuleService.unreadServerCount[t.serverId];if(!u)return;g.logger.log("QChatChannel::begin autoUnSubscribe channels key is",Qi(u)),forEach$1(a=Qi(u)).call(a,(function(a){g.subscribeModuleService._unSubscribeChannel(t.serverId,a)})),g.logger.log("QChatChannel::autoUnSubscribe channels done key is",Qi(u))}})),this.core.eventBus.on("qchatChannel/serverIdentifyChange",(function(t){g.subscribeModuleService.changeCacheServerRoleIds(t)}))},a.createChannel=function createChannel(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a;return cs.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Cd)},name:{type:"string",required:!1},topic:{type:"string",required:!1},ext:{type:"string",required:!1},visitorMode:{type:"number",required:!1}},t),u.next=3,this.core.sendCmd("qchatCreateChannel",{channelInfo:Ot(Ot({},t),{type:Cd[t.type]}),antispamTag:generateAntispamTag(t)});case 3:return a=u.sent,u.abrupt("return",formatChannel(a.content.channelInfo));case 5:case"end":return u.stop()}}),_callee,this)})))},a.deleteChannel=function deleteChannel(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){return cs.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return validate({channelId:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatDeleteChannel",t);case 3:case"end":return a.stop()}}),_callee2,this)})))},a.updateChannel=function updateChannel(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var a,u;return cs.wrap((function _callee3$(h){for(;;)switch(h.prev=h.next){case 0:return validate({channelId:{type:"string",min:1},serverId:{type:"string",required:!1},type:{type:"enum",values:getEnumKeys(Cd),required:!1},name:{type:"string",required:!1},topic:{type:"string",required:!1},ext:{type:"string",required:!1},visitorMode:{type:"number",required:!1}},t),a=t,t.type&&(a.type=Cd[t.type]),h.next=5,this.core.sendCmd("qchatUpdateChannel",{channelInfo:a,antispamTag:generateAntispamTag(t)});case 5:return u=h.sent,h.abrupt("return",formatChannel(u.content.channelInfo));case 7:case"end":return h.stop()}}),_callee3,this)})))},a.getChannels=function getChannels(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a;return cs.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return validate({channelIds:{type:"array",itemType:"string",min:1}},t),u.next=3,this.core.sendCmd("qchatGetChannels",t);case 3:return a=u.sent,u.abrupt("return",formatChannels(a.content.channelList));case 5:case"end":return u.stop()}}),_callee4,this)})))},a.getChannelsByPage=function getChannelsByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var a,u,h,m;return cs.wrap((function _callee5$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",min:1},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetChannelsByPage",{qchatGetChannelListPageTag:Ot({limit:100},t)});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatChannels(h)});case 6:case"end":return g.stop()}}),_callee5,this)})))},a.getMembersByPage=function getMembersByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){var a,u,h,m;return cs.wrap((function _callee6$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetMembersByPage",{qchatGetMembersByPageTag:t});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatMembers$1(h)});case 6:case"end":return g.stop()}}),_callee6,this)})))},a.updateWhiteBlackRole=function updateWhiteBlackRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){return cs.wrap((function _callee7$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},roleId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Md)},opeType:{type:"enum",values:getEnumKeys(Sd)}},t),a.next=3,this.core.sendCmd("qchatUpdateWhiteBlackRole",{qchatUpdateWhiteBlackRoleTag:Ot(Ot({},t),{type:Md[t.type],opeType:Sd[t.opeType]})});case 3:case"end":return a.stop()}}),_callee7,this)})))},a.getWhiteBlackRolesPage=function getWhiteBlackRolesPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){var a,u,h,m;return cs.wrap((function _callee8$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Md)},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetWhiteBlackRolesPage",{qchatGetWhiteBlackRolesPageTag:Ot(Ot({},t),{type:Md[t.type]})});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatRoles(h)});case 6:case"end":return g.stop()}}),_callee8,this)})))},a.updateWhiteBlackMembers=function updateWhiteBlackMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee9(){return cs.wrap((function _callee9$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Md)},opeType:{type:"enum",values:getEnumKeys(Sd)},toAccids:{type:"array",itemType:"string",min:1}},t),a.next=3,this.core.sendCmd("qchatUpdateWhiteBlackMembers",{qchatUpdateWhiteBlackMembersTag:Ot(Ot({},t),{type:Md[t.type],opeType:Sd[t.opeType],toAccids:gs(t.toAccids)})});case 3:case"end":return a.stop()}}),_callee9,this)})))},a.getWhiteBlackMembersPage=function getWhiteBlackMembersPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee10(){var a,u,h,m;return cs.wrap((function _callee10$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Md)},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetWhiteBlackMembersPage",{qchatGetWhiteBlackMembersPageTag:Ot(Ot({limit:100},t),{type:Md[t.type]})});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatMembers$1(h)});case 6:case"end":return g.stop()}}),_callee10,this)})))},a.getExistingWhiteBlackRoles=function getExistingWhiteBlackRoles(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee11(){var a,u;return cs.wrap((function _callee11$(h){for(;;)switch(h.prev=h.next){case 0:return validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Md)},roleIds:{type:"array",itemType:"string",min:1}},t),h.next=3,this.core.sendCmd("qchatGetExistingWhiteBlackRoles",{qchatGetExistingWhiteBlackRolesTag:Ot(Ot({},t),{type:Md[t.type],roleIds:gs(t.roleIds)})});case 3:return a=h.sent,u=a.content.datas,h.abrupt("return",{datas:formatRoles(u)});case 6:case"end":return h.stop()}}),_callee11,this)})))},a.getExistingWhiteBlackMembers=function getExistingWhiteBlackMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee12(){var a,u;return cs.wrap((function _callee12$(h){for(;;)switch(h.prev=h.next){case 0:return validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Md)},accids:{type:"array",itemType:"string",min:1}},t),h.next=3,this.core.sendCmd("qchatGetExistingWhiteBlackMembers",{qchatGetExistingWhiteBlackMembersTag:Ot(Ot({},t),{type:Md[t.type],accids:gs(t.accids)})});case 3:return a=h.sent,u=a.content.datas,h.abrupt("return",{datas:formatMembers$1(u)});case 6:case"end":return h.stop()}}),_callee12,this)})))},a.updateCategoryInfoOfChannel=function updateCategoryInfoOfChannel(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee13(){var a;return cs.wrap((function _callee13$(u){for(;;)switch(u.prev=u.next){case 0:return validate({channelId:{type:"string",min:1},categoryId:{type:"string",allowEmpty:!1,required:!1},syncMode:{type:"number",min:0,max:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateCategoryInfoOfChannel",{qchatUpdateCategoryInfoOfChannelTag:t});case 3:return a=u.sent,u.abrupt("return",formatChannel(a.content.channelInfo));case 5:case"end":return u.stop()}}),_callee13,this)})))},a.createChannelCategory=function createChannelCategory(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee14(){var a;return cs.wrap((function _callee14$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},name:{type:"string",allowEmpty:!1,required:!1},ext:{type:"string",required:!1},viewMode:{type:"number",min:0,max:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatCreateChannelCategory",{qchatCreateChannelCategoryTag:t});case 3:return a=u.sent,u.abrupt("return",formatChannelCategory(a.content.QChatChannelCategoryInfo));case 5:case"end":return u.stop()}}),_callee14,this)})))},a.removeChannelCategory=function removeChannelCategory(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee15(){return cs.wrap((function _callee15$(a){for(;;)switch(a.prev=a.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatRemoveChannelCategory",t);case 3:case"end":return a.stop()}}),_callee15,this)})))},a.updateChannelCategory=function updateChannelCategory(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee16(){var a;return cs.wrap((function _callee16$(u){for(;;)switch(u.prev=u.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1},name:{type:"string",allowEmpty:!1,required:!1},ext:{type:"string",required:!1},viewMode:{type:"number",min:0,max:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateChannelCategory",{qchatUpdateChannelCategoryTag:t});case 3:return a=u.sent,u.abrupt("return",formatChannelCategory(a.content.QChatChannelCategoryInfo));case 5:case"end":return u.stop()}}),_callee16,this)})))},a.getChannelCategoriesByID=function getChannelCategoriesByID(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee17(){var a;return cs.wrap((function _callee17$(u){for(;;)switch(u.prev=u.next){case 0:return validate({categoryIds:{type:"array",itemType:"string",min:1}},t),u.next=3,this.core.sendCmd("qchatGetChannelCategoriesByID",t);case 3:return a=u.sent,u.abrupt("return",formatChannelCategorys(a.content.channelCategoryList));case 5:case"end":return u.stop()}}),_callee17,this)})))},a.updateChannelCategoryWhiteBlackRole=function updateChannelCategoryWhiteBlackRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee18(){return cs.wrap((function _callee18$(a){for(;;)switch(a.prev=a.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Md)},opeType:{type:"enum",values:getEnumKeys(Sd)},roleId:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatUpdateChannelCategoryWhiteBlackRole",{qchatUpdateChannelCategoryWhiteBlackRoleTag:Ot(Ot({},t),{type:Md[t.type],opeType:Sd[t.opeType]})});case 3:case"end":return a.stop()}}),_callee18,this)})))},a.getChannelCategoryWhiteBlackRolesPage=function getChannelCategoryWhiteBlackRolesPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee19(){var a,u,h,m;return cs.wrap((function _callee19$(g){for(;;)switch(g.prev=g.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Md)},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetChannelCategoryWhiteBlackRolesPage",{qchatGetChannelCategoryWhiteBlackRolesPageTag:Ot(Ot({},t),{type:Md[t.type]})});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatRoles(h)});case 6:case"end":return g.stop()}}),_callee19,this)})))},a.updateChannelCategoryWhiteBlackMembers=function updateChannelCategoryWhiteBlackMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee20(){return cs.wrap((function _callee20$(a){for(;;)switch(a.prev=a.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Md)},opeType:{type:"enum",values:getEnumKeys(Sd)},toAccids:{type:"array",itemType:"string",min:1}},t),a.next=3,this.core.sendCmd("qchatUpdateChannelCategoryWhiteBlackMembers",{qchatUpdateChannelCategoryWhiteBlackMembersTag:Ot(Ot({},t),{type:Md[t.type],opeType:Sd[t.opeType],toAccids:gs(t.toAccids)})});case 3:case"end":return a.stop()}}),_callee20,this)})))},a.getChannelCategoryWhiteBlackMembersPage=function getChannelCategoryWhiteBlackMembersPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee21(){var a,u,h,m;return cs.wrap((function _callee21$(g){for(;;)switch(g.prev=g.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Md)},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetChannelCategoryWhiteBlackMembersPage",{qchatGetChannelCategoryWhiteBlackMembersPageTag:Ot(Ot({},t),{type:Md[t.type]})});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatMembers$1(h)});case 6:case"end":return g.stop()}}),_callee21,this)})))},a.getChannelCategoryWhiteBlackRoles=function getChannelCategoryWhiteBlackRoles(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee22(){var a,u;return cs.wrap((function _callee22$(h){for(;;)switch(h.prev=h.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Md)},roleIds:{type:"array",itemType:"string",min:1}},t),h.next=3,this.core.sendCmd("qchatGetChannelCategoryWhiteBlackRoles",{qchatGetChannelCategoryWhiteBlackRolesTag:Ot(Ot({},t),{type:Md[t.type],roleIds:gs(t.roleIds)})});case 3:return a=h.sent,u=a.content.datas,h.abrupt("return",formatRoles(u));case 6:case"end":return h.stop()}}),_callee22,this)})))},a.getChannelCategoryWhiteBlackMembers=function getChannelCategoryWhiteBlackMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee23(){var a,u;return cs.wrap((function _callee23$(h){for(;;)switch(h.prev=h.next){case 0:return validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Md)},accids:{type:"array",itemType:"string",min:1}},t),h.next=3,this.core.sendCmd("qchatGetChannelCategoryWhiteBlackMembers",{qchatGetChannelCategoryWhiteBlackMembersTag:Ot(Ot({},t),{type:Md[t.type],accids:gs(t.accids)})});case 3:return a=h.sent,u=a.content.datas,h.abrupt("return",formatMembers$1(u));case 6:case"end":return h.stop()}}),_callee23,this)})))},a.getChannelCategoriesPage=function getChannelCategoriesPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee24(){var a,u,h,m;return cs.wrap((function _callee24$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetChannelCategoriesPage",{qchatGetChannelCategoriesPageTag:t});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatChannelCategorys(h)});case 6:case"end":return g.stop()}}),_callee24,this)})))},a.getChannelCategoryChannelsPage=function getChannelCategoryChannelsPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee25(){var a,u,h,m;return cs.wrap((function _callee25$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},t),g.next=3,this.core.sendCmd("qchatGetChannelCategoryChannelsPage",{qchatGetChannelCategoryChannelsPageTag:t});case 3:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag)},datas:formatChannels(h)});case 6:case"end":return g.stop()}}),_callee25,this)})))},a.subscribeChannel=function subscribeChannel(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee26(){var a,u;return cs.wrap((function _callee26$(h){for(;;)switch(h.prev=h.next){case 0:if(validate({type:{type:"number",min:1,max:5},opeType:{type:"number",min:1,max:2}},t),!this.config.autoSubscribe||t.isInternalTrigger||5===t.type){h.next=3;break}throw new Gl("subscribe server failed, manual subscribe is not allowed in auto subscribe mode",{},403);case 3:return h.next=5,this.subscribeModuleService.subscribe(t);case 5:if((a=h.sent).unreadInfos=formatUnreadInfos(a.unreadInfos),5!==t.type){h.next=10;break}return this.subscribeModuleService.cacheSubscribe(t),h.abrupt("return");case 10:return 1===t.opeType&&(t.channels=map$6(u=a.unreadInfos).call(u,(function(t){return{serverId:t.serverId,channelId:t.channelId}}))),this.logger.debug("QChatChannel::subscribeChannel:: cacheSubscribe ",t),this.config.autoSubscribe?this.subscribeModuleService.cacheAutoSubscribe(t):this.subscribeModuleService.cacheSubscribe(t),this.subscribeModuleService.updateUnreads(a.unreadInfos),h.abrupt("return",a);case 15:case"end":return h.stop()}}),_callee26,this)})))},a.getChannelUnreadInfos=function getChannelUnreadInfos(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee27(){var a,u;return cs.wrap((function _callee27$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("qchatGetUnreadInfo",t);case 2:return a=h.sent,u=formatUnreadInfos(a.content.unreadInfos),this.subscribeModuleService.updateUnreads(u),h.abrupt("return",u);case 6:case"end":return h.stop()}}),_callee27,this)})))},a.getChannelSearchByPage=function getChannelSearchByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee28(){var a,u,h,m;return cs.wrap((function _callee28$(g){for(;;)switch(g.prev=g.next){case 0:if(validate({keyword:{type:"string",allowEmpty:!1},startTime:{type:"number",min:0,required:!1},endTime:{type:"number",min:1,required:!1},order:{type:"enum",values:getEnumKeys(Zu),required:!1},limit:{type:"number",min:1,required:!1},serverId:{type:"string",required:!1},sort:{type:"enum",values:getEnumKeys(Id),required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},t),!(t.startTime&&t.endTime&&t.startTime>=t.endTime)){g.next=3;break}throw new ql("startTime more than endTime",t,"timeRule");case 3:return g.next=5,this.core.sendCmd("qchatGetChannelSearchByPage",{qchatGetChannelSearchByPageTag:Ot(Ot({},t),{order:t.order&&Zu[t.order],sort:sort(t)&&Id[sort(t)]})});case 5:return a=g.sent,u=a.content,h=u.datas,m=u.listQueryTag,g.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:Kl(m.nextTimetag),cursor:m.cursor},datas:formatChannels(h)});case 8:case"end":return g.stop()}}),_callee28,this)})))},a.channelMemberSearch=function channelMemberSearch(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee29(){var a;return cs.wrap((function _callee29$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},keyword:{type:"string",allowEmpty:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatChannelMemberSearch",{qchatChannelMemberSearchTag:t});case 3:return a=u.sent,u.abrupt("return",formatChannelMembers(a.content.datas));case 5:case"end":return u.stop()}}),_callee29,this)})))},a.subscribeAsVisitor=function subscribeAsVisitor(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee30(){return cs.wrap((function _callee30$(a){for(;;)switch(a.prev=a.next){case 0:return validate({opeType:{type:"number",min:1,max:2},type:{type:"number",required:!1},channels:{type:"array",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1}},min:1}},t),a.next=3,this.subscribeForVisitorService.subscribeChannelAsVisitor(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee30,this)})))},a.qchatAutoSubscribeNotificationHandler=function qchatAutoSubscribeNotificationHandler(t){var a=formatUnreadInfos(t.content.unreadInfos),u=t.content.serverIds;this.subscribeModuleService.updateUnreads(a);var h=[];if(u.length){var m={opeType:1};m.channels=map$6(u).call(u,(function(t){return{serverId:t.serverId,channelId:""}})),m.type=4,h.push(m)}if(a.length){var g={opeType:1};g.channels=map$6(a).call(a,(function(t){return{serverId:t.serverId,channelId:t.channelId}})),g.type=1,h.push(g)}if(h.length)for(var E,I=_createForOfIteratorHelperLoose$2(h);!(E=I()).done;){var M=E.value;this.subscribeModuleService.cacheAutoSubscribe(M)}},QChatChannelService}(ad),cp={"24_61":"qchatCreateServerRole","24_62":"qchatDeleteServerRole","24_63":"qchatUpdateServerRole","24_64":"qchatGetServerRoles","24_65":"qchatAddChannelRole","24_66":"qchatRemoveChannelRole","24_67":"qchatUpdateChannelRole","24_68":"qchatGetChannelRoles","24_69":"qchatAddMemberRole","24_70":"qchatRemoveMemberRole","24_71":"qchatUpdateMemberRole","24_72":"qchatGetMemberRoles","24_73":"qchatAddMembersToServerRole","24_74":"qchatRemoveMembersFromServerRole","24_75":"qchatGetMembersFromServerRole","24_76":"qchatGetServerRolesByAccid","24_77":"qchatGetExistingServerRolesByAccids","24_78":"qchatGetExistingChannelRolesByServerRoleIds","24_79":"qchatGetExistingAccidsOfMemberRoles","24_80":"qchatUpdateServerRolePriorities","24_81":"qchatGetExistingAccidsInServerRole","24_82":"qchatCheckPermission","24_83":"qchatAddChannelCategoryRole","24_84":"qchatRemoveChannelCategoryRole","24_85":"qchatUpdateChannelCategoryRole","24_86":"qchatGetChannelCategoryRole","24_87":"qchatAddChannelCategoryMemberRole","24_88":"qchatRemoveChannelCategoryMemberRole","24_89":"qchatUpdateChannelCategoryMemberRole","24_90":"qchatGetChannelCategoryMemberRole","24_126":"qchatCheckPermissions"},lp={channelRole:{serverId:1,roleId:2,parentRoleId:3,channelId:4,name:5,icon:6,ext:7,auths:8,type:9,createTime:10,updateTime:11},memberRole:{serverId:1,id:2,accid:3,channelId:4,auths:5,createTime:6,updateTime:7,nick:8,avatar:9,ext:10,memberType:11,joinTime:12,inviter:13},antispamTag:{antiSpamBusinessId:1},serverRole:{serverId:1,roleId:2,name:3,icon:4,ext:5,auths:6,type:7,memberCount:8,priority:9,createTime:10,updateTime:11,isMember:12},getRoleByPagesTag:{serverId:1,channelId:2,time:3,limit:4},checkPermissionTag:{serverId:1,channelId:2,auth:3},member:{serverId:1,roleId:2,accid:3,createTime:4,updateTime:5,nick:6,avatar:7,ext:8,type:9,joinTime:10,inviter:11},qchatAddChannelCategoryRoleTag:{categoryId:3,serverId:4,parentRoleId:5},channelCategoryRole:{roleId:1,categoryId:3,serverId:4,parentRoleId:5,type:6,validFlag:7,createTime:8,updateTime:9,auths:10,name:11,icon:12,ext:13},qchatGetChannelCategoryRoleTag:{serverId:1,categoryId:2,timetag:3,limit:4},channelCategoryMemberRole:{id:1,accid:3,categoryId:4,serverId:5,validFlag:6,createTime:7,updateTime:8,auths:9,nick:10,avatar:11,ext:12,memberType:13,joinTime:14,inviter:15},qchatGetChannelCategoryMemberRoleTag:{serverId:1,categoryId:2,timetag:3,limit:4},checkPermissionsTag:{serverId:1,channelId:2,auths:3}},up=function getDeserializeTag(){return invertSerializeMap(lp)},dp=function getCmdConfig(){var t=up();return{qchatCreateServerRole:{service:"qchatRole",sid:24,cid:61,params:[{type:"Property",name:"serverRole",reflectMapper:lp.serverRole},{type:"Property",name:"antispamTag",reflectMapper:lp.antispamTag}],response:[{type:"Property",name:"serverRole",reflectMapper:t.serverRole}]},qchatDeleteServerRole:{service:"qchatRole",sid:24,cid:62,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"}]},qchatUpdateServerRole:{service:"qchatRole",sid:24,cid:63,params:[{type:"Property",name:"updateServerRoleTag",reflectMapper:{serverId:1,roleId:2,name:3,icon:4,ext:5,auths:6,priority:7}},{type:"Property",name:"antispamTag",reflectMapper:lp.antispamTag}],response:[{type:"Property",name:"serverRole",reflectMapper:t.serverRole}]},qchatGetServerRoles:{service:"qchatRole",sid:24,cid:64,params:[{type:"Property",name:"getServerRolesTag",reflectMapper:{serverId:1,timetag:2,limit:3,priority:4,channelId:5,categoryId:6}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:t.serverRole}]},qchatAddChannelRole:{service:"qchatRole",sid:24,cid:65,params:[{type:"Property",name:"channelRole",reflectMapper:lp.channelRole}],response:[{type:"Property",name:"channelRole",reflectMapper:t.channelRole}]},qchatRemoveChannelRole:{service:"qchatRole",sid:24,cid:66,params:[{type:"Long",name:"serverId"},{type:"Long",name:"channelId"},{type:"Long",name:"roleId"}]},qchatUpdateChannelRole:{service:"qchatRole",sid:24,cid:67,params:[{type:"Property",name:"updateChannelRoleTag",reflectMapper:{serverId:1,roleId:2,channelId:3,auths:4}}],response:[{type:"Property",name:"channelRole",reflectMapper:t.channelRole}]},qchatGetChannelRoles:{service:"qchatRole",sid:24,cid:68,params:[{type:"Property",name:"getChannelRolesTag",reflectMapper:{serverId:1,channelId:2,timetag:3,limit:4}}],response:[{type:"PropertyArray",name:"channelRoles",reflectMapper:t.channelRole}]},qchatAddMemberRole:{service:"qchatRole",sid:24,cid:69,params:[{type:"Property",name:"memberRole",reflectMapper:lp.memberRole}],response:[{type:"Property",name:"memberRole",reflectMapper:t.memberRole}]},qchatRemoveMemberRole:{service:"qchatRole",sid:24,cid:70,params:[{type:"Property",name:"memberRole",reflectMapper:lp.memberRole}]},qchatUpdateMemberRole:{service:"qchatRole",sid:24,cid:71,params:[{type:"Property",name:"updateMemberRoleTag",reflectMapper:{serverId:1,accid:2,channelId:3,auths:4}}],response:[{type:"Property",name:"memberRole",reflectMapper:t.memberRole}]},qchatGetMemberRoles:{service:"qchatRole",sid:24,cid:72,params:[{type:"Property",name:"getMemberRolesTag",reflectMapper:{serverId:1,channelId:2,timetag:3,limit:4}}],response:[{type:"PropertyArray",name:"memberRoles",reflectMapper:t.memberRole}]},qchatAddMembersToServerRole:{service:"qchatRole",sid:24,cid:73,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"},{type:"String",name:"accids"}],response:[{type:"Property",name:"accids",reflectMapper:{1:"successAccids",2:"failedAccids"}}]},qchatRemoveMembersFromServerRole:{service:"qchatRole",sid:24,cid:74,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"},{type:"String",name:"accids"}],response:[{type:"Property",name:"accids",reflectMapper:{1:"successAccids",2:"failedAccids"}}]},qchatGetMembersFromServerRole:{service:"qchatRole",sid:24,cid:75,params:[{type:"Property",name:"getMembersFromServerRoleTag",reflectMapper:{serverId:1,roleId:2,timetag:3,accid:4,limit:5}}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.member}]},qchatGetServerRolesByAccid:{service:"qchatRole",sid:24,cid:76,params:[{type:"Property",name:"getServerRolesByAccidTag",reflectMapper:{serverId:1,accid:2,timetag:3,limit:4}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:t.serverRole}]},qchatGetExistingServerRolesByAccids:{service:"qchatRole",sid:24,cid:77,params:[{type:"Long",name:"serverId"},{type:"String",name:"accids"}],response:[{type:"String",name:"serverRoles"}]},qchatGetExistingChannelRolesByServerRoleIds:{service:"qchatRole",sid:24,cid:78,params:[{type:"Long",name:"serverId"},{type:"Long",name:"channelId"},{type:"String",name:"roleIds"}],response:[{type:"PropertyArray",name:"channelRoles",reflectMapper:t.channelRole}]},qchatGetExistingAccidsOfMemberRoles:{service:"qchatRole",sid:24,cid:79,params:[{type:"Long",name:"serverId"},{type:"Long",name:"channelId"},{type:"String",name:"accids"}],response:[{type:"PropertyArray",name:"memberRoles",reflectMapper:t.memberRole}]},qchatUpdateServerRolePriorities:{service:"qchatRole",sid:24,cid:80,params:[{type:"Long",name:"serverId"},{type:"PropertyArray",name:"serverRoles",reflectMapper:{serverId:1,roleId:2,priority:7}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:t.serverRole}]},qchatGetExistingAccidsInServerRole:{service:"qchatRole",sid:24,cid:81,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"},{type:"String",name:"accids"}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.member}]},qchatCheckPermission:{service:"qchatRole",sid:24,cid:82,params:[{type:"Property",name:"checkPermissionTag",reflectMapper:lp.checkPermissionTag}],response:[{type:"Bool",name:"checked"}]},qchatAddChannelCategoryRole:{service:"qchatRole",sid:24,cid:83,params:[{type:"Property",name:"qchatAddChannelCategoryRoleTag",reflectMapper:lp.channelCategoryRole}],response:[{type:"Property",name:"channelCategoryRole",reflectMapper:t.channelCategoryRole}]},qchatRemoveChannelCategoryRole:{service:"qchatRole",sid:24,cid:84,params:[{type:"Long",name:"serverId"},{type:"Long",name:"categoryId"},{type:"Long",name:"roleId"}]},qchatUpdateChannelCategoryRole:{service:"qchatRole",sid:24,cid:85,params:[{type:"Property",name:"qchatUpdateChannelCategoryRoleTag",reflectMapper:lp.channelCategoryRole}],response:[{type:"Property",name:"channelCategoryRole",reflectMapper:t.channelCategoryRole}]},qchatGetChannelCategoryRole:{service:"qchatRole",sid:24,cid:86,params:[{type:"Property",name:"qchatGetChannelCategoryRoleTag",reflectMapper:lp.qchatGetChannelCategoryRoleTag}],response:[{type:"PropertyArray",name:"list",reflectMapper:t.channelCategoryRole}]},qchatAddChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:87,params:[{type:"Property",name:"qchatAddChannelCategoryMemberRoleTag",reflectMapper:lp.channelCategoryMemberRole}],response:[{type:"Property",name:"channelCategoryMemberRole",reflectMapper:t.channelCategoryMemberRole}]},qchatRemoveChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:88,params:[{type:"Long",name:"serverId"},{type:"Long",name:"categoryId"},{type:"String",name:"accid"}]},qchatUpdateChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:89,params:[{type:"Property",name:"qchatUpdateChannelCategoryMemberRoleTag",reflectMapper:lp.channelCategoryMemberRole}],response:[{type:"Property",name:"channelCategoryMemberRole",reflectMapper:t.channelCategoryMemberRole}]},qchatGetChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:90,params:[{type:"Property",name:"qchatGetChannelCategoryMemberRoleTag",reflectMapper:lp.qchatGetChannelCategoryMemberRoleTag}],response:[{type:"PropertyArray",name:"list",reflectMapper:t.channelCategoryMemberRole}]},qchatCheckPermissions:{service:"qchatRole",sid:24,cid:126,params:[{type:"Property",name:"checkPermissionsTag",reflectMapper:lp.checkPermissionsTag}],response:[{type:"PropertyArray",name:"checkPermissionsResult",reflectMapper:{1:"auth",2:"isAllow"}}]}}},pp=function(){function CategoryModuleService(t){this.core=t}var t=CategoryModuleService.prototype;return t.addChannelCategoryRole=function addChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a;return cs.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},parentRoleId:{type:"string",allowEmpty:!1}},t),u.next=3,this.core.sendCmd("qchatAddChannelCategoryRole",{qchatAddChannelCategoryRoleTag:t});case 3:return a=u.sent,u.abrupt("return",formatChannelCategoryRole(a.content.channelCategoryRole));case 5:case"end":return u.stop()}}),_callee,this)})))},t.removeChannelCategoryRole=function removeChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){return cs.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatRemoveChannelCategoryRole",t);case 3:case"end":return a.stop()}}),_callee2,this)})))},t.updateChannelCategoryRole=function updateChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var a;return cs.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},auths:{type:"object"}},t),u.next=3,this.core.sendCmd("qchatUpdateChannelCategoryRole",{qchatUpdateChannelCategoryRoleTag:generatorRoleForCmd(t)});case 3:if(406!==(a=u.sent).raw.code){u.next=6;break}throw new Gl("No update required",{},406);case 6:return u.abrupt("return",formatChannelCategoryRole(a.content.channelCategoryRole));case 7:case"end":return u.stop()}}),_callee3,this)})))},t.getChannelCategoryRole=function getChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a;return cs.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetChannelCategoryRole",{qchatGetChannelCategoryRoleTag:t});case 3:return a=u.sent,u.abrupt("return",(h=a.content.list,zl(h)&&h.length>0?map$6(h).call(h,(function(t){return formatChannelCategoryRole(t)})):[]));case 5:case"end":return u.stop()}var h}),_callee4,this)})))},t.addChannelCategoryMemberRole=function addChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var a;return cs.wrap((function _callee5$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},t),u.next=3,this.core.sendCmd("qchatAddChannelCategoryMemberRole",{qchatAddChannelCategoryMemberRoleTag:t});case 3:return a=u.sent,u.abrupt("return",formatChannelCategoryMemberRole(a.content.channelCategoryMemberRole));case 5:case"end":return u.stop()}}),_callee5,this)})))},t.removeChannelCategoryMemberRole=function removeChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){return cs.wrap((function _callee6$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatRemoveChannelCategoryMemberRole",t);case 3:case"end":return a.stop()}}),_callee6,this)})))},t.updateChannelCategoryMemberRole=function updateChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){var a;return cs.wrap((function _callee7$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},auths:{type:"object"}},t),u.next=3,this.core.sendCmd("qchatUpdateChannelCategoryMemberRole",{qchatUpdateChannelCategoryMemberRoleTag:generatorRoleForCmd(t)});case 3:if(406!==(a=u.sent).raw.code){u.next=6;break}throw new Gl("No update required",{},406);case 6:return u.abrupt("return",formatChannelCategoryMemberRole(a.content.channelCategoryMemberRole));case 7:case"end":return u.stop()}}),_callee7,this)})))},t.getChannelCategoryMemberRole=function getChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){var a;return cs.wrap((function _callee8$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetChannelCategoryMemberRole",{qchatGetChannelCategoryMemberRoleTag:t});case 3:return a=u.sent,u.abrupt("return",(h=a.content.list,zl(h)&&h.length>0?map$6(h).call(h,(function(t){return formatChannelCategoryMemberRole(t)})):[]));case 5:case"end":return u.stop()}var h}),_callee8,this)})))},CategoryModuleService}(),hp=function(t){function QChatRoleService(a){var u;return(u=t.call(this,"qchatRole",a)||this).core=a,registerParser({cmdMap:cp,cmdConfig:dp()}),u.category=new pp(a),u}Tt(QChatRoleService,t);var a=QChatRoleService.prototype;return a.createServerRole=function createServerRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a;return cs.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},name:{type:"string",allowEmpty:!1},priority:{type:"number",min:1},icon:{type:"string",required:!1},ext:{type:"string",required:!1}},t),u.next=3,this.core.sendCmd("qchatCreateServerRole",{serverRole:Ot(Ot({},generatorRoleForCmd(t)),{type:xd.custom}),antispamTag:generateAntispamTag(t)});case 3:return a=u.sent,u.abrupt("return",formatRole(a.content.serverRole));case 5:case"end":return u.stop()}}),_callee,this)})))},a.updateServerRole=function updateServerRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a;return cs.wrap((function _callee2$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},name:{type:"string",required:!1},icon:{type:"string",required:!1},ext:{type:"string",required:!1},priority:{type:"number",required:!1},auths:{type:"object",required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateServerRole",{updateServerRoleTag:generatorRoleForCmd(t),antispamTag:generateAntispamTag(t)});case 3:return a=u.sent,u.abrupt("return",formatRole(a.content.serverRole));case 5:case"end":return u.stop()}}),_callee2,this)})))},a.deleteServerRole=function deleteServerRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){return cs.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatDeleteServerRole",t);case 3:case"end":return a.stop()}}),_callee3,this)})))},a.getServerRoles=function getServerRoles(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a,u,h;return cs.wrap((function _callee4$(m){for(;;)switch(m.prev=m.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},categoryId:{type:"string",allowEmpty:!1,required:!1},limit:{type:"number",min:1,required:!1},priority:{type:"number",required:!1}},t),m.next=3,this.core.sendCmd("qchatGetServerRoles",{getServerRolesTag:generatorRoleForCmd(t)});case 3:return u=m.sent,h=filter(a=u.content.serverRoles).call(a,(function(t){return 1===Kl(t.isMember)})),h=map$6(h).call(h,(function(t){return t.roleId})),m.abrupt("return",{roles:formatRoles(u.content.serverRoles),isMemberRoles:h});case 7:case"end":return m.stop()}}),_callee4,this)})))},a.addChannelRole=function addChannelRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var a;return cs.wrap((function _callee5$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},parentRoleId:{type:"string",allowEmpty:!1}},t),u.next=3,this.core.sendCmd("qchatAddChannelRole",{channelRole:generatorRoleForCmd(t)});case 3:return a=u.sent,u.abrupt("return",formatRole(a.content.channelRole));case 5:case"end":return u.stop()}}),_callee5,this)})))},a.getChannelRoles=function getChannelRoles(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){var a;return cs.wrap((function _callee6$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetChannelRoles",{getChannelRolesTag:t});case 3:return a=u.sent,u.abrupt("return",formatRoles(a.content.channelRoles));case 5:case"end":return u.stop()}}),_callee6,this)})))},a.removeChannelRole=function removeChannelRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){return cs.wrap((function _callee7$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatRemoveChannelRole",t);case 3:case"end":return a.stop()}}),_callee7,this)})))},a.updateChannelRole=function updateChannelRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){var a;return cs.wrap((function _callee8$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},auths:{type:"object",required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateChannelRole",{updateChannelRoleTag:generatorRoleForCmd(t)});case 3:if(406!==(a=u.sent).raw.code){u.next=6;break}throw new Gl("No update required",{},406);case 6:return u.abrupt("return",formatRole(a.content.channelRole));case 7:case"end":return u.stop()}}),_callee8,this)})))},a.addMemberRole=function addMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee9(){var a;return cs.wrap((function _callee9$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},t),u.next=3,this.core.sendCmd("qchatAddMemberRole",{memberRole:t});case 3:return a=u.sent,u.abrupt("return",formatRole(a.content.memberRole));case 5:case"end":return u.stop()}}),_callee9,this)})))},a.getMemberRoles=function getMemberRoles(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee10(){var a;return cs.wrap((function _callee10$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetMemberRoles",{getMemberRolesTag:t});case 3:return a=u.sent,u.abrupt("return",formatRoles(a.content.memberRoles));case 5:case"end":return u.stop()}}),_callee10,this)})))},a.removeMemberRole=function removeMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee11(){return cs.wrap((function _callee11$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatRemoveMemberRole",{memberRole:t});case 3:case"end":return a.stop()}}),_callee11,this)})))},a.updateMemberRole=function updateMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee12(){var a;return cs.wrap((function _callee12$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},auths:{type:"object",required:!1}},t),u.next=3,this.core.sendCmd("qchatUpdateMemberRole",{updateMemberRoleTag:generatorRoleForCmd(t)});case 3:if(406!==(a=u.sent).raw.code){u.next=6;break}throw new Gl("No update required",{},406);case 6:return u.abrupt("return",formatRole(a.content.memberRole));case 7:case"end":return u.stop()}}),_callee12,this)})))},a.addMembersToServerRole=function addMembersToServerRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee13(){var a,u,h;return cs.wrap((function _callee13$(m){for(;;)switch(m.prev=m.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string",min:1}},t),m.next=3,this.core.sendCmd("qchatAddMembersToServerRole",Ot(Ot({},t),{accids:gs(t.accids)}));case 3:return a=m.sent,u=get(a,"content.accids.successAccids"),h=get(a,"content.accids.failedAccids"),m.abrupt("return",{successAccids:u?JSON.parse(u):[],failedAccids:h?JSON.parse(h):[]});case 7:case"end":return m.stop()}}),_callee13,this)})))},a.removeMembersFromServerRole=function removeMembersFromServerRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee14(){var a,u,h;return cs.wrap((function _callee14$(m){for(;;)switch(m.prev=m.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string",min:1}},t),m.next=3,this.core.sendCmd("qchatRemoveMembersFromServerRole",Ot(Ot({},t),{accids:gs(t.accids)}));case 3:return a=m.sent,u=get(a,"content.accids.successAccids"),h=get(a,"content.accids.failedAccids"),m.abrupt("return",{successAccids:u?JSON.parse(u):[],failedAccids:h?JSON.parse(h):[]});case 7:case"end":return m.stop()}}),_callee14,this)})))},a.getMembersFromServerRole=function getMembersFromServerRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee15(){var a;return cs.wrap((function _callee15$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},accid:{type:"string",allowEmpty:!1,required:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetMembersFromServerRole",{getMembersFromServerRoleTag:t});case 3:return a=u.sent,u.abrupt("return",formatRoles(a.content.members));case 5:case"end":return u.stop()}}),_callee15,this)})))},a.getServerRolesByAccid=function getServerRolesByAccid(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee16(){var a;return cs.wrap((function _callee16$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},limit:{type:"number",min:1,required:!1}},t),u.next=3,this.core.sendCmd("qchatGetServerRolesByAccid",{getServerRolesByAccidTag:t});case 3:return a=u.sent,u.abrupt("return",formatRoles(a.content.serverRoles));case 5:case"end":return u.stop()}}),_callee16,this)})))},a.getExistingServerRolesByAccids=function getExistingServerRolesByAccids(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee17(){var a,u,h;return cs.wrap((function _callee17$(m){for(;;)switch(m.prev=m.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string"}},t),m.next=3,this.core.sendCmd("qchatGetExistingServerRolesByAccids",{serverId:t.serverId,accids:gs(t.accids)});case 3:return a=m.sent,m.prev=4,h=JSON.parse(a.content.serverRoles),forEach$1(u=Qi(h)).call(u,(function(t){var a;forEach$1(a=h[t]).call(a,(function(a,u){h[t][u]=deserialize(a,up().serverRole)})),h[t]=formatRoles(h[t])})),m.abrupt("return",h);case 10:throw m.prev=10,m.t0=m.catch(4),this.logger.error("can not parse serverRolesGroupByAccid from "+t.serverId+" and "+t.accids,a.content.serverRoles),m.t0;case 14:case"end":return m.stop()}}),_callee17,this,[[4,10]])})))},a.getExistingChannelRolesByServerRoleIds=function getExistingChannelRolesByServerRoleIds(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee18(){var a;return cs.wrap((function _callee18$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},roleIds:{type:"array",itemType:"string"}},t),u.next=3,this.core.sendCmd("qchatGetExistingChannelRolesByServerRoleIds",{serverId:t.serverId,channelId:t.channelId,roleIds:gs(t.roleIds)});case 3:return a=u.sent,u.abrupt("return",formatRoles(a.content.channelRoles));case 5:case"end":return u.stop()}}),_callee18,this)})))},a.getExistingAccidsOfMemberRoles=function getExistingAccidsOfMemberRoles(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee19(){var a,u;return cs.wrap((function _callee19$(h){for(;;)switch(h.prev=h.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string"}},t),h.next=3,this.core.sendCmd("qchatGetExistingAccidsOfMemberRoles",{serverId:t.serverId,channelId:t.channelId,accids:gs(t.accids)});case 3:return a=h.sent,u=a.content.memberRoles,h.abrupt("return",u&&u.length>0?map$6(u).call(u,(function(t){return t.accid})):[]);case 6:case"end":return h.stop()}}),_callee19,this)})))},a.getExistingAccidsInServerRole=function getExistingAccidsInServerRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee20(){var a,u;return cs.wrap((function _callee20$(h){for(;;)switch(h.prev=h.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string"}},t),h.next=3,this.core.sendCmd("qchatGetExistingAccidsInServerRole",{serverId:t.serverId,roleId:t.roleId,accids:gs(t.accids)});case 3:return a=h.sent,u=a.content.members,h.abrupt("return",u&&u.length>0?map$6(u).call(u,(function(t){return t.accid})):[]);case 6:case"end":return h.stop()}}),_callee20,this)})))},a.updateServerRolePriorities=function updateServerRolePriorities(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee21(){var a,u;return cs.wrap((function _callee21$(h){for(;;)switch(h.prev=h.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},serverRoles:{type:"array"}},t),h.next=3,this.core.sendCmd("qchatUpdateServerRolePriorities",{serverId:t.serverId,serverRoles:map$6(a=t.serverRoles).call(a,(function(t){return{serverId:t.serverId,roleId:t.roleId,priority:t.priority}}))});case 3:return u=h.sent,h.abrupt("return",formatRoles(u.content.serverRoles));case 5:case"end":return h.stop()}}),_callee21,this)})))},a.checkPermission=function checkPermission(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee22(){var a;return cs.wrap((function _callee22$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},auth:{type:"string"}},t),u.next=3,this.core.sendCmd("qchatCheckPermission",{checkPermissionTag:Ot(Ot({},t),{auth:Ld[t.auth]||t.auth})});case 3:return a=u.sent,u.abrupt("return",a.content.checked);case 5:case"end":return u.stop()}}),_callee22,this)})))},a.addChannelCategoryRole=function addChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee23(){return cs.wrap((function _callee23$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.addChannelCategoryRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee23,this)})))},a.removeChannelCategoryRole=function removeChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee24(){return cs.wrap((function _callee24$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.removeChannelCategoryRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee24,this)})))},a.updateChannelCategoryRole=function updateChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee25(){return cs.wrap((function _callee25$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.updateChannelCategoryRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee25,this)})))},a.getChannelCategoryRole=function getChannelCategoryRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee26(){return cs.wrap((function _callee26$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.getChannelCategoryRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee26,this)})))},a.addChannelCategoryMemberRole=function addChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee27(){return cs.wrap((function _callee27$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.addChannelCategoryMemberRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee27,this)})))},a.removeChannelCategoryMemberRole=function removeChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee28(){return cs.wrap((function _callee28$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.removeChannelCategoryMemberRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee28,this)})))},a.updateChannelCategoryMemberRole=function updateChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee29(){return cs.wrap((function _callee29$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.updateChannelCategoryMemberRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee29,this)})))},a.getChannelCategoryMemberRole=function getChannelCategoryMemberRole(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee30(){return cs.wrap((function _callee30$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.category.getChannelCategoryMemberRole(t);case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee30,this)})))},a.checkPermissions=function checkPermissions(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee31(){var a,u,h,m;return cs.wrap((function _callee31$(g){for(;;)switch(g.prev=g.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},auths:{type:"array",itemType:"string"}},t),this.core.logger.log("qchatRole::checkPermission options is",t),g.next=4,this.core.sendCmd("qchatCheckPermissions",{checkPermissionsTag:Ot(Ot({},t),{auths:gs(map$6(a=t.auths).call(a,(function(t){return Ld[t]||t})))})});case 4:return h=g.sent,this.core.logger.log("qchatRole::checkPermission result is",h.content),m={},forEach$1(u=h.content.checkPermissionsResult).call(u,(function(t){m[t.auth]=t.isAllow})),this.core.logger.log("qchatRole::checkPermission auths is",m),g.abrupt("return",formatRoleAuths(m));case 10:case"end":return g.stop()}}),_callee31,this)})))},QChatRoleService}(ad),mp={"24_10":"qchatSendMsg","24_11":"qchatOnMsg","24_12":"qchatOnRecvUnreadInfo","24_13":"qchatSendCustomSysMsg","24_14":"qchatOnSysMsg","24_16":"qchatGetHistoryMsg","24_17":"qchatMarkMessageRead","24_18":"qchatMultiSyncMessageRead","24_22":"qchatUpdateSystemNotification","24_23":"qchatMultiSyncSystemNotificationUpdate","24_24":"qchatSyncSystemNotification","24_25":"qchatUpdateMessage","24_26":"qchatRecvMessageUpdate","24_28":"qchatMarkSysMsgRead","24_94":"qchatMessageSearchByPage","24_100":"qchatGetMessageHistoryByIds","24_101":"qchatGetThreadMessages","24_102":"qchatUpdateQuickComment","24_103":"qchatGetQuickComments","24_108":"qchatGetThreadRootMessagesMeta","24_121":"qchatGetLastMessageOfChannels","24_127":"qchatGetMentionedMeMessages","25_6":"qchatMultiSyncServersMessageRead"},fp={getHistoryMsgTag:{serverId:1,channelId:2,beginTime:3,endTime:4,excludeMsgId:5,limit:6,reverse:7},getThreadHistoryMsgTag:{beginTime:1,endTime:2,excludeMsgId:3,limit:4,reverse:5},qchatMsgTag:{serverId:1,channelId:2,fromAccount:3,fromClientType:4,fromDeviceId:5,fromNick:6,time:7,updateTime:8,type:9,body:10,attach:11,ext:12,msgIdClient:13,msgIdServer:14,resendFlag:15,status:16,pushPayload:17,pushContent:18,mentionAccids:19,mentionAll:20,env:21,callbackExt:22,replyMsgFromAccount:23,replyMsgTime:24,replyMsgIdServer:25,replyMsgIdClient:26,threadMsgFromAccount:27,threadMsgTime:28,threadMsgIdServer:29,threadMsgIdClient:30,useCustomContent:31,antiSpamContent:32,antiSpamBusinessId:33,antiSpamUsingYidun:34,yidunCallback:35,yidunAntiCheating:36,yidunAntiSpamExt:37,yidunAntiSpamRes:38,mentionRoleIds:41,accidsOfMentionedRoles:42,updateContent:39,updateOperatorInfo:40,subType:61,historyEnable:100,pushEnable:101,needBadge:102,needPushNick:103,notifyReason:104,routeEnable:105,isAntispam:106},sysMsg:{toType:1,serverId:2,channelId:3,toAccids:4,fromAccount:5,fromClientType:6,fromDeviceId:7,fromNick:8,time:9,updateTime:10,type:11,msgIdClient:12,msgIdServer:13,body:14,attach:15,ext:16,resendFlag:17,status:18,pushPayload:19,pushContent:20,env:21,callbackExt:22,persistEnable:100,pushEnable:101,needBadge:102,needPushNick:103,routeEnable:104},qchatMsgUpdateTag:{operatorAccount:1,operatorClientType:2,ps:3,ext:4,pushContent:5,pushPayload:6,env:7,routeEnable:100},markMsgReadTag:{serverId:1,channelId:2,time:3},qchatQuickCommentRequestTag:{serverId:1,channelId:2,fromAccount:3,msgIdServer:4,time:5,type:6,opeType:7,opeAccid:8},qchatQuickCommentQueryTag:{serverId:1,channelId:2,msgIdServerList:3},qchatGetLastMessageOfChannelsTag:{serverId:1,channelIdList:2},qchatMultiSyncServersMessageReadTag:{successServerIds:1,failServerIds:2,ackTimestamp:3},qchatMessageSearchByPageTag:{keyword:1,serverId:2,channelId:3,fromAccid:4,fromTime:5,toTime:6,msgTypes:7,subTypes:8,includeSelf:9,order:10,limit:11,sort:12,cursor:13},qchatPageQueryTag:{hasMore:1,nextTimetag:2,cursor:3},unreadInfo:id},gp=function getDeserializeTag(){return invertSerializeMap(fp)},vp=function getCmdConfig(){var t=gp();return{qchatSendMsg:{service:"qchatMsg",sid:24,cid:10,params:[{type:"Property",name:"qchatMsg",reflectMapper:fp.qchatMsgTag}],response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatOnMsg:{service:"qchatMsg",sid:24,cid:11,response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatOnRecvUnreadInfo:{service:"qchatMsg",sid:24,cid:12,response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatSendCustomSysMsg:{service:"qchatMsg",sid:24,cid:13,params:[{type:"Property",name:"sysMsg",reflectMapper:fp.sysMsg}],response:[{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatOnSysMsg:{service:"qchatMsg",sid:24,cid:14,response:[{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatGetHistoryMsg:{service:"qchatMsg",sid:24,cid:16,params:[{type:"Property",name:"getHistoryMsgTag",reflectMapper:fp.getHistoryMsgTag}],response:[{type:"PropertyArray",name:"qchatMsgs",reflectMapper:t.qchatMsgTag}]},qchatUpdateMessage:{service:"qchatMsg",sid:24,cid:25,params:[{type:"Property",name:"qchatMsgUpdateTag",reflectMapper:fp.qchatMsgUpdateTag},{type:"Property",name:"qchatMsg",reflectMapper:fp.qchatMsgTag}],response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatRecvMessageUpdate:{service:"qchatMsg",sid:24,cid:26,response:[{type:"Property",name:"qchatMsgUpdateInfo",reflectMapper:t.qchatMsgUpdateTag},{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatMarkMessageRead:{sid:24,cid:17,service:"qchatMsg",params:[{type:"Property",name:"markMsgReadTag",reflectMapper:fp.markMsgReadTag}],response:[{type:"Property",name:"unreadInfo",reflectMapper:t.unreadInfo}]},qchatMultiSyncMessageRead:{sid:24,cid:18,service:"qchatMsg",response:[{type:"Property",name:"unreadInfo",reflectMapper:t.unreadInfo}]},qchatUpdateSystemNotification:{service:"qchatMsg",sid:24,cid:22,params:[{type:"Property",name:"qchatMsgUpdateTag",reflectMapper:fp.qchatMsgUpdateTag},{type:"Property",name:"sysMsg",reflectMapper:fp.sysMsg}],response:[{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatMultiSyncSystemNotificationUpdate:{service:"qchatMsg",sid:24,cid:23,response:[{type:"Property",name:"qchatMsgUpdateTag",reflectMapper:t.qchatMsgUpdateTag},{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatSyncSystemNotification:{service:"qchatMsg",sid:24,cid:24,response:[{type:"PropertyArray",name:"systemNotifications",reflectMapper:t.sysMsg}]},qchatMarkSysMsgRead:{service:"qchatMsg",sid:24,cid:28,params:[{type:"PropertyArray",name:"sysMsgs",reflectMapper:fp.sysMsg}]},qchatMessageSearchByPage:{service:"qchatMsg",sid:24,cid:94,params:[{type:"Property",name:"qchatMessageSearchByPageTag",reflectMapper:fp.qchatMessageSearchByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:t.qchatPageQueryTag},{type:"PropertyArray",name:"datas",reflectMapper:t.qchatMsgTag}]},qchatGetMessageHistoryByIds:{service:"qchatMsg",sid:24,cid:100,params:[{type:"Property",name:"channelInfo",reflectMapper:{serverId:1,channelId:2}},{type:"PropertyArray",name:"messageReferList",reflectMapper:{msgIdServer:1,time:2}}],response:[{type:"PropertyArray",name:"qchatMsgs",reflectMapper:t.qchatMsgTag}]},qchatGetThreadMessages:{service:"qchatMsg",sid:24,cid:101,params:[{type:"Property",name:"qchatMsg",reflectMapper:fp.qchatMsgTag},{type:"Property",name:"getThreadHistoryMsgTag",reflectMapper:fp.getThreadHistoryMsgTag}],response:[{type:"Property",name:"thread",reflectMapper:t.qchatMsgTag},{type:"Property",name:"threadInfo",reflectMapper:{1:"messageCount",2:"lastMessageTimestamp"}},{type:"PropertyArray",name:"qchatMsgs",reflectMapper:t.qchatMsgTag}]},qchatUpdateQuickComment:{service:"qchatMsg",sid:24,cid:102,params:[{type:"Property",name:"tag",reflectMapper:fp.qchatQuickCommentRequestTag}]},qchatGetQuickComments:{service:"qchatMsg",sid:24,cid:103,params:[{type:"Property",name:"tag",reflectMapper:fp.qchatQuickCommentQueryTag}],response:[{type:"PropertyArray",name:"quickCommentResponse",reflectMapper:{1:"serverId",2:"channelId",3:"msgIdServer",4:"totalCount",5:"lastUpdateTime",6:"details"}}]},qchatGetThreadRootMessagesMeta:{service:"qchatMsg",sid:24,cid:108,params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,channelId:2}},{type:"PropertyArray",name:"qchatMsgs",reflectMapper:{time:7,msgIdServer:14}}],response:[{type:"PropertyArray",name:"metas",reflectMapper:{1:"total",2:"timestamp",3:"msgIdServer",4:"msgTime"}}]},qchatGetLastMessageOfChannels:{service:"qchatMsg",sid:24,cid:121,params:[{type:"Property",name:"tag",reflectMapper:fp.qchatGetLastMessageOfChannelsTag}],response:[{type:"PropertyArray",name:"msgs",reflectMapper:t.qchatMsgTag}]},qchatMultiSyncServersMessageRead:{service:"qchatMsg",sid:25,cid:6,response:[{type:"Property",name:"qchatMultiSyncServersMessageReadTag",reflectMapper:t.qchatMultiSyncServersMessageReadTag}]},qchatGetMentionedMeMessages:{service:"qchatMsg",sid:24,cid:127,params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,channelId:2,timestamp:3,limit:4}}],response:[{type:"Property",name:"page",reflectMapper:t.qchatPageQueryTag},{type:"PropertyArray",name:"datas",reflectMapper:t.qchatMsgTag}]}}};function pick(t,a){t=t||{};var u={};return forEach$1(a=a||[]).call(a,(function(a){void 0!==t[a]&&(u[a]=t[a])})),u}!function(t){t[t.Android=1]="Android",t[t.iOS=2]="iOS",t[t.PC=4]="PC",t[t.WindowsPhone=8]="WindowsPhone",t[t.Web=16]="Web",t[t.Server=32]="Server",t[t.Mac=64]="Mac",t[t.HarmonyOS=65]="HarmonyOS"}(op||(op={}));var _p=["image","audio","video","file"],yp={time:{type:"number"},updateTime:{type:"number"},resendFlag:{type:"boolean"},persistEnable:{type:"boolean"},routeEnable:{type:"boolean"},pushEnable:{type:"boolean"},needBadge:{type:"boolean"},needPushNick:{type:"boolean"},type:{type:"enum",values:Jd},fromClientType:{type:"enum",values:op},status:{type:"number"},toAccids:{type:"object"},toType:{type:"number"},attach:{type:"object"},pushPayload:{type:"object"}};function generatorSysMsgForCmd(t,a){var u=Ot({},t),h=Ot({type:a||Jd.custom},formatReverse(yp,u));return h.serverId&&h.channelId&&h.toAccids?h.toType=4:h.serverId&&h.toAccids?h.toType=3:h.serverId&&h.channelId?h.toType=2:h.serverId?h.toType=1:h.toAccids?h.toType=5:h.toType=0,h}function generatorSysMsgMarkersForCmd(t){var a=pick(t,["msgIdServer","type"]);return formatReverse(yp,a)}var Ep=((ip={})[Jd.channelUpdateWhiteBlackIdentify]=function(t){var a=cd();return t.notify=formatUpdateWhiteBlackRole(deserialize(t.notify,a.qchatUpdateWhiteBlackRoleTag)),t},ip[Jd.channelUpdateWhiteBlackIdentifyUser]=function(t){var a=cd();return t.notify=function formatUpdateWhiteBlackMembers(t){return format(Ad,t)}(deserialize(t.notify,a.qchatUpdateWhiteBlackMembersTag)),t},ip[Jd.updateQuickComment]=function(t){var a=gp();return t.notify=function formatQuickCommentRequest(t){return format(Sp,t)}(deserialize(t.notify,a.qchatQuickCommentRequestTag)),t},ip[Jd.channelCategoryCreate]=function(t){var a=cd();return t.categoryInfo=formatChannelCategory(deserialize(t.categoryInfo,a.QChatChannelCategoryInfo)),t},ip[Jd.channelCategoryUpdate]=function(t){var a=cd();return t.categoryInfo=formatChannelCategory(deserialize(t.categoryInfo,a.QChatChannelCategoryInfo)),t},ip[Jd.channelCategoryUpdateWhiteBlackIdentify]=function(t){var a=cd();return t.notify=formatUpdateWhiteBlackRole(deserialize(t.notify,a.qchatUpdateChannelCategoryWhiteBlackRoleTag)),t},ip[Jd.channelCategoryUpdateWhiteBlackIdentifyUser]=function(t){var a=cd();return t.notify=formatUpdateWhiteBlackRole(deserialize(t.notify,a.qchatUpdateChannelCategoryWhiteBlackMembersTag)),t},ip[Jd.serverIdentifyAdd]=function(t){var a=up();return t.serverIdentifyInfo=formatRole(deserialize(t.serverIdentifyInfo,a.serverRole)),t},ip[Jd.serverIdentifyRemove]=function(t){var a=up();return t.serverIdentifyInfo=formatRole(deserialize(t.serverIdentifyInfo,a.serverRole)),t},ip[Jd.serverIdentifyUpdate]=function(t){var a=up();return t.serverIdentifyInfo=formatRole(deserialize(t.serverIdentifyInfo,a.serverRole)),t},ip[Jd.channelIdentifyUpdate]=function(t){var a=up();return t.channelIdentifyInfo=formatRole(deserialize(t.channelIdentifyInfo,a.channelRole)),t},ip[Jd.userIdentifyUpdate]=function(t){var a=up();return t.userIdentifyInfo=formatRole(deserialize(t.userIdentifyInfo,a.memberRole)),t},ip[Jd.myMemberInfoUpdated]=function(t){var a,u=get(t,"userInfo.name"),h=get(t,"userInfo.icon");return t.updatedInfos=map$6(a=t.reuseServers).call(a,(function(t){var a={serverId:t.serverId};return 1==(1&t.bits)&&"string"==typeof u&&Ot(a,{nickChanged:!0,nick:u}),2==(2&t.bits)&&"string"==typeof h&&Ot(a,{avatarChanged:!0,avatar:h}),a})),delete t.userInfo,delete t.reuseServers,t},ip);function formatSystemNotification(t,a){var u=format(yp,t);if(!u.attach)return u;var h=pd(),m=cd();if(u.attach.serverInfo&&(u.attach.serverInfo=formatServer(deserialize(u.attach.serverInfo,h.serverInfo))),u.attach.channelInfo&&(u.attach.channelInfo=formatChannel(deserialize(u.attach.channelInfo,m.channelInfo))),u.attach.serverMember&&(u.attach.serverMember=formatMember$1(deserialize(u.attach.serverMember,h.memberInfo))),Ep[u.attach.type]&&(u.attach=Ep[u.attach.type](u.attach)),u.attach.updateAuths)try{u.attach.updateAuths=formatRoleAuths(JSON.parse(u.attach.updateAuths))}catch(t){a.error("formatSystemNotification:JSON parse updateAuths error: ",t)}return u.attach.type&&(u.attach.rawType=u.attach.type,u.attach.type=Jd[u.attach.type]),u}var Ip={type:{type:"enum",values:Hd},fromClientType:{type:"enum",values:op},status:{type:"number"},resendFlag:{type:"boolean"},mentionAll:{type:"boolean"},notifyReason:{type:"enum",values:Kd},pushEnable:{type:"boolean"},historyEnable:{type:"boolean"},needBadge:{type:"boolean"},needPushNick:{type:"boolean"},routeEnable:{type:"boolean"},time:{type:"number"},updateTime:{type:"number"},mentionAccids:{type:"object"},mentionRoleIds:{type:"object"},accidsOfMentionedRoles:{type:"object"},attach:{type:"object"},pushPayload:{type:"object"},isAntispam:{type:"boolean"},antiSpamInfo:{useCustomContent:{type:"boolean"},antiSpamContent:{type:"string"},antiSpamBusinessId:{type:"string"},antiSpamUsingYidun:{type:"boolean"},yidunCallback:{type:"string"},yidunAntiCheating:{type:"object"},yidunAntiSpamExt:{type:"object"},yidunAntiSpamRes:{type:"string"}},replyRefer:{fromAccount:{type:"string",rawKey:"replyMsgFromAccount"},time:{type:"number",rawKey:"replyMsgTime"},msgIdServer:{type:"string",rawKey:"replyMsgIdServer"},msgIdClient:{type:"string",rawKey:"replyMsgIdClient"}},threadRefer:{fromAccount:{type:"string",rawKey:"threadMsgFromAccount"},time:{type:"number",rawKey:"threadMsgTime"},msgIdServer:{type:"string",rawKey:"threadMsgIdServer"},msgIdClient:{type:"string",rawKey:"threadMsgIdClient"}}};function generatorMsgForCmd(t){t.onSendBefore,t.onUploadStart,t.onUploadDone,t.onUploadProgress;var a=__rest(t,["onSendBefore","onUploadStart","onUploadDone","onUploadProgress"]),u=formatReverse(Ip,a);if(u.msgIdClient=t.resendFlag?t.msgIdClient:Xl(),!u.msgIdClient)throw new Vl("msgIdClient is required for resend a message","msgIdClient","required");return t.replyMessage&&t.replyMessage.msgIdServer&&(u.replyMsgFromAccount=t.replyMessage.fromAccount,u.replyMsgTime=+t.replyMessage.time,u.replyMsgIdServer=t.replyMessage.msgIdServer,u.replyMsgIdClient=t.replyMessage.msgIdClient,t.replyMessage.threadRefer&&t.replyMessage.threadRefer.msgIdServer?(u.threadMsgFromAccount=t.replyMessage.threadRefer.fromAccount,u.threadMsgTime=+t.replyMessage.threadRefer.time,u.threadMsgIdServer=t.replyMessage.threadRefer.msgIdServer,u.threadMsgIdClient=t.replyMessage.threadRefer.msgIdClient):(u.threadMsgFromAccount=t.replyMessage.fromAccount,u.threadMsgTime=+t.replyMessage.time,u.threadMsgIdServer=t.replyMessage.msgIdServer,u.threadMsgIdClient=t.replyMessage.msgIdClient),delete u.replyMessage),u}function formatMsgOperatorInfo(t){return format({operatorClientType:{type:"enum",values:op},pushPayload:{type:"object"}},t)}function formatMsg(t,a,u){var h,m;return void 0===a&&(a={}),__awaiter(this,void 0,void 0,cs.mark((function _callee(){var g,E,I,M;return cs.wrap((function _callee$(S){for(;;)switch(S.prev=S.next){case 0:return(g=format(Ip,t)).deliveryStatus=a.deliveryStatus?Qd[a.deliveryStatus]:Qd[Qd.success],a.time&&(g.time=a.time),E=gp(),g.updateContent&&(I=format({status:{type:"number"}},I=deserialize(I=JSON.parse(g.updateContent),E.qchatMsgTag)),g.updateContent=I),g.updateOperatorInfo&&(M=formatMsgOperatorInfo(deserialize(M=JSON.parse(g.updateOperatorInfo),E.qchatMsgUpdateTag)),g.updateOperatorInfo=M),""===(null===(h=null==g?void 0:g.threadRefer)||void 0===h?void 0:h.fromAccount)&&delete g.threadRefer,""===(null===(m=null==g?void 0:g.replyRefer)||void 0===m?void 0:m.fromAccount)&&delete g.replyRefer,g.attach&&a.attachUrl&&(g.attach.url=a.attachUrl),S.next=11,formatMsgAttach(g,u);case 11:return S.abrupt("return",S.sent);case 12:case"end":return S.stop()}}),_callee)})))}function formatMsgAttach(t,a){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var u;return cs.wrap((function _callee2$(h){for(;;)switch(h.prev=h.next){case 0:if(includes(_p).call(_p,t.type)&&t.attach&&t.attach.url){h.next=2;break}return h.abrupt("return",t);case 2:if(a&&a.cloudStorage&&"function"==typeof a.cloudStorage.getPrivateUrl&&"function"==typeof a.cloudStorage.getOriginUrl){h.next=4;break}return h.abrupt("return",t);case 4:if(!(indexOf(u=t.attach.url).call(u,"_im_url=1")<0)){h.next=7;break}return t.attach.url=a.cloudStorage.getPrivateUrl(t.attach.url),h.abrupt("return",t);case 7:return h.prev=7,h.next=10,a.cloudStorage.getOriginUrl(t.attach.url);case 10:t.attach.url=h.sent,h.next=16;break;case 13:throw h.prev=13,h.t0=h.catch(7),new Vl('url "'+t.attach.url+'" parse error',"message.attach.url","parse error");case 16:return h.abrupt("return",t);case 17:case"end":return h.stop()}}),_callee2,null,[[7,13]])})))}function formatMsgs(t,a,u){return void 0===a&&(a={}),__awaiter(this,void 0,void 0,cs.mark((function _callee3(){var h;return cs.wrap((function _callee3$(m){for(;;)switch(m.prev=m.next){case 0:if(zl(t)&&t.length>0){m.next=2;break}return m.abrupt("return",[]);case 2:return h=map$6(t).call(t,(function(t){return formatMsg(t,a,u)})),m.next=5,za.all(h);case 5:return m.abrupt("return",m.sent);case 6:case"end":return m.stop()}}),_callee3)})))}var Mp={totalCount:{type:"number"},lastUpdateTime:{type:"number"},details:{type:"object"}};function formatQuickComments(t){var a=map$6(t).call(t,(function(t){var a,u=format(Mp,t);return u.details=map$6(a=u.details).call(a,(function(t){var a=t.createTime?Kl(t.createTime):0;return a=isNaN(a)?0:a,{count:t.count,hasSelf:t.self,severalAccids:t.topN,type:t.type,createTime:a}})),u})),u={};return forEach$1(a).call(a,(function(t){return u[t.msgIdServer]=t})),u}var Sp={type:{type:"number"},time:{type:"number"},opeType:{type:"number"}};var Cp={includeSelf:{type:"number"},order:{type:"enum",values:Zu},sort:{type:"enum",values:$d},msgTypes:{type:"object"},subTypes:{type:"object"}};var Tp={hasMore:{type:"boolean"},nextTimetag:{type:"number"},cursor:{type:"string"}};var bp={6:function _(t){return this.core.qchatChannel.subscribeForVisitorService.deleteAutoSetInServerId(t.serverId),!0},16:function _(t){return this.core.qchatChannel.subscribeForVisitorService.deleteAutoSetInChannel(t.serverId,t.channelId),!0},26:function _(t){var a=get(t,"attach.addAccids");return a&&includes(a).call(a,this.core.account)&&this.core.eventBus.emit("qchatChannel/serverIdentifyChange",t),!0},27:function _(t){var a=get(t,"attach.deleteAccids");return a&&includes(a).call(a,this.core.account)&&this.core.eventBus.emit("qchatChannel/serverIdentifyChange",t),!0},31:function _(t){var a,u;return 1===t.attach.event?(null===(u=null===(a=this.core.qchatChannel)||void 0===a?void 0:a.config)||void 0===u?void 0:u.autoSubscribe)&&this.core.qchatChannel.subscribeChannel({type:1,opeType:1,channels:[{serverId:t.serverId,channelId:t.channelId}],isInternalTrigger:!0}):2===t.attach.event&&(this.core.eventBus.emit("qchatChannel/autoUnSubscribe",t),this.core.eventBus.emit("qchatMedia/serverOrChannelLeave",t)),!0},32:function _(t){var a,u;return 2===t.attach.event?(this.core.eventBus.emit("qchatChannel/autoUnSubscribe",t),this.core.eventBus.emit("qchatMedia/serverOrChannelLeave",t)):1===t.attach.event&&(this.core.qchatChannel.subscribeForVisitorService.deleteServer(t.serverId),(null===(u=null===(a=this.core.qchatChannel)||void 0===a?void 0:a.config)||void 0===u?void 0:u.autoSubscribe)&&this.core.qchatServer.subscribeServer({type:4,opeType:1,servers:[{serverId:t.serverId}],isInternalTrigger:!0})),!0},34:function _(t){return 2===t.attach.event&&this.core.qchatChannel.subscribeForVisitorService.unSubscribeChannel(t.serverId,t.channelId),!0},101:function _(t){var a=pick(t,["serverId","channelId","ext","fromAccount","fromNick","time"]);return this.logger.log("qchat on recvTypingEvent: ",a),this.core.emit("recvTypingEvent",a),this.core.qchatMsg.emit("recvTypingEvent",a),!1}},Rp=function(){function NotificationModuleService(t){this.core=t,this.logger=t.logger}var t=NotificationModuleService.prototype;return t.sendSystemNotification=function sendSystemNotification(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a,u;return cs.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("qchatSendCustomSysMsg",{sysMsg:generatorSysMsgForCmd(t)});case 2:return a=h.sent,u=formatSystemNotification(a.content.sysMsg,this.logger),this.logger.getDebugMode()?this.logger.debug("sendCustomSysMsg success",u):this.logger.log("sendCustomSysMsg success",u.serverId,u.channelId,u.msgIdServer),h.abrupt("return",u);case 6:case"end":return h.stop()}}),_callee,this)})))},t.updateSystemNotification=function updateSystemNotification(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a,u,h,m,g;return cs.wrap((function _callee2$(E){for(;;)switch(E.prev=E.next){case 0:return a=t.systemNotification,u=__rest(t,["systemNotification"]),h=pick(a,["msgIdServer","type","body","ext","status"]),m=generatorSysMsgForCmd(h),E.next=5,this.core.sendCmd("qchatUpdateSystemNotification",{sysMsg:m,qchatMsgUpdateTag:Ot(Ot({},u),{operatorAccount:this.core.account,operatorClientType:op.Web,pushPayload:gs(t.pushPayload),routeEnable:gd.boolean(t,"routeEnable")})});case 5:return g=E.sent,E.abrupt("return",formatSystemNotification(g.content.sysMsg,this.logger));case 7:case"end":return E.stop()}}),_callee2,this)})))},t.markSystemNotificationsRead=function markSystemNotificationsRead(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var a;return cs.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,this.core.sendCmd("qchatMarkSysMsgRead",{sysMsgs:map$6(a=t.systemNotifications).call(a,(function(t){return generatorSysMsgMarkersForCmd(t)}))});case 2:case"end":return u.stop()}}),_callee3,this)})))},t.onSysMsg=function onSysMsg(t){var a=t.content.sysMsg;if(a){var u=formatSystemNotification(a,this.core.logger),h=bp[Jd[u.type]];if(h)if(!1===h.call(this,u))return;var m={feature:"default",systemNotifications:[u]};this.logger.log("qchat on systemNotification: ",m),this.core.emit("systemNotification",m),this.core.qchatMsg.emit("systemNotification",m)}else this.core.logger.warn("No sysMsg in onSysMsg packet")},t.onMultiSysMsg=function onMultiSysMsg(t){var a=t.content.sysMsg;if(a){var u=formatSystemNotification(a,this.logger);this.core.emit("systemNotificationUpdate",u),this.core.qchatMsg.emit("systemNotificationUpdate",u)}},t.onSyncSysMsg=function onSyncSysMsg(t){var a=this,u=t.content.systemNotifications;if(u&&u.length>0){var h=map$6(u).call(u,(function(t){return formatSystemNotification(t,a.logger)}));this.core.emit("systemNotification",{feature:"sync",systemNotifications:h}),this.core.qchatMsg.emit("systemNotification",{feature:"sync",systemNotifications:h})}else this.logger.warn("sync system notification not exist")},NotificationModuleService}(),Ap=["image","audio","video","file"],Np=function(){function MessageModuleService(t){var a=this;this.markMessageRead=throttle((function(t){return __awaiter(a,void 0,void 0,cs.mark((function _callee(){var a,u;return cs.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("qchatMarkMessageRead",{markMsgReadTag:t});case 2:a=h.sent,u=formatUnreadInfo(a.content.unreadInfo),this.core.eventBus.emit("qchatChannel/updateUnreads",[u]);case 5:case"end":return h.stop()}}),_callee,this)})))}),200),this.core=t,this.logger=t.logger}var t=MessageModuleService.prototype;return t.sendMessage=function sendMessage(t){var a,u,h,m;return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var g,E,I,M,S,C;return cs.wrap((function _callee2$(T){for(;;)switch(T.prev=T.next){case 0:if(!(indexOf(Ap).call(Ap,t.type)>-1)){T.next=4;break}return T.next=3,this.doSendFile(t);case 3:t.attach=T.sent;case 4:return g=generatorMsgForCmd(Ot({fromAccount:this.core.account},t)),T.next=7,formatMsg(g,{time:(new Date).getTime(),deliveryStatus:Qd.sending},this.core);case 7:E=T.sent;try{t.onSendBefore&&t.onSendBefore(E)}catch(t){this.logger.error("sendMsg: options.onSendBefore error",t)}return T.prev=9,T.next=12,this.core.sendCmd("qchatSendMsg",{qchatMsg:g});case 12:I=T.sent,T.next=22;break;case 15:return T.prev=15,T.t0=T.catch(9),T.next=19,formatMsg(g,{time:(new Date).getTime(),deliveryStatus:Qd.failed,attachUrl:null===(a=E.attach)||void 0===a?void 0:a.url},this.core);case 19:throw M=T.sent,T.t0.msg=M,T.t0;case 22:return S=I.content,T.next=25,formatMsg(Ot({},S.qchatMsg),{deliveryStatus:Qd.success,attachUrl:null===(u=E.attach)||void 0===u?void 0:u.url},this.core);case 25:if(!(C=T.sent).isAntispam){T.next=29;break}throw{code:10403,msg:C,message:(null===(h=null==C?void 0:C.antiSpamInfo)||void 0===h?void 0:h.yidunAntiSpamRes)||"message has be antispam",isAntispam:!0,yidunAntiSpamRes:(null===(m=null==C?void 0:C.antiSpamInfo)||void 0===m?void 0:m.yidunAntiSpamRes)||""};case 29:return T.abrupt("return",C);case 30:case"end":return T.stop()}}),_callee2,this,[[9,15]])})))},t.doSendFile=function doSendFile(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var a;return cs.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:if(validate({type:{type:"enum",values:["image","audio","video","file"]},attach:{type:"object",rules:{url:{type:"string",allowEmpty:!1}},required:!1},maxSize:{type:"number",min:1,required:!1}},t),a=t.attach){u.next=15;break}if(this.core.cloudStorage&&this.core.cloudStorage.uploadFile){u.next=5;break}throw new Error('Service "cloudStorage" does not exist');case 5:return u.prev=5,u.next=8,this.core.cloudStorage.uploadFile(t);case 8:a=u.sent,u.next=15;break;case 11:throw u.prev=11,u.t0=u.catch(5),this.logger.error("sendFile:: upload File error or abort.",u.t0),u.t0;case 15:return u.abrupt("return",a);case 16:case"end":return u.stop()}}),_callee3,this,[[5,11]])})))},t.resendMessage=function resendMessage(t){var a,u;return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var h,m,g,E,I;return cs.wrap((function _callee4$(M){for(;;)switch(M.prev=M.next){case 0:return(h=generatorMsgForCmd(Ot(Ot({},t),{resendFlag:!0}))).deliveryStatus=Qd.sending,M.prev=2,M.next=5,this.core.sendCmd("qchatSendMsg",{qchatMsg:h});case 5:m=M.sent,M.next=15;break;case 8:return M.prev=8,M.t0=M.catch(2),M.next=12,formatMsg(h,{time:(new Date).getTime(),deliveryStatus:Qd.failed},this.core);case 12:throw g=M.sent,M.t0.msg=g,M.t0;case 15:return E=m.content,M.next=18,formatMsg(Ot({},E.qchatMsg),{deliveryStatus:Qd.success},this.core);case 18:if(!(I=M.sent).isAntispam){M.next=22;break}throw{code:10403,msg:I,message:(null===(a=null==I?void 0:I.antiSpamInfo)||void 0===a?void 0:a.yidunAntiSpamRes)||"message has be antispam",isAntispam:!0,yidunAntiSpamRes:(null===(u=null==I?void 0:I.antiSpamInfo)||void 0===u?void 0:u.yidunAntiSpamRes)||""};case 22:return M.abrupt("return",I);case 23:case"end":return M.stop()}}),_callee4,this,[[2,8]])})))},t.doUpdateMessage=function doUpdateMessage(t){var a,u;return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var h,m,g,E,I;return cs.wrap((function _callee5$(M){for(;;)switch(M.prev=M.next){case 0:return h=t.message,m=__rest(t,["message"]),(g=generatorMsgForCmd(h)).msgIdClient=void 0,g.time=h.time,M.next=6,this.core.sendCmd("qchatUpdateMessage",{qchatMsg:g,qchatMsgUpdateTag:Ot(Ot({},m),{operatorAccount:this.core.account,operatorClientType:op.Web,pushPayload:gs(t.pushPayload),routeEnable:gd.boolean(t,"routeEnable")})});case 6:return E=M.sent,M.next=9,formatMsg(E.content.qchatMsg,{},this.core);case 9:if(I=M.sent,this.core.eventBus.emit("qchatChannel/changeUnread",I,!0),!E.content.qchatMsg.isAntispam){M.next=14;break}throw{code:10403,msg:g,message:(null===(a=E.content.qchatMsg)||void 0===a?void 0:a.yidunAntiSpamRes)||"message has be antispam",isAntispam:!0,yidunAntiSpamRes:(null===(u=E.content.qchatMsg)||void 0===u?void 0:u.yidunAntiSpamRes)||""};case 14:return M.abrupt("return",I);case 15:case"end":return M.stop()}}),_callee5,this)})))},t.getHistoryMessage=function getHistoryMessage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){var a,u,h,m=this;return cs.wrap((function _callee6$(g){for(;;)switch(g.prev=g.next){case 0:return g.next=2,this.core.sendCmd("qchatGetHistoryMsg",{getHistoryMsgTag:Ot(Ot({},t),{reverse:reverse(t)?1:0})});case 2:return u=g.sent,h=u.content,g.next=6,za.all(map$6(a=h.qchatMsgs).call(a,(function(t){return formatMsg(t,{},m.core)})));case 6:return g.abrupt("return",g.sent);case 7:case"end":return g.stop()}}),_callee6,this)})))},t.getMentionedMeMessages=function getMentionedMeMessages(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){var a,u,h,m,g,E=this;return cs.wrap((function _callee7$(I){for(;;)switch(I.prev=I.next){case 0:return I.next=2,this.core.sendCmd("qchatGetMentionedMeMessages",{tag:t});case 2:return u=I.sent,h=u.content,M=h.page,m=format(Tp,M),I.next=7,za.all(map$6(a=h.datas).call(a,(function(t){return formatMsg(t,{},E.core)})));case 7:return g=I.sent,I.abrupt("return",{pageInfo:m,messages:g});case 9:case"end":return I.stop()}var M}),_callee7,this)})))},t.areMentionedMeMessages=function areMentionedMeMessages(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){var a,u,h;return cs.wrap((function _callee8$(m){for(;;)switch(m.prev=m.next){case 0:a={},u=0;case 2:if(!(u<t.length)){m.next=13;break}if((h=t[u]).fromAccount!==this.core.account){m.next=7;break}return a[h.msgIdClient]=!1,m.abrupt("continue",10);case 7:return m.next=9,this.core.qchatChannel.subscribeModuleService.getMentionedFlag(h,!0);case 9:a[h.msgIdClient]=m.sent;case 10:u++,m.next=2;break;case 13:return m.abrupt("return",a);case 14:case"end":return m.stop()}}),_callee8,this)})))},t.getLastMessageOfChannels=function getLastMessageOfChannels(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee9(){var a,u,h;return cs.wrap((function _callee9$(m){for(;;)switch(m.prev=m.next){case 0:return m.next=2,this.core.sendCmd("qchatGetLastMessageOfChannels",{tag:t});case 2:return a=m.sent,u=a.content.msgs,m.next=6,formatMsgs(u,{},this.core);case 6:return h=m.sent,m.abrupt("return",reduce(h).call(h,(function(t,a){return t[a.channelId]=a,t}),{}));case 8:case"end":return m.stop()}}),_callee9,this)})))},t.messageSearchByPage=function messageSearchByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee10(){var a,u,h,m,g;return cs.wrap((function _callee10$(E){for(;;)switch(E.prev=E.next){case 0:return E.next=2,this.core.sendCmd("qchatMessageSearchByPage",{qchatMessageSearchByPageTag:Ot(Ot({},(I=t,formatReverse(Cp,I))),{includeSelf:t.includeSelf?1:""})});case 2:return a=E.sent,u=a.content,h=u.datas,m=u.listQueryTag,E.next=6,formatMsgs(h,{},this.core);case 6:return g=E.sent,E.abrupt("return",{listQueryTag:{hasMore:1==+m.hasMore,nextTimetag:m.nextTimetag?Kl(m.nextTimetag):0,cursor:m.cursor},datas:g});case 8:case"end":return E.stop()}var I}),_callee10,this)})))},t.onMsg=function onMsg(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee11(){var a,u;return cs.wrap((function _callee11$(h){for(;;)switch(h.prev=h.next){case 0:if(a=t.content.qchatMsg){h.next=4;break}return this.logger.warn("No qchatMsg in qchatMsg packet"),h.abrupt("return");case 4:return h.next=6,formatMsg(a,{},this.core);case 6:u=h.sent,this.core.eventBus.emit("qchatChannel/changeUnread",u,!1),this.core.emit("message",u),this.core.qchatMsg.emit("message",u);case 10:case"end":return h.stop()}}),_callee11,this)})))},t.onRecvUnreadInfo=function onRecvUnreadInfo(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee12(){var a,u;return cs.wrap((function _callee12$(h){for(;;)switch(h.prev=h.next){case 0:if(a=t.content.qchatMsg){h.next=3;break}return h.abrupt("return");case 3:return h.next=5,formatMsg(a);case 5:u=h.sent,this.core.eventBus.emit("qchatChannel/changeUnread",u,!1);case 7:case"end":return h.stop()}}),_callee12,this)})))},t.onMultiSyncRead=function onMultiSyncRead(t){var a=t.content.unreadInfo;if(a){var u=formatUnreadInfo(a);this.core.eventBus.emit("qchatChannel/updateUnreads",[u])}},t.onMultiSyncServersRead=function onMultiSyncServersRead(t){var a=t.content.qchatMultiSyncServersMessageReadTag;if(a){var u=formatClearServersUnread(a),h=u.successServerIds,m=u.ackTimestamp;this.core.eventBus.emit("qchatChannel/clearUnreadCountByServers",h,m)}},t.onRecvMsgUpdate=function onRecvMsgUpdate(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee13(){var a,u,h,m;return cs.wrap((function _callee13$(g){for(;;)switch(g.prev=g.next){case 0:if(a=t.content.qchatMsg,u=t.content.qchatMsgUpdateInfo,a){g.next=4;break}return g.abrupt("return");case 4:return g.next=6,formatMsg(a,{},this.core);case 6:h=g.sent,m=formatMsgOperatorInfo(u),this.core.eventBus.emit("qchatChannel/changeUnread",h,!0),this.core.emit("messageUpdate",h,m),this.core.qchatMsg.emit("messageUpdate",h,m);case 11:case"end":return g.stop()}}),_callee13,this)})))},MessageModuleService}(),wp=function(){function ExtendModuleService(t){var a=this;this._sendTypingEvent=throttle((function(t){return __awaiter(a,void 0,void 0,cs.mark((function _callee(){return cs.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("qchatSendCustomSysMsg",{sysMsg:generatorSysMsgForCmd(Ot(Ot({},t),{resendFlag:!1,persistEnable:!1,routeEnable:!1,pushEnable:!1,needBadge:!1,needPushNick:!1}),Jd.msgTyping)});case 2:case"end":return a.stop()}}),_callee,this)})))}),3e3),this.core=t,this.logger=t.logger,this.lastChannelIdInTyping=""}var t=ExtendModuleService.prototype;return t.getMessageHistoryByIds=function getMessageHistoryByIds(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a,u;return cs.wrap((function _callee2$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("qchatGetMessageHistoryByIds",{channelInfo:{serverId:t.serverId,channelId:t.channelId},messageReferList:t.messageReferList});case 2:return u=h.sent,h.abrupt("return",za.all(map$6(a=u.content.qchatMsgs).call(a,(function(t){return formatMsg(t)}))));case 4:case"end":return h.stop()}}),_callee2,this)})))},t.getReferMessages=function getReferMessages(t){var a,u;return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var h;return cs.wrap((function _callee3$(m){for(;;)switch(m.prev=m.next){case 0:if((null===(a=t.message.replyRefer)||void 0===a?void 0:a.msgIdServer)&&(null===(u=t.message.threadRefer)||void 0===u?void 0:u.msgIdServer)){m.next=2;break}throw new Error("Message has no reply");case 2:return m.next=4,this.getMessageHistoryByIds({serverId:t.message.serverId,channelId:t.message.channelId,messageReferList:[t.message.replyRefer,t.message.threadRefer]});case 4:if(h=m.sent,t.referType!==Xd[Xd.all]){m.next=9;break}return m.abrupt("return",{replyMessage:h[0],threadMessage:h[1]});case 9:if(t.referType!==Xd[Xd.reply]){m.next=13;break}return m.abrupt("return",{replyMessage:h[0]});case 13:if(t.referType!==Xd[Xd.thread]){m.next=15;break}return m.abrupt("return",{threadMessage:h[1]});case 15:return m.abrupt("return",{replyMessage:h[0],threadMessage:h[1]});case 16:case"end":return m.stop()}}),_callee3,this)})))},t.getThreadMessages=function getThreadMessages(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var u,h,m,g,E;return cs.wrap((function _callee4$(I){for(;;)switch(I.prev=I.next){case 0:return h=(null===(a=t.message.threadRefer)||void 0===a?void 0:a.msgIdServer)?Ot({serverId:t.message.serverId,channelId:t.message.channelId},t.message.threadRefer):t.message,I.next=3,this.core.sendCmd("qchatGetThreadMessages",{qchatMsg:h,getThreadHistoryMsgTag:Ot(Ot({},t.messageQueryOption),{reverse:gd.boolean(t.messageQueryOption,"reverse")})});case 3:return m=I.sent,I.next=6,formatMsg(m.content.thread);case 6:return g=I.sent,I.next=9,za.all(map$6(u=m.content.qchatMsgs).call(u,(function(t){return formatMsg(t)})));case 9:return E=I.sent,I.abrupt("return",{thread:g,threadInfo:{messageCount:+m.content.threadInfo.messageCount,lastMessageTimestamp:+m.content.threadInfo.lastMessageTimestamp},messages:E});case 11:case"end":return I.stop()}}),_callee4,this)})))},t.getThreadRootMessagesMeta=function getThreadRootMessagesMeta(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var a,u,h,m;return cs.wrap((function _callee5$(g){for(;;)switch(g.prev=g.next){case 0:return a=t.threadRootMessages,u=__rest(t,["threadRootMessages"]),g.next=3,this.core.sendCmd("qchatGetThreadRootMessagesMeta",{qchatMsgs:a,tag:u});case 3:if(h=g.sent,!((m=h.content.metas)&&m.length>0)){g.next=7;break}return g.abrupt("return",map$6(m).call(m,(function(t){return Ot(Ot({},t),{total:Kl(t.total),timestamp:Kl(t.timestamp),msgTime:Kl(t.msgTime)})})));case 7:return g.abrupt("return",[]);case 8:case"end":return g.stop()}}),_callee5,this)})))},t.getQuickComments=function getQuickComments(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){var a,u,h;return cs.wrap((function _callee6$(m){for(;;)switch(m.prev=m.next){case 0:return m.next=2,this.core.sendCmd("qchatGetQuickComments",{tag:{serverId:t.serverId,channelId:t.channelId,msgIdServerList:gs(map$6(a=t.msgList).call(a,(function(t){return t.msgIdServer})))}});case 2:return u=m.sent,h=u.content.quickCommentResponse,m.abrupt("return",formatQuickComments(h));case 5:case"end":return m.stop()}}),_callee6,this)})))},t.updateQuickComment=function updateQuickComment(t,a){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){var u,h,m,g,E,I;return cs.wrap((function _callee7$(M){for(;;)switch(M.prev=M.next){case 0:return validate({type:{type:"number"},commentMessage:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1}}}},t),u=t.commentMessage,h=u.serverId,m=u.channelId,g=u.fromAccount,E=u.msgIdServer,I=u.time,M.next=4,this.core.sendCmd("qchatUpdateQuickComment",{tag:{serverId:h,channelId:m,fromAccount:g,msgIdServer:E,time:I,type:t.type,opeType:a,opeAccid:this.core.account}});case 4:case"end":return M.stop()}}),_callee7,this)})))},t.sendTypingEvent=function sendTypingEvent(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){return cs.wrap((function _callee8$(a){for(;;)switch(a.prev=a.next){case 0:if(t.channelId!==this.lastChannelIdInTyping){a.next=4;break}return this.logger.log("qchatMsg sendTypingEvent throttle"),this._sendTypingEvent(t),a.abrupt("return");case 4:this.lastChannelIdInTyping=t.channelId,this._sendTypingEvent.flush(),this._sendTypingEvent(t);case 7:case"end":return a.stop()}}),_callee8,this)})))},ExtendModuleService}(),Op=function(t){function QChatMsgService(a){var u;return(u=t.call(this,"qchatMsg",a)||this).core=a,u.lastChannelIdInMark="",registerParser({cmdMap:mp,cmdConfig:vp()}),u.notificationModuleService=new Rp(a),u.messageModuleService=new Np(a),u.extendModuleService=new wp(a),u}Tt(QChatMsgService,t);var a=QChatMsgService.prototype;return a.sendMessage=function sendMessage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){return cs.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Hd)},ext:{type:"string",required:!1},mentionAll:{type:"boolean",required:!1},mentionAccids:{type:"array",itemType:"string",required:!1},mentionRoleIds:{type:"array",itemType:"string",required:!1,min:1},historyEnable:{type:"boolean",required:!1},pushEnable:{type:"boolean",required:!1}},t),a.next=3,this.messageModuleService.sendMessage(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee,this)})))},a.updateMessage=function updateMessage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){return cs.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:if(validate({message:{type:"object",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number",min:0},body:{type:"string",required:!1},ext:{type:"string",required:!1}}}},t),!("number"==typeof t.message.status&&t.message.status<1e4)){a.next=3;break}throw new Gl("Status should be greater than or equal to 10000");case 3:return a.next=5,this.messageModuleService.doUpdateMessage(t);case 5:return a.abrupt("return",a.sent);case 6:case"end":return a.stop()}}),_callee2,this)})))},a.resendMessage=function resendMessage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){return cs.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdClient:{type:"string",allowEmpty:!1}},t),a.next=3,this.messageModuleService.resendMessage(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee3,this)})))},a.deleteMessage=function deleteMessage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a;return cs.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return validate({message:{type:"object",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number",min:0}}}},t),(a=JSON.parse(gs(t))).message.status=2,u.next=5,this.messageModuleService.doUpdateMessage(a);case 5:return u.abrupt("return",u.sent);case 6:case"end":return u.stop()}}),_callee4,this)})))},a.revokeMessage=function revokeMessage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var a;return cs.wrap((function _callee5$(u){for(;;)switch(u.prev=u.next){case 0:return validate({message:{type:"object",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number",min:0}}}},t),(a=JSON.parse(gs(t))).message=pick(a.message,["serverId","channelId","msgIdServer","time"]),a.message.status=1,u.next=6,this.messageModuleService.doUpdateMessage(a);case 6:return u.abrupt("return",u.sent);case 7:case"end":return u.stop()}}),_callee5,this)})))},a.markMessageRead=function markMessageRead(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){return cs.wrap((function _callee6$(a){for(;;)switch(a.prev=a.next){case 0:validate({serverId:{type:"string"},channelId:{type:"string"},time:{type:"number",min:0}},t),t.channelId!==this.lastChannelIdInMark&&this.messageModuleService.markMessageRead.flush(),this.lastChannelIdInMark=t.channelId,this.messageModuleService.markMessageRead(t);case 4:case"end":return a.stop()}}),_callee6,this)})))},a.replyMessage=function replyMessage(t){validate({replyMessage:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1}}}},t);var replyMessage=t.replyMessage;if(t.serverId!==replyMessage.serverId||t.channelId!==replyMessage.channelId)throw new Gl("Forbid replying to message from other server, channel");return this.sendMessage(t)},a.getMessageHistoryByIds=function getMessageHistoryByIds(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){return cs.wrap((function _callee7$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},messageReferList:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}}},t),a.next=3,this.extendModuleService.getMessageHistoryByIds(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee7,this)})))},a.getReferMessages=function getReferMessages(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){return cs.wrap((function _callee8$(a){for(;;)switch(a.prev=a.next){case 0:return validate({message:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}},referType:{type:"enum",values:getEnumKeys(Xd),required:!1}},t),a.next=3,this.extendModuleService.getReferMessages(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee8,this)})))},a.getThreadMessages=function getThreadMessages(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee9(){return cs.wrap((function _callee9$(a){for(;;)switch(a.prev=a.next){case 0:return validate({message:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}},messageQueryOption:{type:"object",rules:{beginTime:{type:"number",required:!1},endTime:{type:"number",required:!1},excludeMsgId:{type:"string",required:!1},limit:{type:"number",required:!1},reverse:{type:"boolean",required:!1}}}},t),a.next=3,this.extendModuleService.getThreadMessages(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee9,this)})))},a.getThreadRootMessagesMeta=function getThreadRootMessagesMeta(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee10(){return cs.wrap((function _callee10$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},threadRootMessages:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}}},t),a.next=3,this.extendModuleService.getThreadRootMessagesMeta(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee10,this)})))},a.sendSystemNotification=function sendSystemNotification(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee11(){return cs.wrap((function _callee11$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},attach:{type:"object",required:!1}},t),a.next=3,this.notificationModuleService.sendSystemNotification(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee11,this)})))},a.updateSystemNotification=function updateSystemNotification(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee12(){return cs.wrap((function _callee12$(a){for(;;)switch(a.prev=a.next){case 0:return validate({systemNotification:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Jd)},body:{type:"string",required:!1},ext:{type:"string",required:!1},status:{type:"number",required:!1}}}},t),a.next=3,this.notificationModuleService.updateSystemNotification(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee12,this)})))},a.markSystemNotificationsRead=function markSystemNotificationsRead(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee13(){return cs.wrap((function _callee13$(a){for(;;)switch(a.prev=a.next){case 0:return validate({systemNotifications:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1},type:{type:"string",allowEmpty:!1}},min:1}},t),a.next=3,this.notificationModuleService.markSystemNotificationsRead(t);case 3:case"end":return a.stop()}}),_callee13,this)})))},a.getHistoryMessage=function getHistoryMessage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee14(){return cs.wrap((function _callee14$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},beginTime:{type:"number",min:0,required:!1},endTime:{type:"number",min:0,required:!1},excludeMsgId:{type:"string",allowEmpty:!1,required:!1},limit:{type:"number",min:1,required:!1},reverse:{type:"boolean",required:!1}},t),a.next=3,this.messageModuleService.getHistoryMessage(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee14,this)})))},a.getMentionedMeMessages=function getMentionedMeMessages(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee15(){return cs.wrap((function _callee15$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},timestamp:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}},t),a.next=3,this.messageModuleService.getMentionedMeMessages(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee15,this)})))},a.areMentionedMeMessages=function areMentionedMeMessages(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee16(){var a;return cs.wrap((function _callee16$(u){for(;;)switch(u.prev=u.next){case 0:if(validate({messages:{type:"array",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1}},min:1}},t),every(a=t.messages).call(a,(function(a){return t.messages[0].serverId===a.serverId}))){u.next=4;break}throw new Gl("Different serverId",t,10414);case 4:if(!this.core.qchatChannel.subscribeForVisitorService.isInAutoServers(t.messages[0].serverId)){u.next=7;break}throw new Gl("Not allowed for visitor",t,10403);case 7:return u.next=9,this.messageModuleService.areMentionedMeMessages(t.messages);case 9:return u.abrupt("return",u.sent);case 10:case"end":return u.stop()}}),_callee16,this)})))},a.addQuickComment=function addQuickComment(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee17(){return cs.wrap((function _callee17$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.extendModuleService.updateQuickComment(t,1);case 2:case"end":return a.stop()}}),_callee17,this)})))},a.removeQuickComment=function removeQuickComment(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee18(){return cs.wrap((function _callee18$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.extendModuleService.updateQuickComment(t,2);case 2:case"end":return a.stop()}}),_callee18,this)})))},a.getQuickComments=function getQuickComments(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee19(){return cs.wrap((function _callee19$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgList:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1}},min:1}},t),a.next=3,this.extendModuleService.getQuickComments(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee19,this)})))},a.sendTypingEvent=function sendTypingEvent(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee20(){return cs.wrap((function _callee20$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},ext:{type:"string",required:!1}},t),a.next=3,this.extendModuleService.sendTypingEvent(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee20,this)})))},a.getLastMessageOfChannels=function getLastMessageOfChannels(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee21(){return cs.wrap((function _callee21$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelIdList:{type:"array",itemType:"string"}},t),a.next=3,this.messageModuleService.getLastMessageOfChannels(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee21,this)})))},a.qchatOnMsgHandler=function qchatOnMsgHandler(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee22(){return cs.wrap((function _callee22$(a){for(;;)switch(a.prev=a.next){case 0:this.messageModuleService.onMsg(t);case 1:case"end":return a.stop()}}),_callee22,this)})))},a.qchatOnRecvUnreadInfoHandler=function qchatOnRecvUnreadInfoHandler(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee23(){return cs.wrap((function _callee23$(a){for(;;)switch(a.prev=a.next){case 0:this.messageModuleService.onRecvUnreadInfo(t);case 1:case"end":return a.stop()}}),_callee23,this)})))},a.qchatOnSysMsgHandler=function qchatOnSysMsgHandler(t){this.notificationModuleService.onSysMsg(t)},a.qchatMultiSyncMessageReadHandler=function qchatMultiSyncMessageReadHandler(t){this.messageModuleService.onMultiSyncRead(t)},a.qchatMultiSyncSystemNotificationUpdateHandler=function qchatMultiSyncSystemNotificationUpdateHandler(t){this.notificationModuleService.onMultiSysMsg(t)},a.qchatSyncSystemNotificationHandler=function qchatSyncSystemNotificationHandler(t){this.notificationModuleService.onSyncSysMsg(t)},a.qchatRecvMessageUpdateHandler=function qchatRecvMessageUpdateHandler(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee24(){return cs.wrap((function _callee24$(a){for(;;)switch(a.prev=a.next){case 0:this.messageModuleService.onRecvMsgUpdate(t);case 1:case"end":return a.stop()}}),_callee24,this)})))},a.searchMsgByPage=function searchMsgByPage(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee25(){return cs.wrap((function _callee25$(a){for(;;)switch(a.prev=a.next){case 0:return validate({keyword:{type:"string",allowEmpty:!1,required:!1},serverId:{type:"string",allowEmpty:!1,required:!0},channelId:{type:"string",allowEmpty:!1,required:!1},fromAccid:{type:"string",allowEmpty:!1,required:!1},fromTime:{type:"number",allowEmpty:!1,required:!1},toTime:{type:"number",allowEmpty:!1,required:!1},msgTypes:{type:"array",itemType:"string",allowEmpty:!1,required:!0},subTypes:{type:"array",itemType:"string",allowEmpty:!1,required:!1},includeSelf:{type:"boolean",allowEmpty:!1,required:!1},order:{type:"enum",values:getEnumKeys(Zu),required:!1},limit:{type:"number",min:0,required:!1},sort:{type:"enum",values:getEnumKeys($d),required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},t),a.next=3,this.messageModuleService.messageSearchByPage(t);case 3:return a.abrupt("return",a.sent);case 4:case"end":return a.stop()}}),_callee25,this)})))},a.qchatMultiSyncServersMessageReadHandler=function qchatMultiSyncServersMessageReadHandler(t){this.messageModuleService.onMultiSyncServersRead(t)},QChatMsgService}(ad),xp={serverId:{type:"string"},uid:{type:"string"},accid:{type:"string"},nick:{type:"string"},avatar:{type:"string"},ext:{type:"string"},type:{type:"number"},joinTime:{type:"number"},inviter:{type:"string"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"}},kp={limit:{type:"number"}};function formatMembers(t){return zl(t)&&t.length>0?map$6(t).call(t,(function(t){return function formatMember(t){return format(xp,t)}(t)})):[]}function formatRTCChannelConfig(t){try{t.audio&&(t.audio=JSON.parse(t.audio))}catch(t){throw new ql('result "audio" JSON parse error',{key:"audio"},"JSON parse error")}try{t.video&&(t.video=JSON.parse(t.video))}catch(t){throw new ql('result "video" JSON parse error',{key:"video"},"JSON parse error")}return format(kp,t)}var Pp={"25_1":"qchatGetNeroomToken","25_2":"qchatUpdateRTCChannelConfig","25_3":"qchatGetRTCChannelConfig","25_4":"qchatGetRTCChannelMembers"},Lp={QChatRTCChannelConfigTag:{serverId:1,channelId:2,limit:3,audio:4,video:5},QChatRTCChannelConfigResultTag:{limit:1,audio:2,video:3},QChatGetRTCParams:{serverId:1,channelId:2},qchatGetRTCChannelMembersTag:{serverId:1,channelId:2},memberInfo:{serverId:1,accid:3,nick:4,avatar:5,ext:6,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},QChatGetNeroomTokenResultTag:{token:1,expire:2},QChatGetNeroomTokenParams:{neroomDeviceId:1}},qp=function getCmdConfig(){var t=function getDeserializeTag(){return invertSerializeMap(Lp)}();return{qchatGetNeroomToken:{sid:25,cid:1,service:"qchatMedia",params:[{type:"Property",name:"qchatGetNeroomTokenTag",reflectMapper:Lp.QChatGetNeroomTokenParams}],response:[{type:"Property",name:"neroomToken",reflectMapper:t.QChatGetNeroomTokenResultTag}]},qchatUpdateRTCChannelConfig:{sid:25,cid:2,service:"qchatMedia",params:[{type:"Property",name:"RTCChannelConfig",reflectMapper:Lp.QChatRTCChannelConfigTag}]},qchatGetRTCChannelConfig:{sid:25,cid:3,service:"qchatMedia",params:[{type:"Property",name:"qchatGetRTCChannelConfigTag",reflectMapper:Lp.QChatGetRTCParams}],response:[{type:"Property",name:"RTCChannelConfig",reflectMapper:t.QChatRTCChannelConfigResultTag}]},qchatGetRTCChannelMembers:{sid:25,cid:4,service:"qchatMedia",params:[{type:"Property",name:"qchatGetRTCChannelMembersTag",reflectMapper:Lp.QChatGetRTCParams}],response:[{type:"PropertyArray",name:"memberList",reflectMapper:t.memberInfo}]}}},Dp=function(t){function QChatMediaService(a,u){var h;return(h=t.call(this,"qchatMedia",a)||this).config={},h.tokenExpireTime=24e4,h.getTokenState=!1,h.core=a,registerParser({cmdMap:Pp,cmdConfig:qp()}),u&&h.setOptions(u),h.setListener(),h.tokenExpireTime=24e4,h.channelId="",h.serverId="",h}Tt(QChatMediaService,t);var a=QChatMediaService.prototype;return a.setOptions=function setOptions(t){t&&(this.config=Ot(this.config,t),(null==t?void 0:t.neroom)&&this.setNeroom(t.neroom))},a.setNeroom=function setNeroom(t){this.neroom=new t},a.setListener=function setListener(){var t,a,u,h,m=this;this.core.eventBus.on("disconnect",bind$1(t=this._existRomm).call(t,this)),this.core.eventBus.on("kicked",bind$1(a=this._existRomm).call(a,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLogout",bind$1(u=this._existRomm).call(u,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleKicked",bind$1(h=this._existRomm).call(h,this)),this.core.eventBus.on("qchatMedia/serverOrChannelLeave",(function(t){return __awaiter(m,void 0,void 0,cs.mark((function _callee(){return cs.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(!(t.type===Jd[Jd.channelVisibilityUpdate]&&t.channelId===this.channelId||t.type===Jd[Jd.serverEnterLeave]&&t.serverId===this.serverId)){a.next=4;break}if(!this.roomContext||!this.roomContext.roomUuid){a.next=4;break}return a.next=4,this.disconnectChannel();case 4:case"end":return a.stop()}}),_callee,this)})))}))},a._existRomm=function _existRomm(){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){return cs.wrap((function _callee2$(t){for(;;)switch(t.prev=t.next){case 0:if(!this.roomContext){t.next=3;break}return t.next=3,this.disconnectChannel();case 3:this.tokenTimer&&(this.core.timerManager.deleteTimer(this.tokenTimer),this.tokenTimer=void 0);case 4:case"end":return t.stop()}}),_callee2,this)})))},a._checkPermission=function _checkPermission(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee3(){var a;return cs.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,this.core.qchatRole.checkPermission(t);case 2:return a=u.sent,u.abrupt("return",a);case 4:case"end":return u.stop()}}),_callee3,this)})))},a._checkConnectState=function _checkConnectState(){if("connect"!==this.state)throw new Gl("QChatMedia::you should connect first")},a.kickMemberOut=function kickMemberOut(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){return cs.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),u.prev=2,u.next=5,null===(a=this.roomContext)||void 0===a?void 0:a.kickMemberOut(t.accid);case 5:u.next=11;break;case 7:throw u.prev=7,u.t0=u.catch(2),this.logger.error("QChatMedia::kickMemberOut error params is "+gs(t),u.t0),new Gl("QChatMedia::kickMemberOut error",u.t0);case 11:case"end":return u.stop()}}),_callee4,this,[[2,7]])})))},a.disconnectChannel=function disconnectChannel(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){return cs.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:return this.logger.debug("QChatMedia::disconnect begin disconnect"),a.prev=1,a.next=4,null===(t=this.roomContext)||void 0===t?void 0:t.leaveRoom();case 4:a.next=9;break;case 6:a.prev=6,a.t0=a.catch(1),this.logger.error("QChatMedia::disconnect error",a.t0);case 9:if(!this.authService){a.next=12;break}return a.next=12,this.authService.logout();case 12:this._setStateInit(),this.core.emit("qchatMediaDisconnect"),this.core.qchatMedia.emit("qchatMediaDisconnect");case 15:case"end":return a.stop()}}),_callee5,this,[[1,6]])})))},a._setStateInit=function _setStateInit(){this.logger.debug("QChatMedia::disconnect end"),this.roomContext=void 0,this.rtcController=void 0,this.state="init",this.tokenTimer&&(this.core.timerManager.deleteTimer(this.tokenTimer),this.tokenTimer=void 0),this.channelId="",this.serverId=""},a.muteAudio=function muteAudio(t){var a,u,h,m,g;return __awaiter(this,void 0,void 0,cs.mark((function _callee6(){var E;return cs.wrap((function _callee6$(I){for(;;)switch(I.prev=I.next){case 0:if(this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),(null===(a=this.roomContext)||void 0===a?void 0:a.localMember.uuid)!==t.accid){I.next=14;break}return I.prev=3,I.next=6,null===(u=this.rtcController)||void 0===u?void 0:u.muteMyAudio();case 6:I.next=12;break;case 8:throw I.prev=8,I.t0=I.catch(3),this.logger.error("QChatMedia::muteAudio error",I.t0),new Gl("QChatMedia::muteAudio error",I.t0);case 12:case 25:I.next=31;break;case 14:if(I.prev=14,!(E=null===(h=this.roomContext)||void 0===h?void 0:h.roomProperties.audioOff)||"offNotAllowSelfOn"!==(null===(m=E.value)||void 0===m?void 0:m.split("_")[0])){I.next=23;break}return I.next=19,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"});case 19:if(I.sent){I.next=23;break}return this.logger.error("QChatMedia::unMuteAudio not allow open auido"),I.abrupt("return");case 23:return I.next=25,null===(g=this.rtcController)||void 0===g?void 0:g.muteMemberAudio(t.accid);case 27:throw I.prev=27,I.t1=I.catch(14),this.logger.error("QChatMedia::muteAudio error params is "+gs(t),I.t1),new Gl("QChatMedia::muteAudio error",I.t1);case 31:case"end":return I.stop()}}),_callee6,this,[[3,8],[14,27]])})))},a.unMuteAudio=function unMuteAudio(t){var a,u,h,m,g;return __awaiter(this,void 0,void 0,cs.mark((function _callee7(){var E;return cs.wrap((function _callee7$(I){for(;;)switch(I.prev=I.next){case 0:if(this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),!(E=null===(a=this.roomContext)||void 0===a?void 0:a.roomProperties.audioOff)||"offNotAllowSelfOn"!==(null===(u=E.value)||void 0===u?void 0:u.split("_")[0])){I.next=10;break}return I.next=6,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"});case 6:if(I.sent){I.next=10;break}return this.logger.error("QChatMedia::unMuteAudio not allow open auido"),I.abrupt("return");case 10:if((null===(h=this.roomContext)||void 0===h?void 0:h.localMember.uuid)!==t.accid){I.next=22;break}return I.prev=11,I.next=14,null===(m=this.rtcController)||void 0===m?void 0:m.unmuteMyAudio();case 14:I.next=20;break;case 16:throw I.prev=16,I.t0=I.catch(11),this.logger.error("QChatMedia::unMuteAudio error",I.t0),new Gl("QChatMedia::unMuteAudio error",I.t0);case 20:case 25:I.next=31;break;case 22:return I.prev=22,I.next=25,null===(g=this.rtcController)||void 0===g?void 0:g.unmuteMemberAudio(t.accid);case 27:throw I.prev=27,I.t1=I.catch(22),this.logger.error("QChatMedia::unMuteAudio error params is "+gs(t),I.t1),new Gl("QChatMedia::unMuteAudio error",I.t1);case 31:case"end":return I.stop()}}),_callee7,this,[[11,16],[22,27]])})))},a.muteVideo=function muteVideo(t){var a,u,h,m,g;return __awaiter(this,void 0,void 0,cs.mark((function _callee8(){var E;return cs.wrap((function _callee8$(I){for(;;)switch(I.prev=I.next){case 0:if(this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),(null===(a=this.roomContext)||void 0===a?void 0:a.localMember.uuid)!==t.accid){I.next=14;break}return I.prev=3,I.next=6,null===(u=this.rtcController)||void 0===u?void 0:u.muteMyVideo();case 6:I.next=12;break;case 8:throw I.prev=8,I.t0=I.catch(3),this.logger.error("QChatMedia::muteVideo error",I.t0),new Gl("QChatMedia::muteVideo error",I.t0);case 12:case 25:I.next=31;break;case 14:if(I.prev=14,!(E=null===(h=this.roomContext)||void 0===h?void 0:h.roomProperties.videoOff)||"offNotAllowSelfOn"!==(null===(m=E.value)||void 0===m?void 0:m.split("_")[0])){I.next=23;break}return I.next=19,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"});case 19:if(I.sent){I.next=23;break}return this.logger.error("QChatMedia::unMuteVideo not allow open video"),I.abrupt("return");case 23:return I.next=25,null===(g=this.rtcController)||void 0===g?void 0:g.muteMemberVideo(t.accid);case 27:throw I.prev=27,I.t1=I.catch(14),this.logger.error("QChatMedia::muteVideo error params is "+gs(t),I.t1),new Gl("QChatMedia::muteVideo error",I.t1);case 31:case"end":return I.stop()}}),_callee8,this,[[3,8],[14,27]])})))},a.unMuteVideo=function unMuteVideo(t){var a,u,h,m,g;return __awaiter(this,void 0,void 0,cs.mark((function _callee9(){var E;return cs.wrap((function _callee9$(I){for(;;)switch(I.prev=I.next){case 0:if(this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),!(E=null===(a=this.roomContext)||void 0===a?void 0:a.roomProperties.videoOff)||"offNotAllowSelfOn"!==(null===(u=E.value)||void 0===u?void 0:u.split("_")[0])){I.next=10;break}return I.next=6,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"});case 6:if(I.sent){I.next=10;break}return this.logger.error("QChatMedia::unMuteVideo not allow open video"),I.abrupt("return");case 10:if((null===(h=this.roomContext)||void 0===h?void 0:h.localMember.uuid)!==t.accid){I.next=22;break}return I.prev=11,I.next=14,null===(m=this.rtcController)||void 0===m?void 0:m.unmuteMyVideo();case 14:I.next=20;break;case 16:throw I.prev=16,I.t0=I.catch(11),this.logger.error("QChatMedia::unMuteVideo error",I.t0),new Gl("QChatMedia::unMuteVideo error",I.t0);case 20:I.next=30;break;case 22:I.prev=22,null===(g=this.rtcController)||void 0===g||g.unmuteMemberVideo(t.accid),I.next=30;break;case 26:throw I.prev=26,I.t1=I.catch(22),this.logger.error("QChatMedia::unMuteVideo error",I.t1),new Gl("QChatMedia::unMuteVideo error",I.t1);case 30:case"end":return I.stop()}}),_callee9,this,[[11,16],[22,26]])})))},a.startScreenShare=function startScreenShare(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee10(){return cs.wrap((function _callee10$(a){for(;;)switch(a.prev=a.next){case 0:this._checkConnectState(),a.prev=1,null===(t=this.rtcController)||void 0===t||t.startScreenShare(),a.next=9;break;case 5:throw a.prev=5,a.t0=a.catch(1),this.logger.error("QChatMedia::startScreenShare error",a.t0),new Gl("QChatMedia::startScreenShare error",a.t0);case 9:case"end":return a.stop()}}),_callee10,this,[[1,5]])})))},a.stopScreenShare=function stopScreenShare(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee11(){return cs.wrap((function _callee11$(a){for(;;)switch(a.prev=a.next){case 0:this._checkConnectState(),a.prev=1,null===(t=this.rtcController)||void 0===t||t.stopScreenShare(),a.next=9;break;case 5:throw a.prev=5,a.t0=a.catch(1),this.logger.error("QChatMedia::stopScreenShare error",a.t0),new Gl("QChatMedia::stopScreenShare error",a.t0);case 9:case"end":return a.stop()}}),_callee11,this,[[1,5]])})))},a.stopMemberScreenShare=function stopMemberScreenShare(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee12(){return cs.wrap((function _callee12$(u){for(;;)switch(u.prev=u.next){case 0:this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),u.prev=2,null===(a=this.rtcController)||void 0===a||a.stopMemberScreenShare(t.accid),u.next=10;break;case 6:throw u.prev=6,u.t0=u.catch(2),this.logger.error("QChatMedia::stopMemberScreenShare error",u.t0),new Gl("QChatMedia::stopMemberScreenShare error",u.t0);case 10:case"end":return u.stop()}}),_callee12,this,[[2,6]])})))},a.subscribeRemoteVideoStream=function subscribeRemoteVideoStream(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee13(){return cs.wrap((function _callee13$(u){for(;;)switch(u.prev=u.next){case 0:this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1},streamType:{type:"number",allowEmpty:!1,min:0,max:1}},t),u.prev=2,null===(a=this.rtcController)||void 0===a||a.subscribeRemoteVideoStream(t.accid,t.streamType),u.next=10;break;case 6:throw u.prev=6,u.t0=u.catch(2),this.logger.error("QChatMedia::subscribeRemoteVideoStream error",u.t0),new Gl("QChatMedia::subscribeRemoteVideoStream error",u.t0);case 10:case"end":return u.stop()}}),_callee13,this,[[2,6]])})))},a.unSubscribeRemoteVideoStream=function unSubscribeRemoteVideoStream(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee14(){return cs.wrap((function _callee14$(u){for(;;)switch(u.prev=u.next){case 0:this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1},streamType:{type:"number",allowEmpty:!1,min:0,max:1}},t),u.prev=2,null===(a=this.rtcController)||void 0===a||a.unsubscribeRemoteVideoStream(t.accid,t.streamType),u.next=10;break;case 6:throw u.prev=6,u.t0=u.catch(2),this.logger.error("QChatMedia::unSubscribeRemoteVideoStream error",u.t0),new Gl("QChatMedia::unSubscribeRemoteVideoStream error",u.t0);case 10:case"end":return u.stop()}}),_callee14,this,[[2,6]])})))},a.setupVideoCanvas=function setupVideoCanvas(t){var a;if(this._checkConnectState(),(null===(a=this.roomContext)||void 0===a?void 0:a.localMember.uuid)===t.accid)try{return this.rtcController.setupLocalVideoCanvas(t.videoView)}catch(t){throw this.logger.error("QChatMedia::setupVideoCanvas error",t),new Gl("QChatMedia::setupVideoCanvas error",t)}else try{return this.rtcController.setupRemoteVideoCanvas(t.videoView,t.accid)}catch(t){throw this.logger.error("QChatMedia::setupVideoCanvas error",t),new Gl("QChatMedia::setupVideoCanvas error",t)}},a.setupRemoteVideoSubStreamCanvas=function setupRemoteVideoSubStreamCanvas(t){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t);try{return this.rtcController.setupRemoteVideoSubStreamCanvas(t.videoView,t.accid)}catch(t){throw this.logger.error("QChatMedia::setupRemoteVideoSubStreamCanvas error",t),new Gl("QChatMedia::setupRemoteVideoSubStreamCanvas error",t)}},a.getScreenSharingUserUuid=function getScreenSharingUserUuid(){this._checkConnectState();try{return this.rtcController.getScreenSharingUserUuid()}catch(t){throw this.logger.error("QChatMedia::getScreenSharingUserUuid error",t),new Gl("QChatMedia::getScreenSharingUserUuid error",t)}},a.initQChatMedia=function initQChatMedia(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee15(){return cs.wrap((function _callee15$(a){for(;;)switch(a.prev=a.next){case 0:if(this.neroom){a.next=3;break}return this.logger.warn("QChatMedia::init::you should import neroom SDK"),a.abrupt("return");case 3:if(void 0===this.state){a.next=6;break}return this.logger.error("QChatMedia::init::you already init QChatMedia"),a.abrupt("return");case 6:a.prev=6,this.neroom.initialize({appKey:this.core.options.appkey,serverConfig:t.serverConfig}),a.next=14;break;case 10:throw a.prev=10,a.t0=a.catch(6),this.logger.error("QChatMedia::initQChatMedia error",a.t0),new Gl("QChatMedia::initQChatMedia error",a.t0);case 14:this.authService=this.neroom.authService,this.roomService=this.neroom.roomService,this.messageChannelService=this.neroom.messageChannelService,this.state="init";case 18:case"end":return a.stop()}}),_callee15,this,[[6,10]])})))},a.loginByIM=function loginByIM(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee16(){var a,u;return cs.wrap((function _callee16$(h){for(;;)switch(h.prev=h.next){case 0:if("init"===this.state){h.next=3;break}return this.logger.error("QChatMedia::connect::you should init before login"),h.abrupt("return");case 3:return h.next=5,this._getToken();case 5:return a=h.sent,u=get(this.core,"options.token")||get(this.core,"V2NIMLoginService.token"),h.prev=7,h.next=10,null===(t=this.authService)||void 0===t?void 0:t.loginByIM(this.core.account,a,u);case 10:h.next=16;break;case 12:throw h.prev=12,h.t0=h.catch(7),this.logger.error("QChatMedia::loginByIM error",h.t0),new Gl("QChatMedia::loginByIM error",h.t0);case 16:this.state="login";case 17:case"end":return h.stop()}}),_callee16,this,[[7,12]])})))},a._getToken=function _getToken(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee18(){var a,u,h,m,g=this;return cs.wrap((function _callee18$(E){for(;;)switch(E.prev=E.next){case 0:return this.logger.log("QChatMedia::getToken begin getToken"),E.next=3,this.core.sendCmd("qchatGetNeroomToken",{qchatGetNeroomTokenTag:{neroomDeviceId:null===(t=this.neroom)||void 0===t?void 0:t.deviceId}});case 3:return a=E.sent,u=a.content.neroomToken,h=u.token,m=u.expire,this.logger.log("QChatMedia::getToken success token is "+h+",expire is "+m+" "),this.tokenTimer||(this.logger.debug("QChatMedia::getToken set token timer,expire is "+(1e3*m-this.tokenExpireTime)+" "),this.tokenTimer=this.core.timerManager.addTimer((function(){return __awaiter(g,void 0,void 0,cs.mark((function _callee17(){var t;return cs.wrap((function _callee17$(a){for(;;)switch(a.prev=a.next){case 0:return this.tokenTimer&&this.core.timerManager.deleteTimer(this.tokenTimer),this.tokenTimer=void 0,a.next=4,this._getToken();case 4:t=a.sent,this.authService&&this.authService.renewToken(t);case 6:case"end":return a.stop()}}),_callee17,this)})))}),1e3*m-this.tokenExpireTime)),E.abrupt("return",h);case 8:case"end":return E.stop()}}),_callee18,this)})))},a.connectChannel=function connectChannel(t){var a,u;return __awaiter(this,void 0,void 0,cs.mark((function _callee19(){var h;return cs.wrap((function _callee19$(m){for(;;)switch(m.prev=m.next){case 0:if(void 0!==this.state){m.next=3;break}return this.logger.error("QChatMedia::connect::you should init before login"),m.abrupt("return");case 3:if("init"!==this.state){m.next=6;break}return m.next=6,this.loginByIM();case 6:return this.serverId=t.serverId,this.channelId=t.channelId,m.prev=8,m.next=11,null===(a=this.roomService)||void 0===a?void 0:a.joinRoom({roomUuid:t.channelId,role:"qchatAudience",userName:this.core.account,initialProperties:{}},{});case 11:m.next=17;break;case 13:throw m.prev=13,m.t0=m.catch(8),this.logger.error("QChatMedia::connectChannel error",m.t0),new Gl("QChatMedia::connectChannel error",m.t0);case 17:if(!(h=null===(u=this.roomService)||void 0===u?void 0:u.getRoomContext(t.channelId))){m.next=28;break}return this.roomContext=h,this.rtcController=this.roomContext.rtcController,this.state="connect",m.next=24,this.rtcController.joinRtcChannel();case 24:this.core.emit("connectChannel"),this.core.qchatMedia.emit("connectChannel"),m.next=29;break;case 28:this.logger.error("QChatMedia::connect room not exited");case 29:case"end":return m.stop()}}),_callee19,this,[[8,13]])})))},a.updateRTCChannelInfo=function updateRTCChannelInfo(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee20(){return cs.wrap((function _callee20$(a){for(;;)switch(a.prev=a.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},limit:{type:"number",allowEmpty:!1}},t),a.next=3,this.core.sendCmd("qchatUpdateRTCChannelConfig",{RTCChannelConfig:Ot(Ot({},t),{audio:gs(t.audio),video:gs(t.video)})});case 3:case"end":return a.stop()}}),_callee20,this)})))},a.getRTCChannelInfo=function getRTCChannelInfo(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee21(){var a;return cs.wrap((function _callee21$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1}},t),u.next=3,this.core.sendCmd("qchatGetRTCChannelConfig",{qchatGetRTCChannelConfigTag:t});case 3:return a=u.sent,u.abrupt("return",formatRTCChannelConfig(a.content.RTCChannelConfig));case 5:case"end":return u.stop()}}),_callee21,this)})))},a.getRTCChannelOnlineMembers=function getRTCChannelOnlineMembers(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee22(){var a;return cs.wrap((function _callee22$(u){for(;;)switch(u.prev=u.next){case 0:return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1}},t),u.next=3,this.core.sendCmd("qchatGetRTCChannelMembers",{qchatGetRTCChannelMembersTag:t});case 3:return a=u.sent,u.abrupt("return",formatMembers(a.content.memberList));case 5:case"end":return u.stop()}}),_callee22,this)})))},a.addRTCChannelListener=function addRTCChannelListener(){var t,a,u=this;this._checkConnectState();try{null===(t=this.authService)||void 0===t||t.addAuthListener({onAuthEvent:function onAuthEvent(t){return __awaiter(u,void 0,void 0,cs.mark((function _callee23(){var a;return cs.wrap((function _callee23$(u){for(;;)switch(u.prev=u.next){case 0:if(1026!==t||this.getTokenState){u.next=10;break}return this.logger.log("QChatMedia::loginByIM token expire,begin get new token "),this.getTokenState=!0,u.next=5,this._getToken();case 5:if(a=u.sent,!this.authService){u.next=9;break}return u.next=9,this.authService.renewToken(a);case 9:this.getTokenState=!1;case 10:case"end":return u.stop()}}),_callee23,this)})))}})}catch(t){throw this.getTokenState=!1,this.logger.error("QChatMedia::loginByIM addAuthListener error",t),new Gl("QChatMedia::loginByIM addAuthListener error",t)}null===(a=this.roomContext)||void 0===a||a.addRoomListener({onRoomPropertiesChanged:function onRoomPropertiesChanged(t){var a,h,m,g,E,I;if(u.logger.log("QChatMedia::addRTCChannelListener::onRoomPropertiesChanged",t),t.audioOff){var M=null===(a=t.audioOff.value)||void 0===a?void 0:a.split("_")[0];"offNotAllowSelfOn"!==M&&"offAllowSelfOn"!==M||(null===(h=u.roomContext)||void 0===h?void 0:h.localMember.isAudioOn)&&(null===(m=u.rtcController)||void 0===m||m.muteMyAudio())}else if(t.videoOff){var S=null===(g=t.videoOff.value)||void 0===g?void 0:g.split("_")[0];"offNotAllowSelfOn"!==S&&"offAllowSelfOn"!==S||(null===(E=u.roomContext)||void 0===E?void 0:E.localMember.isVideoOn)&&(null===(I=u.rtcController)||void 0===I||I.muteMyVideo())}},onRoomPropertiesDeleted:function onRoomPropertiesDeleted(t){u.logger.log("QChatMedia::addRTCChannelListener::onRoomPropertiesDeleted",t)},onMemberJoinRtcChannel:function onMemberJoinRtcChannel(t){u.logger.log("QChatMedia::addRTCChannelListener::onMemberJoinRTCChannel",t);var a=map$6(t).call(t,(function(t){return t.uuid}));u.core.emit("memberJoinRTCChannel",a),u.core.qchatMedia.emit("memberJoinRTCChannel",a)},onMemberLeaveRoom:function onMemberLeaveRoom(t){u.logger.log("QChatMedia::addRTCChannelListener::onMemberLeaveRoom",t);var a=map$6(t).call(t,(function(t){return t.uuid}));u.core.emit("memberLeaveRTCChannel",a),u.core.qchatMedia.emit("memberLeaveRTCChannel",a)},onRoomEnded:function onRoomEnded(t){u.authService&&u.authService.logout(),u._setStateInit(),u.logger.log("QChatMedia::addRTCChannelListener::onRoomEnded",t),u.core.emit("RTCChannelEnded",t),u.core.qchatMedia.emit("RTCChannelEnded",t)},onRtcChannelError:function onRtcChannelError(t){u.logger.log("QChatMedia::addRTCChannelListener::onRtcChannelError",t),"SOCKET_ERROR"===t&&u.disconnectChannel(),u.core.emit("RTCChannelError",t),u.core.qchatMedia.emit("RTCChannelError",t)},onRtcAudioVolumeIndication:function onRtcAudioVolumeIndication(t){u.logger.log("QChatMedia::addRTCChannelListener::onRtcAudioVolumeIndication",t),u.core.emit("onRtcAudioVolumeIndication",t),u.core.qchatMedia.emit("onRtcAudioVolumeIndication",t)},onMemberAudioMuteChanged:function onMemberAudioMuteChanged(t,a,h){u.logger.log("QChatMedia::addRTCChannelListener::onMemberAudioMuteChanged",t,a),u.core.emit("memberAudioMuteChanged",{memberAccId:t.uuid,mute:a,operateByAccId:h.uuid}),u.core.qchatMedia.emit("memberAudioMuteChanged",{memberAccId:t.uuid,mute:a,operateByAccId:h.uuid})},onMemberScreenShareStateChanged:function onMemberScreenShareStateChanged(t,a,h){u.logger.log("QChatMedia::addRTCChannelListener::onMemberScreenShareStateChanged",t,a),u.core.emit("memberScreenShareStateChanged",{memberAccId:t.uuid,isSharing:a,operateByAccId:h.uuid}),u.core.qchatMedia.emit("memberScreenShareStateChanged",{memberAccId:t.uuid,isSharing:a,operateByAccId:h.uuid})},onMemberVideoMuteChanged:function onMemberVideoMuteChanged(t,a,h){u.logger.log("QChatMedia::addRTCChannelListener::onMemberVideoMuteChanged",t,a),u.core.emit("memberVideoMuteChanged",{memberAccId:t.uuid,mute:a,operateByAccId:h.uuid}),u.core.qchatMedia.emit("memberVideoMuteChanged",{memberAccId:t.uuid,mute:a,operateByAccId:h.uuid})}})},a.enumCameraDevices=function enumCameraDevices(){var t=this;return this._checkConnectState(),this.rtcController.enumCameraDevices().then((function(t){return t.data}),(function(a){throw t.logger.error("QChatMedia::enumCameraDevices error",a),new Gl("QChatMedia::enumCameraDevices error",a)}))},a.enumPlayoutDevices=function enumPlayoutDevices(){var t=this;return this._checkConnectState(),this.rtcController.enumPlayoutDevices().then((function(t){return t.data}),(function(a){throw t.logger.error("QChatMedia::enumPlayoutDevices error",a),new Gl("QChatMedia::enumPlayoutDevices error",a)}))},a.enumRecordDevices=function enumRecordDevices(){var t=this;return this._checkConnectState(),this.rtcController.enumRecordDevices().then((function(t){return t.data}),(function(a){throw t.logger.error("QChatMedia::enumRecordDevices error",a),new Gl("QChatMedia::enumRecordDevices error",a)}))},a.removeRTCChannelListener=function removeRTCChannelListener(){var t,a;this._checkConnectState(),null===(t=this.roomContext)||void 0===t||t.removeRoomListener({}),null===(a=this.authService)||void 0===a||a.removeAuthListener({})},a.setSelectedCameraDevice=function setSelectedCameraDevice(t){var a=this;return this._checkConnectState(),validate({deviceId:{type:"string",allowEmpty:!1}},t),this.rtcController.setSelectedCameraDevice(t.deviceId).then((function(t){return t.data}),(function(t){throw a.logger.error("QChatMedia::setSelectedCameraDevice error",t),new Gl("QChatMedia::setSelectedCameraDevice error",t)}))},a.setSelectedPlayoutDevice=function setSelectedPlayoutDevice(t){var a=this;return this._checkConnectState(),validate({deviceId:{type:"string",allowEmpty:!1}},t),this.rtcController.setSelectedPlayoutDevice(t.deviceId).then((function(t){return t.data}),(function(t){throw a.logger.error("QChatMedia::setSelectedPlayoutDevice error",t),new Gl("QChatMedia::setSelectedPlayoutDevice error",t)}))},a.setSelectedRecordDevice=function setSelectedRecordDevice(t){var a=this;return this._checkConnectState(),validate({deviceId:{type:"string",allowEmpty:!1}},t),this.rtcController.setSelectedRecordDevice(t.deviceId).then((function(t){return t.data}),(function(t){throw a.logger.error("QChatMedia::setSelectedRecordDevice error",t),new Gl("QChatMedia::setSelectedRecordDevice error",t)}))},a.getRTCMembers=function getRTCMembers(){var t,a;return this._checkConnectState(),map$6(t=filter(a=this.roomContext.remoteMembers).call(a,(function(t){return t.isInRtcChannel}))).call(t,(function(t){return{accid:t.uuid,isAudioOn:!!t.isAudioOn,isVideoOn:!!t.isVideoOn,isSharingScreen:!!t.isSharingScreen,properties:t.properties}}))},a.subscribeRemoteVideoSubStream=function subscribeRemoteVideoSubStream(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee24(){return cs.wrap((function _callee24$(u){for(;;)switch(u.prev=u.next){case 0:this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),u.prev=2,null===(a=this.rtcController)||void 0===a||a.subscribeRemoteVideoSubStream(t.accid),u.next=10;break;case 6:throw u.prev=6,u.t0=u.catch(2),this.logger.error("QChatMedia::subscribeRemoteVideoSubStream error",u.t0),new Gl("QChatMedia::subscribeRemoteVideoSubStream error",u.t0);case 10:case"end":return u.stop()}}),_callee24,this,[[2,6]])})))},a.unsubscribeRemoteVideoSubStream=function unsubscribeRemoteVideoSubStream(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee25(){return cs.wrap((function _callee25$(u){for(;;)switch(u.prev=u.next){case 0:this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},t),u.prev=2,null===(a=this.rtcController)||void 0===a||a.unsubscribeRemoteVideoSubStream(t.accid),u.next=10;break;case 6:throw u.prev=6,u.t0=u.catch(2),this.logger.error("QChatMedia::unsubscribeRemoteVideoSubStream error",u.t0),new Gl("QChatMedia::unsubscribeRemoteVideoSubStream error",u.t0);case 10:case"end":return u.stop()}}),_callee25,this,[[2,6]])})))},a.muteAllAudio=function muteAllAudio(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee26(){return cs.wrap((function _callee26$(a){for(;;)switch(a.prev=a.next){case 0:return this._checkConnectState(),a.next=3,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"});case 3:if(a.sent){a.next=7;break}return this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneMicrophone auth"),a.abrupt("return");case 7:a.prev=7,null===(t=this.roomContext)||void 0===t||t.updateRoomProperty("audioOff",gs({value:"offNotAllowSelfOn_"+(new Date).getTime()})),a.next=15;break;case 11:throw a.prev=11,a.t0=a.catch(7),this.logger.error("QChatMedia::muteAllAudio error",a.t0),new Gl("QChatMedia::muteAllAudio error",a.t0);case 15:case"end":return a.stop()}}),_callee26,this,[[7,11]])})))},a.muteAllVideo=function muteAllVideo(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee27(){return cs.wrap((function _callee27$(a){for(;;)switch(a.prev=a.next){case 0:return this._checkConnectState(),a.next=3,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"});case 3:if(a.sent){a.next=7;break}return this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneCamera auth"),a.abrupt("return");case 7:a.prev=7,null===(t=this.roomContext)||void 0===t||t.updateRoomProperty("videoOff",gs({value:"offNotAllowSelfOn_"+(new Date).getTime()})),a.next=15;break;case 11:throw a.prev=11,a.t0=a.catch(7),this.logger.error("QChatMedia::muteAllVideo error",a.t0),new Gl("QChatMedia::muteAllVideo error",a.t0);case 15:case"end":return a.stop()}}),_callee27,this,[[7,11]])})))},a.unMuteAllAudio=function unMuteAllAudio(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee28(){return cs.wrap((function _callee28$(a){for(;;)switch(a.prev=a.next){case 0:return this._checkConnectState(),a.next=3,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"});case 3:if(a.sent){a.next=7;break}return this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneMicrophone auth"),a.abrupt("return");case 7:a.prev=7,null===(t=this.roomContext)||void 0===t||t.updateRoomProperty("audioOff",gs({value:"disable_"+(new Date).getTime()})),a.next=15;break;case 11:throw a.prev=11,a.t0=a.catch(7),this.logger.error("QChatMedia::unMuteAllAudio error",a.t0),new Gl("QChatMedia::unMuteAllAudio error",a.t0);case 15:case"end":return a.stop()}}),_callee28,this,[[7,11]])})))},a.unMuteAllVideo=function unMuteAllVideo(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee29(){return cs.wrap((function _callee29$(a){for(;;)switch(a.prev=a.next){case 0:return this._checkConnectState(),a.next=3,this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"});case 3:if(a.sent){a.next=7;break}return this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneCamera auth"),a.abrupt("return");case 7:a.prev=7,null===(t=this.roomContext)||void 0===t||t.updateRoomProperty("videoOff",gs({value:"disable_"+(new Date).getTime()})),a.next=15;break;case 11:throw a.prev=11,a.t0=a.catch(7),this.logger.error("QChatMedia::unMuteAllVideo error",a.t0),new Gl("QChatMedia::unMuteAllVideo error",a.t0);case 15:case"end":return a.stop()}}),_callee29,this,[[7,11]])})))},QChatMediaService}(ad),Vp=["error","warn","log","debug"],Up=function emptyFunc(){},Gp=["off","error","warn","log","debug"],Fp=function(){function Logger(t,a){void 0===a&&(a={}),this.storageArr=[],this.debugLevel="off",this.timer=0,this.strategies={debug:{name:"debg",func:console.log},log:{name:"info",func:console.log},warn:{name:"warn",func:console.warn},error:{name:"erro",func:console.error}},this.debug=Up,this.log=Up,this.warn=Up,this.error=Up,this.iid=Math.round(1e3*Math.random()),this.debugLevel=includes(Gp).call(Gp,t)?t:"off",a.debugLevel&&(this.debugLevel=includes(Gp).call(Gp,a.debugLevel)?a.debugLevel:this.debugLevel),this.logStorage=!1===a.storageEnable?null:new au.logStorage(null==a?void 0:a.storageName),this.setOptions(a),this.setLogFunc(this.debugLevel),this.setTimer(),this.open()}var t=Logger.prototype;return t.getDebugMode=function getDebugMode(){return"debug"===this.debugLevel},t.open=function open(t){var a=this;this.logStorage&&this.logStorage.open(t).then((function(){a.log("Logger::open success")})).catch((function(t){a.warn("Logger::open failed",t)}))},t.setOptions=function setOptions(t){if(t&&t.logFunc){var a=t.logFunc;for(var u in a){var h=u,m=a[h];m&&(this.strategies[h].func=m)}}},t.setLogFunc=function setLogFunc(t,a){var u=this;void 0===a&&(a="log");var h=findIndex(Vp).call(Vp,(function(a){return a===t})),m=findIndex(Vp).call(Vp,(function(t){return t===a}));forEach$1(Vp).call(Vp,(function(t,a){u[t]=function(){if(!(a>h&&a>m)){var u=slice(Array.prototype).call(arguments),g=this.strategies[t],E=this.formatArgs(u,g.name);a<=m&&this.logStorage&&this.prepareSaveLog(E,t),a<=h&&g.func(E)}}}))},t.extractLogs=function extractLogs(){var t;return this.logStorage?null===(t=this.logStorage)||void 0===t?void 0:t.extractLogs():za.resolve("")},t.prepareSaveLog=function prepareSaveLog(t,a){this.storageArr.push({text:t,level:a,time:Za(),iid:this.iid}),this.timer||this.setTimer(),this.storageArr.length>=100&&(this.triggerTimer(),this.storageArr=[])},t.saveLogs=function saveLogs(){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var t;return cs.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.logStorage){a.next=2;break}return a.abrupt("return");case 2:return t=this.storageArr,this.storageArr=[],a.prev=4,a.next=7,this.logStorage.addLogs(t);case 7:a.next=11;break;case 9:a.prev=9,a.t0=a.catch(4);case 11:case"end":return a.stop()}}),_callee,this,[[4,9]])})))},t.clearTimer=function clearTimer(){this.timer&&clearTimeout(this.timer),this.timer=0},t.setTimer=function setTimer(){var t;this.clearTimer(),this.timer=go(bind$1(t=this.triggerTimer).call(t,this),5e3)},t.triggerTimer=function triggerTimer(){this.clearTimer(),this.saveLogs()},t.formatArgs=function formatArgs(t,a){var u=new Date;return"[NIM "+this.iid+" "+a+" "+(u.getMonth()+1+"-"+u.getDate()+" "+u.getHours()+":"+u.getMinutes()+":"+u.getSeconds()+":"+u.getMilliseconds())+"] "+map$6(t).call(t,(function(t){return t instanceof Ll?t.toString():t instanceof Error?t&&t.message?t.message:t:"object"==typeof t?gs(t):t})).join(" ")},t.destroy=function destroy(){this.debug=Up,this.log=Up,this.warn=Up,this.error=Up,this.saveLogs(),this.clearTimer(),this.storageArr=[],this.logStorage&&this.logStorage.close()},Logger}(),Bp=TypeError,jp=Object.getOwnPropertyDescriptor,Wp=N&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,a){if(eo(t)&&!jp(t,"length").writable)throw Bp("Cannot set read only .length");return t.length=a}:function(t,a){return t.length=a},Yp=arrayMethodHasSpeciesSupport("splice"),$p=Math.max,Hp=Math.min;_export({target:"Array",proto:!0,forced:!Yp},{splice:function splice(t,a){var u,h,m,g,E,I,M=toObject(this),S=lengthOfArrayLike(M),C=toAbsoluteIndex(t,S),T=arguments.length;for(0===T?u=h=0:1===T?(u=0,h=S-C):(u=T-2,h=Hp($p(toIntegerOrInfinity(a),0),S-C)),doesNotExceedSafeInteger(S+u-h),m=arraySpeciesCreate(M,h),g=0;g<h;g++)(E=C+g)in M&&createProperty(m,g,M[E]);if(m.length=h,u<h){for(g=C;g<S-h;g++)I=g+u,(E=g+h)in M?M[I]=M[E]:deletePropertyOrThrow(M,I);for(g=S;g>S-h+u;g--)deletePropertyOrThrow(M,g-1)}else if(u>h)for(g=S-h;g>C;g--)I=g+u-1,(E=g+h-1)in M?M[I]=M[E]:deletePropertyOrThrow(M,I);for(g=0;g<u;g++)M[g+C]=arguments[g+2];return Wp(M,S-h+u),m}});var Qp=entryVirtual("Array").splice,Kp=Array.prototype,splice=function(t){var a=t.splice;return t===Kp||Y(Kp,t)&&a===Kp.splice?Qp:a};function _createForOfIteratorHelperLoose$1(t,a){var u,h=void 0!==fi&&vu(t)||t["@@iterator"];if(h)return bind$1(u=(h=h.call(t)).next).call(u,h);if(zl(t)||(h=function _unsupportedIterableToArray$1(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$1(t,a);var h=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===h&&t.constructor&&(h=t.constructor.name),"Map"===h||"Set"===h?gu(t):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray$1(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){h&&(t=h);var m=0;return function(){return m>=t.length?{done:!0}:{done:!1,value:t[m++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$1(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,h=Array(a);u<a;u++)h[u]=t[u];return h}var zp=function(){function TimerManager(){this.timerList=[],this.id=1,this.timer=null,this.timeout=0}var t=TimerManager.prototype;return t.addTimer=function addTimer(t,a,u){void 0===a&&(a=0),void 0===u&&(u=1);var h=(new Date).getTime(),m=this.id;return this.timerList.push({id:m,loop:u,count:0,timeout:h+a,interval:a,callback:t}),this.id++,this.checkTimer(h),m},t.checkTimer=function checkTimer(t){if(void 0===t&&(t=(new Date).getTime()),this.removeFinished(),0!==this.timerList.length||null==this.timer){for(var a,u,h=0,m=_createForOfIteratorHelperLoose$1(this.timerList);!(a=m()).done;){var g=a.value;(0===h||h>g.timeout)&&(h=g.timeout)}if(0!==this.timerList.length)if(null===this.timer||h<this.timeout||this.timeout<t)this.timer=go(bind$1(u=this.nowTime).call(u,this),h-t),this.timeout=h}},t.nowTime=function nowTime(){for(var t,nowTime=(new Date).getTime(),a=_createForOfIteratorHelperLoose$1(this.timerList);!(t=a()).done;){var u=t.value;nowTime>=u.timeout&&(u.callback(),u.count++,u.timeout=nowTime+u.interval)}this.clerTime(),this.checkTimer(nowTime)},t.clerTime=function clerTime(){null!==this.timer&&(clearTimeout(this.timer),this.timer=null)},t.deleteTimer=function deleteTimer(t){for(var a=this.timerList.length-1;a>=0;a--){var u;if(this.timerList[a].id===t)splice(u=this.timerList).call(u,a,1)}},t.removeFinished=function removeFinished(){for(var t=this.timerList.length-1;t>=0;t--){var a,u=this.timerList[t];if(u.loop>=0&&u.count>=u.loop)splice(a=this.timerList).call(a,t,1)}},t.destroy=function destroy(){this.clerTime(),this.timerList=[],this.id=1,this.timer=null},TimerManager}(),Jp=function(){function CoreAdapters(t){this.lastSuccUploadHost="",this.core=t}var t=CoreAdapters.prototype;return t.getFileUploadInformation=function getFileUploadInformation(t){return au.getFileUploadInformation(t)},t.request=function request(t,a,u){var h=this,m=(new Date).getTime(),g=(null==u?void 0:u.exception_service)||0;return au.request(t,a).catch((function(u){var E,I,M,S,C=u;throw h.core.reporter.reportTraceStart("exceptions",{user_id:h.core.options.account||(null===(I=null===(E=h.core)||void 0===E?void 0:E.auth)||void 0===I?void 0:I.account),trace_id:null===(S=null===(M=h.core.clientSocket)||void 0===M?void 0:M.socket)||void 0===S?void 0:S.sessionId,start_time:m,action:1,exception_service:g}),h.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof C.code?C.code:0,description:C.message||""+C.code,operation_type:0,target:t,context:a?gs(a):""},{asyncParams:au.net.getNetworkStatus()}),h.core.reporter.reportTraceEnd("exceptions",1),u}))},t.uploadFile=function uploadFile(t){var a,u,h,m;return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var g,E,I,M,S,C,T,b,R,A,N,w,O,x;return cs.wrap((function _callee$(k){for(;;)switch(k.prev=k.next){case 0:I="BROWSER"===au.platform,M=I?t.chunkUploadHostBackupList:t.commonUploadHostBackupList,S=I?t.chunkUploadHost:t.commonUploadHost,C=indexOf(M).call(M,S),T=-1===C?concat(g=[S]).call(g,M):concat(E=[S]).call(E,slice(M).call(M,0,C),slice(M).call(M,C+1)),b=Math.max(indexOf(T).call(T,this.lastSuccUploadHost),0),R=null,A=0;case 8:if(!(A<T.length)){k.next=32;break}return N=(new Date).getTime(),w=T[(A+b)%T.length],k.prev=11,k.next=14,au.uploadFile(Ot(Ot({},t),I?{chunkUploadHost:w}:{commonUploadHost:w}));case 14:return O=k.sent,this.lastSuccUploadHost=w,k.abrupt("return",O);case 19:if(k.prev=19,k.t0=k.catch(11),this.core.cloudStorage.nos.nosErrorCount--,R=k.t0,x=k.t0,this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(u=null===(a=this.core)||void 0===a?void 0:a.auth)||void 0===u?void 0:u.account),trace_id:null===(m=null===(h=this.core.clientSocket)||void 0===h?void 0:h.socket)||void 0===m?void 0:m.sessionId,start_time:N,action:1,exception_service:3}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof x.code?x.code:0,description:x.message||""+x.code,operation_type:1,target:w},{asyncParams:au.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),!k.t0||k.t0.code!==kl.V2NIM_ERROR_CODE_CANCELLED&&10499!==k.t0.errCode){k.next=29;break}throw k.t0;case 29:A++,k.next=8;break;case 32:throw R;case 33:case"end":return k.stop()}}),_callee,this,[[11,19]])})))},CoreAdapters}(),Xp="https://abt-online.netease.im/v1/api/abt/client/getExperimentInfo",Zp="imElite_sdk_abtest_web",eh="https://statistic.live.126.net,https://statistic-overseas.yunxinfw.com",th=function(){function ABTest(t,a){this.abtInfo={},this.core=t,this.config=assignOptions({isAbtestEnable:!0,abtestUrl:Xp,abtestProjectKey:Zp},a)}var t=ABTest.prototype;return t.setOptions=function setOptions(t){this.config=assignOptions(this.config,t)},t.abtRequest=function abtRequest(){var t,a;return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var u;return cs.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:if(this.config.isAbtestEnable){h.next=2;break}return h.abrupt("return");case 2:if(!this.abtInfo.experiments){h.next=4;break}return h.abrupt("return");case 4:if(this.config.abtestUrl){h.next=6;break}return h.abrupt("return");case 6:return h.prev=6,h.next=9,this.core.adapters.request(this.config.abtestUrl,{method:"POST",dataType:"json",headers:{sdktype:"ABTest"},data:{clientInfo:{projectKey:this.config.abtestProjectKey,appKey:this.core.options.appkey,osType:"Web",sdkVersion:"10.8.30",deviceId:this.core.config.deviceId},useLocalCache:!0}},{exception_service:7});case 9:u=h.sent,h.next=15;break;case 12:h.prev=12,h.t0=h.catch(6),this.core.logger.warn("ABTest request failed");case 15:this.abtInfo=(null===(a=null===(t=null==u?void 0:u.data)||void 0===t?void 0:t.data)||void 0===a?void 0:a.abtInfo)||{};case 16:case"end":return h.stop()}}),_callee,this,[[6,12]])})))},ABTest}(),rh=Backoff;function Backoff(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}Backoff.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var a=Math.random(),u=Math.floor(a*this.jitter*t);t=0==(1&Math.floor(10*a))?t-u:t+u}return 0|Math.min(t,this.max)},Backoff.prototype.reset=function(){this.attempts=0},Backoff.prototype.setMin=function(t){this.ms=t},Backoff.prototype.setMax=function(t){this.max=t},Backoff.prototype.setJitter=function(t){this.jitter=t};var nh,ah=["disconnect","connect","heartbeat","message","json","event","ack","error","noop"],oh=["transport not supported","client not handshaken","unauthorized"],ih=["reconnect"],sh=function(t){function BaseWebsocket(a,u,h){var m;return(m=t.call(this)||this).websocket=null,m.socketConnectTimer=0,m.url="",m.linkSSL=!0,m.core=a,m.url=u,m.linkSSL=h,m.status="disconnected",m.logger=a.logger,m.connect(),m}Tt(BaseWebsocket,t);var a=BaseWebsocket.prototype;return a.connect=function connect(){var t=this;"connecting"!==this.status&&"connected"!==this.status?(this.status="connecting",this.core.adapters.request((this.linkSSL?"https":"http")+"://"+this.url+"/socket.io/1/?t="+Za(),{method:"GET",dataType:"text",timeout:this.core.options.xhrConnectTimeout||8e3},{exception_service:6}).then((function(a){if("connecting"===t.status){var u=a.data.split(":"),h=u[0];return u[1],t.sessionId=h,t.logger.log("imsocket::XHR success. status "+t.status+", "+("connecting"===t.status?"continue websocket connection":"stop websocket connection")),t._createWebsocket((t.linkSSL?"wss":"ws")+"://"+t.url+"/socket.io/1/websocket/"+h)}})).catch((function(a){if("connecting"===t.status){var u='imsocket::XHR fail. raw message: "'+(a=a||{}).message+'", code: "'+a.code+'"',h=a.code;h="v2"===get(t.core,"options.apiVersion")?a.code===kl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT?kl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:kl.V2NIM_ERROR_CODE_CONNECT_FAILED:408===a.code?408:415;var m=new Ll({code:h,detail:{reason:u,rawError:a}});t.logger.error(u),t.status="disconnected",t.emit("handshakeFailed",m)}}))):this.logger.warn("imsocket::socket is connecting or connected",this.status)},a.close=function close(){if(this.status="disconnected",this.websocket){this.logger.log("imsocket:: close websocket");try{this.websocket.send(this.encodePacket({type:"disconnect"}))}catch(t){this.logger.warn("imsocket::attempt to send encodePacket error",t)}try{this.websocket.close()}catch(t){this.logger.warn("imsocket::attempt to close websocket error",t)}this.clean(),this.emit("disconnect",{code:0,reason:"Active close websocket"})}},a.clean=function clean(){this.status="disconnected",clearTimeout(this.socketConnectTimer),this.websocket&&(this.socketUrl=void 0,this.websocket.onmessage=null,this.websocket.onopen=null,this.websocket.onerror=null,this.websocket.onclose=null,this.websocket=null)},a.onConnect=function onConnect(){this.status="connected",this.emit("connect"),clearTimeout(this.socketConnectTimer)},a._createWebsocket=function _createWebsocket(t){var a,u=this;this.socketConnectTimer=go((function(){u.logger.error("imsocket::Websocket connect timeout. url: ",u.socketUrl),u.emit("handshakeFailed",new Ll({code:"v2"===get(u.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:415,detail:{reason:"imsocket::Websocket connect timeout. url: "+u.socketUrl}}))}),this.core.options.socketConnectTimeout||8e3),this.socketUrl=t,this.websocket=new au.WebSocket(t),this.websocket.onmessage=bind$1(a=this.onMessage).call(a,this),this.websocket.onclose=function(t){t=t||{},u.logger.log("imsocket::Websocket onclose done "+t.wasClean+"/"+t.code+"/"+t.reason),u.clean(),u.emit("disconnect",{code:t.code||0,reason:t.reason})},this.websocket.onerror=function(t){u.logger.error("imsocket::Websocket onerror",t),"logined"===u.core.status&&u.core.clientSocket.ping()}},a.onMessage=function onMessage(t){var a,u=this.decodePacket(t.data);if(u)switch(u.type){case"connect":this.onConnect();break;case"disconnect":this.close(),this.emit("disconnect",{code:0,reason:"MessageEvent type disconnect"});break;case"message":case"json":this.emit("message",u.data);break;case"event":u.name&&this.emit(u.name,u.args);break;case"error":"unauthorized"===u.reason?this.emit("connect_failed",u.reason):this.emit("error",u.reason),this.logger.error("imsocket::Websocket connect failed, onmessage type error. url: ",this.socketUrl),clearTimeout(this.socketConnectTimer),this.emit("handshakeFailed",new Ll({code:"v2"===get(this.core,"options.apiVersion")?kl.V2NIM_ERROR_CODE_CONNECT_FAILED:408,detail:{reason:"imsocket::Websocket connect failed, onMessage socket error. url: "+this.socketUrl}}));break;case"heartbeat":null===(a=this.websocket)||void 0===a||a.send(this.encodePacket({type:"heartbeat"}));break;default:this.logger.warn("imsocket::Websocket no handler type",u.type)}},a.encodePacket=function encodePacket(t){var a,u,h=t.type,m=t.id,g=void 0===m?"":m,E=t.endpoint,I=void 0===E?"":E,M=t.ack,S=null;if(!h)return"";switch(h){case"error":a=t.reason?indexOf(oh).call(oh,t.reason):"",u=t.advice?indexOf(ih).call(ih,t.advice):"",""===a&&""===u||(S=a+(""!==u?"+"+u:""));break;case"message":""!==t.data&&(S=t.data);break;case"event":a={name:t.name},a=t.args&&t.args.length?{name:t.name,args:t.args}:{name:t.name},S=gs(a);break;case"json":S=gs(t.data);break;case"connect":t.qs&&(S=t.qs);break;case"ack":S=t.ackId+(t.args&&t.args.length?"+"+gs(t.args):"")}var C=[indexOf(ah).call(ah,h),g+("data"===M?"+":""),I];return null!=S&&C.push(S),C.join(":")},a.decodePacket=function decodePacket(t){if(t)if("�"!=t.charAt(0)){var a=t.match(/([^:]+):([0-9]+)?(\+)?:([^:]+)?:?([\s\S]*)?/);if(a){var u,h=a[1],m=a[2],g=a[3],E=a[4],I=a[5],M={type:ah[+h],endpoint:E};switch(m&&(M.id=m,M.ack=!g||"data"),M.type){case"error":u=I.split("+"),M.reason=oh[+u[0]]||"";break;case"message":M.data=I||"";break;case"connect":M.qs=I||"";break;case"event":try{var S=JSON.parse(I);M.name=S.name,M.args=S.args}catch(t){this.logger.error("imsocket::parseData::type::event error",t)}M.args=M.args||[];break;case"json":try{M.data=JSON.parse(I)}catch(t){this.logger.error("imsocket::parseData::type::json error",t)}break;case"ack":if((u=I.match(/^([0-9]+)(\+)?(.*)/))&&(M.ackId=u[1],M.args=[],u[3]))try{M.args=u[3]?JSON.parse(u[3]):[]}catch(t){this.logger.error("imsocket::parseData::type::ack error",t)}}return M}}else this.logger.error("imsocket::unrecognize dataStr",slice(t).call(t,0,20))},a.send=function send(t){var a,u={data:t,type:"message",endpoint:""};null===(a=this.websocket)||void 0===a||a.send(this.encodePacket(u))},BaseWebsocket}(vo);function uniq(t){t=t||[];for(var a=[],u=0;u<t.length;u++)-1===indexOf(a).call(a,t[u])&&a.push(t[u]);return a}function _createForOfIteratorHelperLoose(t,a){var u,h=void 0!==fi&&vu(t)||t["@@iterator"];if(h)return bind$1(u=(h=h.call(t)).next).call(u,h);if(zl(t)||(h=function _unsupportedIterableToArray(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray(t,a);var h=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===h&&t.constructor&&(h=t.constructor.name),"Map"===h||"Set"===h?gu(t):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?_arrayLikeToArray(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){h&&(t=h);var m=0;return function(){return m>=t.length?{done:!0}:{done:!1,value:t[m++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,h=Array(a);u<a;u++)h[u]=t[u];return h}!function(t){t[t.ACTIVE=1]="ACTIVE",t[t.KICKED=2]="KICKED",t[t.OFFLINE=3]="OFFLINE"}(nh||(nh={}));var ch=function(){function V1ClientSocket(t,a){this.linkUrls=[],this.isAutoReconnect=!1,this.packetTimeout=3e4,this.linkSSL=!0,this.packetSer=1,this.retryCount=0,this.reconnectTimer=0,this.backoff=new rh({max:8e3,min:1600,jitter:.01}),this.sendingCmdMap=new hc,this.pingTimer=0,this.hasNetworkListener=!1,this.core=t,a&&(this.auth=a),this.logger=t.logger,this.reporter=t.reporter,this.timerManager=t.timerManager}var t=V1ClientSocket.prototype;return t.setSessionId=function setSessionId(t){},t.setLinkSSL=function setLinkSSL(t){this.linkSSL=t},t.connect=function connect(t,a){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,cs.mark((function _callee(){var u,h,m,g,E,I;return cs.wrap((function _callee$(M){for(;;)switch(M.prev=M.next){case 0:if(validate({linkUrls:{type:"array",itemType:"string",required:!1}},t),/^(unconnected|waitReconnect)$/.test(this.core.status)){M.next=5;break}return u="Core socket status is "+this.core.status+", and would not connect",this.logger.warn(u),M.abrupt("return",za.reject(u));case 5:this.core.status="connecting",t.linkUrls&&t.linkUrls.length>0&&(this.linkUrls=concat(h=t.linkUrls).call(h,this.linkUrls),this.linkUrls=uniq(this.linkUrls)),0===this.linkUrls.length&&this.linkUrls.push("weblink.netease.im:443"),m=0;case 9:if(!(m<this.linkUrls.length)){M.next=31;break}return g=this.linkUrls[m],E=(new Date).getTime(),M.prev=12,M.next=15,this.doConnect(g);case 15:return this.core.status="connected",this.logger.log("clientsocketV1::connect success with url: "+g),M.abrupt("return",g);case 20:M.prev=20,M.t0=M.catch(12),I=M.t0,a&&a(I,g),this.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account,start_time:E,action:0,exception_service:6}),this.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof I.code?I.code:0,description:I.message||""+I.code,operation_type:0,target:g},{asyncParams:au.net.getNetworkStatus()}),this.reporter.reportTraceEnd("exceptions",1),this.logger.warn("clientsocketV1::connect failed with url: "+g,M.t0);case 28:m++,M.next=9;break;case 31:throw 0===this.retryCount?this.doDisconnect(nh.ACTIVE,"SocketHandshakeFailed"):this.doDisconnect(nh.OFFLINE,"ReconnectHadRetryAllLinks"),new Error("clientSocketV1::socket xhr or socket connect failed");case 33:case"end":return M.stop()}}),_callee,this,[[12,20]])})))},t.doConnect=function doConnect(t){var a=this,u=!1;return new za((function(h,m){var g;a.socket=new sh(a.core,t,a.linkSSL),a.socket.on("connect",(function(){a.logger.log("clientSocketV1::on connect",t),a.core.reporterHookLinkKeep&&(a.core.reporterHookLinkKeep.start(),a.core.reporterHookLinkKeep.update({code:0,description:"connection begin",operation_type:0,target:t})),u=!0,h()})),a.socket.on("message",bind$1(g=a.onMessage).call(g,a)),a.socket.on("disconnect",(function(h){return __awaiter(a,void 0,void 0,cs.mark((function _callee2(){return cs.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:if(this.logger.log("clientSocketV1::socket on disconnect",h),!this.core.reporterHookLinkKeep){a.next=5;break}return a.next=4,this.core.reporterHookLinkKeep.update({code:(null==h?void 0:h.code)||0,description:(null==h?void 0:h.reason)||"socket on disconnect",operation_type:1,target:t});case 4:this.core.reporterHookLinkKeep.end(!1);case 5:u=!0,this.doDisconnect(nh.OFFLINE,"SocketOnDisconnect");case 7:case"end":return a.stop()}}),_callee2,this)})))})),a.socket.on("handshakeFailed",(function(t){u?a.ping():(a.logger.error('clientsocketV1::handshake failed: "'+(t&&t.message)+'"'),a.cleanSocket()),u=!0,m(t)}))}))},t.cleanSocket=function cleanSocket(){this.socket&&("function"==typeof this.socket.removeAllListeners&&this.socket.removeAllListeners(),"function"==typeof this.socket.close&&this.socket.close(),this.socket=void 0)},t.beforeConnect=function beforeConnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer)},t.resetConnectStatus=function resetConnectStatus(){clearTimeout(this.reconnectTimer),this.backoff.reset(),this.retryCount=0,this.initOnlineListener()},t.doDisconnect=function doDisconnect(t,a,u){var h,m,g,E,I;if(this.logger.log("doDisconnect: type "+t+", description "+a),"unconnected"!==this.core.status){var M={1:"close",2:"kicked",3:"broken"}[t]||"";this.markAllCmdInvaild(new Ll({code:415,desc:"Packet timeout due to instance disconnect",detail:{reason:"Packet timeout due to instance disconnect",disconnect_reason:M}})),this.timerManager.destroy(),clearTimeout(this.pingTimer),this.cleanSocket();var S=!this.core.options.needReconnect||this.retryCount>=this.core.options.reconnectionAttempts;if(t===nh.ACTIVE||S)this.logger.log("doDisconnect: emit disconnect, type "+t,S),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(h=this.auth)||void 0===h||h.emit("disconnect"),this.destroyOnlineListener();else if(t===nh.KICKED){this.logger.log("doDisconnect: kicked"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer);var C="string"==typeof a?{reason:"unknow",message:a}:a;this.core.eventBus.emit("kicked",C),this.core.emit("kicked",C),null===(m=this.auth)||void 0===m||m.emit("kicked",C),this.destroyOnlineListener()}else t===nh.OFFLINE&&this.core.V1NIMLoginService.isManualLoginAttempt?(this.logger.log("doDisconnect: offline in manual login phase. no reconnect"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.destroyOnlineListener()):t===nh.OFFLINE&&(null===(E=null===(g=this.auth)||void 0===g?void 0:g.authenticator)||void 0===E?void 0:E.checkLoginTerminalCode(null==u?void 0:u.code))?(this.logger.log("doDisconnect: login terminal code "+(null==u?void 0:u.code)+", no reconnect"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.destroyOnlineListener(),this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(I=this.auth)||void 0===I||I.emit("disconnect")):t===nh.OFFLINE?(this.logger.log("doDisconnect: start to reconnect"),this.attempToReconnect()):this.logger.log("doDisconnect: nothing to do")}else this.logger.warn("doDisconnect: already unconnected")},t.attempToReconnect=function attempToReconnect(){var t,a,u=this;if("waitReconnect"!==this.core.status){0===this.retryCount&&(this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(t=this.auth)||void 0===t||t.emit("disconnect"));var h=this.backoff.duration();this.retryCount++,this.logger.log("willReconnect "+this.retryCount+" "+h),this.core.eventBus.emit("willReconnect",{retryCount:this.retryCount,duration:h}),this.core.emit("willReconnect",{retryCount:this.retryCount,duration:h}),null===(a=this.auth)||void 0===a||a.emit("willReconnect",{retryCount:this.retryCount,duration:h}),this.core.status="waitReconnect",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=go((function(){return __awaiter(u,void 0,void 0,cs.mark((function _callee3(){var t=this;return cs.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:if("waitReconnect"===this.core.status){a.next=3;break}return this.logger.warn("doDisconnect: reconnectTimer status is "+this.core.status+", would not go on reconnecting"),a.abrupt("return");case 3:return a.next=5,au.net.getNetworkStatus();case 5:!1===a.sent.net_connect?(this.logger.log("doDisconnect: skip this reconnection attempt because network is offline"),this.core.status="connecting",this.retryCount>=this.core.options.reconnectionAttempts?this.doDisconnect(nh.OFFLINE,"MaxReconnectionAttemptExceed"):this.attempToReconnect()):this.core.V1NIMLoginService.login({isAutoReconnect:!0}).catch((function(){t.logger.error("clientsocketV1::attempToReconnect failed "+t.retryCount)}));case 7:case"end":return a.stop()}}),_callee3,this)})))}),h)}else this.logger.warn("doDisconnect: already is waiting reconnect")},t.sendCmd=function sendCmd(t,a,u){var h=this;if("logined"!==this.core.status&&"login"!==t&&"chatroomLogin"!==t&&"qchatLogin"!==t)return this.logger.warn("instance status is "+this.core.status+", so can not sendCmd "+t),za.reject({cmd:t,error:{code:"No_connected",message:"Connection not established",timetag:(new Date).getTime()}});if(!this.socket||!this.socket.send)return za.reject("No_socket");var m="heartbeat"!==t,g=m?this.packetSer++:0,E=function createCmd(t,a,u,h){var m=Mu[t];if(!m)return u.error("createCmd:: can not find cmd config: ",t),null;var g,E={SER:a,SID:m.sid,CID:m.cid,Q:[]};return m.params&&h&&forEach$1(g=m.params).call(g,(function(t){var a=h[t.name];if(null!=a){var u=t.type,m=t.reflectMapper,g=t.select;switch(t.type){case"PropertyArray":u="ArrayMable",a=map$6(a).call(a,(function(t){return{t:"Property",v:m?serialize(t,m,g):t}}));break;case"Property":a=m?serialize(a,m,g):a;break;case"Bool":a=a?"true":"false"}E.Q.push({t:u,v:a})}})),{packet:E,hasPacketResponse:"boolean"!=typeof m.hasPacketResponse||m.hasPacketResponse,hasPacketTimer:"boolean"!=typeof m.hasPacketTimer||m.hasPacketTimer}}(t,g,this.logger,a);if(!E){var I="SendCmd "+g+" "+t+" error";return this.logger.error(I),za.reject(new Error(I))}var M=E.packet,S=E.hasPacketResponse,C=E.hasPacketTimer,T=gs(M);m&&(this.logger.getDebugMode()?this.logger.debug("clientsocketV1::sendCmd",t,"ser:"+g,T):this.logger.log("clientsocketV1::sendCmd",t,"ser:"+g));var b=(new Date).getTime();return new za((function(m,E){S&&h.sendingCmdMap.set(g,{cmd:t,params:a,callback:[m,E],timer:C?go((function(){var a=new Ll({code:408,desc:"Packet Timeout",detail:{reason:"Packet Timeout",cmd:t,ser:g,timetag:Za()}});h.markCmdInvalid(g,a,t)}),u&&u.timeout?u.timeout:h.core.config.timeout):null});try{h.socket.send(T),S||m(M)}catch(a){var I=new Ll({code:415,detail:{reason:a&&a.message||"Unable to send packet",cmd:t,ser:g,timetag:Za(),rawError:a}});h.markCmdInvalid(g,I,t),E(a)}})).catch((function(t){var a,u=[408,415];if(!includes(u).call(u,t.code))return za.reject(t);h.reporter.reportTraceStart("exceptions",{user_id:h.core.options.account,trace_id:null===(a=h.socket)||void 0===a?void 0:a.sessionId,start_time:b,action:2,exception_service:6});var m=get(t,"data.disconnect_reason")||"",g=408===t.code?"Send failed due to timeout":"Send failed. Reason unknown";return g=415===t.code?gs({disconnect_reason:m}):g,h.reporter.reportTraceUpdateV2("exceptions",{code:t.code||415,description:g,operation_type:1,target:M.SID+"-"+M.CID,context:""+M.SER},{asyncParams:au.net.getNetworkStatus()}),h.reporter.reportTraceEnd("exceptions",1),za.reject(t)}))},t.onMessage=function onMessage(t){var a=function parseCmd(t,a){var u,h;try{h=JSON.parse(t)}catch(u){return void a.error('Parse command error:"'+t+'"')}var m=h.sid+"_"+h.cid,g=h.r;if(includes(u=["4_1","4_2","4_10","4_11"]).call(u,m)){var E=h.r[1].headerPacket;m=E.sid+"_"+E.cid,h.sid=E.sid,h.cid=E.cid,g=h.r[1].body}var I=Su[m],M=[];if(I){for(var S,C=_createForOfIteratorHelperLoose$4(I);!(S=C()).done;){var T=S.value;M.push(parseEachCmd(h,T.config,T.cmd,g,a))}return M}a.error("parseCmd:: mapper not exist",m,h.code)}(t,this.logger);if(a)for(var u,h=_createForOfIteratorHelperLoose(a);!(u=h()).done;){var m=u.value,g=m.raw.ser;if(m.error&&this.logger.error("core:onMessage packet error",m.raw.sid+"_"+m.raw.cid+", ser:"+g+",",m.error),m.notFound)return void this.logger.warn("clientsocketV1::onMessage packet not found",m.raw.sid+"_"+m.raw.cid+", ser:"+g);"heartbeat"!==m.cmd&&(this.logger.getDebugMode()?this.logger.debug("imsocket::recvCmd ser:"+g,m.cmd,m.content):this.logger.log("imsocket::recvCmd ser:"+g,m.cmd)),this.packetHandler(m)}},t.packetHandler=function packetHandler(t){var a,u,h,m,g=this;if(t){var E=t.raw.ser,I=this.sendingCmdMap.get(E);if(I&&I.cmd===t.cmd){var M=I.callback,S=I.timer,C=I.params;if(clearTimeout(S),t.params=C,this.sendingCmdMap.delete(E),"heartbeat"===t.cmd)return void M[0]();var T=null===(u=null===(a=this.core[t.service])||void 0===a?void 0:a.process)||void 0===u?void 0:u.call(a,t);T&&"function"==typeof T.then?T.then((function(t){M[0](t)})).catch((function(t){M[1](t)})):(this.logger.log("imsocket:: handlerFn without promise",t.service,t.cmd),M[0]())}else{var b=null===(m=null===(h=this.core[t.service])||void 0===h?void 0:h.process)||void 0===m?void 0:m.call(h,t);b&&"function"==typeof b.then&&b.catch((function(t){g.logger.error("imsocket::no obj cache, no process handler",t)}))}}},t.markCmdInvalid=function markCmdInvalid(t,a,u){var h=this.sendingCmdMap.get(t);if(h){var m=h.callback,g=h.timer;g&&clearTimeout(g),this.sendingCmdMap.delete(t),this.logger.warn("packet "+t+", "+u+" is invalid:",a),m[1](a)}},t.markAllCmdInvaild=function markAllCmdInvaild(t){var a,u=this;this.logger.log("markAllCmdInvaild",t),forEach$1(a=this.sendingCmdMap).call(a,(function(a){var h=a.callback,m=a.timer,g=a.cmd;u.logger.log('markAllCmdInvaild:: cmd "'+g+'"'),m&&clearTimeout(m),h[1](t)})),this.sendingCmdMap.clear()},t.ping=function ping(){var t;return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){var a=this;return cs.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return clearTimeout(this.pingTimer),u.prev=1,u.next=4,this.sendCmd("heartbeat");case 4:u.next=18;break;case 6:return u.prev=6,u.t0=u.catch(1),u.next=10,this.testHeartBeat5Timeout();case 10:if(!u.sent){u.next=18;break}if(!this.core.reporterHookLinkKeep){u.next=16;break}return u.next=15,this.core.reporterHookLinkKeep.update({code:0,description:"Heartbeat-discovered link failure",operation_type:1,target:null===(t=this.socket)||void 0===t?void 0:t.url});case 15:this.core.reporterHookLinkKeep.end(!0);case 16:return this.doDisconnect(nh.OFFLINE,"PingError"),u.abrupt("return");case 18:this.pingTimer=go((function(){a.ping()}),3e4);case 19:case"end":return u.stop()}}),_callee4,this,[[1,6]])})))},t.testHeartBeat5Timeout=function testHeartBeat5Timeout(){return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var t;return cs.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:clearTimeout(this.pingTimer),t=0;case 2:if(!(t<5)){a.next=15;break}return a.prev=3,a.next=6,this.sendCmd("heartbeat",{},{timeout:3e3});case 6:return a.abrupt("return",!1);case 9:a.prev=9,a.t0=a.catch(3),this.logger.log("clientsocketV1:: test heartbeat "+t+" Timeout");case 12:t++,a.next=2;break;case 15:return a.abrupt("return",!0);case 16:case"end":return a.stop()}}),_callee5,this,[[3,9]])})))},t.initOnlineListener=function initOnlineListener(){var t=this;this.hasNetworkListener||(this.logger.log("clientsocketV1::onlineListener:init"),this.hasNetworkListener=!0,au.net.onNetworkStatusChange((function(a){t.logger.log("clientsocketV1::onlineListener:network change",a),a.isConnected&&"logined"===t.core.status?t.ping():a.isConnected&&"waitReconnect"===t.core.status?(t.reconnectTimer&&clearTimeout(t.reconnectTimer),t.core.V1NIMLoginService.login({isAutoReconnect:!0}).catch((function(){t.logger.error("clientsocketV1::attempToReconnect failed "+t.retryCount)}))):a.isConnected||t.doDisconnect(nh.OFFLINE,"OfflineListener")})))},t.destroyOnlineListener=function destroyOnlineListener(){this.logger.log("clientsocketV1::onlineListener:destroy"),au.net.offNetworkStatusChange(),this.hasNetworkListener=!1},t.disconnect=function disconnect(){switch(this.core.status){case"connected":case"logined":case"connecting":case"waitReconnect":return this.doDisconnect(nh.ACTIVE,"UserActiveDisconnect"),za.resolve();default:return za.resolve()}},V1ClientSocket}(),lh={"1_2":"heartbeat","24_2":"qchatLogin","24_4":"qchatExit","24_5":"qchatBeKicked","24_8":"qchatLoginClientChange","24_9":"qchatKick","24_19":"qchatSync"},uh={qchatLoginTag:{appkey:1,account:2,authType:3,token:4,loginExt:5,clientType:6,clientSession:7,deviceId:8,sdkVersion:9,sdkType:10,userAgent:11,customTag:12,customClientType:13,sdkHumanVersion:14,pushTokenName:20,pushToken:21,pushkitTokenName:22,pushkitToken:23,deviceInfo:24,customPushContentType:25,os:30,mac:31,bundleId:32,simCarrierCode:33,simCountryCode:34,networkCode:35,browser:36,deviceModel:37,androidid:38,imei:39,idfv:40,openuuid:41,pushType:100,hasTokenPreviously:101,consid:102,clientIP:103,clientPort:104,loginTime:105}},dh=function getCmdConfig(){var t=function getDeserializeTag(){return invertSerializeMap(uh)}();return{heartbeat:{sid:1,cid:2,service:"qchatAuth"},qchatLogin:{sid:24,cid:2,service:"qchatAuth",params:[{type:"Property",name:"qchatLoginReqTag",reflectMapper:uh.qchatLoginTag}],response:[{type:"Property",name:"qchatLoginResTag",reflectMapper:t.qchatLoginTag},{type:"PropertyArray",name:"qchatMultiLoginResult",reflectMapper:t.qchatLoginTag}]},qchatExit:{sid:24,cid:4,service:"qchatAuth"},qchatBeKicked:{sid:24,cid:5,service:"qchatAuth",response:[{type:"Property",name:"beKickedTag",reflectMapper:{1:"clientType",2:"reason",3:"ext",4:"customClientType"}}]},qchatLoginClientChange:{sid:24,cid:8,service:"qchatAuth",response:[{type:"Byte",name:"state"},{type:"Property",name:"qchatLoginResTag",reflectMapper:t.qchatLoginTag}]},qchatKick:{sid:24,cid:9,service:"qchatAuth",params:[{type:"StrArray",name:"deviceIds"}],response:[{type:"StrArray",name:"deviceIds"}]},qchatSync:{sid:24,cid:19,service:"qchatAuth",hasPacketTimer:!1,params:[{type:"Property",name:"qchatSyncTag",reflectMapper:{systemNotification:1,pushConfig:2}}],response:[{type:"Long",name:"timetag"}]}}};function formatLoginInfo(t,a){void 0===a&&(a=1);var u=format({clientPort:{type:"number"},clientType:{type:"enum",values:op},customClientType:{type:"number"},loginTime:{type:"number"},hasTokenPreviously:{type:"boolean"},pushType:{type:"number"}},t);return Ot(Ot({},u),{online:2!==a})}var ph={1:{reason:"samePlatformKick",message:"The same account is not allowed to multiple login at the same time"},2:{reason:"serverKick",message:"Kicked out by IM server"},3:{reason:"otherPlatformKick",message:"Kicked out by other client of your account"},4:{reason:"silentlyKick",message:"Quietly kicked"}};var hh={BROWSER:0,RN:2,UNIAPP:3,WECHAT:6},mh=function(){function V1AuthAuthenticatorService(t){this.core=t}return V1AuthAuthenticatorService.prototype.verifyAuthentication=function verifyAuthentication(t){return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,cs.mark((function _callee(){var a,u,h,m,g,E,I;return cs.wrap((function _callee$(M){for(;;)switch(M.prev=M.next){case 0:return a=this.core.options,u=au.getSystemInfo(),h=Ot(Ot({},a),{appLogin:t?0:1,clientType:16,clientSession:this.core.config.clientSession,deviceId:this.core.config.deviceId,sdkVersion:100830,sdkType:hh[au.platform]||0,userAgent:"Native/10.8.30",sdkHumanVersion:"10.8.30",os:u.os,browser:u.browser}),M.next=5,this.core.sendCmd("qchatLogin",{qchatLoginReqTag:h});case 5:if(!(m=M.sent).error){M.next=8;break}throw m.error;case 8:return g=m.content,E=g.qchatLoginResTag,I=g.qchatMultiLoginResult,E.isAutoReconnect=t,M.abrupt("return",{loginResult:formatLoginInfo(E),multiLoginResults:map$6(I).call(I,(function(t){return formatLoginInfo(t)}))});case 11:case"end":return M.stop()}}),_callee,this)})))},V1AuthAuthenticatorService}(),fh=function(t){function QChatAuthService(a){var u;return(u=t.call(this,"qchatAuth",a)||this).account="",u.token="",u.deviceId="",u.isManualLoginAttempt=!1,registerParser({cmdMap:lh,cmdConfig:dh()}),u.authenticatorService=new mh(a),u}Tt(QChatAuthService,t);var a=QChatAuthService.prototype;return a.login=function login(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,cs.mark((function _callee(){return cs.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return t.isAutoReconnect||(this.isManualLoginAttempt=!0),a.next=3,this._connect(t);case 3:return this.core.abtest.abtRequest(),a.prev=4,a.next=7,this.doLogin(t.isAutoReconnect);case 7:this.isManualLoginAttempt=!1,a.next=14;break;case 10:throw a.prev=10,a.t0=a.catch(4),this.isManualLoginAttempt=!1,a.t0;case 14:case"end":return a.stop()}}),_callee,this,[[4,10]])})))},a._connect=function _connect(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,cs.mark((function _callee2(){var a,u;return cs.wrap((function _callee2$(h){for(;;)switch(h.prev=h.next){case 0:if(/^(unconnected|waitReconnect)$/.test(this.core.status)){h.next=4;break}return a="Instance status is "+this.core.status+", and would not connect",this.logger.warn(a),h.abrupt("return",za.reject(a));case 4:return this.core.clientSocket.beforeConnect(),u=t.isAutoReconnect||!1,h.next=8,this.core.clientSocket.connect({linkUrls:this.core.options.linkAddresses,isAutoReconnect:u});case 8:case"end":return h.stop()}}),_callee2,this)})))},a.doLogin=function doLogin(t){var a;return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,cs.mark((function _callee3(){var u;return cs.wrap((function _callee3$(h){for(;;)switch(h.prev=h.next){case 0:return h.prev=0,h.next=3,this.authenticatorService.verifyAuthentication(t);case 3:u=h.sent,this.core.status="logined",h.next=14;break;case 7:if(h.prev=7,h.t0=h.catch(0),this.core.logger.warn("doLogin:: login failed",h.t0),this.core.clientSocket.doDisconnect(nh.OFFLINE,this.isManualLoginAttempt?"FailedToInitializeLogin":"ReconnectLoginFailed"),!this.isManualLoginAttempt){h.next=13;break}throw h.t0;case 13:return h.abrupt("return");case 14:return h.prev=14,h.next=17,this.core.cloudStorage.init(null===(a=u.loginResult)||void 0===a?void 0:a.loginTime);case 17:h.next=22;break;case 19:h.prev=19,h.t1=h.catch(14),this.core.logger.error("doLogin:: cloudStorage init failed ",h.t1);case 22:return this.core.eventBus.emit("logined",u.loginResult),this.core.emit("logined",u.loginResult),u.multiLoginResults&&u.multiLoginResults.length>0&&this.core.emit("multiSpotLogin",u.multiLoginResults),this.core.logger.log("login done"),this.core.clientSocket.resetConnectStatus(),h.next=29,this.sync();case 29:this.core.clientSocket.ping();case 30:case"end":return h.stop()}}),_callee3,this,[[0,7],[14,19]])})))},a.sync=function sync(){return __awaiter(this,void 0,void 0,cs.mark((function _callee4(){return cs.wrap((function _callee4$(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.core.sendCmd("qchatSync",{qchatSyncTag:{systemNotification:this.syncTimeTag||0}});case 2:case"end":return t.stop()}}),_callee4,this)})))},a.kick=function kick(t){var a;return __awaiter(this,void 0,void 0,cs.mark((function _callee5(){var u;return cs.wrap((function _callee5$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("qchatKick",t);case 2:return u=h.sent,h.abrupt("return",null===(a=u.content)||void 0===a?void 0:a.deviceIds);case 4:case"end":return h.stop()}}),_callee5,this)})))},a.qchatSyncHandler=function qchatSyncHandler(t){var a,u;this.logger.log("sync: emit syncdone",null===(a=t.content)||void 0===a?void 0:a.timetag),this.core.emit("syncdone"),this.syncTimeTag=(null===(u=t.content)||void 0===u?void 0:u.timetag)||0},a.qchatLoginClientChangeHandler=function qchatLoginClientChangeHandler(t){if(t.error)this.logger.error("qchatLoginClientChangeHandler:: error, ",t.error);else{var a=t.content,u=formatLoginInfo(a.qchatLoginResTag,a.state);this.core.emit("multiSpotLogin",[u])}},a.qchatBeKickedHandler=function qchatBeKickedHandler(t){if(t.error)this.logger.error("qchatBeKickedHandler error, ",t.error);else{var a=function formatBeKickedTag(t){var a=format({clientType:{type:"enum",values:op},customClientType:{type:"number"}},t),u=ph[a.reason];return Ot(a,u=u||{reason:"unknow",message:"Unknown reason"})}(t.content.beKickedTag);this.logger.warn("bekicked::",a),this.core.clientSocket.doDisconnect(nh.KICKED,a)}},QChatAuthService}(ad),gh={user_id:"",trace_id:"",action:7,exception_service:6,duration:0,start_time:0,state:1,extension:[]},vh=function(){function ReporterHookLinkKeep(t,a){this.traceData=gh,this.core=t,this.traceData=Ot({},gh,a),this.traceData.extension=[]}var t=ReporterHookLinkKeep.prototype;return t.reset=function reset(){this.traceData=Ot({},gh),this.traceData.extension=[]},t.start=function start(){var t,a;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(a=null===(t=this.core.clientSocket)||void 0===t?void 0:t.socket)||void 0===a?void 0:a.sessionId)||"",this.traceData.start_time=(new Date).getTime()},t.update=function update(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a,u,h;return cs.wrap((function _callee$(m){for(;;)switch(m.prev=m.next){case 0:return m.next=2,au.net.getNetworkStatus();case 2:a=m.sent,u=a.net_type,h=a.net_connect,this.traceData.extension.push(Ot({code:0,foreground:!0,foreg_backg_switch:!1,net_type:u,net_connect:h},t));case 6:case"end":return m.stop()}}),_callee,this)})))},t.end=function end(t){var a=this.traceData.extension[0],u=this.traceData.extension[1];if(a&&0===a.operation_type&&u&&1===u.operation_type){var h=a.net_type!==u.net_type||a.net_connect!==u.net_connect;if(t||!h)return this.traceData.duration=(new Date).getTime()-this.traceData.start_time,this.core.reporter.report("exceptions",this.traceData),void this.reset();this.reset()}else this.reset()},ReporterHookLinkKeep}(),_h={user_id:"",trace_id:"",action:0,state:0,duration:0,start_time:0,offset:0,full_size:0,transferred_size:0,operation_type:0,remote_addr:""},yh="ReporterHook::setMonitorForResources:",Eh=function(){function ReporterHookCloudStorage(t,a){this.traceData=_h,this.core=t,this.traceData=Ot({},_h,a)}var t=ReporterHookCloudStorage.prototype;return t.reset=function reset(){this.traceData=Ot({},_h)},t.start=function start(){var t,a;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(a=null===(t=this.core.clientSocket)||void 0===t?void 0:t.socket)||void 0===a?void 0:a.sessionId)||"",this.traceData.start_time="timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Za()},t.update=function update(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){return cs.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.traceData.user_id){a.next=2;break}return a.abrupt("return");case 2:this.core.logger.log(yh+" upload update",t),Ot(this.traceData,t);case 4:case"end":return a.stop()}}),_callee,this)})))},t.end=function end(t){this.traceData.user_id&&(this.core.logger.log(yh+" upload end cause of "+t),this.traceData.state=t,this.traceData.duration=("timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Za())-this.traceData.start_time,this.core.reporter.report("nim_sdk_resources",this.traceData),this.traceData=_h)},ReporterHookCloudStorage}();function getIsDataReportEnable(t){var a,u,h=!0;return"boolean"==typeof(null===(a=null==t?void 0:t.reporterConfig)||void 0===a?void 0:a.enableCompass)?h=t.reporterConfig.enableCompass:"boolean"==typeof(null===(u=null==t?void 0:t.reporterConfig)||void 0===u?void 0:u.isDataReportEnable)&&(h=t.reporterConfig.isDataReportEnable),h}var Ih={debugLevel:"off",needReconnect:!0,reconnectionAttempts:****************,isAbtestEnable:!0,abtestUrl:Xp,abtestProjectKey:Zp},Mh=function(t){function QChat(a,u){var m;void 0===u&&(u={cloudStorageConfig:{}}),(m=t.call(this)||this).instanceName="QChat",m.status="unconnected",m.account="",m.eventBus=new vo,m.options={},m.qchatServer={},m.qchatChannel={},m.qchatMsg={},m.qchatRole={},m.qchatMedia={},m.cloudStorage={},m.logger=new Fp(a.debugLevel,u.loggerConfig),m.setInitOptions(a),m.otherOptions=u,m.timerManager=new zp,m.adapters=new Jp(h(m)),m.abtest=new th(h(m),{isAbtestEnable:m.options.isAbtestEnable,abtestUrl:m.options.abtestUrl,abtestProjectKey:Zp});var g="",E="";m.options.isFixedDeviceId?(g=au.localStorage.getItem("__QCHAT_DEVC_ID__")||Xl(),E=au.localStorage.getItem("__QCHAT_CLIENT_SESSION_ID__")||Xl(),au.localStorage.setItem("__QCHAT_DEVC_ID__",g),au.localStorage.setItem("__QCHAT_CLIENT_SESSION_ID__",E)):(g=Xl(),E=Xl()),m.config={timeout:8e3,deviceId:g,clientSession:E};var I=au.getSystemInfo(),M=function getCompassDataEndpoint(t,a){var u,h,m=null===(u=null==a?void 0:a.reporterConfig)||void 0===u?void 0:u.compassDataEndpoint,g=null===(h=null==a?void 0:a.reporterConfig)||void 0===h?void 0:h.reportConfigUrl;if(m)return m;if(g){var E=g.match(/^https:\/\/([^/]+)\/*/);return zl(E)&&E.length>=1?"https://"+E[1]:(t.error("Invalid reportConfigUrl: "+g),eh)}return eh}(m.logger,m.otherOptions);return m.reporter=new ns(Ot(Ot({},M?{compassDataEndpoint:M}:{}),{isDataReportEnable:getIsDataReportEnable(m.otherOptions),common:{app_key:a.appkey,dev_id:g,platform:"Web",sdk_ver:"10.8.30",env:"online",os_name:I.os,os_ver:I.osVer,model:I.hostEnvVer,manufactor:I.hostEnv,host_env:I.hostEnv,host_env_ver:I.hostEnvVer,v2:!1},request:au.request,logger:m.logger,autoStart:!0})),m.reporterHookLinkKeep=new vh(h(m)),m.reporterHookCloudStorage=new Eh(h(m)),au.setLogger(m.logger),m.qchatAuth=new fh(h(m)),m.auth=m.qchatAuth,m.V1NIMLoginService=m.qchatAuth,m.clientSocket=new ch(h(m)),m.qchatServer=new qd(h(m)),m.qchatChannel=new sp(h(m),u.qchatChannelConfig),m.qchatMsg=new Op(h(m)),m.qchatRole=new hp(h(m)),m.qchatMedia=new Dp(h(m),u.qchatMediaConfig||u.QChatMedia),m.cloudStorage=new Du(h(m),Ot({storageKeyPrefix:m.instanceName},u.cloudStorageConfig)),QChat.instance=h(m),m.logger.log("QChat init, version ","10.8.30"," sdk version ",100830," appkey ",a.appkey),m}Tt(QChat,t),QChat.getInstance=function getInstance(t,a){if(!QChat.instance){if(t)return new QChat(t,a);throw new Error("Instance not exist, please input options")}if(t){if(QChat.instance.options.account===t.account&&QChat.instance.options.appkey===t.appkey)return QChat.instance.setOptions(t),QChat.instance;throw new Error("Unexpected login")}return QChat.instance};var a=QChat.prototype;return a.connect=function connect(t){return void 0===t&&(t={}),this.auth.login(t)},a.login=function login(t){return void 0===t&&(t={}),this.auth.login(t)},a.setInitOptions=function setInitOptions(t){validate({account:{type:"string"},appkey:{type:"string"},token:{type:"string"},linkAddresses:{type:"array",itemType:"string",min:1},needReconnect:{type:"boolean",required:!1},reconnectionAttempts:{type:"number",required:!1},debugLevel:{type:"enum",values:Gp,required:!1}},t),this.logger.log("QChat::setInitOptions options is",t),this.account=t.account,this.options=Ot(Ot({},Ih),t)},a.setOptions=function setOptions(t){if("object"==typeof t&&null!==t&&(Object.prototype.hasOwnProperty.call(t,"account")&&t.account!==this.options.account||Object.prototype.hasOwnProperty.call(t,"appkey")&&t.appkey!==this.options.appkey))throw new Error("QChat::setOptions account and appkey is not allowed to reset");validate({token:{type:"string",required:!1},linkAddresses:{type:"array",itemType:"string",min:1,required:!1},needReconnect:{type:"boolean",required:!1},reconnectionAttempts:{type:"number",required:!1},debugLevel:{type:"enum",values:Gp,required:!1}},t),this.logger.log("QChat::setOptions options is",t),this.options=Ot(Ot({},this.options),t)},a.disconnect=function disconnect(){var t=this;switch(this.status){case"logined":return this.sendCmd("qchatExit",void 0,{timeout:1e3}).then((function(){t.clientSocket.doDisconnect(nh.ACTIVE,"UserActiveDisconnect")})).catch((function(a){t.logger.error("Instance::disconnect sendCmd:logout error",a),t.clientSocket.doDisconnect(nh.ACTIVE,"UserActiveDisconnect")}));case"connected":case"connecting":case"waitReconnect":return this.clientSocket.doDisconnect(nh.ACTIVE,"UserActiveDisconnect"),za.resolve();case"unconnected":case"destroyed":return za.resolve()}},a.logout=function logout(){return this.disconnect()},a.destroy=function destroy(){var t=this;return QChat.instance=void 0,this.disconnect().then((function(){t.status="destroyed",t.removeAllListeners(),t.eventBus.removeAllListeners(),t.logger.destroy(),t.reporter.destroy(),t.timerManager.destroy(),t.connect=emptyFuncWithPromise,t.disconnect=emptyFuncWithPromise,t.destroy=emptyFuncWithPromise}))},a.kickOtherClients=function kickOtherClients(t){return validate({deviceIds:{type:"array",itemType:"string"}},t),this.auth.kick(t)},a.sendCmd=function sendCmd(t,a,u){return this.clientSocket.sendCmd(t,a,u)},a.emit=function emit(a){var u=this;try{for(var h,m,g=Za(),E=arguments.length,I=new Array(E>1?E-1:0),M=1;M<E;M++)I[M-1]=arguments[M];var S=(h=t.prototype.emit).call.apply(h,concat(m=[this,a]).call(m,I)),C=Za()-g;return C>=10&&this.logger.warn("Core::emit event: "+a+" process takes: "+C+"ms"),S}catch(t){return this.logger.error("Core::emit event: "+a+". Error: "+t),go((function(){throw u.logger.error("Core::emit throw error in setTimeout. event: "+a+". Error: "+t),t}),0),!1}},QChat}(vo),Sh=createCommonjsModule((function(t,a){t.exports=function(){function object2String(t){if(t){var a,u="";return forEach$1(a=Qi(t)).call(a,(function(a,h){u+=0===h?"?":"&",u+=a+"="+t[a]})),u}return""}var t=function(t){function V2NIMError(a,u,h,m){var g;return(g=t.call(this,h)||this).source=a,g.code=u,g.desc=h,g.detail=m||{},g}return Tt(V2NIMError,t),V2NIMError}(bc(Error));function request(a,u){void 0===u&&(u={dataType:"json",method:"GET",timeout:5e3});var h="text"===u.dataType?"text/plain; charset=UTF-8":"application/json; charset=UTF-8",m="GET"===u.method?object2String(u.params):"";return new za((function(g,E){if(window.XMLHttpRequest){var I,M=new XMLHttpRequest;if(M.onreadystatechange=function(){if(4===M.readyState)if(200===M.status){try{I=JSON.parse(M.response||"{}")}catch(t){I=M.response}g({status:M.status,data:I})}else go((function(){E(new t(1,M.status,"readyState: "+M.readyState+"; statusText: "+M.statusText))}),0)},M.open(u.method,""+a+m),M.timeout=u.timeout||5e3,M.setRequestHeader("Content-Type",h),u.headers)for(var S in u.headers)M.setRequestHeader(S,u.headers[S]);M.ontimeout=function(a){E(new t(1,408,a&&a.message?a.message:"request timeout"))},M.send(gs(u.data))}else E(new t(2,10400,"request no suppout"))}))}return request}()})),Ch=createCommonjsModule((function(t,a){self,t.exports=function(){var t={d:function d(a,u){for(var h in u)t.o(u,h)&&!t.o(a,h)&&_i(a,h,{enumerable:!0,get:u[h]})},o:function o(t,a){return Object.prototype.hasOwnProperty.call(t,a)}},a={};t.d(a,{default:function _default(){return T}});var u=function e(t){for(var a in function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.directUploadAddr="https://wanproxy-web.127.net",this.retryCount=4,this.trunkSize=4194304,this.trunkUploadTimeout=5e4,this.getOffsetTimeout=1e4,this.version="1.0",this.enableCache=!0,this.logger=console,this.onError=function(t){},this.onProgress=function(t){},this.onUploadProgress=function(t){},this.onComplete=function(t){},t)this[a]=t[a]};function n(t,a){var u=void 0!==fi&&vu(t)||t["@@iterator"];if(!u){if(zl(t)||(u=function(t,a){if(t){var u;if("string"==typeof t)return r(t,a);var h=slice(u=Object.prototype.toString.call(t)).call(u,8,-1);return"Object"===h&&t.constructor&&(h=t.constructor.name),"Map"===h||"Set"===h?gu(t):"Arguments"===h||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(h)?r(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){u&&(t=u);var h=0,m=function i(){};return{s:m,n:function n(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}},e:function e(t){throw t},f:m}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var g,E=!0,I=!1;return{s:function s(){u=u.call(t)},n:function n(){var t=u.next();return E=t.done,t},e:function e(t){I=!0,g=t},f:function f(){try{E||null==u.return||u.return()}finally{if(I)throw g}}}}function r(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,h=new Array(a);u<a;u++)h[u]=t[u];return h}var h={privateObj:{},setItem:function setItem(t,a){h.privateObj[t]=a},getItem:function getItem(t){return h.privateObj[t]},removeItem:function removeItem(t){delete h.privateObj[t]},getKeys:function getKeys(){return Qi(h.privateObj)}},m={getFileKey:function getFileKey(t){var a=t.size.toString(),u=t.lastModified.toString();return"_NosUploader_"+t.name+slice(a).call(a,a.length-5)+slice(u).call(u,u.length-5)},getFileInfo:function getFileInfo(t){var a=h.getItem(t);if(!a)return null;try{return JSON.parse(a)}catch(t){return null}},initFile:function initFile(t,a,u){m.clearExpiredInfo();var g=this.getFileKey(a),E={ctx:void 0!==t.ctx?t.ctx:"",bucket:t.bucketName,obj:t.objectName,token:t.token,modifyAt:Za(),end:!1};return t.payload&&(E.payload=t.payload),u&&h.setItem(g,gs(E)),g},setUploadContext:function setUploadContext(t,a,u){var m=this.getFileInfo(t);m&&(m.ctx=a,u&&h.setItem(t,gs(m)))},setComplete:function setComplete(t,a){var u=this.getFileInfo(t);u&&(u.modifyAt=Za(),u.end=!0,a&&h.setItem(t,gs(u)))},getUploadContext:function getUploadContext(t){var a=this.getFileInfo(t);return a?a.ctx:""},removeFileInfo:function removeFileInfo(t){0===indexOf(t).call(t,"_NosUploader_")&&h.removeItem(t)},clearExpiredInfo:function clearExpiredInfo(){var t,a="function"==typeof h.getKeys?h.getKeys():Qi(h),u=Za(),g=[],E=n(a);try{for(E.s();!(t=E.n()).done;){var I=t.value;if(0===indexOf(I).call(I,"_NosUploader_")){var M=m.getFileInfo(I);null===M||u-M.modifyAt>T.expireTime?h.removeItem(I):g.push({fileInfo:M,key:I})}}}catch(t){E.e(t)}finally{E.f()}if(g.length>T.maxFileCache){var S,C,b=n(slice(S=sort(g).call(g,(function(t,a){return a.fileInfo.modifyAt-t.fileInfo.modifyAt}))).call(S,T.maxFileCache));try{for(b.s();!(C=b.n()).done;){var R,A=C.value;0===indexOf(R=A.key).call(R,"_NosUploader_")&&h.removeItem(A.key)}}catch(t){b.e(t)}finally{b.f()}}}},g=m;function c(t){return(c="function"==typeof fi&&"symbol"==typeof qi?function(t){return typeof t}:function(t){return t&&"function"==typeof fi&&t.constructor===fi&&t!==fi.prototype?"symbol":typeof t})(t)}function s(t,a){return!a||"object"!==c(a)&&"function"!=typeof a?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):a}function f(t){var a="function"==typeof hc?new hc:void 0;return(f=function f(t){var u,h;if(null===t||(h=t,-1===indexOf(u=Function.toString.call(h)).call(u,"[native code]")))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==a){if(a.has(t))return a.get(t);a.set(t,n)}function n(){return l(t,arguments,y(this).constructor)}return n.prototype=dt(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),d(n,t)})(t)}function l(t,a,u){return(l=p()?Mc:function(t,a,u){var h=[null];h.push.apply(h,a);var m=new(bind$1(Function).apply(t,h));return u&&d(m,u.prototype),m}).apply(null,arguments)}function p(){if("undefined"==typeof Reflect||!Mc)return!1;if(Mc.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Mc(Boolean,[],(function(){}))),!0}catch(t){return!1}}function d(t,a){return(d=ft||function(t,a){return t.__proto__=a,t})(t,a)}function y(t){return(y=ft?Ei:function(t){return t.__proto__||Ei(t)})(t)}var E=function(t){!function(t,a){if("function"!=typeof a&&null!==a)throw new TypeError("Super expression must either be null or a function");t.prototype=dt(a&&a.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),a&&d(t,a)}(r,t);var a,u,h=(a=r,u=p(),function(){var t,h=y(a);if(u){var m=y(this).constructor;t=Mc(h,arguments,m)}else t=h.apply(this,arguments);return s(this,t)});function r(t,a){var u;return function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,r),(u=h.call(this,"NosUploadError:"+t)).errCode=a,u.errMsg=t,u}return r}(f(Error)),I=function e(t,a,u){if("uploading"===t.uploadState){var h=t.config,m=t.param,I=g.getUploadContext(t.fileKey);if(!I)return u(0);var M=new XMLHttpRequest,S=h.directUploadAddr+"/".concat(m.bucketName)+"/".concat(encodeURIComponent(m.objectName))+"?uploadContext"+"&context=".concat(I)+"&version=".concat(h.version);M.onreadystatechange=function(){var m;if("abort"!==t.uploadState&&4===M.readyState){var I,C,T,b;try{b=JSON.parse(M.responseText)}catch(t){b={errMsg:"JsonParseError in getOffset. xhr.status = "+M.status+". xhr.responseText: "+M.responseText,errCode:500}}200===M.status?b.errCode?t.config.onError(new E(b.errMsg,b.errCode)):u(b.offset):M.status.toString().match(/^5/)?e(t,a-1,u):a>0?("function"==typeof(null===(m=h.logger)||void 0===m?void 0:m.error)&&h.logger.error(concat(I="getOffset(".concat(S,") error. retry after 3 seconds. ")).call(I,(new Date).toTimeString())),go((function(){e(t,a-1,u)}),3500)):M.status?(g.removeFileInfo(t.fileKey),h.onError(new E(concat(C=concat(T="getOffset(".concat(S,") error: ")).call(T,M.status," ")).call(C,M.statusText)))):h.onError(new E("getOffset(".concat(S,") error. no Error Code")))}},M.open("get",S),M.setRequestHeader("x-nos-token",m.token),M.timeout=h.getOffsetTimeout,M.send()}},M=function e(t,a,u,h){if("uploading"===t.uploadState){var m=t.param,I=t.config,M=slice(File.prototype),S=void 0!==m.ctx?m.ctx:"",C=a+I.trunkSize>=t.file.size,T=C?t.file.size:a+I.trunkSize,b=new XMLHttpRequest,R=I.directUploadAddr+"/".concat(m.bucketName)+"/".concat(encodeURIComponent(m.objectName));if(b.upload.onprogress=function(u){if("abort"!==t.uploadState){var h=0;u.lengthComputable?(h=(a+u.loaded)/t.file.size,I.onProgress(h),I.onUploadProgress({loaded:u.loaded,total:t.file.size,percentage:h,percentageText:(100*h).toFixed(2)+"%"})):I.onError(new E("browser does not support query upload progress"))}},b.onreadystatechange=function(){var m,M;if("abort"!==t.uploadState&&4===b.readyState){var S,T,A,N;try{N=JSON.parse(b.responseText)}catch(t){"function"==typeof(null===(m=I.logger)||void 0===m?void 0:m.error)&&I.logger.error("JsonParseError in uploadTrunk. xhr.status = "+b.status+". xhr.responseText: "+b.responseText,t),N={errMsg:"JsonParseError in uploadTrunk. xhr.status = "+b.status+". xhr.responseText: "+b.responseText}}200===b.status?(t.setContext(N.context),C?(h(),t.setComplete()):e(t,N.offset,I.retryCount,h)):b.status.toString().match(/^5/)?u>0?e(t,a,u-1,h):(g.removeFileInfo(t.fileKey),I.onError(new E(N.errMsg,N.errCode))):u>0?("function"==typeof(null===(M=I.logger)||void 0===M?void 0:M.error)&&I.logger.error(concat(S="uploadTrunk(".concat(R,") error. retry after 3 seconds. ")).call(S,(new Date).toTimeString())),go((function(){e(t,a,u-1,h)}),3500)):b.status?(g.removeFileInfo(t.fileKey),I.onError(new E(concat(T=concat(A="uploadTrunk(".concat(R,") error: ")).call(A,b.status," ")).call(T,b.statusText)))):I.onError(new E("uploadTrunk(".concat(R,") error. no Error Code. Please check your network")))}},b.open("post",R+"?offset=".concat(a)+"&complete=".concat(C)+"&context=".concat(S)+"&version=".concat(I.version)),b.setRequestHeader("x-nos-token",m.token),m.md5&&b.setRequestHeader("content-md5",m.md5),t.file.type&&b.setRequestHeader("content-type",t.file.type),b.timeout=I.trunkUploadTimeout,"undefined"!=typeof FileReader){var A=new FileReader;A.addEventListener("load",(function(t){var a;(null===(a=null==t?void 0:t.target)||void 0===a?void 0:a.result)instanceof ArrayBuffer&&t.target.result.byteLength>0?b.send(t.target.result):I.onError(new E("Read ArrayBuffer failed",194003))})),A.addEventListener("error",(function(t){var a=t.target.error;I.onError(new E("Read ArrayBuffer error. ".concat(a.toString()),194003))})),A.readAsArrayBuffer(M.call(t.file,a,T))}else b.send(M.call(t.file,a,T))}};function v(t,a){for(var u=0;u<a.length;u++){var h=a[u];h.enumerable=h.enumerable||!1,h.configurable=!0,"value"in h&&(h.writable=!0),_i(t,h.key,h)}}var S=function(){function e(t,a,u){!function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.uploadState="paused",this.config=u,this.file=t,this.param=a,this.fileKey=g.initFile(a,t,this.config.enableCache),this.resume()}var t;return(t=[{key:"resume",value:function value(){var t=this;if("uploading"!==this.uploadState){this.setUploadState("uploading");var a=this.config;I(this,a.retryCount,(function(u){M(t,u,a.retryCount,(function(){t.setUploadState("ended"),"function"==typeof a.onComplete&&a.onComplete(t.param)}))}))}}},{key:"pause",value:function value(){this.setUploadState("paused")}},{key:"abort",value:function value(){"ended"!==this.uploadState&&"abort"!==this.uploadState&&(this.setUploadState("abort"),this.config.onError(new E("Upload Aborted",10499)))}},{key:"setUploadState",value:function value(t){t!==this.uploadState&&(this.uploadState=t)}},{key:"setContext",value:function value(t){g.setUploadContext(this.fileKey,t,this.config.enableCache),this.param.ctx=t}},{key:"setComplete",value:function value(){g.setComplete(this.fileKey,this.config.enableCache),this.setUploadState("ended")}}])&&v(e.prototype,t),e}(),C={maxFileCache:1/0,expireTime:864e5,getFileUploadInformation:function getFileUploadInformation(t){var a=g.getFileKey(t),u=g.getFileInfo(a);return null===u?null:Za()-u.modifyAt>C.expireTime?(g.removeFileInfo(a),null):{uploadInfo:Ot({bucketName:u.bucket,objectName:u.obj,token:u.token,ctx:u.ctx},u.payload?{payload:u.payload}:{}),complete:u.end}},setMaxFileCache:function setMaxFileCache(t){C.maxFileCache=t},setExpireTime:function setExpireTime(t){C.expireTime=t},printCaches:function printCaches(){if("undefined"!=typeof localStorage)for(var t=0,a=Qi(localStorage);t<a.length;t++){var u=a[t],h=g.getFileInfo(u);h&&console.log(h,"modifiedAt",new Date(h.modifyAt).toTimeString())}},createConfig:function createConfig(t){return new u(t)},createTask:function createTask(t,a,u){return new S(t,a,u)}},T=C;return a.default}()})),Th={debug:function debug(){},log:function log(){},warn:function warn(){},error:function error(){}};function setLogger(t){Th=t}function isMobile(){if(!navigator||!navigator.userAgent)return!1;var t=[/Android/i,/webOS/i,/iPhone/i,/iPad/i,/iPod/i,/BlackBerry/i,/Windows Phone/i];return some(t).call(t,(function(t){return navigator.userAgent.match(t)}))}function isElectron(){var t;return!(!navigator||!navigator.userAgent)&&("object"==typeof navigator&&"string"==typeof navigator.userAgent&&indexOf(t=navigator.userAgent).call(t,"Electron")>=0)}function isBrowser(){return navigator&&navigator.userAgent}function uploadFileFn(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a,u,h,m,g,E,I;return cs.wrap((function _callee$(M){for(;;)switch(M.prev=M.next){case 0:if(m=Th,t.fileInput||t.file){M.next=3;break}throw new Error("File not exist");case 3:if(!t.file){M.next=7;break}g=t.file,M.next=21;break;case 7:if("string"!=typeof t.fileInput){M.next=16;break}if(!((E=document.getElementById(t.fileInput))&&E.files&&E.files[0])){M.next=13;break}g=E.files[0],M.next=14;break;case 13:throw new Error("Can not get file from fileInput");case 14:M.next=21;break;case 16:if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0])){M.next=20;break}g=t.fileInput.files[0],M.next=21;break;case 20:throw new Error("Can not get file from fileInput "+t.fileInput);case 21:if(!(t.maxSize&&g.size>t.maxSize)){M.next=23;break}throw new Error("The file exceeds maxSize limit. maxSize: "+t.maxSize+", get "+g.size);case 23:return M.next=25,new za((function(a,u){var h,E=Ch.getFileUploadInformation(g),I=Ch.createConfig({enableCache:!0,retryCount:0,directUploadAddr:t.chunkUploadHost,onError:function onError(t){u(t)},onUploadProgress:t.onUploadProgress||function(){},onComplete:function onComplete(t){a(t)}});if(E)if(E.complete)t.onUploadProgress&&t.onUploadProgress({total:g.size,loaded:g.size,percentage:1,percentageText:"100%"}),a(E.uploadInfo);else{h=Ch.createTask(g,E.uploadInfo,I);try{t.onUploadStart&&t.onUploadStart(h)}catch(t){m.error("Adapter uploadFile: options.onUploadStart error",t&&t.message),h.abort(),u(t)}}else{h=Ch.createTask(g,Ot(Ot({bucketName:t.nosToken.bucket,objectName:decodeURIComponent(t.nosToken.objectName),token:t.nosToken.token},t.md5?{md5:t.md5}:{}),t.payload?{payload:t.payload}:{}),I);try{t.onUploadStart&&t.onUploadStart(h)}catch(t){m.error("Adapter uploadFile: options.onUploadStart error",t&&t.message),h.abort(),u(t)}}}));case 25:return(I=M.sent).name=g.name,I.size=g.size,I.type=g.type,I.ext=lastIndexOf(a=I.name).call(a,".")>-1?slice(u=I.name).call(u,lastIndexOf(h=I.name).call(h,".")+1).toLowerCase():"",M.abrupt("return",I);case 31:case"end":return M.stop()}}),_callee)})))}function getFileUploadInformationFn(t){var a;if(t.file)a=t.file;else if("string"==typeof t.fileInput){var u=document.getElementById(t.fileInput);if(!(u&&u.files&&u.files[0]))throw new Error("Can not get file from fileInput");a=u.files[0]}else{if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0]))throw new Error("Can not get file from fileInput "+t.fileInput);a=t.fileInput.files[0]}return Ch.getFileUploadInformation(a)}
/*!
	 * Platform.js v1.3.6
	 * Copyright 2014-2020 Benjamin Tan
	 * Copyright 2011-2013 John-David Dalton
	 * Available under MIT license
	 */var bh=createCommonjsModule((function(a,u){(function(){var h={function:!0,object:!0}[typeof window]&&window||this,m=u,g=a&&!a.nodeType&&a,E=m&&g&&"object"==typeof t&&t;!E||E.global!==E&&E.window!==E&&E.self!==E||(h=E);var I=Math.pow(2,53)-1,M=/\bOpera/,S=Object.prototype,C=S.hasOwnProperty,T=S.toString;function capitalize(t){return(t=String(t)).charAt(0).toUpperCase()+t.slice(1)}function format(t){return t=trim(t),/^(?:webOS|i(?:OS|P))/.test(t)?t:capitalize(t)}function forOwn(t,a){for(var u in t)C.call(t,u)&&a(t[u],u,t)}function getClassOf(t){return null==t?capitalize(t):T.call(t).slice(8,-1)}function qualify(t){return String(t).replace(/([ -])(?!$)/g,"$1?")}function reduce(t,a){var u=null;return function each(t,a){var u=-1,h=t?t.length:0;if("number"==typeof h&&h>-1&&h<=I)for(;++u<h;)a(t[u],u,t);else forOwn(t,a)}(t,(function(h,m){u=a(u,h,m,t)})),u}function trim(t){return String(t).replace(/^ +| +$/g,"")}var b=function parse(t){var a=h,u=t&&"object"==typeof t&&"String"!=getClassOf(t);u&&(a=t,t=null);var m=a.navigator||{},g=m.userAgent||"";t||(t=g);var E,I,S=u?!!m.likeChrome:/\bChrome\b/.test(t)&&!/internal|\n/i.test(T.toString()),C="Object",b=u?C:"ScriptBridgingProxyObject",R=u?C:"Environment",A=u&&a.java?"JavaPackage":getClassOf(a.java),N=u?C:"RuntimeObject",w=/\bJava/.test(A)&&a.java,O=w&&getClassOf(a.environment)==R,x=w?"a":"α",k=w?"b":"β",P=a.document||{},L=a.operamini||a.opera,q=M.test(q=u&&L?L["[[Class]]"]:getClassOf(L))?q:L=null,D=t,V=[],U=null,G=t==g,B=G&&L&&"function"==typeof L.version&&L.version(),j=function getLayout(a){return reduce(a,(function(a,u){return a||RegExp("\\b"+(u.pattern||qualify(u))+"\\b","i").exec(t)&&(u.label||u)}))}([{label:"EdgeHTML",pattern:"Edge"},"Trident",{label:"WebKit",pattern:"AppleWebKit"},"iCab","Presto","NetFront","Tasman","KHTML","Gecko"]),W=function getName(a){return reduce(a,(function(a,u){return a||RegExp("\\b"+(u.pattern||qualify(u))+"\\b","i").exec(t)&&(u.label||u)}))}(["Adobe AIR","Arora","Avant Browser","Breach","Camino","Electron","Epiphany","Fennec","Flock","Galeon","GreenBrowser","iCab","Iceweasel","K-Meleon","Konqueror","Lunascape","Maxthon",{label:"Microsoft Edge",pattern:"(?:Edge|Edg|EdgA|EdgiOS)"},"Midori","Nook Browser","PaleMoon","PhantomJS","Raven","Rekonq","RockMelt",{label:"Samsung Internet",pattern:"SamsungBrowser"},"SeaMonkey",{label:"Silk",pattern:"(?:Cloud9|Silk-Accelerated)"},"Sleipnir","SlimBrowser",{label:"SRWare Iron",pattern:"Iron"},"Sunrise","Swiftfox","Vivaldi","Waterfox","WebPositive",{label:"Yandex Browser",pattern:"YaBrowser"},{label:"UC Browser",pattern:"UCBrowser"},"Opera Mini",{label:"Opera Mini",pattern:"OPiOS"},"Opera",{label:"Opera",pattern:"OPR"},"Chromium","Chrome",{label:"Chrome",pattern:"(?:HeadlessChrome)"},{label:"Chrome Mobile",pattern:"(?:CriOS|CrMo)"},{label:"Firefox",pattern:"(?:Firefox|Minefield)"},{label:"Firefox for iOS",pattern:"FxiOS"},{label:"IE",pattern:"IEMobile"},{label:"IE",pattern:"MSIE"},"Safari"]),Y=getProduct([{label:"BlackBerry",pattern:"BB10"},"BlackBerry",{label:"Galaxy S",pattern:"GT-I9000"},{label:"Galaxy S2",pattern:"GT-I9100"},{label:"Galaxy S3",pattern:"GT-I9300"},{label:"Galaxy S4",pattern:"GT-I9500"},{label:"Galaxy S5",pattern:"SM-G900"},{label:"Galaxy S6",pattern:"SM-G920"},{label:"Galaxy S6 Edge",pattern:"SM-G925"},{label:"Galaxy S7",pattern:"SM-G930"},{label:"Galaxy S7 Edge",pattern:"SM-G935"},"Google TV","Lumia","iPad","iPod","iPhone","Kindle",{label:"Kindle Fire",pattern:"(?:Cloud9|Silk-Accelerated)"},"Nexus","Nook","PlayBook","PlayStation Vita","PlayStation","TouchPad","Transformer",{label:"Wii U",pattern:"WiiU"},"Wii","Xbox One",{label:"Xbox 360",pattern:"Xbox"},"Xoom"]),$=function getManufacturer(a){return reduce(a,(function(a,u,h){return a||(u[Y]||u[/^[a-z]+(?: +[a-z]+\b)*/i.exec(Y)]||RegExp("\\b"+qualify(h)+"(?:\\b|\\w*\\d)","i").exec(t))&&h}))}({Apple:{iPad:1,iPhone:1,iPod:1},Alcatel:{},Archos:{},Amazon:{Kindle:1,"Kindle Fire":1},Asus:{Transformer:1},"Barnes & Noble":{Nook:1},BlackBerry:{PlayBook:1},Google:{"Google TV":1,Nexus:1},HP:{TouchPad:1},HTC:{},Huawei:{},Lenovo:{},LG:{},Microsoft:{Xbox:1,"Xbox One":1},Motorola:{Xoom:1},Nintendo:{"Wii U":1,Wii:1},Nokia:{Lumia:1},Oppo:{},Samsung:{"Galaxy S":1,"Galaxy S2":1,"Galaxy S3":1,"Galaxy S4":1},Sony:{PlayStation:1,"PlayStation Vita":1},Xiaomi:{Mi:1,Redmi:1}}),H=function getOS(a){return reduce(a,(function(a,u){var h=u.pattern||qualify(u);return!a&&(a=RegExp("\\b"+h+"(?:/[\\d.]+|[ \\w.]*)","i").exec(t))&&(a=function cleanupOS(t,a,u){var h={"10.0":"10",6.4:"10 Technical Preview",6.3:"8.1",6.2:"8",6.1:"Server 2008 R2 / 7","6.0":"Server 2008 / Vista",5.2:"Server 2003 / XP 64-bit",5.1:"XP",5.01:"2000 SP1","5.0":"2000","4.0":"NT","4.90":"ME"};return a&&u&&/^Win/i.test(t)&&!/^Windows Phone /i.test(t)&&(h=h[/[\d.]+$/.exec(t)])&&(t="Windows "+h),t=String(t),a&&u&&(t=t.replace(RegExp(a,"i"),u)),format(t.replace(/ ce$/i," CE").replace(/\bhpw/i,"web").replace(/\bMacintosh\b/,"Mac OS").replace(/_PowerPC\b/i," OS").replace(/\b(OS X) [^ \d]+/i,"$1").replace(/\bMac (OS X)\b/,"$1").replace(/\/(\d)/," $1").replace(/_/g,".").replace(/(?: BePC|[ .]*fc[ \d.]+)$/i,"").replace(/\bx86\.64\b/gi,"x86_64").replace(/\b(Windows Phone) OS\b/,"$1").replace(/\b(Chrome OS \w+) [\d.]+\b/,"$1").split(" on ")[0])}(a,h,u.label||u)),a}))}(["Windows Phone","KaiOS","Android","CentOS",{label:"Chrome OS",pattern:"CrOS"},"Debian",{label:"DragonFly BSD",pattern:"DragonFly"},"Fedora","FreeBSD","Gentoo","Haiku","Kubuntu","Linux Mint","OpenBSD","Red Hat","SuSE","Ubuntu","Xubuntu","Cygwin","Symbian OS","hpwOS","webOS ","webOS","Tablet OS","Tizen","Linux","Mac OS X","Macintosh","Mac","Windows 98;","Windows "]);function getProduct(a){return reduce(a,(function(a,u){var h=u.pattern||qualify(u);return!a&&(a=RegExp("\\b"+h+" *\\d+[.\\w_]*","i").exec(t)||RegExp("\\b"+h+" *\\w+-[\\w]*","i").exec(t)||RegExp("\\b"+h+"(?:; *(?:[a-z]+[_-])?[a-z]+\\d+|[^ ();-]*)","i").exec(t))&&((a=String(u.label&&!RegExp(h,"i").test(u.label)?u.label:a).split("/"))[1]&&!/[\d.]+/.test(a[0])&&(a[0]+=" "+a[1]),u=u.label||u,a=format(a[0].replace(RegExp(h,"i"),u).replace(RegExp("; *(?:"+u+"[_-])?","i")," ").replace(RegExp("("+u+")[-_.]?(\\w)","i"),"$1 $2"))),a}))}function getVersion(a){return reduce(a,(function(a,u){return a||(RegExp(u+"(?:-[\\d.]+/|(?: for [\\w-]+)?[ /-])([\\d.]+[^ ();/_-]*)","i").exec(t)||0)[1]||null}))}if(j&&(j=[j]),/\bAndroid\b/.test(H)&&!Y&&(E=/\bAndroid[^;]*;(.*?)(?:Build|\) AppleWebKit)\b/i.exec(t))&&(Y=trim(E[1]).replace(/^[a-z]{2}-[a-z]{2};\s*/i,"")||null),$&&!Y?Y=getProduct([$]):$&&Y&&(Y=Y.replace(RegExp("^("+qualify($)+")[-_.\\s]","i"),$+" ").replace(RegExp("^("+qualify($)+")[-_.]?(\\w)","i"),$+" $2")),(E=/\bGoogle TV\b/.exec(Y))&&(Y=E[0]),/\bSimulator\b/i.test(t)&&(Y=(Y?Y+" ":"")+"Simulator"),"Opera Mini"==W&&/\bOPiOS\b/.test(t)&&V.push("running in Turbo/Uncompressed mode"),"IE"==W&&/\blike iPhone OS\b/.test(t)?($=(E=parse(t.replace(/like iPhone OS/,""))).manufacturer,Y=E.product):/^iP/.test(Y)?(W||(W="Safari"),H="iOS"+((E=/ OS ([\d_]+)/i.exec(t))?" "+E[1].replace(/_/g,"."):"")):"Konqueror"==W&&/^Linux\b/i.test(H)?H="Kubuntu":$&&"Google"!=$&&(/Chrome/.test(W)&&!/\bMobile Safari\b/i.test(t)||/\bVita\b/.test(Y))||/\bAndroid\b/.test(H)&&/^Chrome/.test(W)&&/\bVersion\//i.test(t)?(W="Android Browser",H=/\bAndroid\b/.test(H)?H:"Android"):"Silk"==W?(/\bMobi/i.test(t)||(H="Android",V.unshift("desktop mode")),/Accelerated *= *true/i.test(t)&&V.unshift("accelerated")):"UC Browser"==W&&/\bUCWEB\b/.test(t)?V.push("speed mode"):"PaleMoon"==W&&(E=/\bFirefox\/([\d.]+)\b/.exec(t))?V.push("identifying as Firefox "+E[1]):"Firefox"==W&&(E=/\b(Mobile|Tablet|TV)\b/i.exec(t))?(H||(H="Firefox OS"),Y||(Y=E[1])):!W||(E=!/\bMinefield\b/i.test(t)&&/\b(?:Firefox|Safari)\b/.exec(W))?(W&&!Y&&/[\/,]|^[^(]+?\)/.test(t.slice(t.indexOf(E+"/")+8))&&(W=null),(E=Y||$||H)&&(Y||$||/\b(?:Android|Symbian OS|Tablet OS|webOS)\b/.test(H))&&(W=/[a-z]+(?: Hat)?/i.exec(/\bAndroid\b/.test(H)?H:E)+" Browser")):"Electron"==W&&(E=(/\bChrome\/([\d.]+)\b/.exec(t)||0)[1])&&V.push("Chromium "+E),B||(B=getVersion(["(?:Cloud9|CriOS|CrMo|Edge|Edg|EdgA|EdgiOS|FxiOS|HeadlessChrome|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\d.]+$)|UCBrowser|YaBrowser)","Version",qualify(W),"(?:Firefox|Minefield|NetFront)"])),(E=("iCab"==j&&parseFloat(B)>3?"WebKit":/\bOpera\b/.test(W)&&(/\bOPR\b/.test(t)?"Blink":"Presto"))||/\b(?:Midori|Nook|Safari)\b/i.test(t)&&!/^(?:Trident|EdgeHTML)$/.test(j)&&"WebKit"||!j&&/\bMSIE\b/i.test(t)&&("Mac OS"==H?"Tasman":"Trident")||"WebKit"==j&&/\bPlayStation\b(?! Vita\b)/i.test(W)&&"NetFront")&&(j=[E]),"IE"==W&&(E=(/; *(?:XBLWP|ZuneWP)(\d+)/i.exec(t)||0)[1])?(W+=" Mobile",H="Windows Phone "+(/\+$/.test(E)?E:E+".x"),V.unshift("desktop mode")):/\bWPDesktop\b/i.test(t)?(W="IE Mobile",H="Windows Phone 8.x",V.unshift("desktop mode"),B||(B=(/\brv:([\d.]+)/.exec(t)||0)[1])):"IE"!=W&&"Trident"==j&&(E=/\brv:([\d.]+)/.exec(t))&&(W&&V.push("identifying as "+W+(B?" "+B:"")),W="IE",B=E[1]),G){if(function isHostType(t,a){var u=null!=t?typeof t[a]:"number";return!(/^(?:boolean|number|string|undefined)$/.test(u)||"object"==u&&!t[a])}(a,"global"))if(w&&(D=(E=w.lang.System).getProperty("os.arch"),H=H||E.getProperty("os.name")+" "+E.getProperty("os.version")),O){try{B=a.require("ringo/engine").version.join("."),W="RingoJS"}catch(t){(E=a.system)&&E.global.system==a.system&&(W="Narwhal",H||(H=E[0].os||null))}W||(W="Rhino")}else"object"==typeof a.process&&!a.process.browser&&(E=a.process)&&("object"==typeof E.versions&&("string"==typeof E.versions.electron?(V.push("Node "+E.versions.node),W="Electron",B=E.versions.electron):"string"==typeof E.versions.nw&&(V.push("Chromium "+B,"Node "+E.versions.node),W="NW.js",B=E.versions.nw)),W||(W="Node.js",D=E.arch,H=E.platform,B=(B=/[\d.]+/.exec(E.version))?B[0]:null));else getClassOf(E=a.runtime)==b?(W="Adobe AIR",H=E.flash.system.Capabilities.os):getClassOf(E=a.phantom)==N?(W="PhantomJS",B=(E=E.version||null)&&E.major+"."+E.minor+"."+E.patch):"number"==typeof P.documentMode&&(E=/\bTrident\/(\d+)/i.exec(t))?(B=[B,P.documentMode],(E=+E[1]+4)!=B[1]&&(V.push("IE "+B[1]+" mode"),j&&(j[1]=""),B[1]=E),B="IE"==W?String(B[1].toFixed(1)):B[0]):"number"==typeof P.documentMode&&/^(?:Chrome|Firefox)\b/.test(W)&&(V.push("masking as "+W+" "+B),W="IE",B="11.0",j=["Trident"],H="Windows");H=H&&format(H)}if(B&&(E=/(?:[ab]|dp|pre|[ab]\d+pre)(?:\d+\+?)?$/i.exec(B)||/(?:alpha|beta)(?: ?\d)?/i.exec(t+";"+(G&&m.appMinorVersion))||/\bMinefield\b/i.test(t)&&"a")&&(U=/b/i.test(E)?"beta":"alpha",B=B.replace(RegExp(E+"\\+?$"),"")+("beta"==U?k:x)+(/\d+\+?/.exec(E)||"")),"Fennec"==W||"Firefox"==W&&/\b(?:Android|Firefox OS|KaiOS)\b/.test(H))W="Firefox Mobile";else if("Maxthon"==W&&B)B=B.replace(/\.[\d.]+/,".x");else if(/\bXbox\b/i.test(Y))"Xbox 360"==Y&&(H=null),"Xbox 360"==Y&&/\bIEMobile\b/.test(t)&&V.unshift("mobile mode");else if(!/^(?:Chrome|IE|Opera)$/.test(W)&&(!W||Y||/Browser|Mobi/.test(W))||"Windows CE"!=H&&!/Mobi/i.test(t))if("IE"==W&&G)try{null===a.external&&V.unshift("platform preview")}catch(t){V.unshift("embedded")}else(/\bBlackBerry\b/.test(Y)||/\bBB10\b/.test(t))&&(E=(RegExp(Y.replace(/ +/g," *")+"/([.\\d]+)","i").exec(t)||0)[1]||B)?(H=((E=[E,/BB10/.test(t)])[1]?(Y=null,$="BlackBerry"):"Device Software")+" "+E[0],B=null):this!=forOwn&&"Wii"!=Y&&(G&&L||/Opera/.test(W)&&/\b(?:MSIE|Firefox)\b/i.test(t)||"Firefox"==W&&/\bOS X (?:\d+\.){2,}/.test(H)||"IE"==W&&(H&&!/^Win/.test(H)&&B>5.5||/\bWindows XP\b/.test(H)&&B>8||8==B&&!/\bTrident\b/.test(t)))&&!M.test(E=parse.call(forOwn,t.replace(M,"")+";"))&&E.name&&(E="ing as "+E.name+((E=E.version)?" "+E:""),M.test(W)?(/\bIE\b/.test(E)&&"Mac OS"==H&&(H=null),E="identify"+E):(E="mask"+E,W=q?format(q.replace(/([a-z])([A-Z])/g,"$1 $2")):"Opera",/\bIE\b/.test(E)&&(H=null),G||(B=null)),j=["Presto"],V.push(E));else W+=" Mobile";(E=(/\bAppleWebKit\/([\d.]+\+?)/i.exec(t)||0)[1])&&(E=[parseFloat(E.replace(/\.(\d)$/,".0$1")),E],"Safari"==W&&"+"==E[1].slice(-1)?(W="WebKit Nightly",U="alpha",B=E[1].slice(0,-1)):B!=E[1]&&B!=(E[2]=(/\bSafari\/([\d.]+\+?)/i.exec(t)||0)[1])||(B=null),E[1]=(/\b(?:Headless)?Chrome\/([\d.]+)/i.exec(t)||0)[1],537.36==E[0]&&537.36==E[2]&&parseFloat(E[1])>=28&&"WebKit"==j&&(j=["Blink"]),G&&(S||E[1])?(j&&(j[1]="like Chrome"),E=E[1]||((E=E[0])<530?1:E<532?2:E<532.05?3:E<533?4:E<534.03?5:E<534.07?6:E<534.1?7:E<534.13?8:E<534.16?9:E<534.24?10:E<534.3?11:E<535.01?12:E<535.02?"13+":E<535.07?15:E<535.11?16:E<535.19?17:E<536.05?18:E<536.1?19:E<537.01?20:E<537.11?"21+":E<537.13?23:E<537.18?24:E<537.24?25:E<537.36?26:"Blink"!=j?"27":"28")):(j&&(j[1]="like Safari"),E=(E=E[0])<400?1:E<500?2:E<526?3:E<533?4:E<534?"4+":E<535?5:E<537?6:E<538?7:E<601?8:E<602?9:E<604?10:E<606?11:E<608?12:"12"),j&&(j[1]+=" "+(E+="number"==typeof E?".x":/[.+]/.test(E)?"":"+")),"Safari"==W&&(!B||parseInt(B)>45)?B=E:"Chrome"==W&&/\bHeadlessChrome/i.test(t)&&V.unshift("headless")),"Opera"==W&&(E=/\bzbov|zvav$/.exec(H))?(W+=" ",V.unshift("desktop mode"),"zvav"==E?(W+="Mini",B=null):W+="Mobile",H=H.replace(RegExp(" *"+E+"$"),"")):"Safari"==W&&/\bChrome\b/.exec(j&&j[1])?(V.unshift("desktop mode"),W="Chrome Mobile",B=null,/\bOS X\b/.test(H)?($="Apple",H="iOS 4.3+"):H=null):/\bSRWare Iron\b/.test(W)&&!B&&(B=getVersion("Chrome")),B&&0==B.indexOf(E=/[\d.]+$/.exec(H))&&t.indexOf("/"+E+"-")>-1&&(H=trim(H.replace(E,""))),H&&-1!=H.indexOf(W)&&!RegExp(W+" OS").test(H)&&(H=H.replace(RegExp(" *"+qualify(W)+" *"),"")),j&&!/\b(?:Avant|Nook)\b/.test(W)&&(/Browser|Lunascape|Maxthon/.test(W)||"Safari"!=W&&/^iOS/.test(H)&&/\bSafari\b/.test(j[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|SRWare Iron|Vivaldi|Web)/.test(W)&&j[1])&&(E=j[j.length-1])&&V.push(E),V.length&&(V=["("+V.join("; ")+")"]),$&&Y&&Y.indexOf($)<0&&V.push("on "+$),Y&&V.push((/^on /.test(V[V.length-1])?"":"on ")+Y),H&&(E=/ ([\d.+]+)$/.exec(H),I=E&&"/"==H.charAt(H.length-E[0].length-1),H={architecture:32,family:E&&!I?H.replace(E[0],""):H,version:E?E[1]:null,toString:function(){var t=this.version;return this.family+(t&&!I?" "+t:"")+(64==this.architecture?" 64-bit":"")}}),(E=/\b(?:AMD|IA|Win|WOW|x86_|x)64\b/i.exec(D))&&!/\bi686\b/i.test(D)?(H&&(H.architecture=64,H.family=H.family.replace(RegExp(" *"+E),"")),W&&(/\bWOW64\b/i.test(t)||G&&/\w(?:86|32)$/.test(m.cpuClass||m.platform)&&!/\bWin64; x64\b/i.test(t))&&V.unshift("32-bit")):H&&/^OS X/.test(H.family)&&"Chrome"==W&&parseFloat(B)>=39&&(H.architecture=64),t||(t=null);var Q={};return Q.description=t,Q.layout=j&&j[0],Q.manufacturer=$,Q.name=W,Q.prerelease=U,Q.product=Y,Q.ua=t,Q.version=W&&B,Q.os=H||{architecture:null,family:null,version:null,toString:function(){return"null"}},Q.parse=parse,Q.toString=function toStringPlatform(){return this.description||""},Q.version&&V.unshift(B),Q.name&&V.unshift(W),H&&W&&(H!=String(H).split(" ")[0]||H!=W.split(" ")[0]&&!Y)&&V.push(Y?"("+H+")":"on "+H),V.length&&(Q.description=V.join(" ")),Q}();m&&g?forOwn(b,(function(t,a){m[a]=t})):h.platform=b}).call(t)}));function getSystemInfoFn(){var t,a,u=bh.version||"";if(isElectron())try{var h=navigator.userAgent.match(/Electron\/([\d.]+\d+)/);h&&h[1]&&"string"==typeof h[1]&&(u=h[1])}catch(t){}return{os:(null===(t=bh.os)||void 0===t?void 0:t.family)||"",osVer:(null===(a=bh.os)||void 0===a?void 0:a.version)||"",browser:bh.name||"",browserVer:bh.version||"",libEnv:"BROWSER",hostEnv:isElectron()?"Electron":isMobile()?"H5":isBrowser()?"BROWSER":"Unset",hostEnvEnum:isElectron()?5:isMobile()?101:isBrowser()?100:0,hostEnvVer:u,userAgent:navigator&&navigator.userAgent,model:u,manufactor:bh.name||""}}var Rh=null,Ah=null,Nh={getNetworkStatus:function getNetworkStatus(){return za.resolve({net_type:0,net_connect:"undefined"==typeof navigator||"boolean"!=typeof navigator.onLine||navigator.onLine})},onNetworkStatusChange:function onNetworkStatusChange(t){Rh=function onlineListener(){t({isConnected:!0,networkType:0})},Ah=function offlineListener(){t({isConnected:!1,networkType:0})},window.addEventListener("online",Rh),window.addEventListener("offline",Ah)},offNetworkStatusChange:function offNetworkStatusChange(){Rh&&window.removeEventListener("online",Rh),Ah&&window.removeEventListener("offline",Ah),Rh=null,Ah=null}},wh=bo.find,Oh="find",xh=!0;Oh in[]&&Array(1).find((function(){xh=!1})),_export({target:"Array",proto:!0,forced:xh},{find:function find(t){return wh(this,t,arguments.length>1?arguments[1]:void 0)}});var kh=entryVirtual("Array").find,Ph=Array.prototype,find=function(t){var a=t.find;return t===Ph||Y(Ph,t)&&a===Ph.find?kh:a},Lh="log",qh=function(){function IDB(t,a){this.db=null,this.stores=[],this.name=t,this.version=a}var t=IDB.prototype;return t.setName=function setName(t){this.name=t},t.getDB=function getDB(){if(!this.db)throw new Error("DB not ready");return this.db},t.getStore=function getStore(t){var a,u=find(a=this.stores).call(a,(function(a){return a.storeName===t}));if(!u)throw new Error("LogStorage: store not found. "+t);return u},t.open=function open(){var t=this,a=window.indexedDB.open(this.name,this.version);return new za((function(u,h){a.onerror=function(t){var a=t.target;h(a.error)},a.onsuccess=function(a){var h,m,g=a.target;t.db=g.result,t.db.removeEventListener("close",bind$1(h=t.triggerDBCloseEvt).call(h,t)),t.db.addEventListener("close",bind$1(m=t.triggerDBCloseEvt).call(m,t)),t.stores.push(new Dh(Lh,t)),u()},a.onupgradeneeded=function(a){var u=a.target;t.upgradeDBBySchema(u)}}))},t.triggerDBCloseEvt=function triggerDBCloseEvt(){try{this.db&&this.db.close(),this.db=null}catch(t){}this.open()},t.upgradeDBBySchema=function upgradeDBBySchema(t){var a=t.result,u=t.transaction&&a.objectStoreNames.contains(Lh)?t.transaction.objectStore(Lh):a.createObjectStore(Lh,{keyPath:"id",autoIncrement:!0});try{u.index("time")}catch(t){u.createIndex("time","time",{unique:!1})}},t.close=function close(){var t;this.db&&(this.db.removeEventListener("close",bind$1(t=this.triggerDBCloseEvt).call(t,this)),this.db.close(),this.stores=[],this.db=null)},IDB}(),Dh=function(){function IDBStore(t,a){this.idb=null,this.storeName=t,this.idb=a}var t=IDBStore.prototype;return t.getDB=function getDB(){if(!this.idb)throw new Error("DB not ready");return this.idb.getDB()},t.getStoreName=function getStoreName(){return this.storeName},t.bulkCreate=function bulkCreate(t){var a=this.getDB(),u=this.getStoreName(),h=a.transaction(u,"readwrite"),m=h.objectStore(u);return forEach$1(t).call(t,(function(t){m.add(t)})),new za((function(t,a){h.oncomplete=function(){t()},h.onerror=function(t){var u=t.target;a(u.error)},h.onabort=function(t){var u=t.target;u.error instanceof Error?a(u.error):a(new Error("TransactionAborted"))}}))},t.bulkDelete=function bulkDelete(t){var a=t.keyName,u=t.lower,h=t.upper,m=t.lowerOpen,g=void 0!==m&&m,E=t.upperOpen,I=void 0!==E&&E,M=IDBKeyRange.bound(u,h,g,I),S=this.getDB(),C=this.getStoreName(),T=S.transaction(C,"readwrite"),b=T.objectStore(C).index(a).openCursor(M),R=0;return b.onsuccess=function(t){var a=t.target.result;a&&(a.delete(),R++,a.continue())},new za((function(t,a){T.oncomplete=function(){t(R)},T.onerror=function(t){var u=t.target;a(u.error)},T.onabort=function(t){var u=t.target;u.error instanceof Error?a(u.error):a(new Error("TransactionAborted"))}}))},t.readAllAndClear=function readAllAndClear(){var t=this.getDB(),a=this.getStoreName(),u=t.transaction(a,"readwrite").objectStore(a);if(!u.getAll)throw new Error("IDBExtract not support");var h=u.getAll();return new za((function(t,a){h.onsuccess=function(a){var h=a.target;u.clear(),t(h.result)},h.onerror=function(t){var u=t.target;a(u.error)}}))},IDBStore}(),Vh=function(){function LogStorageImpl(t){void 0===t&&(t="nim-logs"),this.idb=new qh(t,1)}var t=LogStorageImpl.prototype;return t.open=function open(t){return __awaiter(this,void 0,void 0,cs.mark((function _callee(){var a;return cs.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return t&&this.idb.setName(t),u.next=3,this.idb.open();case 3:return a=this.idb.getStore(Lh),u.prev=4,u.next=7,a.bulkDelete({keyName:"time",lower:0,upper:Za()-2592e5});case 7:u.next=11;break;case 9:u.prev=9,u.t0=u.catch(4);case 11:case"end":return u.stop()}}),_callee,this,[[4,9]])})))},t.close=function close(){this.idb.close()},t.addLogs=function addLogs(t){return this.idb.getStore(Lh).bulkCreate(t)},t.extractLogs=function extractLogs(){return __awaiter(this,void 0,void 0,cs.mark((function _callee2(){var t,a,u,h,m;return cs.wrap((function _callee2$(g){for(;;)switch(g.prev=g.next){case 0:return a=this.idb.getStore(Lh),g.next=3,a.readAllAndClear();case 3:if(0!==(u=g.sent).length){g.next=6;break}return g.abrupt("return","");case 6:return h=reduce(u).call(u,(function(t,a){var u=a.iid;return t[u]||(t[u]=[]),t[u].push(a),t}),{}),m=map$6(t=Qi(h)).call(t,(function(t){var a=h[t];return"==========iid:"+t+"==========\n "+map$6(a).call(a,(function(t){return t.text})).join("\n")})).join("\n"),g.abrupt("return",new File([m],"nim-logs.txt",{type:"text/plain"}));case 9:case"end":return g.stop()}}),_callee2,this)})))},LogStorageImpl}();return function setAdapters(t){merge$1(au,t())}((function getAdapter(){return{setLogger:setLogger,platform:"BROWSER",localStorage:window.localStorage,request:Sh,WebSocket:window.WebSocket,uploadFile:uploadFileFn,getFileUploadInformation:getFileUploadInformationFn,getSystemInfo:getSystemInfoFn,net:Nh,logStorage:Vh}})),Mh}));
