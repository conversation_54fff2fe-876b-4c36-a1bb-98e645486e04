// src/config/sip.config.js
export const sipConfig = {
  uri: 'sip:<EMAIL>', // 替换为你的SIP账号
  wsServers: 'wss://your-sip-server:8088', // 替换为使用wss协议的WebSocket地址
  authorizationUser: '1001', // SIP用户名
  password: 'your_password', // SIP密码
  traces: true, // 开启调试日志
  media: {
    constraints: {
      audio: true,
      video: false
    },
    configuration: {
      iceServers: [
        { urls: 'stun:stun.l.google.com:19302' },
        {
          urls: 'turn:numb.viagenie.ca:3478?transport=udp', 
          username: 'turn_username', 
          credential: 'turn_password'
        }
      ]
    }
  }
};