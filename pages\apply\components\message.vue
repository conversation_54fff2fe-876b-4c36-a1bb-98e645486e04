<template>
	<custom-header style="height: 88rpx;" title="信息上报" showBack2 />
	<view class="message-page">
		<!-- 信息上报区域 -->
		<view class="message-section">
			<!-- 封孔信息上报 -->
			<view class="message-item" @click="navigateToReport('seal')">
				<view class="item-left">
					<view class="icon-wrapper">
						<image src="@/static/hole.png" mode="" style="width: 45rpx; height: 40rpx;"></image>
					</view>
					<view class="item_cont">
						<text class="item-text">封孔信息上报</text>
						<text class="item-value">上报封孔相关信息</text>
					</view>
				</view>
				<up-icon name="arrow-right" size="16" color="#9CA3AF"></up-icon>
			</view>

			<!-- 交接班信息上报 -->
			<view class="message-item" @click="navigateToReport('shift')">
				<view class="item-left">
					<view class="icon-wrapper2">
						<image src="@/static/shift.png" mode="" style="width: 40rpx; height: 40rpx;"></image>
					</view>
					<view class="item_cont">
						<text class="item-text">交接班信息上报</text>
						<text class="item-value">上报交接班相关信息</text>
					</view>
				</view>
				<up-icon name="arrow-right" size="16" color="#9CA3AF"></up-icon>
			</view>
		</view>

		<!-- 最近上报记录 -->
		<!-- <view class="recent-records">
			<view class="records-title">最近上报记录</view>

			<!-- 信息记录 -->
			<!--<view class="record-item" v-for="item in msgCont" >
				<view class="record-header">{{item.type == 0 ? '封孔信息上报':'交接班信息上报'}}</view>
				<view class="record-content">
					<view class="record-row">
						<text class="label">上报人：</text>
						<text class="value">{{item.name}}</text>
					</view>
					<view class="record-row">
						<text class="label">孔号：</text>
						<text class="value">{{item.contentid}}</text>
					</view>
					<view class="record-row">
						<text class="label">上报时间：</text>
						<text class="value">{{item.createdAt}}</text>
					</view>
				</view>
			</view>

		</view> -->
	</view>
</template>

<script>
	import Request from '@/components/utils/request';
	import customHeader from '@/components/page/header.vue';
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				msgCont: [
					{
						type:0,
						name: '徐思成1',
						contentid: '1#',
						createdAt: '2024-11-21 10:00:00'
					},
					{
						type:1,
						name: '徐思成2',
						contentid: '2#',
						createdAt: '2024-12-22 10:00:00'
					}
				]
			}
		},
		methods: {
			navigateToReport(type) {
				// 导航到对应的上报页面
				const url = type === 'seal' ? '/pages/apply/components/message/seal' :
					'/pages/apply/components/message/shift'
				uni.navigateTo({
					url
				})
			},
			async handelMessage() {
			
				try {
					let msg_data = {
						page: this.currentPage,
						perPage: this.perPage,
						dateStart:this.dateStart,
						dateEnd:this.dateEnd,
						keyWord: this.searchText
					};
					const res = await Request.post('/drillsite/get_ls', msg_data)
			
					if (res.status == 0) {
						console.log('信息返回数据', res);
						this.data = res.data.items;
						
						
			
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
			
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			
			},
			
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}

	.message-page {
		min-height: 100vh;
		// padding-top: 156rpx;
		background-color: #16171b;
		padding: 32rpx;
		padding-top: 188rpx;
	}

	.message-section {
		margin-bottom: 48rpx;
	}

	.message-title,
	.records-title {
		color: rgba(255,255,255,0.65);
		font-size: 28rpx;
		margin-bottom: 32rpx;
	}

	.message-item {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: rgba(255, 255, 255, 0.04);
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		border-radius: 16rpx;
		margin-bottom: 24rpx;

		.item-left {
			display: flex;
			align-items: center;
			gap: 24rpx;
		}

		.icon-wrapper {
			background-color: #9dcdfd;
			padding: 25rpx;
			border-radius: 28rpx;
		}

		.icon-wrapper2 {
			background: #FFEAD1;
			padding: 28rpx;
			border-radius: 28rpx;
		}

	}

	.item_cont {
		display: flex;
		flex-direction: column;

		.item-text {
			color: rgba(255, 255, 255, 0.85);
			font-size: 28rpx;
			margin-bottom: 8rpx;
		}

		.item-value {
			color: rgba(255, 255, 255, 0.65);
			font-size: 28rpx;
		}
	}

	.record-item {
		background-color: rgba(255, 255, 255, 0.04);
		border-radius: 16rpx;
		padding: 32rpx;
		margin-bottom: 24rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);

		.record-header {
			color: rgba(255, 255, 255, 0.85);
			font-size: 30rpx;
			margin-bottom: 24rpx;
		}

		.record-content {
			.record-row {
				display: flex;
				margin-bottom: 16rpx;

				&:last-child {
					margin-bottom: 0;
				}

				.label {
					color: rgba(255, 255, 255, 0.65);
					font-size: 28rpx;
				}

				.value {
					color: rgba(255, 255, 255, 0.65);
					font-size: 28rpx;
				}
			}
		}
	}
</style>