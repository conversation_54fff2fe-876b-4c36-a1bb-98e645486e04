import { KickOtherClientsOptions, KickOtherClientsResult, NIMEQChatInstanceStatus, QChatEventInterface, QChatInterface } from './QChatInterface';
import { CloudStorageServiceInterface } from './CloudStorageServiceInterface';
import { QChatChannelServiceInterface } from './QChatChannelServiceInterface';
import { QChatInitializeOptions, QChatOtherOptions } from './types';
import { QChatServerServiceInterface } from './QChatServerServiceInterface';
import { QChatMediaServiceInterface } from './QChatMediaServiceInterface';
import { QChatMsgServiceInterface } from './QChatMsgServiceInterface';
import { QChatRoleServiceInterface } from './QChatRoleServiceInterface';
import { EventEmitter } from 'eventemitter3';
export default class QChat extends EventEmitter<QChatEventInterface> implements QChatInterface {
    static instance: QChat | null;
    /**
     * 实例的状态
     */
    status: NIMEQChatInstanceStatus;
    /**
     * 账号
     */
    account: string;
    /**
     * 连接参数
     */
    options: QChatInitializeOptions;
    qchatServer: QChatServerServiceInterface;
    qchatChannel: QChatChannelServiceInterface;
    qchatMsg: QChatMsgServiceInterface;
    qchatRole: QChatRoleServiceInterface;
    qchatMedia: QChatMediaServiceInterface;
    cloudStorage: CloudStorageServiceInterface;
    constructor(options: QChatInitializeOptions, otherOptions?: QChatOtherOptions);
    logout(): Promise<void>;
    setOptions(): Promise<void>;
    destroy(): Promise<void>;
    kickOtherClients(options: KickOtherClientsOptions): Promise<KickOtherClientsResult>;
    login(): Promise<void>;
    /**
     * 单例模式获取实例
     */
    static getInstance(_options?: QChatInitializeOptions, _otherOptions?: QChatOtherOptions): QChat;
    /**
     * 设置适配器
     * @param _newAdapters 适配器
     */
    static setAdapters(_newAdapters: any): void;
}
