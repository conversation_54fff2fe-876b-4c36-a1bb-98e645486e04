import { NIMEStrAnyObj } from './types';
export declare const V2NIMErrorCode: {
    V2NIM_ERROR_CODE_UNKNOWN: number;
    V2NIM_ERROR_CODE_SUCCESS: number;
    V2NIM_ERROR_CODE_HANDSHAKE: number;
    V2NIM_ERROR_CODE_REQUEST_TEMPERARY_FORBIDDEN: number;
    V2NIM_ERROR_CODE_SERVER_UNIT_ERROR: number;
    V2NIM_ERROR_CODE_FORBIDDEN: number;
    V2NIM_ERROR_CODE_NOT_FOUND: number;
    V2NIM_ERROR_CODE_PARAMETER_ERROR: number;
    V2NIM_ERROR_CODE_RATE_LIMIT_REACHED: number;
    V2NIM_ERROR_CODE_MULTI_LOGIN_FORBIDDEN: number;
    V2NIM_ERROR_CODE_SERVER_INTERNAL_ERROR: number;
    V2NIM_ERROR_CODE_SERVER_BUSY: number;
    V2NIM_ERROR_CODE_APP_UNREACHABLE: number;
    V2NIM_ERROR_CODE_SERVICE_UNAVAILABLE: number;
    V2NIM_ERROR_CODE_PROTOCOL_BLACKHOLE_FILTERED: number;
    V2NIM_ERROR_CODE_NO_PERMISSION: number;
    V2NIM_ERROR_CODE_UNPACK_ERROR: number;
    V2NIM_ERROR_CODE_PACK_ERROR: number;
    V2NIM_ERROR_CODE_IM_DISABLED: number;
    V2NIM_ERROR_CODE_SERVICE_ADDRESS_INVALID: number;
    V2NIM_ERROR_CODE_APPKEY_NOT_EXIST: number;
    V2NIM_ERROR_CODE_BUNDLEID_CHECK_FAILED: number;
    V2NIM_ERROR_CODE_APPKEY_BLOCKED: number;
    V2NIM_ERROR_CODE_INVALID_TOKEN: number;
    V2NIM_ERROR_CODE_ROBOT_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_ACCOUNT_NOT_EXIST: number;
    V2NIM_ERROR_CODE_ACCOUNT_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_ACCOUNT_BANNED: number;
    V2NIM_ERROR_CODE_ACCOUNT_IN_BLOCK_LIST: number;
    V2NIM_ERROR_CODE_USER_PROFILE_NOT_EXIST: number;
    V2NIM_ERROR_CODE_USER_PROFILE_HIT_ANTISPAM: number;
    V2NIM_ERROR_CODE_PEER_FRIEND_LIMIT: number;
    V2NIM_ERROR_CODE_FRIEND_APPLICATION_NOT_EXIST: number;
    V2NIM_ERROR_CODE_FRIEND_NOT_EXIST: number;
    V2NIM_ERROR_CODE_FRIEND_ALREADY_EXIST: number;
    V2NIM_ERROR_CODE_SELF_FRIEND_OPERATION_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_FRIEND_LIMIT: number;
    V2NIM_ERROR_CODE_FRIEND_OPERATION_RATE_LIMIT: number;
    V2NIM_ERROR_CODE_FRIEND_HIT_ANTISPAM: number;
    V2NIM_ERROR_CODE_SELF_MUTE_OPERATION_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_MUTE_LIST_LIMIT: number;
    V2NIM_ERROR_CODE_SELF_BLOCK_LIST_OPERATION_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_BLOCK_LIST_LIMIT: number;
    V2NIM_ERROR_CODE_REVOKE_THIRD_PARTY_MESSAGE_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_SHORT_TO_LONG_URL_FAILED: number;
    V2NIM_ERROR_CODE_URL_INVALID: number;
    V2NIM_ERROR_CODE_DURATION_OUT_OF_RANGE: number;
    V2NIM_ERROR_CODE_GET_FILE_META_INFO_FAILED: number;
    V2NIM_ERROR_CODE_AUDIO_FILE_SIZE_LIMIT: number;
    V2NIM_ERROR_CODE_VOICE_TO_TEXT_TIMEOUT: number;
    V2NIM_ERROR_CODE_VOICE_TO_TEXT_FAILED: number;
    V2NIM_ERROR_CODE_REVOKE_EXCEED_TIME_LIMIT: number;
    V2NIM_ERROR_CODE_REVOKE_MESSAGE_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_FORCE_PUSH_LIST_LIMIT: number;
    V2NIM_ERROR_CODE_TEAM_MESSAGE_RECEIPT_RATE_LIMIT: number;
    V2NIM_ERROR_CODE_SNAPSHOT_NOT_EXIST: number;
    V2NIM_ERROR_CODE_PIN_LIMIT: number;
    V2NIM_ERROR_CODE_PIN_NOT_EXIST: number;
    V2NIM_ERROR_CODE_QUICK_COMMENT_LIMIT: number;
    V2NIM_ERROR_CODE_PIN_ALREADY_EXIST: number;
    V2NIM_ERROR_CODE_VOICE_TO_TEXT_FUNCTION_DISABLED: number;
    V2NIM_ERROR_CODE_CLOUD_SEARCH_FUNCTION_DISABLED: number;
    V2NIM_ERROR_CODE_ONE_WAY_DELETE_FUNCTION_DISABLED: number;
    V2NIM_ERROR_CODE_REVOKE_MESSAGE_TO_SELF_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_APP_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_QUICK_COMMENT_FUNCTION_DISABLED: number;
    V2NIM_ERROR_CODE_PIN_FUNCTION_DISABLED: number;
    V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_FUNCTION_DISABLED: number;
    V2NIM_ERROR_CODE_P2P_READ_RECEIPT_FUNCTION_DISABLED: number;
    V2NIM_ERROR_CODE_RATE_LIMIT_FOR_MESSAGING_REACHED: number;
    V2NIM_ERROR_CODE_MESSAGE_HIT_ANTISPAM: number;
    V2NIM_ERROR_CODE_MESSAGE_NOT_EXIST: number;
    V2NIM_ERROR_CODE_UNSENDING_MESSAGE_EXPIRED: number;
    V2NIM_ERROR_CODE_TEAM_MARK_READ_FAILED: number;
    V2NIM_ERROR_CODE_SENDER_OR_MANAGER_PERMISSION_ONLY_REVOKE: number;
    V2NIM_ERROR_CODE_DELETE_SELF_MESSAGE_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_NOT_CHATBOT_ACCOUNT: number;
    V2NIM_ERROR_CODE_MESSAGE_SENSE_REQUIRED: number;
    V2NIM_ERROR_CODE_HIGH_PRIORITY_MESSAGE_RATE_LIMIT: number;
    ACK_MESSAGE_BE_HIGH_PRIORITY: number;
    V2NIM_ERROR_CODE_DUPLICATE_CLIENT_MESSAGE_ID: number;
    V2NIM_ERROR_CODE_INVALID_TIME_RANGE: number;
    V2NIM_ERROR_CODE_NOT_ADVANCED_TEAM: number;
    V2NIM_ERROR_CODE_TEAM_MANAGER_LIMIT: number;
    V2NIM_ERROR_CODE_JOINED_TEAM_LIMIT: number;
    V2NIM_ERROR_CODE_TEAM_NORMAL_MEMBER_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_INVITED_ACCOUNT_NOT_FRIEND: number;
    V2NIM_ERROR_CODE_REJECT_ALL_TEAM_APPLICATIONS: number;
    V2NIM_ERROR_CODE_TEAM_NOT_EXIST: number;
    V2NIM_ERROR_CODE_TEAM_ALREADY_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_ALL_TEAM_MEMBER_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT: number;
    V2NIM_ERROR_CODE_CREATED_TEAM_LIMIT: number;
    V2NIM_ERROR_CODE_TEAM_INVITATION_LIMIT: number;
    V2NIM_ERROR_CODE_TEAM_HIT_ANTISPAM: number;
    V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT_NOT_CONFIGURED: number;
    V2NIM_ERROR_CODE_SUPER_TEAM_SERVICE_DISABLED: number;
    V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_RECORD_NOT_FOUND: number;
    V2NIM_ERROR_CODE_NOT_MANAGER: number;
    V2NIM_ERROR_CODE_ONLINE_MEMBER_COUNT_DISABLED: number;
    V2NIM_ERROR_CODE_TRANSFER_DISABLED: number;
    V2NIM_ERROR_CODE_CREATE_TEAM_DISABLED: number;
    V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_CREATE_FAILED: number;
    V2NIM_ERROR_CODE_TEAM_MESSAGE_READ_RECEIPT_DISABLED: number;
    V2NIM_ERROR_CODE_RETRY: number;
    V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_NOT_TEAM_MEMBER: number;
    V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_OPERATOR: number;
    V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_TEAM_OWNER: number;
    V2NIM_ERROR_CODE_OPERATION_ON_TEAM_MANAGER_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_NO_TEAM_INVITE_PERMISSION: number;
    V2NIM_ERROR_CODE_TEAM_OWNER_QUIT_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_TEAM_OWNER_IN_KICK_LIST: number;
    V2NIM_ERROR_CODE_INVITE_ROBOT_ACCOUNT_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_KICK_OPERATOR_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_TEAM_MEMBER_ALREADY_EXIST: number;
    V2NIM_ERROR_CODE_TEAM_MEMBER_CAN_NOT_MODIFY_SELF: number;
    V2NIM_ERROR_CODE_TEAM_INVITATION_OR_APPLICATION_NOT_EXIST: number;
    V2NIM_ERROR_CODE_OPERATION_ON_TEAM_OWNER_NOT_ALLOWED: number;
    V2NIM_ERROR_CODE_TEAM_MEMBER_NOT_EXIST: number;
    V2NIM_ERROR_CODE_TEAM_MEMBER_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_TEAM_OWNER_OPERATION_PERMISSION_REQUIRED: number;
    V2NIM_ERROR_CODE_TEAM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED: number;
    V2NIM_ERROR_CODE_TEAM_MEMBER_CONCURRENT_OPERATION_FAILED: number;
    V2NIM_ERROR_CODE_TEAM_MEMBER_HIT_ANTISPAM: number;
    V2NIM_ERROR_CODE_CONVERSATION_AND_ACCOUNT_MISMATCH: number;
    V2NIM_ERROR_CODE_CONVERSATION_STICK_TOP_LIMIT: number;
    V2NIM_ERROR_CODE_CONVERSATION_BELONGED_GROUP_LIMIT: number;
    V2NIM_ERROR_CODE_CONVERSATION_NOT_EXIST: number;
    V2NIM_ERROR_CODE_CHATROOM_LINK_UNAVAILABLE: number;
    V2NIM_ERROR_CODE_IM_CONNECTION_ABNORMAL: number;
    V2NIM_ERROR_CODE_CHATROOM_NOT_EXIST: number;
    V2NIM_ERROR_CODE_CHATROOM_CLOSED: number;
    V2NIM_ERROR_CODE_CHATROOM_REPEATED_OPERATION: number;
    V2NIM_ERROR_CODE_CHATROOM_DISABLED: number;
    V2NIM_ERROR_CODE_ALL_CHATROOM_MEMBER_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_CHATROOM_HIT_ANTISPAM: number;
    V2NIM_ERROR_CODE_ANONYMOUS_MEMBER_FORBIDDEN: number;
    V2NIM_ERROR_CODE_CHATROOM_MEMBER_NOT_EXIST: number;
    V2NIM_ERROR_CODE_CHATROOM_MEMBER_REPEATED_OPERATION: number;
    V2NIM_ERROR_CODE_CHATROOM_MEMBER_CHAT_BANNED: number;
    V2NIM_ERROR_CODE_ACCOUNT_IN_CHATROOM_BLOCK_LIST: number;
    V2NIM_ERROR_CODE_CHATROOM_OWNER_OPERATION_PERMISSION_REQUIRED: number;
    V2NIM_ERROR_CODE_SELF_IN_CHATROOM_MEMBER_OPERATION_LIST: number;
    V2NIM_ERROR_CODE_CHATROOM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED: number;
    V2NIM_ERROR_CODE_CHATROOM_MEMBER_LIMIT: number;
    V2NIM_ERROR_CODE_CHATROOM_MEMBER_CONCURRENT_OPERATION_FAILED: number;
    V2NIM_ERROR_CODE_CHATROOM_MEMBER_HIT_ANTISPAM: number;
    V2NIM_ERROR_CODE_CONVERSATION_GROUP_NOT_EXIST: number;
    V2NIM_ERROR_CODE_CONVERSATION_GROUP_LIMIT: number;
    V2NIM_ERROR_CODE_CONVERSATIONS_IN_GROUP_LIMIT: number;
    V2NIM_ERROR_CODE_COLLECTION_LIMIT: number;
    V2NIM_ERROR_CODE_COLLECTION_NOT_EXIST: number;
    V2NIM_ERROR_CODE_COLLECTION_CONCURRENT_OPERATION_FAILED: number;
    V2NIM_ERROR_CODE_INTERNAL: number;
    V2NIM_ERROR_CODE_ILLEGAL_STATE: number;
    V2NIM_ERROR_CODE_MISUSE: number;
    V2NIM_ERROR_CODE_CANCELLED: number;
    V2NIM_ERROR_CODE_CALLBACK_FAILED: number;
    V2NIM_ERROR_CODE_INVALID_PARAMETER: number;
    V2NIM_ERROR_CODE_TIMEOUT: number;
    V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST: number;
    V2NIM_ERROR_CODE_RESOURCE_ALREADY_EXIST: number;
    V2NIM_ERROR_CODE_CONNECT_FAILED: number;
    V2NIM_ERROR_CODE_CONNECT_TIMEOUT: number;
    V2NIM_ERROR_CODE_DISCONNECT: number;
    V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT: number;
    V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED: number;
    V2NIM_ERROR_CODE_REQUEST_FAILED: number;
    V2NIM_ERROR_CODE_FILE_NOT_FOUND: number;
    V2NIM_ERROR_CODE_FILE_CREATE_FAILED: number;
    V2NIM_ERROR_CODE_FILE_OPEN_FAILED: number;
    V2NIM_ERROR_CODE_FILE_WRITE_FAILED: number;
    V2NIM_ERROR_CODE_FILE_READ_FAILED: number;
    V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED: number;
    V2NIM_ERROR_CODE_FILE_DOWNLOAD_FAILED: number;
    V2NIM_ERROR_CODE_CLIENT_ANTISPAM: number;
    V2NIM_ERROR_CODE_SERVER_ANTISPAM: number;
};
export declare const V2NIMErrorDesc: NIMEStrAnyObj;
export { V2NIMLoginAuthType, V2NIMLoginStatus, V2NIMLoginClientType, V2NIMLoginClientChange, V2NIMConnectStatus, V2NIMChatroomKickedReason } from './V2NIMChatroomLoginService';
export { V2NIMChatroomMemberRole } from './V2NIMChatroomMemberService';
export { V2NIMMessageSendingState, V2NIMMessageAttachmentUploadState, V2NIMMessageType, V2NIMQueryDirection, V2NIMChatroomMessageNotificationType, V2NIMClientAntispamOperateType } from './V2NIMChatroomMessageService';
export { V2NIMChatroomStatus } from './V2NIMChatroomService';
export { V2NIMChatroomQueueLevelMode } from './V2NIMChatroomQueueService';
