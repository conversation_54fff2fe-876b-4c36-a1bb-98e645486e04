{"name": "nim-web-sdk-ng", "version": "10.8.30", "sdk": {"version": 100830, "versionFormat": "10.8.30"}, "description": "Yunxin IM SDK next generation", "main": "./dist/v2/NIM_BROWSER_SDK.js", "types": "./dist/v2/NIM_BROWSER_SDK/index.d.ts", "scripts": {"test": "jest --maxWorkers 1 --silent", "lint": "npx eslint ./src", "dev": "cd pages/ && npm run dev", "clean": "rm -rf ./dist", "emitTypes": "tsc --emitDeclarationOnly --declaration --declarationDir temp/types", "build:esm": "npm run emitTypes && rollup --config ./build/esm.config.js", "build": "npm run clean && npm run emitTypes && node script/copyPlugin.js && rollup --config ./build/v1.config.js && rollup --config ./build/v2.config.js && rollup --config ./build/esm.config.js", "analyze": "npm run clean && npx rollup --config ./build/analyze.config.js && ls -lhR dist | grep \".js\"", "pages:build": "cd pages/ && npm install && npm run build && cp -rf dist/ ../dist/", "api": "npm run translate && node script/buildApi.js", "translate": "rm -rf ./apidocs && multilingual-tool convert --indir=src/typings --outdir=apidocs --includes=ts", "api-diff": "multilingual-tool diff --dir=src/typings --receiver=3070898 --exclude=DS_Store", "updateVerInNdp": "node ./script/updateVerInNdp.js"}, "repository": {"type": "git", "url": "ssh://********************:22222/yunxin/im/sdk.git"}, "keywords": ["im", "sdk"], "author": "yunxin", "license": "ISC", "eslintIgnore": ["dist/"], "files": ["README.md", "CHANGELOG.md", "dist/v1/CHATROOM*", "dist/v1/QCHAT*", "dist/v2/NIM*", "dist/v2/CHATROOM*", "dist/esm/*"], "devDependencies": {"@babel/core": "^7.13.15", "@babel/plugin-proposal-class-properties": "^7.13.0", "@babel/plugin-transform-block-scoping": "^7.23.4", "@babel/plugin-transform-runtime": "^7.13.15", "@babel/preset-env": "^7.13.15", "@babel/preset-typescript": "^7.13.0", "@babel/runtime": "^7.13.10", "@babel/runtime-corejs3": "^7.13.10", "@microsoft/api-extractor": "^7.19.4", "@rollup/plugin-babel": "^5.3.0", "@rollup/plugin-commonjs": "^18.0.0", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^11.2.1", "@rollup/plugin-replace": "^3.0.0", "@rollup/plugin-typescript": "^8.3.0", "@types/backo2": "^1.0.1", "@types/jest": "^27.4.1", "@types/lodash": "^4.14.177", "@types/platform": "^1.3.3", "@typescript-eslint/eslint-plugin": "^5.12.0", "@typescript-eslint/parser": "^5.12.0", "@yxfe/multilingual-tool": "^1.0.30", "@yxfe/nim-log-reporter": "^1.0.3", "@yxfe/nos-uploader": "^1.0.28", "@yxfe/request": "^0.2.12", "backo2": "^1.0.2", "browser-md5-file": "^1.1.1", "core-js": "^3.10.1", "cross-env": "^7.0.3", "eslint": "^8.9.0", "eslint-config-prettier": "^8.5.0", "eslint-plugin-prettier": "^4.0.0", "eventemitter3": "^4.0.7", "husky": "^7.0.4", "jest": "^27.5.1", "lodash-es": "^4.17.21", "neroom-web-sdk": "1.7.0", "platform": "^1.3.6", "pre-commit": "^1.2.2", "prettier": "^2.6.1", "react-native": "^0.75.2", "rollup": "^2.45.2", "rollup-plugin-banner": "^0.2.1", "rollup-plugin-banner2": "^1.2.2", "rollup-plugin-copy": "^3.4.0", "rollup-plugin-insert": "^1.3.2", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-visualizer": "^5.12.0", "ts-jest": "^27.1.4", "tslib": "^2.3.1", "typedoc": "^0.22.11", "typescript": "^4.5.4"}}