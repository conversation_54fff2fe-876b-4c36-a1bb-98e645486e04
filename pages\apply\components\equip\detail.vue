<template>
	<custom-header style="height: 88rpx;" title="设备详情" showBack />
	<view class="equip_content">
		<view class="equip_header">
			<text class="header_text">{{prolist.deviceName}}</text>
			<view class="cont">
				<image class="header_img" src="@/static/image/apply/element.png" mode=""></image>
			</view>
			<view class="header_flex">
				<view>
					<view class="flex_label">设备序列号</view>
					<view class="flex_value">{{prolist.deviceCode}}</view>
				</view>
				<view>
					<view class="flex_label">设备型号</view>
					<view class="flex_value">{{prolist.deviceModel}}</view>
				</view>
			</view>
		</view>
		<view class="tab_cont">
			<view class="tab_border">
			<up-tabs 
			:list="list4" 
			lineWidth="30" 
			lineColor="#177DDC" 		
			:activeStyle="{
            color: '#177DDC',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }" :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }" @change="handleTabChange"
				itemStyle="padding:15px; padding-right: 15px; height: 34px;">
			</up-tabs>
			</view>
			<view class="de_con">


				<template v-if="currentTab == 0">
					<view class="content">
						<view class="cont_flex">
							<view class="label" >配置状态</view>
							
							<view class="value">{{getConfigText(prolist.configurationState)}}</view>
						</view>
						<view class="cont_flex">
							<view class="label">当前状态</view>
							<view class="value">{{getCurrentText(prolist.currentState)}}</view>							
						</view>
						<view class="cont_flex">
							<view class="label">创建时间</view>
							<view class="value">{{prolist.creatAt}}</view>
						</view>
						<view class="cont_flex">
							<view class="label">最后更新时间</view>
							<view class="value">{{prolist.updateAt}}</view>
						</view>
						<!-- <view class="cont_flex">
							<view class="label">最后保养日期</view>
							<view class="value">{{prolist.lastMaintenanceDate}}</view>
						</view>
						<view class="cont_flex">
							<view class="label">采面</view>
							<view class="value">{{prolist.miningFace}}</view>
						</view>
						<view class="cont_flex">
							<view class="label">巷道</view>
							<view class="value">{{prolist.lane}}</view>
						</view>
						<view class="cont_flex">
							<view class="label">矿场</view>
							<view class="value">{{prolist.drillSite}}</view>
						</view>
						<view class="cont_flex">
							<view class="label">设备负责人</view>
							<view class="value">{{prolist.director}}</view>
						</view>
						<view class="cont_flex">
							<view class="label">设备负责人账号</view>
							<view class="value">{{prolist.account}}</view>
						</view>
						<view class="cont_flex" >
							<view class="label">设备标签</view>
							<view class="value">
							<text v-for="(item,index) in label" :key="index">{{item.label}}<text v-if="index !== label.length - 1">,</text></text>
							</view>
						</view>
						<view class="cont_flex">
							<view class="label">设备型号ID</view>
							<view class="value">{{prolist.deviceModelId}}</view>
						</view> -->
					</view>
				</template>
				<template v-if="currentTab == 1">
					<control-cont></control-cont>
				</template>
				<template v-if="currentTab == 2">
					<history-vue></history-vue>
				</template>
				<template v-if="currentTab == 3">
					<keep-vue></keep-vue>
				</template>
				<template v-if="currentTab == 4">
					<lifetrack-vue></lifetrack-vue>
				</template>
				<template v-if="currentTab == 5">
					<transfer-vue></transfer-vue>
				</template>

			</view>
		</view>
	</view>
</template>

<script>
	import transferVue from './transfer.vue';
	import historyVue from './history.vue';
	import controlCont from './control.vue';
	import lifetrackVue from './lifetrack.vue';
	import keepVue from './keep.vue';
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request';
	export default {
		 inheritAttrs: false,
		components: {
			customHeader,
			controlCont,
			keepVue,
			historyVue,
			lifetrackVue,
			transferVue
		},
		data() {
			return {
				currentTab: 0,
				id: null,
				list4: [{
						name: '设备信息'
					},
					{
						name: '状态监控'
					},
					// {
					// 	name: '历史记录'
					// },
					// {
					// 	name: '保养计划记录'
					// },
					// {
					// 	name: '设备寿命跟踪'
					// },
					// {
					// 	name: '设备转移记录'
					// }
				],
				prolist: {
					deviceName: 'test设备',
					deviceCode: 'ID345678',
					deviceModel: 'Y4',
					configurationState: 0,
					currentState: 0,
					creatAt: "2024-12-09 09:39:37",
					updateAt: "2024-12-09 09:44:31"
				},
				isPolling: false,
				label:[]
			}
		},
		onLoad(options) {
			this.id = options.id
			// this.handelDetail()
		},
		onShow(){
			if (this.id) {
				this.handelDetail()
			}
		},
		methods: {
			handleTabChange(index) {
				this.currentTab = index.index;
				
				if (this.currentTab === 1) {
					uni.$emit('startPolling', { deviceId: this.id });
				} else {
					uni.$emit('stopPolling');
				}
			},
			async handelDetail() {
				try {
					  let data={
						  id:this.id
					  }
					const res = await Request.get('/device/get_info', data)
					if (res.status == 0) {
						// console.log('返回数据', res);
						this.prolist = res.data;
						// 处理动态字段数据
						if (res.data.label) {
							try {
								this.label = JSON.parse(res.data.label)
								console.log(this.label);
							} catch (e) {
								console.error('解析动态字段失败:', e)
								this.label = []
							}
						}
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}

				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}

			},
			getConfigText(config) {
				if (config === 0) {
					return '启用';
				} else if (config === 1) {
					return '禁用';
				} 
				return '--'; // 默认值
			},
			getCurrentText(current) {
				if (current === 0) {
					return '打开';
				} else if (current === 1) {
					return '关闭';
				} else if (current === 2) {
					return '损坏';
				} 
				return '--'; // 默认值
			},
		},
		
		onHide() {
			uni.$emit('stopPolling');
		},
		beforeDestroy() {
			uni.$emit('stopPolling');
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}

	.equip_content {
		padding: 0 32rpx;
		padding-top: 156rpx;
	}

	.equip_header {
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}

	.header_text {
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.cont {
		display: flex;
		// align-items: center;
		justify-content: center;
	}

	.header_img {
		width: 530rpx;
		height: 244rpx;
	}

	.tab_cont {
		// border-radius: 12rpx;
		// padding: 0 32rpx;
		margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}

	.tab_border{
		border-radius: 12rpx 12rpx 0 0;
		padding:0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
	}
	.content{
		border-radius:0 0 12rpx 12rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		// height: 365rpx;
	}

	.header_flex {
		margin-top: 12rpx;
		margin-right: 138rpx;
		display: flex;
		justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}

	.flex_label {

		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}

	.flex_value {
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}

	.cont_flex {
		display: flex;
		margin-bottom: 24rpx;
		justify-content: space-between;
	}

	.label {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.value {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.8);
		// font-weight: bold;
	}
</style>