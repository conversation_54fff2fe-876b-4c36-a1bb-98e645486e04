<template>
	<custom-header style="height: 88rpx;" title="交接班信息上报" showBack />
  <view class="seal-form">
    <!-- 第一组表单 -->
    <view class="form-content">
      <!-- 操作人 -->
      <view class="form-item">
        <text class="label">操作人</text>
        <text class="value">{{operatorName}}</text>
      </view>
	  </view>
<view class="form-content">
      <!-- 下发任务 -->
      <view class="form-item" @click="handlePlan">
        <text class="label">下发任务</text>
        <view class="value-with-arrow">
          <text style="margin-right: 16rpx;">{{isEmptyPlan ? '选择任务' : '重新选择'}}</text>
          <up-icon name="arrow-right" size="18" color="rgba(255,255,255,0.3)"></up-icon>
        </view>
		
      </view>
	  <view class="plan_cont" v-if="!isEmptyPlan">
	  	 <text class="plan_label">{{plan.holeNumber}}</text>
		 <text class="plan_value">设备名称：{{plan.drillName}}</text>
		 <text class="plan_value">创建时间：{{plan.createdAt}}</text>
	  </view>
    </view>
    <view class="form-content second-group">
      <!-- 备注 -->
 <view class="form-item">
        <text class="label">进尺数</text>
        <up-input
          v-model="footage"
          placeholder="请输入进尺数"
          border="none"
        ></up-input>
      </view>      
<view class="form-item">
        <text class="label">用电量</text>
        <up-input
          v-model="electricity"
          placeholder="请输入用电量"
          border="none"
        ></up-input>
      </view>
	  <view class="form-item">
	    <text class="label">用水量</text>
	    <up-input
	      v-model="water"
	      placeholder="请输入用水量"
	      border="none"
	    ></up-input>
	  </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-btn">
      <button @click="handleSubmit">提交</button>
    </view>
  </view>
</template>

<script>
	import Request from '@/components/utils/request';
	import Queue from '@/components/utils/queue';
	import customHeader from '@/components/page/header.vue';
export default {
  components: {
  			customHeader
  		},
  data() {
    return {
      electricity: '',
	  footage:'',
	  water:'',
	  operatorName: '',
	  plan:{
		  id:'',
		  code:'',
		  createAt:''
	  }
    }
  },
  created() {
    this.getOperatorName()
  },
  computed: {
    isEmptyPlan() {
      return !this.plan.holeNumber && !this.plan.drillName && !this.plan.createdAt;
    },
	
  },
  methods:{
	  async getOperatorName() {
	    const userInfo = await Queue.getData('user')
	    this.operatorName = userInfo?.name || ''
	  },
	  handlePlan(){
	  		uni.navigateTo({
	  			url:'/pages/apply/components/message/planlist?from=shift'
	  		})
	  	},
		async handleSubmit() {
		  // if (!this.plan.id) {
		  //   uni.showToast({
		  //     title: '请选择任务',
		  //     icon: 'none'
		  //   })
		  //   return
		  // }
		  if (!this.electricity) {
		    uni.showToast({
		      title: '请输入用电量',
		      icon: 'none'
		    })
		    return
		  }
		  if (!this.water) {
		    uni.showToast({
		      title: '请输入用水量',
		      icon: 'none'
		    })
		    return
		  }
		if (!this.footage) {
		  uni.showToast({
		    title: '请输入进尺数',
		    icon: 'none'
		  })
		  return
		}
		  try {
		    const res = await Request.post('/drill/report_after_work', {
		      taskId: this.plan.id,
		      footageNum: this.footage,
		      useElectricity: this.electricity,
			  useWater:this.water
		    })
		    
		    if (res.status === 0) {
		      uni.showToast({
		        title: '提交成功',
		        icon: 'none'
		      })
		      uni.navigateBack()
		    }
		  } catch (error) {
		    uni.showToast({
		      title: '提交失败',
		      icon: 'none'
		    })
		  }
		}
  }
}
</script>
<style>
	page{
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page{
		background: #16171b;
	}
.seal-form {
  min-height: 100vh;
  background-color: #1a1b1e;
  padding: 32rpx;
 padding-top: 188rpx;
  .form-content {
    background-color: #2a2b2e;
    border-radius: 16rpx;
    padding: 0 32rpx;
    margin-bottom: 28rpx;
    // 添加第二组表单的上边距
   

    .form-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 32rpx 0;
      border-bottom: 1rpx solid rgba(255, 255, 255, 0.04);

      &:last-child {
        border-bottom: none;
      }

      .label {
        color: rgba(255,255,255,0.85);
        font-size: 28rpx;
      }

      .value {
        color: rgba(255,255,255,0.65);
        font-size: 28rpx;
      }

      .value-with-arrow {
        display: flex;
        align-items: center;
        color: rgba(255,255,255,0.45);
        font-size: 28rpx;

        :deep(.up-icon) {
          margin-left: 10rpx;
        }
      }

      :deep(.uni-input-input) {
        text-align: right;
        color: rgba(255,255,255,0.65);
        
        .up-input__input {
          color: rgba(255,255,255,0.65);
          &::placeholder {
            color: rgba(255,255,255,0.45);
          }
        }
      }
    }
  }
  :deep(.uni-input-input){
	  font-size: 28rpx;
	  text-align: right;
	  // background: #fff;
  }
  :deep(.uni-input-placeholder){
	  font-size: 28rpx;
	  text-align: right;
	  color: rgba(255,255,255,0.45) !important;
  }
  .submit-btn {
    position: fixed;
    bottom: 100rpx;
	width: calc(100% - 64rpx);
	button{
		background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%) !important;
		color: rgba(255,255,255,0.85);
	}
	
  }
}
.form_item{
	padding: 32rpx 0;
	.label{
		color:rgba(255,255,255,0.85);
		// margin-bottom: 28rpx;
	}
}
:deep(.u-textarea){
	border:none;
	
	padding:0 !important;
	padding-top: 24rpx !important;
	background: rgba(255, 255, 255, 0.0);
	color: rgba(255, 255, 255, 0.85) !important;
	.u-textarea__field{
		color: rgba(255, 255, 255, 0.85) !important;
	}
}
    .plan_cont{
		display: flex;
		flex-direction: column;
		padding: 32rpx 0;
		padding-bottom: 20rpx;
		.plan_label{
			font-size: 32rpx;
			margin-bottom: 24rpx;
			color: rgba(255, 255, 255, 0.85);
		}
		.plan_value{
			color: rgba(255, 255, 255, 0.65);
			font-size: 28rpx;
			margin-bottom: 12rpx;
		}
	}
</style>