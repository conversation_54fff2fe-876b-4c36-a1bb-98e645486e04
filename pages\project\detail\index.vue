<template>
	<custom-header style="height: 88rpx;" title="设备详情" showBack />
	<view class="equip_content">

		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        ref="upTabs"
        :list="list4"
        :current="currentTab"
        lineWidth="30"
        lineColor="rgba(255,255,255,0.85)"
        :activeStyle="{
            color: 'rgba(255,255,255,0.85)',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.65)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:22rpx 40px; padding-right: 35px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		<template v-if="currentTab == 0">
			<work :workData="workData" :deviceData="deviceData"></work>
		</template>
		<template v-if="currentTab == 1">
			<monitor ref="monitorComponent" :gbsId="gbsId" :deviceCode="deviceCode" :isVisible="currentTab === 1" :key="monitorKey"></monitor>
		</template>
		<template v-if="currentTab == 2">
			<drill :deviceCode="deviceCode"></drill>
		</template>
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
    import work from './work.vue'
	import monitor from './monitor.vue'
	import drill from './drill.vue'
	// import siteCont from './site.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			work,
			monitor,
			drill
			// siteCont
		},
		data(){
			return{
				currentTab: 0,
				id: null,
				deviceCode: '',
				gbsId:'',
				workData: {},
				deviceData: {},
				monitorKey: 0, // 用于强制重新创建监控组件
				list4:[  
				    // { name: '其他信息' },  
				    { name: '工作' },  
				    { name: '监控' },  
				    { name: '打孔记录' },  
				    // { name: '设备寿命跟踪' },  
				    // { name: '设备转移记录' }
				],
			}
		},
		
		onLoad(options) {
			if (options.deviceCode) {
				this.deviceCode = options.deviceCode;
				this.fetchDeviceData();
			}
		},
		
		onShow(){
			
		},
		methods:{
			handleTabChange(e) {
				// 获取正确的索引值
				const index = e.index !== undefined ? e.index : e;
				console.log('Tab切换到:', index);
				
				// 如果从监控页面切换出去
				if (this.currentTab === 1 && index !== 1) {
					console.log('从监控页面切换出去，终止视频流');
					
					// 调用强制终止方法
					try {
						if (this.$refs.monitorComponent) {
							// 优先使用forceStopVideoStream方法
							if (typeof this.$refs.monitorComponent.forceStopVideoStream === 'function') {
								this.$refs.monitorComponent.forceStopVideoStream();
								console.log('已调用强制终止视频流方法');
							} else if (typeof this.$refs.monitorComponent.stopVideoStream === 'function') {
								this.$refs.monitorComponent.stopVideoStream();
								console.log('已调用常规终止视频流方法');
							}
							
							// 延迟更新key，确保先执行终止操作
							setTimeout(() => {
								this.monitorKey++;
								console.log('已更新monitorKey:', this.monitorKey);
							}, 500);
						}
					} catch (error) {
						console.error('终止视频流失败:', error);
						// 即使出错也更新key
						this.monitorKey++;
					}
				}
				
				// 更新当前标签页
				this.currentTab = index;
			},
			
			async fetchDeviceData() {
				try {
					// Get current device data
					const params = { number: this.deviceCode };
					
					const currentRes = await Request.post('/device_data/get_current', params);
					console.log('获取设备当前数据响应:', currentRes);
					
					if (currentRes.status === 0) {
						this.workData = currentRes.data || {};
					}
					
					// Get device details
					const detailRes = await Request.post('/device_data/get_detail', params);
					console.log('获取设备详情响应:', detailRes);
					
					if (detailRes.status === 0) {
						this.deviceData = detailRes.data || {};
					}
					const detailMon=await Request.post('/device/get_detail', params);
									console.log('detailMon:', detailMon);
					if (detailMon.status === 0) {
						this.gbsId = detailMon.data.gbsId;
						
						console.log('gbsId:', this.gbsId);
					}
				} catch (error) {
					console.error('Failed to fetch device data:', error);
					uni.showToast({
						title: '获取设备数据失败',
						icon: 'none'
					});
				}
			}
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	// page {
	// 	background: #16171b;
	// }
	// :deep(.u-tabs__wrapper__nav){
	// 	justify-content: space-around !important;
	// }
	.equip_content{
		// padding:0 32rpx ;
		padding-top: 156rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		// padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.tab_cont{
		border-radius: 12rpx;
		// padding: 0 32rpx;
		// margin-top: 32rpx;
		// height: 680rpx;
		// background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx  12rpx 0 0;
		// padding:0 32rpx;
		// border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		
	}
	.content{
		border-radius:  0 0 12rpx  12rpx;
		// border: 1rpx solid rgba(255, 255, 255, 0.0972);
		// padding: 32rpx;
		// height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>