<template>
	<custom-header style="height: 88rpx;" title="钻场详情" showBack />
	<view class="equip_content">
		<view class="equip_header">
			<text class="header_text">{{prolist.name}}</text>
        <view class="headerfl">
			<view class="header_flex">
				<view>
					<view class="flex_label">钻场编号</view>
					<view class="flex_value">{{prolist.drillsiteId}}</view>
				</view>
				<view>
					<view class="flex_label">钻场位置</view>
					<view class="flex_value">{{prolist.location}}</view>
				</view>
			</view>
			<view class="header_flex" style="margin-left:120rpx ;">
				<view>
					<view class="flex_label">钻场类型</view>
					<view class="flex_value">{{prolist.type}}</view>
				</view>
				<view>
					<view class="flex_label">使用状态</view>
					<view class="flex_value" v-if="prolist.status==0" style="color:#7a1b2a;">未使用</view>
					<view class="flex_value" v-if="prolist.status==1" style="color: #177DDC;">使用中</view>
				</view>
			</view>
			</view>
		</view>
		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        :list="list4"
        lineWidth="0"
		
        lineColor="#177DDC"
        :activeStyle="{
            color: 'rgba(255,255,255,0.85)',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:10rpx 15px; padding-right: 15px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		
		<template v-if="currentTab == 0">
			<view class="content">
			 <view class="cont_flex">
			 	<view class="label">施工日期</view>
				<view class="value">{{prolist.startTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">竣工日期</view>
			 	<view class="value">{{prolist.endTime}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">钻孔数量</view>
			 	<view class="value">{{prolist.number}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">钻孔长度</view>
			 	<view class="value">{{prolist.length}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">钻孔设计深度</view>
			 	<view class="value">{{prolist.designDepth}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">钻孔实际深度</view>
			 	<view class="value">{{prolist.actualDepth}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">钻孔管理负责人</view>
			 	<view class="value">{{prolist.responsiblePerson}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">备注</view>
			 	<view class="value">{{prolist.remark}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">巷道id</view>
			 	<view class="value">{{prolist.lanewayIds}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">巷道编号</view>
			 	<view class="value">{{prolist.lanewayName}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">创建时间</view>
			 	<view class="value">{{prolist.createdAt}}</view>
			 </view>
			 <view class="cont_flex">
			 	<view class="label">修改时间</view>
			 	<view class="value">{{prolist.updateAt}}</view>
			 </view>
			 </view>
		</template>
	
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			
		},
		data(){
			return{
				currentTab:0,
				id: null,
			list4:[  
			    { name: '其他信息' },  
			    // { name: '状态监控' },  
			    // { name: '历史记录' },  
			    // { name: '保养计划记录' },  
			    // { name: '设备寿命跟踪' },  
			    // { name: '设备转移记录' }
			],
			prolist:{
				drillsiteId: "ZHUANCHANG001",
				name: "1号瓦斯抽采钻场",
				location: "西北翼采区",
				type: "瓦斯抽采钻场",
				startTime: "2023-04-01",
				endTime: "2023-05-11",
				number: "50个",
				length: '2000米',
				designDepth: '300米',
				actualDepth: '250米',
				diameter: '200mm',
				responsiblePerson: "钻场负责人王五",
				status:1,
				remark:'备注',
				lanewayId: 'CAIMIAN001',
				lanewayName:"1号采面"
			}
			}
		},
		onLoad(options) {
			this.id = options.id
			this.handelDetail()
		},
		onShow(){
			if (this.id) {
				// this.handelDetail()
			}
		},
		methods:{
			handleTabChange(index) {
				// console.log("indexaaa",index.index);
				this.currentTab = index.index; // 更新当前选中的标签索引
				// console.log(this.currentTab);
			},
			async handelDetail() {
			
				try {
			
					const res = await Request.get('/drillsite/get_info', {
						id: this.id
					})
			
					if (res.status == 0) {
						console.log('返回数据', res);
						this.prolist = res.data;
			
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });
			
					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
			
				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
			
			},
					
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	.equip_content{
		padding:0 32rpx ;
		padding-top: 1rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.tab_cont{
		border-radius: 12rpx;
		// padding: 0 32rpx;
		margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx  12rpx 0 0;
		padding:0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		
	}
	.content{
		border-radius:  0 0 12rpx  12rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		padding: 32rpx;
		// height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>