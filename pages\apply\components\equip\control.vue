<template>
	<view class="con_content">
	
		<view class="search-bar">
			<view class="bar">
				
			<input 
			type="text" 
			class="sear_inp" 
			placeholder="请输入变量名称" 
			@input="clearInput"
			v-model="searchText" />
			<icon size="16" type="clear" v-if="showClearIcon" @click="clearIcon"></icon>
			</view>
			<!-- <up-input class="sear_inp" placeholder="钻具名称/钻具ID" prefixIcon="search" v-model="searchText"
				color="rgba(255,255,255,0.8)" prefixIconStyle="font-size: 22px;color: #909399"></up-input> -->
			<button class="con_btn" @click="search">查询</button>
		</view>
		<view class="con_list">
			<scroll-view class="scroll-view" scroll-y="true" :scroll-with-animation="true" :lower-threshold="130"
				@scrolltolower="pullUpLoading">
				<view class="con_listitem" v-for="(item,index) in data" :key="index">
					<view class="item">
						<view class="left_text1">{{item.title}}</view>
						<text class="right_text1">{{item.var}}</text>
					</view>
					<view class="item">
						<view class="left_text2" v-if="item.type==0">{{item.val}} {{item.unit}}</view>
						<view class="left_text2" v-if="item.type==2">{{getValText(item.val)}}</view>                          
						<view class="left_text2" v-else>{{item.val}}</view>
						<view class="right_text2">{{getTypeText(item.type)}}</view>
					</view>
					<view class="item">
						<view class="left_text3"></view>
						<view class="left_text3">{{item.updatedAt}}</view>
					</view>
					
				</view>
				<!-- <view class="loading-indicator" v-if="nomore">暂无数据~~</view> -->
				<view class="loading-indicator" v-if="loading">加载更多...</view>
				<view class="loading-indicator" v-else-if="!hasmore">没有更多数据了</view>
			</scroll-view>
		</view>

	</view>
</template>

<script>
	import Request from '@/components/utils/request.js'
	import Quene from '@/components/utils/queue';
	export default {
		inheritAttrs: false,
		data() {
			const baseData = {
				id: "13688",
				title: "A相电压",
				var: 'U',
				unit:"V",
				type: 0,
				val: "236.8",
				updateAt: "2023-12-23 10:12:00",

			};
			const generateData = () => {
				const repeatedData = [];
				for (let i = 0; i < 20; i++) {
					repeatedData.push({
						id: i,
						...baseData
					})
				}
				return repeatedData;
			}
			return {
				dateStart: '',
				dateEnd: '',
				searchText: '',
				data:[],
				// data: generateData(), //表格数据
				flag1: true,
				flag2: true,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				loading: false, // 是否显示"加载更多"  
				hasmore: true,
				nomore:false,
				showClearIcon:false,
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
				timer: null,           // 添加定时器变量
				pollingInterval: 5000,  // 轮询间隔时间，默认5秒
				isPolling: false,
				lastData: null,
				debounceTimer: null,
				deviceId: null,  // 添加设备ID
			}
		},

		created() {
			// 监听开始轮询的消息
			uni.$on('startPolling', ({ deviceId }) => {
				this.deviceId = deviceId;
				this.startPolling();
			});
			
			// 监听停止轮询的消息
			uni.$on('stopPolling', this.stopPolling);
		},
		onShow() {
			this.handelDrill();
			// 页面显示时启动轮询
			this.startPolling();
		},
		onHide() {
			// 页面隐藏时清除轮询
			this.stopPolling();
		},
		onUnload() {
			// 页面卸载时清除轮询
			this.stopPolling();
		},
		// mounted() {
		// 	this.handelDrill();
		// 	// 启动轮询
		// 	this.startPolling();
		// },
		beforeDestroy() {
			// 组件销毁前清除轮询
			this.stopPolling();
			uni.$off('startPolling');
			uni.$off('stopPolling');
		},
		onLoad(options) {
			console.log(options);
			this.dateStart = options.dateStart;
			this.dateEnd = options.dateEnd;
		},
		methods: {
			getTypeText(type) {
				if (type === 0) {
					return '数值';
				} else if (type === 1) {
					return '文本';
				} else if (type === 2) {
					
					return '开关';
					//0：关 ，1：开
				}else if (type === 3) {
					return '时间';
				}else if (type === 4) {
					return '日期';
				}else if (type === 5) {
					return '频谱';
				}
				return '数据点位'; // 默认值
			},
			getValText(val){
				if(val===0){
					return '关'
				}else if(val===1){
					return '开'
				}
			},
			getSignText(sign) {
				if (sign === 0) {
					return '标识1';
				} else if (sign === 1) {
					return '标识2';
				} else if (sign === 2) {
					return '标识3';
				}
				return 'U'; // 默认值
			},
			clearInput(event) {
            this.searchText = event.detail.value;
            if (event.detail.value.length > 0) {
                this.showClearIcon = true;
            } else {
                this.showClearIcon = false;
            }
        },
        clearIcon() {
            this.searchText = '';
            this.showClearIcon = false;
        },
			search() {
				if (this.debounceTimer) {
					clearTimeout(this.debounceTimer);
				}
				
				this.debounceTimer = setTimeout(() => {
					this.currentPage = 1; // 重置页码
					this.data = []; // 清空现有数据
					this.nomore = false; // 重置无数据标志
					this.hasmore = true; // 重置更多数据标志
					this.lastData = null; // 重置上次数据比较
					
					this.stopPolling(); // 停止当前轮询
					this.handelDrill(); // 重新加载数据
					this.startPolling(); // 开始新的轮询
				}, 300);
			},
			//初始加载调取接口获取数据
			async handelDrill() {
				if (this.loading) return;
				
				try {
					this.loading = true;
					let drill_data = {
						page: this.currentPage,
						perPage: this.perPage,
						number:Quene.getData('number'),
						keyWord: this.searchText
					};
					
					const res = await Request.post('/device_data/get_ls', drill_data);

					if (res.status == 0) {
						// 检查数据是否有变化
						const newDataStr = JSON.stringify(res.data.items);
						if (this.lastData !== newDataStr) {
							this.lastData = newDataStr;
							
							// 如果是第一页，直接替换数据
							if (this.currentPage === 1) {
								this.data = res.data.items;
							} else {
								// 如果是加载更多，则追加数据
								this.data = this.data.concat(res.data.items);
							}
							
							// 如果当前页数据少于每页数量，说明没有更多数据了
							if (res.data.items.length < this.perPage) {
								this.hasmore = false;
							} else {
								this.hasmore = true;
							}
							
							if (res.data.items.length === 0) {
								this.nomore = true;
							} else {
								this.nomore = false;
							}
						}
					} else {
						console.error('接口返回错误:', res.msg);
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('接口请求失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				} finally {
					this.loading = false;
				}
			},

			//筛选
			handelSearch() {
				uni.navigateTo({
					url: '/pages/apply/components/equip/search'
				})
			},
			cellStyle({
				row,
				column,
				rowIndex,
				columnIndex
			}) {
				// console.log('row, column, rowIndex, columnIndex')
				if ((columnIndex % 2) != 0) {
					return {
						background: 'red'
					}
				}
			},

			async pullUpLoading() {
				if (this.loading || !this.hasmore) return;
				this.loading = true;
				this.currentPage++;  // 页码加1
						try {
				           const res = await Request.post('/device_data/get_ls', {
				              page: this.currentPage,
				              perPage: this.perPage,
								 number:Quene.getData('number'),	
								 keyWord:this.searchText,
				            });

				           if (res.status == 0) {
							   console.log('加载获取数据',res.data);		 
				           		if (res.data.items && res.data.items.length > 0) {
									this.data = this.data.concat(res.data.items);
									console.log('data11111',this.data);
								}else{
								    this.hasmore = false;    // 没有更多数据，不再显示加载更多
								   
								}
								this.loading = false;
				           } else {

						        //    uni.showToast({
								// 	title: '加载数据失败' ,
								// 	icon: 'none',
								// 	duration: 1000
								// })

				           }
						} catch (error) {
						    console.error("加载更多数据失败:", error);
						      // uni.showToast({
					        	// 	title: '网络错误，请稍后重试' ,
					        	// 	icon: 'none',
					        	// 	duration: 1000
					        	// })
						}
				// setTimeout(() => {
				// 	const newItem1 = {
				// 		id: "13688",
				// 		name: "B相电压---",
				// 		sign: 0,
				// 		type: 0,
				// 		number: "236.8",
				// 		updateAt: "2023-12-23 10:12:00",
				// 	};

				// 	this.data = [...this.data, newItem1];
				// 	this.num++
				// 	console.log(this.num);
				// 	if (this.num === 3) {
				// 		// this.loading = false;
				// 		this.hasmore = false
				// 	}

				// 	this.loading = false;

				// }, 2000)
			},

			// 开始轮询
			startPolling() {
				if (this.timer || this.isPolling) {
					this.stopPolling();
				}
				
				this.isPolling = true;
				this.timer = setInterval(() => {
					// 只在页面可见时进行轮询
					if (!document.hidden) {
						// 如果当前页没有数据，回到第一页进行轮询
						if (this.nomore && this.currentPage > 1) {
							this.currentPage = 1;
						}
						this.handelDrill();
					}
				}, this.pollingInterval);
			},

			// 停止轮询
			stopPolling() {
				this.isPolling = false;
				if (this.timer) {
					clearInterval(this.timer);
					this.timer = null;
				}
			},
		},

	}
</script>

<style scoped lang="scss">
	page {
		background: #16171b;
	}

	.con_content {
		border-radius:0 0 12rpx 12rpx;
		padding: 0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		// height: 600rpx;
		overflow: hidden;
		// background: #fff;
	}

	.filter {
		// margin: 28rpx 0;
		height: 70rpx;
		display: flex;
		// background: #fff;
	}

	.sear_inp {
		height: 70rpx;
		// padding: 10rpx 28rpx;
		padding-left: 28rpx;
		font-size: 25rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.select {
		flex: 1;
		border: none;
		// border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		// background: rgba(255, 255, 255, 0.08);
	}

	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
	}
	.bar{
		flex:1;
		padding-right: 10rpx;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
		display: flex;
		align-items: center;
	}
	.con_btn {
		height: 65rpx;
		line-height: 65rpx;
		background: #007BFF;
		letter-spacing: 0px;
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.sear_inp {
		flex: 1;
		margin-right: 16rpx;
		// background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}

	.con_list {
		// height: 650rpx;
		margin-top: 32rpx;
	}

	.scroll-view {
		height: 650rpx;
		padding-bottom: 16rpx;
	}

	.con_listitem {
		// display: flex;
		// justify-content: space-between;
		border-radius: 16rpx;
		background: rgba(255, 255, 255, 0.08);
		padding: 28rpx;
		margin-bottom: 24rpx;
	}

	.left_text1 {
		color: #9CA3AF;
		font-family: Roboto;
		letter-spacing: 0px;
		font-size: 24rpx;
	}

	.left_text2 {
		color: rgba(255, 255, 255, 0.8);
		font-size: 35rpx;
		margin: 12rpx 0;
		letter-spacing: 0px;
	}

	.left_text3 {
		color: #9CA3AF;
		font-size: 21rpx;
		line-height: 28rpx;
		letter-spacing: 0px;
	}

	.item_right {
		text-align: right;

	}

	.right_text1 {
		// width: 40rpx;
		border-radius: 4rpx;
		padding: 4rpx 12rpx;
		font-size: 21rpx;
		background: rgba(0, 127, 255, 0.12);
		box-sizing: border-box;
		color: #007FFF;
		border: 1px solid rgba(0, 127, 255, 0.48);
	}

	.right_text2 {
		// margin-top: 20rpx;
		font-size: 21rpx;
		font-weight: normal;
		line-height: 28rpx;
		letter-spacing: 0px;
		color: rgba(0, 127, 255, 0.85);
	}

	.item {
		flex: 1;
		margin-bottom: 8rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.loading-indicator {
		text-align: center;
		color: rgba(255, 255, 255, 0.28);
	}
</style>