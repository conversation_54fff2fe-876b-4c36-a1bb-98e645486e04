{"name": "distort_Beauty", "factor": 1, "params": [{"name": "eye", "idxs": [0, 1, 2, 3]}, {"name": "smallface", "idxs": [4, 5, 6, 7, 8, 9, 11, 12, 13, 14, 15, 16, 17]}, {"name": "jaw", "idxs": [10]}, {"name": "thinface", "idxs": [18, 19, 20, 21, 22, 23]}], "elements": [{"from": 52, "to": 55, "method": 3, "rad_type": 1, "rad_scale": 0.08, "dir_scale": 0.3}, {"from": 72, "to": 73, "method": 4, "rad_type": 1, "rad_scale": 0.15, "dir_scale": 0.3}, {"from": 58, "to": 61, "method": 3, "rad_type": 1, "rad_scale": 0.08, "dir_scale": 0.3}, {"from": 75, "to": 76, "method": 4, "rad_type": 1, "rad_scale": 0.15, "dir_scale": 0.3}, {"debug": 0, "from": 29, "to": 44, "method": 0, "rad_type": 1, "rad_scale": 0.8, "dir_scale": 0.08}, {"debug": 0, "from": 3, "to": 44, "method": 0, "rad_type": 1, "rad_scale": 0.8, "dir_scale": 0.08}, {"debug": 0, "from": 24, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 1.1, "dir_scale": 0.17}, {"debug": 0, "from": 8, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 1.1, "dir_scale": 0.17}, {"debug": 0, "from": 22, "to": 10, "method": 0, "rad_type": 1, "rad_scale": 1.5, "dir_scale": 0.03}, {"debug": 0, "from": 10, "to": 22, "method": 0, "rad_type": 1, "rad_scale": 1.5, "dir_scale": 0.03}, {"debug": 0, "from": 16, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 1, "dir_scale": 0.1}, {"debug": 0, "from": 19, "to": 13, "method": 0, "rad_type": 1, "rad_scale": 0.2, "dir_scale": 0.002}, {"debug": 0, "from": 13, "to": 19, "method": 0, "rad_type": 1, "rad_scale": 0.2, "dir_scale": 0.002}, {"debug": 0, "from": 22, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 0.5, "dir_scale": 0.04}, {"debug": 0, "from": 10, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 0.5, "dir_scale": 0.04}, {"debug": 0, "from": 19, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 1.5, "dir_scale": 0.14}, {"debug": 0, "from": 13, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 1.5, "dir_scale": 0.14}, {"debug": 0, "from": 16, "to": 46, "method": 0, "rad_type": 1, "rad_scale": 1, "dir_scale": 0.0001}, {"debug": 0, "from": 30, "to": 44, "method": 0, "rad_type": 1, "rad_scale": 1.2, "dir_scale": 0.08}, {"debug": 0, "from": 2, "to": 44, "method": 0, "rad_type": 1, "rad_scale": 1.2, "dir_scale": 0.08}, {"debug": 0, "from": 25, "to": 97, "method": 0, "rad_type": 1, "rad_scale": 1.2, "dir_scale": 0.06}, {"debug": 0, "from": 7, "to": 97, "method": 0, "rad_type": 1, "rad_scale": 1.2, "dir_scale": 0.06}, {"debug": 0, "from": 12, "to": 20, "method": 0, "rad_type": 1, "rad_scale": 1.2, "dir_scale": 0.06}, {"debug": 0, "from": 20, "to": 12, "method": 0, "rad_type": 1, "rad_scale": 1.2, "dir_scale": 0.06}]}