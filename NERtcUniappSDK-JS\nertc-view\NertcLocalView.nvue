<template>
	<NERTCUniPluginSDK-NERTCLocalViewComponent 
		:mediaType="mediaType" 
		:channel="channel" 
		:viewID="viewID"
		@onViewLoad="onViewLoad"
		>
	</NERTCUniPluginSDK-NERTCLocalViewComponent>
</template>

<script>
	export default {
		name: 'NertcLocalView',
		methods: {
			onViewLoad() {
				this.$emit('onViewLoad')
			}
		},
		emits: ['onViewLoad'],
		props: {
			mediaType: {
				type: String,
				default: ''
			},
			channel: {
				type: String,
				default: ""
			},
			viewID: {
				type: String,
				default: ""
			}
		},
	}
</script>

<style>

</style>
