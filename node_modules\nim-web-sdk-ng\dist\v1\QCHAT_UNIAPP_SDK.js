/**
 * 
 *   Version: 10.8.30
 * 
 *   Git Hash: 53d9f639411d85eb37f1039804197c286cc2e303
 * 
 *   Created At: 4/24/2025, 7:32:47 AM
 * 
 *   Target: QCHAT_UNIAPP_SDK.js
 *   
 */

!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).QChat=t()}(this,(function(){"use strict";"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function createCommonjsModule(e){var t={exports:{}};return e(t,t.exports),t.exports}var e,t,r,i,a,n,o,s,c,l,d,h,u,m,p,g,y,_,v,E,I,f,M,C,T,S,R,A,N,b,O,P,w,k,q,L,D,V,U,x,G,B,F,j,Y,Q,W,H,$,K,J,z,X,Z=createCommonjsModule((function(e){var t=Object.prototype.hasOwnProperty,r="~";function Events(){}function EE(e,t,r){this.fn=e,this.context=t,this.once=r||!1}function addListener(e,t,i,a,n){if("function"!=typeof i)throw new TypeError("The listener must be a function");var o=new EE(i,a||e,n),s=r?r+t:t;return e._events[s]?e._events[s].fn?e._events[s]=[e._events[s],o]:e._events[s].push(o):(e._events[s]=o,e._eventsCount++),e}function clearEvent(e,t){0==--e._eventsCount?e._events=new Events:delete e._events[t]}function EventEmitter(){this._events=new Events,this._eventsCount=0}Object.create&&(Events.prototype=Object.create(null),(new Events).__proto__||(r=!1)),EventEmitter.prototype.eventNames=function eventNames(){var e,i,a=[];if(0===this._eventsCount)return a;for(i in e=this._events)t.call(e,i)&&a.push(r?i.slice(1):i);return Object.getOwnPropertySymbols?a.concat(Object.getOwnPropertySymbols(e)):a},EventEmitter.prototype.listeners=function listeners(e){var t=r?r+e:e,i=this._events[t];if(!i)return[];if(i.fn)return[i.fn];for(var a=0,n=i.length,o=new Array(n);a<n;a++)o[a]=i[a].fn;return o},EventEmitter.prototype.listenerCount=function listenerCount(e){var t=r?r+e:e,i=this._events[t];return i?i.fn?1:i.length:0},EventEmitter.prototype.emit=function emit(e,t,i,a,n,o){var s=r?r+e:e;if(!this._events[s])return!1;var c,l,d=this._events[s],h=arguments.length;if(d.fn){switch(d.once&&this.removeListener(e,d.fn,void 0,!0),h){case 1:return d.fn.call(d.context),!0;case 2:return d.fn.call(d.context,t),!0;case 3:return d.fn.call(d.context,t,i),!0;case 4:return d.fn.call(d.context,t,i,a),!0;case 5:return d.fn.call(d.context,t,i,a,n),!0;case 6:return d.fn.call(d.context,t,i,a,n,o),!0}for(l=1,c=new Array(h-1);l<h;l++)c[l-1]=arguments[l];d.fn.apply(d.context,c)}else{var u,m=d.length;for(l=0;l<m;l++)switch(d[l].once&&this.removeListener(e,d[l].fn,void 0,!0),h){case 1:d[l].fn.call(d[l].context);break;case 2:d[l].fn.call(d[l].context,t);break;case 3:d[l].fn.call(d[l].context,t,i);break;case 4:d[l].fn.call(d[l].context,t,i,a);break;default:if(!c)for(u=1,c=new Array(h-1);u<h;u++)c[u-1]=arguments[u];d[l].fn.apply(d[l].context,c)}}return!0},EventEmitter.prototype.on=function on(e,t,r){return addListener(this,e,t,r,!1)},EventEmitter.prototype.once=function once(e,t,r){return addListener(this,e,t,r,!0)},EventEmitter.prototype.removeListener=function removeListener(e,t,i,a){var n=r?r+e:e;if(!this._events[n])return this;if(!t)return clearEvent(this,n),this;var o=this._events[n];if(o.fn)o.fn!==t||a&&!o.once||i&&o.context!==i||clearEvent(this,n);else{for(var s=0,c=[],l=o.length;s<l;s++)(o[s].fn!==t||a&&!o[s].once||i&&o[s].context!==i)&&c.push(o[s]);c.length?this._events[n]=1===c.length?c[0]:c:clearEvent(this,n)}return this},EventEmitter.prototype.removeAllListeners=function removeAllListeners(e){var t;return e?(t=r?r+e:e,this._events[t]&&clearEvent(this,t)):(this._events=new Events,this._eventsCount=0),this},EventEmitter.prototype.off=EventEmitter.prototype.removeListener,EventEmitter.prototype.addListener=EventEmitter.prototype.on,EventEmitter.prefixed=r,EventEmitter.EventEmitter=EventEmitter,e.exports=EventEmitter})),ee=createCommonjsModule((function(e,t){e.exports=function(){function _regeneratorRuntime(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
_regeneratorRuntime=function(){return e};var e={},t=Object.prototype,r=t.hasOwnProperty,i="function"==typeof Symbol?Symbol:{},a=i.iterator||"@@iterator",n=i.asyncIterator||"@@asyncIterator",o=i.toStringTag||"@@toStringTag";function define(e,t,r){return Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}),e[t]}try{define({},"")}catch(e){define=function(e,t,r){return e[t]=r}}function wrap(e,t,r,i){var a=t&&t.prototype instanceof Generator?t:Generator,n=Object.create(a.prototype),o=new Context(i||[]);return n._invoke=function(e,t,r){var i="suspendedStart";return function(a,n){if("executing"===i)throw new Error("Generator is already running");if("completed"===i){if("throw"===a)throw n;return doneResult()}for(r.method=a,r.arg=n;;){var o=r.delegate;if(o){var c=maybeInvokeDelegate(o,r);if(c){if(c===s)continue;return c}}if("next"===r.method)r.sent=r._sent=r.arg;else if("throw"===r.method){if("suspendedStart"===i)throw i="completed",r.arg;r.dispatchException(r.arg)}else"return"===r.method&&r.abrupt("return",r.arg);i="executing";var l=tryCatch(e,t,r);if("normal"===l.type){if(i=r.done?"completed":"suspendedYield",l.arg===s)continue;return{value:l.arg,done:r.done}}"throw"===l.type&&(i="completed",r.method="throw",r.arg=l.arg)}}}(e,r,o),n}function tryCatch(e,t,r){try{return{type:"normal",arg:e.call(t,r)}}catch(e){return{type:"throw",arg:e}}}e.wrap=wrap;var s={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var c={};define(c,a,(function(){return this}));var l=Object.getPrototypeOf,d=l&&l(l(values([])));d&&d!==t&&r.call(d,a)&&(c=d);var h=GeneratorFunctionPrototype.prototype=Generator.prototype=Object.create(c);function defineIteratorMethods(e){["next","throw","return"].forEach((function(t){define(e,t,(function(e){return this._invoke(t,e)}))}))}function AsyncIterator(e,t){function invoke(i,a,n,o){var s=tryCatch(e[i],e,a);if("throw"!==s.type){var c=s.arg,l=c.value;return l&&"object"==typeof l&&r.call(l,"__await")?t.resolve(l.__await).then((function(e){invoke("next",e,n,o)}),(function(e){invoke("throw",e,n,o)})):t.resolve(l).then((function(e){c.value=e,n(c)}),(function(e){return invoke("throw",e,n,o)}))}o(s.arg)}var i;this._invoke=function(e,r){function callInvokeWithMethodAndArg(){return new t((function(t,i){invoke(e,r,t,i)}))}return i=i?i.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}}function maybeInvokeDelegate(e,t){var r=e.iterator[t.method];if(void 0===r){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=void 0,maybeInvokeDelegate(e,t),"throw"===t.method))return s;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return s}var i=tryCatch(r,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,s;var a=i.arg;return a?a.done?(t[e.resultName]=a.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=void 0),t.delegate=null,s):a:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,s)}function pushTryEntry(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function resetTryEntry(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function Context(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(pushTryEntry,this),this.reset(!0)}function values(e){if(e){var t=e[a];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var i=-1,n=function next(){for(;++i<e.length;)if(r.call(e,i))return next.value=e[i],next.done=!1,next;return next.value=void 0,next.done=!0,next};return n.next=n}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,define(h,"constructor",GeneratorFunctionPrototype),define(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,o,"GeneratorFunction"),e.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===GeneratorFunction||"GeneratorFunction"===(t.displayName||t.name))},e.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,GeneratorFunctionPrototype):(e.__proto__=GeneratorFunctionPrototype,define(e,o,"GeneratorFunction")),e.prototype=Object.create(h),e},e.awrap=function(e){return{__await:e}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,n,(function(){return this})),e.AsyncIterator=AsyncIterator,e.async=function(t,r,i,a,n){void 0===n&&(n=Promise);var o=new AsyncIterator(wrap(t,r,i,a),n);return e.isGeneratorFunction(r)?o:o.next().then((function(e){return e.done?e.value:o.next()}))},defineIteratorMethods(h),define(h,o,"Generator"),define(h,a,(function(){return this})),define(h,"toString",(function(){return"[object Generator]"})),e.keys=function(e){var t=[];for(var r in e)t.push(r);return t.reverse(),function next(){for(;t.length;){var r=t.pop();if(r in e)return next.value=r,next.done=!1,next}return next.done=!0,next}},e.values=values,Context.prototype={constructor:Context,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,this.tryEntries.forEach(resetTryEntry),!e)for(var t in this)"t"===t.charAt(0)&&r.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=void 0)},stop:function(){this.done=!0;var e=this.tryEntries[0].completion;if("throw"===e.type)throw e.arg;return this.rval},dispatchException:function(e){if(this.done)throw e;var t=this;function handle(r,i){return n.type="throw",n.arg=e,t.next=r,i&&(t.method="next",t.arg=void 0),!!i}for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i],n=a.completion;if("root"===a.tryLoc)return handle("end");if(a.tryLoc<=this.prev){var o=r.call(a,"catchLoc"),s=r.call(a,"finallyLoc");if(o&&s){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0);if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}else if(o){if(this.prev<a.catchLoc)return handle(a.catchLoc,!0)}else{if(!s)throw new Error("try statement without catch or finally");if(this.prev<a.finallyLoc)return handle(a.finallyLoc)}}}},abrupt:function(e,t){for(var i=this.tryEntries.length-1;i>=0;--i){var a=this.tryEntries[i];if(a.tryLoc<=this.prev&&r.call(a,"finallyLoc")&&this.prev<a.finallyLoc){var n=a;break}}n&&("break"===e||"continue"===e)&&n.tryLoc<=t&&t<=n.finallyLoc&&(n=null);var o=n?n.completion:{};return o.type=e,o.arg=t,n?(this.method="next",this.next=n.finallyLoc,s):this.complete(o)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),s},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.finallyLoc===e)return this.complete(r.completion,r.afterLoc),resetTryEntry(r),s}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var r=this.tryEntries[t];if(r.tryLoc===e){var i=r.completion;if("throw"===i.type){var a=i.arg;resetTryEntry(r)}return a}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,r){return this.delegate={iterator:values(e),resultName:t,nextLoc:r},"next"===this.method&&(this.arg=void 0),s}},e}function _typeof(e){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},_typeof(e)}function _classCallCheck(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}function _defineProperties(e,t){for(var r=0;r<t.length;r++){var i=t[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}function _createClass(e,t,r){return t&&_defineProperties(e.prototype,t),r&&_defineProperties(e,r),Object.defineProperty(e,"prototype",{writable:!1}),e}function __awaiter(e,t,r,i){function adopt(e){return e instanceof r?e:new r((function(t){t(e)}))}return new(r||(r=Promise))((function(r,a){function fulfilled(e){try{step(i.next(e))}catch(e){a(e)}}function rejected(e){try{step(i.throw(e))}catch(e){a(e)}}function step(e){e.done?r(e.value):adopt(e.value).then(fulfilled,rejected)}step((i=i.apply(e,t||[])).next())}))}var e={isDataReportEnable:!0,maxSize:100,msgListMaxSize:1e3,cacheMaxSize:1e3,maxDelay:3e5,maxInterval:3e4,minInterval:1e4,timeout:5e3,autoStart:!0,loginFailIgnoreInterval:72e5},t=12,r=8e3,i=function emptyFn(){},a=function(){function Reporter(t){_classCallCheck(this,Reporter),this.isUploadEnable=!0,this.serverAllowUpload=!1,this.initConfigLoaded=!1,this.loading=!1,this.isDestroyed=!1,this.reportConfig=e,this.configPath="dispatcher/req",this.dataReportPath="statics/report/common/form",this.traceMsgCache={},this.reqRetryCount=0,this.highPriorityMsgList=[],this.msgList=[],this.lowPriorityMsgList=[],this.cacheMsgList=[],this.lastReportTime=Date.now(),this.timer=null,this.endedAsyncMsgByModule={},this.lastFailLogin={},this.setConfig(t),this.reportConfig.isDataReportEnable&&this.reportConfig.autoStart&&this.initUploadConfig()}return _createClass(Reporter,[{key:"setConfig",value:function setConfig(e){var t=Object.assign({},this.reportConfig.common,e.common);this.reportConfig=Object.assign({},this.reportConfig,e),this.reportConfig.common=t,this.reportConfig.common.sdk_type||(this.reportConfig.common.sdk_type="im")}},{key:"reportImmediately",value:function reportImmediately(e,t){var r=this;this.reportConfig.isDataReportEnable&&this.reportConfig.request(e,Object.assign({dataType:"json",method:"POST",timeout:this.reportConfig.timeout},t)).catch((function(e){var t,i;null===(i=null===(t=r.reportConfig)||void 0===t?void 0:t.logger)||void 0===i||i.warn("Reporter immediately upload failed",e)}))}},{key:"report",value:function report(t,r){var i=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(i.priority||(i.priority=this.getEventPriority(t,r)),this.reportConfig.isDataReportEnable&&t){if("login"===t&&!1===r.succeed&&r.process_id){var a=this.lastFailLogin[r.process_id]||0;if(r.start_time-a<e.loginFailIgnoreInterval)return;this.lastFailLogin[r.process_id]=r.start_time}var n=Date.now();"HIGH"===i.priority?this.highPriorityMsgList.push({module:t,msg:r,createTime:n}):"NORMAL"===i.priority?this.msgList.push({module:t,msg:r,createTime:n}):"LOW"===i.priority&&this.lowPriorityMsgList.push({module:t,msg:r,createTime:n}),this.highPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.highPriorityMsgList.shift(),this.msgList.length>this.reportConfig.msgListMaxSize&&this.msgList.shift(),this.lowPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.lowPriorityMsgList.shift(),this.doReport()}}},{key:"reportTraceStart",value:function reportTraceStart(e,t){if(this.reportConfig.isDataReportEnable&&e&&!this.traceMsgCache[e]){var r=Object.assign(Object.assign({start_time:Date.now()},t),{extension:[]});this.traceMsgCache[e]=r}}},{key:"reportTraceUpdate",value:function reportTraceUpdate(e){}},{key:"reportTraceUpdateV2",value:function reportTraceUpdateV2(e,t,r){var i,a=this;if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[e]){var n=this.traceMsgCache[e].extension,o=n.length,s=(new Date).getTime();0===o?t.duration=s-this.traceMsgCache[e].start_time:n[o-1].end_time?t.duration=s-n[o-1].end_time:t.duration=s-this.traceMsgCache[e].start_time,n.push(Object.assign({end_time:s},t));var c=n.length-1;(null==r?void 0:r.asyncParams)&&((i=this.traceMsgCache[e]).asyncPromiseArray||(i.asyncPromiseArray=[]),this.traceMsgCache[e].asyncPromiseArray.push(r.asyncParams.then((function(t){a.traceMsgCache[e]&&a.traceMsgCache[e].extension[c]&&Object.assign(a.traceMsgCache[e].extension[c],t)}))))}}},{key:"reportTraceEnd",value:function reportTraceEnd(e){var t,r=this,i=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[e])if("nos"!==e||!1===i){"boolean"==typeof i?this.traceMsgCache[e].succeed=!!i:this.traceMsgCache[e].state=i,this.traceMsgCache[e].duration=Date.now()-this.traceMsgCache[e].start_time,this.traceMsgCache[e].extension.forEach((function(e){delete e.end_time}));var a=this.traceMsgCache[e];if(this.traceMsgCache[e]=null,a.asyncPromiseArray){(t=this.endedAsyncMsgByModule)[e]||(t[e]=[]),this.endedAsyncMsgByModule[e].push(a);var n=function asyncCallback(){r.endedAsyncMsgByModule[e]&&r.endedAsyncMsgByModule[e].includes(a)&&(delete a.asyncPromiseArray,r.report(e,a,{priority:r.getEventPriority(e,a)}))};Promise.all(a.asyncPromiseArray).then(n).catch(n)}else this.report(e,a,{priority:this.getEventPriority(e,a)})}else this.traceMsgCache[e]=null}},{key:"getEventPriority",value:function getEventPriority(e,t){if("exceptions"===e){if(0===t.action)return"HIGH";if(2===t.action)return"HIGH";if(1===t.action&&0!==t.exception_service)return"HIGH"}else{if("msgReceive"===e)return"LOW";if("nim_api_trace"===e)return"LOW"}return"NORMAL"}},{key:"reportTraceCancel",value:function reportTraceCancel(e){this.reportConfig.isDataReportEnable&&(this.endedAsyncMsgByModule[e]=[],this.traceMsgCache[e]=null)}},{key:"pause",value:function pause(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!1)}},{key:"restore",value:function restore(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!0,this.initConfigLoaded||this.initUploadConfig())}},{key:"destroy",value:function destroy(){var e=this;this.reportConfig.isDataReportEnable&&(Object.keys(this.traceMsgCache).forEach((function(t){e.reportTraceEnd(t,1)})),null!==this.timer&&clearTimeout(this.timer),this.setConfig=i,this.report=i,this.reportTraceStart=i,this.reportTraceUpdate=i,this.reportTraceEnd=i,this.pause=i,this.restore=i,this.destroy=i,this.reqRetryCount=0,this.cacheMsgList=[],this.traceMsgCache={},this.lowPriorityMsgList=[],this.msgList=[],this.highPriorityMsgList=[],this.reportConfig={},this.isDestroyed=!0)}},{key:"initUploadConfig",value:function initUploadConfig(){var e,i;return __awaiter(this,void 0,void 0,_regeneratorRuntime().mark((function _callee(){var a,n,o,s,c,l=this;return _regeneratorRuntime().wrap((function _callee$(d){for(;;)switch(d.prev=d.next){case 0:if(!this.loading){d.next=2;break}return d.abrupt("return");case 2:this.loading=!0,a=this.reportConfig.common||{},n=this.reportConfig.compassDataEndpoint.split(",").map((function(e){return"".concat(e,"/").concat(l.configPath)})),o=_regeneratorRuntime().mark((function _loop(o){return _regeneratorRuntime().wrap((function _loop$(s){for(;;)switch(s.prev=s.next){case 0:if(!l.initConfigLoaded&&!l.isDestroyed){s.next=2;break}return s.abrupt("return","break");case 2:return s.prev=2,s.next=5,l.reportConfig.request(n[o],{method:"GET",dataType:"json",params:{deviceId:a.dev_id,sdkVer:a.sdk_ver,platform:a.platform,appkey:a.app_key},timeout:l.reportConfig.timeout}).then((function(e){var t,r;if(!l.isDestroyed){if(200===e.status&&e.data&&200===e.data.code){l.initConfigLoaded=!0;var i=e.data.data||{};l.reportConfig.maxSize=i.maxSize>1e3?1e3:i.maxSize,l.reportConfig.maxInterval=i.maxInterval>1e4?1e4:i.maxInterval,l.reportConfig.maxInterval=i.maxInterval<10?10:i.maxInterval,l.reportConfig.minInterval=i.minInterval<2?2:i.minInterval,l.reportConfig.maxDelay=i.maxDelay||300,l.reportConfig.maxInterval=1e3*l.reportConfig.maxInterval,l.reportConfig.minInterval=1e3*l.reportConfig.minInterval,l.reportConfig.maxDelay=1e3*l.reportConfig.maxDelay,i.endpoint?l.dataReportEndpoint=i.endpoint:l.dataReportEndpoint=n[o],l.serverAllowUpload=!0,l.loading=!1,l.reportHeartBeat()}else 200===e.status&&(l.initConfigLoaded=!0);null===(r=null===(t=l.reportConfig)||void 0===t?void 0:t.logger)||void 0===r||r.log("Get reporter upload config success")}})).catch((function(e){var i,a;l.isDestroyed||(l.loading=!1,null===(a=null===(i=l.reportConfig)||void 0===i?void 0:i.logger)||void 0===a||a.error("Get reporter upload config failed",e),l.reqRetryCount<t&&(l.reqRetryCount++,setTimeout((function(){l.isDestroyed||l.initUploadConfig()}),r)))}));case 5:s.next=14;break;case 7:if(s.prev=7,s.t0=s.catch(2),!l.isDestroyed){s.next=11;break}return s.abrupt("return",{v:void 0});case 11:l.loading=!1,null===(i=null===(e=l.reportConfig)||void 0===e?void 0:e.logger)||void 0===i||i.error("Exec reporter request failed",s.t0),l.reqRetryCount<t&&(l.reqRetryCount++,setTimeout((function(){l.isDestroyed||l.initUploadConfig()}),r));case 14:case"end":return s.stop()}}),_loop,null,[[2,7]])})),s=0;case 7:if(!(s<n.length)){d.next=17;break}return d.delegateYield(o(s),"t0",9);case 9:if("break"!==(c=d.t0)){d.next=12;break}return d.abrupt("break",17);case 12:if("object"!==_typeof(c)){d.next=14;break}return d.abrupt("return",c.v);case 14:s++,d.next=7;break;case 17:case"end":return d.stop()}}),_callee,this)})))}},{key:"reportHeartBeat",value:function reportHeartBeat(){var e=this;this.isDestroyed||(this.timer=setTimeout((function(){e.reportHeartBeat()}),this.reportConfig.minInterval),this.doReport())}},{key:"doReport",value:function doReport(){if(!this.isDestroyed){var e=this.highPriorityMsgList.length+this.msgList.length+this.lowPriorityMsgList.length+this.cacheMsgList.length>2*this.reportConfig.maxSize?this.reportConfig.minInterval:this.reportConfig.maxInterval;Date.now()-this.lastReportTime>=e&&this.upload()}}},{key:"getUploadMsg",value:function getUploadMsg(){var e=this,t={},r=Date.now();this.highPriorityMsgList=this.highPriorityMsgList.filter((function(t){return r-t.createTime<e.reportConfig.maxDelay})),this.msgList=this.msgList.filter((function(t){return r-t.createTime<e.reportConfig.maxDelay})),this.lowPriorityMsgList=this.lowPriorityMsgList.filter((function(t){return r-t.createTime<e.reportConfig.maxDelay})),this.cacheMsgList=this.cacheMsgList.filter((function(t){return r-t.createTime<e.reportConfig.maxDelay}));var i=this.highPriorityMsgList.slice(0,this.reportConfig.maxSize);if(this.highPriorityMsgList=this.highPriorityMsgList.slice(i.length),i.length<this.reportConfig.maxSize){var a=this.reportConfig.maxSize-i.length;i=i.concat(this.msgList.slice(0,a)),this.msgList=this.msgList.slice(a)}if(i.length<this.reportConfig.maxSize){var n=this.reportConfig.maxSize-i.length;i=i.concat(this.lowPriorityMsgList.slice(0,n)),this.lowPriorityMsgList=this.lowPriorityMsgList.slice(n)}if(i.length<this.reportConfig.maxSize){var o=this.reportConfig.maxSize-i.length;i=i.concat(this.cacheMsgList.slice(0,o)),this.cacheMsgList=this.cacheMsgList.slice(o)}return i.forEach((function(e){t[e.module]?t[e.module].push(e.msg):t[e.module]=[e.msg]})),{uploadMsgArr:i,uploadMsg:t}}},{key:"upload",value:function upload(){var e,t,r=this;if(this.isUploadEnable&&this.serverAllowUpload&&!(this.lastReportTime&&Date.now()-this.lastReportTime<this.reportConfig.minInterval)){var i=this.getUploadMsg(),a=i.uploadMsgArr,n=i.uploadMsg;if(a.length){this.lastReportTime=Date.now();try{var o="".concat(this.dataReportEndpoint,"/").concat(this.dataReportPath);this.reportConfig.request(o,{dataType:"json",method:"POST",data:{common:this.reportConfig.common,event:n},headers:{sdktype:"im"},timeout:this.reportConfig.timeout}).catch((function(e){var t,i;r.cacheMsgList=r.cacheMsgList.concat(a).slice(0,r.reportConfig.cacheMaxSize),null===(i=null===(t=r.reportConfig)||void 0===t?void 0:t.logger)||void 0===i||i.warn("Reporter upload failed",e)}))}catch(r){null===(t=null===(e=this.reportConfig)||void 0===e?void 0:e.logger)||void 0===t||t.warn("Exec reporter request failed",r)}clearTimeout(this.timer),this.reportHeartBeat()}}}}]),Reporter}();return a}()}));function __rest(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&t.indexOf(i)<0&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var a=0;for(i=Object.getOwnPropertySymbols(e);a<i.length;a++)t.indexOf(i[a])<0&&Object.prototype.propertyIsEnumerable.call(e,i[a])&&(r[i[a]]=e[i[a]])}return r}function __awaiter(e,t,r,i){return new(r||(r=Promise))((function(a,n){function fulfilled(e){try{step(i.next(e))}catch(e){n(e)}}function rejected(e){try{step(i.throw(e))}catch(e){n(e)}}function step(e){e.done?a(e.value):function adopt(e){return e instanceof r?e:new r((function(t){t(e)}))}(e.value).then(fulfilled,rejected)}step((i=i.apply(e,t||[])).next())}))}!function(e){e[e.V2NIM_DATA_SYNC_TYPE_LEVEL_FULL=0]="V2NIM_DATA_SYNC_TYPE_LEVEL_FULL",e[e.V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC=1]="V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC"}(e||(e={})),function(e){e[e.V2NIM_DATA_SYNC_TYPE_MAIN=1]="V2NIM_DATA_SYNC_TYPE_MAIN",e[e.V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER=2]="V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER",e[e.V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER=3]="V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER"}(t||(t={})),function(e){e[e.V2NIM_DATA_SYNC_STATE_WAITING=1]="V2NIM_DATA_SYNC_STATE_WAITING",e[e.V2NIM_DATA_SYNC_STATE_SYNCING=2]="V2NIM_DATA_SYNC_STATE_SYNCING",e[e.V2NIM_DATA_SYNC_STATE_COMPLETED=3]="V2NIM_DATA_SYNC_STATE_COMPLETED"}(r||(r={})),function(e){e[e.V2NIM_CONVERSATION_TYPE_UNKNOWN=0]="V2NIM_CONVERSATION_TYPE_UNKNOWN",e[e.V2NIM_CONVERSATION_TYPE_P2P=1]="V2NIM_CONVERSATION_TYPE_P2P",e[e.V2NIM_CONVERSATION_TYPE_TEAM=2]="V2NIM_CONVERSATION_TYPE_TEAM",e[e.V2NIM_CONVERSATION_TYPE_SUPER_TEAM=3]="V2NIM_CONVERSATION_TYPE_SUPER_TEAM"}(i||(i={})),function(e){e[e.V2NIM_MESSAGE_STATUS_DEFAULT=0]="V2NIM_MESSAGE_STATUS_DEFAULT",e[e.V2NIM_MESSAGE_STATUS_REVOKE=1]="V2NIM_MESSAGE_STATUS_REVOKE",e[e.V2NIM_MESSAGE_STATUS_BACKFILL=2]="V2NIM_MESSAGE_STATUS_BACKFILL"}(a||(a={})),function(e){e[e.V2NIM_FRIEND_MODE_TYPE_ADD=1]="V2NIM_FRIEND_MODE_TYPE_ADD",e[e.V2NIM_FRIEND_MODE_TYPE_APPLY=2]="V2NIM_FRIEND_MODE_TYPE_APPLY"}(n||(n={})),function(e){e[e.V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED=1]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED",e[e.V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED"}(o||(o={})),function(e){e[e.V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT=0]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT",e[e.V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED=1]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED",e[e.V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED",e[e.V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED=3]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED",e[e.V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD=4]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD"}(s||(s={})),function(e){e[e.V2NIM_FRIEND_DELETION_TYPE_BY_SELF=1]="V2NIM_FRIEND_DELETION_TYPE_BY_SELF",e[e.V2NIM_FRIEND_DELETION_TYPE_BY_PEER=2]="V2NIM_FRIEND_DELETION_TYPE_BY_PEER"}(c||(c={})),function(e){e[e.V2NIM_FRIEND_VERIFY_TYPE_ADD=1]="V2NIM_FRIEND_VERIFY_TYPE_ADD",e[e.V2NIM_FRIEND_VERIFY_TYPE_APPLY=2]="V2NIM_FRIEND_VERIFY_TYPE_APPLY",e[e.V2NIM_FRIEND_VERIFY_TYPE_ACCEPT=3]="V2NIM_FRIEND_VERIFY_TYPE_ACCEPT",e[e.V2NIM_FRIEND_VERIFY_TYPE_REJECT=4]="V2NIM_FRIEND_VERIFY_TYPE_REJECT"}(l||(l={})),function(e){e[e.V2NIM_LOGIN_AUTH_TYPE_DEFAULT=0]="V2NIM_LOGIN_AUTH_TYPE_DEFAULT",e[e.V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN=1]="V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN",e[e.V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY=2]="V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY"}(d||(d={})),function(e){e[e.V2NIM_LOGIN_STATUS_LOGOUT=0]="V2NIM_LOGIN_STATUS_LOGOUT",e[e.V2NIM_LOGIN_STATUS_LOGINED=1]="V2NIM_LOGIN_STATUS_LOGINED",e[e.V2NIM_LOGIN_STATUS_LOGINING=2]="V2NIM_LOGIN_STATUS_LOGINING",e[e.V2NIM_LOGIN_STATUS_UNLOGIN=3]="V2NIM_LOGIN_STATUS_UNLOGIN"}(h||(h={})),function(e){e[e.V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN=0]="V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN",e[e.V2NIM_LOGIN_CLIENT_TYPE_ANDROID=1]="V2NIM_LOGIN_CLIENT_TYPE_ANDROID",e[e.V2NIM_LOGIN_CLIENT_TYPE_IOS=2]="V2NIM_LOGIN_CLIENT_TYPE_IOS",e[e.V2NIM_LOGIN_CLIENT_TYPE_PC=4]="V2NIM_LOGIN_CLIENT_TYPE_PC",e[e.V2NIM_LOGIN_CLIENT_TYPE_WP=8]="V2NIM_LOGIN_CLIENT_TYPE_WP",e[e.V2NIM_LOGIN_CLIENT_TYPE_WEB=16]="V2NIM_LOGIN_CLIENT_TYPE_WEB",e[e.V2NIM_LOGIN_CLIENT_TYPE_RESTFUL=32]="V2NIM_LOGIN_CLIENT_TYPE_RESTFUL",e[e.V2NIM_LOGIN_CLIENT_TYPE_MAC_OS=64]="V2NIM_LOGIN_CLIENT_TYPE_MAC_OS",e[e.V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS=65]="V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS"}(u||(u={})),function(e){e[e.V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE=1]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE",e[e.V2NIM_KICKED_OFFLINE_REASON_SERVER=2]="V2NIM_KICKED_OFFLINE_REASON_SERVER",e[e.V2NIM_KICKED_OFFLINE_REASON_CLIENT=3]="V2NIM_KICKED_OFFLINE_REASON_CLIENT",e[e.V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY=4]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY"}(m||(m={})),function(e){e[e.V2NIM_LOGIN_CLIENT_CHANGE_LIST=1]="V2NIM_LOGIN_CLIENT_CHANGE_LIST",e[e.V2NIM_LOGIN_CLIENT_CHANGE_LOGIN=2]="V2NIM_LOGIN_CLIENT_CHANGE_LOGIN",e[e.V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT=3]="V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT"}(p||(p={})),function(e){e[e.V2NIM_CONNECT_STATUS_DISCONNECTED=0]="V2NIM_CONNECT_STATUS_DISCONNECTED",e[e.V2NIM_CONNECT_STATUS_CONNECTED=1]="V2NIM_CONNECT_STATUS_CONNECTED",e[e.V2NIM_CONNECT_STATUS_CONNECTING=2]="V2NIM_CONNECT_STATUS_CONNECTING",e[e.V2NIM_CONNECT_STATUS_WAITING=3]="V2NIM_CONNECT_STATUS_WAITING"}(g||(g={})),function(e){e[e.NIM_MESSAGE_AI_STREAM_STATUS_STREAMING=-1]="NIM_MESSAGE_AI_STREAM_STATUS_STREAMING",e[e.NIM_MESSAGE_AI_STREAM_STATUS_NONE=0]="NIM_MESSAGE_AI_STREAM_STATUS_NONE",e[e.NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER=1]="NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER",e[e.NIM_MESSAGE_AI_STREAM_STATUS_CANCEL=2]="NIM_MESSAGE_AI_STREAM_STATUS_CANCEL",e[e.NIM_MESSAGE_AI_STREAM_STATUS_UPDATE=3]="NIM_MESSAGE_AI_STREAM_STATUS_UPDATE",e[e.NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE=4]="NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE",e[e.NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION=5]="NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION"}(y||(y={})),function(e){e[e.V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT=0]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT",e[e.V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE=1]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE",e[e.V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE=2]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE"}(_||(_={})),function(e){e[e.V2NIM_MESSAGE_AI_REGEN_OP_UPDATE=1]="V2NIM_MESSAGE_AI_REGEN_OP_UPDATE",e[e.V2NIM_MESSAGE_AI_REGEN_OP_NEW=2]="V2NIM_MESSAGE_AI_REGEN_OP_NEW"}(v||(v={})),function(e){e[e.V2NIM_MESSAGE_AI_STATUS_UNKNOW=0]="V2NIM_MESSAGE_AI_STATUS_UNKNOW",e[e.V2NIM_MESSAGE_AI_STATUS_AT=1]="V2NIM_MESSAGE_AI_STATUS_AT",e[e.V2NIM_MESSAGE_AI_STATUS_RESPONSE=2]="V2NIM_MESSAGE_AI_STATUS_RESPONSE"}(E||(E={})),function(e){e[e.V2NIM_MESSAGE_TYPE_INVALID=-1]="V2NIM_MESSAGE_TYPE_INVALID",e[e.V2NIM_MESSAGE_TYPE_TEXT=0]="V2NIM_MESSAGE_TYPE_TEXT",e[e.V2NIM_MESSAGE_TYPE_IMAGE=1]="V2NIM_MESSAGE_TYPE_IMAGE",e[e.V2NIM_MESSAGE_TYPE_AUDIO=2]="V2NIM_MESSAGE_TYPE_AUDIO",e[e.V2NIM_MESSAGE_TYPE_VIDEO=3]="V2NIM_MESSAGE_TYPE_VIDEO",e[e.V2NIM_MESSAGE_TYPE_LOCATION=4]="V2NIM_MESSAGE_TYPE_LOCATION",e[e.V2NIM_MESSAGE_TYPE_NOTIFICATION=5]="V2NIM_MESSAGE_TYPE_NOTIFICATION",e[e.V2NIM_MESSAGE_TYPE_FILE=6]="V2NIM_MESSAGE_TYPE_FILE",e[e.V2NIM_MESSAGE_TYPE_AVCHAT=7]="V2NIM_MESSAGE_TYPE_AVCHAT",e[e.V2NIM_MESSAGE_TYPE_TIPS=10]="V2NIM_MESSAGE_TYPE_TIPS",e[e.V2NIM_MESSAGE_TYPE_ROBOT=11]="V2NIM_MESSAGE_TYPE_ROBOT",e[e.V2NIM_MESSAGE_TYPE_CALL=12]="V2NIM_MESSAGE_TYPE_CALL",e[e.V2NIM_MESSAGE_TYPE_CUSTOM=100]="V2NIM_MESSAGE_TYPE_CUSTOM"}(I||(I={})),function(e){e[e.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR=0]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR",e[e.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND=1]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND"}(f||(f={})),function(e){e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED=-1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE=0]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK=1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE=2]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO=3]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS=4]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS=5]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER=6]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER=7]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER=8]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT=9]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER=10]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE=401]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK=402]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE=403]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO=404]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS=405]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS=410]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER=406]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER=407]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER=408]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT=411]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT",e[e.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER=409]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER"}(M||(M={})),function(e){e[e.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN=0]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN",e[e.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS=1]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS",e[e.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED=2]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED",e[e.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING=3]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING"}(C||(C={})),function(e){e[e.V2NIM_MESSAGE_SENDING_STATE_UNKNOWN=0]="V2NIM_MESSAGE_SENDING_STATE_UNKNOWN",e[e.V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED=1]="V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED",e[e.V2NIM_MESSAGE_SENDING_STATE_FAILED=2]="V2NIM_MESSAGE_SENDING_STATE_FAILED",e[e.V2NIM_MESSAGE_SENDING_STATE_SENDING=3]="V2NIM_MESSAGE_SENDING_STATE_SENDING"}(T||(T={})),function(e){e[e.V2NIM_QUERY_DIRECTION_DESC=0]="V2NIM_QUERY_DIRECTION_DESC",e[e.V2NIM_QUERY_DIRECTION_ASC=1]="V2NIM_QUERY_DIRECTION_ASC"}(S||(S={})),function(e){e[e.V2NIM_CLEAR_HISTORY_MODE_ALL=0]="V2NIM_CLEAR_HISTORY_MODE_ALL",e[e.V2NIM_CLEAR_HISTORY_MODE_LOCAL=1]="V2NIM_CLEAR_HISTORY_MODE_LOCAL"}(R||(R={})),function(e){e[e.V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED=0]="V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED",e[e.V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY=1]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY",e[e.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY=2]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY",e[e.V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY=3]="V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY",e[e.V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY=4]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY",e[e.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY=5]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY"}(A||(A={})),function(e){e[e.V2NIM_MESSAGE_PIN_STATE_NOT_PINNED=0]="V2NIM_MESSAGE_PIN_STATE_NOT_PINNED",e[e.V2NIM_MESSAGE_PIN_STATE_PINNED=1]="V2NIM_MESSAGE_PIN_STATE_PINNED",e[e.V2NIM_MESSAGE_PIN_STATE_UPDATED=2]="V2NIM_MESSAGE_PIN_STATE_UPDATED"}(N||(N={})),function(e){e[e.V2NIM_QUICK_COMMENT_STATE_ADD=1]="V2NIM_QUICK_COMMENT_STATE_ADD",e[e.V2NIM_QUICK_COMMENT_STATE_REMOVE=2]="V2NIM_QUICK_COMMENT_STATE_REMOVE"}(b||(b={})),function(e){e[e.V2NIM_CLIENT_ANTISPAM_OPERATE_NONE=0]="V2NIM_CLIENT_ANTISPAM_OPERATE_NONE",e[e.V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE=1]="V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE",e[e.V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD=2]="V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD",e[e.V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD=3]="V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD"}(O||(O={})),function(e){e[e.V2NIM_SORT_ORDER_DESC=0]="V2NIM_SORT_ORDER_DESC",e[e.V2NIM_SORT_ORDER_ASC=1]="V2NIM_SORT_ORDER_ASC"}(P||(P={})),function(e){e[e.P2P_DELETE_MSG=7]="P2P_DELETE_MSG",e[e.TEAM_DELETE_MSG=8]="TEAM_DELETE_MSG",e[e.SUPERTEAM_DELETE_MSG=12]="SUPERTEAM_DELETE_MSG",e[e.P2P_ONE_WAY_DELETE_MSG=13]="P2P_ONE_WAY_DELETE_MSG",e[e.TEAM_ONE_WAY_DELETE_MSG=14]="TEAM_ONE_WAY_DELETE_MSG",e[e.CUSTOM_P2P_MSG=100]="CUSTOM_P2P_MSG",e[e.CUSTOM_TEAM_MSG=101]="CUSTOM_TEAM_MSG",e[e.CUSTOM_SUPERTEAM_MSG=103]="CUSTOM_SUPERTEAM_MSG"}(w||(w={})),function(e){e[e.V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF",e[e.V2NIM_TEAM_MESSAGE_MUTE_MODE_ON=1]="V2NIM_TEAM_MESSAGE_MUTE_MODE_ON",e[e.V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON=2]="V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON"}(k||(k={})),function(e){e[e.V2NIM_P2P_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_P2P_MESSAGE_MUTE_MODE_OFF",e[e.V2NIM_P2P_MESSAGE_MUTE_MODE_ON=1]="V2NIM_P2P_MESSAGE_MUTE_MODE_ON"}(q||(q={})),function(e){e[e.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL=0]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL",e[e.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL=1]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL",e[e.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER"}(L||(L={})),function(e){e[e.V2NIM_TEAM_TYPE_INVALID=0]="V2NIM_TEAM_TYPE_INVALID",e[e.V2NIM_TEAM_TYPE_ADVANCED=1]="V2NIM_TEAM_TYPE_ADVANCED",e[e.V2NIM_TEAM_TYPE_SUPER=2]="V2NIM_TEAM_TYPE_SUPER"}(D||(D={})),function(e){e[e.V2NIM_TEAM_JOIN_MODE_FREE=0]="V2NIM_TEAM_JOIN_MODE_FREE",e[e.V2NIM_TEAM_JOIN_MODE_APPLY=1]="V2NIM_TEAM_JOIN_MODE_APPLY",e[e.V2NIM_TEAM_JOIN_MODE_INVITE=2]="V2NIM_TEAM_JOIN_MODE_INVITE"}(V||(V={})),function(e){e[e.V2NIM_TEAM_AGREE_MODE_AUTH=0]="V2NIM_TEAM_AGREE_MODE_AUTH",e[e.V2NIM_TEAM_AGREE_MODE_NO_AUTH=1]="V2NIM_TEAM_AGREE_MODE_NO_AUTH"}(U||(U={})),function(e){e[e.V2NIM_TEAM_INVITE_MODE_MANAGER=0]="V2NIM_TEAM_INVITE_MODE_MANAGER",e[e.V2NIM_TEAM_INVITE_MODE_ALL=1]="V2NIM_TEAM_INVITE_MODE_ALL"}(x||(x={})),function(e){e[e.V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER",e[e.V2NIM_TEAM_UPDATE_INFO_MODE_ALL=1]="V2NIM_TEAM_UPDATE_INFO_MODE_ALL"}(G||(G={})),function(e){e[e.V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN=0]="V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN",e[e.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL=1]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL",e[e.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL=3]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL"}(B||(B={})),function(e){e[e.V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER",e[e.V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL=1]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL"}(F||(F={})),function(e){e[e.V2NIM_TEAM_MEMBER_ROLE_NORMAL=0]="V2NIM_TEAM_MEMBER_ROLE_NORMAL",e[e.V2NIM_TEAM_MEMBER_ROLE_OWNER=1]="V2NIM_TEAM_MEMBER_ROLE_OWNER",e[e.V2NIM_TEAM_MEMBER_ROLE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_MANAGER"}(j||(j={})),function(e){e[e.V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION=0]="V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION",e[e.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION=1]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION",e[e.V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION=2]="V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION",e[e.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION=3]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION"}(Y||(Y={})),function(e){e[e.V2NIM_TEAM_JOIN_ACTION_STATUS_INIT=0]="V2NIM_TEAM_JOIN_ACTION_STATUS_INIT",e[e.V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED=1]="V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED",e[e.V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED=2]="V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED",e[e.V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED=3]="V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED"}(Q||(Q={})),function(e){e[e.teamApply=0]="teamApply",e[e.teamApplyReject=1]="teamApplyReject",e[e.teamInvite=2]="teamInvite",e[e.teamInviteReject=3]="teamInviteReject",e[e.tlistUpdate=4]="tlistUpdate",e[e.superTeamApply=15]="superTeamApply",e[e.superTeamApplyReject=16]="superTeamApplyReject",e[e.superTeamInvite=17]="superTeamInvite",e[e.superTeamInviteReject=18]="superTeamInviteReject"}(W||(W={})),function(e){e[e.V2NIM_AI_MODEL_TYPE_UNKNOW=0]="V2NIM_AI_MODEL_TYPE_UNKNOW",e[e.V2NIM_AI_MODEL_TYPE_QWEN=1]="V2NIM_AI_MODEL_TYPE_QWEN",e[e.V2NIM_AI_MODEL_TYPE_AZURE=2]="V2NIM_AI_MODEL_TYPE_AZURE",e[e.V2NIM_AI_MODEL_TYPE_PRIVATE=3]="V2NIM_AI_MODEL_TYPE_PRIVATE"}(H||(H={})),function(e){e[e.V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE=0]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE",e[e.V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL=2]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL",e[e.V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE=4]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE",e[e.V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION=5]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION"}($||($={})),function(e){e.V2NIM_AI_MODEL_ROLE_TYPE_SYSTEM="system",e.V2NIM_AI_MODEL_ROLE_TYPE_USER="user",e.V2NIM_AI_MODEL_ROLE_TYPE_ASSISTANT="assistant"}(K||(K={})),function(e){e[e.V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN=0]="V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN",e[e.V2NIM_SIGNALLING_EVENT_TYPE_CLOSE=1]="V2NIM_SIGNALLING_EVENT_TYPE_CLOSE",e[e.V2NIM_SIGNALLING_EVENT_TYPE_JOIN=2]="V2NIM_SIGNALLING_EVENT_TYPE_JOIN",e[e.V2NIM_SIGNALLING_EVENT_TYPE_INVITE=3]="V2NIM_SIGNALLING_EVENT_TYPE_INVITE",e[e.V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE=4]="V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE",e[e.V2NIM_SIGNALLING_EVENT_TYPE_REJECT=5]="V2NIM_SIGNALLING_EVENT_TYPE_REJECT",e[e.V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT=6]="V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT",e[e.V2NIM_SIGNALLING_EVENT_TYPE_LEAVE=7]="V2NIM_SIGNALLING_EVENT_TYPE_LEAVE",e[e.V2NIM_SIGNALLING_EVENT_TYPE_CONTROL=8]="V2NIM_SIGNALLING_EVENT_TYPE_CONTROL"}(J||(J={})),function(e){e[e.V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO=1]="V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO",e[e.V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO=2]="V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO",e[e.V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM=3]="V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM"}(z||(z={})),function(e){e[e.V2NIM_USER_STATUS_TYPE_UNKNOWN=0]="V2NIM_USER_STATUS_TYPE_UNKNOWN",e[e.V2NIM_USER_STATUS_TYPE_LOGIN=1]="V2NIM_USER_STATUS_TYPE_LOGIN",e[e.V2NIM_USER_STATUS_TYPE_LOGOUT=2]="V2NIM_USER_STATUS_TYPE_LOGOUT",e[e.V2NIM_USER_STATUS_TYPE_DISCONNECT=3]="V2NIM_USER_STATUS_TYPE_DISCONNECT"}(X||(X={}));var te={V2NIM_ERROR_CODE_UNKNOWN:{code:0,message:"unknown error"},V2NIM_ERROR_CODE_SUCCESS:{code:200,message:"success"},V2NIM_ERROR_CODE_HANDSHAKE:{code:201,message:"handshake error"},V2NIM_ERROR_CODE_REQUEST_TEMPERARY_FORBIDDEN:{code:398,message:"request temprary forbidden"},V2NIM_ERROR_CODE_SERVER_UNIT_ERROR:{code:399,message:"server unit error"},V2NIM_ERROR_CODE_FORBIDDEN:{code:403,message:"forbidden"},V2NIM_ERROR_CODE_NOT_FOUND:{code:404,message:"not found"},V2NIM_ERROR_CODE_PARAMETER_ERROR:{code:414,message:"parameter error"},V2NIM_ERROR_CODE_RATE_LIMIT_REACHED:{code:416,message:"rate limit reached"},V2NIM_ERROR_CODE_MULTI_LOGIN_FORBIDDEN:{code:417,message:"multi login forbidden"},V2NIM_ERROR_CODE_SERVER_INTERNAL_ERROR:{code:500,message:"server internal error"},V2NIM_ERROR_CODE_SERVER_BUSY:{code:503,message:"server busy"},V2NIM_ERROR_CODE_APP_UNREACHABLE:{code:511,message:"app server unreachable"},V2NIM_ERROR_CODE_SERVICE_UNAVAILABLE:{code:514,message:"service unavailable"},V2NIM_ERROR_CODE_PROTOCOL_BLACKHOLE_FILTERED:{code:599,message:"protocol filtered by blackhole rule"},V2NIM_ERROR_CODE_NO_PERMISSION:{code:997,message:"appid has no permission to call the protocol"},V2NIM_ERROR_CODE_UNPACK_ERROR:{code:998,message:"unpack error"},V2NIM_ERROR_CODE_PACK_ERROR:{code:999,message:"pack error"},V2NIM_ERROR_CODE_IM_DISABLED:{code:101301,message:"IM disabled"},V2NIM_ERROR_CODE_SERVICE_ADDRESS_INVALID:{code:101302,message:"service address invalid"},V2NIM_ERROR_CODE_APPKEY_NOT_EXIST:{code:101303,message:"appkey not exist"},V2NIM_ERROR_CODE_BUNDLEID_CHECK_FAILED:{code:101304,message:"bundleid check failed"},V2NIM_ERROR_CODE_APPKEY_BLOCKED:{code:101403,message:"appkey blocked"},V2NIM_ERROR_CODE_INVALID_TOKEN:{code:102302,message:"invalid token"},V2NIM_ERROR_CODE_ROBOT_NOT_ALLOWED:{code:102303,message:"robot not allowed"},V2NIM_ERROR_CODE_ACCOUNT_NOT_EXIST:{code:102404,message:"account not exist"},V2NIM_ERROR_CODE_ACCOUNT_CHAT_BANNED:{code:102421,message:"account chat banned"},V2NIM_ERROR_CODE_ACCOUNT_BANNED:{code:102422,message:"account banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_BLOCK_LIST:{code:102426,message:"account in block list"},V2NIM_ERROR_CODE_USER_PROFILE_NOT_EXIST:{code:103404,message:"user profile not exist"},V2NIM_ERROR_CODE_USER_PROFILE_HIT_ANTISPAM:{code:103451,message:"user profile hit antispam"},V2NIM_ERROR_CODE_PEER_FRIEND_LIMIT:{code:104301,message:"peer friend limit"},V2NIM_ERROR_CODE_FRIEND_APPLICATION_NOT_EXIST:{code:104302,message:"friend application not exist"},V2NIM_ERROR_CODE_FRIEND_NOT_EXIST:{code:104404,message:"friend not exist"},V2NIM_ERROR_CODE_FRIEND_ALREADY_EXIST:{code:104405,message:"friend already exist"},V2NIM_ERROR_CODE_SELF_FRIEND_OPERATION_NOT_ALLOWED:{code:104429,message:"self friend operation not allowed"},V2NIM_ERROR_CODE_FRIEND_LIMIT:{code:104435,message:"friend limit"},V2NIM_ERROR_CODE_FRIEND_OPERATION_RATE_LIMIT:{code:104449,message:"friend operation rate limit"},V2NIM_ERROR_CODE_FRIEND_HIT_ANTISPAM:{code:104451,message:"friend hit antispam"},V2NIM_ERROR_CODE_SELF_MUTE_OPERATION_NOT_ALLOWED:{code:105429,message:"self mute operation not allowed"},V2NIM_ERROR_CODE_MUTE_LIST_LIMIT:{code:105435,message:"mute list limit"},V2NIM_ERROR_CODE_SELF_BLOCK_LIST_OPERATION_NOT_ALLOWED:{code:106429,message:"self block list operation not allowed"},V2NIM_ERROR_CODE_BLOCK_LIST_LIMIT:{code:106435,message:"block list limit"},V2NIM_ERROR_CODE_REVOKE_THIRD_PARTY_MESSAGE_NOT_ALLOWED:{code:107301,message:"revoke third party message not allowed"},V2NIM_ERROR_CODE_SHORT_TO_LONG_URL_FAILED:{code:107307,message:"short to long URL failed"},V2NIM_ERROR_CODE_URL_INVALID:{code:107308,message:"URL invalid"},V2NIM_ERROR_CODE_DURATION_OUT_OF_RANGE:{code:107309,message:"duration out of range"},V2NIM_ERROR_CODE_GET_FILE_META_INFO_FAILED:{code:107310,message:"get file meta info failed"},V2NIM_ERROR_CODE_AUDIO_FILE_SIZE_LIMIT:{code:107311,message:"audio file size limit"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_TIMEOUT:{code:107312,message:"voice to text timeout"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FAILED:{code:107313,message:"voice to text failed"},V2NIM_ERROR_CODE_REVOKE_EXCEED_TIME_LIMIT:{code:107314,message:"revoke message exceed time limit"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_NOT_ALLOWED:{code:107315,message:"revoke specific message not allowed"},V2NIM_ERROR_CODE_FORCE_PUSH_LIST_LIMIT:{code:107316,message:"force push list limit"},V2NIM_ERROR_CODE_TEAM_MESSAGE_RECEIPT_RATE_LIMIT:{code:107317,message:"team message receipt rate limit"},V2NIM_ERROR_CODE_SNAPSHOT_NOT_EXIST:{code:107318,message:"snapshot not exist"},V2NIM_ERROR_CODE_PIN_LIMIT:{code:107319,message:"pin limit"},V2NIM_ERROR_CODE_PIN_NOT_EXIST:{code:107320,message:"pin not exist"},V2NIM_ERROR_CODE_QUICK_COMMENT_LIMIT:{code:107321,message:"quick comment limit"},V2NIM_ERROR_CODE_PIN_ALREADY_EXIST:{code:107322,message:"pin already exist"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FUNCTION_DISABLED:{code:107333,message:"voice to text function disabled"},V2NIM_ERROR_CODE_CLOUD_SEARCH_FUNCTION_DISABLED:{code:107334,message:"cloud search function disabled"},V2NIM_ERROR_CODE_ONE_WAY_DELETE_FUNCTION_DISABLED:{code:107335,message:"one-way delete function disabled"},V2NIM_ERRPR_CODE_ONEWAY_DELETION_NOT_ALLOW_FOR_TARGET_MESSAGES:{code:107338,message:"one-way deletion is not allowed for target messages"},V2NIM_ERRPR_CODE_SENDER_CANNOT_INCLUDED_IN_TARGET_LIST:{code:107339,message:"The message sender cannot be included in the target list"},V2NIM_ERROR_CODE_ROBOT_CANNOT_SEND_TARGET_MESSAGE:{code:107340,message:"Robot can not send target message"},V2NIM_ERROR_CODE_PIN_TARGET_MESSAGE_NOT_ALLOWED:{code:107345,message:"Pin target message is not allowed"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_REPLY:{code:107346,message:"Target message not allowed reply"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_QUICK_COMMENT:{code:107347,message:"Target message not allowed quick comment"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_TO_SELF_NOT_ALLOWED:{code:107429,message:"revoke message to self not allowed"},V2NIM_ERROR_CODE_APP_CHAT_BANNED:{code:107410,message:"app chat banned"},V2NIM_ERROR_CODE_QUICK_COMMENT_FUNCTION_DISABLED:{code:107326,message:"quick comment function disabled"},V2NIM_ERROR_CODE_PIN_FUNCTION_DISABLED:{code:107327,message:"PIN function disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_FUNCTION_DISABLED:{code:107324,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_P2P_READ_RECEIPT_FUNCTION_DISABLED:{code:107325,message:"read receipt for p2p messages function disabled"},V2NIM_ERROR_CODE_RATE_LIMIT_FOR_MESSAGING_REACHED:{code:107323,message:"rate limit for messaging reached"},V2NIM_ERROR_CODE_MESSAGE_HIT_ANTISPAM:{code:107451,message:"message hit antispam"},V2NIM_ERROR_CODE_MESSAGE_NOT_EXIST:{code:107404,message:"message not exist"},V2NIM_ERROR_CODE_UNSENDING_MESSAGE_EXPIRED:{code:107406,message:"unsending message expired"},V2NIM_ERROR_CODE_TEAM_MARK_READ_FAILED:{code:107302,message:"sending message failed for marking message read failed for too many team members"},V2NIM_ERROR_CODE_SENDER_OR_MANAGER_PERMISSION_ONLY_REVOKE:{code:107303,message:"only sender or manager can revoke message"},V2NIM_ERROR_CODE_DELETE_SELF_MESSAGE_NOT_ALLOWED:{code:107328,message:"delete self message not allowed"},V2NIM_ERROR_CODE_NOT_CHATBOT_ACCOUNT:{code:107329,message:"is not chatbot account"},V2NIM_ERROR_CODE_MESSAGE_SENSE_REQUIRED:{code:107330,message:"sender or receiver must sense message"},V2NIM_ERROR_CODE_HIGH_PRIORITY_MESSAGE_RATE_LIMIT:{code:107304,message:"rate limit of high-priority messages exceeded"},ACK_MESSAGE_BE_HIGH_PRIORITY:{code:107305,message:"ack message should be high-priority"},V2NIM_ERROR_CODE_DUPLICATE_CLIENT_MESSAGE_ID:{code:107306,message:"duplicate client message ID"},V2NIM_ERROR_CODE_INVALID_TIME_RANGE:{code:107439,message:"invalid time range"},V2NIM_ERROR_CODE_NOT_ADVANCED_TEAM:{code:108302,message:"not advanced team"},V2NIM_ERROR_CODE_TEAM_MANAGER_LIMIT:{code:108303,message:"team manager limit"},V2NIM_ERROR_CODE_JOINED_TEAM_LIMIT:{code:108305,message:"joined team limit"},V2NIM_ERROR_CODE_TEAM_NORMAL_MEMBER_CHAT_BANNED:{code:108306,message:"team normal member chat banned"},V2NIM_ERROR_CODE_INVITED_ACCOUNT_NOT_FRIEND:{code:108307,message:"invited account not friend"},V2NIM_ERROR_CODE_REJECT_ALL_TEAM_APPLICATIONS:{code:108308,message:"reject all team applications"},V2NIM_ERROR_CODE_TARGETING_MESSAGE_FOR_TEAM_DISABLED:{code:108318,message:"Targeting messages for group chat is disabled"},V2NIM_ERROR_CODE_INCLUSIVE_AS_FALSE_NOT_ALLOWED_FOR_SUPER_TEAM:{code:108319,message:'Setting "inclusive" to false for super teams is not allowed'},V2NIM_ERROR_CODE_CANNOT_MAKE_SUPER_TEAM_MESSAGE_VISIBLE_TO_NEW_MEMBERS:{code:108320,message:"Cannot make super team targeted messages visible to new members"},V2NIM_ERROR_CODE_CANNOT_ALLOW_TARGETED_MESSAGES_INCLUSIVE_TO_NEW_MEMBERS:{code:108321,message:"Cannot allow targeted messages inclusive to new members"},V2NIM_ERROR_CODE_TEAM_NOT_EXIST:{code:108404,message:"team not exist"},V2NIM_ERROR_CODE_TEAM_ALREADY_CHAT_BANNED:{code:108420,message:"team already chat banned"},V2NIM_ERROR_CODE_ALL_TEAM_MEMBER_CHAT_BANNED:{code:108423,message:"all team member chat banned"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT:{code:108434,message:"extended super team limit"},V2NIM_ERROR_CODE_CREATED_TEAM_LIMIT:{code:108435,message:"created team limit"},V2NIM_ERROR_CODE_TEAM_INVITATION_LIMIT:{code:108437,message:"team invitation limit"},V2NIM_ERROR_CODE_TEAM_HIT_ANTISPAM:{code:108451,message:"team hit antispam"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT_NOT_CONFIGURED:{code:108304,message:"extended super team limit not configured"},V2NIM_ERROR_CODE_SUPER_TEAM_SERVICE_DISABLED:{code:108311,message:"super team service disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_RECORD_NOT_FOUND:{code:108301,message:"read receipt record for the team message not found"},V2NIM_ERROR_CODE_NOT_MANAGER:{code:108430,message:"unable to assign owner manager"},V2NIM_ERROR_CODE_ONLINE_MEMBER_COUNT_DISABLED:{code:108406,message:"number of online users service disabled"},V2NIM_ERROR_CODE_TRANSFER_DISABLED:{code:108310,message:"unable to transfer the ownership to owner"},V2NIM_ERROR_CODE_CREATE_TEAM_DISABLED:{code:108309,message:"unable to create team with more than %s people"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_CREATE_FAILED:{code:108313,message:"/ extended super team creation failed，use open api to create the team"},V2NIM_ERROR_CODE_TEAM_MESSAGE_READ_RECEIPT_DISABLED:{code:108312,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_RETRY:{code:108449,message:"an error occurred, try again"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_NOT_TEAM_MEMBER:{code:109301,message:"list of chat banned users contains non team members"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_OPERATOR:{code:109303,message:"list of chat banned users contains the operator"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_TEAM_OWNER:{code:109304,message:"list of chat banned users contains the team owner"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_MANAGER_NOT_ALLOWED:{code:109305,message:"operation on team manager not allowed"},V2NIM_ERROR_CODE_NO_TEAM_INVITE_PERMISSION:{code:109306,message:"no team invite permission"},V2NIM_ERROR_CODE_TEAM_OWNER_QUIT_NOT_ALLOWED:{code:109307,message:"team owner quit not allowed"},V2NIM_ERROR_CODE_TEAM_OWNER_IN_KICK_LIST:{code:109308,message:"list of kicked user contains the team owner"},V2NIM_ERROR_CODE_INVITE_ROBOT_ACCOUNT_NOT_ALLOWED:{code:109309,message:"invite robot account not allowed"},V2NIM_ERROR_CODE_KICK_OPERATOR_NOT_ALLOWED:{code:109310,message:"kick operator not allowed"},V2NIM_ERROR_CODE_TEAM_MEMBER_ALREADY_EXIST:{code:109311,message:"team member already exist"},V2NIM_ERROR_CODE_TEAM_INVITATION_OR_APPLICATION_NOT_EXIST:{code:109313,message:"team invitation or application not exist"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_OWNER_NOT_ALLOWED:{code:109314,message:"operation on team owner not allowed"},V2NIM_ERROR_CODE_FORCED_PUSH_LIST_INCLUDES_NON_TARGETED_ACCOUNTS:{code:109318,message:"The forced push list includes non-targeted accounts"},V2NIM_ERROR_CODE_TEAM_MEMBER_NOT_EXIST:{code:109404,message:"team member not exist"},V2NIM_ERROR_CODE_TEAM_MEMBER_CHAT_BANNED:{code:109424,message:"team member chat banned"},V2NIM_ERROR_CODE_TEAM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:109427,message:"team owner operation permission required"},V2NIM_ERROR_CODE_TEAM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:109432,message:"team owner or manager operation permission required"},V2NIM_ERROR_CODE_TEAM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:109449,message:"team member concurrent operation failed"},V2NIM_ERROR_CODE_TEAM_MEMBER_HIT_ANTISPAM:{code:109451,message:"team member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_AND_ACCOUNT_MISMATCH:{code:110302,message:"conversation and account mismatch"},V2NIM_ERROR_CODE_CONVERSATION_STICK_TOP_LIMIT:{code:110303,message:"conversation stick top limit"},V2NIM_ERROR_CODE_CONVERSATION_BELONGED_GROUP_LIMIT:{code:110304,message:"conversation belonged group limit"},V2NIM_ERROR_CODE_CONVERSATION_IS_NOT_STICK_TOP:{code:110305,message:"conversation is not stick top"},V2NIM_ERROR_CODE_STICK_TOP_DISABLED:{code:110306,message:"conversation stick top disabled"},V2NIM_ERROR_CODE_CONVERSATION_NOT_EXIST:{code:110404,message:"conversation not exist"},V2NIM_ERROR_CODE_CHATROOM_LINK_UNAVAILABLE:{code:113304,message:"chatroom link unavailable"},V2NIM_ERROR_CODE_IM_CONNECTION_ABNORMAL:{code:113305,message:"IM connection abnormal"},V2NIM_ERROR_CODE_CHATROOM_NOT_EXIST:{code:113404,message:"chatroom not exist"},V2NIM_ERROR_CODE_CHATROOM_CLOSED:{code:113406,message:"chatroom closed"},V2NIM_ERROR_CODE_CHATROOM_REPEATED_OPERATION:{code:113409,message:"chatroom repeated operation"},V2NIM_ERROR_CODE_CHATROOM_DISABLED:{code:113410,message:"chatroom disabled"},V2NIM_ERROR_CODE_ALL_CHATROOM_MEMBER_CHAT_BANNED:{code:113423,message:"all chatroom member chat banned"},V2NIM_ERROR_CODE_CHATROOM_HIT_ANTISPAM:{code:113451,message:"chatroom hit antispam"},V2NIM_ERROR_CODE_ANONYMOUS_MEMBER_FORBIDDEN:{code:114303,message:"anonymous member forbidden"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_NOT_EXIST:{code:114404,message:"chatroom member not exist"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_REPEATED_OPERATION:{code:114405,message:"chatroom member repeated operation"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CHAT_BANNED:{code:114421,message:"chatroom member chat banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_CHATROOM_BLOCK_LIST:{code:114426,message:"account in chatroom block list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:114427,message:"chatroom owner operation permission required"},V2NIM_ERROR_CODE_SELF_IN_CHATROOM_MEMBER_OPERATION_LIST:{code:114429,message:"self in chatroom member operation list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:114432,message:"chatroom owner or manager operation permission required"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_LIMIT:{code:114437,message:"chatroom member limit"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:114449,message:"chatroom member concurrent operation failed"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_HIT_ANTISPAM:{code:114451,message:"chatroom member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_NOT_EXIST:{code:116404,message:"conversation group not exist"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_LIMIT:{code:116435,message:"conversation group limit"},V2NIM_ERROR_CODE_CONVERSATIONS_IN_GROUP_LIMIT:{code:116437,message:"conversations in group limit"},V2NIM_ERROR_CODE_COLLECTION_LIMIT:{code:189301,message:"collection limit"},V2NIM_ERROR_CODE_COLLECTION_NOT_EXIST:{code:189302,message:"collection not exist"},V2NIM_ERROR_CODE_COLLECTION_CONCURRENT_OPERATION_FAILED:{code:189449,message:"collection concurrent operation failed"},V2NIM_ERROR_CODE_INTERNAL:{code:190001,message:"internal error"},V2NIM_ERROR_CODE_ILLEGAL_STATE:{code:190002,message:"illegal state"},V2NIM_ERROR_CODE_MISUSE:{code:191001,message:"misuse"},V2NIM_ERROR_CODE_CANCELLED:{code:191002,message:"operation cancelled"},V2NIM_ERROR_CODE_CALLBACK_FAILED:{code:191003,message:"callback failed"},V2NIM_ERROR_CODE_INVALID_PARAMETER:{code:191004,message:"invalid parameter"},V2NIM_ERROR_CODE_TIMEOUT:{code:191005,message:"timeout"},V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST:{code:191006,message:"resource not exist"},V2NIM_ERROR_CODE_RESOURCE_ALREADY_EXIST:{code:191007,message:"resource already exist"},V2NIM_ERROR_CODE_CONNECT_FAILED:{code:192001,message:"connect failed"},V2NIM_ERROR_CODE_CONNECT_TIMEOUT:{code:192002,message:"connect timeout"},V2NIM_ERROR_CODE_DISCONNECT:{code:192003,message:"disconnect"},V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT:{code:192004,message:"protocol timeout"},V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED:{code:192005,message:"protocol send failed"},V2NIM_ERROR_CODE_REQUEST_FAILED:{code:192006,message:"request failed"},V2NIM_ERROR_CODE_FILE_NOT_FOUND:{code:194001,message:"file not found"},V2NIM_ERROR_CODE_FILE_CREATE_FAILED:{code:194002,message:"file create failed"},V2NIM_ERROR_CODE_FILE_OPEN_FAILED:{code:194003,message:"file open failed"},V2NIM_ERROR_CODE_FILE_WRITE_FAILED:{code:194004,message:"file write failed"},V2NIM_ERROR_CODE_FILE_READ_FAILED:{code:194005,message:"file read failed"},V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:{code:194006,message:"file upload failed"},V2NIM_ERROR_CODE_FILE_DOWNLOAD_FAILED:{code:194007,message:"file download failed"},V2NIM_ERROR_CODE_CLIENT_ANTISPAM:{code:195001,message:"client anti-spam"},V2NIM_ERROR_CODE_SERVER_ANTISPAM:{code:195002,message:"server anti-spam"},V2NIM_ERROR_CODE_STREAM_OUTPUT_STOPPED:{code:189318,message:"Streaming text response stopped"},V2NIM_ERROR_CODE_STREAM_OUTPUT_GENERATED:{code:189319,message:"Streaming text response generated"},V2NIM_ERROR_CODE_STREAM_OUTPUT_ABORTED:{code:189320,message:"Streaming text response aborted due to exception"},V2NIM_ERROR_CODE_INTERRUPTION_REJECTED:{code:189321,message:"Non-streaming messages cannot be interrupted"}},re=Object.keys(te),ie=re.reduce((function(e,t){var r=te[t];return e[t]=r.code,e}),{}),ae=re.reduce((function(e,t){var r=te[t];return e[r.code]=r.message,e}),{});class V2NIMErrorImpl extends Error{constructor(e){super(e.desc),this.name="V2NIMError",this.code=e.code||0,this.desc=e.desc||ae[this.code]||ne[this.code]||"",this.message=this.desc,this.detail=e.detail||{}}toString(){var e,t=`${this.name}\n code: ${this.code}\n message: "${this.message}"\n detail: ${this.detail?JSON.stringify(this.detail):""}`;return(null===(e=null==this?void 0:this.detail)||void 0===e?void 0:e.rawError)&&(t+=`\n rawError: ${this.detail.rawError.message}`),t}}class ValidateError extends V2NIMErrorImpl{constructor(e,t={},r){super({code:ie.V2NIM_ERROR_CODE_PARAMETER_ERROR,detail:{reason:e,rules:r,data:t}}),this.name="validateError",this.message=this.message+"\n"+JSON.stringify(this.detail,null,2),this.data=t,this.rules=r}}class ValidateErrorV2 extends V2NIMErrorImpl{constructor(e){var t,r,i;super({code:ie.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:null===(t=e.detail)||void 0===t?void 0:t.reason,rules:null===(r=e.detail)||void 0===r?void 0:r.rules,data:null===(i=e.detail)||void 0===i?void 0:i.data}}),this.name="ValidateErrorV2"}}class FormatError extends V2NIMErrorImpl{constructor(e,t,r){super({code:ie.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:e,key:t,rules:r}}),this.name="formatError"}}class UploadError extends V2NIMErrorImpl{constructor(e){super(Object.assign({code:400},e)),this.desc=this.desc||"upload file error",this.message=this.desc,this.name="uploadError"}}class CustomError extends V2NIMErrorImpl{constructor(e,t={},r=400){super({code:r,desc:e,detail:t}),this.name="customError",this.data=t}}var ne={200:null,406:null,808:null,810:null,302:"The user name or password is incorrect.",405:"Parameter length too long",408:"Client request timed out",415:"Client network unavailable",422:"Account disabled",508:"Expiration date",509:"Invalid",7101:"Be pulled black",700:"Partial failure of batch operation",801:"The number of people in the team has reached the upper limit",802:"No permission",803:"The team does not exist or has not changed",804:"The user is not in the team",805:"Team type mismatch",806:"The number of teams created has reached the limit",807:"Team member not valid",809:"Already in the team",811:"The number of accounts in the forced push list exceeds the limit",812:"The team is muted",813:"Due to the limited number of team, some pull people successfully",814:"Disable team message read service",815:"Maximum number of team administrators",816:"Batch operation partial failure",9102:"Channel failure",9103:"This call has been answered / rejected at another end",10201:"Signaling: target NIM client is offline",10202:"Signaling: push is unreachable",10404:"Signaling: channel not exists",10405:"Signaling: channel already exists",10406:"Signaling: member of channel not exists",10407:"Signaling: member of channel already exists",10408:"Signaling: the invitation request does not exist or has expired",10409:"Signaling: the invitation request has been rejected",10410:"Signaling: the invitation request has been accepted",10414:"Signaling: request parameter error",10417:"Signaling: uid conflict",10419:"Signaling: the number of members of channel exceed the limit",10420:"Signaling: member is already in the channel on other client",10700:"Signaling: phased success",13002:"Abnormal chatroom status",13003:"In the blacklist",13004:"In the mute list",13006:"All members are muted, and only the administrator can speak"};function get(e,t){if("object"!=typeof e||null===e)return e;for(var r=(t=t||"").split("."),i=0;i<r.length;i++){var a=r[i],n=e[a],o=a.indexOf("["),s=a.indexOf("]");if(-1!==o&&-1!==s&&o<s){var c=a.slice(0,o),l=parseInt(a.slice(o+1,s));n=e[c],n=Array.isArray(n)?n[l]:void 0}if(null==n)return n;e=n}return e}var oe,se=(oe=function(){return(65536*(1+Math.random())|0).toString(16).substring(1)},function(){return oe()+oe()+oe()+oe()+oe()+oe()+oe()+oe()});function getEnumKeys(e){return Object.keys(e).filter((e=>!(+e>=0)))}function getEnumKeyByEnumValue(e,t){var r=Object.keys(e).filter((r=>e[r]==t));return r.length>0?r[0]:void 0}function assignOptions(e,t){return function assignWith(e,t,r,i){for(var a in e=e||{},r=r||{},i=i||(()=>{}),t=t||{}){var n=i(e[a],t[a]);e[a]=void 0===n?t[a]:n}for(var o in r){var s=i(e[o],r[o]);e[o]=void 0===s?r[o]:s}return e}({},e,t,(function(e,t){return void 0===t?e:t}))}function emptyFuncWithPromise(){return Promise.resolve()}var ce,le,de={file:{md5:"$(Etag)",size:"$(ObjectSize)"},image:{md5:"$(Etag)",size:"$(ObjectSize)",w:"$(ImageInfo.Width)",h:"$(ImageInfo.Height)",orientation:"$(ImageInfo.Orientation)"},audio:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Audio.Duration)"},video:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Video.Duration)",w:"$(AVinfo.Video.Width)",h:"$(AVinfo.Video.Height)"}},he={accessKeyId:"",secretAccessKey:"",sessionToken:"",region:"",maxRetries:0,bucket:"",objectName:"",token:"",shortUrl:""};function getUploadResponseFormat(e="file"){var t=de[e]||{};return JSON.stringify(t).replace(/"/gi,'\\"')}!function(e){e[e.nos=1]="nos",e[e.s3=2]="s3"}(ce||(ce={})),function(e){e[e.dontNeed=-1]="dontNeed",e[e.time=2]="time",e[e.urls=3]="urls"}(le||(le={}));var ue={chunkUploadHost:"https://wannos-web.127.net",chunkUploadHostBackupList:["https://fileup.chatnos.com","https://oss.chatnos.com"],commonUploadHost:"https://fileup.chatnos.com",commonUploadHostBackupList:["https://oss.chatnos.com"],chunkMaxSize:4194304e4,commonMaxSize:104857600,uploadReplaceFormat:"https://{host}/{object}",cdn:{defaultCdnDomain:"nim-nosdn.netease.im",cdnDomain:"",bucket:"",objectNamePrefix:""},downloadUrl:"https://{bucket}-nosdn.netease.im/{object}",downloadHostList:["nos.netease.com"],nosCdnEnable:!0,isNeedToGetUploadPolicyFromServer:!0};function isPlainObject(e){return null!=e&&"object"==typeof e&&Object.getPrototypeOf(e)==Object.prototype}function merge(e,t){var r=isPlainObject(e)||Array.isArray(e),i=isPlainObject(t)||Array.isArray(t);if(r&&i){for(var a in t){var n=merge(e[a],t[a]);void 0!==n&&(e[a]=n)}return e}return t}var me={getNetworkStatus:()=>Promise.resolve({net_type:0,net_connect:!0}),onNetworkStatusChange(e){},offNetworkStatusChange(){}};var pe={setLogger:function(e){throw new Error("setLogger not implemented.")},platform:"",WebSocket:class AdapterSocket{constructor(e,t){throw this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this.binaryType="",new Error("Method not implemented.")}close(e,t){throw new Error("Method not implemented.")}send(e){throw new Error("Method not implemented.")}onclose(e){throw new Error("Method not implemented.")}onerror(e){throw new Error("Method not implemented.")}onmessage(e){throw new Error("Method not implemented.")}onopen(e){throw new Error("Method not implemented.")}},localStorage:{},request:function(e,t){throw new Error("request not implemented.")},uploadFile:function(e){throw new Error("uploadFile not implemented.")},getSystemInfo:function(){throw new Error("getSystemInfo not implemented.")},getFileUploadInformation(e){throw new Error("getFileUploadInformation not implemented.")},envPayload:{},net:me,logStorage:class AdapterLogStorageImpl{constructor(e){}open(){return Promise.resolve()}close(){}addLogs(e){return Promise.resolve()}extractLogs(){return Promise.resolve()}}};function pickBy(e,t){e=e||{},t=t||(()=>!0);var r={};for(var i in e)t(e[i])&&(r[i]=e[i]);return r}class NOS{constructor(e,t){this.nosCdnHostTimer=0,this.nosErrorCount=0,this.core=e,this.cloudStorage=t}get config(){return this.cloudStorage.config}reset(){this.nosErrorCount=0}getNosAccessToken(e){return __awaiter(this,void 0,void 0,(function*(){var t=get(yield this.core.sendCmd("getNosAccessToken",{tag:e}),"content.nosAccessTokenTag.token"),r=e.url;return{token:t,url:-1!==r.indexOf("?")?r+"&token="+t:r+"?token="+t}}))}deleteNosAccessToken(e){return __awaiter(this,void 0,void 0,(function*(){yield this.core.sendCmd("deleteNosAccessToken",{tag:e})}))}nosUpload(e,t){var r,i,a,n,o,s,c,l;return __awaiter(this,void 0,void 0,(function*(){var d=get(this.core,"config.cdn.bucket"),h={tag:e.nosScenes||d||"nim"};e.nosSurvivalTime&&(h.expireSec=e.nosSurvivalTime);var u,m=this.core.adapters.getFileUploadInformation(e);if(!t&&!m)try{u=yield this.core.sendCmd("getNosToken",{responseBody:getUploadResponseFormat(e.type),nosToken:h})}catch(e){if(this.core.logger.error("uploadFile:: getNosToken error",e),e instanceof V2NIMErrorImpl)throw e;throw new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getNosToken error",rawError:e,curProvider:1}})}var p=this.config.uploadReplaceFormat.replace("{host}",this.config.cdn.cdnDomain||this.config.cdn.defaultCdnDomain).replace("{object}",m?null===(r=m.uploadInfo)||void 0===r?void 0:r.objectName:t?null==t?void 0:t.objectName:u.content.nosToken.objectName),g="";t&&t.shortUrl&&(g=t.shortUrl),(null===(n=null===(a=null===(i=null==m?void 0:m.uploadInfo)||void 0===i?void 0:i.payload)||void 0===a?void 0:a.mixStoreToken)||void 0===n?void 0:n.shortUrl)&&(g=m.uploadInfo.payload.mixStoreToken.shortUrl);var y,_=g||p;try{var v=m?{token:null===(o=null==m?void 0:m.uploadInfo)||void 0===o?void 0:o.token,bucket:null===(s=null==m?void 0:m.uploadInfo)||void 0===s?void 0:s.bucketName,objectName:null===(c=null==m?void 0:m.uploadInfo)||void 0===c?void 0:c.objectName}:t||u.content.nosToken;this.core.logger.log("uploadFile:: uploadFile params",{nosToken:v,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,platform:pe.platform});var E="BROWSER"===pe.platform?this.config.chunkUploadHost:`${this.config.commonUploadHost}/${v&&v.bucket}`;this.core.reporterHookCloudStorage.update({remote_addr:E,operation_type:t?2:0}),y=yield this.core.adapters.uploadFile(Object.assign(Object.assign(Object.assign({},e),{nosToken:v,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,maxSize:e.maxSize||this.config.chunkMaxSize}),t?{payload:{mixStoreToken:t}}:{}))}catch(r){this.core.logger.error("uploadFile::nos uploadFile error:",r);var I="v2"===get(this.core,"options.apiVersion");if(r.code===ie.V2NIM_ERROR_CODE_CANCELLED||10499===r.errCode)throw new UploadError({code:I?ie.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:get(r,"message")||"Request abort",rawError:r,curProvider:1}});if(I&&r.errCode===ie.V2NIM_ERROR_CODE_FILE_OPEN_FAILED)throw new V2NIMErrorImpl({code:ie.V2NIM_ERROR_CODE_FILE_OPEN_FAILED,detail:{reason:get(r,"message")||"Read file failed",rawError:r,curProvider:1}});var{net_connect:f}=yield pe.net.getNetworkStatus();if(!1===f)throw new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:r,curProvider:1}});if(t){if(this.nosErrorCount<=0){try{this.cloudStorage.mixStorage._addCircuitTimer()}catch(t){throw new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:t,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.cloudStorage.mixStorage.mixStorePolicy,file:e.file||e.filePath}})}return this.nosErrorCount=get(this.cloudStorage,"mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry"),this.cloudStorage._uploadFile(e)}return this.nosErrorCount--,this.nosUpload(e,t)}throw new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"NOS attempts failed",rawError:r,curProvider:1}})}var M=null==y?void 0:y.type,C=M&&M.indexOf("/")>-1?M.slice(0,M.indexOf("/")):"";C||(C=e.type||"");var T,S={image:"imageInfo",video:"vinfo",audio:"vinfo"};if(!S[C])return Object.assign({url:_},y);try{T=yield this.core.adapters.request(`${p}?${S[C]}`,{method:"GET",dataType:"json",timeout:5e3},{exception_service:3})}catch(e){return this.core.logger.error("uploadFile:: fetch file info error",e),Object.assign({url:_},y)}if(T){var{data:R}=T,A="imageInfo"===S[C]?R:null===(l=null==R?void 0:R.GetVideoInfo)||void 0===l?void 0:l.VideoInfo;return pickBy({url:_,name:y.name,size:y.size,ext:y.ext,w:null==A?void 0:A.Width,h:null==A?void 0:A.Height,orientation:null==A?void 0:A.Orientation,dur:null==A?void 0:A.Duration,audioCodec:null==A?void 0:A.AudioCodec,videoCodec:null==A?void 0:A.VideoCodec,container:null==A?void 0:A.Container},(function(e){return void 0!==e}))}return Object.assign({url:_},y)}))}_getNosCdnHost(){var e;return __awaiter(this,void 0,void 0,(function*(){var t;try{t=yield this.core.sendCmd("getNosCdnHost")}catch(e){return void this.core.logger.error("getNosCdnHost::error",e)}if(t){var r=null===(e=null==t?void 0:t.content)||void 0===e?void 0:e.nosConfigTag,i=parseInt(null==r?void 0:r.expire);0!==i&&r.cdnDomain?-1===i?(this.config.cdn.bucket=r.bucket,this.config.cdn.cdnDomain=r.cdnDomain,this.config.cdn.objectNamePrefix=r.objectNamePrefix):(this.config.cdn.bucket=r.bucket,this.config.cdn.cdnDomain=r.cdnDomain,this.config.cdn.objectNamePrefix=r.objectNamePrefix,this.nosCdnHostTimer=this.core.timerManager.addTimer((()=>{this._getNosCdnHost()}),1e3*i)):(this.config.cdn.bucket="",this.config.cdn.cdnDomain="",this.config.cdn.objectNamePrefix="")}}))}}function difference(e,t){return t=t||[],(e=e||[]).filter((e=>-1===t.indexOf(e)))}function replacer(e,t){return t instanceof RegExp?"__REGEXP "+t.toString():t}function validate(e,t={},r,i=!1){var a={};return Object.keys(e).forEach((n=>{var o=e[n].type,s=r?`In ${r}, `:"";if(null==t){var c=`${s}param is null or undefined`;throw i?new ValidateErrorV2({detail:{reason:c,data:{key:n},rules:"required"}}):new ValidateError(c,{key:n},"required")}if(void 0===t[n]){if(!1===e[n].required)return void(a[n]=t[n]);var l=`${s}param '${n}' is required`;throw i?new ValidateErrorV2({detail:{reason:l,data:{key:n},rules:"required"}}):new ValidateError(l,{key:n},"required")}var d=ge[o];if(d&&!d(t,n,e[n],i)){var h=`${s}param '${n}' unexpected`,u={key:n,value:t[n]};throw i?new ValidateErrorV2({detail:{reason:h,data:u,rules:JSON.stringify(e[n],replacer)}}):new ValidateError(h,u,JSON.stringify(e[n],replacer))}a[n]=t[n]})),a}var ge={string:function(e,t,r){var{allowEmpty:i,max:a,min:n,regExp:o}=r,s=e[t];return"string"==typeof s&&((!1!==i||""!==s)&&(!("number"==typeof a&&s.length>a)&&(!("number"==typeof n&&s.length<n)&&!(function isRegExp(e){return"[object RegExp]"===Object.prototype.toString.call(e)}(o)&&!o.test(s)))))},number:function(e,t,r){var{min:i,max:a}=r,n=e[t];return"number"==typeof n&&(!("number"==typeof i&&n<i)&&!("number"==typeof a&&n>a))},boolean:function(e,t){return"boolean"==typeof e[t]},file:function(e,t){return!0},enum:function(e,t,r){var{values:i}=r,a=e[t];return!i||i.indexOf(a)>-1},jsonstr:function(e,t){try{var r=JSON.parse(e[t]);return"object"==typeof r&&null!==r}catch(e){return!1}},func:function(e,t){return"function"==typeof e[t]},array:function(e,t,r,i=!1){var{itemType:a,itemRules:n,rules:o,min:s,max:c,values:l}=r,d=e[t];if(!Array.isArray(d))return!1;if("number"==typeof c&&d.length>c)return!1;if("number"==typeof s&&d.length<s)return!1;if(n)d.forEach(((e,r)=>{validate({[r]:n},{[r]:e},`${t}[${r}]`,i)}));else if(o)d.forEach(((e,r)=>validate(o,e,`${t}[${r}]`,i)));else if("enum"===a){if(l&&difference(d,l).length)return!1}else if(a&&!d.every((e=>typeof e===a)))return!1;return!0},object:function(e,t,r,i=!1){var{rules:a,allowEmpty:n}=r,o=e[t];if("object"!=typeof o||null===o)return!1;if(a){var s=Object.keys(a),c=Object.keys(o).filter((e=>s.indexOf(e)>-1));if(!1===n&&0===c.length)return!1;validate(a,o,t,i)}return!0}};class PromiseManager{constructor(){this.abortFns=[]}add(e){var t=function getPromiseWithAbort(e){var t={},r=new Promise((function(e,r){t.abort=r}));return t.promise=Promise.race([e,r]),t}(e);return this.abortFns.push(t.abort),t.promise}clear(e){this.abortFns.forEach((t=>t(e||new V2NIMErrorImpl({code:ie.V2NIM_ERROR_CODE_CANCELLED,detail:{reason:"Aborted"}})))),this.abortFns=[]}destroy(){this.clear()}}var ye={tolerantRTT:3e3,bestRTT:100,maxChances:5,enable:!0},_e={timestamp:0,rtt:0,baseClock:0,baseTime:0};class TimeOrigin{constructor(e,t,r="getServerTime"){this.serverOrigin=_e,this.config=ye,this.isSettingNTP=!1,this.currentChance=0,this.failedDelay=2e3,this.successDelay=3e5,this.timer=0,this.cmdName="getServerTime",this.core=e,this.logger=e.logger,this.promiseManager=new PromiseManager,this.cmdName=r,t&&this.setOptions(t)}setOptions(e){this.config=Object.assign({},ye,this.config,e)}reset(){this.timer&&clearTimeout(this.timer),this.promiseManager.clear(),this.serverOrigin=_e,this.currentChance=0}setOriginTimetick(){return __awaiter(this,void 0,void 0,(function*(){if(this.config.enable&&!(this.isSettingNTP||this.currentChance>=this.config.maxChances)){var e=get(this.core,"auth.status"),t=get(this.core,"status"),r=get(this.core,"V2NIMLoginService.lifeCycle.loginStatus");if("logined"===e||"logined"===t||1===r){this.isSettingNTP=!0,this.currentChance++,this.timer&&clearTimeout(this.timer),this.timer=0;var i,a="TimeOrigin::setOriginTimetick:",n=Date.now();this.core.logger.debug(`${a} getServerTime start, times ${this.currentChance}`);try{i=get(yield this.promiseManager.add(this.core.sendCmd(this.cmdName)),"content.time"),this.isSettingNTP=!1}catch(e){var o=e;return this.isSettingNTP=!1,this.logger.warn(`${a} Calculate Delay time, getServerTime error`,o),void(o.code!==ie.V2NIM_ERROR_CODE_CANCELLED&&(clearTimeout(this.timer),this.timer=setTimeout(this.setOriginTimetick.bind(this),this.failedDelay)))}if(!i)return this.core.logger.warn(`${a} Calculate Delay time incorrect format`),void(this.config.enable=!1);var s=Date.now()-n;this.doSet(i,s)}}}))}doSet(e,t){var r="TimeOrigin::setOriginTimetick:";t>this.config.tolerantRTT?(this.logger.warn(`${r} denied RTT:${t}`),clearTimeout(this.timer),this.timer=setTimeout(this.setOriginTimetick.bind(this),this.failedDelay)):t>this.config.bestRTT?(this.serverOrigin.rtt&&t>=this.serverOrigin.rtt?this.logger.warn(`${r} ignore RTT:${t}`):(this.setServerOrigin(t,e),this.logger.log(`${r} accept reluctantly RTT:${t}`)),clearTimeout(this.timer),this.timer=setTimeout(this.setOriginTimetick.bind(this),this.failedDelay)):(this.setServerOrigin(t,e),this.logger.debug(`${r} accept best RTT:${t}`),this.currentChance=0,clearTimeout(this.timer),this.timer=setTimeout(this.setOriginTimetick.bind(this),this.successDelay))}getNTPTime(e){if(void 0===e&&(e=this.getTimeNode()),this.checkNodeReliable(e)){var t=Math.floor(e.time-this.serverOrigin.baseTime);return this.serverOrigin.timestamp+t}return Date.now()}checkNodeReliable(e){if(void 0===e&&(e=this.getTimeNode()),this.serverOrigin.timestamp){if(0===this.serverOrigin.baseClock)return!0;var t=e.clock-this.serverOrigin.baseClock,r=e.time-this.serverOrigin.baseTime;return Math.abs(r-t)<500}return!1}checkPerformance(){return"BROWSER"===pe.platform&&!("undefined"==typeof performance||!performance.now)}static checkPerformance(){return"BROWSER"===pe.platform&&!("undefined"==typeof performance||!performance.now)}getTimeNode(){return{clock:this.checkPerformance()?performance.now():0,time:Date.now()}}static getTimeNode(){return{clock:TimeOrigin.checkPerformance()?performance.now():0,time:Date.now()}}setServerOrigin(e,t){this.serverOrigin={timestamp:t+Math.floor(e/2),rtt:e,baseClock:this.checkPerformance()?performance.now():0,baseTime:Date.now()}}}var ve={},Ee={};function parseEachCmd(e,t,r,i,a){var n,o={cmd:r,raw:e,error:null,service:null==t?void 0:t.service,content:{},__receiveTimeNode:TimeOrigin.getTimeNode()};if(!r||!t)return o.notFound=!0,o;(18===t.sid||t.sid>=26&&t.sid<100)&&(e.code=function toReadableCode(e){if("number"!=typeof e||e!=e)throw new V2NIMErrorImpl({code:ie.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"Read code failed",rawData:`${e}`}});if(e<0||e>=0&&e<1e3||e>=2e4&&e<=20099)return e;var t=(65535&e)>>9;t-=t<=38?1:2;return 1e5+1e3*t+(511&e)}(e.code));var s=function genCmdError(e,t){var r=ae[e],i=ne[e];return null===i?null:new V2NIMErrorImpl({code:e,desc:r||i||e,detail:{cmd:t,timetag:Date.now()}})}(e.code,r);if(o.error=s,o.error){if(o.error.detail.cmd=r,!(null===(n=null==t?void 0:t.ignoreErrCodes)||void 0===n?void 0:n.includes(e.code)))return o;a.warn("parseCmd:: ignore error ",o.error),o.error.detail.ignore=!0}return t.response&&t.response.forEach(((e,t)=>{var r=i[t],a=e.type,n=e.name,s=e.reflectMapper;if(void 0!==r)switch(a){case"Property":o.content[n]=s?deserialize(r,s):r;break;case"PropertyArray":o.content[n]=r.map((e=>s?deserialize(e,s):e));break;case"Int":case"Long":case"Byte":o.content[n]=+r;break;case"Bool":o.content[n]="true"===r||!0===r||1===r;break;default:o.content[n]=r}})),o}function serialize(e,t,r){var i={};for(var a in e=function flattenObjByMapper(e,t){var r={};for(var i in t){var a=t[i],n="number"==typeof a?i:a.access?a.access:i,o=n.split("."),s=e;for(var c of o){if(void 0===s[c]||null===s[c]){s=void 0;break}s=s[c]}void 0!==s&&(r[n]=s)}return r}(e,t),t){var n=t[a],o="number"==typeof n?a:n.access?n.access:a;if(!r||r.includes(a))if(o in e){if("number"==typeof n)i[n]=e[o];else if("object"==typeof n)if(n.converter){var s=n.converter(e[o],e);void 0!==s&&(i[n.id]=s)}else i[n.id]=e[o]}else"object"==typeof n&&n.def&&("function"==typeof n.def?i[n.id]=n.def(e):i[n.id]=n.def)}return i}function deserialize(e,t){var r={};for(var i in e){var a=t[i];if("string"==typeof a)r[a]=e[i];else if("object"==typeof a&&"prop"in a){var n=a.access?a.access:a.prop;if(a.converter){var o=a.converter(e[i],e);void 0!==o&&(r[n]=o)}else a.type&&"number"===a.type?r[n]=+e[i]:a.type&&"boolean"===a.type?r[n]=!("0"===e[i]||!e[i]):r[n]=e[i]}}for(var s in t){var c=t[s];if(c&&void 0!==c.def){var l=c.access?c.access:c.prop;l in r||("function"==typeof c.def?r[l]=c.def(e):r[l]=c.def)}}return r=function unflattenObj(e){var t={},_loop=function(r){var i=r.split(".");i.reduce((function(t,a,n){return t[a]||(t[a]=isNaN(Number(i[n+1]))?i.length-1==n?e[r]:{}:[])}),t)};for(var r in e)_loop(r);return t}(r),r}function registerParser(e){for(var t in Object.assign(ve,e.cmdConfig),e.cmdMap){var r=e.cmdMap[t],i=e.cmdConfig[r];if(i)if(Array.isArray(Ee[t])){var a=!1;for(var n of Ee[t])if(n.cmd===r&&n.config.service===i.service){a=!0;break}a||Ee[t].push({config:i,cmd:r})}else Ee[t]=[{config:i,cmd:r}]}}function invertSerializeMap(e){var t={};return Object.keys(e).forEach((r=>{t[r]=function invertSerializeItem(e){var t={};for(var r in e){var i=e[r];"number"==typeof i?t[i]=r:"object"==typeof i&&(t[i.id]={prop:r,type:i.retType,access:i.retAccess?i.retAccess:i.access?i.access:r,def:i.retDef,converter:i.retConverter})}return t}(e[r])})),t}function invert(e){e=e||{};var t={};for(var r in e)t[e[r]]=r;return t}var Ie={"6_2":"getNosToken","6_22":"getOriginUrl","6_24":"getNosAccessToken","6_25":"deleteNosAccessToken","6_26":"getNosCdnHost","6_27":"getGrayscaleConfig","6_28":"getMixStorePolicy","6_29":"getMixStoreToken","6_30":"getFileAuthToken"},fe={nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},mixStoreTokenReqTag:{provider:0,tokenCount:1,nosSurvivalTime:2,tag:3,returnBody:4,policyVersion:5},nosConfigTag:{bucket:1,cdnDomain:2,expire:3,objectNamePrefix:4},grayConfigTag:{config:0,ttl:1},mixStorePolicyTag:{providers:0,ttl:1,mixEnable:2,nosPolicy:3,s3Policy:4,policyVersion:5},mixStoreTokenResTag:{provider:0,accessKeyId:1,secretAccessKey:2,sessionToken:3,token:4,expireTime:5,bucket:6,objectName:7,fileExpireSec:8,tag:9,shortUrl:10,region:11},nosSafeUrlTag:{safeUrl:0,originUrl:1},mixStoreAuthTokenReqTag:{type:1,urls:2},mixStoreAuthTokenResTag:{type:1,tokens:2,token:3,ttl:4},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3}},Me={getNosToken:{sid:6,cid:2,service:"cloudStorage",params:[{type:"String",name:"responseBody"},{type:"Property",name:"nosToken",entity:"nosToken",reflectMapper:fe.nosToken}],response:[{type:"Property",name:"nosToken",reflectMapper:invert(fe.nosToken)}]},getOriginUrl:{sid:6,cid:22,service:"cloudStorage",params:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:fe.nosSafeUrlTag}],response:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:invert(fe.nosSafeUrlTag)}]},getNosCdnHost:{sid:6,cid:26,service:"cloudStorage",response:[{type:"Property",name:"nosConfigTag",reflectMapper:invert(fe.nosConfigTag)}]},getGrayscaleConfig:{sid:6,cid:27,service:"cloudStorage",params:[{type:"Property",name:"config"}],response:[{type:"Property",name:"grayConfigTag",reflectMapper:invert(fe.grayConfigTag)}]},getMixStorePolicy:{sid:6,cid:28,service:"cloudStorage",params:[{type:"LongArray",name:"supportType"}],response:[{type:"Property",name:"mixStorePolicyTag",reflectMapper:invert(fe.mixStorePolicyTag)}]},getMixStoreToken:{sid:6,cid:29,service:"cloudStorage",params:[{type:"Property",name:"mixStoreTokenReqTag",reflectMapper:fe.mixStoreTokenReqTag}],response:[{type:"Property",name:"mixStoreTokenResTag",reflectMapper:invert(fe.mixStoreTokenResTag)}]},getFileAuthToken:{sid:6,cid:30,service:"cloudStorage",params:[{type:"Property",name:"mixStoreAuthTokenReqTag",reflectMapper:fe.mixStoreAuthTokenReqTag}],response:[{type:"Property",name:"mixStoreAuthTokenResTag",reflectMapper:invert(fe.mixStoreAuthTokenResTag)}]},getNosAccessToken:{sid:6,cid:24,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:fe.nosAccessTokenTag}],response:[{type:"Property",name:"tag",reflectMapper:invert(fe.nosAccessTokenTag)}]},deleteNosAccessToken:{sid:6,cid:25,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:fe.nosAccessTokenTag}]}};class MixStorage{constructor(e,t){this.GRAYKEY="AllGrayscaleConfig",this.MIXSTOREKEY="AllMixStorePolicy",this.grayConfig={mixStoreEnable:!1,timeStamp:0,ttl:0},this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10,this.circuitTimer=0,this.core=e,this.cloudStorage=t,this.logger=e.logger}reset(){this.grayConfig=null,this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10}getGrayscaleConfig(e,t){var r;return __awaiter(this,void 0,void 0,(function*(){if(pe.localStorage)try{pe.localStorage.getItem&&pe.localStorage.getItem(this.GRAYKEY)&&(this.grayConfig=JSON.parse(pe.localStorage.getItem(this.GRAYKEY))[e])}catch(e){pe.localStorage.getItem(this.GRAYKEY)&&this.core.logger.error("uploadFile:: JSON.parse grayscaleConfig error ",e)}if(!this.grayConfig||this.grayConfig.timeStamp+1e3*this.grayConfig.ttl<t){var i=yield this.core.sendCmd("getGrayscaleConfig",{config:{}});if(i.content&&i.content.grayConfigTag){this.logger.log("uploadFile::getAppGrayConfigRequest success ");try{this.grayConfig=JSON.parse(i.content.grayConfigTag.config),this.grayConfig.ttl=JSON.parse(i.content.grayConfigTag.ttl)}catch(e){this.logger.error("getGrayscaleConfig error",e)}if(!this.grayConfig)return;var a=pe.localStorage.getItem(this.GRAYKEY)?JSON.parse(pe.localStorage.getItem(this.GRAYKEY)):{};this.grayConfig.timeStamp=(new Date).getTime(),a[e]=this.grayConfig,pe.localStorage.setItem(this.GRAYKEY,JSON.stringify(a))}else this.logger.log("uploadFile:: result grayConfig:",i.content)}(null===(r=this.grayConfig)||void 0===r?void 0:r.mixStoreEnable)&&(yield this._getMixStorePolicy(e))}))}_getMixStorePolicy(e){return __awaiter(this,void 0,void 0,(function*(){var t=(new Date).getTime();if(pe.localStorage)try{if(this.mixStorePolicy=JSON.parse(pe.localStorage.getItem(this.MIXSTOREKEY))[e],this.curProvider=parseInt(this.mixStorePolicy.providers[0]),this.mixStorePolicy.timeStamp&&this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl>t){var r=this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl-t;this.core.timerManager.addTimer(this._getMixStorePolicy.bind(this,e),r)}}catch(t){pe.localStorage.getItem(this.MIXSTOREKEY)&&JSON.parse(pe.localStorage.getItem(this.MIXSTOREKEY))[e]&&this.core.logger.error("uploadFile:: JSON.parse mixStorePolicy error ",t)}if(!this.mixStorePolicy||this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl<=t)try{var i=(yield this.core.sendCmd("getMixStorePolicy",{supportType:this.cloudStorage.aws.s3?[1,2]:[1]})).content.mixStorePolicyTag;this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.mixStorePolicy.policyVersion=i.policyVersion,this.mixStorePolicy.ttl=Number(i.ttl),this.mixStorePolicy.providers=i.providers.split(","),this.circuitTimer&&this.core.timerManager.deleteTimer(this.circuitTimer),this.curProvider=parseInt(this.mixStorePolicy.providers[0]),this.mixStorePolicy.nosPolicy=i.nosPolicy?JSON.parse(i.nosPolicy):null,this.mixStorePolicy.s3Policy=i.s3Policy?JSON.parse(i.s3Policy):null,null===this.mixStorePolicy.s3Policy?this.mixStorePolicy.providers=["1"]:null===this.mixStorePolicy.nosPolicy?this.mixStorePolicy.providers=["2"]:this.mixStorePolicy.providers=this.mixStorePolicy.s3Policy.priority<this.mixStorePolicy.nosPolicy.priority?["2","1"]:["1","2"],this.core.timerManager.addTimer(this._getMixStorePolicy.bind(this,e),1e3*this.mixStorePolicy.ttl);var a=pe.localStorage.getItem(this.MIXSTOREKEY)?JSON.parse(pe.localStorage.getItem(this.MIXSTOREKEY)):{};this.mixStorePolicy.timeStamp=(new Date).getTime(),a[e]=this.mixStorePolicy,pe.localStorage.setItem(this.MIXSTOREKEY,JSON.stringify(a))}catch(t){if(this.logger.error("getMixStorePolicy error",t),0===this.mixStoreErrorCount)throw new Error("getMixStorePolicy all count error");this._getMixStorePolicy(e),this.mixStoreErrorCount--}this.mixStorePolicy.nosPolicy&&(this.cloudStorage.nos.nosErrorCount=this.mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry)}))}_addCircuitTimer(){var e=this.mixStorePolicy.providers,t=e[(e.indexOf(String(this.curProvider))+1)%e.length];if(!t)throw new Error("uploadFile nextProvider error");if(t===e[0])throw new Error("uploadFile all policy fail");if(this.logger.log(`uploadFile:: upload policy will change,now policy:${this.curProvider} nextProvider:${t}`),this.curProvider=parseInt(t),this.mixStorePolicy.nosPolicy&&this.mixStorePolicy.s3Policy){var r=this.mixStorePolicy[1===this.curProvider?"nosPolicy":"s3Policy"].uploadConfig.retryPolicy.circuit;if(!r||0===r)throw new Error("uploadFile circuit error");this.circuitTimer=this.core.timerManager.addTimer((()=>{this.logger.log(`uploadFile:: upload policy will change,now policy:${this.curProvider} nextProvider:${parseInt(this.mixStorePolicy.providers[0])}`),this.curProvider=parseInt(this.mixStorePolicy.providers[0]),this.core.timerManager.deleteTimer(this.circuitTimer)}),1e3*r)}throw new Error("uploadFile will not retry again")}getFileAuthToken(e){return __awaiter(this,void 0,void 0,(function*(){return(yield this.core.sendCmd("getFileAuthToken",{mixStoreAuthTokenReqTag:e})).content.mixStoreAuthTokenResTag}))}}var Ce,Te,Se,Re=-1;class AWS{constructor(e,t){this.s3=null,this.core=e,this.cloudStorage=t,this.logger=e.logger}get mixStorePolicy(){return this.cloudStorage.mixStorage.mixStorePolicy}s3Upload(e,t){return __awaiter(this,void 0,void 0,(function*(){var r;if(Re+=1,e.file)r=e.file;else if("string"==typeof e.fileInput){this.logger.warn("fileInput will abandon,Please use file or filepath");var i=document.getElementById(e.fileInput);if(!(i&&i.files&&i.files[0]))throw new Error("Can not get file from fileInput");r=i.files[0]}else{if(!(e.fileInput&&e.fileInput.files&&e.fileInput.files[0]))throw new Error(`Can not get file from fileInput ${e.fileInput}`);r=e.fileInput.files[0]}if(!this.mixStorePolicy.s3Policy)throw new Error("dont get s3 policy");var a={accessKeyId:t.accessKeyId,secretAccessKey:t.secretAccessKey,sessionToken:t.sessionToken,region:t.region,maxRetries:this.mixStorePolicy.s3Policy.uploadConfig.retryPolicy.retry},n=this.s3,o=decodeURIComponent(t.bucket),s=decodeURIComponent(t.objectName),c=r,l=`https://${o}.s3.amazonaws.com/${s}`,d={},h=this.mixStorePolicy.s3Policy;if(h&&h.uploadConfig&&Array.isArray(h.uploadConfig.uploadUrl)&&h.uploadConfig.uploadUrl.length>0){var u=h.uploadConfig.uploadUrl.length;Re%=u,d.endpoint=h.uploadConfig.uploadUrl[Re],d.s3ForcePathStyle=!0,l=`${d.endpoint}/${o}/${s}`}this.core.reporterHookCloudStorage.update({remote_addr:l,operation_type:1});var m=new n(d);m.config.update(a);var p={Bucket:o,Key:s,Body:c,Metadata:{token:t.token},ContentType:c.type||"application/octet-stream"};this.core.logger.log("uploadFile:: s3 upload params:",p);var g=m.upload(p);return g.on("httpUploadProgress",(t=>{var r=parseFloat((t.loaded/t.total).toFixed(2));e.onUploadProgress&&e.onUploadProgress({total:t.total,loaded:t.loaded,percentage:r,percentageText:Math.round(100*r)+"%"})})),new Promise(((r,i)=>{var a=(new Date).getTime();g.send(((n,l)=>__awaiter(this,void 0,void 0,(function*(){var d,h,u;if(n&&"RequestAbortedError"===n.code)this.logger.error("uploadFile:","api::s3:upload file abort.",n),i(new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:"S3RequestAbortedError",rawError:n,curProvider:2}}));else{if(!n){var m=this.mixStorePolicy.s3Policy.cdnSchema;m=(m=m.replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn)).replace("{objectName}",l.Key);var p={size:c.size,name:c.name,url:t.shortUrl?t.shortUrl:m,ext:c.name.split(".")[1]||"unknown"},g=e.type||"",y={image:"imageInfo"};return r(y[g]?yield this.getS3FileInfo({url:m,infoSuffix:y[g],s3Result:p}):p)}this.logger.error("uploadFile:","api::s3:upload file failed.",n),this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(h=null===(d=this.core)||void 0===d?void 0:d.auth)||void 0===h?void 0:h.account),trace_id:null===(u=this.core.clientSocket.socket)||void 0===u?void 0:u.sessionId,start_time:a,action:1,exception_service:4}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof n.status?n.status:"number"==typeof n.code?n.code:0,description:n.message||`${n.code}`,operation_type:1,target:JSON.stringify({bucket:o,object:s})},{asyncParams:pe.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1);var{net_connect:_}=yield pe.net.getNetworkStatus();if(!1===_)return i(new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:n,curProvider:this.cloudStorage.mixStorage.curProvider}}));try{this.cloudStorage.mixStorage._addCircuitTimer()}catch(t){return i(new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:t,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.mixStorePolicy,file:e.file||e.filePath}}))}r(this.cloudStorage._uploadFile(e))}})))),e.onUploadStart&&e.onUploadStart(g)}))}))}getS3FileInfo(e){var t;return __awaiter(this,void 0,void 0,(function*(){var r,{url:i,infoSuffix:a,s3Result:n}=e;try{r=yield this.core.adapters.request(`${i}?${a}`,{method:"GET",dataType:"text",timeout:5e3},{exception_service:3})}catch(e){return this.core.logger.error("uploadFile:: fetch file info error",e),n}if(r){var{data:o}=r,s="imageInfo"===a?o:null===(t=null==o?void 0:o.GetVideoInfo)||void 0===t?void 0:t.VideoInfo;return pickBy(Object.assign(Object.assign({},n),{w:null==s?void 0:s.Width,h:null==s?void 0:s.Height,orientation:null==s?void 0:s.Orientation,dur:null==s?void 0:s.Duration,audioCodec:null==s?void 0:s.AudioCodec,videoCodec:null==s?void 0:s.VideoCodec,container:null==s?void 0:s.Container}),(function(e){return void 0!==e}))}return this.core.logger.error("uploadFile:: fetch s3 file info no result",`${i}?${a}`),n}))}}class CloudStorageService{constructor(e,t={}){this.config={},this.uploadTaskMap={},this.name="cloudStorage",this.logger=e.logger,this.core=e,this.nos=new NOS(e,this),this.mixStorage=new MixStorage(e,this),this.aws=new AWS(e,this),registerParser({cmdMap:Ie,cmdConfig:Me}),this.setOptions(t),this.setListeners()}setOptions(e={}){var t=e.storageKeyPrefix||"NIMClient";this.mixStorage.GRAYKEY=t+"-AllGrayscaleConfig",this.mixStorage.MIXSTOREKEY=t+"-AllMixStorePolicy";var{s3:r}=e,i=__rest(e,["s3"]),a=Object.assign({},ue,this.config);if(i&&Object.prototype.hasOwnProperty.call(i,"cdn")){var n=Object.assign(Object.assign({},a.cdn),i.cdn);this.config=Object.assign({},a,i),this.config.cdn=n}else this.config=Object.assign({},a,i);r&&(this.aws.s3=r)}setListeners(){this.core.eventBus.on("kicked",this._clearUnCompleteTask.bind(this)),this.core.eventBus.on("disconnect",this._clearUnCompleteTask.bind(this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLogout",this._clearUnCompleteTask.bind(this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleKicked",this._clearUnCompleteTask.bind(this))}_clearUnCompleteTask(){Object.keys(this.uploadTaskMap).forEach((e=>{var t=this.uploadTaskMap[e];t&&t.abort&&t.abort()})),this.uploadTaskMap={}}init(e=Date.now()){return __awaiter(this,void 0,void 0,(function*(){this.mixStorage.reset(),this.nos.reset(),this.config.isNeedToGetUploadPolicyFromServer&&(yield this.mixStorage.getGrayscaleConfig(this.core.options.appkey,e)),yield this.nos._getNosCdnHost()}))}processCallback(e,t){var r=e.onUploadProgress,i=e.onUploadDone,a=e.onUploadStart;return{onUploadStart:"function"==typeof a?e=>{this.uploadTaskMap[t]=e;try{a(e)}catch(e){this.logger.error("CloudStorage::uploadFile:options.onUploadStart execute error",e)}}:e=>{this.uploadTaskMap[t]=e},onUploadProgress:"function"==typeof r?e=>{this.core.reporterHookCloudStorage.update({transferred_size:e.loaded,full_size:e.total});try{r(e)}catch(e){this.logger.error("CloudStorage::uploadFile:options.onUploadProgress execute error",e)}}:e=>{this.core.reporterHookCloudStorage.update({transferred_size:e.loaded,full_size:e.total})},onUploadDone:"function"==typeof i?e=>{this.core.reporterHookCloudStorage.end(0);try{i(e)}catch(e){this.logger.error("CloudStorage::uploadFile:options.onUploadDone execute error",e)}}:()=>{this.core.reporterHookCloudStorage.end(0)},taskKey:t}}uploadFile(e){return __awaiter(this,void 0,void 0,(function*(){if(validate({maxSize:{type:"number",required:!1},type:{type:"enum",values:["file","image","audio","video"]}},e),!e.fileInput&&!e.file&&!e.filePath)throw new Error("uploadFile needs target file object or a filePath");if(e.type&&"file"!==e.type){var t=get(e,"file.type");if(t&&"string"==typeof t&&-1===t.indexOf(e.type))throw new Error(`The meta type "${t}" does not match "${e.type}"`)}if(this.core.reporterHookCloudStorage.start(),e.file)this.core.reporterHookCloudStorage.update({full_size:e.file.size});else if("string"==typeof e.fileInput){var r=document.getElementById(e.fileInput);r&&r.files&&r.files[0]&&this.core.reporterHookCloudStorage.update({full_size:r.files[0].size})}else e.fileInput&&e.fileInput.files&&e.fileInput.files[0]&&this.core.reporterHookCloudStorage.update({full_size:e.fileInput.files[0].size});var i=se(),{onUploadStart:a,onUploadProgress:n,onUploadDone:o}=this.processCallback(e,i);e.onUploadStart=a,e.onUploadProgress=n,e.onUploadDone=o;var s=null;try{s=yield this._uploadFile(e),e.md5&&(s.md5=e.md5),delete this.uploadTaskMap[i]}catch(e){throw delete this.uploadTaskMap[i],this.core.reporterHookCloudStorage.end((e&&e.code)===ie.V2NIM_ERROR_CODE_CANCELLED?3:1),e}return s&&(s.size=void 0===s.size?void 0:Number(s.size),s.w=void 0===s.w?void 0:Number(s.w),s.h=void 0===s.h?void 0:Number(s.h),s.dur=void 0===s.dur?void 0:Number(s.dur)),s.url=decodeURIComponent(s.url),e.onUploadDone({size:s.size,name:s.name,url:s.url,ext:s.name.split(".")[1]||"unknown"}),s}))}_uploadFile(e){var t,r;return __awaiter(this,void 0,void 0,(function*(){if(!get(this.mixStorage,"grayConfig.mixStoreEnable")||!get(this.mixStorage,"mixStorePolicy.providers.length"))return this.logger.log("uploadFile:: uploadFile begin, use old nos"),this.nos.nosUpload(e);this.logger.log(`uploadFile::_uploadFile, grayConfig enable:${get(this.mixStorage,"grayConfig.mixStoreEnable")} curProvider:${get(this.mixStorage,"curProvider")}`);var i=this.core.adapters.getFileUploadInformation(e),a=!0;i?!1===i.complete&&2===this.mixStorage.curProvider&&(a=!1):a=!1,this.aws.s3||(this.mixStorage.curProvider=1);var n=he;if(!a)try{n=(yield this.core.sendCmd("getMixStoreToken",{mixStoreTokenReqTag:{provider:this.mixStorage.curProvider,tokenCount:1,tag:"qchat",nosSurvivalTime:e.nosSurvivalTime,returnBody:getUploadResponseFormat(e.type),policyVersion:this.mixStorage.mixStorePolicy.policyVersion}})).content.mixStoreTokenResTag}catch(e){if(this.core.logger.error("uploadFile:: getMixStoreToken error",e),e instanceof V2NIMErrorImpl)throw e;throw new UploadError({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getMixStoreToken error",rawError:e,curProvider:this.mixStorage.curProvider,mixStorePolicy:this.mixStorage.mixStorePolicy}})}return a?this.nos.nosUpload(e,null===(r=null===(t=null==i?void 0:i.uploadInfo)||void 0===t?void 0:t.payload)||void 0===r?void 0:r.mixStoreToken):2===this.mixStorage.curProvider?this.aws.s3Upload(e,n):this.nos.nosUpload(e,n)}))}getThumbUrl(e,t){var r,i,a,n,o;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(e))return this.logger.error("illegal file url:"+e),e;var[s,c,l,d,h,u,m,p]=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(e);if(null===(r=this.grayConfig)||void 0===r?void 0:r.mixStoreEnable){var g=this._getUrlType(e);if(2===g&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.imagethumb"))return(null===(a=null===(i=this.mixStorePolicy.s3Policy)||void 0===i?void 0:i.thumbPolicy)||void 0===a?void 0:a.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",u).replace("{x}",t.width.toString()).replace("{y}",t.height.toString());if(1===g&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.imagethumb"))return(null===(o=null===(n=this.mixStorePolicy.nosPolicy)||void 0===n?void 0:n.thumbPolicy)||void 0===o?void 0:o.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",u).replace("{x}",t.width.toString()).replace("{y}",t.height.toString())}return e.includes("?")?e+`&imageView&thumbnail=${t.width}x${t.height}`:e+`?imageView&thumbnail=${t.width}x${t.height}`}getVideoCoverUrl(e,t){var r,i,a,n,o;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(e))return this.logger.error("illegal file url:"+e),e;var[s,c,l,d,h,u,m,p]=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(e);if(null===(r=this.grayConfig)||void 0===r?void 0:r.mixStoreEnable){var g=this._getUrlType(e);if(2===g&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.vframe"))return(null===(a=null===(i=this.mixStorePolicy.s3Policy)||void 0===i?void 0:i.thumbPolicy)||void 0===a?void 0:a.vframe).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",u).replace("{x}",t.width.toString()).replace("{y}",t.height.toString()).replace("{offset}","0").replace("{type}","png");if(1===g&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.vframe"))return(null===(o=null===(n=this.mixStorePolicy.nosPolicy)||void 0===n?void 0:n.thumbPolicy)||void 0===o?void 0:o.vframe).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",u).replace("{x}",t.width.toString()).replace("{y}",t.height.toString()).replace("{offset}","0").replace("{type}","png")}return e.includes("?")?e+`&vframe&offset=0&resize=${t.width}x${t.height}&type=png`:e+`?vframe&offset=0&resize=${t.width}x${t.height}&type=png`}getPrivateUrl(e){var t;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(e))return this.logger.error("illegal file url:"+e),"";var[r,i,a,n,o,s,c,l]=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(e);if(null===(t=this.grayConfig)||void 0===t?void 0:t.mixStoreEnable){var d=this._getUrlType(e);return 2===d&&this.mixStorePolicy.s3Policy&&(e=this.mixStorePolicy.s3Policy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",s)),1===d&&this.mixStorePolicy.nosPolicy&&(e=this.mixStorePolicy.nosPolicy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",s)),e}var{downloadUrl:h,downloadHostList:u,nosCdnEnable:m}=this.config,p=this.config.cdn.cdnDomain,g=this.config.cdn.objectNamePrefix?decodeURIComponent(this.config.cdn.objectNamePrefix):"",y=decodeURIComponent(s),_=y.indexOf(g);if(p&&_>-1&&m)return`${i}${p}/${y.slice(_)}`;if(u.includes(n)&&s.includes("/")){var v=s.indexOf("/"),E=s.substring(0,v),I=s.substring(v+1);return h.replace("{bucket}",E).replace("{object}",I)}var f=u.filter((e=>"string"==typeof n&&n.includes(e)))[0],M=f?n.replace(f,"").replace(/\W/g,""):null;return M?h.replace("{bucket}",M).replace("{object}",s):e}getOriginUrl(e){return __awaiter(this,void 0,void 0,(function*(){return"string"==typeof e&&e.includes("_im_url=1")?(yield this.core.sendCmd("getOriginUrl",{nosSafeUrlTag:{safeUrl:e}})).content.nosSafeUrlTag.originUrl:e}))}getFileToken(e){return __awaiter(this,void 0,void 0,(function*(){validate({type:{type:"number",min:2,max:3},urls:{type:"array",required:!1,itemType:"string"}},e);var t=this.mixStorePolicy.nosPolicy?this.mixStorePolicy.nosPolicy.authPolicy.policyType:null,r=this.mixStorePolicy.s3Policy?this.mixStorePolicy.s3Policy.authPolicy.policyType:null;if(t===String(-1)&&r===String(-1))throw this.logger.error("don't need token"),new Error("don't need token");if(2===e.type){if(t&&t.indexOf(String(2))>=0||r&&r.indexOf(String(2))>0)return this.mixStorage.getFileAuthToken(e);throw this.logger.error("don't support time token "),new Error("don't support type time token ")}if(!e.urls||!e.urls.length)throw this.logger.error("urls is required when urls token"),new Error("urls is required when urls token");var i=[],a=[];if(e.urls.forEach((e=>{var t=this._getUrlType(e);1===t&&a.push(e),2===t&&i.push(e)})),(!r||0!==i.length&&r.indexOf(String(3))<0)&&(this.logger.warn("s3 url don't support url token"),i=[]),(!t||0!==a.length&&t.indexOf(String(3))<0)&&(this.logger.warn("nos url don't support url token"),a=[]),0===i.length&&0===a.length)throw this.logger.error("not support urls"),new Error("not support urls");if(0===i.length||0===a.length)return e.urls=JSON.stringify(e.urls),this.mixStorage.getFileAuthToken(e)}))}_getUrlType(e){return this.mixStorePolicy.nosPolicy&&this.mixStorePolicy.nosPolicy.dlcdns.some((t=>e.indexOf(t)>=0))?1:this.mixStorePolicy.s3Policy&&this.mixStorePolicy.s3Policy.dlcdns.some((t=>e.indexOf(t)>=0))?2:null}getNosAccessToken(e){return validate({url:{type:"string",allowEmpty:!1}},e),this.nos.getNosAccessToken(e)}deleteNosAccessToken(e){return validate({token:{type:"string",allowEmpty:!1}},e),this.nos.deleteNosAccessToken(e)}get grayConfig(){return this.mixStorage.grayConfig}get mixStorePolicy(){return this.mixStorage.mixStorePolicy}process(e){var t=get(e,"error.detail.ignore");return e.error&&!t?Promise.reject(e.error):Promise.resolve(e)}}!function(e){e[e.ASC=1]="ASC",e[e.DESC=2]="DESC"}(Ce||(Ce={})),function(e){e[e.reorderWeight=0]="reorderWeight",e[e.createTime=1]="createTime",e[e.totalMember=2]="totalMember"}(Te||(Te={})),function(e){e[e.square=1]="square",e[e.person=2]="person"}(Se||(Se={}));class Service extends Z{constructor(e,t){super(),this.name=e,this.core=t,this.name=e,this.logger=t.logger,this.core=t}emit(e,...t){try{return super.emit(e,...t)}catch(t){return setTimeout((()=>{throw this.logger.error(`${this.name}::emit throw error in setTimeout. event: ${e.toString()}. Error`,t),t}),0),!1}}process(e){var t=this[e.cmd+"Handler"];if("function"==typeof t)return t.call(this,e);var r=get(e,"error.detail.ignore");return e.error&&!r?Promise.reject(e.error):Promise.resolve(e)}}var Ae={"24_15":"qchatSubscribe","24_27":"qchatGetUnreadInfo","24_48":"qchatCreateChannel","24_49":"qchatDeleteChannel","24_50":"qchatUpdateChannel","24_51":"qchatGetChannels","24_52":"qchatGetChannelsByPage","24_53":"qchatGetMembersByPage","24_54":"qchatUpdateWhiteBlackRole","24_55":"qchatGetWhiteBlackRolesPage","24_56":"qchatUpdateWhiteBlackMembers","24_57":"qchatGetWhiteBlackMembersPage","24_58":"qchatGetExistingWhiteBlackRoles","24_59":"qchatGetExistingWhiteBlackMembers","24_60":"qchatUpdateCategoryInfoOfChannel","24_109":"qchatCreateChannelCategory","24_110":"qchatRemoveChannelCategory","24_111":"qchatUpdateChannelCategory","24_112":"qchatGetChannelCategoriesByID","24_113":"qchatUpdateChannelCategoryWhiteBlackRole","24_114":"qchatGetChannelCategoryWhiteBlackRolesPage","24_115":"qchatUpdateChannelCategoryWhiteBlackMembers","24_116":"qchatGetChannelCategoryWhiteBlackMembersPage","24_117":"qchatGetChannelCategoryWhiteBlackRoles","24_118":"qchatGetChannelCategoryWhiteBlackMembers","24_119":"qchatGetChannelCategoriesPage","24_120":"qchatGetChannelCategoryChannelsPage","24_93":"qchatGetChannelSearchByPage","24_95":"qchatChannelMemberSearch","25_8":"qchatGetRoleIdsByServerId","25_9":"qchatSubscribeAsVisitor","25_12":"qchatAutoSubscribe","25_13":"qchatAutoSubscribeNotification"},Ne={serverId:1,channelId:2,ackTimestamp:3,unreadCount:4,mentionedCount:5,maxCount:6,lastMsgTime:7},be={channelInfo:{channelId:1,serverId:2,name:4,topic:5,ext:6,type:7,validFlag:8,createTime:9,updateTime:10,owner:11,viewMode:12,categoryId:13,syncMode:14,reorderWeight:15,visitorMode:16},antispamTag:{antiSpamBusinessId:1},memberInfo:{serverId:1,accid:3,nick:4,avatar:5,ext:6,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},serverRole:{serverId:1,roleId:2,name:3,icon:4,ext:5,auths:6,type:7,memberCount:8,priority:9,createTime:10,updateTime:11},qchatSubReqTag:{type:1,opeType:2},qchatChannelIdInfoTag:{serverId:1,channelId:2},unreadInfo:Ne,qchatGetChannelListPageTag:{serverId:1,timetag:2,limit:3},qchatGetMembersByPageTag:{serverId:1,channelId:2,timetag:3,limit:4},qchatUpdateWhiteBlackRoleTag:{serverId:1,channelId:2,type:3,opeType:4,roleId:5},qchatGetWhiteBlackRolesPageTag:{serverId:1,channelId:2,type:3,timetag:4,limit:5},qchatUpdateWhiteBlackMembersTag:{serverId:1,channelId:2,type:3,opeType:4,toAccids:5},qchatGetWhiteBlackMembersPageTag:{serverId:1,channelId:2,type:3,timetag:4,limit:5},qchatGetExistingWhiteBlackRolesTag:{serverId:1,channelId:2,type:3,roleIds:4},qchatGetExistingWhiteBlackMembersTag:{serverId:1,channelId:2,type:3,accids:4},QChatChannelCategoryInfo:{categoryId:1,serverId:2,name:4,ext:5,owner:6,viewMode:7,validFlag:8,createTime:9,updateTime:10,channelNumber:11},qchatUpdateChannelCategoryWhiteBlackRoleTag:{serverId:1,categoryId:2,type:3,opeType:4,roleId:5},qchatGetChannelCategoryWhiteBlackRolesPageTag:{serverId:1,categoryId:2,type:3,timetag:4,limit:5},qchatUpdateChannelCategoryWhiteBlackMembersTag:{serverId:1,categoryId:2,type:3,opeType:4,toAccids:5},qchatGetChannelCategoryWhiteBlackMembersPageTag:{serverId:1,categoryId:2,type:3,timetag:4,limit:5},qchatGetChannelCategoryWhiteBlackRolesTag:{serverId:1,categoryId:2,type:3,roleIds:4},qchatGetChannelCategoryWhiteBlackMembersTag:{serverId:1,categoryId:2,type:3,accids:4},qchatGetChannelCategoriesPageTag:{serverId:1,timetag:2,limit:3},qchatGetChannelCategoryChannelsPageTag:{serverId:1,categoryId:2,timetag:3,limit:4},qchatGetChannelSearchByPageTag:{keyword:1,startTime:2,endTime:3,order:4,limit:5,serverId:6,sort:7,cursor:8},qchatUpdateChannelTag:{channelId:1,name:4,topic:5,ext:6,viewMode:12,visitorMode:16},serverRoles:{serverId:1,roleIds:2,timeTag:3},qchatChannelMemberSearchTag:{serverId:1,channelId:2,keyword:3,limit:4},qchatChannelMemberInfo:{serverId:1,channelId:2,avatar:3,accid:4,nick:5,createTime:6,updateTime:7}},getDeserializeTag$5=()=>invertSerializeMap(be),Oe={"24_31":"qchatCreateServer","24_32":"qchatDeleteServer","24_33":"qchatUpdateServer","24_34":"qchatGetServers","24_35":"qchatGetServersByPage","24_36":"qchatInviteServerMembers","24_37":"qchatAcceptServerInvite","24_38":"qchatRejectInviteServer","24_39":"qchatApplyServerJoin","24_40":"qchatAcceptServerApply","24_41":"qchatRejectServerApply","24_42":"qchatKickServerMembers","24_43":"qchatLeaveServer","24_44":"qchatUpdateMyMemberInfo","24_45":"qchatUpdateServerMemberInfo","24_46":"qchatGetServerMembers","24_47":"qchatGetServerMembersByPage","24_104":"qchatUpdateServerMemberBan","24_105":"qchatGetBannedMembersByPage","24_91":"qchatServerSearchByPage","24_92":"qchatServerMemberSearchByPage","24_122":"qchatGenerateInviteCode","24_123":"qchatJoinByInviteCode","24_124":"qchatGetInviteApplyRecordOfServer","24_125":"qchatGetInviteApplyRecordOfSelf","25_5":"qchatClearServersUnread","25_7":"qchatSubscribeChannelsByServers","25_10":"qchatEnterAsVisitor","25_11":"qchatLeaveAsVisitor"},Pe={serverInfo:{serverId:1,name:3,icon:4,ext:5,owner:6,memberNumber:7,inviteMode:8,applyMode:9,validFlag:10,createTime:11,updateTime:12,channelNumber:13,categoryNumber:14,searchType:15,searchEnable:16,reorderWeight:17},antispamTag:{antiSpamBusinessId:1},memberInfo:{serverId:1,accid:3,nick:4,avatar:5,ext:6,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},otherMemberInfo:{serverId:1,accid:3,nick:4,avatar:5,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},getServerListPageTag:{timestamp:1,limit:2},getServerMemberListPageTag:{serverId:1,timetag:2,limit:3},updateServerMemberBanTag:{serverId:1,opeType:2,accid:3,ext:4},getBannedMembersByPageTag:{serverId:1,timetag:2,limit:3},serverMemberBanInfo:{serverId:1,appid:2,accid:3,ext:4,banTime:5,validFlag:6,createTime:7,updateTime:8},serverSearchByPageTag:{keyword:1,startTime:2,endTime:3,order:4,limit:5,serverType:6,searchType:7,sort:8,cursor:9},serverMemberSearchByPageTag:{serverId:1,keyword:3,limit:4},inviteRespParamTag:{requestId:1},applyRespParamTag:{requestId:1,expireTime:2},inviteApplyRecord:{accid:1,type:2,serverId:3,status:4,requestId:5,createTime:6,updateTime:7,expireTime:8,data:9,recordId:10},clearServersUnreadTag:{successServerIds:1,failServerIds:2,ackTimestamp:3},unreadInfo:Ne},getDeserializeTag$4=()=>invertSerializeMap(Pe);function format(e,t){if(!isPlainObject(t))return{};var r=JSON.parse(JSON.stringify(t)),i=doFormat(e,r);return JSON.parse(JSON.stringify(Object.assign(Object.assign({},r),i)))}function doFormat(e,t){if(!isPlainObject(t))return{};var r={};return Object.keys(e).forEach((i=>{var a=e[i].type;if("string"!=typeof a){var n=doFormat(e[i],t);Object.keys(n).length>0&&(r[i]=n)}else{var o=e[i],s=o.rawKey||i,c=we[a](t,s,o);void 0!==c&&(t[s]=void 0,r[i]=c)}})),r}var we={number:function(e,t){if(void 0!==e[t])return+e[t]},string:function(e,t){if(void 0!==e[t])return e[t]},boolean:function(e,t){return+e[t]>0||0!=+e[t]&&void 0},enum:function(e,t,r){return r.values[e[t]]},object:function(e,t){if(void 0!==e[t])try{return JSON.parse(e[t])}catch(e){return{}}}};function formatReverse(e,t){if(!isPlainObject(t))return{};var r=JSON.parse(JSON.stringify(t)),i=doFormatReverse(e,r);return JSON.parse(JSON.stringify(Object.assign(Object.assign({},r),i)))}function doFormatReverse(e,t){if(!isPlainObject(t))return Object.keys(e).reduce(((t,r)=>(t[e[r].rawKey||r]=void 0,t)),{});var r={};return Object.keys(e).forEach((i=>{var a=e[i].type;if("string"!=typeof a){var n=doFormatReverse(e[i],t[i]);return Object.assign(r,n),void(t[i]=void 0)}var o=e[i],s=o.rawKey||i,c=ke[a](t,i,o);t[s]=void 0,r[s]=c})),r}var ke={number:function(e,t){return e[t]},string:function(e,t){return e[t]},boolean:function(e,t){return!0===e[t]?1:!1===e[t]?0:void 0},enum:function(e,t,r){return r.values[e[t]]},object:function(e,t){if(void 0!==e[t])try{return JSON.stringify(e[t])}catch(e){return""}}},qe=ke,Le={serverId:{type:"string"},name:{type:"string"},icon:{type:"string"},ext:{type:"string"},owner:{type:"string"},memberNumber:{type:"number"},inviteMode:{type:"number"},applyMode:{type:"number"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},channelNumber:{type:"number"},categoryNumber:{type:"number"},searchType:{type:"number"},searchEnable:{type:"boolean"},reorderWeight:{type:"string"}},De={serverId:{type:"string"},uid:{type:"string"},accid:{type:"string"},nick:{type:"string"},avatar:{type:"string"},ext:{type:"string"},type:{type:"number"},joinTime:{type:"number"},inviter:{type:"string"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"}},Ve={serverId:{type:"string"},appid:{type:"string"},accid:{type:"string"},ext:{type:"string"},banTime:{type:"number"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"}};function formatServer(e){return format(Le,e)}function formatServers(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatServer(e))):[]}function formatMember$1(e){return format(De,e)}function formatMembers$1(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatMember$1(e))):[]}function formatServerMemberBanInfos(e){return Array.isArray(e)&&e.length>0?e.map((e=>function formatServerMemberBanInfo(e){return format(Ve,e)}(e))):[]}function generateAntispamTag(e){var t,r,i;if(!e.antispamTag)return{};var a=Object.assign({},e.antispamTag);return(null===(t=e.antispamTag)||void 0===t?void 0:t.antiSpamBusinessId)&&"string"!=typeof(null===(r=e.antispamTag)||void 0===r?void 0:r.antiSpamBusinessId)&&(a.antiSpamBusinessId=JSON.stringify(null===(i=e.antispamTag)||void 0===i?void 0:i.antiSpamBusinessId)),a}var Ue={accid:{type:"string"},type:{type:"number"},serverId:{type:"string"},status:{type:"number"},requestId:{type:"string"},createTime:{type:"number"},updateTime:{type:"number"},expireTime:{type:"number"},data:{type:"object"}};function formatInviteApplyRecord(e){return format(Ue,e)}function formatInviteApplyRecords(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatInviteApplyRecord(e))):[]}var xe,Ge,Be,Fe,je={successServerIds:{type:"object"},failServerIds:{type:"object"},ackTimestamp:{type:"number"}};function formatClearServersUnread(e){return format(je,e)}!function(e){e[e.reorderWeight=0]="reorderWeight",e[e.createTime=1]="createTime"}(xe||(xe={})),function(e){e[e.white=1]="white",e[e.black=2]="black"}(Ge||(Ge={})),function(e){e[e.add=1]="add",e[e.remove=2]="remove"}(Be||(Be={})),function(e){e[e.message=0]="message",e[e.media=1]="media",e[e.ext=100]="ext"}(Fe||(Fe={}));var Ye,Qe,We,He,$e={channelId:{type:"string"},serverId:{type:"string"},name:{type:"string"},topic:{type:"string"},ext:{type:"string"},type:{type:"enum",values:Fe},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},viewMode:{type:"number"},categoryId:{type:"string"},syncMode:{type:"number"},visitorMode:{type:"number"},reorderWeight:{type:"string"}},Ke={serverId:{type:"string"},channelId:{type:"string"},ackTimestamp:{type:"number"},unreadCount:{type:"number"},mentionedCount:{type:"number"},maxCount:{type:"number"},lastMsgTime:{type:"number"}},Je={serverId:{type:"string"},channelId:{type:"string"},type:{type:"enum",values:Ge},opeType:{type:"enum",values:Be},toAccids:{type:"object"}},ze={serverId:{type:"string"},channelId:{type:"string"},type:{type:"enum",values:Ge},opeType:{type:"enum",values:Be},roleId:{type:"string"}},Xe={categoryId:{type:"string"},serverId:{type:"string"},name:{type:"string"},ext:{type:"string"},owner:{type:"string"},viewMode:{type:"number"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},channelNumber:{type:"number"}},Ze={serverId:{type:"string"},channelId:{type:"string"},accid:{type:"string"},avatar:{type:"string"},nick:{type:"string"},createTime:{type:"number"},updateTime:{type:"number"}};function formatUpdateWhiteBlackRole(e){return format(ze,e)}function formatChannel(e){return format($e,e)}function formatChannels(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatChannel(e))):[]}function formatUnreadInfo(e){return format(Ke,e)}function formatUnreadInfos(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatUnreadInfo(e))):[]}function formatChannelCategory(e){return format(Xe,e)}function formatChannelCategorys(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatChannelCategory(e))):[]}function formatChannelMembers(e){return Array.isArray(e)&&e.length>0?e.map((e=>function formatChannelMember(e){return format(Ze,e)}(e))):[]}function formatFailServerIds(e){try{return JSON.parse(e)}catch(e){return[]}}class QChatServerService extends Service{constructor(e){var t;super("qchatServer",e),this.core=e,registerParser({cmdMap:Oe,cmdConfig:(t=getDeserializeTag$4(),{qchatCreateServer:{sid:24,cid:31,service:"qchatServer",params:[{type:"Property",name:"serverInfo",reflectMapper:Pe.serverInfo},{type:"Property",name:"antispamTag",reflectMapper:Pe.antispamTag}],response:[{type:"Property",name:"serverInfo",reflectMapper:t.serverInfo}]},qchatDeleteServer:{sid:24,cid:32,service:"qchatServer",params:[{type:"Long",name:"serverId"}]},qchatUpdateServer:{sid:24,cid:33,service:"qchatServer",params:[{type:"Property",name:"serverInfo",reflectMapper:Pe.serverInfo},{type:"Property",name:"antispamTag",reflectMapper:Pe.antispamTag}],response:[{type:"Property",name:"serverInfo",reflectMapper:t.serverInfo}]},qchatGetServers:{sid:24,cid:34,service:"qchatServer",params:[{type:"LongArray",name:"serverIds"}],response:[{type:"PropertyArray",name:"serverList",reflectMapper:t.serverInfo}]},qchatGetServersByPage:{sid:24,cid:35,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:Pe.getServerListPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverInfo}]},qchatInviteServerMembers:{sid:24,cid:36,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"StrArray",name:"accids"},{type:"String",name:"ps"},{type:"Property",name:"params",reflectMapper:{ttl:1}}],response:[{type:"StrArray",name:"failByOverAccids"},{type:"StrArray",name:"failByBanAccids"},{type:"Property",name:"record",reflectMapper:t.applyRespParamTag}]},qchatAcceptServerInvite:{sid:24,cid:37,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"Property",name:"recordInfo",reflectMapper:Pe.inviteRespParamTag}]},qchatRejectInviteServer:{sid:24,cid:38,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"String",name:"ps"},{type:"Property",name:"recordInfo",reflectMapper:Pe.inviteRespParamTag}]},qchatApplyServerJoin:{sid:24,cid:39,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"ps"},{type:"Property",name:"params",reflectMapper:{ttl:1}}],response:[{type:"Property",name:"data",reflectMapper:t.applyRespParamTag}]},qchatAcceptServerApply:{sid:24,cid:40,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"Property",name:"recordInfo",reflectMapper:Pe.applyRespParamTag}]},qchatRejectServerApply:{sid:24,cid:41,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"String",name:"accid"},{type:"String",name:"ps"},{type:"Property",name:"recordInfo",reflectMapper:Pe.applyRespParamTag}]},qchatKickServerMembers:{sid:24,cid:42,service:"qchatServer",params:[{type:"Long",name:"serverId"},{type:"StrArray",name:"accids"}]},qchatLeaveServer:{sid:24,cid:43,service:"qchatServer",params:[{type:"Long",name:"serverId"}]},qchatUpdateMyMemberInfo:{sid:24,cid:44,service:"qchatServer",params:[{type:"Property",name:"memberInfo",reflectMapper:Pe.memberInfo},{type:"Property",name:"antispamTag",reflectMapper:Pe.antispamTag}],response:[{type:"Property",name:"memberInfo",reflectMapper:t.memberInfo}]},qchatUpdateServerMemberInfo:{sid:24,cid:45,service:"qchatServer",params:[{type:"Property",name:"memberInfo",reflectMapper:Pe.otherMemberInfo},{type:"Property",name:"antispamTag",reflectMapper:Pe.antispamTag}],response:[{type:"Property",name:"memberInfo",reflectMapper:t.memberInfo}]},qchatGetServerMembers:{sid:24,cid:46,service:"qchatServer",params:[{type:"StrArray",name:"accids"}],response:[{type:"PropertyArray",name:"accidList",reflectMapper:t.memberInfo}]},qchatGetServerMembersByPage:{sid:24,cid:47,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:Pe.getServerMemberListPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.memberInfo}]},qchatUpdateServerMemberBan:{sid:24,cid:104,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:Pe.updateServerMemberBanTag}]},qchatGetBannedMembersByPage:{sid:24,cid:105,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:Pe.getBannedMembersByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverMemberBanInfo}]},qchatServerSearchByPage:{sid:24,cid:91,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:Pe.serverSearchByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag",3:"cursor"}},{type:"PropertyArray",name:"datas",reflectMapper:t.serverInfo}]},qchatServerMemberSearchByPage:{sid:24,cid:92,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:Pe.serverMemberSearchByPageTag}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.memberInfo}]},qchatGenerateInviteCode:{sid:24,cid:122,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,ttl:2}}],response:[{type:"Property",name:"data",reflectMapper:{1:"serverId",2:"requestId",3:"inviteCode",4:"expireTime"}}]},qchatJoinByInviteCode:{sid:24,cid:123,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,inviteCode:2,ps:3}}]},qchatGetInviteApplyRecordOfServer:{sid:24,cid:124,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,fromTime:2,toTime:3,reverse:4,limit:5,cursor:6}}],response:[{type:"PropertyArray",name:"data",reflectMapper:t.inviteApplyRecord}]},qchatGetInviteApplyRecordOfSelf:{sid:24,cid:125,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{fromTime:1,toTime:2,reverse:3,limit:4,cursor:5}}],response:[{type:"PropertyArray",name:"data",reflectMapper:t.inviteApplyRecord}]},qchatClearServersUnread:{sid:25,cid:5,service:"qchatServer",params:[{type:"LongArray",name:"serverIds"}],response:[{type:"Property",name:"clearServersUnreadTag",reflectMapper:t.clearServersUnreadTag}]},qchatSubscribeChannelsByServers:{sid:25,cid:7,service:"qchatServer",params:[{type:"Int",name:"type"},{type:"LongArray",name:"serverIds"}],response:[{type:"PropertyArray",name:"unreadInfos",reflectMapper:t.unreadInfo},{type:"String",name:"failServerIds"}]},qchatEnterAsVisitor:{sid:25,cid:10,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverIds:1}}],response:[{type:"String",name:"failServerIds"}]},qchatLeaveAsVisitor:{sid:25,cid:11,service:"qchatServer",params:[{type:"Property",name:"tag",reflectMapper:{serverIds:1}}],response:[{type:"String",name:"failServerIds"}]}})})}createServer(e){return __awaiter(this,void 0,void 0,(function*(){return validate({icon:{type:"string",required:!1},name:{type:"string",required:!1},ext:{type:"string",required:!1},searchType:{type:"number",required:!1,min:0},searchEnable:{type:"boolean",required:!1},inviteMode:{type:"number",min:0,max:1,required:!1},applyMode:{type:"number",min:0,max:1,required:!1}},e),formatServer((yield this.core.sendCmd("qchatCreateServer",{serverInfo:Object.assign(Object.assign({},e),{searchEnable:!1===e.searchEnable?0:1}),antispamTag:generateAntispamTag(e)})).content.serverInfo)}))}deleteServer(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1}},e),yield this.core.sendCmd("qchatDeleteServer",e)}))}updateServer(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",min:1},icon:{type:"string",required:!1},name:{type:"string",required:!1},ext:{type:"string",required:!1},searchType:{type:"number",required:!1,min:0},searchEnable:{type:"boolean",required:!1},inviteMode:{type:"number",min:0,max:1,required:!1},applyMode:{type:"number",min:0,max:1,required:!1}},e),formatServer((yield this.core.sendCmd("qchatUpdateServer",{serverInfo:Object.assign(Object.assign({},e),{searchEnable:!1===e.searchEnable?0:1}),antispamTag:generateAntispamTag(e)})).content.serverInfo)}))}getServers(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverIds:{type:"array",itemType:"string",min:1}},e),formatServers((yield this.core.sendCmd("qchatGetServers",e)).content.serverList)}))}getServersByPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({timestamp:{type:"number"},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetServersByPage",{tag:e}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatServers(r)}}))}inviteServerMembers(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},accids:{type:"array",itemType:"string",min:1},ps:{type:"string"},params:{type:"object",rules:{ttl:{type:"number"}},required:!1}},e);var t=yield this.core.sendCmd("qchatInviteServerMembers",e),r=t.content.record||{};return{failByOverAccids:t.content.failByOverAccids,failByBanAccids:t.content.failByBanAccids,recordInfo:formatInviteApplyRecord(r)}}))}acceptServerInvite(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},e),yield this.core.sendCmd("qchatAcceptServerInvite",e)}))}rejectInviteServer(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},ps:{type:"string"},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},e),yield this.core.sendCmd("qchatRejectInviteServer",e)}))}applyServerJoin(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",min:1},ps:{type:"string"},params:{type:"object",rules:{ttl:{type:"number"}},required:!1}},e),formatInviteApplyRecord((yield this.core.sendCmd("qchatApplyServerJoin",e)).content.data)}))}acceptServerApply(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},e),yield this.core.sendCmd("qchatAcceptServerApply",e)}))}rejectServerApply(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},ps:{type:"string"},recordInfo:{type:"object",rules:{requestId:{type:"string",allowEmpty:!1}}}},e),yield this.core.sendCmd("qchatRejectServerApply",e)}))}kickServerMembers(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},accids:{type:"array",itemType:"string",min:1}},e),yield this.core.sendCmd("qchatKickServerMembers",e)}))}leaveServer(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1}},e),yield this.core.sendCmd("qchatLeaveServer",e)}))}updateMyMemberInfo(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",min:1},accid:{type:"string",required:!1},nick:{type:"string",allowEmpty:!0,required:!1},avatar:{type:"string",required:!1},ext:{type:"string",required:!1}},e),formatMember$1((yield this.core.sendCmd("qchatUpdateMyMemberInfo",{memberInfo:e,antispamTag:generateAntispamTag(e)})).content.memberInfo)}))}updateServerMemberInfo(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",min:1},accid:{type:"string",min:1},nick:{type:"string",required:!1},avatar:{type:"string",required:!1}},e),formatMember$1((yield this.core.sendCmd("qchatUpdateServerMemberInfo",{memberInfo:e,antispamTag:generateAntispamTag(e)})).content.memberInfo)}))}getServerMembers(e){return __awaiter(this,void 0,void 0,(function*(){validate({accids:{type:"array"}},e);var t=[];if(e.accids.length)for(var r of e.accids)t.push(`${r.serverId}|${r.accid}`);return formatMembers$1((yield this.core.sendCmd("qchatGetServerMembers",{accids:t})).content.accidList)}))}getServerMembersByPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetServerMembersByPage",{tag:e}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatMembers$1(r)}}))}subscribeServer(e){var t,r,i,a;return __awaiter(this,void 0,void 0,(function*(){if(validate({type:{type:"number",min:4,max:4,required:!1},opeType:{type:"number",min:1,max:2}},e),(null===(r=null===(t=this.core.qchatChannel)||void 0===t?void 0:t.config)||void 0===r?void 0:r.autoSubscribe)&&!e.isInternalTrigger)throw new CustomError("subscribe server failed, manual subscribe is not allowed in auto subscribe mode",{},403);if(e.type||(e.type=4),e.servers.length){var n=Object.assign(Object.assign({},e),{channels:e.servers.map((e=>({serverId:e.serverId,channelId:""})))}),{failedChannels:o}=yield this.core.qchatChannel.subscribeModuleService.subscribe(n);return(null===(a=null===(i=this.core.qchatChannel)||void 0===i?void 0:i.config)||void 0===a?void 0:a.autoSubscribe)?this.core.qchatChannel.subscribeModuleService.cacheAutoSubscribe(n):this.core.qchatChannel.subscribeModuleService.cacheSubscribe(n),{failedServers:o}}}))}banServerMember(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},ext:{type:"string",required:!1}},e),yield this.core.sendCmd("qchatUpdateServerMemberBan",{tag:Object.assign(Object.assign({},e),{opeType:1})})}))}unbanServerMember(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},ext:{type:"string",required:!1}},e),yield this.core.sendCmd("qchatUpdateServerMemberBan",{tag:Object.assign(Object.assign({},e),{opeType:2})})}))}getBannedMembersByPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetBannedMembersByPage",{tag:e}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatServerMemberBanInfos(r)}}))}serverSearchByPage(e){return __awaiter(this,void 0,void 0,(function*(){if(validate({keyword:{type:"string",allowEmpty:!1},startTime:{type:"number",min:0,required:!1},endTime:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1},serverType:{type:"array",itemType:"number",min:0,required:!1},order:{type:"enum",values:getEnumKeys(Ce),required:!1},searchType:{type:"enum",values:getEnumKeys(Se)},sort:{type:"enum",values:getEnumKeys(Te),required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},e),e.startTime&&e.endTime&&e.startTime>=e.endTime)throw new ValidateError("startTime more than endTime",e,"timeRule");var t=yield this.core.sendCmd("qchatServerSearchByPage",{tag:Object.assign(Object.assign({},e),{order:e.order&&Ce[e.order],searchType:Se[e.searchType],sort:e.sort&&Te[e.sort],serverType:JSON.stringify(e.serverType)})}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag),cursor:i.cursor},datas:formatServers(r)}}))}serverMemberSearchByPage(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},keyword:{type:"string",allowEmpty:!1},limit:{type:"number",min:1,required:!1}},e),formatMembers$1((yield this.core.sendCmd("qchatServerMemberSearchByPage",{tag:e})).content.members)}))}generateInviteCode(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},ttl:{type:"number",min:0,required:!1}},e),function formatExpireTime(e){return format({expireTime:{type:"number"}},e)}((yield this.core.sendCmd("qchatGenerateInviteCode",{tag:e})).content.data)}))}joinByInviteCode(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},inviteCode:{type:"string",allowEmpty:!1},ps:{type:"string",required:!1}},e),yield this.core.sendCmd("qchatJoinByInviteCode",{tag:e})}))}getInviteApplyRecordOfServer(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},fromTime:{type:"number",min:0},toTime:{type:"number",min:0},reverse:{type:"boolean",required:!1},limit:{type:"number",min:1,required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},e),formatInviteApplyRecords((yield this.core.sendCmd("qchatGetInviteApplyRecordOfServer",{tag:Object.assign(Object.assign({},e),{reverse:e.reverse?1:0})})).content.data)}))}getInviteApplyRecordOfSelf(e){return __awaiter(this,void 0,void 0,(function*(){return validate({fromTime:{type:"number",min:0},toTime:{type:"number",min:0},reverse:{type:"boolean",required:!1},limit:{type:"number",min:1,required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},e),formatInviteApplyRecords((yield this.core.sendCmd("qchatGetInviteApplyRecordOfSelf",{tag:Object.assign(Object.assign({},e),{reverse:e.reverse?1:0})})).content.data)}))}markRead(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverIds:{type:"array",itemType:"string",required:!0}},e);var t=formatClearServersUnread((yield this.core.sendCmd("qchatClearServersUnread",e)).content.clearServersUnreadTag),{successServerIds:r,ackTimestamp:i}=t;return this.logger.log("qchatServer::clearServersUnread:: begin auto clear servers unreadInfo"),this.core.eventBus.emit("qchatChannel/clearUnreadCountByServers",r,i),t}))}subscribeAllChannel(e){var t,r;return __awaiter(this,void 0,void 0,(function*(){if(validate({type:{type:"number",required:!0},serverIds:{type:"array",itemType:"string",required:!0,max:10}},e),null===(r=null===(t=this.core.qchatChannel)||void 0===t?void 0:t.config)||void 0===r?void 0:r.autoSubscribe)throw new CustomError("subscribe channel failed, manual subscribe is not allowed in auto subscribe mode.",{},403);var i=yield this.core.sendCmd("qchatSubscribeChannelsByServers",e,{timeout:3e4});i.content.unreadInfos=formatUnreadInfos(i.content.unreadInfos),i.content.failServerIds=formatFailServerIds(i.content.failServerIds);var a={type:e.type,opeType:1,channels:i.content.unreadInfos.map((e=>({serverId:e.serverId,channelId:e.channelId})))};return this.core.eventBus.emit("qchatChannel/cacheSubscribe",a),this.core.eventBus.emit("qchatChannel/getRoleIdsByServerId",e.serverIds),this.core.eventBus.emit("qchatChannel/updateUnreads",i.content.unreadInfos),i.content}))}enterAsVisitor(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverIds:{type:"array",itemType:"string",min:1}},e);var t=formatFailServerIds((yield this.core.sendCmd("qchatEnterAsVisitor",{tag:{serverIds:JSON.stringify(e.serverIds)}})).content.failServerIds),r=difference(e.serverIds,t);return this.core.qchatChannel.subscribeForVisitorService.cacheServer(r),{failedServers:t}}))}leaveAsVisitor(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverIds:{type:"array",itemType:"string",min:1}},e);var t=formatFailServerIds((yield this.core.sendCmd("qchatLeaveAsVisitor",{tag:{serverIds:JSON.stringify(e.serverIds)}})).content.failServerIds);return difference(e.serverIds,t).forEach((e=>{this.core.qchatChannel.subscribeForVisitorService.deleteServer(e)})),{failedServers:t}}))}subscribeAsVisitor(e){return __awaiter(this,void 0,void 0,(function*(){return validate({opeType:{type:"number",min:1,max:2},type:{type:"number",required:!1},serverIds:{type:"array",itemType:"string",min:1}},e),e.serverIds.length?yield this.core.qchatChannel.subscribeForVisitorService.subscribeServerAsVisitor(e):{failedServers:[]}}))}}!function(e){e[e.everyone=1]="everyone",e[e.custom=2]="custom"}(Ye||(Ye={})),function(e){e[e.normal=0]="normal",e[e.owner=1]="owner"}(Qe||(Qe={})),function(e){e[e.ignore=0]="ignore",e[e.deny=-1]="deny",e[e.allow=1]="allow"}(We||(We={})),function(e){e[e.manageServer=1]="manageServer",e[e.manageChannel=2]="manageChannel",e[e.manageRole=3]="manageRole",e[e.sendMsg=4]="sendMsg",e[e.accountInfoSelf=5]="accountInfoSelf",e[e.inviteServer=6]="inviteServer",e[e.kickServer=7]="kickServer",e[e.accountInfoOther=8]="accountInfoOther",e[e.recallMsg=9]="recallMsg",e[e.deleteMsg=10]="deleteMsg",e[e.remindOther=11]="remindOther",e[e.remindEveryone=12]="remindEveryone",e[e.manageBlackWhiteList=13]="manageBlackWhiteList",e[e.banServerMember=14]="banServerMember",e[e.RTCChannelConnect=15]="RTCChannelConnect",e[e.RTCChannelDisconnectOther=16]="RTCChannelDisconnectOther",e[e.RTCChannelOpenMicrophone=17]="RTCChannelOpenMicrophone",e[e.RTCChannelOpenCamera=18]="RTCChannelOpenCamera",e[e.RTCChannelOpenCloseOtherMicrophone=19]="RTCChannelOpenCloseOtherMicrophone",e[e.RTCChannelOpenCloseOtherCamera=20]="RTCChannelOpenCloseOtherCamera",e[e.RTCChannelOpenCloseEveryoneMicrophone=21]="RTCChannelOpenCloseEveryoneMicrophone",e[e.RTCChannelOpenCloseEveryoneCamera=22]="RTCChannelOpenCloseEveryoneCamera",e[e.RTCChannelOpenShareScreen=23]="RTCChannelOpenShareScreen",e[e.RTCChannelCloseOtherShareScreen=24]="RTCChannelCloseOtherShareScreen",e[e.manageInviteApply=25]="manageInviteApply",e[e.manageInviteApplyHistory=26]="manageInviteApplyHistory",e[e.mentionedRole=27]="mentionedRole"}(He||(He={}));var et,rt,it,at,nt,ot,st,ct,lt={type:{type:"enum",values:Ye},memberType:{type:"enum",values:Qe},createTime:{type:"number"},updateTime:{type:"number"},priority:{type:"number"},memberCount:{type:"number"},joinTime:{type:"number"}},dt={roleId:{type:"string"},categoryId:{type:"string"},serverId:{type:"string"},type:{type:"enum",values:Ye},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},name:{type:"string"},icon:{type:"string"},ext:{type:"string"}},ht={id:{type:"string"},accid:{type:"string"},categoryId:{type:"string"},serverId:{type:"string"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"},nick:{type:"string"},avatar:{type:"string"},ext:{type:"string"},memberType:{type:"enum",values:Qe},joinTime:{type:"number"},inviter:{type:"string"}};function generatorRoleForCmd(e){var t=Object.assign({},e),r=formatReverse(lt,t);return r.auths&&(r.auths=function generateRoleAuthsForCmd(e){var t=Object.keys(e).reduce(((t,r)=>{var i=He[r];return i?t[i]=We[e[r]]:t[r]=We[e[r]],t}),{});return JSON.stringify(t)}(r.auths)),r}function formatRoleAuths(e){return Object.keys(e).reduce(((t,r)=>{var i=getEnumKeyByEnumValue(He,r);i?t[i]=We[e[r]]:t[parseInt(r)]=We[e[r]];return t}),{})}function formatRole(e){var t=format(lt,e);return e.auths&&(t.auths=formatRoleAuths(JSON.parse(t.auths))),t.isMember&&delete t.isMember,t}function formatRoles(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatRole(e))):[]}function formatChannelCategoryRole(e){return e.auths&&(e.auths=formatRoleAuths(JSON.parse(e.auths))),format(dt,e)}function formatChannelCategoryMemberRole(e){return e.auths&&(e.auths=formatRoleAuths(JSON.parse(e.auths))),format(ht,e)}function isObject(e){var t=typeof e;return null!=e&&("object"==t||"function"==t)}!function(e){e[e.default=1]="default",e[e.sync=2]="sync"}(et||(et={})),function(e){e[e.sendTime=1]="sendTime"}(rt||(rt={})),function(e){e[e.text=0]="text",e[e.image=1]="image",e[e.audio=2]="audio",e[e.video=3]="video",e[e.geo=4]="geo",e[e.notification=5]="notification",e[e.file=6]="file",e[e.tip=10]="tip",e[e.robot=11]="robot",e[e.g2=12]="g2",e[e.custom=100]="custom"}(it||(it={})),function(e){e[e.sending=1]="sending",e[e.success=2]="success",e[e.failed=3]="failed"}(at||(at={})),function(e){e[e.notifyAll=1]="notifyAll",e[e.notifySubscribe=2]="notifySubscribe"}(nt||(nt={})),function(e){e[e.unknown=0]="unknown",e[e.server=1]="server",e[e.channel=2]="channel",e[e.serverAccids=3]="serverAccids",e[e.channelAccids=4]="channelAccids",e[e.accids=5]="accids"}(ot||(ot={})),function(e){e[e.serverMemberInvite=1]="serverMemberInvite",e[e.serverMemberInviteReject=2]="serverMemberInviteReject",e[e.serverMemberApply=3]="serverMemberApply",e[e.serverMemberApplyReject=4]="serverMemberApplyReject",e[e.serverCreate=5]="serverCreate",e[e.serverRemove=6]="serverRemove",e[e.serverUpdate=7]="serverUpdate",e[e.serverMemberInviteDone=8]="serverMemberInviteDone",e[e.serverMemberInviteAccept=9]="serverMemberInviteAccept",e[e.serverMemberApplyDone=10]="serverMemberApplyDone",e[e.serverMemberApplyAccept=11]="serverMemberApplyAccept",e[e.serverMemberKick=12]="serverMemberKick",e[e.serverMemberLeave=13]="serverMemberLeave",e[e.serverMemberUpdate=14]="serverMemberUpdate",e[e.channelCreate=15]="channelCreate",e[e.channelRemove=16]="channelRemove",e[e.channelUpdate=17]="channelUpdate",e[e.channelUpdateWhiteBlackIdentify=18]="channelUpdateWhiteBlackIdentify",e[e.channelUpdateWhiteBlackIdentifyUser=19]="channelUpdateWhiteBlackIdentifyUser",e[e.updateQuickComment=20]="updateQuickComment",e[e.channelCategoryCreate=21]="channelCategoryCreate",e[e.channelCategoryRemove=22]="channelCategoryRemove",e[e.channelCategoryUpdate=23]="channelCategoryUpdate",e[e.channelCategoryUpdateWhiteBlackIdentify=24]="channelCategoryUpdateWhiteBlackIdentify",e[e.channelCategoryUpdateWhiteBlackIdentifyUser=25]="channelCategoryUpdateWhiteBlackIdentifyUser",e[e.serverIdentifyAdd=26]="serverIdentifyAdd",e[e.serverIdentifyRemove=27]="serverIdentifyRemove",e[e.serverIdentifyUpdate=28]="serverIdentifyUpdate",e[e.channelIdentifyUpdate=29]="channelIdentifyUpdate",e[e.userIdentifyUpdate=30]="userIdentifyUpdate",e[e.channelVisibilityUpdate=31]="channelVisibilityUpdate",e[e.serverEnterLeave=32]="serverEnterLeave",e[e.serverMemberJoinByInviteCode=33]="serverMemberJoinByInviteCode",e[e.channelVisibilityToVisitorUpdate=34]="channelVisibilityToVisitorUpdate",e[e.myMemberInfoUpdated=35]="myMemberInfoUpdated",e[e.custom=100]="custom",e[e.msgTyping=101]="msgTyping"}(st||(st={})),function(e){e[e.reply=1]="reply",e[e.thread=2]="thread",e[e.all=3]="all"}(ct||(ct={}));var ut=Math.max,mt=Math.min;function debounce(e,t,r){var i,a,n,o,s,c,l=0,d=!1,h=!1,u=!0;if("function"!=typeof e)throw new TypeError("Expected a function");function invokeFunc(t){var r=i,n=a;return i=a=void 0,l=t,o=e.apply(n,r)}function leadingEdge(e){return l=e,s=setTimeout(timerExpired,t),d?invokeFunc(e):o}function shouldInvoke(e){var r=e-c;return void 0===c||r>=t||r<0||h&&e-l>=n}function timerExpired(){var e=Date.now();if(shouldInvoke(e))return trailingEdge(e);s=setTimeout(timerExpired,function remainingWait(e){var r=t-(e-c);return h?mt(r,n-(e-l)):r}(e))}function trailingEdge(e){return s=void 0,u&&i?invokeFunc(e):(i=a=void 0,o)}function debounced(){var e=Date.now(),r=shouldInvoke(e);if(i=arguments,a=this,c=e,r){if(void 0===s)return leadingEdge(c);if(h)return clearTimeout(s),s=setTimeout(timerExpired,t),invokeFunc(c)}return void 0===s&&(s=setTimeout(timerExpired,t)),o}return t=Number(t)||0,isObject(r)&&(d=!!r.leading,n=(h="maxWait"in r)?ut(Number(r.maxWait)||0,t):n,u="trailing"in r?!!r.trailing:u),debounced.cancel=function cancel(){void 0!==s&&clearTimeout(s),l=0,i=c=a=s=void 0},debounced.flush=function flush(){return void 0===s?o:trailingEdge(Date.now())},debounced}var pt;function throttle(e,t,r){var i=!0,a=!0;if("function"!=typeof e)throw new TypeError("Expected a function");return isObject(r)&&(i="leading"in r?!!r.leading:i,a="trailing"in r?!!r.trailing:a),debounce(e,t,{leading:i,maxWait:t,trailing:a})}function chunk(e,t){e=e||[],t=t||1,t=Math.max(Math.floor(t),1);for(var r=[],i=0;i<e.length;i+=t)r.push(e.slice(i,i+t));return r}!function(e){e[e.sub=1]="sub",e[e.unSub=2]="unSub"}(pt||(pt={}));class UnreadInfoModuleService{constructor(e){this.unreadCount={},this.manualSubscribeUnreadMap={},this.manualSubscribeServerMap={},this.manualSubscribeTypingMap={},this.unreadServerCount={},this.serverRoleIdsMap={},this.autoSubscribeUnreadMap={},this.autoSubscribeServerMap={},this._serverUnreadInfo=throttle((e=>{this.core.emit("serverUnreadInfo",this.getServerUnreadInfo(e)),get(this.core,"qchatServer.emit")&&this.core.qchatServer.emit("serverUnreadInfo",this.getServerUnreadInfo(e))}),100),this.core=e}changeCacheServerRoleIds(e){return __awaiter(this,void 0,void 0,(function*(){var{serverId:t,roleId:r}=e.attach.serverIdentifyInfo;this.serverRoleIdsMap[t]||(yield this.getRoleIdsByServerId([t]));var i=this.serverRoleIdsMap[t];if(!(e.time<i.timeTag)){e.type===st[st.serverIdentifyAdd]?i.roleIds.add(r):i.roleIds.delete(r),this.core.logger.debug(`QChatChannel::qchatChannel/serverIdentifyChange::now ${t} roleIds is`,[...i.roleIds]);var a=this.unreadServerCount[t];if(a)chunk(Object.keys(a),100).forEach((e=>{this.core.qchatChannel.getChannelUnreadInfos({channels:e.map((e=>({serverId:t,channelId:e})))})}))}}))}getRoleIdsByServerId(e){return __awaiter(this,void 0,void 0,(function*(){var t=e.filter((e=>!this.serverRoleIdsMap[e]));if(t.length){var r={serverIdTimeTags:t},i=yield this.core.sendCmd("qchatGetRoleIdsByServerId",{qchatGetRoleIdsByServerIdTag:r});this.core.logger.debug("QChatChannel:: getRoleIdsByServerId, params is ",r,"result is",i),i.content.serverRoles.forEach((e=>{try{e.roleIds=JSON.parse(e.roleIds)}catch(e){this.core.logger.error("QChatChannel:: getRoleIdsByServerId JSON parse roleIds error",e)}this.serverRoleIdsMap[e.serverId]={roleIds:new Set(e.roleIds),timeTag:e.timeTag}}))}}))}_unSubscribeChannel(e,t){return __awaiter(this,void 0,void 0,(function*(){var r=`${e}_${t}`,i=[];if(this.manualSubscribeUnreadMap[r]?i.push(this.manualSubscribeUnreadMap[r]):this.autoSubscribeUnreadMap[r]&&i.push(this.autoSubscribeUnreadMap[r]),this.manualSubscribeTypingMap[r]&&i.push(this.manualSubscribeTypingMap[r]),0!==i.length){for(var a of i)this.subscribe({opeType:pt.unSub,type:a,channels:[{serverId:e,channelId:t}]});this.manualSubscribeUnreadMap[r]?this.manualSubscribeUnreadMap[r]=void 0:this.autoSubscribeUnreadMap[r]&&(this.autoSubscribeUnreadMap[r]=void 0),this.manualSubscribeTypingMap[r]&&(this.manualSubscribeTypingMap[r]=void 0)}}))}_unSubscribeServer(e){return __awaiter(this,void 0,void 0,(function*(){var t=e,r=this.manualSubscribeServerMap[t]||this.autoSubscribeServerMap[t];r&&(this.subscribe({opeType:pt.unSub,type:r,channels:[{serverId:e,channelId:""}]}),this.manualSubscribeServerMap[t]?this.manualSubscribeServerMap[t]=void 0:this.autoSubscribeServerMap[t]&&(this.autoSubscribeServerMap[t]=void 0))}))}subscribe(e){return __awaiter(this,void 0,void 0,(function*(){validate({type:{type:"number",min:1,max:5},opeType:{type:"number",min:1,max:2}},e);var t=yield this.core.sendCmd("qchatSubscribe",{qchatSubReqTag:{type:e.type,opeType:e.opeType},channels:e.channels}),r=new Set;return e.channels.forEach((e=>__awaiter(this,void 0,void 0,(function*(){this.serverRoleIdsMap[e.serverId]||r.add(e.serverId)})))),chunk(Array.from(r),10).forEach((e=>{this.getRoleIdsByServerId(e)})),t.content}))}autoSubscribe(){return __awaiter(this,void 0,void 0,(function*(){yield this.core.sendCmd("qchatAutoSubscribe",{qchatAutoSubReqTag:{}})}))}resumeSubscribe(e){return __awaiter(this,void 0,void 0,(function*(){this.serverRoleIdsMap={};if(!e)return this.unreadServerCount={},void(this.unreadCount={});var t={};Object.keys(this.manualSubscribeUnreadMap).forEach((e=>{var r=this.manualSubscribeUnreadMap[e];if(r){var[i,a]=e.split("_"),n={serverId:i,channelId:a};t[r]=t[r]||[],t[r].push(n)}})),t[5]=[],Object.keys(this.manualSubscribeTypingMap).forEach((e=>__awaiter(this,void 0,void 0,(function*(){if(this.manualSubscribeTypingMap[e]){var[r,i]=e.split("_"),a={serverId:r,channelId:i};t[5].push(a)}})))),t[4]=[],Object.keys(this.manualSubscribeServerMap).forEach((e=>__awaiter(this,void 0,void 0,(function*(){if(this.manualSubscribeServerMap[e]){var r={serverId:e};t[4].push(r)}}))));var r=[];Object.keys(t).forEach((e=>{var i=t[e];return i&&0===i.length?Promise.resolve():i.length>100?chunk(i,100).forEach((t=>{r.push(this.createSubscribePromise(t,e))})):void r.push(this.createSubscribePromise(i,e))})),yield Promise.all(r)}))}createSubscribePromise(e,t){var r={type:parseInt(t),opeType:1,channels:e};return this.core.logger.debug("qchatChannel:: autoSubscribeUnread params",r),this.subscribe(r).then((({unreadInfos:e})=>{e&&e.length>0&&(e=formatUnreadInfos(e),this.updateUnreads(e))})).catch((e=>{this.core.logger.error("qchatChannel:: autoSubscribeUnread error ",e)}))}cacheSubscribe(e){e.channels.forEach((t=>{var r=t.channelId?`${t.serverId}_${t.channelId}`:`${t.serverId}`,i=5===e.type?this.manualSubscribeTypingMap:4===e.type?this.manualSubscribeServerMap:this.manualSubscribeUnreadMap;e.opeType===pt.sub?i[r]=e.type:e.opeType===pt.unSub&&(i[r]=void 0)}))}cacheAutoSubscribe(e){e.channels.forEach((t=>{var r=t.channelId?`${t.serverId}_${t.channelId}`:`${t.serverId}`,i=4===e.type?this.autoSubscribeServerMap:this.autoSubscribeUnreadMap;e.opeType===pt.sub?i[r]=e.type:e.opeType===pt.unSub&&(i[r]=void 0)}))}cacheUnreadCount(e){e.forEach((e=>{var t=`${e.serverId}_${e.channelId}`;this.unreadCount[t]=Object.assign({},e),this.unreadServerCount[e.serverId]||(this.unreadServerCount[e.serverId]={}),this.unreadServerCount[e.serverId][e.channelId]=!0}))}getServerUnreadInfo(e){var t={serverId:e,unreadCount:0,mentionedCount:0,maxCount:0},r=this.unreadServerCount[e];return r&&(Object.keys(r).forEach((r=>{var i=`${e}_${r}`,a=this.unreadCount[i];t.unreadCount+=a.unreadCount,t.mentionedCount+=a.mentionedCount,void 0!==a.maxCount&&(t.maxCount=a.maxCount)})),t.unreadCount=t.unreadCount>t.maxCount?t.maxCount:t.unreadCount,t.mentionedCount=t.mentionedCount>t.maxCount?t.maxCount:t.mentionedCount),t}getUnreadInfo(e){validate({serverId:{type:"string"},channelId:{type:"string"}},e);var t=`${e.serverId}_${e.channelId}`;return this.unreadCount[t]?this.unreadCount[t]:null}shouldChangeUnread(e,t){if(!e.serverId||!e.channelId)return!1;if(this.core.qchatChannel.subscribeForVisitorService.isInSubscribeChannels(e.serverId,e.channelId))return!1;if(!1===e.historyEnable)return!1;if(!1===e.needBadge)return!1;if(e.fromAccount===this.core.account)return!1;if(t&&2!==(null==e?void 0:e.status))return!1;if(e.notifyReason&&e.notifyReason===getEnumKeyByEnumValue(nt,nt.notifySubscribe)){if(!e.serverId||!e.channelId)return!1;var r=this.getUnreadInfo({serverId:e.serverId,channelId:e.channelId}),i=t?"ackTimestamp":"lastMsgTime";if(!r)return!1;if(e.time&&r[i]&&r[i]>e.time)return!1}return!0}shouldChangeMentionedUnread(e,t=!1){return __awaiter(this,void 0,void 0,(function*(){if(e.accidsOfMentionedRoles&&e.accidsOfMentionedRoles.includes(this.core.account))return!0;if(e.mentionRoleIds&&e.serverId){if(!this.serverRoleIdsMap[e.serverId]){if(!t)return this.handledRoleMsg(e),!1;yield this.getRoleIdsByServerId([e.serverId])}for(var r=0;r<e.mentionRoleIds.length;r++)if(this.serverRoleIdsMap[e.serverId].roleIds.has(e.mentionRoleIds[r]))return!0}return!1}))}handledRoleMsg(e){return __awaiter(this,void 0,void 0,(function*(){if(e.mentionRoleIds)if(this.serverRoleIdsMap[e.serverId]||(yield this.getRoleIdsByServerId([e.serverId])),this.serverRoleIdsMap[e.serverId]){var t=!1;if(e.mentionRoleIds.forEach((r=>{this.serverRoleIdsMap[e.serverId].roleIds.has(r)&&(t=!0,this.core.logger.debug("QChatChannel::handledRoleMsg::will update message mentionedCount，message is ",e))})),t){var r=this.unreadCount[`${e.serverId}_${e.channelId}`],i=2===(null==e?void 0:e.status);if(r.mentionedCount=i?r.mentionedCount?r.mentionedCount-1:0:r.mentionedCount?r.mentionedCount+1:1,"number"==typeof r.maxCount){var a=r.mentionedCount;if((a=a>r.maxCount?r.maxCount:a)>r.maxCount)return void this.core.logger.debug("QChatChannel::handledRoleMsg::tempUnreadCount more than maxCount");this.core.emit("unreadInfo",Object.assign(Object.assign({},r),{mentionedCount:a})),this.core.emit("unreadInfos",[Object.assign(Object.assign({},r),{mentionedCount:a})]),this.core.qchatChannel.emit("unreadInfos",[Object.assign(Object.assign({},r),{mentionedCount:a})]),this._serverUnreadInfo(e.serverId)}}}else this.core.logger.warn("QChatChannel::handledRoleMsg::can not get serverRoleIds,server id",e.serverId)}))}getMentionedFlag(e,t=!1){return __awaiter(this,void 0,void 0,(function*(){return!!e.mentionAll||(e.mentionRoleIds||e.accidsOfMentionedRoles?yield this.shouldChangeMentionedUnread(e,t):!(!e.mentionAccids||!e.mentionAccids.includes(this.core.account)))}))}changeUnread(e,t){return __awaiter(this,void 0,void 0,(function*(){var r=e.serverId,i=e.channelId,a=`${r}_${i}`;if(this.shouldChangeUnread(e,t)){if(!this.unreadCount[a])return yield this.core.qchatChannel.getChannelUnreadInfos({channels:[{serverId:r,channelId:i}]}),void(!this.serverRoleIdsMap[r]&&e.mentionRoleIds&&this.getRoleIdsByServerId([r]));var n=this.unreadCount[a],o=2===(null==e?void 0:e.status),s=yield this.getMentionedFlag(e);o?(n.unreadCount=n.unreadCount?n.unreadCount-1:0,s&&(n.mentionedCount=n.mentionedCount?n.mentionedCount-1:0),delete n.lastMsgTime,this.core.logger.debug(`changeUnread: ${a} unread reduce `,n)):(n.unreadCount=n.unreadCount?n.unreadCount+1:1,s&&(n.mentionedCount=n.mentionedCount?n.mentionedCount+1:1),e.time&&(n.lastMsgTime=e.time),this.core.logger.debug(`changeUnread: ${a} unread add `,n));var c="number"==typeof n.maxCount?n.maxCount:100;n.unreadCount>c?this.core.logger.debug("QChatChannel::subscribe::tempUnreadCount more than maxCount"):(this.core.logger.warn("unreadInfo event will abandon,please use unreadInfos"),this.core.emit("unreadInfo",Object.assign(Object.assign({},n),{mentionedCount:n.mentionedCount>c?c:n.mentionedCount,unreadCount:n.unreadCount>c?c:n.unreadCount})),this.core.emit("unreadInfos",[Object.assign(Object.assign({},n),{mentionedCount:n.mentionedCount>c?c:n.mentionedCount,unreadCount:n.unreadCount>c?c:n.unreadCount})]),this.core.qchatChannel.emit("unreadInfos",[Object.assign(Object.assign({},n),{mentionedCount:n.mentionedCount>c?c:n.mentionedCount,unreadCount:n.unreadCount>c?c:n.unreadCount})]),this._serverUnreadInfo(r))}else this.core.logger.debug(`changeUnread: ${a} does not need to update unreadInfo`)}))}updateUnreads(e){if(e&&e.length>0){var t=[],r=[],i=e.filter((e=>{var t=`${e.serverId}_${e.channelId}`,r=this.unreadCount[t],i=get(r,"ackTimestamp"),a=get(e,"ackTimestamp");return!(i&&a&&a<i)&&(get(r,"unreadCount")!==get(e,"unreadCount")||get(r,"mentionedCount")!==get(e,"mentionedCount"))})).map((e=>{t.push(this.getServerUnreadInfo(e.serverId)),r.push(this.unreadServerCount[e.serverId]);var i=e.serverId,a=e.channelId,n=`${i}_${a}`;return this.core.logger.debug("qchat channel updateUnread: ",e),this.unreadCount[n]=Object.assign({},e),this.unreadServerCount[i]||(this.unreadServerCount[i]={}),this.unreadServerCount[i][a]=!0,e}));if(0!==i.length){this.core.emit("unreadInfos",i),this.core.qchatChannel.emit("unreadInfos",i);var a={};i.forEach(((e,i)=>{this.core.emit("unreadInfo",e);var n=t[i],o=r[i],s=this.getServerUnreadInfo(e.serverId),c=this.unreadServerCount[e.serverId],l=n.unreadCount!==s.unreadCount,d=!o||0===Object.keys(o).length,h=c&&Object.keys(c).length>0,u=n.mentionedCount!==s.mentionedCount;(l||u||d&&h)&&(a[e.serverId]=!0)})),Object.keys(a).forEach((e=>{this.core.emit("serverUnreadInfo",this.getServerUnreadInfo(e)),get(this.core,"qchatServer.emit")&&this.core.qchatServer.emit("serverUnreadInfo",this.getServerUnreadInfo(e))}))}}}clearUnreadCountByServers(e,t){var r=[];e.forEach((e=>{this.unreadServerCount[e]&&Object.keys(this.unreadServerCount[e]).forEach((t=>{r.push(`${e}_${t}`)}))}));var i=[];r.forEach((e=>{i.push(Object.assign(Object.assign({},this.unreadCount[e]),{unreadCount:0,mentionedCount:0,ackTimestamp:t}))})),this.updateUnreads(i)}}class SubscribeForVisitorService{constructor(e){this.limitForBatchEnter=10,this.limitForBatchSubscribe=100,this.autoServers=new Set,this.autoVisitorSubscribeServer=new Set,this.autoVisitorSubscribeChannel=new Set,this.core=e}subscribeChannelAsVisitor(e){return __awaiter(this,void 0,void 0,(function*(){var t=(yield this.core.sendCmd("qchatSubscribeAsVisitor",{tag:{type:e.type||6,opeType:e.opeType},datas:e.channels})).content.failedArr;return e.channels.filter((e=>!t.some((t=>t.channelId===e.channelId&&t.serverId===e.serverId)))).forEach((t=>{1===e.opeType?this.autoVisitorSubscribeChannel.add(`${t.serverId}&${t.channelId}`):this.autoVisitorSubscribeChannel.delete(`${t.serverId}&${t.channelId}`)})),{failedChannels:t}}))}subscribeServerAsVisitor(e){return __awaiter(this,void 0,void 0,(function*(){var t=yield this.core.sendCmd("qchatSubscribeAsVisitor",{tag:{type:e.type||7,opeType:e.opeType},datas:e.serverIds.map((e=>({serverId:e})))}),r=t.content.failedArr;return e.serverIds.filter((e=>!r.some((t=>t.serverId===e)))).forEach((t=>{1===e.opeType?this.autoVisitorSubscribeServer.add(t):this.autoVisitorSubscribeServer.delete(t)})),{failedServers:t.content.failedArr.map((e=>e.serverId))}}))}deleteServer(e){this.autoServers.delete(e),this.autoVisitorSubscribeServer.has(e)&&this.subscribeServerAsVisitor({opeType:2,serverIds:[e]});var t=[];this.autoVisitorSubscribeChannel.forEach((r=>{if(0===r.indexOf(e)){var i=r.replace(`${e}&`,"");t.push({serverId:e,channelId:i})}})),t.length>0&&this.subscribeChannelAsVisitor({opeType:2,channels:t})}deleteAutoSetInServerId(e){this.autoServers.delete(e),this.autoVisitorSubscribeServer.delete(e),this.autoVisitorSubscribeChannel.forEach((t=>{0===t.indexOf(e)&&this.autoVisitorSubscribeChannel.delete(t)}))}deleteAutoSetInChannel(e,t){this.autoVisitorSubscribeChannel.delete(`${e}&${t}`)}unSubscribeChannel(e,t){this.subscribeChannelAsVisitor({opeType:2,channels:[{serverId:e,channelId:t}]})}isInSubscribeChannels(e,t){return this.autoVisitorSubscribeChannel.has(`${e}&${t}`)}isInAutoServers(e){return this.autoServers.has(e)}doAutoSubscribe(){var e=[];return chunk(Array.from(this.autoVisitorSubscribeServer),this.limitForBatchSubscribe).forEach((t=>{e.push(this.subscribeServerAsVisitor({opeType:1,serverIds:t}))})),chunk(Array.from(this.autoVisitorSubscribeChannel),this.limitForBatchSubscribe).forEach((t=>{e.push(this.subscribeChannelAsVisitor({opeType:1,channels:t.map((e=>{var t=e.indexOf("&");return{serverId:e.slice(0,t),channelId:e.slice(t+1)}}))}))})),Promise.all(e)}doAutoEnterServer(){return __awaiter(this,void 0,void 0,(function*(){var e=chunk(Array.from(this.autoServers),this.limitForBatchEnter),t=[];e.forEach((e=>{t.push(this.core.qchatServer.enterAsVisitor({serverIds:e}))})),(yield Promise.all(t)).forEach((e=>{e.failedServers.forEach((e=>{this.deleteAutoSetInServerId(e)}))}))}))}resumeSubscribe(e){return __awaiter(this,void 0,void 0,(function*(){e?(yield this.doAutoEnterServer(),yield this.doAutoSubscribe()):(this.autoServers.clear(),this.autoVisitorSubscribeChannel.clear(),this.autoVisitorSubscribeServer.clear())}))}cacheServer(e){e.forEach((e=>this.autoServers.add(e)))}}class QChatChannelService extends Service{constructor(e,t){var r;super("qchatChannel",e),this.config={autoSubscribe:!1},this.core=e,registerParser({cmdMap:Ae,cmdConfig:(r=getDeserializeTag$5(),{qchatCreateChannel:{sid:24,cid:48,service:"qchatChannel",params:[{type:"Property",name:"channelInfo",reflectMapper:be.channelInfo},{type:"Property",name:"antispamTag",reflectMapper:be.antispamTag}],response:[{type:"Property",name:"channelInfo",reflectMapper:r.channelInfo}]},qchatDeleteChannel:{sid:24,cid:49,service:"qchatChannel",params:[{type:"Long",name:"channelId"}]},qchatUpdateChannel:{sid:24,cid:50,service:"qchatChannel",params:[{type:"Property",name:"channelInfo",reflectMapper:be.qchatUpdateChannelTag},{type:"Property",name:"antispamTag",reflectMapper:be.antispamTag}],response:[{type:"Property",name:"channelInfo",reflectMapper:r.channelInfo}]},qchatGetChannels:{sid:24,cid:51,service:"qchatChannel",params:[{type:"LongArray",name:"channelIds"}],response:[{type:"PropertyArray",name:"channelList",reflectMapper:r.channelInfo}]},qchatGetChannelsByPage:{sid:24,cid:52,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelListPageTag",reflectMapper:be.qchatGetChannelListPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.channelInfo}]},qchatGetMembersByPage:{sid:24,cid:53,service:"qchatChannel",params:[{type:"Property",name:"qchatGetMembersByPageTag",reflectMapper:be.qchatGetMembersByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.memberInfo}]},qchatUpdateWhiteBlackRole:{sid:24,cid:54,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateWhiteBlackRoleTag",reflectMapper:be.qchatUpdateWhiteBlackRoleTag}]},qchatGetWhiteBlackRolesPage:{sid:24,cid:55,service:"qchatChannel",params:[{type:"Property",name:"qchatGetWhiteBlackRolesPageTag",reflectMapper:be.qchatGetWhiteBlackRolesPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.serverRole}]},qchatUpdateWhiteBlackMembers:{sid:24,cid:56,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateWhiteBlackMembersTag",reflectMapper:be.qchatUpdateWhiteBlackMembersTag}]},qchatGetWhiteBlackMembersPage:{sid:24,cid:57,service:"qchatChannel",params:[{type:"Property",name:"qchatGetWhiteBlackMembersPageTag",reflectMapper:be.qchatGetWhiteBlackMembersPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.memberInfo}]},qchatGetExistingWhiteBlackRoles:{sid:24,cid:58,service:"qchatChannel",params:[{type:"Property",name:"qchatGetExistingWhiteBlackRolesTag",reflectMapper:be.qchatGetExistingWhiteBlackRolesTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:r.serverRole}]},qchatGetExistingWhiteBlackMembers:{sid:24,cid:59,service:"qchatChannel",params:[{type:"Property",name:"qchatGetExistingWhiteBlackMembersTag",reflectMapper:be.qchatGetExistingWhiteBlackMembersTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:r.memberInfo}]},qchatUpdateCategoryInfoOfChannel:{sid:24,cid:60,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateCategoryInfoOfChannelTag",reflectMapper:be.channelInfo}],response:[{type:"Property",name:"channelInfo",reflectMapper:r.channelInfo}]},qchatCreateChannelCategory:{sid:24,cid:109,service:"qchatChannel",params:[{type:"Property",name:"qchatCreateChannelCategoryTag",reflectMapper:be.QChatChannelCategoryInfo}],response:[{type:"Property",name:"QChatChannelCategoryInfo",reflectMapper:r.QChatChannelCategoryInfo}]},qchatRemoveChannelCategory:{sid:24,cid:110,service:"qchatChannel",params:[{type:"Long",name:"categoryId"}]},qchatUpdateChannelCategory:{sid:24,cid:111,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateChannelCategoryTag",reflectMapper:be.QChatChannelCategoryInfo}],response:[{type:"Property",name:"QChatChannelCategoryInfo",reflectMapper:r.QChatChannelCategoryInfo}]},qchatGetChannelCategoriesByID:{sid:24,cid:112,service:"qchatChannel",params:[{type:"LongArray",name:"categoryIds"}],response:[{type:"PropertyArray",name:"channelCategoryList",reflectMapper:r.QChatChannelCategoryInfo}]},qchatUpdateChannelCategoryWhiteBlackRole:{sid:24,cid:113,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateChannelCategoryWhiteBlackRoleTag",reflectMapper:be.qchatUpdateChannelCategoryWhiteBlackRoleTag}]},qchatGetChannelCategoryWhiteBlackRolesPage:{sid:24,cid:114,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackRolesPageTag",reflectMapper:be.qchatGetChannelCategoryWhiteBlackRolesPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.serverRole}]},qchatUpdateChannelCategoryWhiteBlackMembers:{sid:24,cid:115,service:"qchatChannel",params:[{type:"Property",name:"qchatUpdateChannelCategoryWhiteBlackMembersTag",reflectMapper:be.qchatUpdateChannelCategoryWhiteBlackMembersTag}]},qchatGetChannelCategoryWhiteBlackMembersPage:{sid:24,cid:116,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackMembersPageTag",reflectMapper:be.qchatGetChannelCategoryWhiteBlackMembersPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.memberInfo}]},qchatGetChannelCategoryWhiteBlackRoles:{sid:24,cid:117,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackRolesTag",reflectMapper:be.qchatGetChannelCategoryWhiteBlackRolesTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:r.serverRole}]},qchatGetChannelCategoryWhiteBlackMembers:{sid:24,cid:118,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryWhiteBlackMembersTag",reflectMapper:be.qchatGetChannelCategoryWhiteBlackMembersTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:r.memberInfo}]},qchatGetChannelCategoriesPage:{sid:24,cid:119,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoriesPageTag",reflectMapper:be.qchatGetChannelCategoriesPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.QChatChannelCategoryInfo}]},qchatGetChannelCategoryChannelsPage:{sid:24,cid:120,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelCategoryChannelsPageTag",reflectMapper:be.qchatGetChannelCategoryChannelsPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag"}},{type:"PropertyArray",name:"datas",reflectMapper:r.channelInfo}]},qchatSubscribe:{sid:24,cid:15,service:"qchatChannel",params:[{type:"Property",name:"qchatSubReqTag",reflectMapper:be.qchatSubReqTag},{type:"PropertyArray",name:"channels",reflectMapper:be.qchatChannelIdInfoTag}],response:[{type:"PropertyArray",name:"unreadInfos",reflectMapper:r.unreadInfo},{type:"PropertyArray",name:"failedChannels",reflectMapper:r.qchatChannelIdInfoTag}]},qchatAutoSubscribe:{sid:25,cid:12,service:"qchatChannel",hasPacketTimer:!1,params:[{type:"Property",name:"qchatAutoSubReqTag"}],response:[{type:"Property",name:"qchatAutoSubInfo",reflectMapper:{1:"time"}}]},qchatAutoSubscribeNotification:{service:"qchatChannel",sid:25,cid:13,response:[{type:"PropertyArray",name:"serverIds",reflectMapper:r.unreadInfo},{type:"PropertyArray",name:"unreadInfos",reflectMapper:r.unreadInfo}]},qchatGetUnreadInfo:{sid:24,cid:27,service:"qchatChannel",params:[{type:"PropertyArray",name:"channels",reflectMapper:be.qchatChannelIdInfoTag}],response:[{type:"PropertyArray",name:"unreadInfos",reflectMapper:r.unreadInfo}]},qchatGetChannelSearchByPage:{sid:24,cid:93,service:"qchatChannel",params:[{type:"Property",name:"qchatGetChannelSearchByPageTag",reflectMapper:be.qchatGetChannelSearchByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:{1:"hasMore",2:"nextTimetag",3:"cursor"}},{type:"PropertyArray",name:"datas",reflectMapper:r.channelInfo}]},qchatGetRoleIdsByServerId:{sid:25,cid:8,service:"qchatChannel",params:[{type:"Property",name:"qchatGetRoleIdsByServerIdTag",reflectMapper:{serverIdTimeTags:1}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:r.serverRoles},{type:"String",name:"failServerIds"}]},qchatChannelMemberSearch:{sid:24,cid:95,service:"qchatChannel",params:[{type:"Property",name:"qchatChannelMemberSearchTag",reflectMapper:be.qchatChannelMemberSearchTag}],response:[{type:"PropertyArray",name:"datas",reflectMapper:r.qchatChannelMemberInfo}]},qchatSubscribeAsVisitor:{sid:25,cid:9,service:"qchatChannel",params:[{type:"Property",name:"tag",reflectMapper:be.qchatSubReqTag},{type:"PropertyArray",name:"datas",reflectMapper:be.qchatChannelIdInfoTag}],response:[{type:"PropertyArray",name:"failedArr",reflectMapper:r.qchatChannelIdInfoTag}]}})}),t&&this.setOptions(t),this.subscribeModuleService=new UnreadInfoModuleService(e),this.subscribeForVisitorService=new SubscribeForVisitorService(e),this.setListener()}setOptions(e){e&&(this.config=Object.assign(this.config,e))}setListener(){this.core.eventBus.on("logined",(e=>{this.subscribeModuleService.resumeSubscribe(e.isAutoReconnect),this.subscribeForVisitorService.resumeSubscribe(e.isAutoReconnect),this.config.autoSubscribe&&(this.subscribeModuleService.autoSubscribeServerMap={},this.subscribeModuleService.autoSubscribeUnreadMap={},this.subscribeModuleService.autoSubscribe())})),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLoginSucc",(e=>{this.subscribeModuleService.resumeSubscribe(e.isReconnect),this.subscribeForVisitorService.resumeSubscribe(e.isReconnect),this.config.autoSubscribe&&(this.subscribeModuleService.autoSubscribeServerMap={},this.subscribeModuleService.autoSubscribeUnreadMap={},this.subscribeModuleService.autoSubscribe())})),this.core.eventBus.on("qchatChannel/changeUnread",this.subscribeModuleService.changeUnread.bind(this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/updateUnreads",this.subscribeModuleService.updateUnreads.bind(this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/cacheSubscribe",this.subscribeModuleService.cacheSubscribe.bind(this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/clearUnreadCountByServers",this.subscribeModuleService.clearUnreadCountByServers.bind(this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/getRoleIdsByServerId",this.subscribeModuleService.getRoleIdsByServerId.bind(this.subscribeModuleService)),this.core.eventBus.on("qchatChannel/autoUnSubscribe",(e=>{if(this.logger.log("QChatChannel::enter autoUnSubscribe sysMsg is",e),e.type===st[st.channelVisibilityUpdate])this.logger.log("QChatChannel::begin autoUnSubscribe channel key is",`${e.serverId}_${e.channelId}`),this.subscribeModuleService._unSubscribeChannel(e.serverId,e.channelId),this.logger.log("QChatChannel::autoUnSubscribe channel done key is",`${e.serverId}_${e.channelId}`);else if(e.type===st[st.serverEnterLeave]){(this.subscribeModuleService.manualSubscribeServerMap[e.serverId]||this.subscribeModuleService.autoSubscribeServerMap[e.serverId])&&(this.logger.log("QChatChannel::begin autoUnSubscribe server key is",e.serverId),this.subscribeModuleService._unSubscribeServer(e.serverId),this.logger.log("QChatChannel::autoUnSubscribe server done key is",e.serverId));var t=this.subscribeModuleService.unreadServerCount[e.serverId];if(!t)return;this.logger.log("QChatChannel::begin autoUnSubscribe channels key is",Object.keys(t)),Object.keys(t).forEach((t=>{this.subscribeModuleService._unSubscribeChannel(e.serverId,t)})),this.logger.log("QChatChannel::autoUnSubscribe channels done key is",Object.keys(t))}})),this.core.eventBus.on("qchatChannel/serverIdentifyChange",(e=>{this.subscribeModuleService.changeCacheServerRoleIds(e)}))}createChannel(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Fe)},name:{type:"string",required:!1},topic:{type:"string",required:!1},ext:{type:"string",required:!1},visitorMode:{type:"number",required:!1}},e),formatChannel((yield this.core.sendCmd("qchatCreateChannel",{channelInfo:Object.assign(Object.assign({},e),{type:Fe[e.type]}),antispamTag:generateAntispamTag(e)})).content.channelInfo)}))}deleteChannel(e){return __awaiter(this,void 0,void 0,(function*(){validate({channelId:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatDeleteChannel",e)}))}updateChannel(e){return __awaiter(this,void 0,void 0,(function*(){validate({channelId:{type:"string",min:1},serverId:{type:"string",required:!1},type:{type:"enum",values:getEnumKeys(Fe),required:!1},name:{type:"string",required:!1},topic:{type:"string",required:!1},ext:{type:"string",required:!1},visitorMode:{type:"number",required:!1}},e);var t=e;return e.type&&(t.type=Fe[e.type]),formatChannel((yield this.core.sendCmd("qchatUpdateChannel",{channelInfo:t,antispamTag:generateAntispamTag(e)})).content.channelInfo)}))}getChannels(e){return __awaiter(this,void 0,void 0,(function*(){return validate({channelIds:{type:"array",itemType:"string",min:1}},e),formatChannels((yield this.core.sendCmd("qchatGetChannels",e)).content.channelList)}))}getChannelsByPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetChannelsByPage",{qchatGetChannelListPageTag:Object.assign({limit:100},e)}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatChannels(r)}}))}getMembersByPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetMembersByPage",{qchatGetMembersByPageTag:e}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatMembers$1(r)}}))}updateWhiteBlackRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},roleId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Ge)},opeType:{type:"enum",values:getEnumKeys(Be)}},e),yield this.core.sendCmd("qchatUpdateWhiteBlackRole",{qchatUpdateWhiteBlackRoleTag:Object.assign(Object.assign({},e),{type:Ge[e.type],opeType:Be[e.opeType]})})}))}getWhiteBlackRolesPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Ge)},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetWhiteBlackRolesPage",{qchatGetWhiteBlackRolesPageTag:Object.assign(Object.assign({},e),{type:Ge[e.type]})}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatRoles(r)}}))}updateWhiteBlackMembers(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Ge)},opeType:{type:"enum",values:getEnumKeys(Be)},toAccids:{type:"array",itemType:"string",min:1}},e),yield this.core.sendCmd("qchatUpdateWhiteBlackMembers",{qchatUpdateWhiteBlackMembersTag:Object.assign(Object.assign({},e),{type:Ge[e.type],opeType:Be[e.opeType],toAccids:JSON.stringify(e.toAccids)})})}))}getWhiteBlackMembersPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Ge)},timetag:{type:"number"},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetWhiteBlackMembersPage",{qchatGetWhiteBlackMembersPageTag:Object.assign(Object.assign({limit:100},e),{type:Ge[e.type]})}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatMembers$1(r)}}))}getExistingWhiteBlackRoles(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Ge)},roleIds:{type:"array",itemType:"string",min:1}},e);var t=yield this.core.sendCmd("qchatGetExistingWhiteBlackRoles",{qchatGetExistingWhiteBlackRolesTag:Object.assign(Object.assign({},e),{type:Ge[e.type],roleIds:JSON.stringify(e.roleIds)})}),{datas:r}=t.content;return{datas:formatRoles(r)}}))}getExistingWhiteBlackMembers(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",min:1},channelId:{type:"string",min:1},type:{type:"enum",values:getEnumKeys(Ge)},accids:{type:"array",itemType:"string",min:1}},e);var t=yield this.core.sendCmd("qchatGetExistingWhiteBlackMembers",{qchatGetExistingWhiteBlackMembersTag:Object.assign(Object.assign({},e),{type:Ge[e.type],accids:JSON.stringify(e.accids)})}),{datas:r}=t.content;return{datas:formatMembers$1(r)}}))}updateCategoryInfoOfChannel(e){return __awaiter(this,void 0,void 0,(function*(){return validate({channelId:{type:"string",min:1},categoryId:{type:"string",allowEmpty:!1,required:!1},syncMode:{type:"number",min:0,max:1,required:!1}},e),formatChannel((yield this.core.sendCmd("qchatUpdateCategoryInfoOfChannel",{qchatUpdateCategoryInfoOfChannelTag:e})).content.channelInfo)}))}createChannelCategory(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},name:{type:"string",allowEmpty:!1,required:!1},ext:{type:"string",required:!1},viewMode:{type:"number",min:0,max:1,required:!1}},e),formatChannelCategory((yield this.core.sendCmd("qchatCreateChannelCategory",{qchatCreateChannelCategoryTag:e})).content.QChatChannelCategoryInfo)}))}removeChannelCategory(e){return __awaiter(this,void 0,void 0,(function*(){validate({categoryId:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatRemoveChannelCategory",e)}))}updateChannelCategory(e){return __awaiter(this,void 0,void 0,(function*(){return validate({categoryId:{type:"string",allowEmpty:!1},name:{type:"string",allowEmpty:!1,required:!1},ext:{type:"string",required:!1},viewMode:{type:"number",min:0,max:1,required:!1}},e),formatChannelCategory((yield this.core.sendCmd("qchatUpdateChannelCategory",{qchatUpdateChannelCategoryTag:e})).content.QChatChannelCategoryInfo)}))}getChannelCategoriesByID(e){return __awaiter(this,void 0,void 0,(function*(){return validate({categoryIds:{type:"array",itemType:"string",min:1}},e),formatChannelCategorys((yield this.core.sendCmd("qchatGetChannelCategoriesByID",e)).content.channelCategoryList)}))}updateChannelCategoryWhiteBlackRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Ge)},opeType:{type:"enum",values:getEnumKeys(Be)},roleId:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatUpdateChannelCategoryWhiteBlackRole",{qchatUpdateChannelCategoryWhiteBlackRoleTag:Object.assign(Object.assign({},e),{type:Ge[e.type],opeType:Be[e.opeType]})})}))}getChannelCategoryWhiteBlackRolesPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Ge)},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetChannelCategoryWhiteBlackRolesPage",{qchatGetChannelCategoryWhiteBlackRolesPageTag:Object.assign(Object.assign({},e),{type:Ge[e.type]})}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatRoles(r)}}))}updateChannelCategoryWhiteBlackMembers(e){return __awaiter(this,void 0,void 0,(function*(){validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Ge)},opeType:{type:"enum",values:getEnumKeys(Be)},toAccids:{type:"array",itemType:"string",min:1}},e),yield this.core.sendCmd("qchatUpdateChannelCategoryWhiteBlackMembers",{qchatUpdateChannelCategoryWhiteBlackMembersTag:Object.assign(Object.assign({},e),{type:Ge[e.type],opeType:Be[e.opeType],toAccids:JSON.stringify(e.toAccids)})})}))}getChannelCategoryWhiteBlackMembersPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Ge)},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetChannelCategoryWhiteBlackMembersPage",{qchatGetChannelCategoryWhiteBlackMembersPageTag:Object.assign(Object.assign({},e),{type:Ge[e.type]})}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatMembers$1(r)}}))}getChannelCategoryWhiteBlackRoles(e){return __awaiter(this,void 0,void 0,(function*(){validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Ge)},roleIds:{type:"array",itemType:"string",min:1}},e);var t=yield this.core.sendCmd("qchatGetChannelCategoryWhiteBlackRoles",{qchatGetChannelCategoryWhiteBlackRolesTag:Object.assign(Object.assign({},e),{type:Ge[e.type],roleIds:JSON.stringify(e.roleIds)})}),{datas:r}=t.content;return formatRoles(r)}))}getChannelCategoryWhiteBlackMembers(e){return __awaiter(this,void 0,void 0,(function*(){validate({categoryId:{type:"string",allowEmpty:!1},serverId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(Ge)},accids:{type:"array",itemType:"string",min:1}},e);var t=yield this.core.sendCmd("qchatGetChannelCategoryWhiteBlackMembers",{qchatGetChannelCategoryWhiteBlackMembersTag:Object.assign(Object.assign({},e),{type:Ge[e.type],accids:JSON.stringify(e.accids)})}),{datas:r}=t.content;return formatMembers$1(r)}))}getChannelCategoriesPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetChannelCategoriesPage",{qchatGetChannelCategoriesPageTag:e}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatChannelCategorys(r)}}))}getChannelCategoryChannelsPage(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0},limit:{type:"number",min:1,required:!1}},e);var t=yield this.core.sendCmd("qchatGetChannelCategoryChannelsPage",{qchatGetChannelCategoryChannelsPageTag:e}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag)},datas:formatChannels(r)}}))}subscribeChannel(e){return __awaiter(this,void 0,void 0,(function*(){if(validate({type:{type:"number",min:1,max:5},opeType:{type:"number",min:1,max:2}},e),this.config.autoSubscribe&&!e.isInternalTrigger&&5!==e.type)throw new CustomError("subscribe server failed, manual subscribe is not allowed in auto subscribe mode",{},403);var t=yield this.subscribeModuleService.subscribe(e);if(t.unreadInfos=formatUnreadInfos(t.unreadInfos),5!==e.type)return 1===e.opeType&&(e.channels=t.unreadInfos.map((e=>({serverId:e.serverId,channelId:e.channelId})))),this.logger.debug("QChatChannel::subscribeChannel:: cacheSubscribe ",e),this.config.autoSubscribe?this.subscribeModuleService.cacheAutoSubscribe(e):this.subscribeModuleService.cacheSubscribe(e),this.subscribeModuleService.updateUnreads(t.unreadInfos),t;this.subscribeModuleService.cacheSubscribe(e)}))}getChannelUnreadInfos(e){return __awaiter(this,void 0,void 0,(function*(){var t=formatUnreadInfos((yield this.core.sendCmd("qchatGetUnreadInfo",e)).content.unreadInfos);return this.subscribeModuleService.updateUnreads(t),t}))}getChannelSearchByPage(e){return __awaiter(this,void 0,void 0,(function*(){if(validate({keyword:{type:"string",allowEmpty:!1},startTime:{type:"number",min:0,required:!1},endTime:{type:"number",min:1,required:!1},order:{type:"enum",values:getEnumKeys(Ce),required:!1},limit:{type:"number",min:1,required:!1},serverId:{type:"string",required:!1},sort:{type:"enum",values:getEnumKeys(xe),required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},e),e.startTime&&e.endTime&&e.startTime>=e.endTime)throw new ValidateError("startTime more than endTime",e,"timeRule");var t=yield this.core.sendCmd("qchatGetChannelSearchByPage",{qchatGetChannelSearchByPageTag:Object.assign(Object.assign({},e),{order:e.order&&Ce[e.order],sort:e.sort&&xe[e.sort]})}),{datas:r,listQueryTag:i}=t.content;return{listQueryTag:{hasMore:1==+i.hasMore,nextTimetag:parseInt(i.nextTimetag),cursor:i.cursor},datas:formatChannels(r)}}))}channelMemberSearch(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},keyword:{type:"string",allowEmpty:!1},limit:{type:"number",min:1,required:!1}},e),formatChannelMembers((yield this.core.sendCmd("qchatChannelMemberSearch",{qchatChannelMemberSearchTag:e})).content.datas)}))}subscribeAsVisitor(e){return __awaiter(this,void 0,void 0,(function*(){return validate({opeType:{type:"number",min:1,max:2},type:{type:"number",required:!1},channels:{type:"array",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1}},min:1}},e),yield this.subscribeForVisitorService.subscribeChannelAsVisitor(e)}))}qchatAutoSubscribeNotificationHandler(e){var t=formatUnreadInfos(e.content.unreadInfos),r=e.content.serverIds;this.subscribeModuleService.updateUnreads(t);var i=[];if(r.length){var a={opeType:1};a.channels=r.map((e=>({serverId:e.serverId,channelId:""}))),a.type=4,i.push(a)}if(t.length){var n={opeType:1};n.channels=t.map((e=>({serverId:e.serverId,channelId:e.channelId}))),n.type=1,i.push(n)}if(i.length)for(var o of i)this.subscribeModuleService.cacheAutoSubscribe(o)}}var gt={"24_61":"qchatCreateServerRole","24_62":"qchatDeleteServerRole","24_63":"qchatUpdateServerRole","24_64":"qchatGetServerRoles","24_65":"qchatAddChannelRole","24_66":"qchatRemoveChannelRole","24_67":"qchatUpdateChannelRole","24_68":"qchatGetChannelRoles","24_69":"qchatAddMemberRole","24_70":"qchatRemoveMemberRole","24_71":"qchatUpdateMemberRole","24_72":"qchatGetMemberRoles","24_73":"qchatAddMembersToServerRole","24_74":"qchatRemoveMembersFromServerRole","24_75":"qchatGetMembersFromServerRole","24_76":"qchatGetServerRolesByAccid","24_77":"qchatGetExistingServerRolesByAccids","24_78":"qchatGetExistingChannelRolesByServerRoleIds","24_79":"qchatGetExistingAccidsOfMemberRoles","24_80":"qchatUpdateServerRolePriorities","24_81":"qchatGetExistingAccidsInServerRole","24_82":"qchatCheckPermission","24_83":"qchatAddChannelCategoryRole","24_84":"qchatRemoveChannelCategoryRole","24_85":"qchatUpdateChannelCategoryRole","24_86":"qchatGetChannelCategoryRole","24_87":"qchatAddChannelCategoryMemberRole","24_88":"qchatRemoveChannelCategoryMemberRole","24_89":"qchatUpdateChannelCategoryMemberRole","24_90":"qchatGetChannelCategoryMemberRole","24_126":"qchatCheckPermissions"},yt={channelRole:{serverId:1,roleId:2,parentRoleId:3,channelId:4,name:5,icon:6,ext:7,auths:8,type:9,createTime:10,updateTime:11},memberRole:{serverId:1,id:2,accid:3,channelId:4,auths:5,createTime:6,updateTime:7,nick:8,avatar:9,ext:10,memberType:11,joinTime:12,inviter:13},antispamTag:{antiSpamBusinessId:1},serverRole:{serverId:1,roleId:2,name:3,icon:4,ext:5,auths:6,type:7,memberCount:8,priority:9,createTime:10,updateTime:11,isMember:12},getRoleByPagesTag:{serverId:1,channelId:2,time:3,limit:4},checkPermissionTag:{serverId:1,channelId:2,auth:3},member:{serverId:1,roleId:2,accid:3,createTime:4,updateTime:5,nick:6,avatar:7,ext:8,type:9,joinTime:10,inviter:11},qchatAddChannelCategoryRoleTag:{categoryId:3,serverId:4,parentRoleId:5},channelCategoryRole:{roleId:1,categoryId:3,serverId:4,parentRoleId:5,type:6,validFlag:7,createTime:8,updateTime:9,auths:10,name:11,icon:12,ext:13},qchatGetChannelCategoryRoleTag:{serverId:1,categoryId:2,timetag:3,limit:4},channelCategoryMemberRole:{id:1,accid:3,categoryId:4,serverId:5,validFlag:6,createTime:7,updateTime:8,auths:9,nick:10,avatar:11,ext:12,memberType:13,joinTime:14,inviter:15},qchatGetChannelCategoryMemberRoleTag:{serverId:1,categoryId:2,timetag:3,limit:4},checkPermissionsTag:{serverId:1,channelId:2,auths:3}},getDeserializeTag$3=()=>invertSerializeMap(yt);class CategoryModuleService{constructor(e){this.core=e}addChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},parentRoleId:{type:"string",allowEmpty:!1}},e),formatChannelCategoryRole((yield this.core.sendCmd("qchatAddChannelCategoryRole",{qchatAddChannelCategoryRoleTag:e})).content.channelCategoryRole)}))}removeChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatRemoveChannelCategoryRole",e)}))}updateChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},auths:{type:"object"}},e);var t=yield this.core.sendCmd("qchatUpdateChannelCategoryRole",{qchatUpdateChannelCategoryRoleTag:generatorRoleForCmd(e)});if(406===t.raw.code)throw new CustomError("No update required",{},406);return formatChannelCategoryRole(t.content.channelCategoryRole)}))}getChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}},e),function formatChannelCategoryRoles(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatChannelCategoryRole(e))):[]}((yield this.core.sendCmd("qchatGetChannelCategoryRole",{qchatGetChannelCategoryRoleTag:e})).content.list)}))}addChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},e),formatChannelCategoryMemberRole((yield this.core.sendCmd("qchatAddChannelCategoryMemberRole",{qchatAddChannelCategoryMemberRoleTag:e})).content.channelCategoryMemberRole)}))}removeChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatRemoveChannelCategoryMemberRole",e)}))}updateChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},auths:{type:"object"}},e);var t=yield this.core.sendCmd("qchatUpdateChannelCategoryMemberRole",{qchatUpdateChannelCategoryMemberRoleTag:generatorRoleForCmd(e)});if(406===t.raw.code)throw new CustomError("No update required",{},406);return formatChannelCategoryMemberRole(t.content.channelCategoryMemberRole)}))}getChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},categoryId:{type:"string",allowEmpty:!1},timetag:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}},e),function formatChannelCategoryMemberRoles(e){return Array.isArray(e)&&e.length>0?e.map((e=>formatChannelCategoryMemberRole(e))):[]}((yield this.core.sendCmd("qchatGetChannelCategoryMemberRole",{qchatGetChannelCategoryMemberRoleTag:e})).content.list)}))}}class QChatRoleService extends Service{constructor(e){var t;super("qchatRole",e),this.core=e,registerParser({cmdMap:gt,cmdConfig:(t=getDeserializeTag$3(),{qchatCreateServerRole:{service:"qchatRole",sid:24,cid:61,params:[{type:"Property",name:"serverRole",reflectMapper:yt.serverRole},{type:"Property",name:"antispamTag",reflectMapper:yt.antispamTag}],response:[{type:"Property",name:"serverRole",reflectMapper:t.serverRole}]},qchatDeleteServerRole:{service:"qchatRole",sid:24,cid:62,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"}]},qchatUpdateServerRole:{service:"qchatRole",sid:24,cid:63,params:[{type:"Property",name:"updateServerRoleTag",reflectMapper:{serverId:1,roleId:2,name:3,icon:4,ext:5,auths:6,priority:7}},{type:"Property",name:"antispamTag",reflectMapper:yt.antispamTag}],response:[{type:"Property",name:"serverRole",reflectMapper:t.serverRole}]},qchatGetServerRoles:{service:"qchatRole",sid:24,cid:64,params:[{type:"Property",name:"getServerRolesTag",reflectMapper:{serverId:1,timetag:2,limit:3,priority:4,channelId:5,categoryId:6}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:t.serverRole}]},qchatAddChannelRole:{service:"qchatRole",sid:24,cid:65,params:[{type:"Property",name:"channelRole",reflectMapper:yt.channelRole}],response:[{type:"Property",name:"channelRole",reflectMapper:t.channelRole}]},qchatRemoveChannelRole:{service:"qchatRole",sid:24,cid:66,params:[{type:"Long",name:"serverId"},{type:"Long",name:"channelId"},{type:"Long",name:"roleId"}]},qchatUpdateChannelRole:{service:"qchatRole",sid:24,cid:67,params:[{type:"Property",name:"updateChannelRoleTag",reflectMapper:{serverId:1,roleId:2,channelId:3,auths:4}}],response:[{type:"Property",name:"channelRole",reflectMapper:t.channelRole}]},qchatGetChannelRoles:{service:"qchatRole",sid:24,cid:68,params:[{type:"Property",name:"getChannelRolesTag",reflectMapper:{serverId:1,channelId:2,timetag:3,limit:4}}],response:[{type:"PropertyArray",name:"channelRoles",reflectMapper:t.channelRole}]},qchatAddMemberRole:{service:"qchatRole",sid:24,cid:69,params:[{type:"Property",name:"memberRole",reflectMapper:yt.memberRole}],response:[{type:"Property",name:"memberRole",reflectMapper:t.memberRole}]},qchatRemoveMemberRole:{service:"qchatRole",sid:24,cid:70,params:[{type:"Property",name:"memberRole",reflectMapper:yt.memberRole}]},qchatUpdateMemberRole:{service:"qchatRole",sid:24,cid:71,params:[{type:"Property",name:"updateMemberRoleTag",reflectMapper:{serverId:1,accid:2,channelId:3,auths:4}}],response:[{type:"Property",name:"memberRole",reflectMapper:t.memberRole}]},qchatGetMemberRoles:{service:"qchatRole",sid:24,cid:72,params:[{type:"Property",name:"getMemberRolesTag",reflectMapper:{serverId:1,channelId:2,timetag:3,limit:4}}],response:[{type:"PropertyArray",name:"memberRoles",reflectMapper:t.memberRole}]},qchatAddMembersToServerRole:{service:"qchatRole",sid:24,cid:73,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"},{type:"String",name:"accids"}],response:[{type:"Property",name:"accids",reflectMapper:{1:"successAccids",2:"failedAccids"}}]},qchatRemoveMembersFromServerRole:{service:"qchatRole",sid:24,cid:74,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"},{type:"String",name:"accids"}],response:[{type:"Property",name:"accids",reflectMapper:{1:"successAccids",2:"failedAccids"}}]},qchatGetMembersFromServerRole:{service:"qchatRole",sid:24,cid:75,params:[{type:"Property",name:"getMembersFromServerRoleTag",reflectMapper:{serverId:1,roleId:2,timetag:3,accid:4,limit:5}}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.member}]},qchatGetServerRolesByAccid:{service:"qchatRole",sid:24,cid:76,params:[{type:"Property",name:"getServerRolesByAccidTag",reflectMapper:{serverId:1,accid:2,timetag:3,limit:4}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:t.serverRole}]},qchatGetExistingServerRolesByAccids:{service:"qchatRole",sid:24,cid:77,params:[{type:"Long",name:"serverId"},{type:"String",name:"accids"}],response:[{type:"String",name:"serverRoles"}]},qchatGetExistingChannelRolesByServerRoleIds:{service:"qchatRole",sid:24,cid:78,params:[{type:"Long",name:"serverId"},{type:"Long",name:"channelId"},{type:"String",name:"roleIds"}],response:[{type:"PropertyArray",name:"channelRoles",reflectMapper:t.channelRole}]},qchatGetExistingAccidsOfMemberRoles:{service:"qchatRole",sid:24,cid:79,params:[{type:"Long",name:"serverId"},{type:"Long",name:"channelId"},{type:"String",name:"accids"}],response:[{type:"PropertyArray",name:"memberRoles",reflectMapper:t.memberRole}]},qchatUpdateServerRolePriorities:{service:"qchatRole",sid:24,cid:80,params:[{type:"Long",name:"serverId"},{type:"PropertyArray",name:"serverRoles",reflectMapper:{serverId:1,roleId:2,priority:7}}],response:[{type:"PropertyArray",name:"serverRoles",reflectMapper:t.serverRole}]},qchatGetExistingAccidsInServerRole:{service:"qchatRole",sid:24,cid:81,params:[{type:"Long",name:"serverId"},{type:"Long",name:"roleId"},{type:"String",name:"accids"}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.member}]},qchatCheckPermission:{service:"qchatRole",sid:24,cid:82,params:[{type:"Property",name:"checkPermissionTag",reflectMapper:yt.checkPermissionTag}],response:[{type:"Bool",name:"checked"}]},qchatAddChannelCategoryRole:{service:"qchatRole",sid:24,cid:83,params:[{type:"Property",name:"qchatAddChannelCategoryRoleTag",reflectMapper:yt.channelCategoryRole}],response:[{type:"Property",name:"channelCategoryRole",reflectMapper:t.channelCategoryRole}]},qchatRemoveChannelCategoryRole:{service:"qchatRole",sid:24,cid:84,params:[{type:"Long",name:"serverId"},{type:"Long",name:"categoryId"},{type:"Long",name:"roleId"}]},qchatUpdateChannelCategoryRole:{service:"qchatRole",sid:24,cid:85,params:[{type:"Property",name:"qchatUpdateChannelCategoryRoleTag",reflectMapper:yt.channelCategoryRole}],response:[{type:"Property",name:"channelCategoryRole",reflectMapper:t.channelCategoryRole}]},qchatGetChannelCategoryRole:{service:"qchatRole",sid:24,cid:86,params:[{type:"Property",name:"qchatGetChannelCategoryRoleTag",reflectMapper:yt.qchatGetChannelCategoryRoleTag}],response:[{type:"PropertyArray",name:"list",reflectMapper:t.channelCategoryRole}]},qchatAddChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:87,params:[{type:"Property",name:"qchatAddChannelCategoryMemberRoleTag",reflectMapper:yt.channelCategoryMemberRole}],response:[{type:"Property",name:"channelCategoryMemberRole",reflectMapper:t.channelCategoryMemberRole}]},qchatRemoveChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:88,params:[{type:"Long",name:"serverId"},{type:"Long",name:"categoryId"},{type:"String",name:"accid"}]},qchatUpdateChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:89,params:[{type:"Property",name:"qchatUpdateChannelCategoryMemberRoleTag",reflectMapper:yt.channelCategoryMemberRole}],response:[{type:"Property",name:"channelCategoryMemberRole",reflectMapper:t.channelCategoryMemberRole}]},qchatGetChannelCategoryMemberRole:{service:"qchatRole",sid:24,cid:90,params:[{type:"Property",name:"qchatGetChannelCategoryMemberRoleTag",reflectMapper:yt.qchatGetChannelCategoryMemberRoleTag}],response:[{type:"PropertyArray",name:"list",reflectMapper:t.channelCategoryMemberRole}]},qchatCheckPermissions:{service:"qchatRole",sid:24,cid:126,params:[{type:"Property",name:"checkPermissionsTag",reflectMapper:yt.checkPermissionsTag}],response:[{type:"PropertyArray",name:"checkPermissionsResult",reflectMapper:{1:"auth",2:"isAllow"}}]}})}),this.category=new CategoryModuleService(e)}createServerRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},name:{type:"string",allowEmpty:!1},priority:{type:"number",min:1},icon:{type:"string",required:!1},ext:{type:"string",required:!1}},e),formatRole((yield this.core.sendCmd("qchatCreateServerRole",{serverRole:Object.assign(Object.assign({},generatorRoleForCmd(e)),{type:Ye.custom}),antispamTag:generateAntispamTag(e)})).content.serverRole)}))}updateServerRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},name:{type:"string",required:!1},icon:{type:"string",required:!1},ext:{type:"string",required:!1},priority:{type:"number",required:!1},auths:{type:"object",required:!1}},e),formatRole((yield this.core.sendCmd("qchatUpdateServerRole",{updateServerRoleTag:generatorRoleForCmd(e),antispamTag:generateAntispamTag(e)})).content.serverRole)}))}deleteServerRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatDeleteServerRole",e)}))}getServerRoles(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},categoryId:{type:"string",allowEmpty:!1,required:!1},limit:{type:"number",min:1,required:!1},priority:{type:"number",required:!1}},e);var t=yield this.core.sendCmd("qchatGetServerRoles",{getServerRolesTag:generatorRoleForCmd(e)}),r=t.content.serverRoles.filter((e=>1===parseInt(e.isMember)));return r=r.map((e=>e.roleId)),{roles:formatRoles(t.content.serverRoles),isMemberRoles:r}}))}addChannelRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},parentRoleId:{type:"string",allowEmpty:!1}},e),formatRole((yield this.core.sendCmd("qchatAddChannelRole",{channelRole:generatorRoleForCmd(e)})).content.channelRole)}))}getChannelRoles(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},limit:{type:"number",min:1,required:!1}},e),formatRoles((yield this.core.sendCmd("qchatGetChannelRoles",{getChannelRolesTag:e})).content.channelRoles)}))}removeChannelRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatRemoveChannelRole",e)}))}updateChannelRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},auths:{type:"object",required:!1}},e);var t=yield this.core.sendCmd("qchatUpdateChannelRole",{updateChannelRoleTag:generatorRoleForCmd(e)});if(406===t.raw.code)throw new CustomError("No update required",{},406);return formatRole(t.content.channelRole)}))}addMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},e),formatRole((yield this.core.sendCmd("qchatAddMemberRole",{memberRole:e})).content.memberRole)}))}getMemberRoles(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},limit:{type:"number",min:1,required:!1}},e),formatRoles((yield this.core.sendCmd("qchatGetMemberRoles",{getMemberRolesTag:e})).content.memberRoles)}))}removeMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1}},e),yield this.core.sendCmd("qchatRemoveMemberRole",{memberRole:e})}))}updateMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},auths:{type:"object",required:!1}},e);var t=yield this.core.sendCmd("qchatUpdateMemberRole",{updateMemberRoleTag:generatorRoleForCmd(e)});if(406===t.raw.code)throw new CustomError("No update required",{},406);return formatRole(t.content.memberRole)}))}addMembersToServerRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string",min:1}},e);var t=yield this.core.sendCmd("qchatAddMembersToServerRole",Object.assign(Object.assign({},e),{accids:JSON.stringify(e.accids)})),r=get(t,"content.accids.successAccids"),i=get(t,"content.accids.failedAccids");return{successAccids:r?JSON.parse(r):[],failedAccids:i?JSON.parse(i):[]}}))}removeMembersFromServerRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string",min:1}},e);var t=yield this.core.sendCmd("qchatRemoveMembersFromServerRole",Object.assign(Object.assign({},e),{accids:JSON.stringify(e.accids)})),r=get(t,"content.accids.successAccids"),i=get(t,"content.accids.failedAccids");return{successAccids:r?JSON.parse(r):[],failedAccids:i?JSON.parse(i):[]}}))}getMembersFromServerRole(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},accid:{type:"string",allowEmpty:!1,required:!1},limit:{type:"number",min:1,required:!1}},e),formatRoles((yield this.core.sendCmd("qchatGetMembersFromServerRole",{getMembersFromServerRoleTag:e})).content.members)}))}getServerRolesByAccid(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},accid:{type:"string",allowEmpty:!1},timetag:{type:"number",required:!1},limit:{type:"number",min:1,required:!1}},e),formatRoles((yield this.core.sendCmd("qchatGetServerRolesByAccid",{getServerRolesByAccidTag:e})).content.serverRoles)}))}getExistingServerRolesByAccids(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string"}},e);var t=yield this.core.sendCmd("qchatGetExistingServerRolesByAccids",{serverId:e.serverId,accids:JSON.stringify(e.accids)});try{var r=JSON.parse(t.content.serverRoles);return Object.keys(r).forEach((e=>{r[e].forEach(((t,i)=>{r[e][i]=deserialize(t,getDeserializeTag$3().serverRole)})),r[e]=formatRoles(r[e])})),r}catch(r){throw this.logger.error(`can not parse serverRolesGroupByAccid from ${e.serverId} and ${e.accids}`,t.content.serverRoles),r}}))}getExistingChannelRolesByServerRoleIds(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},roleIds:{type:"array",itemType:"string"}},e),formatRoles((yield this.core.sendCmd("qchatGetExistingChannelRolesByServerRoleIds",{serverId:e.serverId,channelId:e.channelId,roleIds:JSON.stringify(e.roleIds)})).content.channelRoles)}))}getExistingAccidsOfMemberRoles(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string"}},e);var t=(yield this.core.sendCmd("qchatGetExistingAccidsOfMemberRoles",{serverId:e.serverId,channelId:e.channelId,accids:JSON.stringify(e.accids)})).content.memberRoles;return t&&t.length>0?t.map((e=>e.accid)):[]}))}getExistingAccidsInServerRole(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},roleId:{type:"string",allowEmpty:!1},accids:{type:"array",itemType:"string"}},e);var t=(yield this.core.sendCmd("qchatGetExistingAccidsInServerRole",{serverId:e.serverId,roleId:e.roleId,accids:JSON.stringify(e.accids)})).content.members;return t&&t.length>0?t.map((e=>e.accid)):[]}))}updateServerRolePriorities(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},serverRoles:{type:"array"}},e),formatRoles((yield this.core.sendCmd("qchatUpdateServerRolePriorities",{serverId:e.serverId,serverRoles:e.serverRoles.map((e=>({serverId:e.serverId,roleId:e.roleId,priority:e.priority})))})).content.serverRoles)}))}checkPermission(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},auth:{type:"string"}},e),(yield this.core.sendCmd("qchatCheckPermission",{checkPermissionTag:Object.assign(Object.assign({},e),{auth:He[e.auth]||e.auth})})).content.checked}))}addChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.addChannelCategoryRole(e)}))}removeChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.removeChannelCategoryRole(e)}))}updateChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.updateChannelCategoryRole(e)}))}getChannelCategoryRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.getChannelCategoryRole(e)}))}addChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.addChannelCategoryMemberRole(e)}))}removeChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.removeChannelCategoryMemberRole(e)}))}updateChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.updateChannelCategoryMemberRole(e)}))}getChannelCategoryMemberRole(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.category.getChannelCategoryMemberRole(e)}))}checkPermissions(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},auths:{type:"array",itemType:"string"}},e),this.core.logger.log("qchatRole::checkPermission options is",e);var t=yield this.core.sendCmd("qchatCheckPermissions",{checkPermissionsTag:Object.assign(Object.assign({},e),{auths:JSON.stringify(e.auths.map((e=>He[e]||e)))})});this.core.logger.log("qchatRole::checkPermission result is",t.content);var r={};return t.content.checkPermissionsResult.forEach((e=>{r[e.auth]=e.isAllow})),this.core.logger.log("qchatRole::checkPermission auths is",r),formatRoleAuths(r)}))}}var _t,vt={"24_10":"qchatSendMsg","24_11":"qchatOnMsg","24_12":"qchatOnRecvUnreadInfo","24_13":"qchatSendCustomSysMsg","24_14":"qchatOnSysMsg","24_16":"qchatGetHistoryMsg","24_17":"qchatMarkMessageRead","24_18":"qchatMultiSyncMessageRead","24_22":"qchatUpdateSystemNotification","24_23":"qchatMultiSyncSystemNotificationUpdate","24_24":"qchatSyncSystemNotification","24_25":"qchatUpdateMessage","24_26":"qchatRecvMessageUpdate","24_28":"qchatMarkSysMsgRead","24_94":"qchatMessageSearchByPage","24_100":"qchatGetMessageHistoryByIds","24_101":"qchatGetThreadMessages","24_102":"qchatUpdateQuickComment","24_103":"qchatGetQuickComments","24_108":"qchatGetThreadRootMessagesMeta","24_121":"qchatGetLastMessageOfChannels","24_127":"qchatGetMentionedMeMessages","25_6":"qchatMultiSyncServersMessageRead"},Et={getHistoryMsgTag:{serverId:1,channelId:2,beginTime:3,endTime:4,excludeMsgId:5,limit:6,reverse:7},getThreadHistoryMsgTag:{beginTime:1,endTime:2,excludeMsgId:3,limit:4,reverse:5},qchatMsgTag:{serverId:1,channelId:2,fromAccount:3,fromClientType:4,fromDeviceId:5,fromNick:6,time:7,updateTime:8,type:9,body:10,attach:11,ext:12,msgIdClient:13,msgIdServer:14,resendFlag:15,status:16,pushPayload:17,pushContent:18,mentionAccids:19,mentionAll:20,env:21,callbackExt:22,replyMsgFromAccount:23,replyMsgTime:24,replyMsgIdServer:25,replyMsgIdClient:26,threadMsgFromAccount:27,threadMsgTime:28,threadMsgIdServer:29,threadMsgIdClient:30,useCustomContent:31,antiSpamContent:32,antiSpamBusinessId:33,antiSpamUsingYidun:34,yidunCallback:35,yidunAntiCheating:36,yidunAntiSpamExt:37,yidunAntiSpamRes:38,mentionRoleIds:41,accidsOfMentionedRoles:42,updateContent:39,updateOperatorInfo:40,subType:61,historyEnable:100,pushEnable:101,needBadge:102,needPushNick:103,notifyReason:104,routeEnable:105,isAntispam:106},sysMsg:{toType:1,serverId:2,channelId:3,toAccids:4,fromAccount:5,fromClientType:6,fromDeviceId:7,fromNick:8,time:9,updateTime:10,type:11,msgIdClient:12,msgIdServer:13,body:14,attach:15,ext:16,resendFlag:17,status:18,pushPayload:19,pushContent:20,env:21,callbackExt:22,persistEnable:100,pushEnable:101,needBadge:102,needPushNick:103,routeEnable:104},qchatMsgUpdateTag:{operatorAccount:1,operatorClientType:2,ps:3,ext:4,pushContent:5,pushPayload:6,env:7,routeEnable:100},markMsgReadTag:{serverId:1,channelId:2,time:3},qchatQuickCommentRequestTag:{serverId:1,channelId:2,fromAccount:3,msgIdServer:4,time:5,type:6,opeType:7,opeAccid:8},qchatQuickCommentQueryTag:{serverId:1,channelId:2,msgIdServerList:3},qchatGetLastMessageOfChannelsTag:{serverId:1,channelIdList:2},qchatMultiSyncServersMessageReadTag:{successServerIds:1,failServerIds:2,ackTimestamp:3},qchatMessageSearchByPageTag:{keyword:1,serverId:2,channelId:3,fromAccid:4,fromTime:5,toTime:6,msgTypes:7,subTypes:8,includeSelf:9,order:10,limit:11,sort:12,cursor:13},qchatPageQueryTag:{hasMore:1,nextTimetag:2,cursor:3},unreadInfo:Ne},getDeserializeTag$2=()=>invertSerializeMap(Et);function pick(e,t){e=e||{};var r={};return(t=t||[]).forEach((t=>{void 0!==e[t]&&(r[t]=e[t])})),r}!function(e){e[e.Android=1]="Android",e[e.iOS=2]="iOS",e[e.PC=4]="PC",e[e.WindowsPhone=8]="WindowsPhone",e[e.Web=16]="Web",e[e.Server=32]="Server",e[e.Mac=64]="Mac",e[e.HarmonyOS=65]="HarmonyOS"}(_t||(_t={}));var It=["image","audio","video","file"],ft={time:{type:"number"},updateTime:{type:"number"},resendFlag:{type:"boolean"},persistEnable:{type:"boolean"},routeEnable:{type:"boolean"},pushEnable:{type:"boolean"},needBadge:{type:"boolean"},needPushNick:{type:"boolean"},type:{type:"enum",values:st},fromClientType:{type:"enum",values:_t},status:{type:"number"},toAccids:{type:"object"},toType:{type:"number"},attach:{type:"object"},pushPayload:{type:"object"}};function generatorSysMsgForCmd(e,t){var r=Object.assign({},e),i=Object.assign({type:t||st.custom},formatReverse(ft,r));return i.serverId&&i.channelId&&i.toAccids?i.toType=4:i.serverId&&i.toAccids?i.toType=3:i.serverId&&i.channelId?i.toType=2:i.serverId?i.toType=1:i.toAccids?i.toType=5:i.toType=0,i}var Mt={[st.channelUpdateWhiteBlackIdentify]:function(e){var t=getDeserializeTag$5();return e.notify=formatUpdateWhiteBlackRole(deserialize(e.notify,t.qchatUpdateWhiteBlackRoleTag)),e},[st.channelUpdateWhiteBlackIdentifyUser]:function(e){var t=getDeserializeTag$5();return e.notify=function formatUpdateWhiteBlackMembers(e){return format(Je,e)}(deserialize(e.notify,t.qchatUpdateWhiteBlackMembersTag)),e},[st.updateQuickComment]:function(e){var t=getDeserializeTag$2();return e.notify=function formatQuickCommentRequest(e){return format(St,e)}(deserialize(e.notify,t.qchatQuickCommentRequestTag)),e},[st.channelCategoryCreate]:function(e){var t=getDeserializeTag$5();return e.categoryInfo=formatChannelCategory(deserialize(e.categoryInfo,t.QChatChannelCategoryInfo)),e},[st.channelCategoryUpdate]:function(e){var t=getDeserializeTag$5();return e.categoryInfo=formatChannelCategory(deserialize(e.categoryInfo,t.QChatChannelCategoryInfo)),e},[st.channelCategoryUpdateWhiteBlackIdentify]:function(e){var t=getDeserializeTag$5();return e.notify=formatUpdateWhiteBlackRole(deserialize(e.notify,t.qchatUpdateChannelCategoryWhiteBlackRoleTag)),e},[st.channelCategoryUpdateWhiteBlackIdentifyUser]:function(e){var t=getDeserializeTag$5();return e.notify=formatUpdateWhiteBlackRole(deserialize(e.notify,t.qchatUpdateChannelCategoryWhiteBlackMembersTag)),e},[st.serverIdentifyAdd]:function(e){var t=getDeserializeTag$3();return e.serverIdentifyInfo=formatRole(deserialize(e.serverIdentifyInfo,t.serverRole)),e},[st.serverIdentifyRemove]:function(e){var t=getDeserializeTag$3();return e.serverIdentifyInfo=formatRole(deserialize(e.serverIdentifyInfo,t.serverRole)),e},[st.serverIdentifyUpdate]:function(e){var t=getDeserializeTag$3();return e.serverIdentifyInfo=formatRole(deserialize(e.serverIdentifyInfo,t.serverRole)),e},[st.channelIdentifyUpdate]:function(e){var t=getDeserializeTag$3();return e.channelIdentifyInfo=formatRole(deserialize(e.channelIdentifyInfo,t.channelRole)),e},[st.userIdentifyUpdate]:function(e){var t=getDeserializeTag$3();return e.userIdentifyInfo=formatRole(deserialize(e.userIdentifyInfo,t.memberRole)),e},[st.myMemberInfoUpdated]:function(e){var t=get(e,"userInfo.name"),r=get(e,"userInfo.icon");return e.updatedInfos=e.reuseServers.map((e=>{var i={serverId:e.serverId};return 1==(1&e.bits)&&"string"==typeof t&&Object.assign(i,{nickChanged:!0,nick:t}),2==(2&e.bits)&&"string"==typeof r&&Object.assign(i,{avatarChanged:!0,avatar:r}),i})),delete e.userInfo,delete e.reuseServers,e}};function formatSystemNotification(e,t){var r=format(ft,e);if(!r.attach)return r;var i=getDeserializeTag$4(),a=getDeserializeTag$5();if(r.attach.serverInfo&&(r.attach.serverInfo=formatServer(deserialize(r.attach.serverInfo,i.serverInfo))),r.attach.channelInfo&&(r.attach.channelInfo=formatChannel(deserialize(r.attach.channelInfo,a.channelInfo))),r.attach.serverMember&&(r.attach.serverMember=formatMember$1(deserialize(r.attach.serverMember,i.memberInfo))),Mt[r.attach.type]&&(r.attach=Mt[r.attach.type](r.attach)),r.attach.updateAuths)try{r.attach.updateAuths=formatRoleAuths(JSON.parse(r.attach.updateAuths))}catch(e){t.error("formatSystemNotification:JSON parse updateAuths error: ",e)}return r.attach.type&&(r.attach.rawType=r.attach.type,r.attach.type=st[r.attach.type]),r}var Ct={type:{type:"enum",values:it},fromClientType:{type:"enum",values:_t},status:{type:"number"},resendFlag:{type:"boolean"},mentionAll:{type:"boolean"},notifyReason:{type:"enum",values:nt},pushEnable:{type:"boolean"},historyEnable:{type:"boolean"},needBadge:{type:"boolean"},needPushNick:{type:"boolean"},routeEnable:{type:"boolean"},time:{type:"number"},updateTime:{type:"number"},mentionAccids:{type:"object"},mentionRoleIds:{type:"object"},accidsOfMentionedRoles:{type:"object"},attach:{type:"object"},pushPayload:{type:"object"},isAntispam:{type:"boolean"},antiSpamInfo:{useCustomContent:{type:"boolean"},antiSpamContent:{type:"string"},antiSpamBusinessId:{type:"string"},antiSpamUsingYidun:{type:"boolean"},yidunCallback:{type:"string"},yidunAntiCheating:{type:"object"},yidunAntiSpamExt:{type:"object"},yidunAntiSpamRes:{type:"string"}},replyRefer:{fromAccount:{type:"string",rawKey:"replyMsgFromAccount"},time:{type:"number",rawKey:"replyMsgTime"},msgIdServer:{type:"string",rawKey:"replyMsgIdServer"},msgIdClient:{type:"string",rawKey:"replyMsgIdClient"}},threadRefer:{fromAccount:{type:"string",rawKey:"threadMsgFromAccount"},time:{type:"number",rawKey:"threadMsgTime"},msgIdServer:{type:"string",rawKey:"threadMsgIdServer"},msgIdClient:{type:"string",rawKey:"threadMsgIdClient"}}};function generatorMsgForCmd(e){var t=__rest(e,["onSendBefore","onUploadStart","onUploadDone","onUploadProgress"]),r=formatReverse(Ct,t);if(r.msgIdClient=e.resendFlag?e.msgIdClient:se(),!r.msgIdClient)throw new FormatError("msgIdClient is required for resend a message","msgIdClient","required");return e.replyMessage&&e.replyMessage.msgIdServer&&(r.replyMsgFromAccount=e.replyMessage.fromAccount,r.replyMsgTime=+e.replyMessage.time,r.replyMsgIdServer=e.replyMessage.msgIdServer,r.replyMsgIdClient=e.replyMessage.msgIdClient,e.replyMessage.threadRefer&&e.replyMessage.threadRefer.msgIdServer?(r.threadMsgFromAccount=e.replyMessage.threadRefer.fromAccount,r.threadMsgTime=+e.replyMessage.threadRefer.time,r.threadMsgIdServer=e.replyMessage.threadRefer.msgIdServer,r.threadMsgIdClient=e.replyMessage.threadRefer.msgIdClient):(r.threadMsgFromAccount=e.replyMessage.fromAccount,r.threadMsgTime=+e.replyMessage.time,r.threadMsgIdServer=e.replyMessage.msgIdServer,r.threadMsgIdClient=e.replyMessage.msgIdClient),delete r.replyMessage),r}function formatMsgOperatorInfo(e){return format({operatorClientType:{type:"enum",values:_t},pushPayload:{type:"object"}},e)}function formatMsg(e,t={},r){var i,a;return __awaiter(this,void 0,void 0,(function*(){var n=format(Ct,e);n.deliveryStatus=t.deliveryStatus?at[t.deliveryStatus]:at[at.success],t.time&&(n.time=t.time);var o=getDeserializeTag$2();if(n.updateContent){var s=JSON.parse(n.updateContent);s=format({status:{type:"number"}},s=deserialize(s,o.qchatMsgTag)),n.updateContent=s}if(n.updateOperatorInfo){var c=JSON.parse(n.updateOperatorInfo);c=formatMsgOperatorInfo(deserialize(c,o.qchatMsgUpdateTag)),n.updateOperatorInfo=c}return""===(null===(i=null==n?void 0:n.threadRefer)||void 0===i?void 0:i.fromAccount)&&delete n.threadRefer,""===(null===(a=null==n?void 0:n.replyRefer)||void 0===a?void 0:a.fromAccount)&&delete n.replyRefer,n.attach&&t.attachUrl&&(n.attach.url=t.attachUrl),yield function formatMsgAttach(e,t){return __awaiter(this,void 0,void 0,(function*(){if(!(It.includes(e.type)&&e.attach&&e.attach.url))return e;if(!t||!t.cloudStorage||"function"!=typeof t.cloudStorage.getPrivateUrl||"function"!=typeof t.cloudStorage.getOriginUrl)return e;if(e.attach.url.indexOf("_im_url=1")<0)return e.attach.url=t.cloudStorage.getPrivateUrl(e.attach.url),e;try{e.attach.url=yield t.cloudStorage.getOriginUrl(e.attach.url)}catch(t){throw new FormatError(`url "${e.attach.url}" parse error`,"message.attach.url","parse error")}return e}))}(n,r)}))}function formatMsgs(e,t={},r){return __awaiter(this,void 0,void 0,(function*(){if(!(Array.isArray(e)&&e.length>0))return[];var i=e.map((e=>formatMsg(e,t,r)));return yield Promise.all(i)}))}var Tt={totalCount:{type:"number"},lastUpdateTime:{type:"number"},details:{type:"object"}};var St={type:{type:"number"},time:{type:"number"},opeType:{type:"number"}};var Rt={includeSelf:{type:"number"},order:{type:"enum",values:Ce},sort:{type:"enum",values:rt},msgTypes:{type:"object"},subTypes:{type:"object"}};var At={hasMore:{type:"boolean"},nextTimetag:{type:"number"},cursor:{type:"string"}};var Nt={6:function(e){return this.core.qchatChannel.subscribeForVisitorService.deleteAutoSetInServerId(e.serverId),!0},16:function(e){return this.core.qchatChannel.subscribeForVisitorService.deleteAutoSetInChannel(e.serverId,e.channelId),!0},26:function(e){var t=get(e,"attach.addAccids");return t&&t.includes(this.core.account)&&this.core.eventBus.emit("qchatChannel/serverIdentifyChange",e),!0},27:function(e){var t=get(e,"attach.deleteAccids");return t&&t.includes(this.core.account)&&this.core.eventBus.emit("qchatChannel/serverIdentifyChange",e),!0},31:function(e){var t,r;return 1===e.attach.event?(null===(r=null===(t=this.core.qchatChannel)||void 0===t?void 0:t.config)||void 0===r?void 0:r.autoSubscribe)&&this.core.qchatChannel.subscribeChannel({type:1,opeType:1,channels:[{serverId:e.serverId,channelId:e.channelId}],isInternalTrigger:!0}):2===e.attach.event&&(this.core.eventBus.emit("qchatChannel/autoUnSubscribe",e),this.core.eventBus.emit("qchatMedia/serverOrChannelLeave",e)),!0},32:function(e){var t,r;return 2===e.attach.event?(this.core.eventBus.emit("qchatChannel/autoUnSubscribe",e),this.core.eventBus.emit("qchatMedia/serverOrChannelLeave",e)):1===e.attach.event&&(this.core.qchatChannel.subscribeForVisitorService.deleteServer(e.serverId),(null===(r=null===(t=this.core.qchatChannel)||void 0===t?void 0:t.config)||void 0===r?void 0:r.autoSubscribe)&&this.core.qchatServer.subscribeServer({type:4,opeType:1,servers:[{serverId:e.serverId}],isInternalTrigger:!0})),!0},34:function(e){return 2===e.attach.event&&this.core.qchatChannel.subscribeForVisitorService.unSubscribeChannel(e.serverId,e.channelId),!0},101:function(e){var t=pick(e,["serverId","channelId","ext","fromAccount","fromNick","time"]);return this.logger.log("qchat on recvTypingEvent: ",t),this.core.emit("recvTypingEvent",t),this.core.qchatMsg.emit("recvTypingEvent",t),!1}};class NotificationModuleService{constructor(e){this.core=e,this.logger=e.logger}sendSystemNotification(e){return __awaiter(this,void 0,void 0,(function*(){var t=formatSystemNotification((yield this.core.sendCmd("qchatSendCustomSysMsg",{sysMsg:generatorSysMsgForCmd(e)})).content.sysMsg,this.logger);return this.logger.getDebugMode()?this.logger.debug("sendCustomSysMsg success",t):this.logger.log("sendCustomSysMsg success",t.serverId,t.channelId,t.msgIdServer),t}))}updateSystemNotification(e){return __awaiter(this,void 0,void 0,(function*(){var{systemNotification:t}=e,r=__rest(e,["systemNotification"]),i=generatorSysMsgForCmd(pick(t,["msgIdServer","type","body","ext","status"]));return formatSystemNotification((yield this.core.sendCmd("qchatUpdateSystemNotification",{sysMsg:i,qchatMsgUpdateTag:Object.assign(Object.assign({},r),{operatorAccount:this.core.account,operatorClientType:_t.Web,pushPayload:JSON.stringify(e.pushPayload),routeEnable:qe.boolean(e,"routeEnable")})})).content.sysMsg,this.logger)}))}markSystemNotificationsRead(e){return __awaiter(this,void 0,void 0,(function*(){yield this.core.sendCmd("qchatMarkSysMsgRead",{sysMsgs:e.systemNotifications.map((e=>function generatorSysMsgMarkersForCmd(e){var t=pick(e,["msgIdServer","type"]);return formatReverse(ft,t)}(e)))})}))}onSysMsg(e){var t=e.content.sysMsg;if(t){var r=formatSystemNotification(t,this.core.logger),i=Nt[st[r.type]];if(i)if(!1===i.call(this,r))return;var a={feature:"default",systemNotifications:[r]};this.logger.log("qchat on systemNotification: ",a),this.core.emit("systemNotification",a),this.core.qchatMsg.emit("systemNotification",a)}else this.core.logger.warn("No sysMsg in onSysMsg packet")}onMultiSysMsg(e){var t=e.content.sysMsg;if(t){var r=formatSystemNotification(t,this.logger);this.core.emit("systemNotificationUpdate",r),this.core.qchatMsg.emit("systemNotificationUpdate",r)}}onSyncSysMsg(e){var t=e.content.systemNotifications;if(t&&t.length>0){var r=t.map((e=>formatSystemNotification(e,this.logger)));this.core.emit("systemNotification",{feature:"sync",systemNotifications:r}),this.core.qchatMsg.emit("systemNotification",{feature:"sync",systemNotifications:r})}else this.logger.warn("sync system notification not exist")}}var bt=["image","audio","video","file"];class MessageModuleService{constructor(e){this.markMessageRead=throttle((e=>__awaiter(this,void 0,void 0,(function*(){var t=formatUnreadInfo((yield this.core.sendCmd("qchatMarkMessageRead",{markMsgReadTag:e})).content.unreadInfo);this.core.eventBus.emit("qchatChannel/updateUnreads",[t])}))),200),this.core=e,this.logger=e.logger}sendMessage(e){var t,r,i,a;return __awaiter(this,void 0,void 0,(function*(){bt.indexOf(e.type)>-1&&(e.attach=yield this.doSendFile(e));var n,o=generatorMsgForCmd(Object.assign({fromAccount:this.core.account},e)),s=yield formatMsg(o,{time:(new Date).getTime(),deliveryStatus:at.sending},this.core);try{e.onSendBefore&&e.onSendBefore(s)}catch(e){this.logger.error("sendMsg: options.onSendBefore error",e)}try{n=yield this.core.sendCmd("qchatSendMsg",{qchatMsg:o})}catch(e){var c=yield formatMsg(o,{time:(new Date).getTime(),deliveryStatus:at.failed,attachUrl:null===(t=s.attach)||void 0===t?void 0:t.url},this.core);throw e.msg=c,e}var{content:l}=n,d=yield formatMsg(Object.assign({},l.qchatMsg),{deliveryStatus:at.success,attachUrl:null===(r=s.attach)||void 0===r?void 0:r.url},this.core);if(d.isAntispam)throw{code:10403,msg:d,message:(null===(i=null==d?void 0:d.antiSpamInfo)||void 0===i?void 0:i.yidunAntiSpamRes)||"message has be antispam",isAntispam:!0,yidunAntiSpamRes:(null===(a=null==d?void 0:d.antiSpamInfo)||void 0===a?void 0:a.yidunAntiSpamRes)||""};return d}))}doSendFile(e){return __awaiter(this,void 0,void 0,(function*(){validate({type:{type:"enum",values:["image","audio","video","file"]},attach:{type:"object",rules:{url:{type:"string",allowEmpty:!1}},required:!1},maxSize:{type:"number",min:1,required:!1}},e);var t=e.attach;if(!t){if(!this.core.cloudStorage||!this.core.cloudStorage.uploadFile)throw new Error('Service "cloudStorage" does not exist');try{t=yield this.core.cloudStorage.uploadFile(e)}catch(e){throw this.logger.error("sendFile:: upload File error or abort.",e),e}}return t}))}resendMessage(e){var t,r;return __awaiter(this,void 0,void 0,(function*(){var i,a=generatorMsgForCmd(Object.assign(Object.assign({},e),{resendFlag:!0}));a.deliveryStatus=at.sending;try{i=yield this.core.sendCmd("qchatSendMsg",{qchatMsg:a})}catch(e){var n=yield formatMsg(a,{time:(new Date).getTime(),deliveryStatus:at.failed},this.core);throw e.msg=n,e}var{content:o}=i,s=yield formatMsg(Object.assign({},o.qchatMsg),{deliveryStatus:at.success},this.core);if(s.isAntispam)throw{code:10403,msg:s,message:(null===(t=null==s?void 0:s.antiSpamInfo)||void 0===t?void 0:t.yidunAntiSpamRes)||"message has be antispam",isAntispam:!0,yidunAntiSpamRes:(null===(r=null==s?void 0:s.antiSpamInfo)||void 0===r?void 0:r.yidunAntiSpamRes)||""};return s}))}doUpdateMessage(e){var t,r;return __awaiter(this,void 0,void 0,(function*(){var{message:i}=e,a=__rest(e,["message"]),n=generatorMsgForCmd(i);n.msgIdClient=void 0,n.time=i.time;var o=yield this.core.sendCmd("qchatUpdateMessage",{qchatMsg:n,qchatMsgUpdateTag:Object.assign(Object.assign({},a),{operatorAccount:this.core.account,operatorClientType:_t.Web,pushPayload:JSON.stringify(e.pushPayload),routeEnable:qe.boolean(e,"routeEnable")})}),s=yield formatMsg(o.content.qchatMsg,{},this.core);if(this.core.eventBus.emit("qchatChannel/changeUnread",s,!0),o.content.qchatMsg.isAntispam)throw{code:10403,msg:n,message:(null===(t=o.content.qchatMsg)||void 0===t?void 0:t.yidunAntiSpamRes)||"message has be antispam",isAntispam:!0,yidunAntiSpamRes:(null===(r=o.content.qchatMsg)||void 0===r?void 0:r.yidunAntiSpamRes)||""};return s}))}getHistoryMessage(e){return __awaiter(this,void 0,void 0,(function*(){var t=yield this.core.sendCmd("qchatGetHistoryMsg",{getHistoryMsgTag:Object.assign(Object.assign({},e),{reverse:e.reverse?1:0})}),{content:r}=t;return yield Promise.all(r.qchatMsgs.map((e=>formatMsg(e,{},this.core))))}))}getMentionedMeMessages(e){return __awaiter(this,void 0,void 0,(function*(){var t=yield this.core.sendCmd("qchatGetMentionedMeMessages",{tag:e}),{content:r}=t;return{pageInfo:function formatPageInfo(e){return format(At,e)}(r.page),messages:yield Promise.all(r.datas.map((e=>formatMsg(e,{},this.core))))}}))}areMentionedMeMessages(e){return __awaiter(this,void 0,void 0,(function*(){for(var t={},r=0;r<e.length;r++){var i=e[r];i.fromAccount!==this.core.account?t[i.msgIdClient]=yield this.core.qchatChannel.subscribeModuleService.getMentionedFlag(i,!0):t[i.msgIdClient]=!1}return t}))}getLastMessageOfChannels(e){return __awaiter(this,void 0,void 0,(function*(){var t=(yield this.core.sendCmd("qchatGetLastMessageOfChannels",{tag:e})).content.msgs;return(yield formatMsgs(t,{},this.core)).reduce(((e,t)=>(e[t.channelId]=t,e)),{})}))}messageSearchByPage(e){return __awaiter(this,void 0,void 0,(function*(){var t,r=yield this.core.sendCmd("qchatMessageSearchByPage",{qchatMessageSearchByPageTag:Object.assign(Object.assign({},(t=e,formatReverse(Rt,t))),{includeSelf:e.includeSelf?1:""})}),{datas:i,listQueryTag:a}=r.content,n=yield formatMsgs(i,{},this.core);return{listQueryTag:{hasMore:1==+a.hasMore,nextTimetag:a.nextTimetag?parseInt(a.nextTimetag):0,cursor:a.cursor},datas:n}}))}onMsg(e){return __awaiter(this,void 0,void 0,(function*(){var t=e.content.qchatMsg;if(t){var r=yield formatMsg(t,{},this.core);this.core.eventBus.emit("qchatChannel/changeUnread",r,!1),this.core.emit("message",r),this.core.qchatMsg.emit("message",r)}else this.logger.warn("No qchatMsg in qchatMsg packet")}))}onRecvUnreadInfo(e){return __awaiter(this,void 0,void 0,(function*(){var t=e.content.qchatMsg;if(t){var r=yield formatMsg(t);this.core.eventBus.emit("qchatChannel/changeUnread",r,!1)}}))}onMultiSyncRead(e){var t=e.content.unreadInfo;if(t){var r=formatUnreadInfo(t);this.core.eventBus.emit("qchatChannel/updateUnreads",[r])}}onMultiSyncServersRead(e){var t=e.content.qchatMultiSyncServersMessageReadTag;if(t){var{successServerIds:r,ackTimestamp:i}=formatClearServersUnread(t);this.core.eventBus.emit("qchatChannel/clearUnreadCountByServers",r,i)}}onRecvMsgUpdate(e){return __awaiter(this,void 0,void 0,(function*(){var t=e.content.qchatMsg,r=e.content.qchatMsgUpdateInfo;if(t){var i=yield formatMsg(t,{},this.core),a=formatMsgOperatorInfo(r);this.core.eventBus.emit("qchatChannel/changeUnread",i,!0),this.core.emit("messageUpdate",i,a),this.core.qchatMsg.emit("messageUpdate",i,a)}}))}}class ExtendModuleService{constructor(e){this._sendTypingEvent=throttle((e=>__awaiter(this,void 0,void 0,(function*(){yield this.core.sendCmd("qchatSendCustomSysMsg",{sysMsg:generatorSysMsgForCmd(Object.assign(Object.assign({},e),{resendFlag:!1,persistEnable:!1,routeEnable:!1,pushEnable:!1,needBadge:!1,needPushNick:!1}),st.msgTyping)})}))),3e3),this.core=e,this.logger=e.logger,this.lastChannelIdInTyping=""}getMessageHistoryByIds(e){return __awaiter(this,void 0,void 0,(function*(){var t=yield this.core.sendCmd("qchatGetMessageHistoryByIds",{channelInfo:{serverId:e.serverId,channelId:e.channelId},messageReferList:e.messageReferList});return Promise.all(t.content.qchatMsgs.map((e=>formatMsg(e))))}))}getReferMessages(e){var t,r;return __awaiter(this,void 0,void 0,(function*(){if(!(null===(t=e.message.replyRefer)||void 0===t?void 0:t.msgIdServer)||!(null===(r=e.message.threadRefer)||void 0===r?void 0:r.msgIdServer))throw new Error("Message has no reply");var i=yield this.getMessageHistoryByIds({serverId:e.message.serverId,channelId:e.message.channelId,messageReferList:[e.message.replyRefer,e.message.threadRefer]});return e.referType===ct[ct.all]?{replyMessage:i[0],threadMessage:i[1]}:e.referType===ct[ct.reply]?{replyMessage:i[0]}:e.referType===ct[ct.thread]?{threadMessage:i[1]}:{replyMessage:i[0],threadMessage:i[1]}}))}getThreadMessages(e){var t;return __awaiter(this,void 0,void 0,(function*(){var r;r=(null===(t=e.message.threadRefer)||void 0===t?void 0:t.msgIdServer)?Object.assign({serverId:e.message.serverId,channelId:e.message.channelId},e.message.threadRefer):e.message;var i=yield this.core.sendCmd("qchatGetThreadMessages",{qchatMsg:r,getThreadHistoryMsgTag:Object.assign(Object.assign({},e.messageQueryOption),{reverse:qe.boolean(e.messageQueryOption,"reverse")})}),a=yield formatMsg(i.content.thread),n=yield Promise.all(i.content.qchatMsgs.map((e=>formatMsg(e))));return{thread:a,threadInfo:{messageCount:+i.content.threadInfo.messageCount,lastMessageTimestamp:+i.content.threadInfo.lastMessageTimestamp},messages:n}}))}getThreadRootMessagesMeta(e){return __awaiter(this,void 0,void 0,(function*(){var{threadRootMessages:t}=e,r=__rest(e,["threadRootMessages"]),i=(yield this.core.sendCmd("qchatGetThreadRootMessagesMeta",{qchatMsgs:t,tag:r})).content.metas;return i&&i.length>0?i.map((e=>Object.assign(Object.assign({},e),{total:parseInt(e.total),timestamp:parseInt(e.timestamp),msgTime:parseInt(e.msgTime)}))):[]}))}getQuickComments(e){return __awaiter(this,void 0,void 0,(function*(){var t=yield this.core.sendCmd("qchatGetQuickComments",{tag:{serverId:e.serverId,channelId:e.channelId,msgIdServerList:JSON.stringify(e.msgList.map((e=>e.msgIdServer)))}}),{quickCommentResponse:r}=t.content;return function formatQuickComments(e){var t=e.map((e=>{var t=format(Tt,e);return t.details=t.details.map((e=>{var t=e.createTime?parseInt(e.createTime):0;return t=isNaN(t)?0:t,{count:e.count,hasSelf:e.self,severalAccids:e.topN,type:e.type,createTime:t}})),t})),r={};return t.forEach((e=>r[e.msgIdServer]=e)),r}(r)}))}updateQuickComment(e,t){return __awaiter(this,void 0,void 0,(function*(){validate({type:{type:"number"},commentMessage:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1}}}},e);var{serverId:r,channelId:i,fromAccount:a,msgIdServer:n,time:o}=e.commentMessage;yield this.core.sendCmd("qchatUpdateQuickComment",{tag:{serverId:r,channelId:i,fromAccount:a,msgIdServer:n,time:o,type:e.type,opeType:t,opeAccid:this.core.account}})}))}sendTypingEvent(e){return __awaiter(this,void 0,void 0,(function*(){if(e.channelId===this.lastChannelIdInTyping)return this.logger.log("qchatMsg sendTypingEvent throttle"),void this._sendTypingEvent(e);this.lastChannelIdInTyping=e.channelId,this._sendTypingEvent.flush(),this._sendTypingEvent(e)}))}}class QChatMsgService extends Service{constructor(e){var t;super("qchatMsg",e),this.core=e,this.lastChannelIdInMark="",registerParser({cmdMap:vt,cmdConfig:(t=getDeserializeTag$2(),{qchatSendMsg:{service:"qchatMsg",sid:24,cid:10,params:[{type:"Property",name:"qchatMsg",reflectMapper:Et.qchatMsgTag}],response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatOnMsg:{service:"qchatMsg",sid:24,cid:11,response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatOnRecvUnreadInfo:{service:"qchatMsg",sid:24,cid:12,response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatSendCustomSysMsg:{service:"qchatMsg",sid:24,cid:13,params:[{type:"Property",name:"sysMsg",reflectMapper:Et.sysMsg}],response:[{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatOnSysMsg:{service:"qchatMsg",sid:24,cid:14,response:[{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatGetHistoryMsg:{service:"qchatMsg",sid:24,cid:16,params:[{type:"Property",name:"getHistoryMsgTag",reflectMapper:Et.getHistoryMsgTag}],response:[{type:"PropertyArray",name:"qchatMsgs",reflectMapper:t.qchatMsgTag}]},qchatUpdateMessage:{service:"qchatMsg",sid:24,cid:25,params:[{type:"Property",name:"qchatMsgUpdateTag",reflectMapper:Et.qchatMsgUpdateTag},{type:"Property",name:"qchatMsg",reflectMapper:Et.qchatMsgTag}],response:[{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatRecvMessageUpdate:{service:"qchatMsg",sid:24,cid:26,response:[{type:"Property",name:"qchatMsgUpdateInfo",reflectMapper:t.qchatMsgUpdateTag},{type:"Property",name:"qchatMsg",reflectMapper:t.qchatMsgTag}]},qchatMarkMessageRead:{sid:24,cid:17,service:"qchatMsg",params:[{type:"Property",name:"markMsgReadTag",reflectMapper:Et.markMsgReadTag}],response:[{type:"Property",name:"unreadInfo",reflectMapper:t.unreadInfo}]},qchatMultiSyncMessageRead:{sid:24,cid:18,service:"qchatMsg",response:[{type:"Property",name:"unreadInfo",reflectMapper:t.unreadInfo}]},qchatUpdateSystemNotification:{service:"qchatMsg",sid:24,cid:22,params:[{type:"Property",name:"qchatMsgUpdateTag",reflectMapper:Et.qchatMsgUpdateTag},{type:"Property",name:"sysMsg",reflectMapper:Et.sysMsg}],response:[{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatMultiSyncSystemNotificationUpdate:{service:"qchatMsg",sid:24,cid:23,response:[{type:"Property",name:"qchatMsgUpdateTag",reflectMapper:t.qchatMsgUpdateTag},{type:"Property",name:"sysMsg",reflectMapper:t.sysMsg}]},qchatSyncSystemNotification:{service:"qchatMsg",sid:24,cid:24,response:[{type:"PropertyArray",name:"systemNotifications",reflectMapper:t.sysMsg}]},qchatMarkSysMsgRead:{service:"qchatMsg",sid:24,cid:28,params:[{type:"PropertyArray",name:"sysMsgs",reflectMapper:Et.sysMsg}]},qchatMessageSearchByPage:{service:"qchatMsg",sid:24,cid:94,params:[{type:"Property",name:"qchatMessageSearchByPageTag",reflectMapper:Et.qchatMessageSearchByPageTag}],response:[{type:"Property",name:"listQueryTag",reflectMapper:t.qchatPageQueryTag},{type:"PropertyArray",name:"datas",reflectMapper:t.qchatMsgTag}]},qchatGetMessageHistoryByIds:{service:"qchatMsg",sid:24,cid:100,params:[{type:"Property",name:"channelInfo",reflectMapper:{serverId:1,channelId:2}},{type:"PropertyArray",name:"messageReferList",reflectMapper:{msgIdServer:1,time:2}}],response:[{type:"PropertyArray",name:"qchatMsgs",reflectMapper:t.qchatMsgTag}]},qchatGetThreadMessages:{service:"qchatMsg",sid:24,cid:101,params:[{type:"Property",name:"qchatMsg",reflectMapper:Et.qchatMsgTag},{type:"Property",name:"getThreadHistoryMsgTag",reflectMapper:Et.getThreadHistoryMsgTag}],response:[{type:"Property",name:"thread",reflectMapper:t.qchatMsgTag},{type:"Property",name:"threadInfo",reflectMapper:{1:"messageCount",2:"lastMessageTimestamp"}},{type:"PropertyArray",name:"qchatMsgs",reflectMapper:t.qchatMsgTag}]},qchatUpdateQuickComment:{service:"qchatMsg",sid:24,cid:102,params:[{type:"Property",name:"tag",reflectMapper:Et.qchatQuickCommentRequestTag}]},qchatGetQuickComments:{service:"qchatMsg",sid:24,cid:103,params:[{type:"Property",name:"tag",reflectMapper:Et.qchatQuickCommentQueryTag}],response:[{type:"PropertyArray",name:"quickCommentResponse",reflectMapper:{1:"serverId",2:"channelId",3:"msgIdServer",4:"totalCount",5:"lastUpdateTime",6:"details"}}]},qchatGetThreadRootMessagesMeta:{service:"qchatMsg",sid:24,cid:108,params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,channelId:2}},{type:"PropertyArray",name:"qchatMsgs",reflectMapper:{time:7,msgIdServer:14}}],response:[{type:"PropertyArray",name:"metas",reflectMapper:{1:"total",2:"timestamp",3:"msgIdServer",4:"msgTime"}}]},qchatGetLastMessageOfChannels:{service:"qchatMsg",sid:24,cid:121,params:[{type:"Property",name:"tag",reflectMapper:Et.qchatGetLastMessageOfChannelsTag}],response:[{type:"PropertyArray",name:"msgs",reflectMapper:t.qchatMsgTag}]},qchatMultiSyncServersMessageRead:{service:"qchatMsg",sid:25,cid:6,response:[{type:"Property",name:"qchatMultiSyncServersMessageReadTag",reflectMapper:t.qchatMultiSyncServersMessageReadTag}]},qchatGetMentionedMeMessages:{service:"qchatMsg",sid:24,cid:127,params:[{type:"Property",name:"tag",reflectMapper:{serverId:1,channelId:2,timestamp:3,limit:4}}],response:[{type:"Property",name:"page",reflectMapper:t.qchatPageQueryTag},{type:"PropertyArray",name:"datas",reflectMapper:t.qchatMsgTag}]}})}),this.notificationModuleService=new NotificationModuleService(e),this.messageModuleService=new MessageModuleService(e),this.extendModuleService=new ExtendModuleService(e)}sendMessage(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(it)},ext:{type:"string",required:!1},mentionAll:{type:"boolean",required:!1},mentionAccids:{type:"array",itemType:"string",required:!1},mentionRoleIds:{type:"array",itemType:"string",required:!1,min:1},historyEnable:{type:"boolean",required:!1},pushEnable:{type:"boolean",required:!1}},e),yield this.messageModuleService.sendMessage(e)}))}updateMessage(e){return __awaiter(this,void 0,void 0,(function*(){if(validate({message:{type:"object",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number",min:0},body:{type:"string",required:!1},ext:{type:"string",required:!1}}}},e),"number"==typeof e.message.status&&e.message.status<1e4)throw new CustomError("Status should be greater than or equal to 10000");return yield this.messageModuleService.doUpdateMessage(e)}))}resendMessage(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdClient:{type:"string",allowEmpty:!1}},e),yield this.messageModuleService.resendMessage(e)}))}deleteMessage(e){return __awaiter(this,void 0,void 0,(function*(){validate({message:{type:"object",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number",min:0}}}},e);var t=JSON.parse(JSON.stringify(e));return t.message.status=2,yield this.messageModuleService.doUpdateMessage(t)}))}revokeMessage(e){return __awaiter(this,void 0,void 0,(function*(){validate({message:{type:"object",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number",min:0}}}},e);var t=JSON.parse(JSON.stringify(e));return t.message=pick(t.message,["serverId","channelId","msgIdServer","time"]),t.message.status=1,yield this.messageModuleService.doUpdateMessage(t)}))}markMessageRead(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string"},channelId:{type:"string"},time:{type:"number",min:0}},e),e.channelId!==this.lastChannelIdInMark&&this.messageModuleService.markMessageRead.flush(),this.lastChannelIdInMark=e.channelId,this.messageModuleService.markMessageRead(e)}))}replyMessage(e){validate({replyMessage:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1}}}},e);var t=e.replyMessage;if(e.serverId!==t.serverId||e.channelId!==t.channelId)throw new CustomError("Forbid replying to message from other server, channel");return this.sendMessage(e)}getMessageHistoryByIds(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},messageReferList:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}}},e),yield this.extendModuleService.getMessageHistoryByIds(e)}))}getReferMessages(e){return __awaiter(this,void 0,void 0,(function*(){return validate({message:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}},referType:{type:"enum",values:getEnumKeys(ct),required:!1}},e),yield this.extendModuleService.getReferMessages(e)}))}getThreadMessages(e){return __awaiter(this,void 0,void 0,(function*(){return validate({message:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}},messageQueryOption:{type:"object",rules:{beginTime:{type:"number",required:!1},endTime:{type:"number",required:!1},excludeMsgId:{type:"string",required:!1},limit:{type:"number",required:!1},reverse:{type:"boolean",required:!1}}}},e),yield this.extendModuleService.getThreadMessages(e)}))}getThreadRootMessagesMeta(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},threadRootMessages:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1},time:{type:"number"}}}},e),yield this.extendModuleService.getThreadRootMessagesMeta(e)}))}sendSystemNotification(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1,required:!1},attach:{type:"object",required:!1}},e),yield this.notificationModuleService.sendSystemNotification(e)}))}updateSystemNotification(e){return __awaiter(this,void 0,void 0,(function*(){return validate({systemNotification:{type:"object",rules:{msgIdServer:{type:"string",allowEmpty:!1},type:{type:"enum",values:getEnumKeys(st)},body:{type:"string",required:!1},ext:{type:"string",required:!1},status:{type:"number",required:!1}}}},e),yield this.notificationModuleService.updateSystemNotification(e)}))}markSystemNotificationsRead(e){return __awaiter(this,void 0,void 0,(function*(){validate({systemNotifications:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1},type:{type:"string",allowEmpty:!1}},min:1}},e),yield this.notificationModuleService.markSystemNotificationsRead(e)}))}getHistoryMessage(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},beginTime:{type:"number",min:0,required:!1},endTime:{type:"number",min:0,required:!1},excludeMsgId:{type:"string",allowEmpty:!1,required:!1},limit:{type:"number",min:1,required:!1},reverse:{type:"boolean",required:!1}},e),yield this.messageModuleService.getHistoryMessage(e)}))}getMentionedMeMessages(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},timestamp:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}},e),yield this.messageModuleService.getMentionedMeMessages(e)}))}areMentionedMeMessages(e){return __awaiter(this,void 0,void 0,(function*(){if(validate({messages:{type:"array",rules:{serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgIdServer:{type:"string",allowEmpty:!1}},min:1}},e),!e.messages.every((t=>e.messages[0].serverId===t.serverId)))throw new CustomError("Different serverId",e,10414);if(this.core.qchatChannel.subscribeForVisitorService.isInAutoServers(e.messages[0].serverId))throw new CustomError("Not allowed for visitor",e,10403);return yield this.messageModuleService.areMentionedMeMessages(e.messages)}))}addQuickComment(e){return __awaiter(this,void 0,void 0,(function*(){yield this.extendModuleService.updateQuickComment(e,1)}))}removeQuickComment(e){return __awaiter(this,void 0,void 0,(function*(){yield this.extendModuleService.updateQuickComment(e,2)}))}getQuickComments(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},msgList:{type:"array",rules:{msgIdServer:{type:"string",allowEmpty:!1}},min:1}},e),yield this.extendModuleService.getQuickComments(e)}))}sendTypingEvent(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},ext:{type:"string",required:!1}},e),yield this.extendModuleService.sendTypingEvent(e)}))}getLastMessageOfChannels(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelIdList:{type:"array",itemType:"string"}},e),yield this.messageModuleService.getLastMessageOfChannels(e)}))}qchatOnMsgHandler(e){return __awaiter(this,void 0,void 0,(function*(){this.messageModuleService.onMsg(e)}))}qchatOnRecvUnreadInfoHandler(e){return __awaiter(this,void 0,void 0,(function*(){this.messageModuleService.onRecvUnreadInfo(e)}))}qchatOnSysMsgHandler(e){this.notificationModuleService.onSysMsg(e)}qchatMultiSyncMessageReadHandler(e){this.messageModuleService.onMultiSyncRead(e)}qchatMultiSyncSystemNotificationUpdateHandler(e){this.notificationModuleService.onMultiSysMsg(e)}qchatSyncSystemNotificationHandler(e){this.notificationModuleService.onSyncSysMsg(e)}qchatRecvMessageUpdateHandler(e){return __awaiter(this,void 0,void 0,(function*(){this.messageModuleService.onRecvMsgUpdate(e)}))}searchMsgByPage(e){return __awaiter(this,void 0,void 0,(function*(){return validate({keyword:{type:"string",allowEmpty:!1,required:!1},serverId:{type:"string",allowEmpty:!1,required:!0},channelId:{type:"string",allowEmpty:!1,required:!1},fromAccid:{type:"string",allowEmpty:!1,required:!1},fromTime:{type:"number",allowEmpty:!1,required:!1},toTime:{type:"number",allowEmpty:!1,required:!1},msgTypes:{type:"array",itemType:"string",allowEmpty:!1,required:!0},subTypes:{type:"array",itemType:"string",allowEmpty:!1,required:!1},includeSelf:{type:"boolean",allowEmpty:!1,required:!1},order:{type:"enum",values:getEnumKeys(Ce),required:!1},limit:{type:"number",min:0,required:!1},sort:{type:"enum",values:getEnumKeys(rt),required:!1},cursor:{type:"string",allowEmpty:!1,required:!1}},e),yield this.messageModuleService.messageSearchByPage(e)}))}qchatMultiSyncServersMessageReadHandler(e){this.messageModuleService.onMultiSyncServersRead(e)}}var Ot={serverId:{type:"string"},uid:{type:"string"},accid:{type:"string"},nick:{type:"string"},avatar:{type:"string"},ext:{type:"string"},type:{type:"number"},joinTime:{type:"number"},inviter:{type:"string"},validFlag:{type:"boolean"},createTime:{type:"number"},updateTime:{type:"number"}},Pt={limit:{type:"number"}};function formatMembers(e){return Array.isArray(e)&&e.length>0?e.map((e=>function formatMember(e){return format(Ot,e)}(e))):[]}var wt={"25_1":"qchatGetNeroomToken","25_2":"qchatUpdateRTCChannelConfig","25_3":"qchatGetRTCChannelConfig","25_4":"qchatGetRTCChannelMembers"},kt={QChatRTCChannelConfigTag:{serverId:1,channelId:2,limit:3,audio:4,video:5},QChatRTCChannelConfigResultTag:{limit:1,audio:2,video:3},QChatGetRTCParams:{serverId:1,channelId:2},qchatGetRTCChannelMembersTag:{serverId:1,channelId:2},memberInfo:{serverId:1,accid:3,nick:4,avatar:5,ext:6,type:7,joinTime:8,inviter:9,validFlag:10,createTime:11,updateTime:12},QChatGetNeroomTokenResultTag:{token:1,expire:2},QChatGetNeroomTokenParams:{neroomDeviceId:1}},getCmdConfig$1=()=>{var e=invertSerializeMap(kt);return{qchatGetNeroomToken:{sid:25,cid:1,service:"qchatMedia",params:[{type:"Property",name:"qchatGetNeroomTokenTag",reflectMapper:kt.QChatGetNeroomTokenParams}],response:[{type:"Property",name:"neroomToken",reflectMapper:e.QChatGetNeroomTokenResultTag}]},qchatUpdateRTCChannelConfig:{sid:25,cid:2,service:"qchatMedia",params:[{type:"Property",name:"RTCChannelConfig",reflectMapper:kt.QChatRTCChannelConfigTag}]},qchatGetRTCChannelConfig:{sid:25,cid:3,service:"qchatMedia",params:[{type:"Property",name:"qchatGetRTCChannelConfigTag",reflectMapper:kt.QChatGetRTCParams}],response:[{type:"Property",name:"RTCChannelConfig",reflectMapper:e.QChatRTCChannelConfigResultTag}]},qchatGetRTCChannelMembers:{sid:25,cid:4,service:"qchatMedia",params:[{type:"Property",name:"qchatGetRTCChannelMembersTag",reflectMapper:kt.QChatGetRTCParams}],response:[{type:"PropertyArray",name:"memberList",reflectMapper:e.memberInfo}]}}};class QChatMediaService extends Service{constructor(e,t){super("qchatMedia",e),this.config={},this.tokenExpireTime=24e4,this.getTokenState=!1,this.core=e,registerParser({cmdMap:wt,cmdConfig:getCmdConfig$1()}),t&&this.setOptions(t),this.setListener(),this.tokenExpireTime=24e4,this.channelId="",this.serverId=""}setOptions(e){e&&(this.config=Object.assign(this.config,e),(null==e?void 0:e.neroom)&&this.setNeroom(e.neroom))}setNeroom(e){this.neroom=new e}setListener(){this.core.eventBus.on("disconnect",this._existRomm.bind(this)),this.core.eventBus.on("kicked",this._existRomm.bind(this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLogout",this._existRomm.bind(this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleKicked",this._existRomm.bind(this)),this.core.eventBus.on("qchatMedia/serverOrChannelLeave",(e=>__awaiter(this,void 0,void 0,(function*(){(e.type===st[st.channelVisibilityUpdate]&&e.channelId===this.channelId||e.type===st[st.serverEnterLeave]&&e.serverId===this.serverId)&&this.roomContext&&this.roomContext.roomUuid&&(yield this.disconnectChannel())}))))}_existRomm(){return __awaiter(this,void 0,void 0,(function*(){this.roomContext&&(yield this.disconnectChannel()),this.tokenTimer&&(this.core.timerManager.deleteTimer(this.tokenTimer),this.tokenTimer=void 0)}))}_checkPermission(e){return __awaiter(this,void 0,void 0,(function*(){return yield this.core.qchatRole.checkPermission(e)}))}_checkConnectState(){if("connect"!==this.state)throw new CustomError("QChatMedia::you should connect first")}kickMemberOut(e){var t;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e);try{yield null===(t=this.roomContext)||void 0===t?void 0:t.kickMemberOut(e.accid)}catch(t){throw this.logger.error("QChatMedia::kickMemberOut error params is "+JSON.stringify(e),t),new CustomError("QChatMedia::kickMemberOut error",t)}}))}disconnectChannel(){var e;return __awaiter(this,void 0,void 0,(function*(){this.logger.debug("QChatMedia::disconnect begin disconnect");try{yield null===(e=this.roomContext)||void 0===e?void 0:e.leaveRoom()}catch(e){this.logger.error("QChatMedia::disconnect error",e)}this.authService&&(yield this.authService.logout()),this._setStateInit(),this.core.emit("qchatMediaDisconnect"),this.core.qchatMedia.emit("qchatMediaDisconnect")}))}_setStateInit(){this.logger.debug("QChatMedia::disconnect end"),this.roomContext=void 0,this.rtcController=void 0,this.state="init",this.tokenTimer&&(this.core.timerManager.deleteTimer(this.tokenTimer),this.tokenTimer=void 0),this.channelId="",this.serverId=""}muteAudio(e){var t,r,i,a,n;return __awaiter(this,void 0,void 0,(function*(){if(this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e),(null===(t=this.roomContext)||void 0===t?void 0:t.localMember.uuid)===e.accid)try{yield null===(r=this.rtcController)||void 0===r?void 0:r.muteMyAudio()}catch(e){throw this.logger.error("QChatMedia::muteAudio error",e),new CustomError("QChatMedia::muteAudio error",e)}else try{var o=null===(i=this.roomContext)||void 0===i?void 0:i.roomProperties.audioOff;if(o&&"offNotAllowSelfOn"===(null===(a=o.value)||void 0===a?void 0:a.split("_")[0]))if(!(yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"})))return void this.logger.error("QChatMedia::unMuteAudio not allow open auido");yield null===(n=this.rtcController)||void 0===n?void 0:n.muteMemberAudio(e.accid)}catch(t){throw this.logger.error("QChatMedia::muteAudio error params is "+JSON.stringify(e),t),new CustomError("QChatMedia::muteAudio error",t)}}))}unMuteAudio(e){var t,r,i,a,n;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e);var o=null===(t=this.roomContext)||void 0===t?void 0:t.roomProperties.audioOff;if(o&&"offNotAllowSelfOn"===(null===(r=o.value)||void 0===r?void 0:r.split("_")[0])&&!(yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"})))return void this.logger.error("QChatMedia::unMuteAudio not allow open auido");if((null===(i=this.roomContext)||void 0===i?void 0:i.localMember.uuid)===e.accid)try{yield null===(a=this.rtcController)||void 0===a?void 0:a.unmuteMyAudio()}catch(e){throw this.logger.error("QChatMedia::unMuteAudio error",e),new CustomError("QChatMedia::unMuteAudio error",e)}else try{yield null===(n=this.rtcController)||void 0===n?void 0:n.unmuteMemberAudio(e.accid)}catch(t){throw this.logger.error("QChatMedia::unMuteAudio error params is "+JSON.stringify(e),t),new CustomError("QChatMedia::unMuteAudio error",t)}}))}muteVideo(e){var t,r,i,a,n;return __awaiter(this,void 0,void 0,(function*(){if(this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e),(null===(t=this.roomContext)||void 0===t?void 0:t.localMember.uuid)===e.accid)try{yield null===(r=this.rtcController)||void 0===r?void 0:r.muteMyVideo()}catch(e){throw this.logger.error("QChatMedia::muteVideo error",e),new CustomError("QChatMedia::muteVideo error",e)}else try{var o=null===(i=this.roomContext)||void 0===i?void 0:i.roomProperties.videoOff;if(o&&"offNotAllowSelfOn"===(null===(a=o.value)||void 0===a?void 0:a.split("_")[0]))if(!(yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"})))return void this.logger.error("QChatMedia::unMuteVideo not allow open video");yield null===(n=this.rtcController)||void 0===n?void 0:n.muteMemberVideo(e.accid)}catch(t){throw this.logger.error("QChatMedia::muteVideo error params is "+JSON.stringify(e),t),new CustomError("QChatMedia::muteVideo error",t)}}))}unMuteVideo(e){var t,r,i,a,n;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e);var o=null===(t=this.roomContext)||void 0===t?void 0:t.roomProperties.videoOff;if(o&&"offNotAllowSelfOn"===(null===(r=o.value)||void 0===r?void 0:r.split("_")[0])&&!(yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"})))return void this.logger.error("QChatMedia::unMuteVideo not allow open video");if((null===(i=this.roomContext)||void 0===i?void 0:i.localMember.uuid)===e.accid)try{yield null===(a=this.rtcController)||void 0===a?void 0:a.unmuteMyVideo()}catch(e){throw this.logger.error("QChatMedia::unMuteVideo error",e),new CustomError("QChatMedia::unMuteVideo error",e)}else try{null===(n=this.rtcController)||void 0===n||n.unmuteMemberVideo(e.accid)}catch(e){throw this.logger.error("QChatMedia::unMuteVideo error",e),new CustomError("QChatMedia::unMuteVideo error",e)}}))}startScreenShare(){var e;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState();try{null===(e=this.rtcController)||void 0===e||e.startScreenShare()}catch(e){throw this.logger.error("QChatMedia::startScreenShare error",e),new CustomError("QChatMedia::startScreenShare error",e)}}))}stopScreenShare(){var e;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState();try{null===(e=this.rtcController)||void 0===e||e.stopScreenShare()}catch(e){throw this.logger.error("QChatMedia::stopScreenShare error",e),new CustomError("QChatMedia::stopScreenShare error",e)}}))}stopMemberScreenShare(e){var t;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e);try{null===(t=this.rtcController)||void 0===t||t.stopMemberScreenShare(e.accid)}catch(e){throw this.logger.error("QChatMedia::stopMemberScreenShare error",e),new CustomError("QChatMedia::stopMemberScreenShare error",e)}}))}subscribeRemoteVideoStream(e){var t;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1},streamType:{type:"number",allowEmpty:!1,min:0,max:1}},e);try{null===(t=this.rtcController)||void 0===t||t.subscribeRemoteVideoStream(e.accid,e.streamType)}catch(e){throw this.logger.error("QChatMedia::subscribeRemoteVideoStream error",e),new CustomError("QChatMedia::subscribeRemoteVideoStream error",e)}}))}unSubscribeRemoteVideoStream(e){var t;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1},streamType:{type:"number",allowEmpty:!1,min:0,max:1}},e);try{null===(t=this.rtcController)||void 0===t||t.unsubscribeRemoteVideoStream(e.accid,e.streamType)}catch(e){throw this.logger.error("QChatMedia::unSubscribeRemoteVideoStream error",e),new CustomError("QChatMedia::unSubscribeRemoteVideoStream error",e)}}))}setupVideoCanvas(e){var t;if(this._checkConnectState(),(null===(t=this.roomContext)||void 0===t?void 0:t.localMember.uuid)===e.accid)try{return this.rtcController.setupLocalVideoCanvas(e.videoView)}catch(e){throw this.logger.error("QChatMedia::setupVideoCanvas error",e),new CustomError("QChatMedia::setupVideoCanvas error",e)}else try{return this.rtcController.setupRemoteVideoCanvas(e.videoView,e.accid)}catch(e){throw this.logger.error("QChatMedia::setupVideoCanvas error",e),new CustomError("QChatMedia::setupVideoCanvas error",e)}}setupRemoteVideoSubStreamCanvas(e){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e);try{return this.rtcController.setupRemoteVideoSubStreamCanvas(e.videoView,e.accid)}catch(e){throw this.logger.error("QChatMedia::setupRemoteVideoSubStreamCanvas error",e),new CustomError("QChatMedia::setupRemoteVideoSubStreamCanvas error",e)}}getScreenSharingUserUuid(){this._checkConnectState();try{return this.rtcController.getScreenSharingUserUuid()}catch(e){throw this.logger.error("QChatMedia::getScreenSharingUserUuid error",e),new CustomError("QChatMedia::getScreenSharingUserUuid error",e)}}initQChatMedia(e){return __awaiter(this,void 0,void 0,(function*(){if(this.neroom)if(void 0===this.state){try{this.neroom.initialize({appKey:this.core.options.appkey,serverConfig:e.serverConfig})}catch(e){throw this.logger.error("QChatMedia::initQChatMedia error",e),new CustomError("QChatMedia::initQChatMedia error",e)}this.authService=this.neroom.authService,this.roomService=this.neroom.roomService,this.messageChannelService=this.neroom.messageChannelService,this.state="init"}else this.logger.error("QChatMedia::init::you already init QChatMedia");else this.logger.warn("QChatMedia::init::you should import neroom SDK")}))}loginByIM(){var e;return __awaiter(this,void 0,void 0,(function*(){if("init"===this.state){var t=yield this._getToken(),r=get(this.core,"options.token")||get(this.core,"V2NIMLoginService.token");try{yield null===(e=this.authService)||void 0===e?void 0:e.loginByIM(this.core.account,t,r)}catch(e){throw this.logger.error("QChatMedia::loginByIM error",e),new CustomError("QChatMedia::loginByIM error",e)}this.state="login"}else this.logger.error("QChatMedia::connect::you should init before login")}))}_getToken(){var e;return __awaiter(this,void 0,void 0,(function*(){this.logger.log("QChatMedia::getToken begin getToken");var t=yield this.core.sendCmd("qchatGetNeroomToken",{qchatGetNeroomTokenTag:{neroomDeviceId:null===(e=this.neroom)||void 0===e?void 0:e.deviceId}}),{token:r,expire:i}=t.content.neroomToken;return this.logger.log(`QChatMedia::getToken success token is ${r},expire is ${i} `),this.tokenTimer||(this.logger.debug(`QChatMedia::getToken set token timer,expire is ${1e3*i-this.tokenExpireTime} `),this.tokenTimer=this.core.timerManager.addTimer((()=>__awaiter(this,void 0,void 0,(function*(){this.tokenTimer&&this.core.timerManager.deleteTimer(this.tokenTimer),this.tokenTimer=void 0;var e=yield this._getToken();this.authService&&this.authService.renewToken(e)}))),1e3*i-this.tokenExpireTime)),r}))}connectChannel(e){var t,r;return __awaiter(this,void 0,void 0,(function*(){if(void 0!==this.state){"init"===this.state&&(yield this.loginByIM()),this.serverId=e.serverId,this.channelId=e.channelId;try{yield null===(t=this.roomService)||void 0===t?void 0:t.joinRoom({roomUuid:e.channelId,role:"qchatAudience",userName:this.core.account,initialProperties:{}},{})}catch(e){throw this.logger.error("QChatMedia::connectChannel error",e),new CustomError("QChatMedia::connectChannel error",e)}var i=null===(r=this.roomService)||void 0===r?void 0:r.getRoomContext(e.channelId);i?(this.roomContext=i,this.rtcController=this.roomContext.rtcController,this.state="connect",yield this.rtcController.joinRtcChannel(),this.core.emit("connectChannel"),this.core.qchatMedia.emit("connectChannel")):this.logger.error("QChatMedia::connect room not exited")}else this.logger.error("QChatMedia::connect::you should init before login")}))}updateRTCChannelInfo(e){return __awaiter(this,void 0,void 0,(function*(){validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1},limit:{type:"number",allowEmpty:!1}},e),yield this.core.sendCmd("qchatUpdateRTCChannelConfig",{RTCChannelConfig:Object.assign(Object.assign({},e),{audio:JSON.stringify(e.audio),video:JSON.stringify(e.video)})})}))}getRTCChannelInfo(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1}},e),function formatRTCChannelConfig(e){try{e.audio&&(e.audio=JSON.parse(e.audio))}catch(e){throw new ValidateError('result "audio" JSON parse error',{key:"audio"},"JSON parse error")}try{e.video&&(e.video=JSON.parse(e.video))}catch(e){throw new ValidateError('result "video" JSON parse error',{key:"video"},"JSON parse error")}return format(Pt,e)}((yield this.core.sendCmd("qchatGetRTCChannelConfig",{qchatGetRTCChannelConfigTag:e})).content.RTCChannelConfig)}))}getRTCChannelOnlineMembers(e){return __awaiter(this,void 0,void 0,(function*(){return validate({serverId:{type:"string",allowEmpty:!1},channelId:{type:"string",allowEmpty:!1}},e),formatMembers((yield this.core.sendCmd("qchatGetRTCChannelMembers",{qchatGetRTCChannelMembersTag:e})).content.memberList)}))}addRTCChannelListener(){var e,t;this._checkConnectState();try{null===(e=this.authService)||void 0===e||e.addAuthListener({onAuthEvent:e=>__awaiter(this,void 0,void 0,(function*(){if(1026===e&&!this.getTokenState){this.logger.log("QChatMedia::loginByIM token expire,begin get new token "),this.getTokenState=!0;var t=yield this._getToken();this.authService&&(yield this.authService.renewToken(t)),this.getTokenState=!1}}))})}catch(e){throw this.getTokenState=!1,this.logger.error("QChatMedia::loginByIM addAuthListener error",e),new CustomError("QChatMedia::loginByIM addAuthListener error",e)}null===(t=this.roomContext)||void 0===t||t.addRoomListener({onRoomPropertiesChanged:e=>{var t,r,i,a,n,o;if(this.logger.log("QChatMedia::addRTCChannelListener::onRoomPropertiesChanged",e),e.audioOff){var s=null===(t=e.audioOff.value)||void 0===t?void 0:t.split("_")[0];"offNotAllowSelfOn"!==s&&"offAllowSelfOn"!==s||(null===(r=this.roomContext)||void 0===r?void 0:r.localMember.isAudioOn)&&(null===(i=this.rtcController)||void 0===i||i.muteMyAudio())}else if(e.videoOff){var c=null===(a=e.videoOff.value)||void 0===a?void 0:a.split("_")[0];"offNotAllowSelfOn"!==c&&"offAllowSelfOn"!==c||(null===(n=this.roomContext)||void 0===n?void 0:n.localMember.isVideoOn)&&(null===(o=this.rtcController)||void 0===o||o.muteMyVideo())}},onRoomPropertiesDeleted:e=>{this.logger.log("QChatMedia::addRTCChannelListener::onRoomPropertiesDeleted",e)},onMemberJoinRtcChannel:e=>{this.logger.log("QChatMedia::addRTCChannelListener::onMemberJoinRTCChannel",e);var t=e.map((e=>e.uuid));this.core.emit("memberJoinRTCChannel",t),this.core.qchatMedia.emit("memberJoinRTCChannel",t)},onMemberLeaveRoom:e=>{this.logger.log("QChatMedia::addRTCChannelListener::onMemberLeaveRoom",e);var t=e.map((e=>e.uuid));this.core.emit("memberLeaveRTCChannel",t),this.core.qchatMedia.emit("memberLeaveRTCChannel",t)},onRoomEnded:e=>{this.authService&&this.authService.logout(),this._setStateInit(),this.logger.log("QChatMedia::addRTCChannelListener::onRoomEnded",e),this.core.emit("RTCChannelEnded",e),this.core.qchatMedia.emit("RTCChannelEnded",e)},onRtcChannelError:e=>{this.logger.log("QChatMedia::addRTCChannelListener::onRtcChannelError",e),"SOCKET_ERROR"===e&&this.disconnectChannel(),this.core.emit("RTCChannelError",e),this.core.qchatMedia.emit("RTCChannelError",e)},onRtcAudioVolumeIndication:e=>{this.logger.log("QChatMedia::addRTCChannelListener::onRtcAudioVolumeIndication",e),this.core.emit("onRtcAudioVolumeIndication",e),this.core.qchatMedia.emit("onRtcAudioVolumeIndication",e)},onMemberAudioMuteChanged:(e,t,r)=>{this.logger.log("QChatMedia::addRTCChannelListener::onMemberAudioMuteChanged",e,t),this.core.emit("memberAudioMuteChanged",{memberAccId:e.uuid,mute:t,operateByAccId:r.uuid}),this.core.qchatMedia.emit("memberAudioMuteChanged",{memberAccId:e.uuid,mute:t,operateByAccId:r.uuid})},onMemberScreenShareStateChanged:(e,t,r)=>{this.logger.log("QChatMedia::addRTCChannelListener::onMemberScreenShareStateChanged",e,t),this.core.emit("memberScreenShareStateChanged",{memberAccId:e.uuid,isSharing:t,operateByAccId:r.uuid}),this.core.qchatMedia.emit("memberScreenShareStateChanged",{memberAccId:e.uuid,isSharing:t,operateByAccId:r.uuid})},onMemberVideoMuteChanged:(e,t,r)=>{this.logger.log("QChatMedia::addRTCChannelListener::onMemberVideoMuteChanged",e,t),this.core.emit("memberVideoMuteChanged",{memberAccId:e.uuid,mute:t,operateByAccId:r.uuid}),this.core.qchatMedia.emit("memberVideoMuteChanged",{memberAccId:e.uuid,mute:t,operateByAccId:r.uuid})}})}enumCameraDevices(){return this._checkConnectState(),this.rtcController.enumCameraDevices().then((e=>e.data),(e=>{throw this.logger.error("QChatMedia::enumCameraDevices error",e),new CustomError("QChatMedia::enumCameraDevices error",e)}))}enumPlayoutDevices(){return this._checkConnectState(),this.rtcController.enumPlayoutDevices().then((e=>e.data),(e=>{throw this.logger.error("QChatMedia::enumPlayoutDevices error",e),new CustomError("QChatMedia::enumPlayoutDevices error",e)}))}enumRecordDevices(){return this._checkConnectState(),this.rtcController.enumRecordDevices().then((e=>e.data),(e=>{throw this.logger.error("QChatMedia::enumRecordDevices error",e),new CustomError("QChatMedia::enumRecordDevices error",e)}))}removeRTCChannelListener(){var e,t;this._checkConnectState(),null===(e=this.roomContext)||void 0===e||e.removeRoomListener({}),null===(t=this.authService)||void 0===t||t.removeAuthListener({})}setSelectedCameraDevice(e){return this._checkConnectState(),validate({deviceId:{type:"string",allowEmpty:!1}},e),this.rtcController.setSelectedCameraDevice(e.deviceId).then((e=>e.data),(e=>{throw this.logger.error("QChatMedia::setSelectedCameraDevice error",e),new CustomError("QChatMedia::setSelectedCameraDevice error",e)}))}setSelectedPlayoutDevice(e){return this._checkConnectState(),validate({deviceId:{type:"string",allowEmpty:!1}},e),this.rtcController.setSelectedPlayoutDevice(e.deviceId).then((e=>e.data),(e=>{throw this.logger.error("QChatMedia::setSelectedPlayoutDevice error",e),new CustomError("QChatMedia::setSelectedPlayoutDevice error",e)}))}setSelectedRecordDevice(e){return this._checkConnectState(),validate({deviceId:{type:"string",allowEmpty:!1}},e),this.rtcController.setSelectedRecordDevice(e.deviceId).then((e=>e.data),(e=>{throw this.logger.error("QChatMedia::setSelectedRecordDevice error",e),new CustomError("QChatMedia::setSelectedRecordDevice error",e)}))}getRTCMembers(){return this._checkConnectState(),this.roomContext.remoteMembers.filter((e=>e.isInRtcChannel)).map((e=>({accid:e.uuid,isAudioOn:!!e.isAudioOn,isVideoOn:!!e.isVideoOn,isSharingScreen:!!e.isSharingScreen,properties:e.properties})))}subscribeRemoteVideoSubStream(e){var t;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e);try{null===(t=this.rtcController)||void 0===t||t.subscribeRemoteVideoSubStream(e.accid)}catch(e){throw this.logger.error("QChatMedia::subscribeRemoteVideoSubStream error",e),new CustomError("QChatMedia::subscribeRemoteVideoSubStream error",e)}}))}unsubscribeRemoteVideoSubStream(e){var t;return __awaiter(this,void 0,void 0,(function*(){this._checkConnectState(),validate({accid:{type:"string",allowEmpty:!1}},e);try{null===(t=this.rtcController)||void 0===t||t.unsubscribeRemoteVideoSubStream(e.accid)}catch(e){throw this.logger.error("QChatMedia::unsubscribeRemoteVideoSubStream error",e),new CustomError("QChatMedia::unsubscribeRemoteVideoSubStream error",e)}}))}muteAllAudio(){var e;return __awaiter(this,void 0,void 0,(function*(){if(this._checkConnectState(),yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"}))try{null===(e=this.roomContext)||void 0===e||e.updateRoomProperty("audioOff",JSON.stringify({value:`offNotAllowSelfOn_${(new Date).getTime()}`}))}catch(e){throw this.logger.error("QChatMedia::muteAllAudio error",e),new CustomError("QChatMedia::muteAllAudio error",e)}else this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneMicrophone auth")}))}muteAllVideo(){var e;return __awaiter(this,void 0,void 0,(function*(){if(this._checkConnectState(),yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"}))try{null===(e=this.roomContext)||void 0===e||e.updateRoomProperty("videoOff",JSON.stringify({value:`offNotAllowSelfOn_${(new Date).getTime()}`}))}catch(e){throw this.logger.error("QChatMedia::muteAllVideo error",e),new CustomError("QChatMedia::muteAllVideo error",e)}else this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneCamera auth")}))}unMuteAllAudio(){var e;return __awaiter(this,void 0,void 0,(function*(){if(this._checkConnectState(),yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneMicrophone"}))try{null===(e=this.roomContext)||void 0===e||e.updateRoomProperty("audioOff",JSON.stringify({value:`disable_${(new Date).getTime()}`}))}catch(e){throw this.logger.error("QChatMedia::unMuteAllAudio error",e),new CustomError("QChatMedia::unMuteAllAudio error",e)}else this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneMicrophone auth")}))}unMuteAllVideo(){var e;return __awaiter(this,void 0,void 0,(function*(){if(this._checkConnectState(),yield this._checkPermission({serverId:this.serverId,channelId:this.channelId,auth:"RTCChannelOpenCloseEveryoneCamera"}))try{null===(e=this.roomContext)||void 0===e||e.updateRoomProperty("videoOff",JSON.stringify({value:`disable_${(new Date).getTime()}`}))}catch(e){throw this.logger.error("QChatMedia::unMuteAllVideo error",e),new CustomError("QChatMedia::unMuteAllVideo error",e)}else this.logger.error("QChatMedia::connect::auth your have not RTCChannelOpenCloseEveryoneCamera auth")}))}}var qt=["error","warn","log","debug"],emptyFunc=function(){},Lt=["off","error","warn","log","debug"];class Logger{constructor(e,t={}){this.storageArr=[],this.debugLevel="off",this.timer=0,this.strategies={debug:{name:"debg",func:console.log},log:{name:"info",func:console.log},warn:{name:"warn",func:console.warn},error:{name:"erro",func:console.error}},this.debug=emptyFunc,this.log=emptyFunc,this.warn=emptyFunc,this.error=emptyFunc,this.iid=Math.round(1e3*Math.random()),this.debugLevel=Lt.includes(e)?e:"off",t.debugLevel&&(this.debugLevel=Lt.includes(t.debugLevel)?t.debugLevel:this.debugLevel),this.logStorage=!1===t.storageEnable?null:new pe.logStorage(null==t?void 0:t.storageName),this.setOptions(t),this.setLogFunc(this.debugLevel),this.setTimer(),this.open()}getDebugMode(){return"debug"===this.debugLevel}open(e){this.logStorage&&this.logStorage.open(e).then((()=>{this.log("Logger::open success")})).catch((e=>{this.warn("Logger::open failed",e)}))}setOptions(e){if(e&&e.logFunc){var t=e.logFunc;for(var r in t){var i=r,a=t[i];a&&(this.strategies[i].func=a)}}}setLogFunc(e,t="log"){var r=qt.findIndex((t=>t===e)),i=qt.findIndex((e=>e===t));qt.forEach(((e,t)=>{this[e]=function(){if(!(t>r&&t>i)){var a=Array.prototype.slice.call(arguments),n=this.strategies[e],o=this.formatArgs(a,n.name);t<=i&&this.logStorage&&this.prepareSaveLog(o,e),t<=r&&n.func(o)}}}))}extractLogs(){var e;return this.logStorage?null===(e=this.logStorage)||void 0===e?void 0:e.extractLogs():Promise.resolve("")}prepareSaveLog(e,t){this.storageArr.push({text:e,level:t,time:Date.now(),iid:this.iid}),this.timer||this.setTimer(),this.storageArr.length>=100&&(this.triggerTimer(),this.storageArr=[])}saveLogs(){return __awaiter(this,void 0,void 0,(function*(){if(this.logStorage){var e=this.storageArr;this.storageArr=[];try{yield this.logStorage.addLogs(e)}catch(e){}}}))}clearTimer(){this.timer&&clearTimeout(this.timer),this.timer=0}setTimer(){this.clearTimer(),this.timer=setTimeout(this.triggerTimer.bind(this),5e3)}triggerTimer(){this.clearTimer(),this.saveLogs()}formatArgs(e,t){var r=new Date;return`[NIM ${this.iid} ${t} ${`${r.getMonth()+1}-${r.getDate()} ${r.getHours()}:${r.getMinutes()}:${r.getSeconds()}:${r.getMilliseconds()}`}] `+e.map((e=>e instanceof V2NIMErrorImpl?e.toString():e instanceof Error?e&&e.message?e.message:e:"object"==typeof e?JSON.stringify(e):e)).join(" ")}destroy(){this.debug=emptyFunc,this.log=emptyFunc,this.warn=emptyFunc,this.error=emptyFunc,this.saveLogs(),this.clearTimer(),this.storageArr=[],this.logStorage&&this.logStorage.close()}}class TimerManager{constructor(){this.timerList=[],this.id=1,this.timer=null,this.timeout=0}addTimer(e,t=0,r=1){var i=(new Date).getTime(),a=this.id;return this.timerList.push({id:a,loop:r,count:0,timeout:i+t,interval:t,callback:e}),this.id++,this.checkTimer(i),a}checkTimer(e=(new Date).getTime()){if(this.removeFinished(),0!==this.timerList.length||null==this.timer){var t=0;for(var r of this.timerList)(0===t||t>r.timeout)&&(t=r.timeout);0!==this.timerList.length&&(null===this.timer||t<this.timeout||this.timeout<e)&&(this.timer=setTimeout(this.nowTime.bind(this),t-e),this.timeout=t)}}nowTime(){var e=(new Date).getTime();for(var t of this.timerList)e>=t.timeout&&(t.callback(),t.count++,t.timeout=e+t.interval);this.clerTime(),this.checkTimer(e)}clerTime(){null!==this.timer&&(clearTimeout(this.timer),this.timer=null)}deleteTimer(e){for(var t=this.timerList.length-1;t>=0;t--){this.timerList[t].id===e&&this.timerList.splice(t,1)}}removeFinished(){for(var e=this.timerList.length-1;e>=0;e--){var t=this.timerList[e];t.loop>=0&&t.count>=t.loop&&this.timerList.splice(e,1)}}destroy(){this.clerTime(),this.timerList=[],this.id=1,this.timer=null}}class CoreAdapters{constructor(e){this.lastSuccUploadHost="",this.core=e}getFileUploadInformation(e){return pe.getFileUploadInformation(e)}request(e,t,r){var i=(new Date).getTime(),a=(null==r?void 0:r.exception_service)||0;return pe.request(e,t).catch((r=>{var n,o,s,c,l=r;throw this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(o=null===(n=this.core)||void 0===n?void 0:n.auth)||void 0===o?void 0:o.account),trace_id:null===(c=null===(s=this.core.clientSocket)||void 0===s?void 0:s.socket)||void 0===c?void 0:c.sessionId,start_time:i,action:1,exception_service:a}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof l.code?l.code:0,description:l.message||`${l.code}`,operation_type:0,target:e,context:t?JSON.stringify(t):""},{asyncParams:pe.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),r}))}uploadFile(e){var t,r,i,a;return __awaiter(this,void 0,void 0,(function*(){for(var n="BROWSER"===pe.platform,o=n?e.chunkUploadHostBackupList:e.commonUploadHostBackupList,s=n?e.chunkUploadHost:e.commonUploadHost,c=o.indexOf(s),l=-1===c?[s,...o]:[s,...o.slice(0,c),...o.slice(c+1)],d=Math.max(l.indexOf(this.lastSuccUploadHost),0),h=null,u=0;u<l.length;u++){var m=(new Date).getTime(),p=l[(u+d)%l.length];try{var g=yield pe.uploadFile(Object.assign(Object.assign({},e),n?{chunkUploadHost:p}:{commonUploadHost:p}));return this.lastSuccUploadHost=p,g}catch(e){this.core.cloudStorage.nos.nosErrorCount--,h=e;var y=e;if(this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(r=null===(t=this.core)||void 0===t?void 0:t.auth)||void 0===r?void 0:r.account),trace_id:null===(a=null===(i=this.core.clientSocket)||void 0===i?void 0:i.socket)||void 0===a?void 0:a.sessionId,start_time:m,action:1,exception_service:3}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof y.code?y.code:0,description:y.message||`${y.code}`,operation_type:1,target:p},{asyncParams:pe.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),e&&(e.code===ie.V2NIM_ERROR_CODE_CANCELLED||10499===e.errCode))throw e}}throw h}))}}var Dt="https://abt-online.netease.im/v1/api/abt/client/getExperimentInfo",Vt="imElite_sdk_abtest_web",Ut="https://statistic.live.126.net,https://statistic-overseas.yunxinfw.com";class ABTest{constructor(e,t){this.abtInfo={},this.core=e,this.config=assignOptions({isAbtestEnable:!0,abtestUrl:Dt,abtestProjectKey:Vt},t)}setOptions(e){this.config=assignOptions(this.config,e)}abtRequest(){var e,t;return __awaiter(this,void 0,void 0,(function*(){if(this.config.isAbtestEnable&&!this.abtInfo.experiments&&this.config.abtestUrl){var r;try{r=yield this.core.adapters.request(this.config.abtestUrl,{method:"POST",dataType:"json",headers:{sdktype:"ABTest"},data:{clientInfo:{projectKey:this.config.abtestProjectKey,appKey:this.core.options.appkey,osType:"Web",sdkVersion:"10.8.30",deviceId:this.core.config.deviceId},useLocalCache:!0}},{exception_service:7})}catch(e){this.core.logger.warn("ABTest request failed")}this.abtInfo=(null===(t=null===(e=null==r?void 0:r.data)||void 0===e?void 0:e.data)||void 0===t?void 0:t.abtInfo)||{}}}))}}var xt=Backoff;function Backoff(e){e=e||{},this.ms=e.min||100,this.max=e.max||1e4,this.factor=e.factor||2,this.jitter=e.jitter>0&&e.jitter<=1?e.jitter:0,this.attempts=0}Backoff.prototype.duration=function(){var e=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var t=Math.random(),r=Math.floor(t*this.jitter*e);e=0==(1&Math.floor(10*t))?e-r:e+r}return 0|Math.min(e,this.max)},Backoff.prototype.reset=function(){this.attempts=0},Backoff.prototype.setMin=function(e){this.ms=e},Backoff.prototype.setMax=function(e){this.max=e},Backoff.prototype.setJitter=function(e){this.jitter=e};var Gt,Bt=["disconnect","connect","heartbeat","message","json","event","ack","error","noop"],Ft=["transport not supported","client not handshaken","unauthorized"],jt=["reconnect"];class BaseWebsocket extends Z{constructor(e,t,r){super(),this.websocket=null,this.socketConnectTimer=0,this.url="",this.linkSSL=!0,this.core=e,this.url=t,this.linkSSL=r,this.status="disconnected",this.logger=e.logger,this.connect()}connect(){"connecting"!==this.status&&"connected"!==this.status?(this.status="connecting",this.core.adapters.request(`${this.linkSSL?"https":"http"}://${this.url}/socket.io/1/?t=${Date.now()}`,{method:"GET",dataType:"text",timeout:this.core.options.xhrConnectTimeout||8e3},{exception_service:6}).then((e=>{if("connecting"===this.status){var[t,r]=e.data.split(":");return this.sessionId=t,this.logger.log(`imsocket::XHR success. status ${this.status}, ${"connecting"===this.status?"continue websocket connection":"stop websocket connection"}`),this._createWebsocket(`${this.linkSSL?"wss":"ws"}://${this.url}/socket.io/1/websocket/${t}`)}})).catch((e=>{if("connecting"===this.status){var t=`imsocket::XHR fail. raw message: "${(e=e||{}).message}", code: "${e.code}"`,r=e.code;r="v2"===get(this.core,"options.apiVersion")?e.code===ie.V2NIM_ERROR_CODE_CONNECT_TIMEOUT?ie.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:ie.V2NIM_ERROR_CODE_CONNECT_FAILED:408===e.code?408:415;var i=new V2NIMErrorImpl({code:r,detail:{reason:t,rawError:e}});this.logger.error(t),this.status="disconnected",this.emit("handshakeFailed",i)}}))):this.logger.warn("imsocket::socket is connecting or connected",this.status)}close(){if(this.status="disconnected",this.websocket){this.logger.log("imsocket:: close websocket");try{this.websocket.send(this.encodePacket({type:"disconnect"}))}catch(e){this.logger.warn("imsocket::attempt to send encodePacket error",e)}try{this.websocket.close()}catch(e){this.logger.warn("imsocket::attempt to close websocket error",e)}this.clean(),this.emit("disconnect",{code:0,reason:"Active close websocket"})}}clean(){this.status="disconnected",clearTimeout(this.socketConnectTimer),this.websocket&&(this.socketUrl=void 0,this.websocket.onmessage=null,this.websocket.onopen=null,this.websocket.onerror=null,this.websocket.onclose=null,this.websocket=null)}onConnect(){this.status="connected",this.emit("connect"),clearTimeout(this.socketConnectTimer)}_createWebsocket(e){this.socketConnectTimer=setTimeout((()=>{this.logger.error("imsocket::Websocket connect timeout. url: ",this.socketUrl),this.emit("handshakeFailed",new V2NIMErrorImpl({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:415,detail:{reason:`imsocket::Websocket connect timeout. url: ${this.socketUrl}`}}))}),this.core.options.socketConnectTimeout||8e3),this.socketUrl=e,this.websocket=new pe.WebSocket(e),this.websocket.onmessage=this.onMessage.bind(this),this.websocket.onclose=e=>{e=e||{},this.logger.log(`imsocket::Websocket onclose done ${e.wasClean}/${e.code}/${e.reason}`),this.clean(),this.emit("disconnect",{code:e.code||0,reason:e.reason})},this.websocket.onerror=e=>{this.logger.error("imsocket::Websocket onerror",e),"logined"===this.core.status&&this.core.clientSocket.ping()}}onMessage(e){var t,r=this.decodePacket(e.data);if(r)switch(r.type){case"connect":this.onConnect();break;case"disconnect":this.close(),this.emit("disconnect",{code:0,reason:"MessageEvent type disconnect"});break;case"message":case"json":this.emit("message",r.data);break;case"event":r.name&&this.emit(r.name,r.args);break;case"error":"unauthorized"===r.reason?this.emit("connect_failed",r.reason):this.emit("error",r.reason),this.logger.error("imsocket::Websocket connect failed, onmessage type error. url: ",this.socketUrl),clearTimeout(this.socketConnectTimer),this.emit("handshakeFailed",new V2NIMErrorImpl({code:"v2"===get(this.core,"options.apiVersion")?ie.V2NIM_ERROR_CODE_CONNECT_FAILED:408,detail:{reason:`imsocket::Websocket connect failed, onMessage socket error. url: ${this.socketUrl}`}}));break;case"heartbeat":null===(t=this.websocket)||void 0===t||t.send(this.encodePacket({type:"heartbeat"}));break;default:this.logger.warn("imsocket::Websocket no handler type",r.type)}}encodePacket(e){var t,r,{type:i,id:a="",endpoint:n="",ack:o}=e,s=null;if(!i)return"";switch(i){case"error":t=e.reason?Ft.indexOf(e.reason):"",r=e.advice?jt.indexOf(e.advice):"",""===t&&""===r||(s=t+(""!==r?"+"+r:""));break;case"message":""!==e.data&&(s=e.data);break;case"event":t={name:e.name},t=e.args&&e.args.length?{name:e.name,args:e.args}:{name:e.name},s=JSON.stringify(t);break;case"json":s=JSON.stringify(e.data);break;case"connect":e.qs&&(s=e.qs);break;case"ack":s=e.ackId+(e.args&&e.args.length?"+"+JSON.stringify(e.args):"")}var c=[Bt.indexOf(i),a+("data"===o?"+":""),n];return null!=s&&c.push(s),c.join(":")}decodePacket(e){if(e)if("�"!=e.charAt(0)){var t=e.match(/([^:]+):([0-9]+)?(\+)?:([^:]+)?:?([\s\S]*)?/);if(t){var r,[,i,a,n,o,s]=t,c={type:Bt[+i],endpoint:o};switch(a&&(c.id=a,c.ack=!n||"data"),c.type){case"error":r=s.split("+"),c.reason=Ft[+r[0]]||"";break;case"message":c.data=s||"";break;case"connect":c.qs=s||"";break;case"event":try{var l=JSON.parse(s);c.name=l.name,c.args=l.args}catch(e){this.logger.error("imsocket::parseData::type::event error",e)}c.args=c.args||[];break;case"json":try{c.data=JSON.parse(s)}catch(e){this.logger.error("imsocket::parseData::type::json error",e)}break;case"ack":if((r=s.match(/^([0-9]+)(\+)?(.*)/))&&(c.ackId=r[1],c.args=[],r[3]))try{c.args=r[3]?JSON.parse(r[3]):[]}catch(e){this.logger.error("imsocket::parseData::type::ack error",e)}}return c}}else this.logger.error("imsocket::unrecognize dataStr",e.slice(0,20))}send(e){var t,r={data:e,type:"message",endpoint:""};null===(t=this.websocket)||void 0===t||t.send(this.encodePacket(r))}}!function(e){e[e.ACTIVE=1]="ACTIVE",e[e.KICKED=2]="KICKED",e[e.OFFLINE=3]="OFFLINE"}(Gt||(Gt={}));class V1ClientSocket{constructor(e,t){this.linkUrls=[],this.isAutoReconnect=!1,this.packetTimeout=3e4,this.linkSSL=!0,this.packetSer=1,this.retryCount=0,this.reconnectTimer=0,this.backoff=new xt({max:8e3,min:1600,jitter:.01}),this.sendingCmdMap=new Map,this.pingTimer=0,this.hasNetworkListener=!1,this.core=e,t&&(this.auth=t),this.logger=e.logger,this.reporter=e.reporter,this.timerManager=e.timerManager}setSessionId(e){}setLinkSSL(e){this.linkSSL=e}connect(e={},t){return __awaiter(this,void 0,void 0,(function*(){if(validate({linkUrls:{type:"array",itemType:"string",required:!1}},e),!/^(unconnected|waitReconnect)$/.test(this.core.status)){var r=`Core socket status is ${this.core.status}, and would not connect`;return this.logger.warn(r),Promise.reject(r)}this.core.status="connecting",e.linkUrls&&e.linkUrls.length>0&&(this.linkUrls=e.linkUrls.concat(this.linkUrls),this.linkUrls=function uniq(e){e=e||[];for(var t=[],r=0;r<e.length;r++)-1===t.indexOf(e[r])&&t.push(e[r]);return t}(this.linkUrls)),0===this.linkUrls.length&&this.linkUrls.push("weblink.netease.im:443");for(var i=0;i<this.linkUrls.length;i++){var a=this.linkUrls[i],n=(new Date).getTime();try{return yield this.doConnect(a),this.core.status="connected",this.logger.log(`clientsocketV1::connect success with url: ${a}`),a}catch(e){var o=e;t&&t(o,a),this.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account,start_time:n,action:0,exception_service:6}),this.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof o.code?o.code:0,description:o.message||`${o.code}`,operation_type:0,target:a},{asyncParams:pe.net.getNetworkStatus()}),this.reporter.reportTraceEnd("exceptions",1),this.logger.warn(`clientsocketV1::connect failed with url: ${a}`,e)}}throw 0===this.retryCount?this.doDisconnect(Gt.ACTIVE,"SocketHandshakeFailed"):this.doDisconnect(Gt.OFFLINE,"ReconnectHadRetryAllLinks"),new Error("clientSocketV1::socket xhr or socket connect failed")}))}doConnect(e){var t=!1;return new Promise(((r,i)=>{this.socket=new BaseWebsocket(this.core,e,this.linkSSL),this.socket.on("connect",(()=>{this.logger.log("clientSocketV1::on connect",e),this.core.reporterHookLinkKeep&&(this.core.reporterHookLinkKeep.start(),this.core.reporterHookLinkKeep.update({code:0,description:"connection begin",operation_type:0,target:e})),t=!0,r()})),this.socket.on("message",this.onMessage.bind(this)),this.socket.on("disconnect",(r=>__awaiter(this,void 0,void 0,(function*(){this.logger.log("clientSocketV1::socket on disconnect",r),this.core.reporterHookLinkKeep&&(yield this.core.reporterHookLinkKeep.update({code:(null==r?void 0:r.code)||0,description:(null==r?void 0:r.reason)||"socket on disconnect",operation_type:1,target:e}),this.core.reporterHookLinkKeep.end(!1)),t=!0,this.doDisconnect(Gt.OFFLINE,"SocketOnDisconnect")})))),this.socket.on("handshakeFailed",(e=>{t?this.ping():(this.logger.error(`clientsocketV1::handshake failed: "${e&&e.message}"`),this.cleanSocket()),t=!0,i(e)}))}))}cleanSocket(){this.socket&&("function"==typeof this.socket.removeAllListeners&&this.socket.removeAllListeners(),"function"==typeof this.socket.close&&this.socket.close(),this.socket=void 0)}beforeConnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer)}resetConnectStatus(){clearTimeout(this.reconnectTimer),this.backoff.reset(),this.retryCount=0,this.initOnlineListener()}doDisconnect(e,t,r){var i,a,n,o,s;if(this.logger.log(`doDisconnect: type ${e}, description ${t}`),"unconnected"!==this.core.status){var c={1:"close",2:"kicked",3:"broken"}[e]||"";this.markAllCmdInvaild(new V2NIMErrorImpl({code:415,desc:"Packet timeout due to instance disconnect",detail:{reason:"Packet timeout due to instance disconnect",disconnect_reason:c}})),this.timerManager.destroy(),clearTimeout(this.pingTimer),this.cleanSocket();var l=!this.core.options.needReconnect||this.retryCount>=this.core.options.reconnectionAttempts;if(e===Gt.ACTIVE||l)this.logger.log("doDisconnect: emit disconnect, type "+e,l),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(i=this.auth)||void 0===i||i.emit("disconnect"),this.destroyOnlineListener();else if(e===Gt.KICKED){this.logger.log("doDisconnect: kicked"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer);var d="string"==typeof t?{reason:"unknow",message:t}:t;this.core.eventBus.emit("kicked",d),this.core.emit("kicked",d),null===(a=this.auth)||void 0===a||a.emit("kicked",d),this.destroyOnlineListener()}else e===Gt.OFFLINE&&this.core.V1NIMLoginService.isManualLoginAttempt?(this.logger.log("doDisconnect: offline in manual login phase. no reconnect"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.destroyOnlineListener()):e===Gt.OFFLINE&&(null===(o=null===(n=this.auth)||void 0===n?void 0:n.authenticator)||void 0===o?void 0:o.checkLoginTerminalCode(null==r?void 0:r.code))?(this.logger.log(`doDisconnect: login terminal code ${null==r?void 0:r.code}, no reconnect`),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.destroyOnlineListener(),this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(s=this.auth)||void 0===s||s.emit("disconnect")):e===Gt.OFFLINE?(this.logger.log("doDisconnect: start to reconnect"),this.attempToReconnect()):this.logger.log("doDisconnect: nothing to do")}else this.logger.warn("doDisconnect: already unconnected")}attempToReconnect(){var e,t;if("waitReconnect"!==this.core.status){0===this.retryCount&&(this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(e=this.auth)||void 0===e||e.emit("disconnect"));var r=this.backoff.duration();this.retryCount++,this.logger.log(`willReconnect ${this.retryCount} ${r}`),this.core.eventBus.emit("willReconnect",{retryCount:this.retryCount,duration:r}),this.core.emit("willReconnect",{retryCount:this.retryCount,duration:r}),null===(t=this.auth)||void 0===t||t.emit("willReconnect",{retryCount:this.retryCount,duration:r}),this.core.status="waitReconnect",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=setTimeout((()=>__awaiter(this,void 0,void 0,(function*(){"waitReconnect"===this.core.status?!1===(yield pe.net.getNetworkStatus()).net_connect?(this.logger.log("doDisconnect: skip this reconnection attempt because network is offline"),this.core.status="connecting",this.retryCount>=this.core.options.reconnectionAttempts?this.doDisconnect(Gt.OFFLINE,"MaxReconnectionAttemptExceed"):this.attempToReconnect()):this.core.V1NIMLoginService.login({isAutoReconnect:!0}).catch((()=>{this.logger.error(`clientsocketV1::attempToReconnect failed ${this.retryCount}`)})):this.logger.warn(`doDisconnect: reconnectTimer status is ${this.core.status}, would not go on reconnecting`)}))),r)}else this.logger.warn("doDisconnect: already is waiting reconnect")}sendCmd(e,t,r){if("logined"!==this.core.status&&"login"!==e&&"chatroomLogin"!==e&&"qchatLogin"!==e)return this.logger.warn(`instance status is ${this.core.status}, so can not sendCmd ${e}`),Promise.reject({cmd:e,error:{code:"No_connected",message:"Connection not established",timetag:(new Date).getTime()}});if(!this.socket||!this.socket.send)return Promise.reject("No_socket");var i="heartbeat"!==e,a=i?this.packetSer++:0,n=function createCmd(e,t,r,i){var a=ve[e];if(!a)return r.error("createCmd:: can not find cmd config: ",e),null;var n={SER:t,SID:a.sid,CID:a.cid,Q:[]};return a.params&&i&&a.params.forEach((function(e){var t=i[e.name];if(null!=t){var r=e.type,{reflectMapper:a,select:o}=e;switch(e.type){case"PropertyArray":r="ArrayMable",t=t.map((e=>({t:"Property",v:a?serialize(e,a,o):e})));break;case"Property":t=a?serialize(t,a,o):t;break;case"Bool":t=t?"true":"false"}n.Q.push({t:r,v:t})}})),{packet:n,hasPacketResponse:"boolean"!=typeof a.hasPacketResponse||a.hasPacketResponse,hasPacketTimer:"boolean"!=typeof a.hasPacketTimer||a.hasPacketTimer}}(e,a,this.logger,t);if(!n){var o=`SendCmd ${a} ${e} error`;return this.logger.error(o),Promise.reject(new Error(o))}var{packet:s,hasPacketResponse:c,hasPacketTimer:l}=n,d=JSON.stringify(s);i&&(this.logger.getDebugMode()?this.logger.debug("clientsocketV1::sendCmd",e,`ser:${a}`,d):this.logger.log("clientsocketV1::sendCmd",e,`ser:${a}`));var h=(new Date).getTime();return new Promise(((i,n)=>{c&&this.sendingCmdMap.set(a,{cmd:e,params:t,callback:[i,n],timer:l?setTimeout((()=>{var t=new V2NIMErrorImpl({code:408,desc:"Packet Timeout",detail:{reason:"Packet Timeout",cmd:e,ser:a,timetag:Date.now()}});this.markCmdInvalid(a,t,e)}),r&&r.timeout?r.timeout:this.core.config.timeout):null});try{this.socket.send(d),c||i(s)}catch(t){var o=new V2NIMErrorImpl({code:415,detail:{reason:t&&t.message||"Unable to send packet",cmd:e,ser:a,timetag:Date.now(),rawError:t}});this.markCmdInvalid(a,o,e),n(t)}})).catch((e=>{var t;if(![408,415].includes(e.code))return Promise.reject(e);this.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account,trace_id:null===(t=this.socket)||void 0===t?void 0:t.sessionId,start_time:h,action:2,exception_service:6});var r=get(e,"data.disconnect_reason")||"",i=408===e.code?"Send failed due to timeout":"Send failed. Reason unknown";return i=415===e.code?JSON.stringify({disconnect_reason:r}):i,this.reporter.reportTraceUpdateV2("exceptions",{code:e.code||415,description:i,operation_type:1,target:`${s.SID}-${s.CID}`,context:`${s.SER}`},{asyncParams:pe.net.getNetworkStatus()}),this.reporter.reportTraceEnd("exceptions",1),Promise.reject(e)}))}onMessage(e){var t=function parseCmd(e,t){var r;try{r=JSON.parse(e)}catch(r){return void t.error(`Parse command error:"${e}"`)}var i=r.sid+"_"+r.cid,a=r.r;if(["4_1","4_2","4_10","4_11"].includes(i)){var n=r.r[1].headerPacket;i=`${n.sid}_${n.cid}`,r.sid=n.sid,r.cid=n.cid,a=r.r[1].body}var o=Ee[i],s=[];if(o){for(var c of o)s.push(parseEachCmd(r,c.config,c.cmd,a,t));return s}t.error("parseCmd:: mapper not exist",i,r.code)}(e,this.logger);if(t)for(var r of t){var i=r.raw.ser;if(r.error&&this.logger.error("core:onMessage packet error",`${r.raw.sid}_${r.raw.cid}, ser:${i},`,r.error),r.notFound)return void this.logger.warn("clientsocketV1::onMessage packet not found",`${r.raw.sid}_${r.raw.cid}, ser:${i}`);"heartbeat"!==r.cmd&&(this.logger.getDebugMode()?this.logger.debug(`imsocket::recvCmd ser:${i}`,r.cmd,r.content):this.logger.log(`imsocket::recvCmd ser:${i}`,r.cmd)),this.packetHandler(r)}}packetHandler(e){var t,r,i,a;if(e){var n=e.raw.ser,o=this.sendingCmdMap.get(n);if(o&&o.cmd===e.cmd){var{callback:s,timer:c,params:l}=o;if(clearTimeout(c),e.params=l,this.sendingCmdMap.delete(n),"heartbeat"===e.cmd)return void s[0]();var d=null===(r=null===(t=this.core[e.service])||void 0===t?void 0:t.process)||void 0===r?void 0:r.call(t,e);d&&"function"==typeof d.then?d.then((e=>{s[0](e)})).catch((e=>{s[1](e)})):(this.logger.log("imsocket:: handlerFn without promise",e.service,e.cmd),s[0]())}else{var h=null===(a=null===(i=this.core[e.service])||void 0===i?void 0:i.process)||void 0===a?void 0:a.call(i,e);h&&"function"==typeof h.then&&h.catch((e=>{this.logger.error("imsocket::no obj cache, no process handler",e)}))}}}markCmdInvalid(e,t,r){var i=this.sendingCmdMap.get(e);if(i){var{callback:a,timer:n}=i;n&&clearTimeout(n),this.sendingCmdMap.delete(e),this.logger.warn(`packet ${e}, ${r} is invalid:`,t),a[1](t)}}markAllCmdInvaild(e){this.logger.log("markAllCmdInvaild",e),this.sendingCmdMap.forEach((t=>{var{callback:r,timer:i,cmd:a}=t;this.logger.log(`markAllCmdInvaild:: cmd "${a}"`),i&&clearTimeout(i),r[1](e)})),this.sendingCmdMap.clear()}ping(){var e;return __awaiter(this,void 0,void 0,(function*(){clearTimeout(this.pingTimer);try{yield this.sendCmd("heartbeat")}catch(t){if(yield this.testHeartBeat5Timeout())return this.core.reporterHookLinkKeep&&(yield this.core.reporterHookLinkKeep.update({code:0,description:"Heartbeat-discovered link failure",operation_type:1,target:null===(e=this.socket)||void 0===e?void 0:e.url}),this.core.reporterHookLinkKeep.end(!0)),void this.doDisconnect(Gt.OFFLINE,"PingError")}this.pingTimer=setTimeout((()=>{this.ping()}),3e4)}))}testHeartBeat5Timeout(){return __awaiter(this,void 0,void 0,(function*(){clearTimeout(this.pingTimer);for(var e=0;e<5;e++)try{return yield this.sendCmd("heartbeat",{},{timeout:3e3}),!1}catch(t){this.logger.log(`clientsocketV1:: test heartbeat ${e} Timeout`)}return!0}))}initOnlineListener(){this.hasNetworkListener||(this.logger.log("clientsocketV1::onlineListener:init"),this.hasNetworkListener=!0,pe.net.onNetworkStatusChange((e=>{this.logger.log("clientsocketV1::onlineListener:network change",e),e.isConnected&&"logined"===this.core.status?this.ping():e.isConnected&&"waitReconnect"===this.core.status?(this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.core.V1NIMLoginService.login({isAutoReconnect:!0}).catch((()=>{this.logger.error(`clientsocketV1::attempToReconnect failed ${this.retryCount}`)}))):e.isConnected||this.doDisconnect(Gt.OFFLINE,"OfflineListener")})))}destroyOnlineListener(){this.logger.log("clientsocketV1::onlineListener:destroy"),pe.net.offNetworkStatusChange(),this.hasNetworkListener=!1}disconnect(){switch(this.core.status){case"connected":case"logined":case"connecting":case"waitReconnect":return this.doDisconnect(Gt.ACTIVE,"UserActiveDisconnect"),Promise.resolve();default:return Promise.resolve()}}}var Yt={"1_2":"heartbeat","24_2":"qchatLogin","24_4":"qchatExit","24_5":"qchatBeKicked","24_8":"qchatLoginClientChange","24_9":"qchatKick","24_19":"qchatSync"},Qt={qchatLoginTag:{appkey:1,account:2,authType:3,token:4,loginExt:5,clientType:6,clientSession:7,deviceId:8,sdkVersion:9,sdkType:10,userAgent:11,customTag:12,customClientType:13,sdkHumanVersion:14,pushTokenName:20,pushToken:21,pushkitTokenName:22,pushkitToken:23,deviceInfo:24,customPushContentType:25,os:30,mac:31,bundleId:32,simCarrierCode:33,simCountryCode:34,networkCode:35,browser:36,deviceModel:37,androidid:38,imei:39,idfv:40,openuuid:41,pushType:100,hasTokenPreviously:101,consid:102,clientIP:103,clientPort:104,loginTime:105}},getCmdConfig=()=>{var e=invertSerializeMap(Qt);return{heartbeat:{sid:1,cid:2,service:"qchatAuth"},qchatLogin:{sid:24,cid:2,service:"qchatAuth",params:[{type:"Property",name:"qchatLoginReqTag",reflectMapper:Qt.qchatLoginTag}],response:[{type:"Property",name:"qchatLoginResTag",reflectMapper:e.qchatLoginTag},{type:"PropertyArray",name:"qchatMultiLoginResult",reflectMapper:e.qchatLoginTag}]},qchatExit:{sid:24,cid:4,service:"qchatAuth"},qchatBeKicked:{sid:24,cid:5,service:"qchatAuth",response:[{type:"Property",name:"beKickedTag",reflectMapper:{1:"clientType",2:"reason",3:"ext",4:"customClientType"}}]},qchatLoginClientChange:{sid:24,cid:8,service:"qchatAuth",response:[{type:"Byte",name:"state"},{type:"Property",name:"qchatLoginResTag",reflectMapper:e.qchatLoginTag}]},qchatKick:{sid:24,cid:9,service:"qchatAuth",params:[{type:"StrArray",name:"deviceIds"}],response:[{type:"StrArray",name:"deviceIds"}]},qchatSync:{sid:24,cid:19,service:"qchatAuth",hasPacketTimer:!1,params:[{type:"Property",name:"qchatSyncTag",reflectMapper:{systemNotification:1,pushConfig:2}}],response:[{type:"Long",name:"timetag"}]}}};function formatLoginInfo(e,t=1){var r=format({clientPort:{type:"number"},clientType:{type:"enum",values:_t},customClientType:{type:"number"},loginTime:{type:"number"},hasTokenPreviously:{type:"boolean"},pushType:{type:"number"}},e);return Object.assign(Object.assign({},r),{online:2!==t})}var Wt={1:{reason:"samePlatformKick",message:"The same account is not allowed to multiple login at the same time"},2:{reason:"serverKick",message:"Kicked out by IM server"},3:{reason:"otherPlatformKick",message:"Kicked out by other client of your account"},4:{reason:"silentlyKick",message:"Quietly kicked"}};var Ht={BROWSER:0,RN:2,UNIAPP:3,WECHAT:6};class V1AuthAuthenticatorService{constructor(e){this.core=e}verifyAuthentication(e=!1){return __awaiter(this,void 0,void 0,(function*(){var t=this.core.options,r=pe.getSystemInfo(),i=Object.assign(Object.assign({},t),{appLogin:e?0:1,clientType:16,clientSession:this.core.config.clientSession,deviceId:this.core.config.deviceId,sdkVersion:100830,sdkType:Ht[pe.platform]||0,userAgent:"Native/10.8.30",sdkHumanVersion:"10.8.30",os:r.os,browser:r.browser}),a=yield this.core.sendCmd("qchatLogin",{qchatLoginReqTag:i});if(a.error)throw a.error;var{qchatLoginResTag:n,qchatMultiLoginResult:o}=a.content;return n.isAutoReconnect=e,{loginResult:formatLoginInfo(n),multiLoginResults:o.map((e=>formatLoginInfo(e)))}}))}}class QChatAuthService extends Service{constructor(e){super("qchatAuth",e),this.account="",this.token="",this.deviceId="",this.isManualLoginAttempt=!1,registerParser({cmdMap:Yt,cmdConfig:getCmdConfig()}),this.authenticatorService=new V1AuthAuthenticatorService(e)}login(e={}){return __awaiter(this,void 0,void 0,(function*(){e.isAutoReconnect||(this.isManualLoginAttempt=!0),yield this._connect(e),this.core.abtest.abtRequest();try{yield this.doLogin(e.isAutoReconnect),this.isManualLoginAttempt=!1}catch(e){throw this.isManualLoginAttempt=!1,e}}))}_connect(e={}){return __awaiter(this,void 0,void 0,(function*(){if(!/^(unconnected|waitReconnect)$/.test(this.core.status)){var t=`Instance status is ${this.core.status}, and would not connect`;return this.logger.warn(t),Promise.reject(t)}this.core.clientSocket.beforeConnect();var r=e.isAutoReconnect||!1;yield this.core.clientSocket.connect({linkUrls:this.core.options.linkAddresses,isAutoReconnect:r})}))}doLogin(e=!1){var t;return __awaiter(this,void 0,void 0,(function*(){var r;try{r=yield this.authenticatorService.verifyAuthentication(e),this.core.status="logined"}catch(e){if(this.core.logger.warn("doLogin:: login failed",e),this.core.clientSocket.doDisconnect(Gt.OFFLINE,this.isManualLoginAttempt?"FailedToInitializeLogin":"ReconnectLoginFailed"),this.isManualLoginAttempt)throw e;return}try{yield this.core.cloudStorage.init(null===(t=r.loginResult)||void 0===t?void 0:t.loginTime)}catch(e){this.core.logger.error("doLogin:: cloudStorage init failed ",e)}this.core.eventBus.emit("logined",r.loginResult),this.core.emit("logined",r.loginResult),r.multiLoginResults&&r.multiLoginResults.length>0&&this.core.emit("multiSpotLogin",r.multiLoginResults),this.core.logger.log("login done"),this.core.clientSocket.resetConnectStatus(),yield this.sync(),this.core.clientSocket.ping()}))}sync(){return __awaiter(this,void 0,void 0,(function*(){yield this.core.sendCmd("qchatSync",{qchatSyncTag:{systemNotification:this.syncTimeTag||0}})}))}kick(e){var t;return __awaiter(this,void 0,void 0,(function*(){var r=yield this.core.sendCmd("qchatKick",e);return null===(t=r.content)||void 0===t?void 0:t.deviceIds}))}qchatSyncHandler(e){var t,r;this.logger.log("sync: emit syncdone",null===(t=e.content)||void 0===t?void 0:t.timetag),this.core.emit("syncdone"),this.syncTimeTag=(null===(r=e.content)||void 0===r?void 0:r.timetag)||0}qchatLoginClientChangeHandler(e){if(e.error)this.logger.error("qchatLoginClientChangeHandler:: error, ",e.error);else{var{qchatLoginResTag:t,state:r}=e.content,i=formatLoginInfo(t,r);this.core.emit("multiSpotLogin",[i])}}qchatBeKickedHandler(e){if(e.error)this.logger.error("qchatBeKickedHandler error, ",e.error);else{var t=function formatBeKickedTag(e){var t=format({clientType:{type:"enum",values:_t},customClientType:{type:"number"}},e),r=Wt[t.reason];return r=r||{reason:"unknow",message:"Unknown reason"},Object.assign(t,r)}(e.content.beKickedTag);this.logger.warn("bekicked::",t),this.core.clientSocket.doDisconnect(Gt.KICKED,t)}}}var $t={user_id:"",trace_id:"",action:7,exception_service:6,duration:0,start_time:0,state:1,extension:[]};class ReporterHookLinkKeep{constructor(e,t){this.traceData=$t,this.core=e,this.traceData=Object.assign({},$t,t),this.traceData.extension=[]}reset(){this.traceData=Object.assign({},$t),this.traceData.extension=[]}start(){var e,t;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(t=null===(e=this.core.clientSocket)||void 0===e?void 0:e.socket)||void 0===t?void 0:t.sessionId)||"",this.traceData.start_time=(new Date).getTime()}update(e){return __awaiter(this,void 0,void 0,(function*(){var{net_type:t,net_connect:r}=yield pe.net.getNetworkStatus();this.traceData.extension.push(Object.assign({code:0,foreground:!0,foreg_backg_switch:!1,net_type:t,net_connect:r},e))}))}end(e){var t=this.traceData.extension[0],r=this.traceData.extension[1];if(t&&0===t.operation_type&&r&&1===r.operation_type){var i=t.net_type!==r.net_type||t.net_connect!==r.net_connect;if(e||!i)return this.traceData.duration=(new Date).getTime()-this.traceData.start_time,this.core.reporter.report("exceptions",this.traceData),void this.reset();this.reset()}else this.reset()}}var Kt={user_id:"",trace_id:"",action:0,state:0,duration:0,start_time:0,offset:0,full_size:0,transferred_size:0,operation_type:0,remote_addr:""},Jt="ReporterHook::setMonitorForResources:";class ReporterHookCloudStorage{constructor(e,t){this.traceData=Kt,this.core=e,this.traceData=Object.assign({},Kt,t)}reset(){this.traceData=Object.assign({},Kt)}start(){var e,t;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(t=null===(e=this.core.clientSocket)||void 0===e?void 0:e.socket)||void 0===t?void 0:t.sessionId)||"",this.traceData.start_time="timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Date.now()}update(e){return __awaiter(this,void 0,void 0,(function*(){this.traceData.user_id&&(this.core.logger.log(`${Jt} upload update`,e),Object.assign(this.traceData,e))}))}end(e){this.traceData.user_id&&(this.core.logger.log(`${Jt} upload end cause of ${e}`),this.traceData.state=e,this.traceData.duration=("timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Date.now())-this.traceData.start_time,this.core.reporter.report("nim_sdk_resources",this.traceData),this.traceData=Kt)}}function getIsDataReportEnable(e){var t,r,i=!0;return"boolean"==typeof(null===(t=null==e?void 0:e.reporterConfig)||void 0===t?void 0:t.enableCompass)?i=e.reporterConfig.enableCompass:"boolean"==typeof(null===(r=null==e?void 0:e.reporterConfig)||void 0===r?void 0:r.isDataReportEnable)&&(i=e.reporterConfig.isDataReportEnable),i}var zt={debugLevel:"off",needReconnect:!0,reconnectionAttempts:Number.MAX_SAFE_INTEGER,isAbtestEnable:!0,abtestUrl:Dt,abtestProjectKey:Vt};class QChat extends Z{constructor(e,t={cloudStorageConfig:{}}){super(),this.instanceName="QChat",this.status="unconnected",this.account="",this.eventBus=new Z,this.options={},this.qchatServer={},this.qchatChannel={},this.qchatMsg={},this.qchatRole={},this.qchatMedia={},this.cloudStorage={},this.logger=new Logger(e.debugLevel,t.loggerConfig),this.setInitOptions(e),this.otherOptions=t,this.timerManager=new TimerManager,this.adapters=new CoreAdapters(this),this.abtest=new ABTest(this,{isAbtestEnable:this.options.isAbtestEnable,abtestUrl:this.options.abtestUrl,abtestProjectKey:Vt});var r="",i="";this.options.isFixedDeviceId?(r=pe.localStorage.getItem("__QCHAT_DEVC_ID__")||se(),i=pe.localStorage.getItem("__QCHAT_CLIENT_SESSION_ID__")||se(),pe.localStorage.setItem("__QCHAT_DEVC_ID__",r),pe.localStorage.setItem("__QCHAT_CLIENT_SESSION_ID__",i)):(r=se(),i=se()),this.config={timeout:8e3,deviceId:r,clientSession:i};var a=pe.getSystemInfo(),n=function getCompassDataEndpoint(e,t){var r,i,a=null===(r=null==t?void 0:t.reporterConfig)||void 0===r?void 0:r.compassDataEndpoint,n=null===(i=null==t?void 0:t.reporterConfig)||void 0===i?void 0:i.reportConfigUrl;if(a)return a;if(n){var o=n.match(/^https:\/\/([^/]+)\/*/);return Array.isArray(o)&&o.length>=1?`https://${o[1]}`:(e.error(`Invalid reportConfigUrl: ${n}`),Ut)}return Ut}(this.logger,this.otherOptions);this.reporter=new ee(Object.assign(Object.assign({},n?{compassDataEndpoint:n}:{}),{isDataReportEnable:getIsDataReportEnable(this.otherOptions),common:{app_key:e.appkey,dev_id:r,platform:"Web",sdk_ver:"10.8.30",env:"online",os_name:a.os,os_ver:a.osVer,model:a.hostEnvVer,manufactor:a.hostEnv,host_env:a.hostEnv,host_env_ver:a.hostEnvVer,v2:!1},request:pe.request,logger:this.logger,autoStart:!0})),this.reporterHookLinkKeep=new ReporterHookLinkKeep(this),this.reporterHookCloudStorage=new ReporterHookCloudStorage(this),pe.setLogger(this.logger),this.qchatAuth=new QChatAuthService(this),this.auth=this.qchatAuth,this.V1NIMLoginService=this.qchatAuth,this.clientSocket=new V1ClientSocket(this),this.qchatServer=new QChatServerService(this),this.qchatChannel=new QChatChannelService(this,t.qchatChannelConfig),this.qchatMsg=new QChatMsgService(this),this.qchatRole=new QChatRoleService(this),this.qchatMedia=new QChatMediaService(this,t.qchatMediaConfig||t.QChatMedia),this.cloudStorage=new CloudStorageService(this,Object.assign({storageKeyPrefix:this.instanceName},t.cloudStorageConfig)),QChat.instance=this,this.logger.log("QChat init, version ","10.8.30"," sdk version ",100830," appkey ",e.appkey)}static getInstance(e,t){if(!QChat.instance){if(e)return new QChat(e,t);throw new Error("Instance not exist, please input options")}if(e){if(QChat.instance.options.account===e.account&&QChat.instance.options.appkey===e.appkey)return QChat.instance.setOptions(e),QChat.instance;throw new Error("Unexpected login")}return QChat.instance}connect(e={}){return this.auth.login(e)}login(e={}){return this.auth.login(e)}setInitOptions(e){validate({account:{type:"string"},appkey:{type:"string"},token:{type:"string"},linkAddresses:{type:"array",itemType:"string",min:1},needReconnect:{type:"boolean",required:!1},reconnectionAttempts:{type:"number",required:!1},debugLevel:{type:"enum",values:Lt,required:!1}},e),this.logger.log("QChat::setInitOptions options is",e),this.account=e.account,this.options=Object.assign(Object.assign({},zt),e)}setOptions(e){if("object"==typeof e&&null!==e&&(Object.prototype.hasOwnProperty.call(e,"account")&&e.account!==this.options.account||Object.prototype.hasOwnProperty.call(e,"appkey")&&e.appkey!==this.options.appkey))throw new Error("QChat::setOptions account and appkey is not allowed to reset");validate({token:{type:"string",required:!1},linkAddresses:{type:"array",itemType:"string",min:1,required:!1},needReconnect:{type:"boolean",required:!1},reconnectionAttempts:{type:"number",required:!1},debugLevel:{type:"enum",values:Lt,required:!1}},e),this.logger.log("QChat::setOptions options is",e),this.options=Object.assign(Object.assign({},this.options),e)}disconnect(){switch(this.status){case"logined":return this.sendCmd("qchatExit",void 0,{timeout:1e3}).then((()=>{this.clientSocket.doDisconnect(Gt.ACTIVE,"UserActiveDisconnect")})).catch((e=>{this.logger.error("Instance::disconnect sendCmd:logout error",e),this.clientSocket.doDisconnect(Gt.ACTIVE,"UserActiveDisconnect")}));case"connected":case"connecting":case"waitReconnect":return this.clientSocket.doDisconnect(Gt.ACTIVE,"UserActiveDisconnect"),Promise.resolve();case"unconnected":case"destroyed":return Promise.resolve()}}logout(){return this.disconnect()}destroy(){return QChat.instance=void 0,this.disconnect().then((()=>{this.status="destroyed",this.removeAllListeners(),this.eventBus.removeAllListeners(),this.logger.destroy(),this.reporter.destroy(),this.timerManager.destroy(),this.connect=emptyFuncWithPromise,this.disconnect=emptyFuncWithPromise,this.destroy=emptyFuncWithPromise}))}kickOtherClients(e){return validate({deviceIds:{type:"array",itemType:"string"}},e),this.auth.kick(e)}sendCmd(e,t,r){return this.clientSocket.sendCmd(e,t,r)}emit(e,...t){try{var r=Date.now(),i=super.emit(e,...t),a=Date.now()-r;return a>=10&&this.logger.warn(`Core::emit event: ${e} process takes: ${a}ms`),i}catch(t){return this.logger.error(`Core::emit event: ${e}. Error: ${t}`),setTimeout((()=>{throw this.logger.error(`Core::emit throw error in setTimeout. event: ${e}. Error: ${t}`),t}),0),!1}}}var Xt={debug(...e){},log(...e){},warn(...e){},error(...e){}};function setLogger(e){Xt=e}function getLogger(){return Xt}function base64ToArrayBuffer(e){for(var t=function base64Decode(e){var t=String(e).replace(/[=]+$/,"");if(t.length%4==1)throw new Error("'atob' failed: The string to be decoded is not correctly encoded.");for(var r,i="",a=0,n=0,o=0;r=t.charAt(o++);~r&&(n=a%4?64*n+r:r,a++%4)?i+=String.fromCharCode(255&n>>(-2*a&6)):0)r="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=".indexOf(r);return i}(e),r=t.length,i=new Uint8Array(r),a=0;a<r;a++)i[a]=t.charCodeAt(a);return i.buffer}function isMobile(){if(!navigator||!navigator.userAgent)return!1;return[/Android/i,/webOS/i,/iPhone/i,/iPad/i,/iPod/i,/BlackBerry/i,/Windows Phone/i].some((e=>navigator.userAgent.match(e)))}function isElectron(){return!(!navigator||!navigator.userAgent)&&("object"==typeof navigator&&"string"==typeof navigator.userAgent&&navigator.userAgent.indexOf("Electron")>=0)}function isBrowser(){return navigator&&navigator.userAgent}var Zt={clear(){uni.clearStorageSync()},getItem:e=>uni.getStorageSync(e),setItem:(e,t)=>uni.setStorageSync(e,t),removeItem:e=>uni.removeStorageSync(e)},er={wifi:2,"2g":3,"3g":4,"4g":5,"5g":6,ethernet:1,unknown:0,none:0,notreachable:0,wwan:0},tr={__onNetworkStatusChangeFn:null,getNetworkStatus(){var e=uni.getSystemInfoSync()||{};return"app"===e.uniPlatform&&"harmonyos"===e.osName?Promise.resolve({net_type:0,net_connect:!0}):new Promise(((e,t)=>{uni.getNetworkType({success:function(t){var r=!1;r="boolean"==typeof t.networkAvailable?t.networkAvailable:"none"!==t.networkType.toLowerCase(),e({net_type:er[t.networkType.toLowerCase()],net_connect:r})},fail:function(){t(new Error("getNetworkType failed"))}})}))},onNetworkStatusChange(e){this.offNetworkStatusChange(),uni.onNetworkStatusChange&&(this.__onNetworkStatusChangeFn=function(t){var r=t.networkType.toLowerCase();e({isConnected:t.isConnected||"none"!==r,networkType:er[r]})},uni.onNetworkStatusChange(this.__onNetworkStatusChangeFn))},offNetworkStatusChange(){uni.offNetworkStatusChange&&(this.__onNetworkStatusChangeFn&&uni.offNetworkStatusChange(this.__onNetworkStatusChangeFn),this.__onNetworkStatusChangeFn=null)}};function requestFn(e,t){return t&&(t.data=t.data||(null==t?void 0:t.params)||{}),new Promise(((r,i)=>{uni.request(Object.assign(Object.assign({method:"GET",url:e},t),{success:function(t){"number"==typeof(t=t||{}).statusCode&&t.statusCode.toString().startsWith("2")?(t={data:t.data,status:t.statusCode,errMsg:t.errMsg,header:t.header},r(t)):i({code:t.statusCode||0,message:t.data||`uniApp request fail. url: ${e}`})},fail:function(t){var r=`uniApp request fail. url: ${e}`;i(t?{code:t.errCode||0,message:t.errMsg||r}:{code:0,message:r})}}))}))}var getUserAgent=function(){var e=uni.getSystemInfoSync()||{};if("mp-weixin"===e.uniPlatform&&"undefined"!=typeof wx&&wx.getSystemInfoSync){var t=wx.getSystemInfoSync();return`NIM/Web/UNIAPP(${e.uniRuntimeVersion})/WeChatMiniApp(${t.SDKVersion})/V10.8.30/{{appkey}}`}if("undefined"!=typeof tt&&tt.getSystemInfoSync){var r=tt.getSystemInfoSync();return`NIM/Web/UNIAPP(${e.uniRuntimeVersion})/TiktokMiniApp(${r.SDKVersion})/V10.8.30/{{appkey}}`}if("undefined"!=typeof swan&&swan.getSystemInfoSync){var i=swan.getSystemInfoSync();return`NIM/Web/UNIAPP(${e.uniRuntimeVersion})/BaiduMiniApp(${i.SDKVersion})/V10.8.30/{{appkey}}`}if("undefined"!=typeof my&&my.getSystemInfoSync){var a=my.getSystemInfoSync();return`NIM/Web/UNIAPP(${e.uniRuntimeVersion})/AliMiniApp(${a.SDKVersion})/V10.8.30/{{appkey}}`}if(navigator&&navigator.userAgent)return navigator.userAgent;if(e.ua)return e.ua;var n=uni.getSystemInfoSync();return`NIM/Web/UNIAPP(${n.uniRuntimeVersion})/${n.osName}(${n.osVersion})/V10.8.30/{{appkey}}`},getHostEnvVer=function(){var e=uni.getSystemInfoSync()||{};if("mp-weixin"===e.uniPlatform&&"undefined"!=typeof wx&&wx.getSystemInfoSync){var t=wx.getSystemInfoSync();return`${e.uniRuntimeVersion}/${t.version}`}if("undefined"!=typeof tt&&tt.getSystemInfoSync){var r=tt.getSystemInfoSync();return`${e.uniRuntimeVersion}/${r.version}`}if("undefined"!=typeof swan&&swan.getSystemInfoSync){var i=swan.getSystemInfoSync();return`${e.uniRuntimeVersion}/${i.version}`}if("undefined"!=typeof my&&my.getSystemInfoSync){var a=my.getSystemInfoSync();return`${e.uniRuntimeVersion}/${a.version}`}return`${e.uniRuntimeVersion}`},getModel=function(){var e=uni.getSystemInfoSync()||{};if("mp-weixin"===e.uniPlatform&&"undefined"!=typeof wx&&wx.getSystemInfoSync){var t=wx.getSystemInfoSync();return`${e.uniRuntimeVersion}/${t.SDKVersion}`}if("undefined"!=typeof tt&&tt.getSystemInfoSync){var r=tt.getSystemInfoSync();return`${e.uniRuntimeVersion}/${r.SDKVersion}`}if("undefined"!=typeof swan&&swan.getSystemInfoSync){var i=swan.getSystemInfoSync();return`${e.uniRuntimeVersion}/${i.SDKVersion}`}return"undefined"!=typeof my&&my.getSystemInfoSync?(my.getSystemInfoSync(),`${e.uniRuntimeVersion}/${my.SDKVersion}`):`${e.uniRuntimeVersion}`};function getSystemInfoFn(){var e=function(){var e=uni.getSystemInfoSync()||{};return"mp-weixin"===e.uniPlatform?[6,"WeiXin"]:"app"===e.uniPlatform?[101,"H5"]:"undefined"!=typeof tt&&tt.getSystemInfoSync?[104,"Tiktok"]:"undefined"!=typeof swan&&swan.getSystemInfoSync?[103,"Baidu"]:"undefined"!=typeof my&&my.getSystemInfoSync?[102,"Ali"]:[isElectron()?5:isMobile()?101:isBrowser()?100:0,isElectron()?"Electron":isMobile()?"H5":isBrowser()?"BROWSER":"Unset"]}(),t=uni.getSystemInfoSync()||{};return{os:t.osName||"UNIAPP_UNKNOW",osVer:t.osVersion,browser:t.browserName||"",browserVer:t.browserVersion||"",libEnv:"UNIAPP",hostEnv:e[1],hostEnvEnum:e[0],hostEnvVer:getHostEnvVer(),userAgent:getUserAgent(),model:getModel(),manufactor:e[1],pushDeviceInfo:{PRODUCT:t.model,DEVICE:t.model,MANUFACTURER:t.brand}}}function uploadFileFn(e){var t=getLogger(),r=e.headers||{};return e.md5&&(r["Content-MD5"]=e.md5),new Promise(((i,a)=>{var n=uni.uploadFile(Object.assign(Object.assign({url:`${e.commonUploadHost}/${e.nosToken.bucket}`},Object.keys(r).length>0?{header:r}:{}),{formData:{Object:decodeURIComponent(e.nosToken.objectName),"x-nos-token":e.nosToken.token,"x-nos-entity-type":"json"},name:"file",fileType:e.type,filePath:e.filePath,success(t){if(200==t.statusCode)try{var r;try{r=JSON.parse(t.data)}catch(e){r={}}r.name=e.filePath,r.ext=r.name.lastIndexOf(".")>-1?r.name.slice(r.name.lastIndexOf(".")+1).toLowerCase():"",i(r)}catch(e){a(new Error(`Upload Error parse result error: ${t.data}`))}else a(new Error(`Upload error ${t.statusCode}: ${t.errMsg}`))},fail(e){"uploadFile:fail abort"===e.errMsg&&(e.code=ie.V2NIM_ERROR_CODE_CANCELLED),e.message=e.errMsg,a(e)}}));try{e.onUploadStart&&e.onUploadStart(n)}catch(e){t.error("Adapter uploadFile: options.onUploadStart error",e&&e.message),n.abort(),a(e)}e.onUploadProgress&&n.onProgressUpdate((function(t){e.onUploadProgress&&e.onUploadProgress({total:t.totalBytesExpectedToSend,loaded:t.totalBytesSent,percentage:parseFloat((t.totalBytesSent/t.totalBytesExpectedToSend).toFixed(2)),percentageText:t.progress+"%"})}))}))}function getFileUploadInformationFn(e){return null}class WebsocketFn{constructor(e,t=""){if(this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this.binaryType="",this.onclose=function(e){getLogger().log("Adapter uniapp: sockets on close ",e)},this.onerror=function(e){getLogger().error("Adapter uniapp: sockets error ",e)},this.onmessage=function(e){},this.onopen=function(){},!e)throw new Error("Failed to construct 'socket': url required");this.url=e.replace(/:443(\/|$)/,"$1"),this.protocol=t,this.readyState=this.CONNECTING;var r=this.protocol?{protocols:this.protocol}:{};this.socketTask=uni.connectSocket(Object.assign(Object.assign({url:this.url},r),{multiple:!0,fail:e=>{this.errorHandler(e)}})),this.socketTask.onOpen((e=>{getLogger().log("Adapter uniapp:: onOpen. event: ",e),this.readyState=this.OPEN,this.binaryType?this.onopen():this.onmessage&&this.onmessage({type:"open",header:e})})),this.socketTask.onError((e=>{getLogger().log("Adapter uniapp:: onError. event: ",e),this.errorHandler(e)})),this.socketTask.onClose((e=>{(this.readyState=this.CLOSED,"function"==typeof this.onclose)&&(this.onclose&&this.onclose(e),getLogger().log("Adapter uniapp:: onClose. event: ",e));this.socketTask=null})),this.socketTask.onMessage((e=>{var t;t="string"==typeof e.data||e.data instanceof ArrayBuffer?e.data:e.data.isBuffer&&"string"==typeof e.data.data?base64ToArrayBuffer(e.data.data):e.data.data,this.onmessage&&this.onmessage({data:t})}))}close(){getLogger().log("Adapter uniapp:: close uni socket actively"),this.socketTask.close({code:1e3,reason:"user force close websocket",complete:()=>{this.socketTask=null}})}send(e){if(this.readyState!==this.OPEN)throw new Error(`Adapter uniapp:: socket sendMsg when readyState=${this.readyState}`);if(!("string"==typeof e||e instanceof ArrayBuffer))throw new TypeError("Adapter uniapp:: socket sendMsg only String/ArrayBuffer supported");this.socketTask.send({data:e})}errorHandler(e){getLogger().error("Adapter uniapp:: errorHandler. event: ",e),this.readyState=this.CLOSED,this.onerror&&this.onerror({type:"error",message:e&&e.errMsg}),e.errMsg&&"[object Array]"===Object.prototype.toString.call(e.errMsg)&&(e.errMsg.indexOf("断裂管道")>0||e.errMsg.indexOf("broken pipe")>0)&&this.onclose&&this.onclose(e)}}return function setAdapters(e){merge(pe,e())}((()=>({setLogger:setLogger,platform:"UNIAPP",localStorage:Zt,request:requestFn,WebSocket:WebsocketFn,uploadFile:uploadFileFn,getFileUploadInformation:getFileUploadInformationFn,getSystemInfo:getSystemInfoFn,net:tr}))),QChat}));
