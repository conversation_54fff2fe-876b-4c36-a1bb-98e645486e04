<template>
	<!-- 3D -->
	<view class="model-page">
		<div ref="container"></div>
		
		<!-- 控制面板 -->
		<view class="control-panel">
			<view class="control-button" @click="zoomIn">
				<text class="iconfont">+</text>
				<text>放大</text>
			</view>
			<view class="control-button" @click="zoomOut">
				<text class="iconfont">-</text>
				<text>缩小</text>
			</view>
			<view class="control-button" @click="toggleAutoRotate">
				<text class="iconfont">{{isAutoRotating ? '⏸' : '▶'}}</text>
				<text>自动旋转</text>
			</view>
			<view class="control-button" @click="resetCamera">
				<text class="iconfont">↺</text>
				<text>重置视角</text>
			</view>
		</view>
	</view>
</template>

<script>
import * as THREE from 'three';
import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';
import { GLTFLoader } from 'three/examples/jsm/loaders/GLTFLoader';

export default {
	data() {
		return {
			isAutoRotating: false
		}
	},
	mounted() {
		// 使用 nextTick 确保 DOM 已经更新
		this.$nextTick(() => {
			this.initThree();
		});
	},
	methods: {
		initThree() {
			// 创建场景
			const scene = new THREE.Scene();
			scene.background = new THREE.Color(0x16171b);
			
			// 创建相机
			const camera = new THREE.PerspectiveCamera(
				75,
				window.innerWidth / window.innerHeight,
				0.1,
				800
			);
			camera.position.set(0, 0.5, 5);
			camera.lookAt(0, 0, 0);
			
			// 创建渲染器
			const renderer = new THREE.WebGLRenderer({ antialias: true });
			renderer.setSize(window.innerWidth, window.innerHeight);
			renderer.setPixelRatio(window.devicePixelRatio);
			
			// 使用 ref 获取容器
			this.$refs.container.appendChild(renderer.domElement);
			
			// 添加光源
			const ambientLight = new THREE.AmbientLight(0xffffff, 0.5);
			scene.add(ambientLight);
			
			const directionalLight = new THREE.DirectionalLight(0xffffff, 1);
			directionalLight.position.set(5, 5, 5);
			scene.add(directionalLight);
			
			// 创建控制器
			const controls = new OrbitControls(camera, renderer.domElement);
			controls.enableDamping = true;
			controls.dampingFactor = 0.05;
			controls.maxDistance = 20;
			controls.minDistance = 2;
			controls.enableZoom = true;
			controls.zoomSpeed = 1.0;
			
			// 存储必要的对象
			this._scene = scene;
			this._camera = camera;
			this._renderer = renderer;
			this._controls = controls;
			
			// 加载模型
			this.loadModel();
			
			// 添加窗口大小改变监听
			window.addEventListener('resize', this.onWindowResize);
			
			// 开始动画循环
			this.animate();
		},
		
		async loadModel() {
			const loader = new GLTFLoader();
			
			try {
				// 显示加载进度
				uni.showLoading({
					title: '加载模型中...'
				});
				
				const gltf = await loader.loadAsync('../../static/car.gltf');
				this._model = gltf.scene;
				
				// 调整模型
				this._model.scale.set(0.08, 0.08, 0.08); // 调整大小
				this._scene.add(this._model);
				
				// 自动调整相机位置以适应模型
				const box = new THREE.Box3().setFromObject(this._model);
				const center = box.getCenter(new THREE.Vector3());
				const size = box.getSize(new THREE.Vector3());
				
				const maxDim = Math.max(size.x, size.y, size.z);
				const fov = this._camera.fov * (Math.PI / 180);
				let cameraZ = Math.abs(maxDim / Math.sin(fov / 2));
				
				this._camera.position.z = cameraZ * 1.5;
				this._camera.lookAt(center);
				
				// 隐藏加载提示
				uni.hideLoading();
				
			} catch (error) {
				console.error('加载模型失败:', error);
				uni.hideLoading();
				uni.showToast({
					title: '模型加载失败',
					icon: 'none'
				});
			}
		},
		
		animate() {
			if (this.isAutoRotating && this._model) {
				this._model.rotation.y -= 0.01;
			}
			
			if (this._controls) {
				this._controls.update();
			}
			
			this._renderer.render(this._scene, this._camera);
			this._animationId = requestAnimationFrame(this.animate);
		},
		
		onWindowResize() {
			if (this._camera && this._renderer) {
				this._camera.aspect = window.innerWidth / window.innerHeight;
				this._camera.updateProjectionMatrix();
				this._renderer.setSize(window.innerWidth, window.innerHeight);
			}
		},
		
		toggleAutoRotate() {
			this.isAutoRotating = !this.isAutoRotating;
		},
		
		resetCamera() {
			if (!this._camera || !this._controls) return;
			
			const defaultDistance = 5;
			this._camera.position.set(0, 0, defaultDistance);
			this._camera.lookAt(0, 0, 0);
			this._controls.reset();
			
			if (this._model) {
				this._model.rotation.set(0, 0, 0);
			}
		},
		
		zoomIn() {
			if (this._camera) {
				const direction = new THREE.Vector3();
				this._camera.getWorldDirection(direction);
				
				const moveDistance = 0.5;
				this._camera.position.addScaledVector(direction, moveDistance);
				
				const distance = this._camera.position.length();
				if (distance < this._controls.minDistance) {
					this._camera.position.setLength(this._controls.minDistance);
				}
			}
		},
		
		zoomOut() {
			if (this._camera) {
				const direction = new THREE.Vector3();
				this._camera.getWorldDirection(direction);
				
				const moveDistance = -0.5;
				this._camera.position.addScaledVector(direction, moveDistance);
				
				const distance = this._camera.position.length();
				if (distance > this._controls.maxDistance) {
					this._camera.position.setLength(this._controls.maxDistance);
				}
			}
		}
	},
	beforeDestroy() {
		// 清理资源
		window.removeEventListener('resize', this.onWindowResize);
		
		if (this._animationId) {
			cancelAnimationFrame(this._animationId);
		}
		
		if (this._renderer) {
			this._renderer.dispose();
		}
		
		if (this._scene) {
			this._scene.traverse((object) => {
				if (object.geometry) {
					object.geometry.dispose();
				}
				if (object.material) {
					if (Array.isArray(object.material)) {
						object.material.forEach(material => material.dispose());
					} else {
						object.material.dispose();
					}
				}
			});
		}
	}
}
</script>

<style lang="scss" scoped>
.model-page {
	position: relative;
	width: 100%;
	height: 600rpx;
	overflow: hidden;
	background: #16171b;
}

div {
	width: 100%;
	height: 100%;
}

.control-panel {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	height: 120rpx;
	display: flex;
	justify-content: space-around;
	align-items: center;
	gap: 20rpx;
	padding: 0 40rpx;
	background: rgba(255, 255, 255, 0.05);
	backdrop-filter: blur(20rpx);
	z-index: 1;
}

.control-button {
	display: flex;
	flex-direction: column;
	align-items: center;
	color: rgba(255, 255, 255, 0.8);
	cursor: pointer;
	padding: 10rpx 20rpx;
	
	.iconfont {
		font-size: 40rpx;
		margin-bottom: 8rpx;
		color: #3161FE;
		&:active {
			opacity: 0.7;
		}
	}
	
	text {
		font-size: 24rpx;
	}
}

.control-button:active {
	opacity: 0.7;
}
</style>

