<template>
	<custom-header style="height: 88rpx;" title="钻具列表" showBack2 />
	<view class="" style="padding-top: 156rpx;">
		
	<scroll-view 
			class="content" 
			scroll-y 
			@refresherrefresh="onRefresh"
			@refresherpulling="onPulling"
			:refresher-enabled="true"
			:refresher-triggered="isRefreshing"
			refresher-background="#16171b"
			:refresher-threshold="30"
			refresher-default-style="black"
		>
		<view class="search-bar">
			<!-- <input @input="handleSearchInput" type="text" class="sear_inp" placeholder="钻具名称/钻具ID" v-model="searchText" /> -->
			<up-input 
			class="sear_inp" 
			placeholder="钻具名称" 
			prefixIcon="search" v-model="searchText"
			:clearable="true"
			@change="handleSearchInput"
			@clear="handleClear"
			color="rgba(255,255,255,0.8)" prefixIconStyle="font-size: 22px;color: #909399"></up-input>
		<!-- <view class="filter_icon" @click="handelSearch">
			<image src="@/static/image/apply/search.png" mode=""></image>
		</view> -->
		</view>
		
			<view style="height:600px;magin:10rpx;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
			<!-- <view> -->
				<zb-table 
				@sort-change="sortChange" 
				:pullUpLoading="pullUpLoading" 
				:isShowLoadMore="true"
				:highlight="true" 
				:show-header="true" 
				:columns="column" 
				:fit="false" 
				:permissionBtn="permissionBtn"
				:stripe="true" 
				row-key="id" 
				v-bind="$attrs"
				@rowClick="rowClick" 
				:border="false"  @edit="buttonEdit"
				 :data="data"></zb-table>

			</view>

	</scroll-view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request.js'
	let that = null
	export default {
		inheritAttrs: false,
		components: {
			customHeader
		},
		data() {
			const baseData = {
			      serialNum: "ZJD00111111111111111",
			      title: "三牙钻头",
			      model: "钻头",
			      specs: "GD325-5",
			      productionTime: "2023-12-23 10:12:00",
			      purchaseTime: "2023-12-23 10:12:00",
			      lifespan: "20",
			      status: 0,
			       remark:"有拉满取作府红议少认越物政但采队因改。业有习极具部回率认按表价路。直非权则提片却次",
			      lastUsedTime: "2025-01-01 10:12:00",
			      nextRepaireTime: "2025-01-04 10:12:00",
			    };
			      const generateData = ()=>{
					  const repeatedData = [];
			          for (let i =0; i < 20; i++) {
			           repeatedData.push(
			                {id:i,...baseData}
			               )
			          }
			        return repeatedData;
					 }
			return {				
				dateStart:'',
				dateEnd:'',
				searchText: '',
				column: [
					// { type:'selection', fixed:true,width:60 },
					{
						name: 'serialNum',
						label: '钻具ID',
						fixed: true,
						align: 'center',
						emptyString: '--'
					},
					{
						name: 'title',
						label: '钻具名称',
						// sorter: 'custom',
					},
					{
						name: 'model',
						label: '钻具类型',
					},
					{
						name: 'specs',
						label: '钻具型号',
					},
					{
						name: 'productionTime',
						label: '制造日期',
						fixed: true
					},
					{
						name: 'purchaseTime',
						label: '购置日期',
					},
					{
						name: 'lifespan',
						label: '使用寿命',
						sorter: true,
						width: 120,
						type: 'progress'
					},
					 {
					          name: 'status',
					          label: '使用状态',
					          filters: {
					            0: '未使用',
					            1: '使用中'
					          },
							  
					},
					{
						name: 'lastUsedTime',
						label: '上次使用日期',
						// sorter: true
					},
					{
						name: 'nextRepaireTime',
						label: '下次维护日期',
						// sorter: true
					},
					{
						name: 'remark',
						label: '备注',
					},
					{
						name: 'operation',
						type: 'operation',
						label: '操作',
						align: 'center',
						renders: [{
								name: '详情',
								class: 'edit',
								type: "primary",
								align: 'center',
								func: 'edit' // func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
							},

						]
					},
				],
				data: [],//表格数据
				data1: [],
				flag1: true,
				flag2: true,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				isShowLoadMore: true, // 是否显示"加载更多"  
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
				isRefreshing: false,  // 是否正在刷新
				isPulling: false, // 添加下拉状态
			}
		},
		onLoad() {
			this.handelDrill();
			
			// 添加筛选更新事件监听
			uni.$on('updateDrillList', ({dateStart, dateEnd}) => {
				this.dateStart = dateStart;
				this.dateEnd = dateEnd;
				this.currentPage = 1;
				this.data = [];
				this.handelDrill();
			});
		},
		created() {
			that = this
		},
		mounted() {
			// this.handelDrill();
		},
		methods: {
			 handleSearchInput(e) {
			 	// console.log('搜索输入值:', e);
			 	// 清除之前的定时器
			 	if(this.searchTimer) {
			 		clearTimeout(this.searchTimer);
			 	}
			 	
			 	// 设置新的定时器，延迟300ms执行搜索
			 	this.searchTimer = setTimeout(() => {
			 		this.searchText = e; // 更新搜索文本
			 		this.currentPage = 1; // 重置页码
			 		this.data = []; // 清空数据
			 		this.isShowLoadMore = true; // 重置加载更多
			 		this.handelDrill(); // 重新加载数据
			 	}, 300);
			 },
			 
			 // 处理清空
			 handleClear() {
			 	this.searchText = '';
			 	this.currentPage = 1;
			 	this.data = [];
			 	this.isShowLoadMore = true;
			 	this.handelDrill();
			 },
			//初始加载调取接口获取数据
			async handelDrill() {

				try {
					let drill_data = {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord:this.searchText,
						dateStart:this.dateStart,
						dateEnd:this.dateEnd
					};
					const res = await Request.post('/rod/get_ls', drill_data)

						if (res.status == 0) {
							console.log('返回数据', res);
							this.data=res.data.items;
							if (res.data.items.length < this.perPage) {
							             this.isShowLoadMore = false;
							          }
							// 更新成功
							// uni.showToast({
							// 	title: '实名认证成功',
							// 	icon: 'none',
							// 	duration: 2000
							// });

						} else {
							// 失败
							uni.showToast({
								title: res.msg,
								icon: 'none',
								duration: 2000
							});
						}

				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}
				
			},
			
			change(e) {
				console.log("e:", e);
			},
			sortChange(item, sorterMode, index) {

				console.log('触发排序', item, sorterMode, index)
			},
			//筛选
			handelSearch(){
				uni.navigateTo({
					url: '/pages/apply/components/drill/search'
				})
			},
			cellStyle({
				row,
				column,
				rowIndex,
				columnIndex
			}) {
				// console.log('row, column, rowIndex, columnIndex')
				if ((columnIndex % 2) != 0) {
					return {
						background: 'red'
					}
				}
			},
			async pullUpLoading(done) {
				if (!this.flag1) {
					return
				}
				this.currentPage++;  // 页码加1
						try {
				           const res = await Request.post('/rod/get_ls', {
				              page: this.currentPage,
				              perPage: this.perPage,
							 keyWord:this.searchText,
							 dateStart:this.dateStart,
							 dateEnd:this.dateEnd
				            });
				
				           if (res.status == 0) {
							   console.log('加载获取数据',res.data);
							   // this.data.push(res.data.items);
							   console.log('data11111',this.data);
				           		if (res.data.items && res.data.items.length > 0) {
				           			this.data = this.data.concat(res.data.items);
				           			console.log('data11111',this.data);
				           			done(); // 通知 zb-table 加载完成
				           			
				           		}else{
				           		   done('ok'); // 通知zb-table 没有更多数据
				           		   this.flag1 = false
				           		   uni.showToast({
				           		   	title: '暂无更多数据' ,
				           		   	icon: 'none',
				           		   	duration: 1000
				           		   })
				           		}
				
				           } else {
				
						        //    uni.showToast({
								// 	title: '加载数据失败' ,
								// 	icon: 'none',
								// 	duration: 1000
								// })
						        done();       // 结束加载
				           }
						} catch (error) {
						    console.error("加载更多数据失败:", error);
						      // uni.showToast({
					        	// 	title: '加载数据失败' ,
					        	// 	icon: 'none',
					        	// 	duration: 1000
					        	// })
						        done();    //  结束加载
						}
				// setTimeout(() => {
				// 	this.data.push({
				// 		serialNum: 'ZJD0021',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	},
				// 	{
				// 		serialNum: 'ZJD0022',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	})

				// 	this.num++
				// 	if (this.num === 3) {
				// 		done('ok')
				// 		this.flag1 = false
				// 	} else {
				// 		done()
				// 	}
				// }, 2000)
			},
			permissionBtn(row, renders, rowIndex) {
				if (row.id == 2) {
					let arr = renders.filter(item => item.func === 'edit')
					return arr
				}
				return renders
			},
			
			buttonEdit(ite, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '点击编辑'
				// })
				// console.log(ite, index),
				uni.navigateTo({
					url:`/pages/apply/components/drill/detail?id=${ite.id}`
				})
			},

			rowClick(row, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '单击某行'
				// })
				// console.log('单击某行', row, index)
				uni.navigateTo({
					url: `/pages/apply/components/drill/detail?id=${row.id}`
				})
			},
			// 添加下拉刷新方法
						async onRefresh() {
							this.isPulling = false;  // 开始刷新时关闭下拉状态
							this.isRefreshing = true;
							
							try {
								// 重置页码和数据
								this.currentPage = 1;
								this.flag1 = true;
								
								// 重新加载数据
								await this.handelDrill();
								
								// 提示刷新成功
								uni.showToast({
									title: '刷新成功',
									icon: 'none',
									duration: 1000
								});
							} catch (error) {
								console.error('刷新失败:', error);
								uni.showToast({
									title: '刷新失败',
									icon: 'none',
									duration: 1000
								});
							} finally {
								// 停止刷新动画
								this.isRefreshing = false;
							}
						},
						
						// 添加下拉事件处理
						onPulling(e) {
							this.isPulling = true;
						},
		},

		// 添加组件销毁时的清理
		beforeDestroy() {
			// 移除事件监听
			uni.$off('updateDrillList');
		},
	}
</script>

<style>
	page {
		background: #16171b;
	}
</style>
<style scoped lang="scss">
	page {
		background: #16171b;
	}

	.filter {
		margin: 28rpx 0;
		height: 70rpx;
		display: flex;
		// background: #fff;
	}

	.select zxz-uni-data-select {
		border: none;
	}

	uni-icons {
		width: 20rpx;
		height: 20rpx;
		color: #fff;
	}

	.sear_inp {
		border:none;
		padding: 12rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.select {
		flex: 1;
		border: none;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
	}
	.filter_icon{
		height: 42rpx;
		width: 28rpx;
		 padding: 14rpx;
		 border-radius: 10rpx;
		background: rgba(255, 255, 255, 0.08);
	}
	.filter_icon image {
		width: 28rpx;
		height: 28rpx;
		
	}

	::v-deep {
		.uni-card {
			margin: 8px !important;
			padding: 0 !important;

			.uni-card__content {
				padding: 0 !important;
			}
		}
	}

	text {
		color: #fff;
	}

	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
	}

	.sear_inp {
		flex:1;
		margin-right: 10rpx;
		background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}

	.content {
			padding: 0 34rpx;
			// height: calc(100vh - 88rpx);
			box-sizing: border-box;
			background: #16171b;
			position: relative;
			
			/* 自定义下拉刷新样式 */
			:deep(.uni-scroll-view-refresher) {
				width: 100% !important;
				height: 20px;
				background: #16171b;
				display: flex;
				justify-content: center;
				align-items: center;
				
				/* 隐藏默认图标 */
				.uni-scroll-view-refresher__indicator-box {
					display: none;
				}
				
				/* 自定义文本 */
				.uni-scroll-view-refresher__indicator {
					&::before {
						content: '加载中';
						color: rgba(255, 255, 255, 0.8);
						font-size: 14px;
					}
				}
			}
			
			/* 自定义刷新文本样式 */
			.refresh-text {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 30px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: rgba(255, 255, 255, 0.8);
				font-size: 14px;
				background: #16171b;
				z-index: 100;
			}
			
			/* 隐藏默认的刷新图标 */
			:deep(.uni-scroll-view-refresher) {
				.uni-scroll-view-refresher__indicator-box {
					opacity: 0;
				}
			}
		}
</style>