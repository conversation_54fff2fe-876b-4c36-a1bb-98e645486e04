import JSEncrypt from 'jsencrypt/bin/jsencrypt.min';
 
// 加密
 
export function encrypt(txt, publicKey) {
 
    // console.log(txt, publicKey);
 
    const encryptor = new JSEncrypt();
 
    encryptor.setPublicKey(publicKey); // 设置公钥
 
    // console.log(encryptor.encrypt(txt));
 
    return encryptor.encrypt(txt); // 对数据进行加密
 
}
 
 
 
// 解密
 
export function decrypt(txt, privateKey) {
 
    const encryptor = new JSEncrypt();
 
    encryptor.setPrivateKey(privateKey); // 设置私钥
 
    return encryptor.decrypt(txt); // 对数据进行解密
 
}