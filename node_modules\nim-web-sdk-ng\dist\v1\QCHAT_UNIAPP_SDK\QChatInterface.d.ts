import { NIMEQChatChannelServiceListener, QChatChannelUnreadInfo } from './QChatChannelServiceInterface';
import { NIMEQChatMediaServiceListener } from './QChatMediaServiceInterface';
import { NIMEQChatMsgServiceListener } from './QChatMsgServiceInterface';
import { NIMEQChatServerServiceListener } from './QChatServerServiceInterface';
import { LoginResult, MultiSpotLoginResult, TKickedReason, WillReconnectEventResult, QChatInitializeOptions, QChatOtherOptions } from './types';
export interface QChatInterface {
    /**
     * 实例状态
     */
    status: NIMEQChatInstanceStatus;
    /**
     * 建立连接，并且登录
     */
    login(): Promise<void>;
    /**
     * 断开连接。
     *
     * 退出登录状态，并断开 websocket 连接
     *
     * logout 完成后，实例不会被销毁，可再次 login 方法登录
     */
    logout(): Promise<void>;
    /**
     * 更新初始化传入的参数，在初始化完成后使用
     *
     * 请注意！传入的参数会在下一次调用login或重连时生效
     */
    setOptions(options: QChatInitializeOptions): void;
    /**
     * 销毁实例
     *
     * 销毁当前 IM 实例，同时会退出登录状态，并断开 websocket 连接
     *
     * 移除所有监听事件，销毁部分内部变量，并且此实例再也无法调用 login 恢复 IM 连接
     */
    destroy(): Promise<void>;
    /**
     * 踢出当前用户从其它端登录上来的连接
     */
    kickOtherClients(options: KickOtherClientsOptions): Promise<KickOtherClientsResult>;
}
/**
 * @Multi_Lang_Tag
 * @locale cn
 * 静态方法
 * @locale
 *
 * @locale en
 * Static method
 * @locale
 */
export interface QChatInterfaceStatic {
    /**
     * 构造函数
     */
    new (options?: QChatInitializeOptions, otherOptions?: QChatOtherOptions): QChatInterface;
    /**
     * 单例模式获取实例
     */
    getInstance(_options?: QChatInitializeOptions, _otherOptions?: QChatOtherOptions): QChatInterface;
    /**
     * 设置适配器
     * @param _newAdapters 适配器
     */
    setAdapters(_newAdapters: any): void;
}
/**
 * @Multi_Lang_Tag
 * @locale cn
 * 实例的状态标识
 *
 * - unconnected: 尚未建立连接(初始化、主动登出、达到最大重连次数)
 * - connecting: 正在建立连接中
 * - connected: 已连接，尚未完成鉴权认证
 * - logined: 已连接, 并且完成了鉴权认证，可以正常开始发送协议
 * - waitReconnect: 等待重连中
 * - destroyed: 实例已经销毁
 * @locale
 *
 * @locale en
 * Status of the instance
 *
 * - unconnected: no connection has been established(Initialization, active logout, and maximum reconnection times)
 * - connecting: Establishing connection
 * - connected: Connected, authentication has not been completed
 * - logined: Connected and completed the authentication
 * - waitReconnect: Waiting for reconnection
 * - destroyed: The instance has been destroyed
 * @locale
 */
export declare type NIMEQChatInstanceStatus = 'unconnected' | 'connecting' | 'connected' | 'logined' | 'waitReconnect' | 'destroyed';
/**
 * Example：
 *
 * const instance = new SDK()
 *
 * instance.on('msg', msg => { console.log(msg) }
 */
export interface QChatEventInterface {
    /**
     * 登录成功
     */
    logined: [loginResult: LoginResult];
    /**
     * 被踢下线
     */
    kicked: [kickedReason: TKickedReason];
    /**
     * 同步完成
     */
    syncdone: [];
    /**
     * 多端登陆通知
     */
    multiSpotLogin: [multiLoginResults: MultiSpotLoginResult[]];
    /**
     * 开始自动重连
     */
    willReconnect: [result: WillReconnectEventResult];
    /**
     * 连接断开
     *
     * * 注: 此事件不包含被踢而断开的情况<br/>
     * v0.14.0 之前触发条件: 1. 手动断开。 2. 在连接断开后, 自动登录超过重试上限次数。<br/>
     * v0.14.0，包含本版本，更新触发条件: 1. 手动断开。2. 在连接断开后，自动登录超过重试上限次数。3. 登陆保持阶段第一次断开时触发
     */
    disconnect: [];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMsg 模块的监听事件 {@link NIMEQChatMsgServiceListener | NIMEQChatMsgServiceListener}
     *
     * 收到消息
     */
    message: NIMEQChatMsgServiceListener['message'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMsg 模块的监听事件 {@link NIMEQChatMsgServiceListener | NIMEQChatMsgServiceListener}
     *
     * 收到消息更新
     */
    messageUpdate: NIMEQChatMsgServiceListener['messageUpdate'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMsg 模块的监听事件 {@link NIMEQChatMsgServiceListener | NIMEQChatMsgServiceListener}
     *
     * 收到系统通知
     */
    systemNotification: NIMEQChatMsgServiceListener['systemNotification'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMsg 模块的监听事件 {@link NIMEQChatMsgServiceListener | NIMEQChatMsgServiceListener}
     *
     * 收到系统通知更新
     */
    systemNotificationUpdate: NIMEQChatMsgServiceListener['systemNotificationUpdate'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMsg 模块的监听事件 {@link NIMEQChatMsgServiceListener | NIMEQChatMsgServiceListener}
     *
     * 收到“正在输入”事件
     */
    recvTypingEvent: NIMEQChatMsgServiceListener['recvTypingEvent'];
    /**
     * @deprecated 自 1.0 版本开始, 这个事件废弃，由 {@link NIMEQChatChannelServiceListener.unreadInfos |  NIMEQChatChannelServiceListener.unreadInfos}取代
     *
     * 频道收到消息未读通知
     */
    unreadInfo: [msg: QChatChannelUnreadInfo];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatChannel 模块的监听事件 {@link NIMEQChatChannelServiceListener | NIMEQChatChannelServiceListener}
     *
     * 多个频道收到消息未读通知
     */
    unreadInfos: NIMEQChatChannelServiceListener['unreadInfos'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatServer 模块的监听事件 {@link NIMEQChatServerServiceListener | NIMEQChatServerServiceListener}
     *
     * 收到服务器总消息未读通知
     */
    serverUnreadInfo: NIMEQChatServerServiceListener['serverUnreadInfo'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * 断开rtc房间连接
     */
    qchatMediaDisconnect: NIMEQChatMediaServiceListener['qchatMediaDisconnect'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * rtc房间连接成功
     */
    connectChannel: NIMEQChatMediaServiceListener['connectChannel'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * 成员进入RTC频道
     */
    memberJoinRTCChannel: NIMEQChatMediaServiceListener['memberJoinRTCChannel'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * 成员离开RTC频道
     */
    memberLeaveRTCChannel: NIMEQChatMediaServiceListener['memberLeaveRTCChannel'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * RTC频道错误
     */
    RTCChannelError: NIMEQChatMediaServiceListener['RTCChannelError'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * RTC 频道结束
     */
    RTCChannelEnded: NIMEQChatMediaServiceListener['RTCChannelEnded'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * 提示房间内谁正在说话及说话者瞬时音量的回调，不包含本端，如果列表为空，则表示此时远端没有人说话。
     */
    onRtcAudioVolumeIndication: NIMEQChatMediaServiceListener['onRtcAudioVolumeIndication'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * 成员音频状态回调
     */
    memberAudioMuteChanged: NIMEQChatMediaServiceListener['memberAudioMuteChanged'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * 成员屏幕共享状态回调
     */
    memberScreenShareStateChanged: NIMEQChatMediaServiceListener['memberScreenShareStateChanged'];
    /**
     * @deprecated 自 1.0 版本开始, 此实例上的事件将被移动到具体模块中，参见 qchatMedia 模块的监听事件 {@link NIMEQChatMediaServiceListener | NIMEQChatMediaServiceListener}
     *
     * 成员视频状态回调
     */
    memberVideoMuteChanged: NIMEQChatMediaServiceListener['memberVideoMuteChanged'];
}
export interface KickOtherClientsOptions {
    /**
     * 要踢掉的端的设备号数组
     *
     * 端设备号能在 multiPortLogin 多端登陆事件里拿到
     */
    deviceIds: string[];
}
export interface KickOtherClientsResult {
    /**
     * 成功被踢掉的设备号数组
     */
    deviceIds: string[];
}
