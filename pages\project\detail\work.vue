<template>
	<view class="work-detail">
		<custom-header title="工作区域"></custom-header>

		<view class="detail-container">
			<!-- 工作区域信息卡片 -->
			<view class="detail_work">
				<view class="work_title">{{ sectionConfig[0].title }}</view>

				<view class="area-data-grid">
					<view v-for="(item, itemIndex) in sectionConfig[0].items" :key="itemIndex" class="area-data-item">
						<text class="area-data-label">{{ item.label }}</text>
						<text class="area-data-value"
							:class="{'status-running': item.label === '工作状态' && workData[item.dataIndex] === '运行中'}">{{ workData[item.dataIndex] }}</text>
					</view>
				</view>
			</view>

			<view class="extra-sections">
				<view v-for="(section, sectionIndex) in sectionConfig.slice(1)" :key="sectionIndex" class="detail_work">
					<view class="work_title">{{ section.title }}</view>
					<view class="area-data-grid">
						<view v-for="(item, itemIndex) in section.items" :key="itemIndex" class="area-data-item">
							<text class="area-data-label">{{ item.label }}</text>
							<text class="area-data-value">{{ workData[item.dataIndex] }}</text>
						</view>
					</view>
				</view>
			</view>


			<view class="tab_content">


				<!-- Tab 切换区域 -->
				<view class="tab-container">
					<view v-for="(tab, index) in tabs" :key="index" class="tab-item"
						:class="{ active: currentTab === index }" @click="switchTab(index)">
						{{ tab.name }}
					</view>
				</view>

				<!-- Tab 内容区域 -->
				<view class="tab-content">
					<!-- 设备参数 Tab -->
					<view v-if="currentTab === 0" class="device-info">
						<view v-for="item in deviceInfoConfig" :key="item.dataIndex" class="info-item">
							<text class="info-label">{{ item.label }}</text>
							<text class="info-value">{{ deviceData[item.dataIndex] || '-' }}</text>
						</view>
					</view>

					<!-- 运行信息 Tab -->
					<view v-if="currentTab === 1" class="run-info">
						<view v-for="item in runDataConfig" :key="item.dataIndex" class="info-item">
							<text class="info-label">{{ item.label }}</text>
							<text class="info-value">{{ deviceData[item.dataIndex] || '-' }}</text>
						</view>
					</view>

					<!-- 施工信息 Tab -->
					<view v-if="currentTab === 2" class="site-info">
						<view v-for="item in siteInfoData" :key="item.dataIndex" class="info-item">
							<text class="info-label">{{ item.label }}</text>
							<text class="info-value">{{ deviceData[item.dataIndex] || '-' }}</text>
						</view>
					</view>

					<!-- 环境信息 Tab -->
					<view v-if="currentTab === 3" class="env-info">
						<view v-for="item in envInfoData" :key="item.dataIndex" class="info-item">
							<text class="info-label">{{ item.label }}</text>
							<text class="info-value">{{ deviceData[item.dataIndex] || '-' }}</text>
						</view>
					</view>

				</view>

			</view>
			<view class="" style="height: 20rpx;">
				<text> </text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'WorkDetail',
		props: {
			workData: {
				type: Object,
				default: () => ({})
			},
			deviceData: {
				type: Object,
				default: () => ({})
			}
		},
		data() {
			return {
				isLoading: false,
				currentTab: 0,


				// 区域信息数据
				// 区域信息配置
				sectionConfig: [{
						title: '工作区域',
						items: [{
								label: '当前区域',
								dataIndex: 'position'
							},
							{
								label: '工作状态',
								dataIndex: 'workState'
							},
							{
								label: '开始时间',
								dataIndex: 'createdAt'
							},
							{
								label: '进钻深度',
								dataIndex: 'drillingDepth'
							}
						]
					},
					{
						title: '工作参数',
						items: [{
								label: '工作模式',
								dataIndex: 'workMode'
							},
							{
								label: '推进速度',
								dataIndex: 'pushSpeed'
							},
							{
								label: '系统压力',
								dataIndex: 'systemPre'
							},
							{
								label: '推进功率',
								dataIndex: 'pushPower'
							}
						]
					},
					{
						title: '工作统计',
						items: [{
								label: '累计施工深度',
								dataIndex: 'workDepth'
							},
							{
								label: '能耗统计',
								dataIndex: 'powerConsumption'
							},
							{
								label: '累计运行时长',
								dataIndex: 'operatingTime'
							},
							{
								label: '异常次数',
								dataIndex: 'count'
							}
						]
					}
				],

				tabs: [{
						name: '设备参数'
					},
					{
						name: '运行信息'
					},
					{
						name: '施工信息'
					},
					{
						name: '环境信息'
					}
				],

				// 设备信息配置
				deviceInfoConfig: [{
						label: '设备名称',
						dataIndex: 'deviceName'
					},
					{
						label: '设备序列号',
						dataIndex: 'deviceCode'
					},
					{
						label: '设备地点',
						dataIndex: 'position'
					},
					{
						label: '当前工作井号',
						dataIndex: 'drillNumber'
					},
					{
						label: '当前施工人员',
						dataIndex: 'userName'
					}
				],

				// 运行数据配置
				runDataConfig: [{
						label: '系统压力',
						dataIndex: 'systemPre',
					},
					{
						label: '功率',
						dataIndex: 'motorPower',
					},
					{
						label: '电压',
						dataIndex: 'current',
					},
					{
						label: '电流',
						dataIndex: 'current',
					},
					{
						label: '水流量',
						dataIndex: 'waterFlow',
					},
					{
						label: '水压',
						dataIndex: 'waterPre',
					},
					{
						label: '吸油压力',
						dataIndex: 'suctionPre',
					},
					{
						label: '回油压力',
						dataIndex: 'returnPre',
					},
					{
						label: '油位',
						dataIndex: 'tankLevel',
					},
					{
						label: '油温',
						dataIndex: 'oilTemp',
					},
					{
						label: '密度',
						dataIndex: 'oilDensity',
					},
					{
						label: '含水量',
						dataIndex: 'waterContentPer',
					},
					{
						label: '油品油质',
						dataIndex: 'oilQuality',
					},
					{
						label: '动力粘度',
						dataIndex: 'oilViscosity',
					},
					{
						label: '水活性',
						dataIndex: 'waterActivity',
					},
					{
						label: '电机开关状态',
						dataIndex: 'motorSwitch',
					},
					{
						label: '水阀开关状态',
						dataIndex: 'waterValve',
					},
				],

				// 施工信息数据
				siteInfoData: [{
						label: '当前任务',
						dataIndex: 'task',
					},
					{
						label: '当前孔深',
						dataIndex: 'drillingDepth',
					},
					{
						label: '进度百分比',
						dataIndex: 'process',
					},
				],

				// 环境信息数据
				envInfoData: [{
						label: '一氧化碳',
						dataIndex: 'coConc',
					},
					{
						label: '甲烷',
						dataIndex: 'ch4Conc',
					},
				],
			}
		},
		mounted() {
			this.getData()
		},
		methods: {
			getData() {
				this.isLoading = true
				// 模拟API调用
				setTimeout(() => {
					this.deviceData = {
						deviceName: '对接用设备',
						deviceCode: 'XLH2025D4971GG0',
						position: '工地A区',
						drillNumber: 'GD123-5',
						userName: '张工',
						coConc: "0.0(%)",
						chuckPre: "0.0(MPa)",
						clampPre: "0.0(MPa)",
						rotationLoad: "0.0(kN·h)",
						tankLevel: "0.0(%)",
						phRpm: "0.0(rpm)",
						phVibration: "0.0(mm/s²)",
						returnPre: "0.0(MPa)",
						waterPre: "0.0(MPa)",
						startStopCount: "0(次)"
					}
					this.isLoading = false
				}, 800)
			},
			formatValue(value) {
				return value || '-'
			},
			switchTab(index) {
				this.currentTab = index
				if (index === 1) {
					this.getData()
				}
			},

		}
	}
</script>

<style scoped>
	page {
		/* background: #16171b; */
	}

	.work_title {
		margin-top: 6rpx;
		margin-bottom: 20rpx;
		color: rgba(255, 255, 255, 0.85);
		font-size: 32rpx;
	}

	.detail_work {
		margin-top: 28rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
		border: 0.5px solid rgba(255, 255, 255, 0.0972);
	}

	.work-detail {
		padding: 0 28rpx;
	}

	.area-data-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 20rpx 40rpx;
		margin-bottom: 20rpx;
	}

	.area-data-item {
		display: flex;
		flex-direction: column;
	}

	.area-data-label {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.45);
		margin-bottom: 20rpx;
	}

	.area-data-value {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.85);
	}

	.status-running {
		color: #49AA19;
	}

	.toggle-button {
		display: flex;
		justify-content: center;
		align-items: center;
		margin-top: 10rpx;
		padding: 16rpx 0;
		color: rgba(255, 255, 255, 0.45);
		font-size: 28rpx;
		cursor: pointer;
	}

	.arrow-icon {
		margin-left: 10rpx;
	}

	.arrow-down:after {
		content: "▼";
		font-size: 22rpx;
	}

	.arrow-up:after {
		content: "▲";
		font-size: 22rpx;
	}

	.extra-sections {
		animation: fadeIn 0.3s ease-in-out;
	}

	@keyframes fadeIn {
		from {
			opacity: 0;
			transform: translateY(-10rpx);
		}

		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	.detail-section {
		display: flex;
		margin-bottom: 40rpx;
	}

	.detail-item,
	.info-item {
		display: flex;
		/* flex-direction: column; */
		justify-content: space-between;
		margin-bottom: 30rpx;
	}

	.item-label,
	.info-label {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.8);
		margin-bottom: 10rpx;
	}

	.item-value,
	.info-value {
		font-size: 26rpx;
		color: rgba(255, 255, 255, 0.85);
	}

	.tab_content {
		margin-top: 28rpx;
		margin-bottom: 38rpx;
		padding: 0 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
		border: 0.5px solid rgba(255, 255, 255, 0.0972);
	}

	/* Tab 切换样式 */
	.tab-container {
		display: flex;
		justify-content: space-between;
		/* border-top: 1rpx solid #333333; */
		border-bottom: 1rpx solid #333333;
		padding: 20rpx 0;

		margin-bottom: 30rpx;
	}

	.tab-content {
		/* display: flex;
	justify-content: space-between; */
	}

	.tab-item {

		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.45);
		padding: 10rpx 20rpx;
		position: relative;
	}

	.tab-item.active {
		color: #0088ff;
	}

	.tab-item.active::after {
		content: '';
		position: absolute;
		bottom: -21rpx;
		left: 50%;
		transform: translateX(-50%);
		width: 40rpx;
		height: 4rpx;
		background-color: #0088ff;
	}

	.run-info {
		/* height: 800rpx; */
	}

	.loading {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 400rpx;
	}

	.run-data-scroll {
		height: 700rpx;
	}

	.run-data-grid {
		display: grid;
		grid-template-columns: repeat(2, 1fr);
		gap: 30rpx;
		padding: 20rpx 0;
	}

	.run-data-item {
		background-color: #292929;
		border-radius: 10rpx;
		padding: 30rpx;
	}

	.run-data-label {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.45);
		margin-bottom: 20rpx;
		display: block;
	}

	.run-data-value {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.85);
	}
</style>