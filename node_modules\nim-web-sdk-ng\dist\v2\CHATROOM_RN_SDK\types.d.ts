import { ArgumentMap } from 'eventemitter3';
/**
 * 基础 service 模块定义
 *
 * 因 typedoc 无法编译 nodeModules 里的 eventEmitter，故而只能己方写个定义
 */
export interface NIMEBaseServiceInterface<I extends object> {
    /**
     * 继承自 eventEmitter3 的监听事件方法
     */
    on<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    /**
     * 继承自 eventEmitter3 的监听事件方法
     */
    once<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    /**
     * 继承自 eventEmitter3 的取消监听方法
     */
    off<T extends keyof I>(eventName: T, fn: (...args: ArgumentMap<I>[Extract<T, keyof I>]) => void): void;
    /**
     * 继承自 eventEmitter3 的移除事件方法
     */
    removeAllListeners<T extends keyof I>(eventName?: T): void;
}
export declare type NIMEBaseListener = {
    [key: string]: [...args: any];
};
export declare type NIMChatroomServiceName = 'auth' | 'V2NIMChatroomLoginService';
/**
 * 易盾反垃圾配置
 *
 * 使用场景: 未使用云信安全通, 直接对接了易盾
 */
export declare type V2NIMAntispamConfig = {
    /**
     * 指定易盾业务ID，而不使用云信后台配置的安全通
     *
     * @example
     * ```
     * '{"textbid":"","picbid":""}'
     * ```
     */
    antispamBusinessId: string;
};
export declare type NIMEStrAnyObj = {
    [key: string]: any;
};
export interface V2NIMError extends Error {
    /**
     * 错误码
     *
     * 注: 客户端错误码范围: 190000 ~ 199999
     */
    code: number;
    /**
     * 错误描述
     */
    desc: string;
    /**
     * 错误详情
     */
    detail: {
        /**
         * 可能的详细错误描述
         */
        reason?: string;
        /**
         * 原始错误
         */
        rawError?: Error;
        /**
         * 请求返回的原始数据
         */
        rawData?: string;
        /**
         * 错误发生的时间
         */
        timetag?: number;
        [key: string]: any;
    };
}
