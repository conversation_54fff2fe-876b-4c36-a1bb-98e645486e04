/**
 * 调用方式:
 * ```js
 * qchat.cloudStorage.uploadFile(options)
 * ```
 */
export interface CloudStorageServiceInterface {
    /**
     * 上传文件
     */
    uploadFile(options: UploadFileOptions): Promise<UploadFileResult>;
    /**
     * 若开启了融合存储，则默认返回文件的短链地址。若需要返回文件的原始链接，可以通过该接口获取
     *
     * ```js
     * const url = await qchat.cloudStorage.getOriginUrl(shortLinkUrl)
     *
     * console.log('origin url is: ${url}')
     * ```
     */
    getOriginUrl(options: string): Promise<string>;
    /**
     * 获取文件鉴权的token。
     *
     * 在云信后台可以配置上传文件是否需要鉴权。如果需要鉴权，可以通过这个接口获取token。
     *
     * - 若 type = 2，表示设置的为带有过期时间的全局 token
     * - 若 type = 3，表示设置的为文件级别的 url 鉴权
     *
     * ```js
     * const res = await qchat.cloudStorage.getFileToken({
     *   type: 2
     * })
     *
     * console.log('url is: ${url}?token=${res.token}')
     * ```
     */
    getFileToken(options: GetFileTokenOptions): Promise<GetFileTokenResult | void>;
}
export interface UploadFileResult {
    /**
     * 文件名
     */
    name: string;
    /**
     * 文件 url
     */
    url: string;
    /**
     * 文件后缀
     */
    ext: string;
    /**
     * 文件大小，单位字节
     */
    size?: number;
    /**
     * 宽度。
     */
    w?: number;
    /**
     * 高度
     */
    h?: number;
    /**
     * 音频/视频 文件的时长
     */
    dur?: number;
    md5?: string;
    /**
     * 图片的转向
     */
    orientation?: string;
    /**
     * 音频解码格式
     */
    audioCodec?: string;
    /**
     * 视频解码格式
     */
    videoCodec?: string;
    /**
     * 音视频文件的容器
     */
    container?: string;
}
export interface FileProgressObject {
    /**
     * 总大小
     */
    total: number;
    /**
     * 已上传大小
     */
    loaded: number;
    /**
     * 已上传进度
     */
    percentage: number;
    /**
     * 已上传进度的文本描述
     */
    percentageText: string;
}
/**
 * 当发送消息为 image/video/audio/file 时，这些字段才能生效
 */
export interface UploadFileOptions {
    /**
     * 文件类型
     */
    type?: 'image' | 'audio' | 'video' | 'file';
    /**
     * JS 的 File 对象。
     *
     * 浏览器专用
     */
    file?: File;
    /**
     * @deprecated Use {@link UploadFileOptions.file} instead.
     *
     * 即将弃用！！存储文件的 DOM 元素，与上面的 file 只要选填一个就可以了。
     *
     * 浏览器专用。
     *
     * 如果传字符串，最后会以 document.getElementById('fileInput').files[0] 拿到 File 对象
     * 如果传的是 DOM 节点，最后以 fileInput.files[0] 拿到 File 对象
     *
     */
    fileInput?: string | HTMLInputElement;
    /**
     * 临时文件路径
     *
     * uni-app，RN，小程序等特殊的 JS 运行环境专用（chooseImage 拿到的临时路径）
     */
    filePath?: string;
    /**
     * maxSize 限制文件大小。
     *
     * 只对浏览器生效。
     *
     * uni-app，小程序等，由于sdk只能得到一个 filePath 临时路径，不能得到整个文件的信息。
     * 所以请开发者自行在选择文件后进行判断，参考那些端的API如 wx.chooseImage，uni.chooseImage
     */
    maxSize?: number;
    /**
     * 存储场景，不传默认实例化配置，默认为"im"
     */
    nosScenes?: string;
    /**
     * 存储有效时间，不传则默认实例化配置
     *
     * 不得小于一天，单位秒
     */
    nosSurvivalTime?: number;
    /**
     * 上传文件 md5 值。NOS 厂商根据该值判断文件完整性
     */
    md5?: string;
    /**
     * 上传进度
     */
    onUploadProgress?: (obj: FileProgressObject) => void;
    /**
     * 上传前回调事件。返回一个 task 对象，可以调用 task.abort() 取消上传
     */
    onUploadStart?: (task: {
        abort: () => void;
        [key: string]: any;
    }) => void;
    /**
     * 上传完成的回调
     */
    onUploadDone?: (file: UploadFileResult) => void;
}
export interface IUploadFileOptions {
    /**
     * JS 的 File 对象。
     *
     * 浏览器专用
     */
    file?: File;
    /**
     * 即将弃用！！存储文件的 DOM 元素，与上面的 file 只要选填一个就可以了。
     *
     * 浏览器专用。
     *
     * 如果传字符串，最后会以 document.getElementById('fileInput').files[0] 拿到 File 对象
     * 如果传的是 DOM 节点，最后以 fileInput.files[0] 拿到 File 对象
     *
     */
    fileInput?: string | HTMLInputElement;
    /**
     * 临时文件路径
     *
     * uni-app，RN，小程序等特殊的 JS 运行环境专用（chooseImage 拿到的临时路径）
     */
    filePath?: string;
    /**
     * maxSize 限制文件大小。
     *
     * 只对浏览器生效。
     *
     * uni-app，小程序等，由于sdk只能得到一个 filePath 临时路径，不能得到整个文件的信息。
     * 所以请开发者自行在选择文件后进行判断，参考那些端的API如 wx.chooseImage，uni.chooseImage
     */
    maxSize?: number;
    /**
     * 存储场景，不传默认实例化配置，默认为"im"
     */
    nosScenes?: string;
    /**
     * 存储有效时间，不传则默认实例化配置
     *
     * 不得小于一天，单位秒
     */
    nosSurvivalTime?: number;
    /**
     * 上传进度
     */
    onUploadProgress?: (obj: FileProgressObject) => void;
    /**
     * 上传前回调事件
     */
    onUploadStart?: (task: {
        abort: () => void;
        [key: string]: any;
    }) => void;
    /**
     * 上传完成的回调
     */
    onUploadDone?: (file: UploadFileResult) => void;
}
export interface GetFileTokenOptions {
    /**
     * 类型 2表示带过期时间的全局token鉴权，3表示文件级别的url鉴权
     */
    type: 2 | 3;
    /**
     * 如果type=3,是url鉴权，需要传url数组
     */
    urls?: string[] | string;
}
export interface GetFileTokenResult {
    /**
     * 类型 2表示带过期时间的全局token鉴权，3表示文件级别的url鉴权
     */
    type: 2 | 3;
    /**
     * 如果是url鉴权，就返回url数组对应的tokens
     */
    tokens?: string[];
    /**
     * 基于过期时间鉴权的token
     */
    token?: string;
    /**
     * token的过期时间，单位s
     */
    ttl: number;
}
export interface NIMEModuleParamCloudStorageConfig {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * NOS上传地址（直传）
     * 小程序、UniApp上传地址
     * @locale
     *
     * @locale en
     * Address of NOS upload (direct transfer)
     * MiniApp, UniApp upload address
     * @locale
     */
    commonUploadHost?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 小程序、UniApp上传地址备用域名
     * @locale
     *
     * @locale en
     * MiniApp, UniApp upload backup address array
     * @locale
     */
    commonUploadHostBackupList?: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * NOS上传地址（分片）
     * @locale
     *
     * @locale en
     * NOS upload (chunk)
     * @locale
     */
    chunkUploadHost?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送文件消息中文件的url的通配符地址，例：'https://{host}/{object}'
     * @locale
     *
     * @locale en
     * Wildcard address of the file URL in the file message, for example: 'https://{host}/{object}'.
     * @locale
     */
    uploadReplaceFormat?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 接收到文件消息的替换模版
     * 这个是用来接到消息后，要按一定模式替换掉文件链接的。给予一个安全下载链接。
     * 例：'https://{bucket}-nosdn.netease.im/{object}'
     * @locale
     *
     * @locale en
     * The template for the URL of the received file of a file message
     * If a file messages is received, the URL of a file is replaced with a specified patten for a secured download URL
     * Example: 'https://{bucket}-nosdn.netease.im/{object}'
     * @locale
     */
    downloadUrl?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 收到哪些host地址，需要替换成downloadUrl，例：收到nos.netease.com/{bucket}/{obj}
     * @locale
     *
     * @locale en
     * The received addresses to be replaced with downloadUrl, for example: nos.netease.com/{bucket}/{obj}.
     * @locale
     */
    downloadHostList?: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 服务器下发的域名存在，并且对象前缀匹配成功，那么强行替换为`${protocol}${serverCdnDomain}/${decodePath.slice(prefixIndex)}`
     * @locale
     *
     * @locale en
     * If the domain name issued by the server exists and the object prefix matches, it will be forcibly replaced with `${protocol}${serverCdnDomain}/${decodePath.slice(prefixIndex)}`
     * @locale
     */
    nosCdnEnable?: boolean;
    /**
     * NOS 上传专用的 cdn 配置
     */
    cdn?: {
        /**
         * 默认的下载域名
         */
        defaultCdnDomain?: string;
        /**
         * 下载域名
         */
        cdnDomain?: string;
        /**
         * 桶名, 一般 NOS 默认为 "nim"
         */
        bucket?: string;
        /**
         * 路径前缀，一般不需要填写
         */
        objectNamePrefix?: string;
    };
    /**
     * amazon aws s3 sdk
     *
     * 注：若传入 s3 sdk 后，本 SDK 根据融合存储策略配置，可能会 new 创建出它的实例并使用它的实例方法进行上传/存储。
     */
    s3?: any;
    /**
     * localStorage 缓存的云存储配置的键名的前缀。默认叫 NIMClient
     *
     * 注: 举个例子，根据默认配置，策略缓存的键叫 'NIMClient-AllGrayscaleConfig'。
     */
    storageKeyPrefix?: string;
    /**
     * 是否需要开启融合存储整个策略。默认为 true
     *
     * 注: 为 false 则不会进行 lbs 灰度开关和策略获取，直接退化到老的 nos 上传逻辑。
     */
    isNeedToGetUploadPolicyFromServer?: boolean;
}
