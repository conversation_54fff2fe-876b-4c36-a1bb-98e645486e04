import { V2NIMError } from './types';
import { V2NIMChatroomMember } from './V2NIMChatroomMemberService';
import { V2NIMChatroomEnterParams, V2NIMChatroomInitParams, V2NIMChatroomService, V2NIMChatroomStatus, V2NIMOtherParams } from './V2NIMChatroomService';
import { V2NIMChatroomQueueService } from './V2NIMChatroomQueueService';
import { V2NIMChatroomMessageCreator } from './V2NIMChatroomMessageService';
import { ArgumentMap } from 'eventemitter3';
import { V2NIMStorageService } from './V2NIMStorageService';
import { V2NIMChatroomQueueLevelMode } from './V2NIMChatroomQueueService';
import { V2NIMChatroomKickedReason } from './V2NIMChatroomLoginService';
export declare class V2NIMChatroomClient {
    V2NIMChatroomService: V2NIMChatroomService;
    V2NIMChatroomQueueService: V2NIMChatroomQueueService;
    V2NIMChatroomMessageCreator: V2NIMChatroomMessageCreator;
    V2NIMStorageService: V2NIMStorageService;
    /**
     * SDK 版本号-数字格式
     */
    static sdkVersion: number;
    /**
     * SDK 版本号-字符串格式
     */
    static sdkVersionFormat: string;
    /**
     * 销毁所有聊天室实例
     */
    static destroyAll(): void;
    /**
     * 根据实例 ID 获取聊天室实例
     * @param instanceId 实例 ID
     */
    static getInstance(instanceId: number): V2NIMChatroomClient;
    /**
     * 获取所有聊天室实例
     */
    static getInstanceList(): V2NIMChatroomClient[];
    /**
     * 新建聊天室实例
     * @param initParams 初始化参数
     *
     * @example
     * ```js
     * const chatroom = V2NIMChatroomClient.newInstance({
     *    appkey: "YOUR_APPKEY",
     *    debugLevel: "debug"
     * })
     * ```
     */
    static newInstance(initParams: V2NIMChatroomInitParams, otherParams?: V2NIMOtherParams): V2NIMChatroomClient;
    /**
     * 根据实例 ID 销毁聊天室实例
     * @param instanceId 实例 ID
     */
    static destroyInstance(instanceId: number): void;
    /**
     * 进入聊天室
     *
     * @param roomId 聊天室 ID
     * @param enterParams 进入聊天室参数
     *
     * @example
     * ```js
     * const res = await instance.enter(roomId, {
     *     accountId: 'ACCID',
     *     token: 'TOKEN',
     *     roomNick: 'YOUR_NICK',
     *     roomAvatar: 'YOUR_IMG_URL',
     *     linkProvider: function() {return 'CHATROOM_LINK_ADDRESS'}
     * })
     * ```
     */
    enter(roomId: string, enterParams: V2NIMChatroomEnterParams): Promise<V2NIMChatroomEnterResult>;
    /**
     * 退出聊天室
     */
    exit(): void;
    /**
     * 查询聊天室基本信息
     */
    getChatroomInfo(): V2NIMChatroomInfo | null;
    /**
     * 返回聊天室的实例id
     */
    getInstanceId(): number;
    /**
     * 监听聊天室事件
     *
     * 在聊天室实例上监听的方法有: onChatroomStatus, onChatroomEntered, onChatroomExited, onChatroomKicked
     *
     * @example
     * ```js
     * chatroom.on('onChatroomStatus', (status, error) => {})
     * ```
     */
    on<T extends keyof V2NIMChatroomClientListener>(event: T, fn: (...args: ArgumentMap<V2NIMChatroomClientListener>[Extract<T, keyof V2NIMChatroomClientListener>]) => void, context?: any): this;
    /**
     * 解除聊天室事件监听
     *
     * 在聊天室实例上监听的方法有: onChatroomStatus, onChatroomEntered, onChatroomExited, onChatroomKicked
     */
    off<T extends keyof V2NIMChatroomClientListener>(event: T, fn?: ((...args: ArgumentMap<V2NIMChatroomClientListener>[Extract<T, keyof V2NIMChatroomClientListener>]) => void) | undefined, context?: any, once?: boolean | undefined): this;
}
/**
 * 聊天室的事件
 */
export interface V2NIMChatroomClientListener {
    /**
     * @param status 聊天室连接状态
     * @param error  断开时的错误信息
     */
    onChatroomStatus: [status: V2NIMChatroomStatus, error?: V2NIMError];
    /**
     * todo: 连接成功，进入聊天室
     */
    onChatroomEntered: [];
    /**
     * 从登录保持态，退出聊天室
     *
     * @param error 错误信息
     */
    onChatroomExited: [error?: V2NIMError];
    /**
     * 被踢出聊天室
     *
     * @param kickedInfo 被踢出的详细信息
     */
    onChatroomKicked: [kickedInfo: V2NIMChatroomKickedInfo];
}
export interface V2NIMChatroomEnterResult {
    /**
     * 聊天室信息
     */
    chatroom: V2NIMChatroomInfo;
    /**
     * 用户信息
     */
    selfMember: V2NIMChatroomMember;
}
export interface V2NIMChatroomInfo {
    /**
     * 聊天室 ID
     */
    roomId: string;
    /**
     * 聊天室名称
     */
    roomName: string;
    /**
     * 聊天室公告
     */
    announcement: string;
    /**
     * 视频直播拉流地址
     */
    liveUrl: string;
    /**
     * 聊天室是否有效
     */
    isValidRoom: boolean;
    /**
     * 聊天室扩展字段
     */
    serverExtension: string;
    /**
     * 聊天室队列操作权限模式
     */
    queueLevelMode: V2NIMChatroomQueueLevelMode;
    /**
     * 聊天室创建者 ID
     */
    creatorAccountId: string;
    /**
     * 聊天室创建时间
     */
    createTime: number;
    /**
     * 聊天室当前在线人数
     */
    onlineUserCount: number;
    /**
     * 聊天室禁言状态
     */
    chatBanned: boolean;
}
export interface V2NIMChatroomKickedInfo {
    /**
     * 被踢原因
     */
    kickedReason: V2NIMChatroomKickedReason;
    /**
     * 被踢扩展字段
     */
    serverExtension: string;
}
export * as V2NIMChatroomConst from './V2NIMChatroomConst';
export default V2NIMChatroomClient;
