<template>
	<custom-header style="height: 88rpx;" title="通讯呼叫" showBack2 />
	<view class="equip_content">
		<view class="de_con">
			<scroll-view 
			class="content" 
			scroll-y 
			:refresher-enabled="true" 
			refresher-background="#16171b"
			@refresherrefresh="onRefresh"
			:refresher-threshold="30" 
			:refresher-triggered="isRefreshing"
			refresher-default-style="black"
			>
            
			<view v-if="deviceData.length === 0 && !loading" class="no-data">
				暂无数据
			</view>

			<view class="phone_cont" v-for="(item, index) in deviceData" :key="index">
				<view class="phone_bet" style="margin-bottom: 32rpx;">
					<view class="phone_title">{{ item.deviceCode }}</view>
					<view :class="['phone_green', item.normal ? 'abnormal' : 'normal']">
						{{ item.normal ? '异常' : '正常' }}
					</view>
				</view>
				<view class="phone_bet" style="margin-bottom: 16rpx;">
					<view class="phone_value">运行动态</view>
					<view class="phone_value">
						{{ item.workState ? '工作中' : '空闲中' }}
					</view>
				</view>
				<view class="phone_bet">
					<view class="phone_value">工作模式</view>
					<view class="phone_value">
						{{ 
							item.workMode === 0 ? '下发任务中' :
							item.workMode === 1 ? '打孔中' :
							item.workMode === 2 ? '封控中' :
							item.workMode === 3 ? '验孔中' : '非空中'
						}}
					</view>
				</view>
				<button class="phone_btn">
					<image src="@/static/phone.png" mode=""></image>
					语音呼叫
				</button>
			</view>

			<view v-if="deviceData.length > 0 && !hasMore" class="no-more">
				暂无更多设备
			</view>

			</scroll-view>


		</view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request';
	export default {
		inheritAttrs: false,
		components: {
			customHeader,

		},

		data() {
          
			return {
				deviceData: [],
				page: 1,
				perPage: 5,
				isRefreshing: false,
				loading: false,
				hasMore: true
			}
		},
		onLoad() {
			this.getDeviceList();
		},
		onShow() {},
		methods: {
			async getDeviceList() {
				if (this.loading) return;
				this.loading = true;
				
				try {
					const params = {
						page: this.page,
						perPage: this.perPage,
						workState: 1
					};
					
					const res = await Request.post('/device/get_ls', params);
					console.log('返回数据',res.data);
					if (res.status === 0) {
						this.deviceData = res.data.items;
						this.hasMore = res.data.hasMore;
					} else {
						console.error('获取设备列表失败:', res.message);
					}
				} catch (error) {
					console.error('请求出错:', error);
				} finally {
					this.loading = false;
				}
			},
			// 添加下拉刷新方法
					async onRefresh() {
						this.isPulling = false;  // 开始刷新时关闭下拉状态
						this.isRefreshing = true;
						
						try {
							// 重置页码和数据
							this.currentPage = 1;
							this.isRefreshing = true;
							
							// 重新加载数据
							await this.getDeviceList();
							
							// 提示刷新成功
							uni.showToast({
								title: '刷新成功',
								icon: 'none',
								duration: 1000
							});
						} catch (error) {
							console.error('刷新失败:', error);
							uni.showToast({
								title: '刷新失败',
								icon: 'none',
								duration: 1000
							});
						} finally {
							// 停止刷新动画
							this.isRefreshing = false;
						}
					},
					
					// 添加下拉事件处理
					onPulling(e) {
						this.isPulling = true;
					},
		},
		
	
	}
</script>
<style>
page {
		background: #16171b;
	}

	.u-modal {
		width: 18rem !important;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}

	.equip_content {

		padding-top: 1rpx;
	}

	.cont {
		display: flex;
		// align-items: center;
		justify-content: center;
	}

	.de_con {
		padding: 0 32rpx;
		padding-top: 16rpx;
		// background: rgba(255, 255, 255, 0.04);
	}
    
	.content {
		// padding: 0 32rpx;
		// height: calc(100vh - 188rpx);
		box-sizing: border-box;
		background: #16171b;
		position: relative;

		/* 自定义下拉刷新样式 */
		:deep(.uni-scroll-view-refresher) {
			width: 100% !important;
			height: 20px;
			background: #16171b;
			display: flex;
			justify-content: center;
			align-items: center;

			/* 隐藏默认图标 */
			.uni-scroll-view-refresher__indicator-box {
				display: none;
			}

			/* 自定义文本 */
			.uni-scroll-view-refresher__indicator {
				&::before {
					content: '加载中';
					color: rgba(255, 255, 255, 0.8);
					font-size: 14px;
				}
			}
		}

		/* 自定义刷新文本样式 */
		.refresh-text {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: rgba(255, 255, 255, 0.8);
			font-size: 14px;
			background: #16171b;
			z-index: 100;
		}

		/* 隐藏默认的刷新图标 */
		:deep(.uni-scroll-view-refresher) {
			.uni-scroll-view-refresher__indicator-box {
				opacity: 0;
			}
		}
	}

	.phone_cont{
		padding:32rpx;
		margin-top: 32rpx;
		border-radius:12rpx;
		background: rgba(255, 255, 255, 0.04);
		border: 0.5px solid rgba(255, 255, 255, 0.0972);
	}
	.phone_bet{
		display: flex;
		justify-content: space-between;
		align-items: center;
	}
	.phone_title{
		color: rgba(255, 255, 255, 0.85);
		font-size: 34rpx;
		line-height: 44rpx;
	}
	.phone_value{
		color: rgba(255, 255, 255, 0.85);
		font-size: 30rpx;
		line-height: 44rpx;
	}
	.phone_btn{
		margin-top: 32rpx;
		border: 1px solid rgba(255, 255, 255, 0.2);
		background: rgba(255, 255, 255, 0);
		color: rgba(255, 255, 255, 0.85);
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		image{
			margin-right: 10rpx;
			width: 40rpx;
			height: 40rpx;
	}
	}
	.phone_green {
		border-radius: 12rpx;
		padding: 12rpx 20rpx;
		&.normal {
			color: #00B042;
			background: rgba(0, 176, 66, 0.12);
		}
		&.abnormal {
			color: #FF4D4F;
			background: rgba(255, 77, 79, 0.12);
		}
	}

	.no-data {
		text-align: center;
		color: rgba(255, 255, 255, 0.6);
		font-size: 32rpx;
		padding: 64rpx 0;
	}

	.no-more {
		text-align: center;
		color: rgba(255, 255, 255, 0.6);
		font-size: 28rpx;
		padding: 32rpx 0;
	}
</style>