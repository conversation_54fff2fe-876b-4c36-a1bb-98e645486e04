<template>
	<view>
		<custom-header title="意见反馈" showBack3 />
		<view class="feedback_content">
			<view class="feedback_header">
				<image src="" mode="aspectFit" class="header_img"></image>
				<text class="header_text">您的反馈是我们进步的动力</text>
			</view>
			
			<view class="feedback_form">
				<view class="form_item">
					<text class="item_label">标题</text>
					<input type="text" 
						v-model="formData.title" 
						placeholder="清晰的标题会被更快的核实" 
						placeholder-style="color: rgba(255,255,255,0.3)"
						class="item_input" />
				</view>
				
				<view class="form_item">
					<text class="item_label">手机号码</text>
					<input type="number" 
						v-model="formData.phone" 
						placeholder="请输入手机号码" 
						placeholder-style="color: rgba(255,255,255,0.3)"
						class="item_input" />
				</view>
				
				<view class="form_item">
					<text class="item_label">详情描述</text>
					<textarea v-model="formData.content" 
						placeholder="请详细描述您的意见使用场景及您的优化/改进方法" 
						placeholder-style="color: rgba(255,255,255,0.3)"
						class="item_textarea" />
				</view>
				
				<button class="submit_btn" @click="handleSubmit">提交</button>
			</view>
		</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request.js'
	import customHeader from '@/components/page/header.vue'
	
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				formData: {
					title: '',
					phone: '',
					content: ''
				}
			}
		},
		methods: {
			validateForm() {
				if (!this.formData.title) {
					uni.showToast({
						title: '请输入标题',
						icon: 'none'
					});
					return false;
				}
				if (!this.formData.phone) {
					uni.showToast({
						title: '请输入手机号码',
						icon: 'none'
					});
					return false;
				}
				if (!/^1[3-9]\d{9}$/.test(this.formData.phone)) {
					uni.showToast({
						title: '请输入正确的手机号码',
						icon: 'none'
					});
					return false;
				}
				if (!this.formData.content) {
					uni.showToast({
						title: '请输入反馈内容',
						icon: 'none'
					});
					return false;
				}
				return true;
			},
			async handleSubmit() {
				if (!this.validateForm()) return;
				uni.showToast({
					title: '提交成功',
					icon: 'none',
					duration: 1000
				});
				uni.switchTab({
					url:'/pages/user/user'
				})
				// try {
				// 	const res = await Request.post('/feedback/submit', this.formData);
					
				// 	if (res.status === 0) {
				// 		uni.showToast({
				// 			title: '提交成功',
				// 			icon: 'success',
				// 			duration: 2000
				// 		});
				// 		setTimeout(() => {
				// 			uni.navigateBack();
				// 		}, 2000);
				// 	} else {
				// 		uni.showToast({
				// 			title: res.msg || '提交失败',
				// 			icon: 'none',
				// 			duration: 2000
				// 		});
				// 	}
				// } catch (error) {
				// 	console.error('Error submitting feedback:', error);
				// 	uni.showToast({
				// 		title: '网络错误，请稍后重试',
				// 		icon: 'none',
				// 		duration: 2000
				// 	});
				// }
			}
		}
	}
</script>

<style lang="scss" scoped>
	page {
		// background: #16171b;
	}
	
	.feedback_content {
		// padding: 32rpx;
	}
	
	.feedback_header {
		display: flex;
		flex-direction: column;
		align-items: center;
		margin-bottom: 48rpx;
		
		.header_img {
			width: 530rpx;
			height: 244rpx;
			margin-bottom: 24rpx;
		}
		
		.header_text {
			font-size: 32rpx;
			font-weight: 500;
			color: rgba(255, 255, 255, 0.85);
		}
	}
	
	.feedback_form {
		padding: 32rpx;
		.form_item {
			margin-bottom: 32rpx;
			
			.item_label {
				display: block;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.85);
				margin-bottom: 16rpx;
			}
			
			.item_input {
				// width: 100%;
				height: 88rpx;
				background: rgba(255, 255, 255, 0.04);
				border-radius: 12rpx;
				padding: 0 24rpx;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.85);
				border: 1rpx solid rgba(255, 255, 255, 0.0972);
			}
			
			.item_textarea {
				width: calc(100% - 52rpx);
				height: 240rpx;
				background: rgba(255, 255, 255, 0.04);
				border-radius: 12rpx;
				padding: 24rpx;
				font-size: 28rpx;
				color: rgba(255, 255, 255, 0.85);
				border: 1rpx solid rgba(255, 255, 255, 0.0972);
			}
		}
		
		.submit_btn {
			position: fixed;
			bottom: 30px;
			width: calc(100% - 64rpx);
			height: 88rpx;
			line-height: 88rpx;
			background: #007BFF;
			border-radius: 12rpx;
			font-size: 32rpx;
			color: rgba(255, 255, 255, 0.85);
			text-align: center;
			margin-top: 48rpx;
		}
	}
</style>