<template>
	<custom-header style="" title="云平台" />
	<view class="content">
		<!-- 施工管理 -->
		<view class="menu-list">
			<text class="apply_header">施工管理</text>
			<view class="list">		
			<view v-for="(item, index) in menuItems" :key="index" class="menu-item" @click="handleMenuClick(item)">
				<view class="menu-item-left">
					<image class="menu-icon" :src="item.icon" />
				</view>
				<view class="menu-item-right">
					<text class="menu-text">{{item.text}}</text>
				</view>
			</view>
			</view>
		</view>
		<!-- 资产管理 -->
		<view class="menu-list">
			<text class="apply_header">资产管理</text>
			<view class="list">		
			<view v-for="(item, index) in proItems" :key="index" class="menu-item" @click="handleproClick(item)">
				<view class="menu-item-left">
					<image class="menu-icon" :src="item.icon" />
				</view>
				<view class="menu-item-right">
					<text class="menu-text">{{item.text}}</text>
				</view>
			</view>
			</view>
		</view>
		<!-- 施工管理 -->
		<view class="menu-list">
			<text class="apply_header">施工管理</text>
			<view class="list">		
			<view v-for="(item, index) in applyItems" :key="index" class="menu-item" @click="handleapplyClick(item)">
				<view class="menu-item-left">
					<image class="menu-icon" :src="item.icon" />
				</view>
				<view class="menu-item-right">
					<text class="menu-text">{{item.text}}</text>
				</view>
			</view>
			</view>
		</view>
		<!-- 其他 -->
		<view class="menu-list">
			<text class="apply_header">其他</text>
			<view class="list">		
			<view v-for="(item, index) in Items" :key="index" class="menu-item" @click="handleClick(item)">
				<view class="menu-item-left">
					<image class="menu-icon" :src="item.icon" />
				</view>
				<view class="menu-item-right">
					<text class="menu-text">{{item.text}}</text>
				</view>
			</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Quene from '../../components/utils/queue'
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue';
	import {
		route
	} from '@/uni_modules/uview-plus';
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				userInfo: {
					// profilePic: '', // 初始化为空字符串，避免初始渲染的问题
					// name: '',
					// account: ''
				},

				menuItems: [
					{
						icon: '/static/image/apply/project.png',
						text: '施工项目',
						path: '/pages/apply/components/project'
					},
					{
						icon: '/static/image/apply/slot.png',
						text: '打孔管理',
						path: '/pages/apply/components/slot'
					}
				],
				proItems: [
					{
						icon: '/static/image/apply/equip.png',
						text: '设备管理',
						path: '/pages/apply/components/equip'
					},
					{
						icon: '/static/image/apply/drill.png',
						text: '钻具管理',
						path: '/pages/apply/components/drill'
					},
					// {
					// 	icon: '/static/image/apply/asset.png',
					// 	text: '资产分组',
					// 	path: '/pages/user/components/security'
					// }
				],
				applyItems: [
					// {
					// 	icon: '/static/image/apply/config.png',
					// 	text: '企业配置',
					// 	path: '/pages/user/notification/notification'
					// },
					{
						icon: '/static/image/apply/dig.png',
						text: '矿区概括',
						path: '/pages/apply/components/dig'
					},
					// {
					// 	icon: '/static/image/apply/inform.png',
					// 	text: '通知提醒',
					// 	path: '/pages/user/components/security'
					// }
				],
				Items: [
					{
						icon: '/static/image/apply/conputer.png',
						text: '运维中心',
						path: '/pages/apply/components/computer'
					},                 
					// {
					// 	icon: '/static/image/apply/math.png',
					// 	text: '数据洞察',
					// 	path: '/pages/user/components/security'
					// },
					// {
					// 	icon: '/static/image/apply/log.png',
					// 	text: '审计日志',
					// 	path: '/pages/apply/components/log'
					// },
					{
						icon: '/static/image/apply/log.png',
						text: '应急处理',
						path: '/pages/apply/components/emergency'
					}
				]
			}
		},
		async onLoad() {
		
		},

		methods: {
			

			// 处理菜单点击
			handleMenuClick: function(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			handleproClick: function(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			handleapplyClick: function(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			handleClick: function(item) {
				uni.navigateTo({
					url: item.path
				})
			},
		}
	}
</script>

<style lang="scss" scoped>
	.content {
		height: 100%;
		// -webkit-overflow-scrolling: touch;
		// overscroll-behavior: contain; 
		// overflow: hidden;
		padding: 0 26rpx;
		padding-top: 1rpx;
	}

	.user-info-card {
		padding: 32rpx;
		background: rgba(255, 255, 255, 0.0362);
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		height: 176rpx;
		border-radius: 12rpx;
		flex-direction: row;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.user-info {
		display: flex;
		flex-direction: row;
		align-items: center;
	}

	.avatar-content {
		width: 104rpx;
		height: 104rpx;
		padding: 4rpx;
		background: rgba(84, 146, 247, 0.6);
		border-radius: 16rpx;
		display: flex;
		flex-direction: row;
		flex-wrap: wrap;
		align-content: flex-start;
	}

	.avatar {
		width: 100%;
		height: 100%;
		border-radius: 16rpx;
	}

	.info-content {
		margin-left: 24rpx;
		display: flex;
		flex-direction: column;
	}

	.content-name {
		display: flex;
		flex-direction: row;
		align-items: center;
		text-align: center;
		margin-bottom: 24rpx;
	}

	.username {
		font-size: 40rpx;
		margin-right: 28rpx;
		color: #FFFFFF;
		font-weight: 500;
	}
.surealname{
	padding: 8rpx;
	font-size: 24rpx;
	border-radius: 12rpx;
	background: rgba(22, 119, 255, 0.12);
	color: #1677FF;
	font-weight: normal;
}
	.realname {
		padding: 8rpx;
		font-size: 24rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.12);
		color: rgba(255, 255, 255, 0.85);
		font-weight: normal;
	}

	.phone {
		font-size: 28rpx;
		font-family: PingFang SC;
		color: rgba(255, 255, 255, 0.65);
	}

	.menu-list {
		margin-top: 44rpx;
		background: rgba(255, 255, 255, 0.0362);
		box-sizing: border-box;
		padding: 32rpx;
		border-radius: 12rpx;
	}
	.list{
		display: flex;
		
	}
	.apply_header{
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.7);
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		
	}
	.menu-item {
		margin-top: 26.5rpx;
		display: flex;
		height: 116rpx;
		padding: 0 34rpx;
		flex-direction: column;
		align-items: center;
		justify-content: center;
	}

	.menu-item-left {
		padding: 24rpx;
		// height: 84rpx;
		// width: 84rpx;
		display: flex;
		flex-direction: row;
		justify-content: center;
		align-items: center;
		margin-bottom: 14rpx;
		border-radius: 32rpx;
		background: rgba(255, 255, 255, 0.1);
	}

	.menu-item-right {
		flex: 1;
		height: 100%;
		// padding-right: 32rpx;
		flex-direction: row;
		align-items: center;
		text-align: center;
		// padding-top: 32rpx;
		display: flex;
		// border-bottom: 1rpx solid rgba(167, 169, 172, 0.15);
		justify-content: space-between;
	}

	.menu-item:last-child .menu-item-right {
		border-bottom: none;
	}

	.menu-icon {
		width: 36rpx;
		height: 36rpx;
		// margin-right: 32rpx;
	}

	.menu-text {
		font-family: PingFang SC;
		font-size: 21rpx;
		letter-spacing: 0px;
		font-weight: normal;
		color:rgba(255, 255, 255, 0.85);
	}

	.arrow-right {
		width: 60rpx;
		height: 60rpx;
	}
</style>