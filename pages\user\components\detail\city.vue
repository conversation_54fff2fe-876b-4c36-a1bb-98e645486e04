<template>
  <view class="city-page">
    <custom-header style="height: 88rpx;" title="选择城市" showBack />
    <view class="area-list">
      <view
        class="area-item"
        v-for="(city, index) in cities"
        :key="index"
        @click="selectCity(city)"
      >
        <text>{{ city.name }}</text>
      </view>
    </view>
  </view>
</template>

<script>
import Request from '@/components/utils/request'
import citiesData from '@/static/area/city.json'
import customHeader from '@/components/page/header.vue'
import Quene from '@/components/utils/queue'
import { url } from '../../../../uni_modules/uview-plus/libs/function/test'

export default {
    components: {
      customHeader
    },
    data() {
      return {
          cities: [],
          provinceId: '',
          provinceName: '',
          userInfo: {}
      }
    },
    onShow() {
      this.userInfo = Quene.getData('userinfo')
    },
    onLoad(options) {
      this.provinceId = options.provinceId
      this.provinceName = options.provinceName
      this.loadCities()
    },
    computed: {
      citys() {
         if (this.provinceId) {
           return citiesData[this.provinceId] || []
         }
         return []
      }
    },
    methods: {
       loadCities(){
           if (!this.citys || this.citys.length === 0) {
             this.cities = []
             this.showErrorToast("该省份下没有城市数据")
             return
           }
           this.cities = this.citys.map((item) => ({
                  name: item.name,
                  code: item.id,
                  data: item
            }))
          if(this.cities.length === 0){
               this.showErrorToast("该省份下没有城市数据")
          }
       },
     async selectCity(city) {
       if(!city){
          this.showErrorToast("请选择城市");
          return;
       }
      try {
          const params = {
              province: this.provinceId,
              city: city.code
          };
          const res = await Request.post('/personal/post_modify', params);

          if (res.status === 0) {
              // 使用事件通知更新城市信息
              uni.$emit('updateUserInfo', {
                  province: this.provinceId,
                  city: city.code
              });
              
              uni.navigateTo({
              	url: `/pages/user/components/detail`
              })
          } else {
              this.showErrorToast(res.msg || '修改失败');
          }
        } catch (error) {
           this.showErrorToast('网络错误，请稍后重试');
           console.error('修改失败:', error);
        }
     },
     showErrorToast(title) {
          uni.showToast({
               title,
              icon: 'none'
          })
     }
    }
}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
.city-page {
  min-height: 100vh;
  box-sizing: border-box;
}

.area-list {
	padding-top: 156rpx;
  background: rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
}

.area-item {
  color: rgba(255, 255, 255, 0.65);
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid rgba(255, 255, 255, 0.0972);
  cursor: pointer;
  
  &:last-child {
    border: none;
  }
}
</style>
