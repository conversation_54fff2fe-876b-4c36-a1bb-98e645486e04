export interface V2NIMChatroomUpdateParams {
    /**
     * 聊天室名称
     */
    roomName?: string;
    /**
     * 聊天室公告
     */
    announcement?: string;
    /**
     * 聊天室直播地址
     */
    liveUrl?: string;
    /**
     * 聊天室扩展字段
     */
    serverExtension?: string;
    /**
     * 是否需要通知
     */
    notificationEnabled?: boolean;
    /**
     * 本次操作生成的通知中的扩展字段
     */
    notificationExtension?: string;
}
export interface V2NIMChatroomtagsUpdateParams {
    /**
     * 标签，可以设置多个。如果传空，或者传入空数组，会删除原有标签
     */
    tags?: string[];
    /**
     * 消息的目标标签表达式
     */
    notifyTargetTags?: string;
    /**
     * 是否需要通知。默认值为 true
     */
    notificationEnabled?: boolean;
    /**
     * 本次操作生成的通知中的扩展字段
     */
    notificationExtension?: string;
}
