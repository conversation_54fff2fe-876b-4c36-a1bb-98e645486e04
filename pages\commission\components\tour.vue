<template>
	<view class="content_page">
		
	<custom-header style="height: 88rpx;" title="云平台" showBack />
	<view class="content">
		
		<view class="menu-list">
		
			<view class="menu-item">
				<view class="menu-item-right">
					<text class="menu-text">操作人</text>
					
					<view class="menu-item-content">
						{{submitter}}
					</view>
				</view>
			</view>
			
			<view class="menu-item" >
				<view class="">
					<view class="menu-text">备注</view>
					<textarea class="menu_textarea" v-model="remark" value="" placeholder="请输入交接班信息" />
				</view>
			</view>
			
		</view>
		<view class="search_btn">
			<button class="btn2" @click="confirm">提交</button>
		</view>
	</view>
	</view>
</template>

<script>
	import customHeader from '@/components/page/header.vue';

	import config from '@/components/utils/config';
	import Request from '@/components/utils/request';
	export default {
		inheritAttrs: false,
		components: {
			customHeader
		},

		data() {

			return {
				remark:'',
				id:'',
				submitter:''
			}
		},
		computed: {
			
		},
		onLoad(options) {
			if (options.id) {
				this.id = options.id
				this.submitter=options.submitter
				// this.getData()
			}
		},
		onShow() {
		
		},
		methods: {	
			async confirm(){
				try {
					if (!this.remark.trim()) {
						uni.showToast({
							title: '请输入交接班信息',
							icon: 'none'
						});
						return;
					}
					const params = {
						id: this.id, // 待办事项的 id
						action: 1 ,// 操作类型
						remark:this.remark
					};
					const res = await Request.post('/todo/post_modify', params);
					if (res.status == 0) {
						// 更新待办事项状态
						
						uni.showToast({
							title: '已处理',
							icon: 'none'
						});
						uni.switchTab({
							url:'/pages/commission/commission'
						})
						// this.getTodoList();
					} else {
						uni.showToast({
							title: res.message || '处理失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('处理同意操作失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				}
			}
		}

	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	.content_page{
		box-sizing: border-box;
	}
	.content {
		padding: 32rpx;
		// padding-top: 32rpx;
		// margin-top: 166rpx;
		// position: relative;
		box-sizing: border-box;
	}

	.menu-list {
		// background: rgba(255, 255, 255, 0.0362);
		box-sizing: border-box;
		// border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 12rpx;
	}

	.menu-item {
		display: flex;
		background: rgba(255, 255, 255, 0.08);
		margin-bottom: 32rpx;
		border-radius: 8rpx;
		padding: 32rpx;
		// height: 116rpx;
		flex-direction: row;
		align-items: center;
	}

	.avatar {
		width: 56rpx;
		height: 56rpx;
		border-radius: 8rpx;
	}

	.menu-item-left {
		display: flex;
		align-items: center;
	}
.menu_textarea{
	margin-top: 20rpx;
	color: rgba(255,255,255,0.85);
	font-size: 28rpx;
}
	.menu-item-right {
		flex: 1;
		height: 100%;
		padding-right: 32rpx;
		flex-direction: row;
		text-align: center;
		// padding-top: 32rpx;
		align-items: center;
		display: flex;
		// border-bottom: 1rpx solid rgba(167, 169, 172, 0.15);
		justify-content: space-between;
	}

	.menu-item:last-child .menu-item-right {
		border-bottom: none;
	}

	.menu-item-content {

color: rgba(255, 255, 255, 0.65);
		font-size: 28rpx;
		letter-spacing: 0px;
		line-height: 42rpx;
	}

	.menu-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 32rpx;
	}

	.menu-text {
		font-family: PingFang SC;
		font-size: 28rpx;
		letter-spacing: 0px;
		line-height: 42rpx;
		font-weight: normal;
		color: rgba(255, 255, 255, 0.85);
	}

	.arrow-right {
		width: 60rpx;
		height: 60rpx;
	}
	.search_btn{
		position: fixed;
		bottom: 30px;
		// display: flex;
		width: 92%;
	}
	
	.btn2{
		width: 100%;
		color: rgba(255,255,255,0.85);
		background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	}
</style>