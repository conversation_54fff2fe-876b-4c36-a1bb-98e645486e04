var __renderjsModules={};
__renderjsModules["0020cf84"]=(()=>{var ri=Object.defineProperty;var qn=Object.getOwnPropertyDescriptor;var jn=Object.getOwnPropertyNames;var zn=Object.prototype.hasOwnProperty;var Ys=Math.pow;var Xn=(s,e)=>{for(var t in e)ri(s,t,{get:e[t],enumerable:!0})},Qn=(s,e,t,i)=>{if(e&&typeof e=="object"||typeof e=="function")for(let r of jn(e))!zn.call(s,r)&&r!==t&&ri(s,r,{get:()=>e[r],enumerable:!(i=qn(e,r))||i.enumerable});return s};var Jn=s=>Qn(ri({},"__esModule",{value:!0}),s);var Ws=(s,e,t)=>new Promise((i,r)=>{var n=l=>{try{a(t.next(l))}catch(c){r(c)}},o=l=>{try{a(t.throw(l))}catch(c){r(c)}},a=l=>l.done?i(l.value):Promise.resolve(l.value).then(n,o);a((t=t.apply(s,e)).next())});var _c={};Xn(_c,{default:()=>Lc});function A({container:s,src:e,title:t,poster:i,formats:r,barrages:n,barrageShow:o,barrageGap:a,barrageConfig:l,controls:c,playShow:h=!0,progressShow:u=!0,timeShow:d=!0,volumeShow:f=!0,settingShow:g=!0,fullscreenShow:m=!0,fullscreen:y=!1,autoplay:T,mirror:v,pictureInPicture:E,nextBtnShow:_,prevBtnShow:x,muted:I,loop:L,preload:C,settings:k,initialTime:R,duration:D,volume:V,longpressPlaybackRate:P,playbackRate:K,objectFit:H,crossOrigin:N,segments:q,isLive:X,flvConfig:F,enableLongpressPlaybackRate:O,enableDoubleToggle:z,enableBlob:W}){if(!(this instanceof A))throw new TypeError("TypeError: Class constructor YBPlayer cannot be invoked without 'new'");this.container=typeof s=="string"?document.getElementById(s):s,this.src=e||"",this.title=t||"",this.poster=i||"",this.formats=r||"auto",this.barrages=n,this.barrageShow=o,this.barrageGap=a||5,this.barrageConfig=l,this.controls=c,this.playShow=h,this.progressShow=u,this.timeShow=d,this.volumeShow=f,this.settingShow=g,this.fullscreenShow=m,this.fullscreen=y,this.autoplay=T,this.mirror=v,this.pictureInPicture=E,this.nextBtnShow=_,this.prevBtnShow=x,this.muted=I,this.loop=L,this.preload=C||"auto",this.settings=k||"all",this.initialTime=R||0,this.duration=D||0,this.volume=V||1,this.longpressPlaybackRate=P||3,this.playbackRate=K||1,this.objectFit=H||"contain",this.crossOrigin=N||"",this.segments=q,this.isLive=X,this.flvConfig=F||{},this.enableLongpressPlaybackRate=O,this.enableDoubleToggle=z,this.enableBlob=W,this.video=null,this.hls=null,this.flv=null,this.barrage=null,this._eventCallback={},this._wrapperEl=null,this._videoEl=null,this._posterEl=null,this._videoBackground="inherit",this._headersEl=null,this._controlsEl=null,this._barrageEl=null,this._settingEl=null,this._playbackRateEl=null,this._slotsEl=null,this._controlSlotsEl=null,this._longPlaybackRateEl=null,this._isDrag=!1,this._controlsTimer=null,this._seizingTimer=null,this._clickTime=0,this._touchstartX=0,this._touchstartY=0,this._touchmoveX=0,this._touchmoveY=0,this._touchTime=0,this._mousedown=!1,this._init()}Object.defineProperty(A.prototype,"_init",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._init is not a constructor");if(this.container&&typeof this.container!="undefined")try{this.container.style.position="relative",this.container.style.overflow="hidden",this.container.addEventListener("fullscreenerror",this._fullscreenerror.bind(this)),this.container.addEventListener("mozfullscreenerror",this._fullscreenerror.bind(this)),this.container.addEventListener("msfullscreenerror",this._fullscreenerror.bind(this)),this.container.addEventListener("webkitfullscreenerror",this._fullscreenerror.bind(this)),this.container.addEventListener("fullscreenchange",this._fullscreenchanged.bind(this)),this.container.addEventListener("mozfullscreenchange",this._fullscreenchanged.bind(this)),this.container.addEventListener("msfullscreenchange",this._fullscreenchanged.bind(this)),this.container.addEventListener("webkitfullscreenchange",this._fullscreenchanged.bind(this)),this._wrapperEl=document.createElement("DIV"),this._wrapperEl.setAttribute("class","ybplayer-video-wrapper"),this.container.appendChild(this._wrapperEl);let s=()=>{this._touchstartX=0,this._touchstartY=0,this._touchmoveX=0,this._touchmoveY=0,this._touchTime=0,this._clickTime=0},e=50,t=150,i=o=>{this._touchTimer&&(window.clearTimeout(this._touchTimer),this._touchTimer=null),this._longTimer&&(window.clearTimeout(this._longTimer),this._longTimer=null);let a=o.touches[0];this._touchstartX=a.pageX,this._touchstartY=a.pageY,this._clickTime++,this._clickTime==1&&(this._touchTimer=window.setTimeout(()=>{this._touchTime=t},t),this._longTimer=window.setTimeout(()=>{this._touchTime=500,this._touchmoveX<=e&&this._touchmoveY<=e&&this.enableLongpressPlaybackRate&&(this._isLongTouch=1,this._showLongPlaybackRate()),s()},500))},r=o=>{if(this._clickTime>0){let a=o.touches[0];this._touchmoveX=Math.abs(a.pageX-this._touchstartX),this._touchmoveY=Math.abs(a.pageY-this._touchstartY)}},n=o=>{if(this._touchTimer&&(window.clearTimeout(this._touchTimer),this._touchTimer=null),this._longTimer&&(window.clearTimeout(this._longTimer),this._longTimer=null),this._hideLongPlaybackRate(),this._clickTime==0||this._touchTime>t||this._touchmoveX>e||this._touchmoveY>e){s();return}this._clickTime==1&&(this._touchTimer=window.setTimeout(()=>{this._playbackRateEl?this._hidePlaybackRate():this._settingEl?this._hideSetting():this._controlsEl&&(this._controlsEl.classList.value.indexOf("ybplayer-controls-show")>-1?this._hideControls():this._showControls()),s()},t)),this._clickTime==2&&(this.enableDoubleToggle&&this.toggle(),s())};this._wrapperEl.ontouchstart=i,this._wrapperEl.ontouchmove=r,this._wrapperEl.ontouchend=n,this._wrapperEl.onmousedown=o=>{"ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch||(this._mousedown=!0,i({touches:[{pageX:o.pageX,pageY:o.pageY}]}))},this._wrapperEl.onmousemove=o=>{this._mousedown&&r({touches:[{pageX:o.pageX,pageY:o.pageY}]})},this._wrapperEl.onmouseup=o=>{this._mousedown=!1,n({touches:[{pageX:o.pageX,pageY:o.pageY}]})},this._initVideo(),this._initSlots(),this._setVideoUrl(),this.fullscreen&&this._initHeaders(),this.controls&&this._initControls()}catch(s){throw new Error(s.toString())}},enumerable:!1});Object.defineProperty(A.prototype,"_destroy",{value:function(s=!0){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroy is not a constructor");this._destroyHls(),this._destroyFlv(),this._destroyControls(),this._destroyHeaders(),this._destroyBarrage(),this._destroyVideo(),this._destroySlots(),this._wrapperEl&&(this._wrapperEl.onclick=null,this._wrapperEl.remove(),this._wrapperEl=null),this.container&&s&&(this.container.removeEventListener("fullscreenerror",this._fullscreenerror.bind(this)),this.container.removeEventListener("mozfullscreenerror",this._fullscreenerror.bind(this)),this.container.removeEventListener("msfullscreenerror",this._fullscreenerror.bind(this)),this.container.removeEventListener("webkitfullscreenerror",this._fullscreenerror.bind(this)),this.container.removeEventListener("fullscreenchange",this._fullscreenchanged.bind(this)),this.container.removeEventListener("mozfullscreenchange",this._fullscreenchanged.bind(this)),this.container.removeEventListener("msfullscreenchange",this._fullscreenchanged.bind(this)),this.container.removeEventListener("webkitfullscreenchange",this._fullscreenchanged.bind(this)),this.container=null)},enumerable:!1});Object.defineProperty(A.prototype,"_initVideo",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._initVideo is not a constructor");this._videoEl=document.createElement("DIV"),this._videoEl.setAttribute("class","ybplayer-video-content"),this._wrapperEl.appendChild(this._videoEl),this.video=document.createElement("VIDEO"),this.video.setAttribute("style","width: 100%;height:100%;flex:1;opacity:0;object-fit:"+this.objectFit+";"),this.video.setAttribute("preload",this.preload),this.video.setAttribute("volume",this.volume),this.video.setAttribute("x-webkit-airplay","allow"),this.video.setAttribute("webkit-playsinline",!0),this.video.setAttribute("playsinline",!0),this.video.setAttribute("x5-video-player-type","h5"),this.video.setAttribute("x5-video-play",!0),this.crossOrigin&&this.video.setAttribute("crossOrigin",this.crossOrigin),this.video.innerHTML="\u60A8\u7684\u6D4F\u89C8\u5668\u4E0D\u652F\u6301 video \u6807\u7B7E\u3002",this._videoEl.appendChild(this.video),this._showPoster(),this.video.muted=this.muted,this.video.defaultMuted=this.muted,this.video.playbackRate=this.playbackRate,this.video.defaultPlaybackRate=this.playbackRate,this.video.autoplay=this.autoplay,this.video.loop=this.loop,this.video.oncanplay=()=>{this._eventCallback.canplay&&this._eventCallback.canplay({duration:this.video.duration,width:this.video.videoWidth,height:this.video.videoHeight})},this.video.oncanplaythrough=()=>{this._eventCallback.canplaythrough&&this._eventCallback.canplaythrough({duration:this.video.duration,width:this.video.videoWidth,height:this.video.videoHeight})},this.video.onloadeddata=()=>{this._eventCallback.loadeddata&&this._eventCallback.loadeddata({duration:this.video.duration,width:this.video.videoWidth,height:this.video.videoHeight}),this.video.currentTime=this.initialTime,this.video.style.opacity=1},this.video.onloadedmetadata=()=>{this._eventCallback.loadedmetadata&&this._eventCallback.loadedmetadata({duration:this.video.duration,width:this.video.videoWidth,height:this.video.videoHeight}),this.barrageShow&&this._initBarrage()},this.video.onloadstart=()=>{this._eventCallback.loadstart&&this._eventCallback.loadstart()},this.video.onplay=()=>{this._hidePoster(),this._eventCallback.play&&this._eventCallback.play(),this.barrage&&this.barrage.play(),this._setControlsView("play"),this.duration&&this.video.currentTime>=this.duration&&!this.isLive&&this.seek(0),this.flv&&this.flv.play()},this.video.onpause=()=>{this._eventCallback.pause&&this._eventCallback.pause(),this.barrage&&this.barrage.pause(),this._setControlsView("play"),this.flv&&this.flv.pause()},this.video.onended=()=>{this._eventCallback.ended&&this._eventCallback.ended()},this.video.onseeking=()=>{this._eventCallback.seeking&&this._eventCallback.seeking({currentTime:this.video.currentTime})},this.video.onseeked=()=>{this._eventCallback.seeked&&this._eventCallback.seeked({currentTime:this.video.currentTime}),this.barrage&&this.barrage.seek(this.video.currentTime)},this.video.ontimeupdate=()=>{this._eventCallback.timeupdate&&this._eventCallback.timeupdate({currentTime:this.video.currentTime}),this.duration&&this.video.currentTime>=this.duration&&!this.isLive&&(this.loop?this.seek(0):(this.pause(),this._eventCallback.ended&&this._eventCallback.ended())),this._seizingTimer&&(window.clearTimeout(this._seizingTimer),this._seizingTimer=null),this._seizingTimer=window.setTimeout(()=>{this.video&&this.video.src&&!this.video.paused&&this._eventCallback.seizing&&this._eventCallback.seizing()},5e3),this._setControlsView("timeUpdate")},this.video.ondurationchange=()=>{this._eventCallback.durationchange&&this._eventCallback.durationchange({duration:this.video.duration})},this.video.onwaiting=()=>{this._eventCallback.waiting&&this._eventCallback.waiting({currentTime:this.video.currentTime}),this.barrage&&this.barrage.pause()},this.video.onplaying=()=>{this._eventCallback.playing&&this._eventCallback.playing({currentTime:this.video.currentTime}),this.barrage&&this.barrage.play()},this.video.onprogress=()=>{this._eventCallback.progress&&this._eventCallback.progress({buffered:this.video.buffered})},this.video.onabort=()=>{this._eventCallback.abort&&this._eventCallback.abort()},this.video.onerror=s=>{this._eventCallback.error&&this._eventCallback.error(this._deepClone(s))},this.video.onvolumechange=()=>{this._eventCallback.volumechange&&this._eventCallback.volumechange({volume:this.video.volume}),this._setControlsView("volume")},this.video.onratechange=()=>{if(this._eventCallback.ratechange&&this._eventCallback.ratechange({playbackRate:this.video.playbackRate}),this._playbackRateEl&&this._querySelector("ybplayer-setting"))for(let s=0;s<this._querySelectorAll("ybplayer-setting").length;s++)this._querySelectorAll("ybplayer-setting")[s].getElementsByClassName("ybplayer-setting-text")[0].style.color=this.playbackRate==this._querySelectorAll("ybplayer-setting")[s].getAttribute("data-rate")?"#2ca2f9":"#333";this.barrage&&this.barrage.setConfig({playbackRate:this.video.playbackRate})},this.video.onenterpictureinpicture=()=>{this._eventCallback.enterpictureinpicture&&this._eventCallback.enterpictureinpicture(),this.pictureInPicture=!0,this._setSettingView("pictureInPicture")},this.video.onleavepictureinpicture=()=>{this._eventCallback.leavepictureinpicture&&this._eventCallback.leavepictureinpicture(),this.pictureInPicture=!1,this._setSettingView("pictureInPicture")},this._barrageEl=document.createElement("DIV"),this._barrageEl.setAttribute("class","ybplayer-video-barrage"),this._barrageEl.setAttribute("style","margin:"+this.barrageGap+"px 0;"),this._wrapperEl.appendChild(this._barrageEl)},enumerable:!1});Object.defineProperty(A.prototype,"_destroyVideo",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroyVideo is not a constructor");this.video&&(this.video.pause(),this.video.removeAttribute("src"),this.video.load(),this.video.oncanplay=null,this.video.onloadedmetadata=null,this.video.onplay=null,this.video.onpause=null,this.video.onended=null,this.video.onseeking=null,this.video.onseeked=null,this.video.ontimeupdate=null,this.video.ondurationchange=null,this.video.onwaiting=null,this.video.onplaying=null,this.video.onprogress=null,this.video.onabort=null,this.video.onerror=null,this.video.onvolumechange=null,this.video.onratechange=null,this.video.onenterpictureinpicture=null,this.video.onleavepictureinpicture=null,this.video.remove(),this.video=null),this._hidePoster(),this._hideLongPlaybackRate(),this._videoEl&&(this._videoEl.remove(),this._videoEl=null),this._barrageEl&&(this._barrageEl.remove(),this._barrageEl=null),this._seizingTimer&&(window.clearTimeout(this._seizingTimer),this._seizingTimer=null)},enumerable:!1});Object.defineProperty(A.prototype,"_showPoster",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._showPoster is not a constructor");if(this._videoEl&&this.poster&&this.video.paused){let s=new Image;s.onload=()=>{this.video.paused&&(this._posterEl=document.createElement("DIV"),this._posterEl.setAttribute("class","ybplayer-video-poster"),this._posterEl.setAttribute("style",`background-size: ${this.objectFit};background-image: url(${this.poster})`),this._videoEl.appendChild(this._posterEl)),s=null},s.onerror=()=>{s=null},s.src=this.poster}},enumerable:!1});Object.defineProperty(A.prototype,"_hidePoster",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._hidePoster is not a constructor");this._posterEl&&(this._posterEl.onerror=null,this._posterEl.onload=null,this._posterEl.remove(),this._posterEl=null)},enumerable:!1});Object.defineProperty(A.prototype,"_initHeaders",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._initHeaders is not a constructor");this.container&&this.title&&(this._destroyHeaders(),this._headersEl=document.createElement("DIV"),this._headersEl.setAttribute("class","ybplayer-headers"),this._headersEl.innerHTML=`
				<div class="ybplayer-headers-shadow"></div>
				<i class="ybplayerIconfont icon-angle-arrow-left ybplayer-icon ${this._setClassName("ybplayer-icon-back")}"></i>
				<span class="${this._setClassName("ybplayer-headers-title")}">${this.title}</span>
			`,this.container.appendChild(this._headersEl),this._querySelector("ybplayer-icon-back").onclick=()=>{this.exitFullscreen()})},enumerable:!1});Object.defineProperty(A.prototype,"_destroyHeaders",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroyHeaders is not a constructor");this._headersEl&&(this._querySelector("ybplayer-icon-back").onclick=null,this._headersEl.remove(),this._headersEl=null)},enumerable:!1});Object.defineProperty(A.prototype,"_initControls",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._initControls is not a constructor");this.container&&(this._controlsEl=document.createElement("DIV"),this._controlsEl.setAttribute("class","ybplayer-controls"),this._controlsEl.innerHTML=`
				<div class="ybplayer-controls-top">
					<div class="${this._setClassName("ybplayer-icon-play-prev")} ybplayer-controls-item" style="margin-right: ${this.prevBtnShow?"10px":0};">
						${this.prevBtnShow?`
						<i class="ybplayerIconfont icon-play-prev-fill ybplayer-icon"></i>
						`:""}
					</div>
					<div class="${this._setClassName("ybplayer-icon-play")} ybplayer-controls-item">
						${this.playShow?`
						<i class="ybplayerIconfont ${this.video&&!this.video.paused?"icon-pause":"icon-play"} ybplayer-icon"></i>
						`:""}
					</div>
					<div class="${this._setClassName("ybplayer-icon-play-next")} ybplayer-controls-item" style="margin-left: ${this.nextBtnShow?"10px":0};">
						${this.nextBtnShow?`
						<i class="ybplayerIconfont icon-play-next-fill ybplayer-icon"></i>
						`:""}
					</div>
					<div class="${this._setClassName("ybplayer-controls-time")} ybplayer-controls-item">
						${this.timeShow?" 00:00 / 00:00 ":""}
					</div>
					<div class="${this._setClassName("ybplayer-icon-fullscreen")} ybplayer-controls-item ybplayer-controls-item-right" style="margin-left: ${this.fullscreenShow?"25px":0};">
						${this.fullscreenShow?`
						<i class="ybplayerIconfont ${this.fullscreen?"icon-out-fullscreen":"icon-in-fullscreen"} ybplayer-icon"></i>
						`:""}
					</div>
					<div class="${this._setClassName("ybplayer-icon-setting")} ybplayer-controls-item ybplayer-controls-item-right" style="margin-left: ${this.settingShow?"25px":0};">
						${this.settingShow?`
						<i class="ybplayerIconfont icon-setting ybplayer-icon"></i>
						`:""}
					</div>
					<div class="${this._setClassName("ybplayer-icon-volume")} ybplayer-controls-item ybplayer-controls-item-right" style="margin-left: ${this.volumeShow?"25px":0};">
						${this.volumeShow?`
						<i class="ybplayerIconfont icon-volume-${this.muted?"muted":"medium"} ybplayer-icon"></i>
						`:""}
					</div>
				</div>
				<div class="${this._setClassName("ybplayer-controls-progress")}">
				</div>
				<div class="ybplayer-controls-shadow"></div>
			`,this.container.appendChild(this._controlsEl),this._controlsEl.onmousedown=()=>{this._clear_controlsTimer()},this._controlsEl.onmouseleave=()=>{this._start_controlsTimer()},this.playShow&&(this._querySelector("ybplayer-icon-play").onclick=()=>{this.toggle()}),this.volumeShow&&(this._querySelector("ybplayer-icon-volume").onclick=()=>{this.setConfig("muted",!this.muted)}),this.settingShow&&(this._querySelector("ybplayer-icon-setting").onclick=()=>{this._showSetting()}),this.fullscreenShow&&(this._querySelector("ybplayer-icon-fullscreen").onclick=()=>{this.switchFullscreen()}),this.prevBtnShow&&(this._querySelector("ybplayer-icon-play-prev").onclick=()=>{this._eventCallback.prevBtnClick&&this._eventCallback.prevBtnClick()}),this.nextBtnShow&&(this._querySelector("ybplayer-icon-play-next").onclick=()=>{this._eventCallback.nextBtnClick&&this._eventCallback.nextBtnClick()}),this._initProgress(),this._showControls())},enumerable:!1});Object.defineProperty(A.prototype,"_destroyControls",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroyControls is not a constructor");if(this._controlsEl&&(this._destroyProgress(),this._controlsEl.onmousedown=null,this._controlsEl.onmouseleave=null,this.playShow&&this._querySelector("ybplayer-icon-volume")&&(this._querySelector("ybplayer-icon-play").onclick=null),this.volumeShow&&this._querySelector("ybplayer-icon-volume")&&(this._querySelector("ybplayer-icon-volume").onclick=null),this.settingShow&&this._querySelector("ybplayer-icon-setting")&&(this._querySelector("ybplayer-icon-setting").onclick=null),this.fullscreenShow&&this._querySelector("ybplayer-icon-fullscreen")&&(this._querySelector("ybplayer-icon-fullscreen").onclick=null),this.prevBtnShow&&this._querySelector("ybplayer-icon-play-prev")&&(this._querySelector("ybplayer-icon-play-prev").onclick=null),this.nextBtnShow&&this._querySelector("ybplayer-icon-play-next")&&(this._querySelector("ybplayer-icon-play-next").onclick=null),this._controlsEl.remove(),this._controlsEl=null),this._settingEl){for(let s=0;s<this._querySelectorAll("ybplayer-setting").length;s++)this._querySelectorAll("ybplayer-setting").onclick=null;this._settingEl.remove(),this._settingEl=null}if(this._playbackRateEl){for(let s=0;s<this._querySelectorAll("ybplayer-setting").length;s++)this._querySelectorAll("ybplayer-setting")[s].onclick=null;this._playbackRateEl.remove(),this._playbackRateEl=null}},enumerable:!1});Object.defineProperty(A.prototype,"_showControls",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._showControls is not a constructor");this._clear_controlsTimer(),this._controlsEl&&(this._controlsEl.setAttribute("class","ybplayer-controls ybplayer-controls-show"),this._controlSlotsEl&&(this._controlSlotsEl.style.visibility="visible"),this._headersEl&&this._headersEl.setAttribute("class","ybplayer-headers ybplayer-headers-show"),this._eventCallback.controlsChange&&this._eventCallback.controlsChange({controls:!0}),this._start_controlsTimer())},enumerable:!1});Object.defineProperty(A.prototype,"_hideControls",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._hideControls is not a constructor");this._clear_controlsTimer(),this._controlsEl&&(this._controlsEl.setAttribute("class","ybplayer-controls ybplayer-controls-hide"),this._headersEl&&this._headersEl.setAttribute("class","ybplayer-headers ybplayer-headers-hide"),this._controlSlotsEl&&(this._controlSlotsEl.style.visibility="hidden"),this._eventCallback.controlsChange&&this._eventCallback.controlsChange({controls:!1}))},enumerable:!1});Object.defineProperty(A.prototype,"_setControlsView",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setControlsView is not a constructor");if(this.controls)if(s=="play"&&this._querySelector("ybplayer-icon-play"))this.playShow?this._querySelector("ybplayer-icon-play").innerHTML=`<i class="ybplayerIconfont icon-${this.video.paused?"play":"pause"} ybplayer-icon"></i>`:this._querySelector("ybplayer-icon-play").innerHTML="";else if(s=="timeUpdate"){if(!this._isDrag)if(this.timeShow||this.progressShow){var e=this.video&&this.video.currentTime&&this.video.currentTime!="Infinity"?this.video.currentTime:0,t=this.isLive?this.duration:this.video?this.duration||(this.video.duration&&this.video.duration!="Infinity"?this.video.duration:0):0,i=t>0?e/t:0;this.progressShow&&(this._querySelector("ybplayer-slider-focus")&&(this._querySelector("ybplayer-slider-focus").style.width=i*100+"%"),this._querySelector("ybplayer-slider")&&(this._querySelector("ybplayer-slider").value=i*100)),this._querySelector("ybplayer-controls-time")&&this.timeShow&&(this._querySelector("ybplayer-controls-time").innerHTML=this.isLive?this._timesFormat(parseInt(t)+parseInt(e)):`${this._timesFormat(e)} / ${this._timesFormat(t)}`)}else this._querySelector("ybplayer-controls-time").innerHTML=""}else s=="volume"&&this._querySelector("ybplayer-icon-volume")?this.volumeShow?(this._querySelector("ybplayer-icon-volume").style.marginLeft="25px",this._querySelector("ybplayer-icon-volume").innerHTML='<i class="ybplayerIconfont icon-volume-'+(this.muted?"muted":"medium")+' ybplayer-icon"></i>',this._querySelector("ybplayer-icon-volume").onclick=()=>{this.setConfig("muted",!this.muted)}):(this._querySelector("ybplayer-icon-volume").style.marginLeft="",this._querySelector("ybplayer-icon-volume").innerHTML="",this._querySelector("ybplayer-icon-volume").onclick=null):s=="setting"&&this._querySelector("ybplayer-icon-setting")?this.settingShow?(this._querySelector("ybplayer-icon-setting").style.marginLeft="25px",this._querySelector("ybplayer-icon-setting").innerHTML='<i class="ybplayerIconfont icon-setting ybplayer-icon"></i>',this._querySelector("ybplayer-icon-setting").onclick=()=>{this._showSetting()}):(this._querySelector("ybplayer-icon-setting").style.marginLeft="",this._querySelector("ybplayer-icon-setting").innerHTML="",this._querySelector("ybplayer-icon-setting").onclick=null):s=="fullscreen"&&this._querySelector("ybplayer-icon-fullscreen")?this.fullscreenShow?(this._querySelector("ybplayer-icon-fullscreen").style.marginLeft="25px",this._querySelector("ybplayer-icon-fullscreen").innerHTML='<i class="ybplayerIconfont icon-'+(this.fullscreen?"out-fullscreen":"in-fullscreen")+' ybplayer-icon"></i>',this._querySelector("ybplayer-icon-fullscreen").onclick=()=>{this.switchFullscreen()}):(this._querySelector("ybplayer-icon-fullscreen").style.marginLeft="",this._querySelector("ybplayer-icon-fullscreen").innerHTML="",this._querySelector("ybplayer-icon-fullscreen").onclick=null):s=="prevBtn"&&this._querySelector("ybplayer-icon-play-prev")?this.prevBtnShow?(this._querySelector("ybplayer-icon-play-prev").style.marginRight="10px",this._querySelector("ybplayer-icon-play-prev").innerHTML='<i class="ybplayerIconfont icon-play-prev-fill ybplayer-icon"></i>',this._querySelector("ybplayer-icon-play-prev").onclick=()=>{this._eventCallback.prevBtnClick&&this._eventCallback.prevBtnClick()}):(this._querySelector("ybplayer-icon-play-prev").style.marginRight="",this._querySelector("ybplayer-icon-play-prev").innerHTML="",this._querySelector("ybplayer-icon-play-prev").onclick=null):s=="nextBtn"&&this._querySelector("ybplayer-icon-play-next")&&(this.nextBtnShow?(this._querySelector("ybplayer-icon-play-next").style.marginLeft="10px",this._querySelector("ybplayer-icon-play-next").innerHTML='<i class="ybplayerIconfont icon-play-next-fill ybplayer-icon"></i>',this._querySelector("ybplayer-icon-play-next").onclick=()=>{this._eventCallback.nextBtnClick&&this._eventCallback.nextBtnClick()}):(this._querySelector("ybplayer-icon-play-next").style.marginLeft="",this._querySelector("ybplayer-icon-play-next").innerHTML="",this._querySelector("ybplayer-icon-play-next").onclick=null))},enumerable:!1});Object.defineProperty(A.prototype,"_initProgress",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._initProgress is not a constructor");this._querySelector("ybplayer-controls-progress")&&this.progressShow&&!this.isLive&&(this._querySelector("ybplayer-controls-progress").innerHTML=`
				<div class="ybplayer-slider-box">
					<div class="${this._setClassName("ybplayer-slider-track")}"></div>
					<div class="${this._setClassName("ybplayer-slider-focus")}"></div>
					<input class="${this._setClassName("ybplayer-slider")} ybplayer-controls-slider" value="0" type="range">
				</div>
			`,this._querySelector("ybplayer-slider").onchange=s=>{this._isDrag=!1,this.video&&this.seek(this._querySelector("ybplayer-slider").value/100*(this.duration||this.video.duration)),this._start_controlsTimer()},this._querySelector("ybplayer-slider").oninput=s=>{this._isDrag=!0,this._querySelector("ybplayer-slider-focus")&&(this._querySelector("ybplayer-slider-focus").style.width=this._querySelector("ybplayer-slider").value+"%"),this._clear_controlsTimer()})},enumerable:!1});Object.defineProperty(A.prototype,"_destroyProgress",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroyProgress is not a constructor");this._querySelector("ybplayer-controls-progress")&&(this._querySelector("ybplayer-slider")&&(this._querySelector("ybplayer-slider").onchange=null),this._querySelector("ybplayer-slider")&&(this._querySelector("ybplayer-slider").oninput=null),this._querySelector("ybplayer-controls-progress").innerHTML="")},enumerable:!1});Object.defineProperty(A.prototype,"_showLongPlaybackRate",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._showLongPlaybackRate is not a constructor");this._wrapperEl&&this.video&&!this.video.paused&&(this.video.playbackRate=this.longpressPlaybackRate,this._longPlaybackRateEl=document.createElement("DIV"),this._longPlaybackRateEl.setAttribute("style","position:absolute;top:20px;left:50%;transform:translateX(-50%);padding:8px 13px;border-radius:5px;background-color:rgba(0,0,0,.5);color:#fff;font-size:13px;"),this._longPlaybackRateEl.innerHTML=`
			<i class="ybplayerIconfont icon-play ybplayer-icon ybplayer-long-playbackrate-1"></i>
			<i class="ybplayerIconfont icon-play ybplayer-icon ybplayer-long-playbackrate-2"></i>
			<i class="ybplayerIconfont icon-play ybplayer-icon ybplayer-long-playbackrate-3"></i>
			<span style="margin-left:5px">\u5FEB\u901F\u64AD\u653E\u4E2D</span>`,this._wrapperEl.appendChild(this._longPlaybackRateEl))},enumerable:!1});Object.defineProperty(A.prototype,"_hideLongPlaybackRate",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._showLongPlaybackRate is not a constructor");this._longPlaybackRateEl&&(this._longPlaybackRateEl.remove(),this._longPlaybackRateEl=null),this.video&&(this.video.playbackRate=this.playbackRate)},enumerable:!1});Object.defineProperty(A.prototype,"_clear_controlsTimer",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._clear_controlsTimer is not a constructor");this.controls&&this._controlsTimer&&(window.clearTimeout(this._controlsTimer),this._controlsTimer=null)},enumerable:!1});Object.defineProperty(A.prototype,"_start_controlsTimer",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._start_controlsTimer is not a constructor");this.controls&&(this._controlsTimer=window.setTimeout(()=>{this._hideControls()},5e3))},enumerable:!1});Object.defineProperty(A.prototype,"_initSlots",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._initSlots is not a constructor");let s=this.container.childNodes;for(let e=0;e<s.length;e++)if([].concat(s[e].classList).toString().indexOf("ybplayer-slots")>-1){this._slotsEl=s[e];let t=this._slotsEl.childNodes;for(let i=0;i<t.length;i++)[].concat(t[i].classList).toString().indexOf("ybplayer-controls-slots")>-1&&(this._controlSlotsEl=t[i]);this._wrapperEl.appendChild(this._slotsEl)}},enumerable:!1});Object.defineProperty(A.prototype,"_destroySlots",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroySlots is not a constructor");this._controlSlotsEl&&(this._controlSlotsEl=null),this._slotsEl&&this.container&&(this.container.appendChild(this._slotsEl),this._slotsEl=null)},enumerable:!1});Object.defineProperty(A.prototype,"_showSetting",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._showSetting is not a constructor");if(this.container){this._settingEl=document.createElement("DIV"),this._settingEl.setAttribute("class","ybplayer-settings");let s="",e=this.settings.split(",").map(t=>t.trim());try{s=YBBarrage?`
					<div class="${this._setClassName("ybplayer-setting")}" data-value="barrage">
						<i class="ybplayerIconfont icon-barrage-${this.barrageShow?"show":"hide"} ybplayer-setting-icon"></i>
						<p class="ybplayer-setting-text">${this.barrageShow?"\u5173\u95ED\u5F39\u5E55":"\u5F00\u542F\u5F39\u5E55"}</p>
					</div>
				`:""}catch(t){}this._settingEl.innerHTML=`
				${this.settings=="all"||e.indexOf("barrage")>-1?s:""}
				${this.settings=="all"||e.indexOf("playbackRate")>-1?`
					<div class="${this._setClassName("ybplayer-setting")}" data-value="playbackRate">
						<i class="ybplayerIconfont icon-play-rate-circle ybplayer-setting-icon"></i>
						<p class="ybplayer-setting-text">\u64AD\u653E\u901F\u5EA6</p>
					</div>
				`:""}
				${this.settings=="all"||e.indexOf("mirror")>-1?`
					<div class="${this._setClassName("ybplayer-setting")}" data-value="mirror">
						<i class="ybplayerIconfont icon-mirror ybplayer-setting-icon"></i>
						<p class="ybplayer-setting-text">${this.mirror?"\u5173\u95ED\u955C\u50CF":"\u955C\u50CF\u753B\u9762"}</p>
					</div>
				`:""}
				${this.settings=="all"||e.indexOf("capture")>-1?`
					<div class="${this._setClassName("ybplayer-setting")}" data-value="capture">
						<i class="ybplayerIconfont icon-screenshot ybplayer-setting-icon"></i>
						<p class="ybplayer-setting-text">\u622A\u53D6\u753B\u9762</p>
					</div>
				`:""}
				${(this.settings=="all"||e.indexOf("pictureInPicture")>-1)&&document.pictureInPictureEnabled?`
					<div class="${this._setClassName("ybplayer-setting")}" data-value="pictureInPicture">
						<i class="ybplayerIconfont icon-picture-in-picture-${this.pictureInPicture?"open":"exit"} ybplayer-setting-icon"></i>
						<p class="ybplayer-setting-text">${this.pictureInPicture?"\u9000\u51FA\u753B\u4E2D\u753B":"\u753B\u4E2D\u753B"}</p>
					</div>
				`:""}
				${this.settings=="all"||e.indexOf("dlna")>-1?`
					<div class="${this._setClassName("ybplayer-setting")}" data-value="dlna">
						<i class="ybplayerIconfont icon-picture-in-picture-open ybplayer-setting-icon"></i>
						<p class="ybplayer-setting-text">\u5F00\u542F\u6295\u5C4F</p>
					</div>
				`:""}
			`,this.container.appendChild(this._settingEl);for(let t=0;t<this._querySelectorAll("ybplayer-setting").length;t++)this._querySelectorAll("ybplayer-setting")[t].onclick=()=>{let i=this._querySelectorAll("ybplayer-setting")[t].getAttribute("data-value");i=="barrage"?this.setConfig("barrageShow",!this.barrageShow):i=="playbackRate"?this._playbackRateEl?this._hidePlaybackRate():this._showPlaybackRate():i=="mirror"?this.setConfig("mirror",!this.mirror):i=="capture"?(this.capture(),this._hideSetting()):i=="pictureInPicture"?this.setConfig("pictureInPicture",!this.pictureInPicture):this._startSearchDlna()}}},enumerable:!1});Object.defineProperty(A.prototype,"_hideSetting",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._hideSetting is not a constructor");if(this.container&&this._settingEl){this._settingEl.setAttribute("class","ybplayer-settings ybplayer-settings-hide");for(let s=0;s<this._querySelectorAll("ybplayer-setting").length;s++)this._querySelectorAll("ybplayer-setting")[s].onclick=null;window.setTimeout(()=>{this.container.removeChild(this._settingEl),this._settingEl=null},300)}},enumerable:!1});Object.defineProperty(A.prototype,"_setSettingView",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setSettingView is not a constructor");if(this._querySelector("ybplayer-setting"))for(let e=0;e<this._querySelectorAll("ybplayer-setting").length;e++)this._querySelectorAll("ybplayer-setting")[e].getAttribute("data-value")==s&&(s=="barrage"?this._querySelectorAll("ybplayer-setting")[e].innerHTML=`
							<i class="ybplayerIconfont icon-barrage-${this.barrageShow?"show":"hide"} ybplayer-setting-icon"></i>
							<p class="ybplayer-setting-text">${this.barrageShow?"\u5173\u95ED\u5F39\u5E55":"\u5F00\u542F\u5F39\u5E55"}</p>
						`:s=="mirror"?this._querySelectorAll("ybplayer-setting")[e].innerHTML=`
							<i class="ybplayerIconfont icon-mirror ybplayer-setting-icon"></i>
							<p class="ybplayer-setting-text">${this.mirror?"\u5173\u95ED\u955C\u50CF":"\u955C\u50CF\u753B\u9762"}</p>
						`:s=="pictureInPicture"&&(this._querySelectorAll("ybplayer-setting")[e].innerHTML=`
							<i class="ybplayerIconfont icon-picture-in-picture-${this.pictureInPicture?"open":"exit"} ybplayer-setting-icon"></i>
							<p class="ybplayer-setting-text">${this.pictureInPicture?"\u9000\u51FA\u753B\u4E2D\u753B":"\u753B\u4E2D\u753B"}</p>
						`))},enumerable:!1});Object.defineProperty(A.prototype,"_startSearchDlna",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._startSearchDlna is not a constructor");try{jsBridge.dlna.search(function(s,e){console.log("succ",s),console.log("data",e)})}catch(s){}},enumerable:!1});Object.defineProperty(A.prototype,"_showPlaybackRate",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._showPlaybackRate is not a constructor");if(this.container){this._hideSetting(),this._playbackRateEl=document.createElement("DIV"),this._playbackRateEl.setAttribute("class","ybplayer-settings");let s=[{label:"0.25",value:.25},{label:"0.5",value:.5},{label:"0.75",value:.75},{label:"\u6B63\u5E38",value:1},{label:"1.25",value:1.25},{label:"1.5",value:1.5},{label:"1.75",value:1.75},{label:"2.0",value:2}];for(let t of s)this._playbackRateEl.innerHTML+=`
					<div class="${this._setClassName("ybplayer-setting")}" data-rate="${t.value}">
						<p class="ybplayer-setting-text" style="color: ${t.value==this.playbackRate?"#2ca2f9":"#333"};">${t.label}</p>
					</div>
				`;this.container.appendChild(this._playbackRateEl);let e=this;for(let t=0;t<this._querySelectorAll("ybplayer-setting").length;t++)this._querySelectorAll("ybplayer-setting")[t].onclick=function(){e.setConfig("playbackRate",this.getAttribute("data-rate"))}}},enumerable:!1});Object.defineProperty(A.prototype,"_hidePlaybackRate",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._hidePlaybackRate is not a constructor");if(this.container&&this._playbackRateEl){this._playbackRateEl.setAttribute("class","ybplayer-settings ybplayer-settings-hide");for(let s=0;s<this._querySelectorAll("ybplayer-setting").length;s++)this._querySelectorAll("ybplayer-setting")[s].onclick=null;window.setTimeout(()=>{this.container.removeChild(this._playbackRateEl),this._playbackRateEl=null,this._showSetting()},300)}},enumerable:!1});Object.defineProperty(A.prototype,"_initBarrage",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._initBarrage is not a constructor");try{this.barrage=new YBBarrage({container:this._barrageEl,barrages:this.barrages,config:Object.assign({},this.barrageConfig,{initialTime:this.video?this.video.currentTime:0})}),this.video.paused||this.barrage.play(),this._setSettingView("barrage"),this._eventCallback.barrageChange&&this._eventCallback.barrageChange({show:!0})}catch(s){}},enumerable:!1});Object.defineProperty(A.prototype,"_destroyBarrage",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroyBarrage is not a constructor");this.barrage&&(this.barrage.stop(),this.barrage=null,this._setSettingView("barrage"),this._eventCallback.barrageChange&&this._eventCallback.barrageChange({show:!1}))},enumerable:!1});Object.defineProperty(A.prototype,"_setConfig",{value:function(s,e){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setConfig is not a constructor");let t=["autoplay","loop","muted","preload","volume","playbackRate","crossOrigin"];this.video&&t.includes(s)&&(this.video[s]=e),this.video&&s=="objectFit"&&(this.video.style.objectFit=e),this.video&&s=="mirror"&&(this.video.style.transform="rotateY("+(e?180:0)+"deg)",this._setSettingView("mirror")),this.video&&s=="pictureInPicture"&&document.pictureInPictureEnabled&&(e?this.video.requestPictureInPicture():document.pictureInPictureElement&&document.exitPictureInPicture()),s=="controls"&&(e?this._initControls():this._destroyControls()),s=="prevBtnShow"&&this._setControlsView("prevBtn"),s=="nextBtnShow"&&this._setControlsView("nextBtn"),s=="timeShow"&&this._setControlsView("timeUpdate"),s=="settingShow"&&this._setControlsView("setting"),s=="progressShow"&&(e?this._initProgress():this._destroyProgress()),s=="barrageShow"&&(e?this._initBarrage():this._destroyBarrage()),s=="barrageConfig"&&this.barrage&&this.barrage.setConfig(e),s=="barrageGap"&&this.barrage&&this.barrage.refresh(),s=="title"&&(e?this._querySelector("ybplayer-headers-title")?this._querySelector("ybplayer-headers-title").innerHTML=e:this._initHeaders():this._destroyHeaders()),(s=="src"||s=="segments")&&(this._destroy(!1),this._init())},enumerable:!1});Object.defineProperty(A.prototype,"_suffix",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._suffix is not a constructor");let e=s.lastIndexOf("."),t=s.length;return s.substring(e+1,t)},enumerable:!1});Object.defineProperty(A.prototype,"_timesFormat",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._timesFormat is not a constructor");let e=Math.floor(s/60/60%60)>=10?Math.floor(s/60/60%60):"0"+Math.floor(s/60/60%60),t=Math.floor(s/60%60)>=10?Math.floor(s/60%60):"0"+Math.floor(s/60%60),i=Math.floor(s%60)>=10?Math.floor(s%60):"0"+Math.floor(s%60);return e=="00"?t+":"+i:e+":"+t+":"+i},enumerable:!1});Object.defineProperty(A.prototype,"_deepClone",{value:function(s){if(typeof s!="object"&&typeof s!="function")return s;var e=Object.prototype.toString.call(s)==="[object Array]"?[]:{};for(let t in s)s.hasOwnProperty(t)&&(e[t]=t==="loader"||Object.prototype.toString.call(s[t])==="[object XMLHttpRequest]"?"":typeof s[t]=="object"?this._deepClone(s[t]):s[t]);return e},enumerable:!1});Object.defineProperty(A.prototype,"_setClassName",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setClassName is not a constructor");return this.container.id+"_"+s+" "+s},enumerable:!1});Object.defineProperty(A.prototype,"_querySelector",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._querySelector is not a constructor");return this.container&&document.getElementsByClassName(this.container.id+"_"+s)&&document.getElementsByClassName(this.container.id+"_"+s).length>0?document.getElementsByClassName(this.container.id+"_"+s)[0]:null},enumerable:!1});Object.defineProperty(A.prototype,"_querySelectorAll",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._querySelectorAll is not a constructor");return this.container?document.getElementsByClassName(this.container.id+"_"+s)||[]:[]},enumerable:!1});Object.defineProperty(A.prototype,"_setVideoUrl",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setVideoUrl is not a constructor");if(this.src){let s="";this.formats=="auto"?s=this._suffix(this.src.split("?")[0]):s=this.formats,s=="m3u8"?this._setM3u8():s=="flv"?this._setFlv():this._setBlob()}else this.segments&&this._setFlv()},enumerable:!1});Object.defineProperty(A.prototype,"_setBlob",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setBlob is not a constructor");if(this.enableBlob){let s=new XMLHttpRequest;s.open("GET",this.src,!0),s.responseType="blob",s.onload=()=>{if(s.status==200)try{this.video.srcObject=s.response}catch(e){let t=window.URL||window.webkitURL;this.video.src=t.createObjectURL(s.response)}else this.video.src=this.src;s.abort(),s=null},s.onerror=()=>{this.video.src=this.src,s=null},s.send()}else this.video.src=this.src},enumerable:!1});Object.defineProperty(A.prototype,"_setM3u8",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setM3u8 is not a constructor");try{Hls.isSupported()?(this.hls=new Hls,this.hls.loadSource(this.src),this.hls.attachMedia(this.video),this.hls.on(Hls.Events.ERROR,(s,e)=>{this._eventCallback.error&&this._eventCallback.error(this._deepClone(e))})):this.video.src=this.src}catch(s){this._eventCallback.error&&this._eventCallback.error(this._deepClone(s))}},enumerable:!1});Object.defineProperty(A.prototype,"_destroyHls",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroyHls is not a constructor");this.hls&&(this.hls.destroy(),this.hls=null)},enumerable:!1});Object.defineProperty(A.prototype,"_setFlv",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._setFlv is not a constructor");try{flvjs.isSupported()?this.flv=flvjs.createPlayer({type:"flv",isLive:this.isLive,url:this.src,segments:this.segment&&this.segments.length>0?this.segment:null,cors:this.flvConfig.cors,withCredentials:this.flvConfig.withCredentials,hasAudio:this.flvConfig.hasAudio,hasVideo:this.flvConfig.hasVideo,duration:this.flvConfig.duration,filesize:this.flvConfig.filesize},Object.assign({},{enableWorker:!1,enableStashBuffer:!1,isLive:!0,lazyLoad:!1},this.flvConfig)):this.video.src=this.src,this.flv.on("error",this._flvError.bind(this)),this.flv.on("statistics_info",this._statisticsInfo.bind(this)),this.flv.on("loading_complete",this._loadingComplete.bind(this)),this.flv.on("recovered_early_eof",this._recoveredEarlyEof.bind(this)),this.flv.on("media_info",this._mediaInfo.bind(this)),this.flv.on("metadata_arrived",this._metadataArrived.bind(this)),this.flv.on("scriptdata_arrived",this._scriptdataArrived.bind(this)),this.flv.attachMediaElement(this.video),this.flv.load()}catch(s){this._flvError(s)}},enumerable:!1});Object.defineProperty(A.prototype,"_destroyFlv",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._destroyFlv is not a constructor");this.flv&&(this.flv.off("error",this._flvError),this.flv.off("statistics_info",this._statisticsInfo.bind(this)),this.flv.off("loading_complete",this._loadingComplete.bind(this)),this.flv.off("recovered_early_eof",this._recoveredEarlyEof.bind(this)),this.flv.off("media_info",this._mediaInfo.bind(this)),this.flv.off("metadata_arrived",this._metadataArrived.bind(this)),this.flv.off("scriptdata_arrived",this._scriptdataArrived.bind(this)),this.flv.pause(),this.flv.unload(),this.flv.detachMediaElement(),this.flv.destroy(),this.flv=null)},enumerable:!1});Object.defineProperty(A.prototype,"_flvError",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._flvError is not a constructor");this._eventCallback.error&&this._eventCallback.error(this._deepClone(s))},enumerable:!1});Object.defineProperty(A.prototype,"_statisticsInfo",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._statisticsInfo is not a constructor");this._eventCallback.statisticsInfo&&this._eventCallback.statisticsInfo(this._deepClone(s))},enumerable:!1});Object.defineProperty(A.prototype,"_loadingComplete",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._loadingComplete is not a constructor");this._eventCallback.loadingComplete&&this._eventCallback.loadingComplete(this._deepClone(s))},enumerable:!1});Object.defineProperty(A.prototype,"_recoveredEarlyEof",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._recoveredEarlyEof is not a constructor");this._eventCallback.recoveredEarlyEof&&this._eventCallback.recoveredEarlyEof(this._deepClone(s))},enumerable:!1});Object.defineProperty(A.prototype,"_mediaInfo",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._mediaInfo is not a constructor");this._eventCallback.mediaInfo&&this._eventCallback.mediaInfo(this._deepClone(s))},enumerable:!1});Object.defineProperty(A.prototype,"_metadataArrived",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._metadataArrived is not a constructor");this._eventCallback.metadataArrived&&this._eventCallback.metadataArrived(this._deepClone(s))},enumerable:!1});Object.defineProperty(A.prototype,"_scriptdataArrived",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._scriptdataArrived is not a constructor");this._eventCallback.scriptdataArrived&&this._eventCallback.scriptdataArrived(this._deepClone(s))},enumerable:!1});Object.defineProperty(A.prototype,"_fullscreenerror",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._fullscreenerror is not a constructor");this.container&&(this.container.style.position="fixed",this.container.style.left=0,this.container.style.right=0,this.container.style.bottom=0,this.container.style.top=0,this.container.style.width="100vw",this.container.style.height="100vh",this.container.style.zIndex=999,this._videoBackground=this.container.style.background,this.container.style.background="#000",this._cssfullscreenchange())},enumerable:!1});Object.defineProperty(A.prototype,"_fullscreenchanged",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._fullscreenchanged is not a constructor");document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement?(this._eventCallback.fullscreenChange&&this._eventCallback.fullscreenChange({fullscreen:!0,type:"requestfullscreen"}),this.fullscreen=!0,this._initHeaders()):(this._eventCallback.fullscreenChange&&this._eventCallback.fullscreenChange({fullscreen:!1,type:"requestfullscreen"}),this.fullscreen=!1,this._destroyHeaders()),this._setControlsView("fullscreen"),this.refreshBarrage()},enumerable:!1});Object.defineProperty(A.prototype,"_cssfullscreenchange",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer._cssfullscreenchange is not a constructor");this.container&&this.container.style.position=="fixed"?(this._eventCallback.fullscreenChange&&this._eventCallback.fullscreenChange({fullscreen:!0,type:"cssfullscreen"}),this.fullscreen=!0,this._initHeaders()):(this._eventCallback.fullscreenChange&&this._eventCallback.fullscreenChange({fullscreen:!1,type:"cssfullscreen"}),this.fullscreen=!1,this._destroyHeaders()),this._setControlsView("fullscreen"),this.refreshBarrage()},enumerable:!1});Object.defineProperty(A.prototype,"on",{value:function(s,e){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.on is not a constructor");this._eventCallback[s]=e},enumerable:!1});Object.defineProperty(A.prototype,"play",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.play is not a constructor");this.video&&this.video.play()},enumerable:!1});Object.defineProperty(A.prototype,"pause",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.pause is not a constructor");this.video&&this.video.pause()},enumerable:!1});Object.defineProperty(A.prototype,"toggle",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.toggle is not a constructor");this.video&&this.video.paused?this.video.play():this.video.pause()},enumerable:!1});Object.defineProperty(A.prototype,"seek",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.seek is not a constructor");this.video&&(this.video.currentTime=s)},enumerable:!1});Object.defineProperty(A.prototype,"reload",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.reload is not a constructor");this._destroy(!1),this._init()},enumerable:!1});Object.defineProperty(A.prototype,"stop",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.stop is not a constructor");this._destroy()},enumerable:!1});Object.defineProperty(A.prototype,"capture",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.capture is not a constructor");let s=document.createElement("canvas");s.width=this.video.videoWidth,s.height=this.video.videoHeight,s.getContext("2d").drawImage(this.video,0,0,s.width,s.height),s.toBlob(e=>{this._eventCallback.captureFinish&&this._eventCallback.captureFinish({blob:e,base64:s.toDataURL("image/jpg")})},"image/jpg")},enumerable:!1});Object.defineProperty(A.prototype,"drawBarrage",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.drawBarrage is not a constructor");this.barrage&&this.barrage.add(s)},enumerable:!1});Object.defineProperty(A.prototype,"refreshBarrage",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.refreshBarrage is not a constructor");this.barrage&&this.barrage.refresh()},enumerable:!1});Object.defineProperty(A.prototype,"setBarrages",{value:function(s){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.setBarrages is not a constructor");this.barrages=s,this.barrage&&this.barrage.setBarrages(s)},enumerable:!1});Object.defineProperty(A.prototype,"setConfig",{value:function(s,e){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.setConfig is not a constructor");this[s]=e,this._setConfig(s,e)},enumerable:!1});Object.defineProperty(A.prototype,"switchFullscreen",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.switchFullscreen is not a constructor");document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement||this.container&&this.container.style.position=="fixed"?this.exitFullscreen():this.lanuchFullscreen()},enumerable:!1});Object.defineProperty(A.prototype,"lanuchFullscreen",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.lanuchFullscreen is not a constructor");if(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement)return;let s=document.documentElement.requestFullscreen||document.documentElement.webkitRequestFullscreen||document.documentElement.mozRequestFullscreen||document.documentElement.requestFullScreen||document.documentElement.webkitRequestFullScreen||document.documentElement.mozRequestFullScreen;if(typeof s!="undefined"&&s&&this.container)s.call(this.container);else if(typeof window.ActiveXObject!="undefined"){var e=new ActiveXObject("WScript.Shell");e!=null&&e.SendKeys("{F11}")}else this._fullscreenerror()},enumerable:!1});Object.defineProperty(A.prototype,"exitFullscreen",{value:function(){if(!(this instanceof A))throw new TypeError("TypeError: YBPlayer.exitFullscreen is not a constructor");if(document.fullscreenElement||document.mozFullScreenElement||document.msFullscreenElement||document.webkitFullscreenElement){let e=document.exitFullscreen||document.mozCancelFullScreen||document.msExitFullscreen||document.webkitExitFullscreen;typeof e!="undefined"&&e&&e.call(document)}else if(typeof window.ActiveXObject!="undefined"){var s=new ActiveXObject("WScript.Shell");s!=null&&s.SendKeys("{F11}")}else this.container&&this.container.style.position=="fixed"&&(this.container.style.position="relative",this.container.style.width="100%",this.container.style.height="100%",this.container.style.top="inherit",this.container.style.left="inherit",this.container.style.right="inherit",this.container.style.bottom="inherit",this.container.style.zIndex="inherit",this.container.style.background=this._videoBackground,this._videoBackground="inherit",this._cssfullscreenchange())},enumerable:!1});function U({container:s,barrages:e=[],config:t={}}){if(!(this instanceof U))throw new TypeError("TypeError: Class constructor YBBarrage cannot be invoked without 'new'");this.container=s,this.canvas=null,this.paused=!0,this.barrages=e,this.config={},this._ctx=null,this._barrages=[],this._w=0,this._h=0,this._drawTimer=null,this._drawAnima=null,this.currentTime=0,this._count=0,this._startTime=0,this._refreshTimer=null,this.setConfig(t),this._init()}Object.defineProperty(U.prototype,"_init",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._init is not a constructor");this.canvas=document.createElement("CANVAS"),this.canvas.setAttribute("width",this.container.offsetWidth),this.canvas.setAttribute("height",this.container.offsetHeight),this.container.appendChild(this.canvas),this._ctx=this.canvas.getContext("2d");let s=this.canvas.getBoundingClientRect();this._w=s.right-s.left,this._h=s.bottom-s.top,this.currentTime=this.config.initialTime,window.addEventListener("resize",this.refresh.bind(this))},enumerable:!1});Object.defineProperty(U.prototype,"_destroy",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._destroy is not a constructor");this.paused=!0,this._clearTimer(),this._cancelAnima(),this._clear(),this.barrages=[],this._barrages=[],this.container=null,this._ctx=null,this.canvas&&(this.canvas.remove(),this.canvas=null),window.removeEventListener("resize",this.refresh.bind(this))},enumerable:!1});Object.defineProperty(U.prototype,"_render",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._render is not a constructor");if(this._cancelAnima(),!this.paused){if(this._barrages.length){this._clear();for(let s=0;s<this._barrages.length;s++){let e=this._barrages[s];if(e.left+e.width<=0){this._barrages.splice(s,1),s--;continue}e.offset=this._detectionBump(e);let t=e.offset*this.config.playbackRate;e.left-=t,this._drawText(e)}}this._drawAnima=window.requestAnimationFrame(this._render.bind(this))}},enumerable:!1});Object.defineProperty(U.prototype,"_cancelAnima",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._cancelAnima is not a constructor");this._drawAnima&&(window.cancelAnimationFrame(this._drawAnima),this._drawAnima=null)},enumerable:!1});Object.defineProperty(U.prototype,"_clear",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._clear is not a constructor");this._ctx&&this._ctx.clearRect(0,0,this._w,this._h)},enumerable:!1});Object.defineProperty(U.prototype,"_drawText",{value:function(s){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._drawText is not a constructor");if(this._ctx){this._ctx.beginPath();let e=s.fontSize||this.config.fontSize;this._ctx.font=`${e}px ${this.config.fontFamily}`,this._ctx.strokeStyle=this._hex2rgba(this._getStrokeColor(s.color||this.config.defaultColor),this.config.opacity),this._ctx.strokeText(s.text,s.left,s.top),this._ctx.fillStyle=this._hex2rgba(s.color||this.config.defaultColor,this.config.opacity),this._ctx.fillText(s.text,s.left,s.top),this._ctx.closePath()}},enumerable:!1});Object.defineProperty(U.prototype,"_startTimer",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._startTimer is not a constructor");this._startTime=new Date().getTime(),this._count=0,this._drawTimer=window.setTimeout(this._fixed.bind(this),1e3)},enumerable:!1});Object.defineProperty(U.prototype,"_clearTimer",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._clearTimer is not a constructor");this._drawTimer&&(window.clearTimeout(this._drawTimer),this._drawTimer=null)},enumerable:!1});Object.defineProperty(U.prototype,"_fixed",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._fixed is not a constructor");if(this._clearTimer(),this.paused)return;if(this.config.duration>-1&&this.currentTime>=this.config.duration){this.seek(0);return}this._count++,this.currentTime+=1*this.config.playbackRate;let s=this.barrages.filter(i=>parseInt(i.time)==parseInt(this.currentTime));for(let i=0;i<s.length;i++){let r=this._getBarrage(s[i]);r&&this._barrages.push(r)}let t=1e3-(new Date().getTime()-(this._startTime+this._count*1e3));t<0&&(t=0),this._drawTimer=window.setTimeout(this._fixed.bind(this),t)},enumerable:!1});Object.defineProperty(U.prototype,"_getBarrage",{value:function(s,e=!1){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._getBarrage is not a constructor");let t=s.fontSize||this.config.fontSize,i=this._getOffset();this._ctx.font=`${s.fontSize||this.config.fontSize}px ${this.config.fontFamily}`;let r=Math.ceil(this._ctx.measureText(s.text).width),n=this._getTop(t,r,i,e);return n>-1?{text:s.text,time:s.time,fontSize:t,color:s.color||this.config.defaultColor,top:n,left:this._w,offset:i,width:r}:!1},enumerable:!1});Object.defineProperty(U.prototype,"_getStrokeColor",{value:function(s){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._getStrokeColor is not a constructor");let e=s.length==7?s:"#"+s.slice(1,4)+s.slice(1,4),t=parseInt(e.slice(1,3),16),i=parseInt(e.slice(3,5),16),r=parseInt(e.slice(5,7),16);return t*.299+i*.587+r*.144>=120?"#000000":"#ffffff"},enumerable:!1});Object.defineProperty(U.prototype,"_hex2rgba",{value:function(s,e){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._hex2rgba is not a constructor");s=s.length==7?s:"#"+s.slice(1,4)+s.slice(1,4);let t="rgba(",i=parseInt(s.slice(1,3),16).toString(),r=parseInt(s.slice(3,5),16).toString(),n=parseInt(s.slice(5,7),16).toString();return t+=i+","+r+","+n+","+e+")",t},enumerable:!1});Object.defineProperty(U.prototype,"_getTop",{value:function(s,e,t,i=!1){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._getTop is not a constructor");let r=-1,n=-1;for(let o=0;o<Math.floor(this._h/(s+this.config.lineHeight));o++){let a=(o+1)*s+o*this.config.lineHeight,l=this._barrages.filter(c=>c.top<a+s&&c.fontSize+c.top>a);if(l.length>0){let c=l.map(u=>u.left+u.width);if((!this.config.overlap||i)&&(c.length<n||n<0)&&(n=c.length,r=a),Math.max(...c)<this._w-(i?e:10)){r=a;break}}else{r=a;break}}return r},enumerable:!1});Object.defineProperty(U.prototype,"_getOffset",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._getOffset is not a constructor");return parseFloat((this.config.speed/70+Math.random()).toFixed(1))},enumerable:!1});Object.defineProperty(U.prototype,"_detectionBump",{value:function(s){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage._detectionBump is not a constructor");let e=s.top,t=s.offset,i=this._barrages.filter(o=>o.top<e+s.fontSize&&o.fontSize+o.top>e&&s.left!=o.left),r=i.filter(o=>s.left+s.width>=o.left+o.width+10&&o.left+o.width>0),n=i.filter(o=>s.left+s.width+10<o.left&&o.left+o.width>0);if(r.length>0){let o=r.map(c=>c.left+c.width),a=Math.max(...o),l=o.indexOf(a);s.left>a+20?t=r[l].offset+.01:t=r[l].offset-.01}if(n.length>0)if(s.left+s.width>this._w&&r.length==0)t=s.offset+.01;else{let o=n.map(c=>c.left),a=Math.min(...o),l=o.indexOf(a);a-(s.left+s.width)<=20&&(t=s.offset+.01)}return t<.5&&(t=.5),t>3&&(t=3),t},enumerable:!1});Object.defineProperty(U.prototype,"setBarrages",{value:function(s){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.setBarrages is not a constructor");this.barrages=s},enumerable:!1});Object.defineProperty(U.prototype,"add",{value:function(s){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.add is not a constructor");(Object.prototype.toString.call(s)=="[object Array]"?s:[s]).forEach(t=>{if(t.time=t.time||this.currentTime,this.barrages.push(t),parseInt(t.time)==parseInt(this.currentTime)){let i=this._getBarrage(t,!0);i.left=this._w-i.width,this._barrages.push(i)}})},enumerable:!1});Object.defineProperty(U.prototype,"setConfig",{value:function(s){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.setConfig is not a constructor");this.config=Object.assign({},{duration:-1,speed:150,fontSize:24,fontFamily:"Microsoft Yahei",opacity:1,defaultColor:"#fff",lineHeight:5,overlap:!0,playbackRate:1,initialTime:0},this.config,s)},enumerable:!1});Object.defineProperty(U.prototype,"refresh",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.refresh is not a constructor");this._refreshTimer&&(window.clearTimeout(this._refreshTimer),this._refreshTimer=null),this._refreshTimer=window.setTimeout(()=>{this.canvas&&this.container&&(this.canvas.width=this.container.offsetWidth,this.canvas.height=this.container.offsetHeight,this._w=this.container.offsetWidth,this._h=this.container.offsetHeight,this.paused||this.play(),window.clearTimeout(this._refreshTimer),this._refreshTimer=null)},200)},enumerable:!1});Object.defineProperty(U.prototype,"play",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.play is not a constructor");this.paused&&(this.paused=!1,this._startTimer(),this._render())},enumerable:!1});Object.defineProperty(U.prototype,"pause",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.pause is not a constructor");this.paused||(this.paused=!0,this._clearTimer(),this._cancelAnima())},enumerable:!1});Object.defineProperty(U.prototype,"stop",{value:function(){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.stop is not a constructor");this._destroy()},enumerable:!1});Object.defineProperty(U.prototype,"seek",{value:function(s){if(!(this instanceof U))throw new TypeError("TypeError: YBBarrage.seek is not a constructor");this._barrages=[],this.currentTime=parseInt(s),this._clear(),this.paused||this.play()},enumerable:!1});function Zn(s){return s&&s.__esModule&&Object.prototype.hasOwnProperty.call(s,"default")?s.default:s}var Vr={exports:{}};(function(s,e){(function(t){var i=/^(?=((?:[a-zA-Z0-9+\-.]+:)?))\1(?=((?:\/\/[^\/?#]*)?))\2(?=((?:(?:[^?#\/]*\/)*[^;?#\/]*)?))\3((?:;[^?#]*)?)(\?[^#]*)?(#[^]*)?$/,r=/^(?=([^\/?#]*))\1([^]*)$/,n=/(?:\/|^)\.(?=\/)/g,o=/(?:\/|^)\.\.\/(?!\.\.\/)[^\/]*(?=\/)/g,a={buildAbsoluteURL:function(l,c,h){if(h=h||{},l=l.trim(),c=c.trim(),!c){if(!h.alwaysNormalize)return l;var u=a.parseURL(l);if(!u)throw new Error("Error trying to parse base URL.");return u.path=a.normalizePath(u.path),a.buildURLFromParts(u)}var d=a.parseURL(c);if(!d)throw new Error("Error trying to parse relative URL.");if(d.scheme)return h.alwaysNormalize?(d.path=a.normalizePath(d.path),a.buildURLFromParts(d)):c;var f=a.parseURL(l);if(!f)throw new Error("Error trying to parse base URL.");if(!f.netLoc&&f.path&&f.path[0]!=="/"){var g=r.exec(f.path);f.netLoc=g[1],f.path=g[2]}f.netLoc&&!f.path&&(f.path="/");var m={scheme:f.scheme,netLoc:d.netLoc,path:null,params:d.params,query:d.query,fragment:d.fragment};if(!d.netLoc&&(m.netLoc=f.netLoc,d.path[0]!=="/"))if(!d.path)m.path=f.path,d.params||(m.params=f.params,d.query||(m.query=f.query));else{var y=f.path,T=y.substring(0,y.lastIndexOf("/")+1)+d.path;m.path=a.normalizePath(T)}return m.path===null&&(m.path=h.alwaysNormalize?a.normalizePath(d.path):d.path),a.buildURLFromParts(m)},parseURL:function(l){var c=i.exec(l);return c?{scheme:c[1]||"",netLoc:c[2]||"",path:c[3]||"",params:c[4]||"",query:c[5]||"",fragment:c[6]||""}:null},normalizePath:function(l){for(l=l.split("").reverse().join("").replace(n,"");l.length!==(l=l.replace(o,"")).length;);return l.split("").reverse().join("")},buildURLFromParts:function(l){return l.scheme+l.netLoc+l.path+l.params+l.query+l.fragment}};s.exports=a})()})(Vr);var Ps=Vr.exports;function qs(s,e){var t=Object.keys(s);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(s);e&&(i=i.filter(function(r){return Object.getOwnPropertyDescriptor(s,r).enumerable})),t.push.apply(t,i)}return t}function he(s){for(var e=1;e<arguments.length;e++){var t=arguments[e]!=null?arguments[e]:{};e%2?qs(Object(t),!0).forEach(function(i){io(s,i,t[i])}):Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(t)):qs(Object(t)).forEach(function(i){Object.defineProperty(s,i,Object.getOwnPropertyDescriptor(t,i))})}return s}function eo(s,e){if(typeof s!="object"||!s)return s;var t=s[Symbol.toPrimitive];if(t!==void 0){var i=t.call(s,e||"default");if(typeof i!="object")return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return(e==="string"?String:Number)(s)}function to(s){var e=eo(s,"string");return typeof e=="symbol"?e:String(e)}function io(s,e,t){return e=to(e),e in s?Object.defineProperty(s,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):s[e]=t,s}function se(){return se=Object.assign?Object.assign.bind():function(s){for(var e=1;e<arguments.length;e++){var t=arguments[e];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(s[i]=t[i])}return s},se.apply(this,arguments)}var M=Number.isFinite||function(s){return typeof s=="number"&&isFinite(s)},so=Number.isSafeInteger||function(s){return typeof s=="number"&&Math.abs(s)<=ro},ro=Number.MAX_SAFE_INTEGER||9007199254740991,p=function(s){return s.MEDIA_ATTACHING="hlsMediaAttaching",s.MEDIA_ATTACHED="hlsMediaAttached",s.MEDIA_DETACHING="hlsMediaDetaching",s.MEDIA_DETACHED="hlsMediaDetached",s.BUFFER_RESET="hlsBufferReset",s.BUFFER_CODECS="hlsBufferCodecs",s.BUFFER_CREATED="hlsBufferCreated",s.BUFFER_APPENDING="hlsBufferAppending",s.BUFFER_APPENDED="hlsBufferAppended",s.BUFFER_EOS="hlsBufferEos",s.BUFFER_FLUSHING="hlsBufferFlushing",s.BUFFER_FLUSHED="hlsBufferFlushed",s.MANIFEST_LOADING="hlsManifestLoading",s.MANIFEST_LOADED="hlsManifestLoaded",s.MANIFEST_PARSED="hlsManifestParsed",s.LEVEL_SWITCHING="hlsLevelSwitching",s.LEVEL_SWITCHED="hlsLevelSwitched",s.LEVEL_LOADING="hlsLevelLoading",s.LEVEL_LOADED="hlsLevelLoaded",s.LEVEL_UPDATED="hlsLevelUpdated",s.LEVEL_PTS_UPDATED="hlsLevelPtsUpdated",s.LEVELS_UPDATED="hlsLevelsUpdated",s.AUDIO_TRACKS_UPDATED="hlsAudioTracksUpdated",s.AUDIO_TRACK_SWITCHING="hlsAudioTrackSwitching",s.AUDIO_TRACK_SWITCHED="hlsAudioTrackSwitched",s.AUDIO_TRACK_LOADING="hlsAudioTrackLoading",s.AUDIO_TRACK_LOADED="hlsAudioTrackLoaded",s.SUBTITLE_TRACKS_UPDATED="hlsSubtitleTracksUpdated",s.SUBTITLE_TRACKS_CLEARED="hlsSubtitleTracksCleared",s.SUBTITLE_TRACK_SWITCH="hlsSubtitleTrackSwitch",s.SUBTITLE_TRACK_LOADING="hlsSubtitleTrackLoading",s.SUBTITLE_TRACK_LOADED="hlsSubtitleTrackLoaded",s.SUBTITLE_FRAG_PROCESSED="hlsSubtitleFragProcessed",s.CUES_PARSED="hlsCuesParsed",s.NON_NATIVE_TEXT_TRACKS_FOUND="hlsNonNativeTextTracksFound",s.INIT_PTS_FOUND="hlsInitPtsFound",s.FRAG_LOADING="hlsFragLoading",s.FRAG_LOAD_EMERGENCY_ABORTED="hlsFragLoadEmergencyAborted",s.FRAG_LOADED="hlsFragLoaded",s.FRAG_DECRYPTED="hlsFragDecrypted",s.FRAG_PARSING_INIT_SEGMENT="hlsFragParsingInitSegment",s.FRAG_PARSING_USERDATA="hlsFragParsingUserdata",s.FRAG_PARSING_METADATA="hlsFragParsingMetadata",s.FRAG_PARSED="hlsFragParsed",s.FRAG_BUFFERED="hlsFragBuffered",s.FRAG_CHANGED="hlsFragChanged",s.FPS_DROP="hlsFpsDrop",s.FPS_DROP_LEVEL_CAPPING="hlsFpsDropLevelCapping",s.MAX_AUTO_LEVEL_UPDATED="hlsMaxAutoLevelUpdated",s.ERROR="hlsError",s.DESTROYING="hlsDestroying",s.KEY_LOADING="hlsKeyLoading",s.KEY_LOADED="hlsKeyLoaded",s.LIVE_BACK_BUFFER_REACHED="hlsLiveBackBufferReached",s.BACK_BUFFER_REACHED="hlsBackBufferReached",s.STEERING_MANIFEST_LOADED="hlsSteeringManifestLoaded",s}({}),G=function(s){return s.NETWORK_ERROR="networkError",s.MEDIA_ERROR="mediaError",s.KEY_SYSTEM_ERROR="keySystemError",s.MUX_ERROR="muxError",s.OTHER_ERROR="otherError",s}({}),b=function(s){return s.KEY_SYSTEM_NO_KEYS="keySystemNoKeys",s.KEY_SYSTEM_NO_ACCESS="keySystemNoAccess",s.KEY_SYSTEM_NO_SESSION="keySystemNoSession",s.KEY_SYSTEM_NO_CONFIGURED_LICENSE="keySystemNoConfiguredLicense",s.KEY_SYSTEM_LICENSE_REQUEST_FAILED="keySystemLicenseRequestFailed",s.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED="keySystemServerCertificateRequestFailed",s.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED="keySystemServerCertificateUpdateFailed",s.KEY_SYSTEM_SESSION_UPDATE_FAILED="keySystemSessionUpdateFailed",s.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED="keySystemStatusOutputRestricted",s.KEY_SYSTEM_STATUS_INTERNAL_ERROR="keySystemStatusInternalError",s.MANIFEST_LOAD_ERROR="manifestLoadError",s.MANIFEST_LOAD_TIMEOUT="manifestLoadTimeOut",s.MANIFEST_PARSING_ERROR="manifestParsingError",s.MANIFEST_INCOMPATIBLE_CODECS_ERROR="manifestIncompatibleCodecsError",s.LEVEL_EMPTY_ERROR="levelEmptyError",s.LEVEL_LOAD_ERROR="levelLoadError",s.LEVEL_LOAD_TIMEOUT="levelLoadTimeOut",s.LEVEL_PARSING_ERROR="levelParsingError",s.LEVEL_SWITCH_ERROR="levelSwitchError",s.AUDIO_TRACK_LOAD_ERROR="audioTrackLoadError",s.AUDIO_TRACK_LOAD_TIMEOUT="audioTrackLoadTimeOut",s.SUBTITLE_LOAD_ERROR="subtitleTrackLoadError",s.SUBTITLE_TRACK_LOAD_TIMEOUT="subtitleTrackLoadTimeOut",s.FRAG_LOAD_ERROR="fragLoadError",s.FRAG_LOAD_TIMEOUT="fragLoadTimeOut",s.FRAG_DECRYPT_ERROR="fragDecryptError",s.FRAG_PARSING_ERROR="fragParsingError",s.FRAG_GAP="fragGap",s.REMUX_ALLOC_ERROR="remuxAllocError",s.KEY_LOAD_ERROR="keyLoadError",s.KEY_LOAD_TIMEOUT="keyLoadTimeOut",s.BUFFER_ADD_CODEC_ERROR="bufferAddCodecError",s.BUFFER_INCOMPATIBLE_CODECS_ERROR="bufferIncompatibleCodecsError",s.BUFFER_APPEND_ERROR="bufferAppendError",s.BUFFER_APPENDING_ERROR="bufferAppendingError",s.BUFFER_STALLED_ERROR="bufferStalledError",s.BUFFER_FULL_ERROR="bufferFullError",s.BUFFER_SEEK_OVER_HOLE="bufferSeekOverHole",s.BUFFER_NUDGE_ON_STALL="bufferNudgeOnStall",s.INTERNAL_EXCEPTION="internalException",s.INTERNAL_ABORTED="aborted",s.UNKNOWN="unknown",s}({}),Fe=function(){},Ai={trace:Fe,debug:Fe,log:Fe,warn:Fe,info:Fe,error:Fe},it=Ai;function no(s){let e=self.console[s];return e?e.bind(self.console,`[${s}] >`):Fe}function oo(s,...e){e.forEach(function(t){it[t]=s[t]?s[t].bind(s):no(t)})}function ao(s,e){if(typeof console=="object"&&s===!0||typeof s=="object"){oo(s,"debug","log","info","warn","error");try{it.log(`Debug logs enabled for "${e}" in hls.js version 1.5.8`)}catch(t){it=Ai}}else it=Ai}var S=it,lo=/^(\d+)x(\d+)$/,js=/(.+?)=(".*?"|.*?)(?:,|$)/g,te=class s{constructor(e){typeof e=="string"&&(e=s.parseAttrList(e)),se(this,e)}get clientAttrs(){return Object.keys(this).filter(e=>e.substring(0,2)==="X-")}decimalInteger(e){let t=parseInt(this[e],10);return t>Number.MAX_SAFE_INTEGER?1/0:t}hexadecimalInteger(e){if(this[e]){let t=(this[e]||"0x").slice(2);t=(t.length&1?"0":"")+t;let i=new Uint8Array(t.length/2);for(let r=0;r<t.length/2;r++)i[r]=parseInt(t.slice(r*2,r*2+2),16);return i}else return null}hexadecimalIntegerAsNumber(e){let t=parseInt(this[e],16);return t>Number.MAX_SAFE_INTEGER?1/0:t}decimalFloatingPoint(e){return parseFloat(this[e])}optionalFloat(e,t){let i=this[e];return i?parseFloat(i):t}enumeratedString(e){return this[e]}bool(e){return this[e]==="YES"}decimalResolution(e){let t=lo.exec(this[e]);if(t!==null)return{width:parseInt(t[1],10),height:parseInt(t[2],10)}}static parseAttrList(e){let t,i={},r='"';for(js.lastIndex=0;(t=js.exec(e))!==null;){let n=t[2];n.indexOf(r)===0&&n.lastIndexOf(r)===n.length-1&&(n=n.slice(1,-1));let o=t[1].trim();i[o]=n}return i}};function co(s){return s!=="ID"&&s!=="CLASS"&&s!=="START-DATE"&&s!=="DURATION"&&s!=="END-DATE"&&s!=="END-ON-NEXT"}function ho(s){return s==="SCTE35-OUT"||s==="SCTE35-IN"}var Ct=class{constructor(e,t){if(this.attr=void 0,this._startDate=void 0,this._endDate=void 0,this._badValueForSameId=void 0,t){let i=t.attr;for(let r in i)if(Object.prototype.hasOwnProperty.call(e,r)&&e[r]!==i[r]){S.warn(`DATERANGE tag attribute: "${r}" does not match for tags with ID: "${e.ID}"`),this._badValueForSameId=r;break}e=se(new te({}),i,e)}if(this.attr=e,this._startDate=new Date(e["START-DATE"]),"END-DATE"in this.attr){let i=new Date(this.attr["END-DATE"]);M(i.getTime())&&(this._endDate=i)}}get id(){return this.attr.ID}get class(){return this.attr.CLASS}get startDate(){return this._startDate}get endDate(){if(this._endDate)return this._endDate;let e=this.duration;return e!==null?new Date(this._startDate.getTime()+e*1e3):null}get duration(){if("DURATION"in this.attr){let e=this.attr.decimalFloatingPoint("DURATION");if(M(e))return e}else if(this._endDate)return(this._endDate.getTime()-this._startDate.getTime())/1e3;return null}get plannedDuration(){return"PLANNED-DURATION"in this.attr?this.attr.decimalFloatingPoint("PLANNED-DURATION"):null}get endOnNext(){return this.attr.bool("END-ON-NEXT")}get isValid(){return!!this.id&&!this._badValueForSameId&&M(this.startDate.getTime())&&(this.duration===null||this.duration>=0)&&(!this.endOnNext||!!this.class)}},Xe=class{constructor(){this.aborted=!1,this.loaded=0,this.retry=0,this.total=0,this.chunkCount=0,this.bwEstimate=0,this.loading={start:0,first:0,end:0},this.parsing={start:0,end:0},this.buffering={start:0,first:0,end:0}}},J={AUDIO:"audio",VIDEO:"video",AUDIOVIDEO:"audiovideo"},Dt=class{constructor(e){this._byteRange=null,this._url=null,this.baseurl=void 0,this.relurl=void 0,this.elementaryStreams={[J.AUDIO]:null,[J.VIDEO]:null,[J.AUDIOVIDEO]:null},this.baseurl=e}setByteRange(e,t){let i=e.split("@",2),r;i.length===1?r=(t==null?void 0:t.byteRangeEndOffset)||0:r=parseInt(i[1]),this._byteRange=[r,parseInt(i[0])+r]}get byteRange(){return this._byteRange?this._byteRange:[]}get byteRangeStartOffset(){return this.byteRange[0]}get byteRangeEndOffset(){return this.byteRange[1]}get url(){return!this._url&&this.baseurl&&this.relurl&&(this._url=Ps.buildAbsoluteURL(this.baseurl,this.relurl,{alwaysNormalize:!0})),this._url||""}set url(e){this._url=e}},st=class extends Dt{constructor(e,t){super(t),this._decryptdata=null,this.rawProgramDateTime=null,this.programDateTime=null,this.tagList=[],this.duration=0,this.sn=0,this.levelkeys=void 0,this.type=void 0,this.loader=null,this.keyLoader=null,this.level=-1,this.cc=0,this.startPTS=void 0,this.endPTS=void 0,this.startDTS=void 0,this.endDTS=void 0,this.start=0,this.deltaPTS=void 0,this.maxStartPTS=void 0,this.minEndPTS=void 0,this.stats=new Xe,this.data=void 0,this.bitrateTest=!1,this.title=null,this.initSegment=null,this.endList=void 0,this.gap=void 0,this.urlId=0,this.type=e}get decryptdata(){let{levelkeys:e}=this;if(!e&&!this._decryptdata)return null;if(!this._decryptdata&&this.levelkeys&&!this.levelkeys.NONE){let t=this.levelkeys.identity;if(t)this._decryptdata=t.getDecryptData(this.sn);else{let i=Object.keys(this.levelkeys);if(i.length===1)return this._decryptdata=this.levelkeys[i[0]].getDecryptData(this.sn)}}return this._decryptdata}get end(){return this.start+this.duration}get endProgramDateTime(){if(this.programDateTime===null||!M(this.programDateTime))return null;let e=M(this.duration)?this.duration:0;return this.programDateTime+e*1e3}get encrypted(){var e;if((e=this._decryptdata)!=null&&e.encrypted)return!0;if(this.levelkeys){let t=Object.keys(this.levelkeys),i=t.length;if(i>1||i===1&&this.levelkeys[t[0]].encrypted)return!0}return!1}setKeyFormat(e){if(this.levelkeys){let t=this.levelkeys[e];t&&!this._decryptdata&&(this._decryptdata=t.getDecryptData(this.sn))}}abortRequests(){var e,t;(e=this.loader)==null||e.abort(),(t=this.keyLoader)==null||t.abort()}setElementaryStreamInfo(e,t,i,r,n,o=!1){let{elementaryStreams:a}=this,l=a[e];if(!l){a[e]={startPTS:t,endPTS:i,startDTS:r,endDTS:n,partial:o};return}l.startPTS=Math.min(l.startPTS,t),l.endPTS=Math.max(l.endPTS,i),l.startDTS=Math.min(l.startDTS,r),l.endDTS=Math.max(l.endDTS,n)}clearElementaryStreamInfo(){let{elementaryStreams:e}=this;e[J.AUDIO]=null,e[J.VIDEO]=null,e[J.AUDIOVIDEO]=null}},Li=class extends Dt{constructor(e,t,i,r,n){super(i),this.fragOffset=0,this.duration=0,this.gap=!1,this.independent=!1,this.relurl=void 0,this.fragment=void 0,this.index=void 0,this.stats=new Xe,this.duration=e.decimalFloatingPoint("DURATION"),this.gap=e.bool("GAP"),this.independent=e.bool("INDEPENDENT"),this.relurl=e.enumeratedString("URI"),this.fragment=t,this.index=r;let o=e.enumeratedString("BYTERANGE");o&&this.setByteRange(o,n),n&&(this.fragOffset=n.fragOffset+n.duration)}get start(){return this.fragment.start+this.fragOffset}get end(){return this.start+this.duration}get loaded(){let{elementaryStreams:e}=this;return!!(e.audio||e.video||e.audiovideo)}},uo=10,_i=class{constructor(e){this.PTSKnown=!1,this.alignedSliding=!1,this.averagetargetduration=void 0,this.endCC=0,this.endSN=0,this.fragments=void 0,this.fragmentHint=void 0,this.partList=null,this.dateRanges=void 0,this.live=!0,this.ageHeader=0,this.advancedDateTime=void 0,this.updated=!0,this.advanced=!0,this.availabilityDelay=void 0,this.misses=0,this.startCC=0,this.startSN=0,this.startTimeOffset=null,this.targetduration=0,this.totalduration=0,this.type=null,this.url=void 0,this.m3u8="",this.version=null,this.canBlockReload=!1,this.canSkipUntil=0,this.canSkipDateRanges=!1,this.skippedSegments=0,this.recentlyRemovedDateranges=void 0,this.partHoldBack=0,this.holdBack=0,this.partTarget=0,this.preloadHint=void 0,this.renditionReports=void 0,this.tuneInGoal=0,this.deltaUpdateFailed=void 0,this.driftStartTime=0,this.driftEndTime=0,this.driftStart=0,this.driftEnd=0,this.encryptedFragments=void 0,this.playlistParsingError=null,this.variableList=null,this.hasVariableRefs=!1,this.fragments=[],this.encryptedFragments=[],this.dateRanges={},this.url=e}reloaded(e){if(!e){this.advanced=!0,this.updated=!0;return}let t=this.lastPartSn-e.lastPartSn,i=this.lastPartIndex-e.lastPartIndex;this.updated=this.endSN!==e.endSN||!!i||!!t||!this.live,this.advanced=this.endSN>e.endSN||t>0||t===0&&i>0,this.updated||this.advanced?this.misses=Math.floor(e.misses*.6):this.misses=e.misses+1,this.availabilityDelay=e.availabilityDelay}get hasProgramDateTime(){return this.fragments.length?M(this.fragments[this.fragments.length-1].programDateTime):!1}get levelTargetDuration(){return this.averagetargetduration||this.targetduration||uo}get drift(){let e=this.driftEndTime-this.driftStartTime;return e>0?(this.driftEnd-this.driftStart)*1e3/e:1}get edge(){return this.partEnd||this.fragmentEnd}get partEnd(){var e;return(e=this.partList)!=null&&e.length?this.partList[this.partList.length-1].end:this.fragmentEnd}get fragmentEnd(){var e;return(e=this.fragments)!=null&&e.length?this.fragments[this.fragments.length-1].end:0}get age(){return this.advancedDateTime?Math.max(Date.now()-this.advancedDateTime,0)/1e3:0}get lastPartIndex(){var e;return(e=this.partList)!=null&&e.length?this.partList[this.partList.length-1].index:-1}get lastPartSn(){var e;return(e=this.partList)!=null&&e.length?this.partList[this.partList.length-1].fragment.sn:this.endSN}};function Os(s){return Uint8Array.from(atob(s),e=>e.charCodeAt(0))}function fo(s){let e=Ri(s).subarray(0,16),t=new Uint8Array(16);return t.set(e,16-e.length),t}function go(s){let e=function(i,r,n){let o=i[r];i[r]=i[n],i[n]=o};e(s,0,3),e(s,1,2),e(s,4,5),e(s,6,7)}function po(s){let e=s.split(":"),t=null;if(e[0]==="data"&&e.length===2){let i=e[1].split(";"),r=i[i.length-1].split(",");if(r.length===2){let n=r[0]==="base64",o=r[1];n?(i.splice(-1,1),t=Os(o)):t=fo(o)}}return t}function Ri(s){return Uint8Array.from(unescape(encodeURIComponent(s)),e=>e.charCodeAt(0))}var Qe=typeof self!="undefined"?self:void 0,ee={CLEARKEY:"org.w3.clearkey",FAIRPLAY:"com.apple.fps",PLAYREADY:"com.microsoft.playready",WIDEVINE:"com.widevine.alpha"},pe={CLEARKEY:"org.w3.clearkey",FAIRPLAY:"com.apple.streamingkeydelivery",PLAYREADY:"com.microsoft.playready",WIDEVINE:"urn:uuid:edef8ba9-79d6-4ace-a3c8-27dcd51d21ed"};function zs(s){switch(s){case pe.FAIRPLAY:return ee.FAIRPLAY;case pe.PLAYREADY:return ee.PLAYREADY;case pe.WIDEVINE:return ee.WIDEVINE;case pe.CLEARKEY:return ee.CLEARKEY}}var Yr={WIDEVINE:"edef8ba979d64acea3c827dcd51d21ed"};function mo(s){if(s===Yr.WIDEVINE)return ee.WIDEVINE}function Xs(s){switch(s){case ee.FAIRPLAY:return pe.FAIRPLAY;case ee.PLAYREADY:return pe.PLAYREADY;case ee.WIDEVINE:return pe.WIDEVINE;case ee.CLEARKEY:return pe.CLEARKEY}}function ni(s){let{drmSystems:e,widevineLicenseUrl:t}=s,i=e?[ee.FAIRPLAY,ee.WIDEVINE,ee.PLAYREADY,ee.CLEARKEY].filter(r=>!!e[r]):[];return!i[ee.WIDEVINE]&&t&&i.push(ee.WIDEVINE),i}var Wr=function(s){return Qe!=null&&(s=Qe.navigator)!=null&&s.requestMediaKeySystemAccess?self.navigator.requestMediaKeySystemAccess.bind(self.navigator):null}();function yo(s,e,t,i){let r;switch(s){case ee.FAIRPLAY:r=["cenc","sinf"];break;case ee.WIDEVINE:case ee.PLAYREADY:r=["cenc"];break;case ee.CLEARKEY:r=["cenc","keyids"];break;default:throw new Error(`Unknown key-system: ${s}`)}return To(r,e,t,i)}function To(s,e,t,i){return[{initDataTypes:s,persistentState:i.persistentState||"optional",distinctiveIdentifier:i.distinctiveIdentifier||"optional",sessionTypes:i.sessionTypes||[i.sessionType||"temporary"],audioCapabilities:e.map(n=>({contentType:`audio/mp4; codecs="${n}"`,robustness:i.audioRobustness||"",encryptionScheme:i.audioEncryptionScheme||null})),videoCapabilities:t.map(n=>({contentType:`video/mp4; codecs="${n}"`,robustness:i.videoRobustness||"",encryptionScheme:i.videoEncryptionScheme||null}))}]}function Ue(s,e,t){return Uint8Array.prototype.slice?s.slice(e,t):new Uint8Array(Array.prototype.slice.call(s,e,t))}var Ms=(s,e)=>e+10<=s.length&&s[e]===73&&s[e+1]===68&&s[e+2]===51&&s[e+3]<255&&s[e+4]<255&&s[e+6]<128&&s[e+7]<128&&s[e+8]<128&&s[e+9]<128,qr=(s,e)=>e+10<=s.length&&s[e]===51&&s[e+1]===68&&s[e+2]===73&&s[e+3]<255&&s[e+4]<255&&s[e+6]<128&&s[e+7]<128&&s[e+8]<128&&s[e+9]<128,nt=(s,e)=>{let t=e,i=0;for(;Ms(s,e);){i+=10;let r=ii(s,e+6);i+=r,qr(s,e+10)&&(i+=10),e+=i}if(i>0)return s.subarray(t,t+i)},ii=(s,e)=>{let t=0;return t=(s[e]&127)<<21,t|=(s[e+1]&127)<<14,t|=(s[e+2]&127)<<7,t|=s[e+3]&127,t},Eo=(s,e)=>Ms(s,e)&&ii(s,e+6)+10<=s.length-e,Fs=s=>{let e=zr(s);for(let t=0;t<e.length;t++){let i=e[t];if(jr(i))return Lo(i)}},jr=s=>s&&s.key==="PRIV"&&s.info==="com.apple.streaming.transportStreamTimestamp",vo=s=>{let e=String.fromCharCode(s[0],s[1],s[2],s[3]),t=ii(s,4),i=10;return{type:e,size:t,data:s.subarray(i,i+t)}},zr=s=>{let e=0,t=[];for(;Ms(s,e);){let i=ii(s,e+6);e+=10;let r=e+i;for(;e+8<r;){let n=vo(s.subarray(e)),o=xo(n);o&&t.push(o),e+=n.size+10}qr(s,e)&&(e+=10)}return t},xo=s=>s.type==="PRIV"?So(s):s.type[0]==="W"?Ao(s):bo(s),So=s=>{if(s.size<2)return;let e=Re(s.data,!0),t=new Uint8Array(s.data.subarray(e.length+1));return{key:s.type,info:e,data:t.buffer}},bo=s=>{if(s.size<2)return;if(s.type==="TXXX"){let t=1,i=Re(s.data.subarray(t),!0);t+=i.length+1;let r=Re(s.data.subarray(t));return{key:s.type,info:i,data:r}}let e=Re(s.data.subarray(1));return{key:s.type,data:e}},Ao=s=>{if(s.type==="WXXX"){if(s.size<2)return;let t=1,i=Re(s.data.subarray(t),!0);t+=i.length+1;let r=Re(s.data.subarray(t));return{key:s.type,info:i,data:r}}let e=Re(s.data);return{key:s.type,data:e}},Lo=s=>{if(s.data.byteLength===8){let e=new Uint8Array(s.data),t=e[3]&1,i=(e[4]<<23)+(e[5]<<15)+(e[6]<<7)+e[7];return i/=45,t&&(i+=4772185884e-2),Math.round(i)}},Re=(s,e=!1)=>{let t=_o();if(t){let c=t.decode(s);if(e){let h=c.indexOf("\0");return h!==-1?c.substring(0,h):c}return c.replace(/\0/g,"")}let i=s.length,r,n,o,a="",l=0;for(;l<i;){if(r=s[l++],r===0&&e)return a;if(r===0||r===3)continue;switch(r>>4){case 0:case 1:case 2:case 3:case 4:case 5:case 6:case 7:a+=String.fromCharCode(r);break;case 12:case 13:n=s[l++],a+=String.fromCharCode((r&31)<<6|n&63);break;case 14:n=s[l++],o=s[l++],a+=String.fromCharCode((r&15)<<12|(n&63)<<6|(o&63)<<0);break}}return a},oi;function _o(){if(!navigator.userAgent.includes("PlayStation 4"))return!oi&&typeof self.TextDecoder!="undefined"&&(oi=new self.TextDecoder("utf-8")),oi}var Ae={hexDump:function(s){let e="";for(let t=0;t<s.length;t++){let i=s[t].toString(16);i.length<2&&(i="0"+i),e+=i}return e}},kt=Math.pow(2,32)-1,Ro=[].push,Xr={video:1,audio:2,id3:3,text:4};function re(s){return String.fromCharCode.apply(null,s)}function Qr(s,e){let t=s[e]<<8|s[e+1];return t<0?65536+t:t}function B(s,e){let t=Jr(s,e);return t<0?4294967296+t:t}function Qs(s,e){let t=B(s,e);return t*=Math.pow(2,32),t+=B(s,e+4),t}function Jr(s,e){return s[e]<<24|s[e+1]<<16|s[e+2]<<8|s[e+3]}function ai(s,e,t){s[e]=t>>24,s[e+1]=t>>16&255,s[e+2]=t>>8&255,s[e+3]=t&255}function Io(s){let e=s.byteLength;for(let t=0;t<e;){let i=B(s,t);if(i>8&&s[t+4]===109&&s[t+5]===111&&s[t+6]===111&&s[t+7]===102)return!0;t=i>1?t+i:e}return!1}function Y(s,e){let t=[];if(!e.length)return t;let i=s.byteLength;for(let r=0;r<i;){let n=B(s,r),o=re(s.subarray(r+4,r+8)),a=n>1?r+n:i;if(o===e[0])if(e.length===1)t.push(s.subarray(r+8,a));else{let l=Y(s.subarray(r+8,a),e.slice(1));l.length&&Ro.apply(t,l)}r=a}return t}function wo(s){let e=[],t=s[0],i=8,r=B(s,i);i+=4;let n=0,o=0;t===0?(n=B(s,i),o=B(s,i+4),i+=8):(n=Qs(s,i),o=Qs(s,i+8),i+=16),i+=2;let a=s.length+o,l=Qr(s,i);i+=2;for(let c=0;c<l;c++){let h=i,u=B(s,h);h+=4;let d=u&2147483647;if((u&2147483648)>>>31===1)return S.warn("SIDX has hierarchical references (not supported)"),null;let g=B(s,h);h+=4,e.push({referenceSize:d,subsegmentDuration:g,info:{duration:g/r,start:a,end:a+d-1}}),a+=d,h+=4,i=h}return{earliestPresentationTime:n,timescale:r,version:t,referencesCount:l,references:e}}function Zr(s){let e=[],t=Y(s,["moov","trak"]);for(let r=0;r<t.length;r++){let n=t[r],o=Y(n,["tkhd"])[0];if(o){let a=o[0],l=B(o,a===0?12:20),c=Y(n,["mdia","mdhd"])[0];if(c){a=c[0];let h=B(c,a===0?12:20),u=Y(n,["mdia","hdlr"])[0];if(u){let d=re(u.subarray(8,12)),f={soun:J.AUDIO,vide:J.VIDEO}[d];if(f){let g=Y(n,["mdia","minf","stbl","stsd"])[0],m=Co(g);e[l]={timescale:h,type:f},e[f]=he({timescale:h,id:l},m)}}}}}return Y(s,["moov","mvex","trex"]).forEach(r=>{let n=B(r,4),o=e[n];o&&(o.default={duration:B(r,12),flags:B(r,20)})}),e}function Co(s){let e=s.subarray(8),t=e.subarray(86),i=re(e.subarray(4,8)),r=i,n=i==="enca"||i==="encv";if(n){let a=Y(e,[i])[0].subarray(i==="enca"?28:78);Y(a,["sinf"]).forEach(c=>{let h=Y(c,["schm"])[0];if(h){let u=re(h.subarray(4,8));if(u==="cbcs"||u==="cenc"){let d=Y(c,["frma"])[0];d&&(r=re(d))}}})}switch(r){case"avc1":case"avc2":case"avc3":case"avc4":{let o=Y(t,["avcC"])[0];r+="."+gt(o[1])+gt(o[2])+gt(o[3]);break}case"mp4a":{let o=Y(e,[i])[0],a=Y(o.subarray(28),["esds"])[0];if(a&&a.length>12){let l=4;if(a[l++]!==3)break;l=li(a,l),l+=2;let c=a[l++];if(c&128&&(l+=2),c&64&&(l+=a[l++]),a[l++]!==4)break;l=li(a,l);let h=a[l++];if(h===64)r+="."+gt(h);else break;if(l+=12,a[l++]!==5)break;l=li(a,l);let u=a[l++],d=(u&248)>>3;d===31&&(d+=1+((u&7)<<3)+((a[l]&224)>>5)),r+="."+d}break}case"hvc1":case"hev1":{let o=Y(t,["hvcC"])[0],a=o[1],l=["","A","B","C"][a>>6],c=a&31,h=B(o,2),u=(a&32)>>5?"H":"L",d=o[12],f=o.subarray(6,12);r+="."+l+c,r+="."+h.toString(16).toUpperCase(),r+="."+u+d;let g="";for(let m=f.length;m--;){let y=f[m];(y||g)&&(g="."+y.toString(16).toUpperCase()+g)}r+=g;break}case"dvh1":case"dvhe":{let o=Y(t,["dvcC"])[0],a=o[2]>>1&127,l=o[2]<<5&32|o[3]>>3&31;r+="."+be(a)+"."+be(l);break}case"vp09":{let o=Y(t,["vpcC"])[0],a=o[4],l=o[5],c=o[6]>>4&15;r+="."+be(a)+"."+be(l)+"."+be(c);break}case"av01":{let o=Y(t,["av1C"])[0],a=o[1]>>>5,l=o[1]&31,c=o[2]>>>7?"H":"M",h=(o[2]&64)>>6,u=(o[2]&32)>>5,d=a===2&&h?u?12:10:h?10:8,f=(o[2]&16)>>4,g=(o[2]&8)>>3,m=(o[2]&4)>>2,y=o[2]&3;r+="."+a+"."+be(l)+c+"."+be(d)+"."+f+"."+g+m+y+"."+be(1)+"."+be(1)+"."+be(1)+"."+0;break}}return{codec:r,encrypted:n}}function li(s,e){let t=e+5;for(;s[e++]&128&&e<t;);return e}function gt(s){return("0"+s.toString(16).toUpperCase()).slice(-2)}function be(s){return(s<10?"0":"")+s}function Do(s,e){if(!s||!e)return s;let t=e.keyId;return t&&e.isCommonEncryption&&Y(s,["moov","trak"]).forEach(r=>{let o=Y(r,["mdia","minf","stbl","stsd"])[0].subarray(8),a=Y(o,["enca"]),l=a.length>0;l||(a=Y(o,["encv"])),a.forEach(c=>{let h=l?c.subarray(28):c.subarray(78);Y(h,["sinf"]).forEach(d=>{let f=en(d);if(f){let g=f.subarray(8,24);g.some(m=>m!==0)||(S.log(`[eme] Patching keyId in 'enc${l?"a":"v"}>sinf>>tenc' box: ${Ae.hexDump(g)} -> ${Ae.hexDump(t)}`),f.set(t,8))}})})}),s}function en(s){let e=Y(s,["schm"])[0];if(e){let t=re(e.subarray(4,8));if(t==="cbcs"||t==="cenc")return Y(s,["schi","tenc"])[0]}return S.error("[eme] missing 'schm' box"),null}function ko(s,e){return Y(e,["moof","traf"]).reduce((t,i)=>{let r=Y(i,["tfdt"])[0],n=r[0],o=Y(i,["tfhd"]).reduce((a,l)=>{let c=B(l,4),h=s[c];if(h){let u=B(r,4);if(n===1){if(u===kt)return S.warn("[mp4-demuxer]: Ignoring assumed invalid signed 64-bit track fragment decode time"),a;u*=kt+1,u+=B(r,8)}let d=h.timescale||9e4,f=u/d;if(M(f)&&(a===null||f<a))return f}return a},null);return o!==null&&M(o)&&(t===null||o<t)?o:t},null)}function Po(s,e){let t=0,i=0,r=0,n=Y(s,["moof","traf"]);for(let o=0;o<n.length;o++){let a=n[o],l=Y(a,["tfhd"])[0],c=B(l,4),h=e[c];if(!h)continue;let u=h.default,d=B(l,0)|(u==null?void 0:u.flags),f=u==null?void 0:u.duration;d&8&&(d&2?f=B(l,12):f=B(l,8));let g=h.timescale||9e4,m=Y(a,["trun"]);for(let y=0;y<m.length;y++){if(t=Oo(m[y]),!t&&f){let T=B(m[y],4);t=f*T}h.type===J.VIDEO?i+=t/g:h.type===J.AUDIO&&(r+=t/g)}}if(i===0&&r===0){let o=1/0,a=0,l=0,c=Y(s,["sidx"]);for(let h=0;h<c.length;h++){let u=wo(c[h]);if(u!=null&&u.references){o=Math.min(o,u.earliestPresentationTime/u.timescale);let d=u.references.reduce((f,g)=>f+g.info.duration||0,0);a=Math.max(a,d+u.earliestPresentationTime/u.timescale),l=a-o}}if(l&&M(l))return l}return i||r}function Oo(s){let e=B(s,0),t=8;e&1&&(t+=4),e&4&&(t+=4);let i=0,r=B(s,4);for(let n=0;n<r;n++){if(e&256){let o=B(s,t);i+=o,t+=4}e&512&&(t+=4),e&1024&&(t+=4),e&2048&&(t+=4)}return i}function Mo(s,e,t){Y(e,["moof","traf"]).forEach(i=>{Y(i,["tfhd"]).forEach(r=>{let n=B(r,4),o=s[n];if(!o)return;let a=o.timescale||9e4;Y(i,["tfdt"]).forEach(l=>{let c=l[0],h=t*a;if(h){let u=B(l,4);if(c===0)u-=h,u=Math.max(u,0),ai(l,4,u);else{u*=Math.pow(2,32),u+=B(l,8),u-=h,u=Math.max(u,0);let d=Math.floor(u/(kt+1)),f=Math.floor(u%(kt+1));ai(l,4,d),ai(l,8,f)}}})})})}function Fo(s){let e={valid:null,remainder:null},t=Y(s,["moof"]);if(t.length<2)return e.remainder=s,e;let i=t[t.length-1];return e.valid=Ue(s,0,i.byteOffset-8),e.remainder=Ue(s,i.byteOffset-8),e}function Te(s,e){let t=new Uint8Array(s.length+e.length);return t.set(s),t.set(e,s.length),t}function Js(s,e){let t=[],i=e.samples,r=e.timescale,n=e.id,o=!1;return Y(i,["moof"]).map(l=>{let c=l.byteOffset-8;Y(l,["traf"]).map(u=>{let d=Y(u,["tfdt"]).map(f=>{let g=f[0],m=B(f,4);return g===1&&(m*=Math.pow(2,32),m+=B(f,8)),m/r})[0];return d!==void 0&&(s=d),Y(u,["tfhd"]).map(f=>{let g=B(f,4),m=B(f,0)&16777215,y=(m&1)!==0,T=(m&2)!==0,v=(m&8)!==0,E=0,_=(m&16)!==0,x=0,I=(m&32)!==0,L=8;g===n&&(y&&(L+=8),T&&(L+=4),v&&(E=B(f,L),L+=4),_&&(x=B(f,L),L+=4),I&&(L+=4),e.type==="video"&&(o=No(e.codec)),Y(u,["trun"]).map(C=>{let k=C[0],R=B(C,0)&16777215,D=(R&1)!==0,V=0,P=(R&4)!==0,K=(R&256)!==0,H=0,N=(R&512)!==0,q=0,X=(R&1024)!==0,F=(R&2048)!==0,O=0,z=B(C,4),W=8;D&&(V=B(C,W),W+=4),P&&(W+=4);let Q=V+c;for(let ie=0;ie<z;ie++){if(K?(H=B(C,W),W+=4):H=E,N?(q=B(C,W),W+=4):q=x,X&&(W+=4),F&&(k===0?O=B(C,W):O=Jr(C,W),W+=4),e.type===J.VIDEO){let oe=0;for(;oe<q;){let ue=B(i,Q);if(Q+=4,Bo(o,i[Q])){let me=i.subarray(Q,Q+ue);tn(me,o?2:1,s+O/r,t)}Q+=ue,oe+=ue+4}}s+=H/r}}))})})}),t}function No(s){if(!s)return!1;let e=s.indexOf("."),t=e<0?s:s.substring(0,e);return t==="hvc1"||t==="hev1"||t==="dvh1"||t==="dvhe"}function Bo(s,e){if(s){let t=e>>1&63;return t===39||t===40}else return(e&31)===6}function tn(s,e,t,i){let r=sn(s),n=0;n+=e;let o=0,a=0,l=0;for(;n<r.length;){o=0;do{if(n>=r.length)break;l=r[n++],o+=l}while(l===255);a=0;do{if(n>=r.length)break;l=r[n++],a+=l}while(l===255);let c=r.length-n,h=n;if(a<c)n+=a;else if(a>c){S.error(`Malformed SEI payload. ${a} is too small, only ${c} bytes left to parse.`);break}if(o===4){if(r[h++]===181){let d=Qr(r,h);if(h+=2,d===49){let f=B(r,h);if(h+=4,f===1195456820){let g=r[h++];if(g===3){let m=r[h++],y=31&m,T=64&m,v=T?2+y*3:0,E=new Uint8Array(v);if(T){E[0]=m;for(let _=1;_<v;_++)E[_]=r[h++]}i.push({type:g,payloadType:o,pts:t,bytes:E})}}}}}else if(o===5&&a>16){let u=[];for(let g=0;g<16;g++){let m=r[h++].toString(16);u.push(m.length==1?"0"+m:m),(g===3||g===5||g===7||g===9)&&u.push("-")}let d=a-16,f=new Uint8Array(d);for(let g=0;g<d;g++)f[g]=r[h++];i.push({payloadType:o,pts:t,uuid:u.join(""),userData:Re(f),userDataBytes:f})}}}function sn(s){let e=s.byteLength,t=[],i=1;for(;i<e-2;)s[i]===0&&s[i+1]===0&&s[i+2]===3?(t.push(i+2),i+=2):i++;if(t.length===0)return s;let r=e-t.length,n=new Uint8Array(r),o=0;for(i=0;i<r;o++,i++)o===t[0]&&(o++,t.shift()),n[i]=s[o];return n}function Uo(s){let e=s[0],t="",i="",r=0,n=0,o=0,a=0,l=0,c=0;if(e===0){for(;re(s.subarray(c,c+1))!=="\0";)t+=re(s.subarray(c,c+1)),c+=1;for(t+=re(s.subarray(c,c+1)),c+=1;re(s.subarray(c,c+1))!=="\0";)i+=re(s.subarray(c,c+1)),c+=1;i+=re(s.subarray(c,c+1)),c+=1,r=B(s,12),n=B(s,16),a=B(s,20),l=B(s,24),c=28}else if(e===1){c+=4,r=B(s,c),c+=4;let u=B(s,c);c+=4;let d=B(s,c);for(c+=4,o=Ys(2,32)*u+d,so(o)||(o=Number.MAX_SAFE_INTEGER,S.warn("Presentation time exceeds safe integer limit and wrapped to max safe integer in parsing emsg box")),a=B(s,c),c+=4,l=B(s,c),c+=4;re(s.subarray(c,c+1))!=="\0";)t+=re(s.subarray(c,c+1)),c+=1;for(t+=re(s.subarray(c,c+1)),c+=1;re(s.subarray(c,c+1))!=="\0";)i+=re(s.subarray(c,c+1)),c+=1;i+=re(s.subarray(c,c+1)),c+=1}let h=s.subarray(c,s.byteLength);return{schemeIdUri:t,value:i,timeScale:r,presentationTime:o,presentationTimeDelta:n,eventDuration:a,id:l,payload:h}}function $o(s,...e){let t=e.length,i=8,r=t;for(;r--;)i+=e[r].byteLength;let n=new Uint8Array(i);for(n[0]=i>>24&255,n[1]=i>>16&255,n[2]=i>>8&255,n[3]=i&255,n.set(s,4),r=0,i=8;r<t;r++)n.set(e[r],i),i+=e[r].byteLength;return n}function Go(s,e,t){if(s.byteLength!==16)throw new RangeError("Invalid system id");let i,r;if(e){i=1,r=new Uint8Array(e.length*16);for(let a=0;a<e.length;a++){let l=e[a];if(l.byteLength!==16)throw new RangeError("Invalid key");r.set(l,a*16)}}else i=0,r=new Uint8Array;let n;i>0?(n=new Uint8Array(4),e.length>0&&new DataView(n.buffer).setUint32(0,e.length,!1)):n=new Uint8Array;let o=new Uint8Array(4);return t&&t.byteLength>0&&new DataView(o.buffer).setUint32(0,t.byteLength,!1),$o([112,115,115,104],new Uint8Array([i,0,0,0]),s,n,r,o,t||new Uint8Array)}function Ho(s){if(!(s instanceof ArrayBuffer)||s.byteLength<32)return null;let e={version:0,systemId:"",kids:null,data:null},t=new DataView(s),i=t.getUint32(0);if(s.byteLength!==i&&i>44||t.getUint32(4)!==1886614376||(e.version=t.getUint32(8)>>>24,e.version>1))return null;e.systemId=Ae.hexDump(new Uint8Array(s,12,16));let n=t.getUint32(28);if(e.version===0){if(i-32<n)return null;e.data=new Uint8Array(s,32,n)}else if(e.version===1){e.kids=[];for(let o=0;o<n;o++)e.kids.push(new Uint8Array(s,32+o*16,16))}return e}var pt={},ot=class s{static clearKeyUriToKeyIdMap(){pt={}}constructor(e,t,i,r=[1],n=null){this.uri=void 0,this.method=void 0,this.keyFormat=void 0,this.keyFormatVersions=void 0,this.encrypted=void 0,this.isCommonEncryption=void 0,this.iv=null,this.key=null,this.keyId=null,this.pssh=null,this.method=e,this.uri=t,this.keyFormat=i,this.keyFormatVersions=r,this.iv=n,this.encrypted=e?e!=="NONE":!1,this.isCommonEncryption=this.encrypted&&e!=="AES-128"}isSupported(){if(this.method){if(this.method==="AES-128"||this.method==="NONE")return!0;if(this.keyFormat==="identity")return this.method==="SAMPLE-AES";switch(this.keyFormat){case pe.FAIRPLAY:case pe.WIDEVINE:case pe.PLAYREADY:case pe.CLEARKEY:return["ISO-23001-7","SAMPLE-AES","SAMPLE-AES-CENC","SAMPLE-AES-CTR"].indexOf(this.method)!==-1}}return!1}getDecryptData(e){if(!this.encrypted||!this.uri)return null;if(this.method==="AES-128"&&this.uri&&!this.iv){typeof e!="number"&&(this.method==="AES-128"&&!this.iv&&S.warn(`missing IV for initialization segment with method="${this.method}" - compliance issue`),e=0);let i=Ko(e);return new s(this.method,this.uri,"identity",this.keyFormatVersions,i)}let t=po(this.uri);if(t)switch(this.keyFormat){case pe.WIDEVINE:this.pssh=t,t.length>=22&&(this.keyId=t.subarray(t.length-22,t.length-6));break;case pe.PLAYREADY:{let i=new Uint8Array([154,4,240,121,152,64,66,134,171,146,230,91,224,136,95,149]);this.pssh=Go(i,null,t);let r=new Uint16Array(t.buffer,t.byteOffset,t.byteLength/2),n=String.fromCharCode.apply(null,Array.from(r)),o=n.substring(n.indexOf("<"),n.length),c=new DOMParser().parseFromString(o,"text/xml").getElementsByTagName("KID")[0];if(c){let h=c.childNodes[0]?c.childNodes[0].nodeValue:c.getAttribute("VALUE");if(h){let u=Os(h).subarray(0,16);go(u),this.keyId=u}}break}default:{let i=t.subarray(0,16);if(i.length!==16){let r=new Uint8Array(16);r.set(i,16-i.length),i=r}this.keyId=i;break}}if(!this.keyId||this.keyId.byteLength!==16){let i=pt[this.uri];if(!i){let r=Object.keys(pt).length%Number.MAX_SAFE_INTEGER;i=new Uint8Array(16),new DataView(i.buffer,12,4).setUint32(0,r),pt[this.uri]=i}this.keyId=i}return this}};function Ko(s){let e=new Uint8Array(16);for(let t=12;t<16;t++)e[t]=s>>8*(15-t)&255;return e}var rn=/\{\$([a-zA-Z0-9-_]+)\}/g;function Zs(s){return rn.test(s)}function fe(s,e,t){if(s.variableList!==null||s.hasVariableRefs)for(let i=t.length;i--;){let r=t[i],n=e[r];n&&(e[r]=Ii(s,n))}}function Ii(s,e){if(s.variableList!==null||s.hasVariableRefs){let t=s.variableList;return e.replace(rn,i=>{let r=i.substring(2,i.length-1),n=t==null?void 0:t[r];return n===void 0?(s.playlistParsingError||(s.playlistParsingError=new Error(`Missing preceding EXT-X-DEFINE tag for Variable Reference: "${r}"`)),i):n})}return e}function er(s,e,t){let i=s.variableList;i||(s.variableList=i={});let r,n;if("QUERYPARAM"in e){r=e.QUERYPARAM;try{let o=new self.URL(t).searchParams;if(o.has(r))n=o.get(r);else throw new Error(`"${r}" does not match any query parameter in URI: "${t}"`)}catch(o){s.playlistParsingError||(s.playlistParsingError=new Error(`EXT-X-DEFINE QUERYPARAM: ${o.message}`))}}else r=e.NAME,n=e.VALUE;r in i?s.playlistParsingError||(s.playlistParsingError=new Error(`EXT-X-DEFINE duplicate Variable Name declarations: "${r}"`)):i[r]=n||""}function Vo(s,e,t){let i=e.IMPORT;if(t&&i in t){let r=s.variableList;r||(s.variableList=r={}),r[i]=t[i]}else s.playlistParsingError||(s.playlistParsingError=new Error(`EXT-X-DEFINE IMPORT attribute not found in Multivariant Playlist: "${i}"`))}function $e(s=!0){return typeof self=="undefined"?void 0:(s||!self.MediaSource)&&self.ManagedMediaSource||self.MediaSource||self.WebKitMediaSource}function Yo(s){return typeof self!="undefined"&&s===self.ManagedMediaSource}var Pt={audio:{a3ds:1,"ac-3":.95,"ac-4":1,alac:.9,alaw:1,dra1:1,"dts+":1,"dts-":1,dtsc:1,dtse:1,dtsh:1,"ec-3":.9,enca:1,fLaC:.9,flac:.9,FLAC:.9,g719:1,g726:1,m4ae:1,mha1:1,mha2:1,mhm1:1,mhm2:1,mlpa:1,mp4a:1,"raw ":1,Opus:1,opus:1,samr:1,sawb:1,sawp:1,sevc:1,sqcp:1,ssmv:1,twos:1,ulaw:1},video:{avc1:1,avc2:1,avc3:1,avc4:1,avcp:1,av01:.8,drac:1,dva1:1,dvav:1,dvh1:.7,dvhe:.7,encv:1,hev1:.75,hvc1:.75,mjp2:1,mp4v:1,mvc1:1,mvc2:1,mvc3:1,mvc4:1,resv:1,rv60:1,s263:1,svc1:1,svc2:1,"vc-1":1,vp08:1,vp09:.9},text:{stpp:1,wvtt:1}};function Wo(s,e){let t=Pt[e];return!!t&&!!t[s.slice(0,4)]}function ci(s,e,t=!0){return!s.split(",").some(i=>!nn(i,e,t))}function nn(s,e,t=!0){var i;let r=$e(t);return(i=r==null?void 0:r.isTypeSupported(at(s,e)))!=null?i:!1}function at(s,e){return`${e}/mp4;codecs="${s}"`}function tr(s){if(s){let e=s.substring(0,4);return Pt.video[e]}return 2}function Ot(s){return s.split(",").reduce((e,t)=>{let i=Pt.video[t];return i?(i*2+e)/(e?3:2):(Pt.audio[t]+e)/(e?2:1)},0)}var hi={};function qo(s,e=!0){if(hi[s])return hi[s];let t={flac:["flac","fLaC","FLAC"],opus:["opus","Opus"]}[s];for(let i=0;i<t.length;i++)if(nn(t[i],"audio",e))return hi[s]=t[i],t[i];return s}var jo=/flac|opus/i;function Mt(s,e=!0){return s.replace(jo,t=>qo(t.toLowerCase(),e))}function ir(s,e){return s&&s!=="mp4a"?s:e&&e.split(",")[0]}function zo(s){let e=s.split(".");if(e.length>2){let t=e.shift()+".";return t+=parseInt(e.shift()).toString(16),t+=("000"+parseInt(e.shift()).toString(16)).slice(-4),t}return s}var sr=/#EXT-X-STREAM-INF:([^\r\n]*)(?:[\r\n](?:#[^\r\n]*)?)*([^\r\n]+)|#EXT-X-(SESSION-DATA|SESSION-KEY|DEFINE|CONTENT-STEERING|START):([^\r\n]*)[\r\n]+/g,rr=/#EXT-X-MEDIA:(.*)/g,Xo=/^#EXT(?:INF|-X-TARGETDURATION):/m,nr=new RegExp([/#EXTINF:\s*(\d*(?:\.\d+)?)(?:,(.*)\s+)?/.source,/(?!#) *(\S[\S ]*)/.source,/#EXT-X-BYTERANGE:*(.+)/.source,/#EXT-X-PROGRAM-DATE-TIME:(.+)/.source,/#.*/.source].join("|"),"g"),Qo=new RegExp([/#(EXTM3U)/.source,/#EXT-X-(DATERANGE|DEFINE|KEY|MAP|PART|PART-INF|PLAYLIST-TYPE|PRELOAD-HINT|RENDITION-REPORT|SERVER-CONTROL|SKIP|START):(.+)/.source,/#EXT-X-(BITRATE|DISCONTINUITY-SEQUENCE|MEDIA-SEQUENCE|TARGETDURATION|VERSION): *(\d+)/.source,/#EXT-X-(DISCONTINUITY|ENDLIST|GAP|INDEPENDENT-SEGMENTS)/.source,/(#)([^:]*):(.*)/.source,/(#)(.*)(?:.*)\r?\n?/.source].join("|")),Ne=class s{static findGroup(e,t){for(let i=0;i<e.length;i++){let r=e[i];if(r.id===t)return r}}static resolve(e,t){return Ps.buildAbsoluteURL(t,e,{alwaysNormalize:!0})}static isMediaPlaylist(e){return Xo.test(e)}static parseMasterPlaylist(e,t){let i=Zs(e),r={contentSteering:null,levels:[],playlistParsingError:null,sessionData:null,sessionKeys:null,startTimeOffset:null,variableList:null,hasVariableRefs:i},n=[];sr.lastIndex=0;let o;for(;(o=sr.exec(e))!=null;)if(o[1]){var a;let c=new te(o[1]);fe(r,c,["CODECS","SUPPLEMENTAL-CODECS","ALLOWED-CPC","PATHWAY-ID","STABLE-VARIANT-ID","AUDIO","VIDEO","SUBTITLES","CLOSED-CAPTIONS","NAME"]);let h=Ii(r,o[2]),u={attrs:c,bitrate:c.decimalInteger("BANDWIDTH")||c.decimalInteger("AVERAGE-BANDWIDTH"),name:c.NAME,url:s.resolve(h,t)},d=c.decimalResolution("RESOLUTION");d&&(u.width=d.width,u.height=d.height),Jo(c.CODECS,u),(a=u.unknownCodecs)!=null&&a.length||n.push(u),r.levels.push(u)}else if(o[3]){let c=o[3],h=o[4];switch(c){case"SESSION-DATA":{let u=new te(h);fe(r,u,["DATA-ID","LANGUAGE","VALUE","URI"]);let d=u["DATA-ID"];d&&(r.sessionData===null&&(r.sessionData={}),r.sessionData[d]=u);break}case"SESSION-KEY":{let u=or(h,t,r);u.encrypted&&u.isSupported()?(r.sessionKeys===null&&(r.sessionKeys=[]),r.sessionKeys.push(u)):S.warn(`[Keys] Ignoring invalid EXT-X-SESSION-KEY tag: "${h}"`);break}case"DEFINE":{{let u=new te(h);fe(r,u,["NAME","VALUE","QUERYPARAM"]),er(r,u,t)}break}case"CONTENT-STEERING":{let u=new te(h);fe(r,u,["SERVER-URI","PATHWAY-ID"]),r.contentSteering={uri:s.resolve(u["SERVER-URI"],t),pathwayId:u["PATHWAY-ID"]||"."};break}case"START":{r.startTimeOffset=ar(h);break}}}let l=n.length>0&&n.length<r.levels.length;return r.levels=l?n:r.levels,r.levels.length===0&&(r.playlistParsingError=new Error("no levels found in manifest")),r}static parseMasterPlaylistMedia(e,t,i){let r,n={},o=i.levels,a={AUDIO:o.map(c=>({id:c.attrs.AUDIO,audioCodec:c.audioCodec})),SUBTITLES:o.map(c=>({id:c.attrs.SUBTITLES,textCodec:c.textCodec})),"CLOSED-CAPTIONS":[]},l=0;for(rr.lastIndex=0;(r=rr.exec(e))!==null;){let c=new te(r[1]),h=c.TYPE;if(h){let u=a[h],d=n[h]||[];n[h]=d,fe(i,c,["URI","GROUP-ID","LANGUAGE","ASSOC-LANGUAGE","STABLE-RENDITION-ID","NAME","INSTREAM-ID","CHARACTERISTICS","CHANNELS"]);let f=c.LANGUAGE,g=c["ASSOC-LANGUAGE"],m=c.CHANNELS,y=c.CHARACTERISTICS,T=c["INSTREAM-ID"],v={attrs:c,bitrate:0,id:l++,groupId:c["GROUP-ID"]||"",name:c.NAME||f||"",type:h,default:c.bool("DEFAULT"),autoselect:c.bool("AUTOSELECT"),forced:c.bool("FORCED"),lang:f,url:c.URI?s.resolve(c.URI,t):""};if(g&&(v.assocLang=g),m&&(v.channels=m),y&&(v.characteristics=y),T&&(v.instreamId=T),u!=null&&u.length){let E=s.findGroup(u,v.groupId)||u[0];lr(v,E,"audioCodec"),lr(v,E,"textCodec")}d.push(v)}}return n}static parseLevelPlaylist(e,t,i,r,n,o){let a=new _i(t),l=a.fragments,c=null,h=0,u=0,d=0,f=0,g=null,m=new st(r,t),y,T,v,E=-1,_=!1,x=null;for(nr.lastIndex=0,a.m3u8=e,a.hasVariableRefs=Zs(e);(y=nr.exec(e))!==null;){_&&(_=!1,m=new st(r,t),m.start=d,m.sn=h,m.cc=f,m.level=i,c&&(m.initSegment=c,m.rawProgramDateTime=c.rawProgramDateTime,c.rawProgramDateTime=null,x&&(m.setByteRange(x),x=null)));let k=y[1];if(k){m.duration=parseFloat(k);let R=(" "+y[2]).slice(1);m.title=R||null,m.tagList.push(R?["INF",k,R]:["INF",k])}else if(y[3]){if(M(m.duration)){m.start=d,v&&ur(m,v,a),m.sn=h,m.level=i,m.cc=f,l.push(m);let R=(" "+y[3]).slice(1);m.relurl=Ii(a,R),cr(m,g),g=m,d+=m.duration,h++,u=0,_=!0}}else if(y[4]){let R=(" "+y[4]).slice(1);g?m.setByteRange(R,g):m.setByteRange(R)}else if(y[5])m.rawProgramDateTime=(" "+y[5]).slice(1),m.tagList.push(["PROGRAM-DATE-TIME",m.rawProgramDateTime]),E===-1&&(E=l.length);else{if(y=y[0].match(Qo),!y){S.warn("No matches on slow regex match for level playlist!");continue}for(T=1;T<y.length&&typeof y[T]=="undefined";T++);let R=(" "+y[T]).slice(1),D=(" "+y[T+1]).slice(1),V=y[T+2]?(" "+y[T+2]).slice(1):"";switch(R){case"PLAYLIST-TYPE":a.type=D.toUpperCase();break;case"MEDIA-SEQUENCE":h=a.startSN=parseInt(D);break;case"SKIP":{let P=new te(D);fe(a,P,["RECENTLY-REMOVED-DATERANGES"]);let K=P.decimalInteger("SKIPPED-SEGMENTS");if(M(K)){a.skippedSegments=K;for(let N=K;N--;)l.unshift(null);h+=K}let H=P.enumeratedString("RECENTLY-REMOVED-DATERANGES");H&&(a.recentlyRemovedDateranges=H.split("	"));break}case"TARGETDURATION":a.targetduration=Math.max(parseInt(D),1);break;case"VERSION":a.version=parseInt(D);break;case"INDEPENDENT-SEGMENTS":case"EXTM3U":break;case"ENDLIST":a.live=!1;break;case"#":(D||V)&&m.tagList.push(V?[D,V]:[D]);break;case"DISCONTINUITY":f++,m.tagList.push(["DIS"]);break;case"GAP":m.gap=!0,m.tagList.push([R]);break;case"BITRATE":m.tagList.push([R,D]);break;case"DATERANGE":{let P=new te(D);fe(a,P,["ID","CLASS","START-DATE","END-DATE","SCTE35-CMD","SCTE35-OUT","SCTE35-IN"]),fe(a,P,P.clientAttrs);let K=new Ct(P,a.dateRanges[P.ID]);K.isValid||a.skippedSegments?a.dateRanges[K.id]=K:S.warn(`Ignoring invalid DATERANGE tag: "${D}"`),m.tagList.push(["EXT-X-DATERANGE",D]);break}case"DEFINE":{{let P=new te(D);fe(a,P,["NAME","VALUE","IMPORT","QUERYPARAM"]),"IMPORT"in P?Vo(a,P,o):er(a,P,t)}break}case"DISCONTINUITY-SEQUENCE":f=parseInt(D);break;case"KEY":{let P=or(D,t,a);if(P.isSupported()){if(P.method==="NONE"){v=void 0;break}v||(v={}),v[P.keyFormat]&&(v=se({},v)),v[P.keyFormat]=P}else S.warn(`[Keys] Ignoring invalid EXT-X-KEY tag: "${D}"`);break}case"START":a.startTimeOffset=ar(D);break;case"MAP":{let P=new te(D);if(fe(a,P,["BYTERANGE","URI"]),m.duration){let K=new st(r,t);hr(K,P,i,v),c=K,m.initSegment=c,c.rawProgramDateTime&&!m.rawProgramDateTime&&(m.rawProgramDateTime=c.rawProgramDateTime)}else{let K=m.byteRangeEndOffset;if(K){let H=m.byteRangeStartOffset;x=`${K-H}@${H}`}else x=null;hr(m,P,i,v),c=m,_=!0}break}case"SERVER-CONTROL":{let P=new te(D);a.canBlockReload=P.bool("CAN-BLOCK-RELOAD"),a.canSkipUntil=P.optionalFloat("CAN-SKIP-UNTIL",0),a.canSkipDateRanges=a.canSkipUntil>0&&P.bool("CAN-SKIP-DATERANGES"),a.partHoldBack=P.optionalFloat("PART-HOLD-BACK",0),a.holdBack=P.optionalFloat("HOLD-BACK",0);break}case"PART-INF":{let P=new te(D);a.partTarget=P.decimalFloatingPoint("PART-TARGET");break}case"PART":{let P=a.partList;P||(P=a.partList=[]);let K=u>0?P[P.length-1]:void 0,H=u++,N=new te(D);fe(a,N,["BYTERANGE","URI"]);let q=new Li(N,m,t,H,K);P.push(q),m.duration+=q.duration;break}case"PRELOAD-HINT":{let P=new te(D);fe(a,P,["URI"]),a.preloadHint=P;break}case"RENDITION-REPORT":{let P=new te(D);fe(a,P,["URI"]),a.renditionReports=a.renditionReports||[],a.renditionReports.push(P);break}default:S.warn(`line parsed but not handled: ${y}`);break}}}g&&!g.relurl?(l.pop(),d-=g.duration,a.partList&&(a.fragmentHint=g)):a.partList&&(cr(m,g),m.cc=f,a.fragmentHint=m,v&&ur(m,v,a));let I=l.length,L=l[0],C=l[I-1];if(d+=a.skippedSegments*a.targetduration,d>0&&I&&C){a.averagetargetduration=d/I;let k=C.sn;a.endSN=k!=="initSegment"?k:0,a.live||(C.endList=!0),L&&(a.startCC=L.cc)}else a.endSN=0,a.startCC=0;return a.fragmentHint&&(d+=a.fragmentHint.duration),a.totalduration=d,a.endCC=f,E>0&&Zo(l,E),a}};function or(s,e,t){var i,r;let n=new te(s);fe(t,n,["KEYFORMAT","KEYFORMATVERSIONS","URI","IV","URI"]);let o=(i=n.METHOD)!=null?i:"",a=n.URI,l=n.hexadecimalInteger("IV"),c=n.KEYFORMATVERSIONS,h=(r=n.KEYFORMAT)!=null?r:"identity";a&&n.IV&&!l&&S.error(`Invalid IV: ${n.IV}`);let u=a?Ne.resolve(a,e):"",d=(c||"1").split("/").map(Number).filter(Number.isFinite);return new ot(o,u,h,d,l)}function ar(s){let t=new te(s).decimalFloatingPoint("TIME-OFFSET");return M(t)?t:null}function Jo(s,e){let t=(s||"").split(/[ ,]+/).filter(i=>i);["video","audio","text"].forEach(i=>{let r=t.filter(n=>Wo(n,i));r.length&&(e[`${i}Codec`]=r.join(","),t=t.filter(n=>r.indexOf(n)===-1))}),e.unknownCodecs=t}function lr(s,e,t){let i=e[t];i&&(s[t]=i)}function Zo(s,e){let t=s[e];for(let i=e;i--;){let r=s[i];if(!r)return;r.programDateTime=t.programDateTime-r.duration*1e3,t=r}}function cr(s,e){s.rawProgramDateTime?s.programDateTime=Date.parse(s.rawProgramDateTime):e!=null&&e.programDateTime&&(s.programDateTime=e.endProgramDateTime),M(s.programDateTime)||(s.programDateTime=null,s.rawProgramDateTime=null)}function hr(s,e,t,i){s.relurl=e.URI,e.BYTERANGE&&s.setByteRange(e.BYTERANGE),s.level=t,s.sn="initSegment",i&&(s.levelkeys=i),s.initSegment=null}function ur(s,e,t){s.levelkeys=e;let{encryptedFragments:i}=t;(!i.length||i[i.length-1].levelkeys!==e)&&Object.keys(e).some(r=>e[r].isCommonEncryption)&&i.push(s)}var j={MANIFEST:"manifest",LEVEL:"level",AUDIO_TRACK:"audioTrack",SUBTITLE_TRACK:"subtitleTrack"},$={MAIN:"main",AUDIO:"audio",SUBTITLE:"subtitle"};function dr(s){let{type:e}=s;switch(e){case j.AUDIO_TRACK:return $.AUDIO;case j.SUBTITLE_TRACK:return $.SUBTITLE;default:return $.MAIN}}function ui(s,e){let t=s.url;return(t===void 0||t.indexOf("data:")===0)&&(t=e.url),t}var wi=class{constructor(e){this.hls=void 0,this.loaders=Object.create(null),this.variableList=null,this.hls=e,this.registerListeners()}startLoad(e){}stopLoad(){this.destroyInternalLoaders()}registerListeners(){let{hls:e}=this;e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.LEVEL_LOADING,this.onLevelLoading,this),e.on(p.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),e.on(p.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)}unregisterListeners(){let{hls:e}=this;e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.LEVEL_LOADING,this.onLevelLoading,this),e.off(p.AUDIO_TRACK_LOADING,this.onAudioTrackLoading,this),e.off(p.SUBTITLE_TRACK_LOADING,this.onSubtitleTrackLoading,this)}createInternalLoader(e){let t=this.hls.config,i=t.pLoader,r=t.loader,n=i||r,o=new n(t);return this.loaders[e.type]=o,o}getInternalLoader(e){return this.loaders[e.type]}resetInternalLoader(e){this.loaders[e]&&delete this.loaders[e]}destroyInternalLoaders(){for(let e in this.loaders){let t=this.loaders[e];t&&t.destroy(),this.resetInternalLoader(e)}}destroy(){this.variableList=null,this.unregisterListeners(),this.destroyInternalLoaders()}onManifestLoading(e,t){let{url:i}=t;this.variableList=null,this.load({id:null,level:0,responseType:"text",type:j.MANIFEST,url:i,deliveryDirectives:null})}onLevelLoading(e,t){let{id:i,level:r,pathwayId:n,url:o,deliveryDirectives:a}=t;this.load({id:i,level:r,pathwayId:n,responseType:"text",type:j.LEVEL,url:o,deliveryDirectives:a})}onAudioTrackLoading(e,t){let{id:i,groupId:r,url:n,deliveryDirectives:o}=t;this.load({id:i,groupId:r,level:null,responseType:"text",type:j.AUDIO_TRACK,url:n,deliveryDirectives:o})}onSubtitleTrackLoading(e,t){let{id:i,groupId:r,url:n,deliveryDirectives:o}=t;this.load({id:i,groupId:r,level:null,responseType:"text",type:j.SUBTITLE_TRACK,url:n,deliveryDirectives:o})}load(e){var t;let i=this.hls.config,r=this.getInternalLoader(e);if(r){let c=r.context;if(c&&c.url===e.url&&c.level===e.level){S.trace("[playlist-loader]: playlist request ongoing");return}S.log(`[playlist-loader]: aborting previous loader for type: ${e.type}`),r.abort()}let n;if(e.type===j.MANIFEST?n=i.manifestLoadPolicy.default:n=se({},i.playlistLoadPolicy.default,{timeoutRetry:null,errorRetry:null}),r=this.createInternalLoader(e),M((t=e.deliveryDirectives)==null?void 0:t.part)){let c;if(e.type===j.LEVEL&&e.level!==null?c=this.hls.levels[e.level].details:e.type===j.AUDIO_TRACK&&e.id!==null?c=this.hls.audioTracks[e.id].details:e.type===j.SUBTITLE_TRACK&&e.id!==null&&(c=this.hls.subtitleTracks[e.id].details),c){let h=c.partTarget,u=c.targetduration;if(h&&u){let d=Math.max(h*3,u*.8)*1e3;n=se({},n,{maxTimeToFirstByteMs:Math.min(d,n.maxTimeToFirstByteMs),maxLoadTimeMs:Math.min(d,n.maxTimeToFirstByteMs)})}}}let o=n.errorRetry||n.timeoutRetry||{},a={loadPolicy:n,timeout:n.maxLoadTimeMs,maxRetry:o.maxNumRetry||0,retryDelay:o.retryDelayMs||0,maxRetryDelay:o.maxRetryDelayMs||0},l={onSuccess:(c,h,u,d)=>{let f=this.getInternalLoader(u);this.resetInternalLoader(u.type);let g=c.data;if(g.indexOf("#EXTM3U")!==0){this.handleManifestParsingError(c,u,new Error("no EXTM3U delimiter"),d||null,h);return}h.parsing.start=performance.now(),Ne.isMediaPlaylist(g)?this.handleTrackOrLevelPlaylist(c,h,u,d||null,f):this.handleMasterPlaylist(c,h,u,d)},onError:(c,h,u,d)=>{this.handleNetworkError(h,u,!1,c,d)},onTimeout:(c,h,u)=>{this.handleNetworkError(h,u,!0,void 0,c)}};r.load(e,a,l)}handleMasterPlaylist(e,t,i,r){let n=this.hls,o=e.data,a=ui(e,i),l=Ne.parseMasterPlaylist(o,a);if(l.playlistParsingError){this.handleManifestParsingError(e,i,l.playlistParsingError,r,t);return}let{contentSteering:c,levels:h,sessionData:u,sessionKeys:d,startTimeOffset:f,variableList:g}=l;this.variableList=g;let{AUDIO:m=[],SUBTITLES:y,"CLOSED-CAPTIONS":T}=Ne.parseMasterPlaylistMedia(o,a,l);m.length&&!m.some(E=>!E.url)&&h[0].audioCodec&&!h[0].attrs.AUDIO&&(S.log("[playlist-loader]: audio codec signaled in quality level, but no embedded audio track signaled, create one"),m.unshift({type:"main",name:"main",groupId:"main",default:!1,autoselect:!1,forced:!1,id:-1,attrs:new te({}),bitrate:0,url:""})),n.trigger(p.MANIFEST_LOADED,{levels:h,audioTracks:m,subtitles:y,captions:T,contentSteering:c,url:a,stats:t,networkDetails:r,sessionData:u,sessionKeys:d,startTimeOffset:f,variableList:g})}handleTrackOrLevelPlaylist(e,t,i,r,n){let o=this.hls,{id:a,level:l,type:c}=i,h=ui(e,i),u=0,d=M(l)?l:M(a)?a:0,f=dr(i),g=Ne.parseLevelPlaylist(e.data,h,d,f,u,this.variableList);if(c===j.MANIFEST){let m={attrs:new te({}),bitrate:0,details:g,name:"",url:h};o.trigger(p.MANIFEST_LOADED,{levels:[m],audioTracks:[],url:h,stats:t,networkDetails:r,sessionData:null,sessionKeys:null,contentSteering:null,startTimeOffset:null,variableList:null})}t.parsing.end=performance.now(),i.levelDetails=g,this.handlePlaylistLoaded(g,e,t,i,r,n)}handleManifestParsingError(e,t,i,r,n){this.hls.trigger(p.ERROR,{type:G.NETWORK_ERROR,details:b.MANIFEST_PARSING_ERROR,fatal:t.type===j.MANIFEST,url:e.url,err:i,error:i,reason:i.message,response:e,context:t,networkDetails:r,stats:n})}handleNetworkError(e,t,i=!1,r,n){let o=`A network ${i?"timeout":"error"+(r?" (status "+r.code+")":"")} occurred while loading ${e.type}`;e.type===j.LEVEL?o+=`: ${e.level} id: ${e.id}`:(e.type===j.AUDIO_TRACK||e.type===j.SUBTITLE_TRACK)&&(o+=` id: ${e.id} group-id: "${e.groupId}"`);let a=new Error(o);S.warn(`[playlist-loader]: ${o}`);let l=b.UNKNOWN,c=!1,h=this.getInternalLoader(e);switch(e.type){case j.MANIFEST:l=i?b.MANIFEST_LOAD_TIMEOUT:b.MANIFEST_LOAD_ERROR,c=!0;break;case j.LEVEL:l=i?b.LEVEL_LOAD_TIMEOUT:b.LEVEL_LOAD_ERROR,c=!1;break;case j.AUDIO_TRACK:l=i?b.AUDIO_TRACK_LOAD_TIMEOUT:b.AUDIO_TRACK_LOAD_ERROR,c=!1;break;case j.SUBTITLE_TRACK:l=i?b.SUBTITLE_TRACK_LOAD_TIMEOUT:b.SUBTITLE_LOAD_ERROR,c=!1;break}h&&this.resetInternalLoader(e.type);let u={type:G.NETWORK_ERROR,details:l,fatal:c,url:e.url,loader:h,context:e,error:a,networkDetails:t,stats:n};if(r){let d=(t==null?void 0:t.url)||e.url;u.response=he({url:d,data:void 0},r)}this.hls.trigger(p.ERROR,u)}handlePlaylistLoaded(e,t,i,r,n,o){let a=this.hls,{type:l,level:c,id:h,groupId:u,deliveryDirectives:d}=r,f=ui(t,r),g=dr(r),m=typeof r.level=="number"&&g===$.MAIN?c:void 0;if(!e.fragments.length){let T=new Error("No Segments found in Playlist");a.trigger(p.ERROR,{type:G.NETWORK_ERROR,details:b.LEVEL_EMPTY_ERROR,fatal:!1,url:f,error:T,reason:T.message,response:t,context:r,level:m,parent:g,networkDetails:n,stats:i});return}e.targetduration||(e.playlistParsingError=new Error("Missing Target Duration"));let y=e.playlistParsingError;if(y){a.trigger(p.ERROR,{type:G.NETWORK_ERROR,details:b.LEVEL_PARSING_ERROR,fatal:!1,url:f,error:y,reason:y.message,response:t,context:r,level:m,parent:g,networkDetails:n,stats:i});return}switch(e.live&&o&&(o.getCacheAge&&(e.ageHeader=o.getCacheAge()||0),(!o.getCacheAge||isNaN(e.ageHeader))&&(e.ageHeader=0)),l){case j.MANIFEST:case j.LEVEL:a.trigger(p.LEVEL_LOADED,{details:e,level:m||0,id:h||0,stats:i,networkDetails:n,deliveryDirectives:d});break;case j.AUDIO_TRACK:a.trigger(p.AUDIO_TRACK_LOADED,{details:e,id:h||0,groupId:u||"",stats:i,networkDetails:n,deliveryDirectives:d});break;case j.SUBTITLE_TRACK:a.trigger(p.SUBTITLE_TRACK_LOADED,{details:e,id:h||0,groupId:u||"",stats:i,networkDetails:n,deliveryDirectives:d});break}}};function on(s,e){let t;try{t=new Event("addtrack")}catch(i){t=document.createEvent("Event"),t.initEvent("addtrack",!1,!1)}t.track=s,e.dispatchEvent(t)}function an(s,e){let t=s.mode;if(t==="disabled"&&(s.mode="hidden"),s.cues&&!s.cues.getCueById(e.id))try{if(s.addCue(e),!s.cues.getCueById(e.id))throw new Error(`addCue is failed for: ${e}`)}catch(i){S.debug(`[texttrack-utils]: ${i}`);try{let r=new self.TextTrackCue(e.startTime,e.endTime,e.text);r.id=e.id,s.addCue(r)}catch(r){S.debug(`[texttrack-utils]: Legacy TextTrackCue fallback failed: ${r}`)}}t==="disabled"&&(s.mode=t)}function We(s){let e=s.mode;if(e==="disabled"&&(s.mode="hidden"),s.cues)for(let t=s.cues.length;t--;)s.removeCue(s.cues[t]);e==="disabled"&&(s.mode=e)}function Ci(s,e,t,i){let r=s.mode;if(r==="disabled"&&(s.mode="hidden"),s.cues&&s.cues.length>0){let n=ta(s.cues,e,t);for(let o=0;o<n.length;o++)(!i||i(n[o]))&&s.removeCue(n[o])}r==="disabled"&&(s.mode=r)}function ea(s,e){if(e<s[0].startTime)return 0;let t=s.length-1;if(e>s[t].endTime)return-1;let i=0,r=t;for(;i<=r;){let n=Math.floor((r+i)/2);if(e<s[n].startTime)r=n-1;else if(e>s[n].startTime&&i<t)i=n+1;else return n}return s[i].startTime-e<e-s[r].startTime?i:r}function ta(s,e,t){let i=[],r=ea(s,e);if(r>-1)for(let n=r,o=s.length;n<o;n++){let a=s[n];if(a.startTime>=e&&a.endTime<=t)i.push(a);else if(a.startTime>t)return i}return i}function bt(s){let e=[];for(let t=0;t<s.length;t++){let i=s[t];(i.kind==="subtitles"||i.kind==="captions")&&i.label&&e.push(s[t])}return e}var Se={audioId3:"org.id3",dateRange:"com.apple.quicktime.HLS",emsg:"https://aomedia.org/emsg/ID3"},ia=.25;function Di(){if(typeof self!="undefined")return self.VTTCue||self.TextTrackCue}function fr(s,e,t,i,r){let n=new s(e,t,"");try{n.value=i,r&&(n.type=r)}catch(o){n=new s(e,t,JSON.stringify(r?he({type:r},i):i))}return n}var mt=(()=>{let s=Di();try{s&&new s(0,Number.POSITIVE_INFINITY,"")}catch(e){return Number.MAX_VALUE}return Number.POSITIVE_INFINITY})();function di(s,e){return s.getTime()/1e3-e}function sa(s){return Uint8Array.from(s.replace(/^0x/,"").replace(/([\da-fA-F]{2}) ?/g,"0x$1 ").replace(/ +$/,"").split(" ")).buffer}var ki=class{constructor(e){this.hls=void 0,this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=e,this._registerListeners()}destroy(){this._unregisterListeners(),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={},this.hls=null}_registerListeners(){let{hls:e}=this;e.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),e.on(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(p.LEVEL_UPDATED,this.onLevelUpdated,this)}_unregisterListeners(){let{hls:e}=this;e.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.FRAG_PARSING_METADATA,this.onFragParsingMetadata,this),e.off(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(p.LEVEL_UPDATED,this.onLevelUpdated,this)}onMediaAttached(e,t){this.media=t.media}onMediaDetaching(){this.id3Track&&(We(this.id3Track),this.id3Track=null,this.media=null,this.dateRangeCuesAppended={})}onManifestLoading(){this.dateRangeCuesAppended={}}createTrack(e){let t=this.getID3Track(e.textTracks);return t.mode="hidden",t}getID3Track(e){if(this.media){for(let t=0;t<e.length;t++){let i=e[t];if(i.kind==="metadata"&&i.label==="id3")return on(i,this.media),i}return this.media.addTextTrack("metadata","id3")}}onFragParsingMetadata(e,t){if(!this.media)return;let{hls:{config:{enableEmsgMetadataCues:i,enableID3MetadataCues:r}}}=this;if(!i&&!r)return;let{samples:n}=t;this.id3Track||(this.id3Track=this.createTrack(this.media));let o=Di();if(o)for(let a=0;a<n.length;a++){let l=n[a].type;if(l===Se.emsg&&!i||!r)continue;let c=zr(n[a].data);if(c){let h=n[a].pts,u=h+n[a].duration;u>mt&&(u=mt),u-h<=0&&(u=h+ia);for(let f=0;f<c.length;f++){let g=c[f];if(!jr(g)){this.updateId3CueEnds(h,l);let m=fr(o,h,u,g,l);m&&this.id3Track.addCue(m)}}}}}updateId3CueEnds(e,t){var i;let r=(i=this.id3Track)==null?void 0:i.cues;if(r)for(let n=r.length;n--;){let o=r[n];o.type===t&&o.startTime<e&&o.endTime===mt&&(o.endTime=e)}}onBufferFlushing(e,{startOffset:t,endOffset:i,type:r}){let{id3Track:n,hls:o}=this;if(!o)return;let{config:{enableEmsgMetadataCues:a,enableID3MetadataCues:l}}=o;if(n&&(a||l)){let c;r==="audio"?c=h=>h.type===Se.audioId3&&l:r==="video"?c=h=>h.type===Se.emsg&&a:c=h=>h.type===Se.audioId3&&l||h.type===Se.emsg&&a,Ci(n,t,i,c)}}onLevelUpdated(e,{details:t}){if(!this.media||!t.hasProgramDateTime||!this.hls.config.enableDateRangeMetadataCues)return;let{dateRangeCuesAppended:i,id3Track:r}=this,{dateRanges:n}=t,o=Object.keys(n);if(r){let h=Object.keys(i).filter(u=>!o.includes(u));for(let u=h.length;u--;){let d=h[u];Object.keys(i[d].cues).forEach(f=>{r.removeCue(i[d].cues[f])}),delete i[d]}}let a=t.fragments[t.fragments.length-1];if(o.length===0||!M(a==null?void 0:a.programDateTime))return;this.id3Track||(this.id3Track=this.createTrack(this.media));let l=a.programDateTime/1e3-a.start,c=Di();for(let h=0;h<o.length;h++){let u=o[h],d=n[u],f=di(d.startDate,l),g=i[u],m=(g==null?void 0:g.cues)||{},y=(g==null?void 0:g.durationKnown)||!1,T=mt,v=d.endDate;if(v)T=di(v,l),y=!0;else if(d.endOnNext&&!y){let _=o.reduce((x,I)=>{if(I!==d.id){let L=n[I];if(L.class===d.class&&L.startDate>d.startDate&&(!x||d.startDate<x.startDate))return L}return x},null);_&&(T=di(_.startDate,l),y=!0)}let E=Object.keys(d.attr);for(let _=0;_<E.length;_++){let x=E[_];if(!co(x))continue;let I=m[x];if(I)y&&!g.durationKnown&&(I.endTime=T);else if(c){let L=d.attr[x];ho(x)&&(L=sa(L));let C=fr(c,f,T,{key:x,data:L},Se.dateRange);C&&(C.id=u,this.id3Track.addCue(C),m[x]=C)}}i[u]={cues:m,dateRange:d,durationKnown:y}}}},Pi=class{constructor(e){this.hls=void 0,this.config=void 0,this.media=null,this.levelDetails=null,this.currentTime=0,this.stallCount=0,this._latency=null,this.timeupdateHandler=()=>this.timeupdate(),this.hls=e,this.config=e.config,this.registerListeners()}get latency(){return this._latency||0}get maxLatency(){let{config:e,levelDetails:t}=this;return e.liveMaxLatencyDuration!==void 0?e.liveMaxLatencyDuration:t?e.liveMaxLatencyDurationCount*t.targetduration:0}get targetLatency(){let{levelDetails:e}=this;if(e===null)return null;let{holdBack:t,partHoldBack:i,targetduration:r}=e,{liveSyncDuration:n,liveSyncDurationCount:o,lowLatencyMode:a}=this.config,l=this.hls.userConfig,c=a&&i||t;(l.liveSyncDuration||l.liveSyncDurationCount||c===0)&&(c=n!==void 0?n:o*r);let h=r;return c+Math.min(this.stallCount*1,h)}get liveSyncPosition(){let e=this.estimateLiveEdge(),t=this.targetLatency,i=this.levelDetails;if(e===null||t===null||i===null)return null;let r=i.edge,n=e-t-this.edgeStalled,o=r-i.totalduration,a=r-(this.config.lowLatencyMode&&i.partTarget||i.targetduration);return Math.min(Math.max(o,n),a)}get drift(){let{levelDetails:e}=this;return e===null?1:e.drift}get edgeStalled(){let{levelDetails:e}=this;if(e===null)return 0;let t=(this.config.lowLatencyMode&&e.partTarget||e.targetduration)*3;return Math.max(e.age-t,0)}get forwardBufferLength(){let{media:e,levelDetails:t}=this;if(!e||!t)return 0;let i=e.buffered.length;return(i?e.buffered.end(i-1):t.edge)-this.currentTime}destroy(){this.unregisterListeners(),this.onMediaDetaching(),this.levelDetails=null,this.hls=this.timeupdateHandler=null}registerListeners(){this.hls.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.on(p.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(p.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.on(p.ERROR,this.onError,this)}unregisterListeners(){this.hls.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),this.hls.off(p.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(p.LEVEL_UPDATED,this.onLevelUpdated,this),this.hls.off(p.ERROR,this.onError,this)}onMediaAttached(e,t){this.media=t.media,this.media.addEventListener("timeupdate",this.timeupdateHandler)}onMediaDetaching(){this.media&&(this.media.removeEventListener("timeupdate",this.timeupdateHandler),this.media=null)}onManifestLoading(){this.levelDetails=null,this._latency=null,this.stallCount=0}onLevelUpdated(e,{details:t}){this.levelDetails=t,t.advanced&&this.timeupdate(),!t.live&&this.media&&this.media.removeEventListener("timeupdate",this.timeupdateHandler)}onError(e,t){var i;t.details===b.BUFFER_STALLED_ERROR&&(this.stallCount++,(i=this.levelDetails)!=null&&i.live&&S.warn("[playback-rate-controller]: Stall detected, adjusting target latency"))}timeupdate(){let{media:e,levelDetails:t}=this;if(!e||!t)return;this.currentTime=e.currentTime;let i=this.computeLatency();if(i===null)return;this._latency=i;let{lowLatencyMode:r,maxLiveSyncPlaybackRate:n}=this.config;if(!r||n===1||!t.live)return;let o=this.targetLatency;if(o===null)return;let a=i-o,l=Math.min(this.maxLatency,o+t.targetduration);if(a<l&&a>.05&&this.forwardBufferLength>1){let h=Math.min(2,Math.max(1,n)),u=Math.round(2/(1+Math.exp(-.75*a-this.edgeStalled))*20)/20;e.playbackRate=Math.min(h,Math.max(1,u))}else e.playbackRate!==1&&e.playbackRate!==0&&(e.playbackRate=1)}estimateLiveEdge(){let{levelDetails:e}=this;return e===null?null:e.edge+e.age}computeLatency(){let e=this.estimateLiveEdge();return e===null?null:e-this.currentTime}},Oi=["NONE","TYPE-0","TYPE-1",null];function ra(s){return Oi.indexOf(s)>-1}var Ft=["SDR","PQ","HLG"];function na(s){return!!s&&Ft.indexOf(s)>-1}var At={No:"",Yes:"YES",v2:"v2"};function gr(s){let{canSkipUntil:e,canSkipDateRanges:t,age:i}=s,r=i<e/2;return e&&r?t?At.v2:At.Yes:At.No}var Nt=class{constructor(e,t,i){this.msn=void 0,this.part=void 0,this.skip=void 0,this.msn=e,this.part=t,this.skip=i}addDirectives(e){let t=new self.URL(e);return this.msn!==void 0&&t.searchParams.set("_HLS_msn",this.msn.toString()),this.part!==void 0&&t.searchParams.set("_HLS_part",this.part.toString()),this.skip&&t.searchParams.set("_HLS_skip",this.skip),t.href}},Oe=class{constructor(e){this._attrs=void 0,this.audioCodec=void 0,this.bitrate=void 0,this.codecSet=void 0,this.url=void 0,this.frameRate=void 0,this.height=void 0,this.id=void 0,this.name=void 0,this.videoCodec=void 0,this.width=void 0,this.details=void 0,this.fragmentError=0,this.loadError=0,this.loaded=void 0,this.realBitrate=0,this.supportedPromise=void 0,this.supportedResult=void 0,this._avgBitrate=0,this._audioGroups=void 0,this._subtitleGroups=void 0,this._urlId=0,this.url=[e.url],this._attrs=[e.attrs],this.bitrate=e.bitrate,e.details&&(this.details=e.details),this.id=e.id||0,this.name=e.name,this.width=e.width||0,this.height=e.height||0,this.frameRate=e.attrs.optionalFloat("FRAME-RATE",0),this._avgBitrate=e.attrs.decimalInteger("AVERAGE-BANDWIDTH"),this.audioCodec=e.audioCodec,this.videoCodec=e.videoCodec,this.codecSet=[e.videoCodec,e.audioCodec].filter(t=>!!t).map(t=>t.substring(0,4)).join(","),this.addGroupId("audio",e.attrs.AUDIO),this.addGroupId("text",e.attrs.SUBTITLES)}get maxBitrate(){return Math.max(this.realBitrate,this.bitrate)}get averageBitrate(){return this._avgBitrate||this.realBitrate||this.bitrate}get attrs(){return this._attrs[0]}get codecs(){return this.attrs.CODECS||""}get pathwayId(){return this.attrs["PATHWAY-ID"]||"."}get videoRange(){return this.attrs["VIDEO-RANGE"]||"SDR"}get score(){return this.attrs.optionalFloat("SCORE",0)}get uri(){return this.url[0]||""}hasAudioGroup(e){return pr(this._audioGroups,e)}hasSubtitleGroup(e){return pr(this._subtitleGroups,e)}get audioGroups(){return this._audioGroups}get subtitleGroups(){return this._subtitleGroups}addGroupId(e,t){if(t){if(e==="audio"){let i=this._audioGroups;i||(i=this._audioGroups=[]),i.indexOf(t)===-1&&i.push(t)}else if(e==="text"){let i=this._subtitleGroups;i||(i=this._subtitleGroups=[]),i.indexOf(t)===-1&&i.push(t)}}}get urlId(){return 0}set urlId(e){}get audioGroupIds(){return this.audioGroups?[this.audioGroupId]:void 0}get textGroupIds(){return this.subtitleGroups?[this.textGroupId]:void 0}get audioGroupId(){var e;return(e=this.audioGroups)==null?void 0:e[0]}get textGroupId(){var e;return(e=this.subtitleGroups)==null?void 0:e[0]}addFallback(){}};function pr(s,e){return!e||!s?!1:s.indexOf(e)!==-1}function fi(s,e){let t=e.startPTS;if(M(t)){let i=0,r;e.sn>s.sn?(i=t-s.start,r=s):(i=s.start-t,r=e),r.duration!==i&&(r.duration=i)}else e.sn>s.sn?s.cc===e.cc&&s.minEndPTS?e.start=s.start+(s.minEndPTS-s.start):e.start=s.start+s.duration:e.start=Math.max(s.start-e.duration,0)}function ln(s,e,t,i,r,n){i-t<=0&&(S.warn("Fragment should have a positive duration",e),i=t+e.duration,n=r+e.duration);let a=t,l=i,c=e.startPTS,h=e.endPTS;if(M(c)){let y=Math.abs(c-t);M(e.deltaPTS)?e.deltaPTS=Math.max(y,e.deltaPTS):e.deltaPTS=y,a=Math.max(t,c),t=Math.min(t,c),r=Math.min(r,e.startDTS),l=Math.min(i,h),i=Math.max(i,h),n=Math.max(n,e.endDTS)}let u=t-e.start;e.start!==0&&(e.start=t),e.duration=i-e.start,e.startPTS=t,e.maxStartPTS=a,e.startDTS=r,e.endPTS=i,e.minEndPTS=l,e.endDTS=n;let d=e.sn;if(!s||d<s.startSN||d>s.endSN)return 0;let f,g=d-s.startSN,m=s.fragments;for(m[g]=e,f=g;f>0;f--)fi(m[f],m[f-1]);for(f=g;f<m.length-1;f++)fi(m[f],m[f+1]);return s.fragmentHint&&fi(m[m.length-1],s.fragmentHint),s.PTSKnown=s.alignedSliding=!0,u}function oa(s,e){let t=null,i=s.fragments;for(let l=i.length-1;l>=0;l--){let c=i[l].initSegment;if(c){t=c;break}}s.fragmentHint&&delete s.fragmentHint.endPTS;let r=0,n;if(ca(s,e,(l,c)=>{l.relurl&&(r=l.cc-c.cc),M(l.startPTS)&&M(l.endPTS)&&(c.start=c.startPTS=l.startPTS,c.startDTS=l.startDTS,c.maxStartPTS=l.maxStartPTS,c.endPTS=l.endPTS,c.endDTS=l.endDTS,c.minEndPTS=l.minEndPTS,c.duration=l.endPTS-l.startPTS,c.duration&&(n=c),e.PTSKnown=e.alignedSliding=!0),c.elementaryStreams=l.elementaryStreams,c.loader=l.loader,c.stats=l.stats,l.initSegment&&(c.initSegment=l.initSegment,t=l.initSegment)}),t&&(e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments).forEach(c=>{var h;c&&(!c.initSegment||c.initSegment.relurl===((h=t)==null?void 0:h.relurl))&&(c.initSegment=t)}),e.skippedSegments)if(e.deltaUpdateFailed=e.fragments.some(l=>!l),e.deltaUpdateFailed){S.warn("[level-helper] Previous playlist missing segments skipped in delta playlist");for(let l=e.skippedSegments;l--;)e.fragments.shift();e.startSN=e.fragments[0].sn,e.startCC=e.fragments[0].cc}else e.canSkipDateRanges&&(e.dateRanges=aa(s.dateRanges,e.dateRanges,e.recentlyRemovedDateranges));let o=e.fragments;if(r){S.warn("discontinuity sliding from playlist, take drift into account");for(let l=0;l<o.length;l++)o[l].cc+=r}e.skippedSegments&&(e.startCC=e.fragments[0].cc),la(s.partList,e.partList,(l,c)=>{c.elementaryStreams=l.elementaryStreams,c.stats=l.stats}),n?ln(e,n,n.startPTS,n.endPTS,n.startDTS,n.endDTS):cn(s,e),o.length&&(e.totalduration=e.edge-o[0].start),e.driftStartTime=s.driftStartTime,e.driftStart=s.driftStart;let a=e.advancedDateTime;if(e.advanced&&a){let l=e.edge;e.driftStart||(e.driftStartTime=a,e.driftStart=l),e.driftEndTime=a,e.driftEnd=l}else e.driftEndTime=s.driftEndTime,e.driftEnd=s.driftEnd,e.advancedDateTime=s.advancedDateTime}function aa(s,e,t){let i=se({},s);return t&&t.forEach(r=>{delete i[r]}),Object.keys(e).forEach(r=>{let n=new Ct(e[r].attr,i[r]);n.isValid?i[r]=n:S.warn(`Ignoring invalid Playlist Delta Update DATERANGE tag: "${JSON.stringify(e[r].attr)}"`)}),i}function la(s,e,t){if(s&&e){let i=0;for(let r=0,n=s.length;r<=n;r++){let o=s[r],a=e[r+i];o&&a&&o.index===a.index&&o.fragment.sn===a.fragment.sn?t(o,a):i--}}}function ca(s,e,t){let i=e.skippedSegments,r=Math.max(s.startSN,e.startSN)-e.startSN,n=(s.fragmentHint?1:0)+(i?e.endSN:Math.min(s.endSN,e.endSN))-e.startSN,o=e.startSN-s.startSN,a=e.fragmentHint?e.fragments.concat(e.fragmentHint):e.fragments,l=s.fragmentHint?s.fragments.concat(s.fragmentHint):s.fragments;for(let c=r;c<=n;c++){let h=l[o+c],u=a[c];i&&!u&&c<i&&(u=e.fragments[c]=h),h&&u&&t(h,u)}}function cn(s,e){let t=e.startSN+e.skippedSegments-s.startSN,i=s.fragments;t<0||t>=i.length||Mi(e,i[t].start)}function Mi(s,e){if(e){let t=s.fragments;for(let i=s.skippedSegments;i<t.length;i++)t[i].start+=e;s.fragmentHint&&(s.fragmentHint.start+=e)}}function ha(s,e=1/0){let t=1e3*s.targetduration;if(s.updated){let i=s.fragments;if(i.length&&t*4>e){let n=i[i.length-1].duration*1e3;n<t&&(t=n)}}else t/=2;return Math.round(t)}function ua(s,e,t){if(!(s!=null&&s.details))return null;let i=s.details,r=i.fragments[e-i.startSN];return r||(r=i.fragmentHint,r&&r.sn===e)?r:e<i.startSN&&t&&t.sn===e?t:null}function mr(s,e,t){var i;return s!=null&&s.details?hn((i=s.details)==null?void 0:i.partList,e,t):null}function hn(s,e,t){if(s)for(let i=s.length;i--;){let r=s[i];if(r.index===t&&r.fragment.sn===e)return r}return null}function un(s){s.forEach((e,t)=>{let{details:i}=e;i!=null&&i.fragments&&i.fragments.forEach(r=>{r.level=t})})}function Bt(s){switch(s.details){case b.FRAG_LOAD_TIMEOUT:case b.KEY_LOAD_TIMEOUT:case b.LEVEL_LOAD_TIMEOUT:case b.MANIFEST_LOAD_TIMEOUT:return!0}return!1}function yr(s,e){let t=Bt(e);return s.default[`${t?"timeout":"error"}Retry`]}function Ns(s,e){let t=s.backoff==="linear"?1:Math.pow(2,e);return Math.min(t*s.retryDelayMs,s.maxRetryDelayMs)}function Tr(s){return he(he({},s),{errorRetry:null,timeoutRetry:null})}function Ut(s,e,t,i){if(!s)return!1;let r=i==null?void 0:i.code,n=e<s.maxNumRetry&&(da(r)||!!t);return s.shouldRetry?s.shouldRetry(s,e,t,i,n):n}function da(s){return s===0&&navigator.onLine===!1||!!s&&(s<400||s>499)}var dn={search:function(s,e){let t=0,i=s.length-1,r=null,n=null;for(;t<=i;){r=(t+i)/2|0,n=s[r];let o=e(n);if(o>0)t=r+1;else if(o<0)i=r-1;else return n}return null}};function fa(s,e,t){if(e===null||!Array.isArray(s)||!s.length||!M(e))return null;let i=s[0].programDateTime;if(e<(i||0))return null;let r=s[s.length-1].endProgramDateTime;if(e>=(r||0))return null;t=t||0;for(let n=0;n<s.length;++n){let o=s[n];if(ga(e,t,o))return o}return null}function $t(s,e,t=0,i=0){let r=null;if(s){r=e[s.sn-e[0].sn+1]||null;let o=s.endDTS-t;o>0&&o<15e-7&&(t+=15e-7)}else t===0&&e[0].start===0&&(r=e[0]);if(r&&(!s||s.level===r.level)&&Fi(t,i,r)===0)return r;let n=dn.search(e,Fi.bind(null,t,i));return n&&(n!==s||!r)?n:r}function Fi(s=0,e=0,t){if(t.start<=s&&t.start+t.duration>s)return 0;let i=Math.min(e,t.duration+(t.deltaPTS?t.deltaPTS:0));return t.start+t.duration-i<=s?1:t.start-i>s&&t.start?-1:0}function ga(s,e,t){let i=Math.min(e,t.duration+(t.deltaPTS?t.deltaPTS:0))*1e3;return(t.endProgramDateTime||0)-i>s}function pa(s,e){return dn.search(s,t=>t.cc<e?1:t.cc>e?-1:0)}var le={DoNothing:0,SendEndCallback:1,SendAlternateToPenaltyBox:2,RemoveAlternatePermanently:3,InsertDiscontinuity:4,RetryRequest:5},Ee={None:0,MoveAllAlternatesMatchingHost:1,MoveAllAlternatesMatchingHDCP:2,SwitchToSDR:4},Ni=class{constructor(e){this.hls=void 0,this.playlistError=0,this.penalizedRenditions={},this.log=void 0,this.warn=void 0,this.error=void 0,this.hls=e,this.log=S.log.bind(S,"[info]:"),this.warn=S.warn.bind(S,"[warning]:"),this.error=S.error.bind(S,"[error]:"),this.registerListeners()}registerListeners(){let e=this.hls;e.on(p.ERROR,this.onError,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.LEVEL_UPDATED,this.onLevelUpdated,this)}unregisterListeners(){let e=this.hls;e&&(e.off(p.ERROR,this.onError,this),e.off(p.ERROR,this.onErrorOut,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.LEVEL_UPDATED,this.onLevelUpdated,this))}destroy(){this.unregisterListeners(),this.hls=null,this.penalizedRenditions={}}startLoad(e){}stopLoad(){this.playlistError=0}getVariantLevelIndex(e){return(e==null?void 0:e.type)===$.MAIN?e.level:this.hls.loadLevel}onManifestLoading(){this.playlistError=0,this.penalizedRenditions={}}onLevelUpdated(){this.playlistError=0}onError(e,t){var i,r;if(t.fatal)return;let n=this.hls,o=t.context;switch(t.details){case b.FRAG_LOAD_ERROR:case b.FRAG_LOAD_TIMEOUT:case b.KEY_LOAD_ERROR:case b.KEY_LOAD_TIMEOUT:t.errorAction=this.getFragRetryOrSwitchAction(t);return;case b.FRAG_PARSING_ERROR:if((i=t.frag)!=null&&i.gap){t.errorAction={action:le.DoNothing,flags:Ee.None};return}case b.FRAG_GAP:case b.FRAG_DECRYPT_ERROR:{t.errorAction=this.getFragRetryOrSwitchAction(t),t.errorAction.action=le.SendAlternateToPenaltyBox;return}case b.LEVEL_EMPTY_ERROR:case b.LEVEL_PARSING_ERROR:{var a,l;let c=t.parent===$.MAIN?t.level:n.loadLevel;t.details===b.LEVEL_EMPTY_ERROR&&((a=t.context)!=null&&(l=a.levelDetails)!=null&&l.live)?t.errorAction=this.getPlaylistRetryOrSwitchAction(t,c):(t.levelRetry=!1,t.errorAction=this.getLevelSwitchAction(t,c))}return;case b.LEVEL_LOAD_ERROR:case b.LEVEL_LOAD_TIMEOUT:typeof(o==null?void 0:o.level)=="number"&&(t.errorAction=this.getPlaylistRetryOrSwitchAction(t,o.level));return;case b.AUDIO_TRACK_LOAD_ERROR:case b.AUDIO_TRACK_LOAD_TIMEOUT:case b.SUBTITLE_LOAD_ERROR:case b.SUBTITLE_TRACK_LOAD_TIMEOUT:if(o){let c=n.levels[n.loadLevel];if(c&&(o.type===j.AUDIO_TRACK&&c.hasAudioGroup(o.groupId)||o.type===j.SUBTITLE_TRACK&&c.hasSubtitleGroup(o.groupId))){t.errorAction=this.getPlaylistRetryOrSwitchAction(t,n.loadLevel),t.errorAction.action=le.SendAlternateToPenaltyBox,t.errorAction.flags=Ee.MoveAllAlternatesMatchingHost;return}}return;case b.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED:{let c=n.levels[n.loadLevel],h=c==null?void 0:c.attrs["HDCP-LEVEL"];h?t.errorAction={action:le.SendAlternateToPenaltyBox,flags:Ee.MoveAllAlternatesMatchingHDCP,hdcpLevel:h}:this.keySystemError(t)}return;case b.BUFFER_ADD_CODEC_ERROR:case b.REMUX_ALLOC_ERROR:case b.BUFFER_APPEND_ERROR:t.errorAction=this.getLevelSwitchAction(t,(r=t.level)!=null?r:n.loadLevel);return;case b.INTERNAL_EXCEPTION:case b.BUFFER_APPENDING_ERROR:case b.BUFFER_FULL_ERROR:case b.LEVEL_SWITCH_ERROR:case b.BUFFER_STALLED_ERROR:case b.BUFFER_SEEK_OVER_HOLE:case b.BUFFER_NUDGE_ON_STALL:t.errorAction={action:le.DoNothing,flags:Ee.None};return}t.type===G.KEY_SYSTEM_ERROR&&this.keySystemError(t)}keySystemError(e){let t=this.getVariantLevelIndex(e.frag);e.levelRetry=!1,e.errorAction=this.getLevelSwitchAction(e,t)}getPlaylistRetryOrSwitchAction(e,t){let i=this.hls,r=yr(i.config.playlistLoadPolicy,e),n=this.playlistError++;if(Ut(r,n,Bt(e),e.response))return{action:le.RetryRequest,flags:Ee.None,retryConfig:r,retryCount:n};let a=this.getLevelSwitchAction(e,t);return r&&(a.retryConfig=r,a.retryCount=n),a}getFragRetryOrSwitchAction(e){let t=this.hls,i=this.getVariantLevelIndex(e.frag),r=t.levels[i],{fragLoadPolicy:n,keyLoadPolicy:o}=t.config,a=yr(e.details.startsWith("key")?o:n,e),l=t.levels.reduce((h,u)=>h+u.fragmentError,0);if(r&&(e.details!==b.FRAG_GAP&&r.fragmentError++,Ut(a,l,Bt(e),e.response)))return{action:le.RetryRequest,flags:Ee.None,retryConfig:a,retryCount:l};let c=this.getLevelSwitchAction(e,i);return a&&(c.retryConfig=a,c.retryCount=l),c}getLevelSwitchAction(e,t){let i=this.hls;t==null&&(t=i.loadLevel);let r=this.hls.levels[t];if(r){var n,o;let c=e.details;r.loadError++,c===b.BUFFER_APPEND_ERROR&&r.fragmentError++;let h=-1,{levels:u,loadLevel:d,minAutoLevel:f,maxAutoLevel:g}=i;i.autoLevelEnabled||(i.loadLevel=-1);let m=(n=e.frag)==null?void 0:n.type,T=(m===$.AUDIO&&c===b.FRAG_PARSING_ERROR||e.sourceBufferName==="audio"&&(c===b.BUFFER_ADD_CODEC_ERROR||c===b.BUFFER_APPEND_ERROR))&&u.some(({audioCodec:I})=>r.audioCodec!==I),E=e.sourceBufferName==="video"&&(c===b.BUFFER_ADD_CODEC_ERROR||c===b.BUFFER_APPEND_ERROR)&&u.some(({codecSet:I,audioCodec:L})=>r.codecSet!==I&&r.audioCodec===L),{type:_,groupId:x}=(o=e.context)!=null?o:{};for(let I=u.length;I--;){let L=(I+d)%u.length;if(L!==d&&L>=f&&L<=g&&u[L].loadError===0){var a,l;let C=u[L];if(c===b.FRAG_GAP&&e.frag){let k=u[L].details;if(k){let R=$t(e.frag,k.fragments,e.frag.start);if(R!=null&&R.gap)continue}}else{if(_===j.AUDIO_TRACK&&C.hasAudioGroup(x)||_===j.SUBTITLE_TRACK&&C.hasSubtitleGroup(x))continue;if(m===$.AUDIO&&(a=r.audioGroups)!=null&&a.some(k=>C.hasAudioGroup(k))||m===$.SUBTITLE&&(l=r.subtitleGroups)!=null&&l.some(k=>C.hasSubtitleGroup(k))||T&&r.audioCodec===C.audioCodec||!T&&r.audioCodec!==C.audioCodec||E&&r.codecSet===C.codecSet)continue}h=L;break}}if(h>-1&&i.loadLevel!==h)return e.levelRetry=!0,this.playlistError=0,{action:le.SendAlternateToPenaltyBox,flags:Ee.None,nextAutoLevel:h}}return{action:le.SendAlternateToPenaltyBox,flags:Ee.MoveAllAlternatesMatchingHost}}onErrorOut(e,t){var i;switch((i=t.errorAction)==null?void 0:i.action){case le.DoNothing:break;case le.SendAlternateToPenaltyBox:this.sendAlternateToPenaltyBox(t),!t.errorAction.resolved&&t.details!==b.FRAG_GAP?t.fatal=!0:/MediaSource readyState: ended/.test(t.error.message)&&(this.warn(`MediaSource ended after "${t.sourceBufferName}" sourceBuffer append error. Attempting to recover from media error.`),this.hls.recoverMediaError());break;case le.RetryRequest:break}if(t.fatal){this.hls.stopLoad();return}}sendAlternateToPenaltyBox(e){let t=this.hls,i=e.errorAction;if(!i)return;let{flags:r,hdcpLevel:n,nextAutoLevel:o}=i;switch(r){case Ee.None:this.switchLevel(e,o);break;case Ee.MoveAllAlternatesMatchingHDCP:n&&(t.maxHdcpLevel=Oi[Oi.indexOf(n)-1],i.resolved=!0),this.warn(`Restricting playback to HDCP-LEVEL of "${t.maxHdcpLevel}" or lower`);break}i.resolved||this.switchLevel(e,o)}switchLevel(e,t){t!==void 0&&e.errorAction&&(this.warn(`switching to level ${t} after ${e.details}`),this.hls.nextAutoLevel=t,e.errorAction.resolved=!0,this.hls.nextLoadLevel=this.hls.nextAutoLevel)}},lt=class{constructor(e,t){this.hls=void 0,this.timer=-1,this.requestScheduled=-1,this.canLoad=!1,this.log=void 0,this.warn=void 0,this.log=S.log.bind(S,`${t}:`),this.warn=S.warn.bind(S,`${t}:`),this.hls=e}destroy(){this.clearTimer(),this.hls=this.log=this.warn=null}clearTimer(){this.timer!==-1&&(self.clearTimeout(this.timer),this.timer=-1)}startLoad(){this.canLoad=!0,this.requestScheduled=-1,this.loadPlaylist()}stopLoad(){this.canLoad=!1,this.clearTimer()}switchParams(e,t,i){let r=t==null?void 0:t.renditionReports;if(r){let n=-1;for(let o=0;o<r.length;o++){let a=r[o],l;try{l=new self.URL(a.URI,t.url).href}catch(c){S.warn(`Could not construct new URL for Rendition Report: ${c}`),l=a.URI||""}if(l===e){n=o;break}else l===e.substring(0,l.length)&&(n=o)}if(n!==-1){let o=r[n],a=parseInt(o["LAST-MSN"])||(t==null?void 0:t.lastPartSn),l=parseInt(o["LAST-PART"])||(t==null?void 0:t.lastPartIndex);if(this.hls.config.lowLatencyMode){let h=Math.min(t.age-t.partTarget,t.targetduration);l>=0&&h>t.partTarget&&(l+=1)}let c=i&&gr(i);return new Nt(a,l>=0?l:void 0,c)}}}loadPlaylist(e){this.requestScheduled===-1&&(this.requestScheduled=self.performance.now())}shouldLoadPlaylist(e){return this.canLoad&&!!e&&!!e.url&&(!e.details||e.details.live)}shouldReloadPlaylist(e){return this.timer===-1&&this.requestScheduled===-1&&this.shouldLoadPlaylist(e)}playlistLoaded(e,t,i){let{details:r,stats:n}=t,o=self.performance.now(),a=n.loading.first?Math.max(0,o-n.loading.first):0;if(r.advancedDateTime=Date.now()-a,r.live||i!=null&&i.live){if(r.reloaded(i),i&&this.log(`live playlist ${e} ${r.advanced?"REFRESHED "+r.lastPartSn+"-"+r.lastPartIndex:r.updated?"UPDATED":"MISSED"}`),i&&r.fragments.length>0&&oa(i,r),!this.canLoad||!r.live)return;let l,c,h;if(r.canBlockReload&&r.endSN&&r.advanced){let y=this.hls.config.lowLatencyMode,T=r.lastPartSn,v=r.endSN,E=r.lastPartIndex,_=E!==-1,x=T===v,I=y?0:E;_?(c=x?v+1:T,h=x?I:E+1):c=v+1;let L=r.age,C=L+r.ageHeader,k=Math.min(C-r.partTarget,r.targetduration*1.5);if(k>0){if(i&&k>i.tuneInGoal)this.warn(`CDN Tune-in goal increased from: ${i.tuneInGoal} to: ${k} with playlist age: ${r.age}`),k=0;else{let R=Math.floor(k/r.targetduration);if(c+=R,h!==void 0){let D=Math.round(k%r.targetduration/r.partTarget);h+=D}this.log(`CDN Tune-in age: ${r.ageHeader}s last advanced ${L.toFixed(2)}s goal: ${k} skip sn ${R} to part ${h}`)}r.tuneInGoal=k}if(l=this.getDeliveryDirectives(r,t.deliveryDirectives,c,h),y||!x){this.loadPlaylist(l);return}}else(r.canBlockReload||r.canSkipUntil)&&(l=this.getDeliveryDirectives(r,t.deliveryDirectives,c,h));let u=this.hls.mainForwardBufferInfo,d=u?u.end-u.len:0,f=(r.edge-d)*1e3,g=ha(r,f);r.updated&&o>this.requestScheduled+g&&(this.requestScheduled=n.loading.start),c!==void 0&&r.canBlockReload?this.requestScheduled=n.loading.first+g-(r.partTarget*1e3||1e3):this.requestScheduled===-1||this.requestScheduled+g<o?this.requestScheduled=o:this.requestScheduled-o<=0&&(this.requestScheduled+=g);let m=this.requestScheduled-o;m=Math.max(0,m),this.log(`reload live playlist ${e} in ${Math.round(m)} ms`),this.timer=self.setTimeout(()=>this.loadPlaylist(l),m)}else this.clearTimer()}getDeliveryDirectives(e,t,i,r){let n=gr(e);return t!=null&&t.skip&&e.deltaUpdateFailed&&(i=t.msn,r=t.part,n=At.No),new Nt(i,r,n)}checkRetry(e){let t=e.details,i=Bt(e),r=e.errorAction,{action:n,retryCount:o=0,retryConfig:a}=r||{},l=!!r&&!!a&&(n===le.RetryRequest||!r.resolved&&n===le.SendAlternateToPenaltyBox);if(l){var c;if(this.requestScheduled=-1,o>=a.maxNumRetry)return!1;if(i&&(c=e.context)!=null&&c.deliveryDirectives)this.warn(`Retrying playlist loading ${o+1}/${a.maxNumRetry} after "${t}" without delivery-directives`),this.loadPlaylist();else{let h=Ns(a,o);this.timer=self.setTimeout(()=>this.loadPlaylist(),h),this.warn(`Retrying playlist loading ${o+1}/${a.maxNumRetry} after "${t}" in ${h}ms`)}e.levelRetry=!0,r.resolved=!0}return l}},Pe=class{constructor(e,t=0,i=0){this.halfLife=void 0,this.alpha_=void 0,this.estimate_=void 0,this.totalWeight_=void 0,this.halfLife=e,this.alpha_=e?Math.exp(Math.log(.5)/e):0,this.estimate_=t,this.totalWeight_=i}sample(e,t){let i=Math.pow(this.alpha_,e);this.estimate_=t*(1-i)+i*this.estimate_,this.totalWeight_+=e}getTotalWeight(){return this.totalWeight_}getEstimate(){if(this.alpha_){let e=1-Math.pow(this.alpha_,this.totalWeight_);if(e)return this.estimate_/e}return this.estimate_}},Bi=class{constructor(e,t,i,r=100){this.defaultEstimate_=void 0,this.minWeight_=void 0,this.minDelayMs_=void 0,this.slow_=void 0,this.fast_=void 0,this.defaultTTFB_=void 0,this.ttfb_=void 0,this.defaultEstimate_=i,this.minWeight_=.001,this.minDelayMs_=50,this.slow_=new Pe(e),this.fast_=new Pe(t),this.defaultTTFB_=r,this.ttfb_=new Pe(e)}update(e,t){let{slow_:i,fast_:r,ttfb_:n}=this;i.halfLife!==e&&(this.slow_=new Pe(e,i.getEstimate(),i.getTotalWeight())),r.halfLife!==t&&(this.fast_=new Pe(t,r.getEstimate(),r.getTotalWeight())),n.halfLife!==e&&(this.ttfb_=new Pe(e,n.getEstimate(),n.getTotalWeight()))}sample(e,t){e=Math.max(e,this.minDelayMs_);let i=8*t,r=e/1e3,n=i/r;this.fast_.sample(r,n),this.slow_.sample(r,n)}sampleTTFB(e){let t=e/1e3,i=Math.sqrt(2)*Math.exp(-Math.pow(t,2)/2);this.ttfb_.sample(i,Math.max(e,5))}canEstimate(){return this.fast_.getTotalWeight()>=this.minWeight_}getEstimate(){return this.canEstimate()?Math.min(this.fast_.getEstimate(),this.slow_.getEstimate()):this.defaultEstimate_}getEstimateTTFB(){return this.ttfb_.getTotalWeight()>=this.minWeight_?this.ttfb_.getEstimate():this.defaultTTFB_}destroy(){}},fn={supported:!0,configurations:[],decodingInfoResults:[{supported:!0,powerEfficient:!0,smooth:!0}]},Er={};function ma(s,e,t,i,r,n){let o=s.audioCodec?s.audioGroups:null,a=n==null?void 0:n.audioCodec,l=n==null?void 0:n.channels,c=l?parseInt(l):a?1/0:2,h=null;if(o!=null&&o.length)try{o.length===1&&o[0]?h=e.groups[o[0]].channels:h=o.reduce((u,d)=>{if(d){let f=e.groups[d];if(!f)throw new Error(`Audio track group ${d} not found`);Object.keys(f.channels).forEach(g=>{u[g]=(u[g]||0)+f.channels[g]})}return u},{2:0})}catch(u){return!0}return s.videoCodec!==void 0&&(s.width>1920&&s.height>1088||s.height>1920&&s.width>1088||s.frameRate>Math.max(i,30)||s.videoRange!=="SDR"&&s.videoRange!==t||s.bitrate>Math.max(r,8e6))||!!h&&M(c)&&Object.keys(h).some(u=>parseInt(u)>c)}function ya(s,e,t){let i=s.videoCodec,r=s.audioCodec;if(!i||!r||!t)return Promise.resolve(fn);let n={width:s.width,height:s.height,bitrate:Math.ceil(Math.max(s.bitrate*.9,s.averageBitrate)),framerate:s.frameRate||30},o=s.videoRange;o!=="SDR"&&(n.transferFunction=o.toLowerCase());let a=i.split(",").map(l=>({type:"media-source",video:he(he({},n),{},{contentType:at(l,"video")})}));return r&&s.audioGroups&&s.audioGroups.forEach(l=>{var c;l&&((c=e.groups[l])==null||c.tracks.forEach(h=>{if(h.groupId===l){let u=h.channels||"",d=parseFloat(u);M(d)&&d>2&&a.push.apply(a,r.split(",").map(f=>({type:"media-source",audio:{contentType:at(f,"audio"),channels:""+d}})))}}))}),Promise.all(a.map(l=>{let c=Ta(l);return Er[c]||(Er[c]=t.decodingInfo(l))})).then(l=>({supported:!l.some(c=>!c.supported),configurations:a,decodingInfoResults:l})).catch(l=>({supported:!1,configurations:a,decodingInfoResults:[],error:l}))}function Ta(s){let{audio:e,video:t}=s,i=t||e;if(i){let r=i.contentType.split('"')[1];if(t)return`r${t.height}x${t.width}f${Math.ceil(t.framerate)}${t.transferFunction||"sd"}_${r}_${Math.ceil(t.bitrate/1e5)}`;if(e)return`c${e.channels}${e.spatialRendering?"s":"n"}_${r}`}return""}function Ea(){if(typeof matchMedia=="function"){let s=matchMedia("(dynamic-range: high)"),e=matchMedia("bad query");if(s.media!==e.media)return s.matches===!0}return!1}function va(s,e){let t=!1,i=[];return s&&(t=s!=="SDR",i=[s]),e&&(i=e.allowedVideoRanges||Ft.slice(0),t=e.preferHDR!==void 0?e.preferHDR:Ea(),t?i=i.filter(r=>r!=="SDR"):i=["SDR"]),{preferHDR:t,allowedVideoRanges:i}}function xa(s,e,t,i,r){let n=Object.keys(s),o=i==null?void 0:i.channels,a=i==null?void 0:i.audioCodec,l=o&&parseInt(o)===2,c=!0,h=!1,u=1/0,d=1/0,f=1/0,g=0,m=[],{preferHDR:y,allowedVideoRanges:T}=va(e,r);for(let x=n.length;x--;){let I=s[n[x]];c=I.channels[2]>0,u=Math.min(u,I.minHeight),d=Math.min(d,I.minFramerate),f=Math.min(f,I.minBitrate);let L=T.filter(C=>I.videoRanges[C]>0);L.length>0&&(h=!0,m=L)}u=M(u)?u:0,d=M(d)?d:0;let v=Math.max(1080,u),E=Math.max(30,d);return f=M(f)?f:t,t=Math.max(f,t),h||(e=void 0,m=[]),{codecSet:n.reduce((x,I)=>{let L=s[I];if(I===x)return x;if(L.minBitrate>t)return we(I,`min bitrate of ${L.minBitrate} > current estimate of ${t}`),x;if(!L.hasDefaultAudio)return we(I,"no renditions with default or auto-select sound found"),x;if(a&&I.indexOf(a.substring(0,4))%5!==0)return we(I,`audio codec preference "${a}" not found`),x;if(o&&!l){if(!L.channels[o])return we(I,`no renditions with ${o} channel sound found (channels options: ${Object.keys(L.channels)})`),x}else if((!a||l)&&c&&L.channels[2]===0)return we(I,"no renditions with stereo sound found"),x;return L.minHeight>v?(we(I,`min resolution of ${L.minHeight} > maximum of ${v}`),x):L.minFramerate>E?(we(I,`min framerate of ${L.minFramerate} > maximum of ${E}`),x):m.some(C=>L.videoRanges[C]>0)?L.maxScore<g?(we(I,`max score of ${L.maxScore} < selected max of ${g}`),x):x&&(Ot(I)>=Ot(x)||L.fragmentError>s[x].fragmentError)?x:(g=L.maxScore,I):(we(I,`no variants with VIDEO-RANGE of ${JSON.stringify(m)} found`),x)},void 0),videoRanges:m,preferHDR:y,minFramerate:d,minBitrate:f}}function we(s,e){S.log(`[abr] start candidates with "${s}" ignored because ${e}`)}function Sa(s){return s.reduce((e,t)=>{let i=e.groups[t.groupId];i||(i=e.groups[t.groupId]={tracks:[],channels:{2:0},hasDefault:!1,hasAutoSelect:!1}),i.tracks.push(t);let r=t.channels||"2";return i.channels[r]=(i.channels[r]||0)+1,i.hasDefault=i.hasDefault||t.default,i.hasAutoSelect=i.hasAutoSelect||t.autoselect,i.hasDefault&&(e.hasDefaultAudio=!0),i.hasAutoSelect&&(e.hasAutoSelectAudio=!0),e},{hasDefaultAudio:!1,hasAutoSelectAudio:!1,groups:{}})}function ba(s,e,t,i){return s.slice(t,i+1).reduce((r,n)=>{if(!n.codecSet)return r;let o=n.audioGroups,a=r[n.codecSet];a||(r[n.codecSet]=a={minBitrate:1/0,minHeight:1/0,minFramerate:1/0,maxScore:0,videoRanges:{SDR:0},channels:{2:0},hasDefaultAudio:!o,fragmentError:0}),a.minBitrate=Math.min(a.minBitrate,n.bitrate);let l=Math.min(n.height,n.width);return a.minHeight=Math.min(a.minHeight,l),a.minFramerate=Math.min(a.minFramerate,n.frameRate),a.maxScore=Math.max(a.maxScore,n.score),a.fragmentError+=n.fragmentError,a.videoRanges[n.videoRange]=(a.videoRanges[n.videoRange]||0)+1,o&&o.forEach(c=>{if(!c)return;let h=e.groups[c];h&&(a.hasDefaultAudio=a.hasDefaultAudio||e.hasDefaultAudio?h.hasDefault:h.hasAutoSelect||!e.hasDefaultAudio&&!e.hasAutoSelectAudio,Object.keys(h.channels).forEach(u=>{a.channels[u]=(a.channels[u]||0)+h.channels[u]}))}),r},{})}function _e(s,e,t){if("attrs"in s){let i=e.indexOf(s);if(i!==-1)return i}for(let i=0;i<e.length;i++){let r=e[i];if(je(s,r,t))return i}return-1}function je(s,e,t){let{groupId:i,name:r,lang:n,assocLang:o,characteristics:a,default:l}=s,c=s.forced;return(i===void 0||e.groupId===i)&&(r===void 0||e.name===r)&&(n===void 0||e.lang===n)&&(n===void 0||e.assocLang===o)&&(l===void 0||e.default===l)&&(c===void 0||e.forced===c)&&(a===void 0||Aa(a,e.characteristics))&&(t===void 0||t(s,e))}function Aa(s,e=""){let t=s.split(","),i=e.split(",");return t.length===i.length&&!t.some(r=>i.indexOf(r)===-1)}function Ge(s,e){let{audioCodec:t,channels:i}=s;return(t===void 0||(e.audioCodec||"").substring(0,4)===t.substring(0,4))&&(i===void 0||i===(e.channels||"2"))}function La(s,e,t,i,r){let n=e[i],a=e.reduce((d,f,g)=>{let m=f.uri;return(d[m]||(d[m]=[])).push(g),d},{})[n.uri];a.length>1&&(i=Math.max.apply(Math,a));let l=n.videoRange,c=n.frameRate,h=n.codecSet.substring(0,4),u=vr(e,i,d=>{if(d.videoRange!==l||d.frameRate!==c||d.codecSet.substring(0,4)!==h)return!1;let f=d.audioGroups,g=t.filter(m=>!f||f.indexOf(m.groupId)!==-1);return _e(s,g,r)>-1});return u>-1?u:vr(e,i,d=>{let f=d.audioGroups,g=t.filter(m=>!f||f.indexOf(m.groupId)!==-1);return _e(s,g,r)>-1})}function vr(s,e,t){for(let i=e;i;i--)if(t(s[i]))return i;for(let i=e+1;i<s.length;i++)if(t(s[i]))return i;return-1}var Ui=class{constructor(e){this.hls=void 0,this.lastLevelLoadSec=0,this.lastLoadedFragLevel=-1,this.firstSelection=-1,this._nextAutoLevel=-1,this.nextAutoLevelKey="",this.audioTracksByGroup=null,this.codecTiers=null,this.timer=-1,this.fragCurrent=null,this.partCurrent=null,this.bitrateTestDelay=0,this.bwEstimator=void 0,this._abandonRulesCheck=()=>{let{fragCurrent:t,partCurrent:i,hls:r}=this,{autoLevelEnabled:n,media:o}=r;if(!t||!o)return;let a=performance.now(),l=i?i.stats:t.stats,c=i?i.duration:t.duration,h=a-l.loading.start,u=r.minAutoLevel;if(l.aborted||l.loaded&&l.loaded===l.total||t.level<=u){this.clearTimer(),this._nextAutoLevel=-1;return}if(!n||o.paused||!o.playbackRate||!o.readyState)return;let d=r.mainForwardBufferInfo;if(d===null)return;let f=this.bwEstimator.getEstimateTTFB(),g=Math.abs(o.playbackRate);if(h<=Math.max(f,1e3*(c/(g*2))))return;let m=d.len/g,y=l.loading.first?l.loading.first-l.loading.start:-1,T=l.loaded&&y>-1,v=this.getBwEstimate(),E=r.levels,_=E[t.level],x=l.total||Math.max(l.loaded,Math.round(c*_.averageBitrate/8)),I=T?h-y:h;I<1&&T&&(I=Math.min(h,l.loaded*8/v));let L=T?l.loaded*1e3/I:0,C=L?(x-l.loaded)/L:x*8/v+f/1e3;if(C<=m)return;let k=L?L*8:v,R=Number.POSITIVE_INFINITY,D;for(D=t.level-1;D>u;D--){let P=E[D].maxBitrate;if(R=this.getTimeToLoadFrag(f/1e3,k,c*P,!E[D].details),R<m)break}if(R>=C||R>c*10)return;r.nextLoadLevel=r.nextAutoLevel=D,T?this.bwEstimator.sample(h-Math.min(f,y),l.loaded):this.bwEstimator.sampleTTFB(h);let V=E[D].maxBitrate;this.getBwEstimate()*this.hls.config.abrBandWidthUpFactor>V&&this.resetEstimator(V),this.clearTimer(),S.warn(`[abr] Fragment ${t.sn}${i?" part "+i.index:""} of level ${t.level} is loading too slowly;
      Time to underbuffer: ${m.toFixed(3)} s
      Estimated load time for current fragment: ${C.toFixed(3)} s
      Estimated load time for down switch fragment: ${R.toFixed(3)} s
      TTFB estimate: ${y|0} ms
      Current BW estimate: ${M(v)?v|0:"Unknown"} bps
      New BW estimate: ${this.getBwEstimate()|0} bps
      Switching to level ${D} @ ${V|0} bps`),r.trigger(p.FRAG_LOAD_EMERGENCY_ABORTED,{frag:t,part:i,stats:l})},this.hls=e,this.bwEstimator=this.initEstimator(),this.registerListeners()}resetEstimator(e){e&&(S.log(`setting initial bwe to ${e}`),this.hls.config.abrEwmaDefaultEstimate=e),this.firstSelection=-1,this.bwEstimator=this.initEstimator()}initEstimator(){let e=this.hls.config;return new Bi(e.abrEwmaSlowVoD,e.abrEwmaFastVoD,e.abrEwmaDefaultEstimate)}registerListeners(){let{hls:e}=this;e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.FRAG_LOADING,this.onFragLoading,this),e.on(p.FRAG_LOADED,this.onFragLoaded,this),e.on(p.FRAG_BUFFERED,this.onFragBuffered,this),e.on(p.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(p.LEVEL_LOADED,this.onLevelLoaded,this),e.on(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(p.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),e.on(p.ERROR,this.onError,this)}unregisterListeners(){let{hls:e}=this;e&&(e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.FRAG_LOADING,this.onFragLoading,this),e.off(p.FRAG_LOADED,this.onFragLoaded,this),e.off(p.FRAG_BUFFERED,this.onFragBuffered,this),e.off(p.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(p.LEVEL_LOADED,this.onLevelLoaded,this),e.off(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(p.MAX_AUTO_LEVEL_UPDATED,this.onMaxAutoLevelUpdated,this),e.off(p.ERROR,this.onError,this))}destroy(){this.unregisterListeners(),this.clearTimer(),this.hls=this._abandonRulesCheck=null,this.fragCurrent=this.partCurrent=null}onManifestLoading(e,t){this.lastLoadedFragLevel=-1,this.firstSelection=-1,this.lastLevelLoadSec=0,this.fragCurrent=this.partCurrent=null,this.onLevelsUpdated(),this.clearTimer()}onLevelsUpdated(){this.lastLoadedFragLevel>-1&&this.fragCurrent&&(this.lastLoadedFragLevel=this.fragCurrent.level),this._nextAutoLevel=-1,this.onMaxAutoLevelUpdated(),this.codecTiers=null,this.audioTracksByGroup=null}onMaxAutoLevelUpdated(){this.firstSelection=-1,this.nextAutoLevelKey=""}onFragLoading(e,t){let i=t.frag;if(!this.ignoreFragment(i)){if(!i.bitrateTest){var r;this.fragCurrent=i,this.partCurrent=(r=t.part)!=null?r:null}this.clearTimer(),this.timer=self.setInterval(this._abandonRulesCheck,100)}}onLevelSwitching(e,t){this.clearTimer()}onError(e,t){if(!t.fatal)switch(t.details){case b.BUFFER_ADD_CODEC_ERROR:case b.BUFFER_APPEND_ERROR:this.lastLoadedFragLevel=-1,this.firstSelection=-1;break;case b.FRAG_LOAD_TIMEOUT:{let i=t.frag,{fragCurrent:r,partCurrent:n}=this;if(i&&r&&i.sn===r.sn&&i.level===r.level){let o=performance.now(),a=n?n.stats:i.stats,l=o-a.loading.start,c=a.loading.first?a.loading.first-a.loading.start:-1;if(a.loaded&&c>-1){let u=this.bwEstimator.getEstimateTTFB();this.bwEstimator.sample(l-Math.min(u,c),a.loaded)}else this.bwEstimator.sampleTTFB(l)}break}}}getTimeToLoadFrag(e,t,i,r){let n=e+i/t,o=r?this.lastLevelLoadSec:0;return n+o}onLevelLoaded(e,t){let i=this.hls.config,{loading:r}=t.stats,n=r.end-r.start;M(n)&&(this.lastLevelLoadSec=n/1e3),t.details.live?this.bwEstimator.update(i.abrEwmaSlowLive,i.abrEwmaFastLive):this.bwEstimator.update(i.abrEwmaSlowVoD,i.abrEwmaFastVoD)}onFragLoaded(e,{frag:t,part:i}){let r=i?i.stats:t.stats;if(t.type===$.MAIN&&this.bwEstimator.sampleTTFB(r.loading.first-r.loading.start),!this.ignoreFragment(t)){if(this.clearTimer(),t.level===this._nextAutoLevel&&(this._nextAutoLevel=-1),this.firstSelection=-1,this.hls.config.abrMaxWithRealBitrate){let n=i?i.duration:t.duration,o=this.hls.levels[t.level],a=(o.loaded?o.loaded.bytes:0)+r.loaded,l=(o.loaded?o.loaded.duration:0)+n;o.loaded={bytes:a,duration:l},o.realBitrate=Math.round(8*a/l)}if(t.bitrateTest){let n={stats:r,frag:t,part:i,id:t.type};this.onFragBuffered(p.FRAG_BUFFERED,n),t.bitrateTest=!1}else this.lastLoadedFragLevel=t.level}}onFragBuffered(e,t){let{frag:i,part:r}=t,n=r!=null&&r.stats.loaded?r.stats:i.stats;if(n.aborted||this.ignoreFragment(i))return;let o=n.parsing.end-n.loading.start-Math.min(n.loading.first-n.loading.start,this.bwEstimator.getEstimateTTFB());this.bwEstimator.sample(o,n.loaded),n.bwEstimate=this.getBwEstimate(),i.bitrateTest?this.bitrateTestDelay=o/1e3:this.bitrateTestDelay=0}ignoreFragment(e){return e.type!==$.MAIN||e.sn==="initSegment"}clearTimer(){this.timer>-1&&(self.clearInterval(this.timer),this.timer=-1)}get firstAutoLevel(){let{maxAutoLevel:e,minAutoLevel:t}=this.hls,i=this.getBwEstimate(),r=this.hls.config.maxStarvationDelay,n=this.findBestLevel(i,t,e,0,r,1,1);if(n>-1)return n;let o=this.hls.firstLevel,a=Math.min(Math.max(o,t),e);return S.warn(`[abr] Could not find best starting auto level. Defaulting to first in playlist ${o} clamped to ${a}`),a}get forcedAutoLevel(){return this.nextAutoLevelKey?-1:this._nextAutoLevel}get nextAutoLevel(){let e=this.forcedAutoLevel,i=this.bwEstimator.canEstimate(),r=this.lastLoadedFragLevel>-1;if(e!==-1&&(!i||!r||this.nextAutoLevelKey===this.getAutoLevelKey()))return e;let n=i&&r?this.getNextABRAutoLevel():this.firstAutoLevel;if(e!==-1){let o=this.hls.levels;if(o.length>Math.max(e,n)&&o[e].loadError<=o[n].loadError)return e}return this._nextAutoLevel=n,this.nextAutoLevelKey=this.getAutoLevelKey(),n}getAutoLevelKey(){return`${this.getBwEstimate()}_${this.getStarvationDelay().toFixed(2)}`}getNextABRAutoLevel(){let{fragCurrent:e,partCurrent:t,hls:i}=this,{maxAutoLevel:r,config:n,minAutoLevel:o}=i,a=t?t.duration:e?e.duration:0,l=this.getBwEstimate(),c=this.getStarvationDelay(),h=n.abrBandWidthFactor,u=n.abrBandWidthUpFactor;if(c){let y=this.findBestLevel(l,o,r,c,0,h,u);if(y>=0)return y}let d=a?Math.min(a,n.maxStarvationDelay):n.maxStarvationDelay;if(!c){let y=this.bitrateTestDelay;y&&(d=(a?Math.min(a,n.maxLoadingDelay):n.maxLoadingDelay)-y,S.info(`[abr] bitrate test took ${Math.round(1e3*y)}ms, set first fragment max fetchDuration to ${Math.round(1e3*d)} ms`),h=u=1)}let f=this.findBestLevel(l,o,r,c,d,h,u);if(S.info(`[abr] ${c?"rebuffering expected":"buffer is empty"}, optimal quality level ${f}`),f>-1)return f;let g=i.levels[o],m=i.levels[i.loadLevel];return(g==null?void 0:g.bitrate)<(m==null?void 0:m.bitrate)?o:i.loadLevel}getStarvationDelay(){let e=this.hls,t=e.media;if(!t)return 1/0;let i=t&&t.playbackRate!==0?Math.abs(t.playbackRate):1,r=e.mainForwardBufferInfo;return(r?r.len:0)/i}getBwEstimate(){return this.bwEstimator.canEstimate()?this.bwEstimator.getEstimate():this.hls.config.abrEwmaDefaultEstimate}findBestLevel(e,t,i,r,n,o,a){var l;let c=r+n,h=this.lastLoadedFragLevel,u=h===-1?this.hls.firstLevel:h,{fragCurrent:d,partCurrent:f}=this,{levels:g,allAudioTracks:m,loadLevel:y,config:T}=this.hls;if(g.length===1)return 0;let v=g[u],E=!!(v!=null&&(l=v.details)!=null&&l.live),_=y===-1||h===-1,x,I="SDR",L=(v==null?void 0:v.frameRate)||0,{audioPreference:C,videoPreference:k}=T,R=this.audioTracksByGroup||(this.audioTracksByGroup=Sa(m));if(_){if(this.firstSelection!==-1)return this.firstSelection;let H=this.codecTiers||(this.codecTiers=ba(g,R,t,i)),N=xa(H,I,e,C,k),{codecSet:q,videoRanges:X,minFramerate:F,minBitrate:O,preferHDR:z}=N;x=q,I=z?X[X.length-1]:X[0],L=F,e=Math.max(e,O),S.log(`[abr] picked start tier ${JSON.stringify(N)}`)}else x=v==null?void 0:v.codecSet,I=v==null?void 0:v.videoRange;let D=f?f.duration:d?d.duration:0,V=this.bwEstimator.getEstimateTTFB()/1e3,P=[];for(let H=i;H>=t;H--){var K;let N=g[H],q=H>u;if(!N)continue;if(T.useMediaCapabilities&&!N.supportedResult&&!N.supportedPromise){let ie=navigator.mediaCapabilities;typeof(ie==null?void 0:ie.decodingInfo)=="function"&&ma(N,R,I,L,e,C)?(N.supportedPromise=ya(N,R,ie),N.supportedPromise.then(oe=>{if(!this.hls)return;N.supportedResult=oe;let ue=this.hls.levels,me=ue.indexOf(N);oe.error?S.warn(`[abr] MediaCapabilities decodingInfo error: "${oe.error}" for level ${me} ${JSON.stringify(oe)}`):oe.supported||(S.warn(`[abr] Unsupported MediaCapabilities decodingInfo result for level ${me} ${JSON.stringify(oe)}`),me>-1&&ue.length>1&&(S.log(`[abr] Removing unsupported level ${me}`),this.hls.removeLevel(me)))})):N.supportedResult=fn}if(x&&N.codecSet!==x||I&&N.videoRange!==I||q&&L>N.frameRate||!q&&L>0&&L<N.frameRate||N.supportedResult&&!((K=N.supportedResult.decodingInfoResults)!=null&&K[0].smooth)){P.push(H);continue}let X=N.details,F=(f?X==null?void 0:X.partTarget:X==null?void 0:X.averagetargetduration)||D,O;q?O=a*e:O=o*e;let z=D&&r>=D*2&&n===0?g[H].averageBitrate:g[H].maxBitrate,W=this.getTimeToLoadFrag(V,O,z*F,X===void 0);if(O>=z&&(H===h||N.loadError===0&&N.fragmentError===0)&&(W<=V||!M(W)||E&&!this.bitrateTestDelay||W<c)){let ie=this.forcedAutoLevel;return H!==y&&(ie===-1||ie!==y)&&(P.length&&S.trace(`[abr] Skipped level(s) ${P.join(",")} of ${i} max with CODECS and VIDEO-RANGE:"${g[P[0]].codecs}" ${g[P[0]].videoRange}; not compatible with "${v.codecs}" ${I}`),S.info(`[abr] switch candidate:${u}->${H} adjustedbw(${Math.round(O)})-bitrate=${Math.round(O-z)} ttfb:${V.toFixed(1)} avgDuration:${F.toFixed(1)} maxFetchDuration:${c.toFixed(1)} fetchDuration:${W.toFixed(1)} firstSelection:${_} codecSet:${x} videoRange:${I} hls.loadLevel:${y}`)),_&&(this.firstSelection=H),H}}return-1}set nextAutoLevel(e){let{maxAutoLevel:t,minAutoLevel:i}=this.hls,r=Math.min(Math.max(e,i),t);this._nextAutoLevel!==r&&(this.nextAutoLevelKey="",this._nextAutoLevel=r)}},$i=class{constructor(){this._boundTick=void 0,this._tickTimer=null,this._tickInterval=null,this._tickCallCount=0,this._boundTick=this.tick.bind(this)}destroy(){this.onHandlerDestroying(),this.onHandlerDestroyed()}onHandlerDestroying(){this.clearNextTick(),this.clearInterval()}onHandlerDestroyed(){}hasInterval(){return!!this._tickInterval}hasNextTick(){return!!this._tickTimer}setInterval(e){return this._tickInterval?!1:(this._tickCallCount=0,this._tickInterval=self.setInterval(this._boundTick,e),!0)}clearInterval(){return this._tickInterval?(self.clearInterval(this._tickInterval),this._tickInterval=null,!0):!1}clearNextTick(){return this._tickTimer?(self.clearTimeout(this._tickTimer),this._tickTimer=null,!0):!1}tick(){this._tickCallCount++,this._tickCallCount===1&&(this.doTick(),this._tickCallCount>1&&this.tickImmediate(),this._tickCallCount=0)}tickImmediate(){this.clearNextTick(),this._tickTimer=self.setTimeout(this._boundTick,0)}doTick(){}},ce={NOT_LOADED:"NOT_LOADED",APPENDING:"APPENDING",PARTIAL:"PARTIAL",OK:"OK"},Gi=class{constructor(e){this.activePartLists=Object.create(null),this.endListFragments=Object.create(null),this.fragments=Object.create(null),this.timeRanges=Object.create(null),this.bufferPadding=.2,this.hls=void 0,this.hasGaps=!1,this.hls=e,this._registerListeners()}_registerListeners(){let{hls:e}=this;e.on(p.BUFFER_APPENDED,this.onBufferAppended,this),e.on(p.FRAG_BUFFERED,this.onFragBuffered,this),e.on(p.FRAG_LOADED,this.onFragLoaded,this)}_unregisterListeners(){let{hls:e}=this;e.off(p.BUFFER_APPENDED,this.onBufferAppended,this),e.off(p.FRAG_BUFFERED,this.onFragBuffered,this),e.off(p.FRAG_LOADED,this.onFragLoaded,this)}destroy(){this._unregisterListeners(),this.fragments=this.activePartLists=this.endListFragments=this.timeRanges=null}getAppendedFrag(e,t){let i=this.activePartLists[t];if(i)for(let r=i.length;r--;){let n=i[r];if(!n)break;let o=n.end;if(n.start<=e&&o!==null&&e<=o)return n}return this.getBufferedFrag(e,t)}getBufferedFrag(e,t){let{fragments:i}=this,r=Object.keys(i);for(let n=r.length;n--;){let o=i[r[n]];if((o==null?void 0:o.body.type)===t&&o.buffered){let a=o.body;if(a.start<=e&&e<=a.end)return a}}return null}detectEvictedFragments(e,t,i,r){this.timeRanges&&(this.timeRanges[e]=t);let n=(r==null?void 0:r.fragment.sn)||-1;Object.keys(this.fragments).forEach(o=>{let a=this.fragments[o];if(!a||n>=a.body.sn)return;if(!a.buffered&&!a.loaded){a.body.type===i&&this.removeFragment(a.body);return}let l=a.range[e];l&&l.time.some(c=>{let h=!this.isTimeBuffered(c.startPTS,c.endPTS,t);return h&&this.removeFragment(a.body),h})})}detectPartialFragments(e){let t=this.timeRanges,{frag:i,part:r}=e;if(!t||i.sn==="initSegment")return;let n=He(i),o=this.fragments[n];if(!o||o.buffered&&i.gap)return;let a=!i.relurl;Object.keys(t).forEach(l=>{let c=i.elementaryStreams[l];if(!c)return;let h=t[l],u=a||c.partial===!0;o.range[l]=this.getBufferedTimes(i,r,u,h)}),o.loaded=null,Object.keys(o.range).length?(o.buffered=!0,(o.body.endList=i.endList||o.body.endList)&&(this.endListFragments[o.body.type]=o),yt(o)||this.removeParts(i.sn-1,i.type)):this.removeFragment(o.body)}removeParts(e,t){let i=this.activePartLists[t];i&&(this.activePartLists[t]=i.filter(r=>r.fragment.sn>=e))}fragBuffered(e,t){let i=He(e),r=this.fragments[i];!r&&t&&(r=this.fragments[i]={body:e,appendedPTS:null,loaded:null,buffered:!1,range:Object.create(null)},e.gap&&(this.hasGaps=!0)),r&&(r.loaded=null,r.buffered=!0)}getBufferedTimes(e,t,i,r){let n={time:[],partial:i},o=e.start,a=e.end,l=e.minEndPTS||a,c=e.maxStartPTS||o;for(let h=0;h<r.length;h++){let u=r.start(h)-this.bufferPadding,d=r.end(h)+this.bufferPadding;if(c>=u&&l<=d){n.time.push({startPTS:Math.max(o,r.start(h)),endPTS:Math.min(a,r.end(h))});break}else if(o<d&&a>u){let f=Math.max(o,r.start(h)),g=Math.min(a,r.end(h));g>f&&(n.partial=!0,n.time.push({startPTS:f,endPTS:g}))}else if(a<=u)break}return n}getPartialFragment(e){let t=null,i,r,n,o=0,{bufferPadding:a,fragments:l}=this;return Object.keys(l).forEach(c=>{let h=l[c];h&&yt(h)&&(r=h.body.start-a,n=h.body.end+a,e>=r&&e<=n&&(i=Math.min(e-r,n-e),o<=i&&(t=h.body,o=i)))}),t}isEndListAppended(e){let t=this.endListFragments[e];return t!==void 0&&(t.buffered||yt(t))}getState(e){let t=He(e),i=this.fragments[t];return i?i.buffered?yt(i)?ce.PARTIAL:ce.OK:ce.APPENDING:ce.NOT_LOADED}isTimeBuffered(e,t,i){let r,n;for(let o=0;o<i.length;o++){if(r=i.start(o)-this.bufferPadding,n=i.end(o)+this.bufferPadding,e>=r&&t<=n)return!0;if(t<=r)return!1}return!1}onFragLoaded(e,t){let{frag:i,part:r}=t;if(i.sn==="initSegment"||i.bitrateTest)return;let n=r?null:t,o=He(i);this.fragments[o]={body:i,appendedPTS:null,loaded:n,buffered:!1,range:Object.create(null)}}onBufferAppended(e,t){let{frag:i,part:r,timeRanges:n}=t;if(i.sn==="initSegment")return;let o=i.type;if(r){let a=this.activePartLists[o];a||(this.activePartLists[o]=a=[]),a.push(r)}this.timeRanges=n,Object.keys(n).forEach(a=>{let l=n[a];this.detectEvictedFragments(a,l,o,r)})}onFragBuffered(e,t){this.detectPartialFragments(t)}hasFragment(e){let t=He(e);return!!this.fragments[t]}hasParts(e){var t;return!!((t=this.activePartLists[e])!=null&&t.length)}removeFragmentsInRange(e,t,i,r,n){r&&!this.hasGaps||Object.keys(this.fragments).forEach(o=>{let a=this.fragments[o];if(!a)return;let l=a.body;l.type!==i||r&&!l.gap||l.start<t&&l.end>e&&(a.buffered||n)&&this.removeFragment(l)})}removeFragment(e){let t=He(e);e.stats.loaded=0,e.clearElementaryStreamInfo();let i=this.activePartLists[e.type];if(i){let r=e.sn;this.activePartLists[e.type]=i.filter(n=>n.fragment.sn!==r)}delete this.fragments[t],e.endList&&delete this.endListFragments[e.type]}removeAllFragments(){this.fragments=Object.create(null),this.endListFragments=Object.create(null),this.activePartLists=Object.create(null),this.hasGaps=!1}};function yt(s){var e,t,i;return s.buffered&&(s.body.gap||((e=s.range.video)==null?void 0:e.partial)||((t=s.range.audio)==null?void 0:t.partial)||((i=s.range.audiovideo)==null?void 0:i.partial))}function He(s){return`${s.type}_${s.level}_${s.sn}`}var _a={length:0,start:()=>0,end:()=>0},Z=class s{static isBuffered(e,t){try{if(e){let i=s.getBuffered(e);for(let r=0;r<i.length;r++)if(t>=i.start(r)&&t<=i.end(r))return!0}}catch(i){}return!1}static bufferInfo(e,t,i){try{if(e){let r=s.getBuffered(e),n=[],o;for(o=0;o<r.length;o++)n.push({start:r.start(o),end:r.end(o)});return this.bufferedInfo(n,t,i)}}catch(r){}return{len:0,start:t,end:t,nextStart:void 0}}static bufferedInfo(e,t,i){t=Math.max(0,t),e.sort(function(c,h){let u=c.start-h.start;return u||h.end-c.end});let r=[];if(i)for(let c=0;c<e.length;c++){let h=r.length;if(h){let u=r[h-1].end;e[c].start-u<i?e[c].end>u&&(r[h-1].end=e[c].end):r.push(e[c])}else r.push(e[c])}else r=e;let n=0,o,a=t,l=t;for(let c=0;c<r.length;c++){let h=r[c].start,u=r[c].end;if(t+i>=h&&t<u)a=h,l=u,n=l-t;else if(t+i<h){o=h;break}}return{len:n,start:a||0,end:l||0,nextStart:o}}static getBuffered(e){try{return e.buffered}catch(t){return S.log("failed to get media.buffered",t),_a}}},ct=class{constructor(e,t,i,r=0,n=-1,o=!1){this.level=void 0,this.sn=void 0,this.part=void 0,this.id=void 0,this.size=void 0,this.partial=void 0,this.transmuxing=Tt(),this.buffering={audio:Tt(),video:Tt(),audiovideo:Tt()},this.level=e,this.sn=t,this.id=i,this.size=r,this.part=n,this.partial=o}};function Tt(){return{start:0,executeStart:0,executeEnd:0,end:0}}function Lt(s,e){for(let i=0,r=s.length;i<r;i++){var t;if(((t=s[i])==null?void 0:t.cc)===e)return s[i]}return null}function Ra(s,e,t){return!!(e&&(t.endCC>t.startCC||s&&s.cc<t.startCC))}function Ia(s,e){let t=s.fragments,i=e.fragments;if(!i.length||!t.length){S.log("No fragments to align");return}let r=Lt(t,i[0].cc);if(!r||r&&!r.startPTS){S.log("No frag in previous level to align on");return}return r}function xr(s,e){if(s){let t=s.start+e;s.start=s.startPTS=t,s.endPTS=t+s.duration}}function gn(s,e){let t=e.fragments;for(let i=0,r=t.length;i<r;i++)xr(t[i],s);e.fragmentHint&&xr(e.fragmentHint,s),e.alignedSliding=!0}function wa(s,e,t){e&&(Ca(s,t,e),!t.alignedSliding&&e&&Gt(t,e),!t.alignedSliding&&e&&!t.skippedSegments&&cn(e,t))}function Ca(s,e,t){if(Ra(s,t,e)){let i=Ia(t,e);i&&M(i.start)&&(S.log(`Adjusting PTS using last level due to CC increase within current level ${e.url}`),gn(i.start,e))}}function Gt(s,e){if(!s.hasProgramDateTime||!e.hasProgramDateTime)return;let t=s.fragments,i=e.fragments;if(!t.length||!i.length)return;let r,n,o=Math.min(e.endCC,s.endCC);e.startCC<o&&s.startCC<o&&(r=Lt(i,o),n=Lt(t,o)),(!r||!n)&&(r=i[Math.floor(i.length/2)],n=Lt(t,r.cc)||t[Math.floor(t.length/2)]);let a=r.programDateTime,l=n.programDateTime;if(!a||!l)return;let c=(l-a)/1e3-(n.start-r.start);gn(c,s)}var Sr=Math.pow(2,17),Hi=class{constructor(e){this.config=void 0,this.loader=null,this.partLoadTimeout=-1,this.config=e}destroy(){this.loader&&(this.loader.destroy(),this.loader=null)}abort(){this.loader&&this.loader.abort()}load(e,t){let i=e.url;if(!i)return Promise.reject(new xe({type:G.NETWORK_ERROR,details:b.FRAG_LOAD_ERROR,fatal:!1,frag:e,error:new Error(`Fragment does not have a ${i?"part list":"url"}`),networkDetails:null}));this.abort();let r=this.config,n=r.fLoader,o=r.loader;return new Promise((a,l)=>{if(this.loader&&this.loader.destroy(),e.gap)if(e.tagList.some(f=>f[0]==="GAP")){l(Ar(e));return}else e.gap=!1;let c=this.loader=e.loader=n?new n(r):new o(r),h=br(e),u=Tr(r.fragLoadPolicy.default),d={loadPolicy:u,timeout:u.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:e.sn==="initSegment"?1/0:Sr};e.stats=c.stats,c.load(h,d,{onSuccess:(f,g,m,y)=>{this.resetLoader(e,c);let T=f.data;m.resetIV&&e.decryptdata&&(e.decryptdata.iv=new Uint8Array(T.slice(0,16)),T=T.slice(16)),a({frag:e,part:null,payload:T,networkDetails:y})},onError:(f,g,m,y)=>{this.resetLoader(e,c),l(new xe({type:G.NETWORK_ERROR,details:b.FRAG_LOAD_ERROR,fatal:!1,frag:e,response:he({url:i,data:void 0},f),error:new Error(`HTTP Error ${f.code} ${f.text}`),networkDetails:m,stats:y}))},onAbort:(f,g,m)=>{this.resetLoader(e,c),l(new xe({type:G.NETWORK_ERROR,details:b.INTERNAL_ABORTED,fatal:!1,frag:e,error:new Error("Aborted"),networkDetails:m,stats:f}))},onTimeout:(f,g,m)=>{this.resetLoader(e,c),l(new xe({type:G.NETWORK_ERROR,details:b.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,error:new Error(`Timeout after ${d.timeout}ms`),networkDetails:m,stats:f}))},onProgress:(f,g,m,y)=>{t&&t({frag:e,part:null,payload:m,networkDetails:y})}})})}loadPart(e,t,i){this.abort();let r=this.config,n=r.fLoader,o=r.loader;return new Promise((a,l)=>{if(this.loader&&this.loader.destroy(),e.gap||t.gap){l(Ar(e,t));return}let c=this.loader=e.loader=n?new n(r):new o(r),h=br(e,t),u=Tr(r.fragLoadPolicy.default),d={loadPolicy:u,timeout:u.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0,highWaterMark:Sr};t.stats=c.stats,c.load(h,d,{onSuccess:(f,g,m,y)=>{this.resetLoader(e,c),this.updateStatsFromPart(e,t);let T={frag:e,part:t,payload:f.data,networkDetails:y};i(T),a(T)},onError:(f,g,m,y)=>{this.resetLoader(e,c),l(new xe({type:G.NETWORK_ERROR,details:b.FRAG_LOAD_ERROR,fatal:!1,frag:e,part:t,response:he({url:h.url,data:void 0},f),error:new Error(`HTTP Error ${f.code} ${f.text}`),networkDetails:m,stats:y}))},onAbort:(f,g,m)=>{e.stats.aborted=t.stats.aborted,this.resetLoader(e,c),l(new xe({type:G.NETWORK_ERROR,details:b.INTERNAL_ABORTED,fatal:!1,frag:e,part:t,error:new Error("Aborted"),networkDetails:m,stats:f}))},onTimeout:(f,g,m)=>{this.resetLoader(e,c),l(new xe({type:G.NETWORK_ERROR,details:b.FRAG_LOAD_TIMEOUT,fatal:!1,frag:e,part:t,error:new Error(`Timeout after ${d.timeout}ms`),networkDetails:m,stats:f}))}})})}updateStatsFromPart(e,t){let i=e.stats,r=t.stats,n=r.total;if(i.loaded+=r.loaded,n){let l=Math.round(e.duration/t.duration),c=Math.min(Math.round(i.loaded/n),l),u=(l-c)*Math.round(i.loaded/c);i.total=i.loaded+u}else i.total=Math.max(i.loaded,i.total);let o=i.loading,a=r.loading;o.start?o.first+=a.first-a.start:(o.start=a.start,o.first=a.first),o.end=a.end}resetLoader(e,t){e.loader=null,this.loader===t&&(self.clearTimeout(this.partLoadTimeout),this.loader=null),t.destroy()}};function br(s,e=null){let t=e||s,i={frag:s,part:e,responseType:"arraybuffer",url:t.url,headers:{},rangeStart:0,rangeEnd:0},r=t.byteRangeStartOffset,n=t.byteRangeEndOffset;if(M(r)&&M(n)){var o;let a=r,l=n;if(s.sn==="initSegment"&&((o=s.decryptdata)==null?void 0:o.method)==="AES-128"){let c=n-r;c%16&&(l=n+(16-c%16)),r!==0&&(i.resetIV=!0,a=r-16)}i.rangeStart=a,i.rangeEnd=l}return i}function Ar(s,e){let t=new Error(`GAP ${s.gap?"tag":"attribute"} found`),i={type:G.MEDIA_ERROR,details:b.FRAG_GAP,fatal:!1,frag:s,error:t,networkDetails:null};return e&&(i.part=e),(e||s).stats.aborted=!0,new xe(i)}var xe=class extends Error{constructor(e){super(e.error.message),this.data=void 0,this.data=e}},Ki=class{constructor(e,t){this.subtle=void 0,this.aesIV=void 0,this.subtle=e,this.aesIV=t}decrypt(e,t){return this.subtle.decrypt({name:"AES-CBC",iv:this.aesIV},t,e)}},Vi=class{constructor(e,t){this.subtle=void 0,this.key=void 0,this.subtle=e,this.key=t}expandKey(){return this.subtle.importKey("raw",this.key,{name:"AES-CBC"},!1,["encrypt","decrypt"])}};function Da(s){let e=s.byteLength,t=e&&new DataView(s.buffer).getUint8(e-1);return t?Ue(s,0,e-t):s}var Yi=class{constructor(){this.rcon=[0,1,2,4,8,16,32,64,128,27,54],this.subMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.invSubMix=[new Uint32Array(256),new Uint32Array(256),new Uint32Array(256),new Uint32Array(256)],this.sBox=new Uint32Array(256),this.invSBox=new Uint32Array(256),this.key=new Uint32Array(0),this.ksRows=0,this.keySize=0,this.keySchedule=void 0,this.invKeySchedule=void 0,this.initTable()}uint8ArrayToUint32Array_(e){let t=new DataView(e),i=new Uint32Array(4);for(let r=0;r<4;r++)i[r]=t.getUint32(r*4);return i}initTable(){let e=this.sBox,t=this.invSBox,i=this.subMix,r=i[0],n=i[1],o=i[2],a=i[3],l=this.invSubMix,c=l[0],h=l[1],u=l[2],d=l[3],f=new Uint32Array(256),g=0,m=0,y=0;for(y=0;y<256;y++)y<128?f[y]=y<<1:f[y]=y<<1^283;for(y=0;y<256;y++){let T=m^m<<1^m<<2^m<<3^m<<4;T=T>>>8^T&255^99,e[g]=T,t[T]=g;let v=f[g],E=f[v],_=f[E],x=f[T]*257^T*16843008;r[g]=x<<24|x>>>8,n[g]=x<<16|x>>>16,o[g]=x<<8|x>>>24,a[g]=x,x=_*16843009^E*65537^v*257^g*16843008,c[T]=x<<24|x>>>8,h[T]=x<<16|x>>>16,u[T]=x<<8|x>>>24,d[T]=x,g?(g=v^f[f[f[_^v]]],m^=f[f[m]]):g=m=1}}expandKey(e){let t=this.uint8ArrayToUint32Array_(e),i=!0,r=0;for(;r<t.length&&i;)i=t[r]===this.key[r],r++;if(i)return;this.key=t;let n=this.keySize=t.length;if(n!==4&&n!==6&&n!==8)throw new Error("Invalid aes key size="+n);let o=this.ksRows=(n+6+1)*4,a,l,c=this.keySchedule=new Uint32Array(o),h=this.invKeySchedule=new Uint32Array(o),u=this.sBox,d=this.rcon,f=this.invSubMix,g=f[0],m=f[1],y=f[2],T=f[3],v,E;for(a=0;a<o;a++){if(a<n){v=c[a]=t[a];continue}E=v,a%n===0?(E=E<<8|E>>>24,E=u[E>>>24]<<24|u[E>>>16&255]<<16|u[E>>>8&255]<<8|u[E&255],E^=d[a/n|0]<<24):n>6&&a%n===4&&(E=u[E>>>24]<<24|u[E>>>16&255]<<16|u[E>>>8&255]<<8|u[E&255]),c[a]=v=(c[a-n]^E)>>>0}for(l=0;l<o;l++)a=o-l,l&3?E=c[a]:E=c[a-4],l<4||a<=4?h[l]=E:h[l]=g[u[E>>>24]]^m[u[E>>>16&255]]^y[u[E>>>8&255]]^T[u[E&255]],h[l]=h[l]>>>0}networkToHostOrderSwap(e){return e<<24|(e&65280)<<8|(e&16711680)>>8|e>>>24}decrypt(e,t,i){let r=this.keySize+6,n=this.invKeySchedule,o=this.invSBox,a=this.invSubMix,l=a[0],c=a[1],h=a[2],u=a[3],d=this.uint8ArrayToUint32Array_(i),f=d[0],g=d[1],m=d[2],y=d[3],T=new Int32Array(e),v=new Int32Array(T.length),E,_,x,I,L,C,k,R,D,V,P,K,H,N,q=this.networkToHostOrderSwap;for(;t<T.length;){for(D=q(T[t]),V=q(T[t+1]),P=q(T[t+2]),K=q(T[t+3]),L=D^n[0],C=K^n[1],k=P^n[2],R=V^n[3],H=4,N=1;N<r;N++)E=l[L>>>24]^c[C>>16&255]^h[k>>8&255]^u[R&255]^n[H],_=l[C>>>24]^c[k>>16&255]^h[R>>8&255]^u[L&255]^n[H+1],x=l[k>>>24]^c[R>>16&255]^h[L>>8&255]^u[C&255]^n[H+2],I=l[R>>>24]^c[L>>16&255]^h[C>>8&255]^u[k&255]^n[H+3],L=E,C=_,k=x,R=I,H=H+4;E=o[L>>>24]<<24^o[C>>16&255]<<16^o[k>>8&255]<<8^o[R&255]^n[H],_=o[C>>>24]<<24^o[k>>16&255]<<16^o[R>>8&255]<<8^o[L&255]^n[H+1],x=o[k>>>24]<<24^o[R>>16&255]<<16^o[L>>8&255]<<8^o[C&255]^n[H+2],I=o[R>>>24]<<24^o[L>>16&255]<<16^o[C>>8&255]<<8^o[k&255]^n[H+3],v[t]=q(E^f),v[t+1]=q(I^g),v[t+2]=q(x^m),v[t+3]=q(_^y),f=D,g=V,m=P,y=K,t=t+4}return v.buffer}},ka=16,ht=class{constructor(e,{removePKCS7Padding:t=!0}={}){if(this.logEnabled=!0,this.removePKCS7Padding=void 0,this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null,this.useSoftware=void 0,this.useSoftware=e.enableSoftwareAES,this.removePKCS7Padding=t,t)try{let i=self.crypto;i&&(this.subtle=i.subtle||i.webkitSubtle)}catch(i){}this.subtle===null&&(this.useSoftware=!0)}destroy(){this.subtle=null,this.softwareDecrypter=null,this.key=null,this.fastAesKey=null,this.remainderData=null,this.currentIV=null,this.currentResult=null}isSync(){return this.useSoftware}flush(){let{currentResult:e,remainderData:t}=this;if(!e||t)return this.reset(),null;let i=new Uint8Array(e);return this.reset(),this.removePKCS7Padding?Da(i):i}reset(){this.currentResult=null,this.currentIV=null,this.remainderData=null,this.softwareDecrypter&&(this.softwareDecrypter=null)}decrypt(e,t,i){return this.useSoftware?new Promise((r,n)=>{this.softwareDecrypt(new Uint8Array(e),t,i);let o=this.flush();o?r(o.buffer):n(new Error("[softwareDecrypt] Failed to decrypt data"))}):this.webCryptoDecrypt(new Uint8Array(e),t,i)}softwareDecrypt(e,t,i){let{currentIV:r,currentResult:n,remainderData:o}=this;this.logOnce("JS AES decrypt"),o&&(e=Te(o,e),this.remainderData=null);let a=this.getValidChunk(e);if(!a.length)return null;r&&(i=r);let l=this.softwareDecrypter;l||(l=this.softwareDecrypter=new Yi),l.expandKey(t);let c=n;return this.currentResult=l.decrypt(a.buffer,0,i),this.currentIV=Ue(a,-16).buffer,c||null}webCryptoDecrypt(e,t,i){let r=this.subtle;return(this.key!==t||!this.fastAesKey)&&(this.key=t,this.fastAesKey=new Vi(r,t)),this.fastAesKey.expandKey().then(n=>r?(this.logOnce("WebCrypto AES decrypt"),new Ki(r,new Uint8Array(i)).decrypt(e.buffer,n)):Promise.reject(new Error("web crypto not initialized"))).catch(n=>(S.warn(`[decrypter]: WebCrypto Error, disable WebCrypto API, ${n.name}: ${n.message}`),this.onWebCryptoError(e,t,i)))}onWebCryptoError(e,t,i){this.useSoftware=!0,this.logEnabled=!0,this.softwareDecrypt(e,t,i);let r=this.flush();if(r)return r.buffer;throw new Error("WebCrypto and softwareDecrypt: failed to decrypt data")}getValidChunk(e){let t=e,i=e.length-e.length%ka;return i!==e.length&&(t=Ue(e,0,i),this.remainderData=Ue(e,i)),t}logOnce(e){this.logEnabled&&(S.log(`[decrypter]: ${e}`),this.logEnabled=!1)}},Pa={toString:function(s){let e="",t=s.length;for(let i=0;i<t;i++)e+=`[${s.start(i).toFixed(3)}-${s.end(i).toFixed(3)}]`;return e}},w={STOPPED:"STOPPED",IDLE:"IDLE",KEY_LOADING:"KEY_LOADING",FRAG_LOADING:"FRAG_LOADING",FRAG_LOADING_WAITING_RETRY:"FRAG_LOADING_WAITING_RETRY",WAITING_TRACK:"WAITING_TRACK",PARSING:"PARSING",PARSED:"PARSED",ENDED:"ENDED",ERROR:"ERROR",WAITING_INIT_PTS:"WAITING_INIT_PTS",WAITING_LEVEL:"WAITING_LEVEL"},ut=class extends $i{constructor(e,t,i,r,n){super(),this.hls=void 0,this.fragPrevious=null,this.fragCurrent=null,this.fragmentTracker=void 0,this.transmuxer=null,this._state=w.STOPPED,this.playlistType=void 0,this.media=null,this.mediaBuffer=null,this.config=void 0,this.bitrateTest=!1,this.lastCurrentTime=0,this.nextLoadPosition=0,this.startPosition=0,this.startTimeOffset=null,this.loadedmetadata=!1,this.retryDate=0,this.levels=null,this.fragmentLoader=void 0,this.keyLoader=void 0,this.levelLastLoaded=null,this.startFragRequested=!1,this.decrypter=void 0,this.initPTS=[],this.onvseeking=null,this.onvended=null,this.logPrefix="",this.log=void 0,this.warn=void 0,this.playlistType=n,this.logPrefix=r,this.log=S.log.bind(S,`${r}:`),this.warn=S.warn.bind(S,`${r}:`),this.hls=e,this.fragmentLoader=new Hi(e.config),this.keyLoader=i,this.fragmentTracker=t,this.config=e.config,this.decrypter=new ht(e.config),e.on(p.MANIFEST_LOADED,this.onManifestLoaded,this)}doTick(){this.onTickEnd()}onTickEnd(){}startLoad(e){}stopLoad(){this.fragmentLoader.abort(),this.keyLoader.abort(this.playlistType);let e=this.fragCurrent;e!=null&&e.loader&&(e.abortRequests(),this.fragmentTracker.removeFragment(e)),this.resetTransmuxer(),this.fragCurrent=null,this.fragPrevious=null,this.clearInterval(),this.clearNextTick(),this.state=w.STOPPED}_streamEnded(e,t){if(t.live||e.nextStart||!e.end||!this.media)return!1;let i=t.partList;if(i!=null&&i.length){let n=i[i.length-1];return Z.isBuffered(this.media,n.start+n.duration/2)}let r=t.fragments[t.fragments.length-1].type;return this.fragmentTracker.isEndListAppended(r)}getLevelDetails(){if(this.levels&&this.levelLastLoaded!==null){var e;return(e=this.levelLastLoaded)==null?void 0:e.details}}onMediaAttached(e,t){let i=this.media=this.mediaBuffer=t.media;this.onvseeking=this.onMediaSeeking.bind(this),this.onvended=this.onMediaEnded.bind(this),i.addEventListener("seeking",this.onvseeking),i.addEventListener("ended",this.onvended);let r=this.config;this.levels&&r.autoStartLoad&&this.state===w.STOPPED&&this.startLoad(r.startPosition)}onMediaDetaching(){let e=this.media;e!=null&&e.ended&&(this.log("MSE detaching and video ended, reset startPosition"),this.startPosition=this.lastCurrentTime=0),e&&this.onvseeking&&this.onvended&&(e.removeEventListener("seeking",this.onvseeking),e.removeEventListener("ended",this.onvended),this.onvseeking=this.onvended=null),this.keyLoader&&this.keyLoader.detach(),this.media=this.mediaBuffer=null,this.loadedmetadata=!1,this.fragmentTracker.removeAllFragments(),this.stopLoad()}onMediaSeeking(){let{config:e,fragCurrent:t,media:i,mediaBuffer:r,state:n}=this,o=i?i.currentTime:0,a=Z.bufferInfo(r||i,o,e.maxBufferHole);if(this.log(`media seeking to ${M(o)?o.toFixed(3):o}, state: ${n}`),this.state===w.ENDED)this.resetLoadingState();else if(t){let l=e.maxFragLookUpTolerance,c=t.start-l,h=t.start+t.duration+l;if(!a.len||h<a.start||c>a.end){let u=o>h;(o<c||u)&&(u&&t.loader&&(this.log("seeking outside of buffer while fragment load in progress, cancel fragment load"),t.abortRequests(),this.resetLoadingState()),this.fragPrevious=null)}}i&&(this.fragmentTracker.removeFragmentsInRange(o,1/0,this.playlistType,!0),this.lastCurrentTime=o),!this.loadedmetadata&&!a.len&&(this.nextLoadPosition=this.startPosition=o),this.tickImmediate()}onMediaEnded(){this.startPosition=this.lastCurrentTime=0}onManifestLoaded(e,t){this.startTimeOffset=t.startTimeOffset,this.initPTS=[]}onHandlerDestroying(){this.hls.off(p.MANIFEST_LOADED,this.onManifestLoaded,this),this.stopLoad(),super.onHandlerDestroying(),this.hls=null}onHandlerDestroyed(){this.state=w.STOPPED,this.fragmentLoader&&this.fragmentLoader.destroy(),this.keyLoader&&this.keyLoader.destroy(),this.decrypter&&this.decrypter.destroy(),this.hls=this.log=this.warn=this.decrypter=this.keyLoader=this.fragmentLoader=this.fragmentTracker=null,super.onHandlerDestroyed()}loadFragment(e,t,i){this._loadFragForPlayback(e,t,i)}_loadFragForPlayback(e,t,i){let r=n=>{if(this.fragContextChanged(e)){this.warn(`Fragment ${e.sn}${n.part?" p: "+n.part.index:""} of level ${e.level} was dropped during download.`),this.fragmentTracker.removeFragment(e);return}e.stats.chunkCount++,this._handleFragmentLoadProgress(n)};this._doFragLoad(e,t,i,r).then(n=>{if(!n)return;let o=this.state;if(this.fragContextChanged(e)){(o===w.FRAG_LOADING||!this.fragCurrent&&o===w.PARSING)&&(this.fragmentTracker.removeFragment(e),this.state=w.IDLE);return}"payload"in n&&(this.log(`Loaded fragment ${e.sn} of level ${e.level}`),this.hls.trigger(p.FRAG_LOADED,n)),this._handleFragmentLoadComplete(n)}).catch(n=>{this.state===w.STOPPED||this.state===w.ERROR||(this.warn(n),this.resetFragmentLoading(e))})}clearTrackerIfNeeded(e){var t;let{fragmentTracker:i}=this;if(i.getState(e)===ce.APPENDING){let n=e.type,o=this.getFwdBufferInfo(this.mediaBuffer,n),a=Math.max(e.duration,o?o.len:this.config.maxBufferLength);this.reduceMaxBufferLength(a)&&i.removeFragment(e)}else((t=this.mediaBuffer)==null?void 0:t.buffered.length)===0?i.removeAllFragments():i.hasParts(e.type)&&(i.detectPartialFragments({frag:e,part:null,stats:e.stats,id:e.type}),i.getState(e)===ce.PARTIAL&&i.removeFragment(e))}checkLiveUpdate(e){if(e.updated&&!e.live){let t=e.fragments[e.fragments.length-1];this.fragmentTracker.detectPartialFragments({frag:t,part:null,stats:t.stats,id:t.type})}e.fragments[0]||(e.deltaUpdateFailed=!0)}flushMainBuffer(e,t,i=null){if(!(e-t))return;let r={startOffset:e,endOffset:t,type:i};this.hls.trigger(p.BUFFER_FLUSHING,r)}_loadInitSegment(e,t){this._doFragLoad(e,t).then(i=>{if(!i||this.fragContextChanged(e)||!this.levels)throw new Error("init load aborted");return i}).then(i=>{let{hls:r}=this,{payload:n}=i,o=e.decryptdata;if(n&&n.byteLength>0&&o!=null&&o.key&&o.iv&&o.method==="AES-128"){let a=self.performance.now();return this.decrypter.decrypt(new Uint8Array(n),o.key.buffer,o.iv.buffer).catch(l=>{throw r.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_DECRYPT_ERROR,fatal:!1,error:l,reason:l.message,frag:e}),l}).then(l=>{let c=self.performance.now();return r.trigger(p.FRAG_DECRYPTED,{frag:e,payload:l,stats:{tstart:a,tdecrypt:c}}),i.payload=l,this.completeInitSegmentLoad(i)})}return this.completeInitSegmentLoad(i)}).catch(i=>{this.state===w.STOPPED||this.state===w.ERROR||(this.warn(i),this.resetFragmentLoading(e))})}completeInitSegmentLoad(e){let{levels:t}=this;if(!t)throw new Error("init load aborted, missing levels");let i=e.frag.stats;this.state=w.IDLE,e.frag.data=new Uint8Array(e.payload),i.parsing.start=i.buffering.start=self.performance.now(),i.parsing.end=i.buffering.end=self.performance.now(),this.tick()}fragContextChanged(e){let{fragCurrent:t}=this;return!e||!t||e.sn!==t.sn||e.level!==t.level}fragBufferedComplete(e,t){var i,r,n,o;let a=this.mediaBuffer?this.mediaBuffer:this.media;if(this.log(`Buffered ${e.type} sn: ${e.sn}${t?" part: "+t.index:""} of ${this.playlistType===$.MAIN?"level":"track"} ${e.level} (frag:[${((i=e.startPTS)!=null?i:NaN).toFixed(3)}-${((r=e.endPTS)!=null?r:NaN).toFixed(3)}] > buffer:${a?Pa.toString(Z.getBuffered(a)):"(detached)"})`),e.sn!=="initSegment"){var l;if(e.type!==$.SUBTITLE){let h=e.elementaryStreams;if(!Object.keys(h).some(u=>!!h[u])){this.state=w.IDLE;return}}let c=(l=this.levels)==null?void 0:l[e.level];c!=null&&c.fragmentError&&(this.log(`Resetting level fragment error count of ${c.fragmentError} on frag buffered`),c.fragmentError=0)}this.state=w.IDLE,a&&(!this.loadedmetadata&&e.type==$.MAIN&&a.buffered.length&&((n=this.fragCurrent)==null?void 0:n.sn)===((o=this.fragPrevious)==null?void 0:o.sn)&&(this.loadedmetadata=!0,this.seekToStartPos()),this.tick())}seekToStartPos(){}_handleFragmentLoadComplete(e){let{transmuxer:t}=this;if(!t)return;let{frag:i,part:r,partsLoaded:n}=e,o=!n||n.length===0||n.some(l=>!l),a=new ct(i.level,i.sn,i.stats.chunkCount+1,0,r?r.index:-1,!o);t.flush(a)}_handleFragmentLoadProgress(e){}_doFragLoad(e,t,i=null,r){var n;let o=t==null?void 0:t.details;if(!this.levels||!o)throw new Error(`frag load aborted, missing level${o?"":" detail"}s`);let a=null;if(e.encrypted&&!((n=e.decryptdata)!=null&&n.key)?(this.log(`Loading key for ${e.sn} of [${o.startSN}-${o.endSN}], ${this.logPrefix==="[stream-controller]"?"level":"track"} ${e.level}`),this.state=w.KEY_LOADING,this.fragCurrent=e,a=this.keyLoader.load(e).then(h=>{if(!this.fragContextChanged(h.frag))return this.hls.trigger(p.KEY_LOADED,h),this.state===w.KEY_LOADING&&(this.state=w.IDLE),h}),this.hls.trigger(p.KEY_LOADING,{frag:e}),this.fragCurrent===null&&(a=Promise.reject(new Error("frag load aborted, context changed in KEY_LOADING")))):!e.encrypted&&o.encryptedFragments.length&&this.keyLoader.loadClear(e,o.encryptedFragments),i=Math.max(e.start,i||0),this.config.lowLatencyMode&&e.sn!=="initSegment"){let h=o.partList;if(h&&r){i>e.end&&o.fragmentHint&&(e=o.fragmentHint);let u=this.getNextPart(h,e,i);if(u>-1){let d=h[u];this.log(`Loading part sn: ${e.sn} p: ${d.index} cc: ${e.cc} of playlist [${o.startSN}-${o.endSN}] parts [0-${u}-${h.length-1}] ${this.logPrefix==="[stream-controller]"?"level":"track"}: ${e.level}, target: ${parseFloat(i.toFixed(3))}`),this.nextLoadPosition=d.start+d.duration,this.state=w.FRAG_LOADING;let f;return a?f=a.then(g=>!g||this.fragContextChanged(g.frag)?null:this.doFragPartsLoad(e,d,t,r)).catch(g=>this.handleFragLoadError(g)):f=this.doFragPartsLoad(e,d,t,r).catch(g=>this.handleFragLoadError(g)),this.hls.trigger(p.FRAG_LOADING,{frag:e,part:d,targetBufferTime:i}),this.fragCurrent===null?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING parts")):f}else if(!e.url||this.loadedEndOfParts(h,i))return Promise.resolve(null)}}this.log(`Loading fragment ${e.sn} cc: ${e.cc} ${o?"of ["+o.startSN+"-"+o.endSN+"] ":""}${this.logPrefix==="[stream-controller]"?"level":"track"}: ${e.level}, target: ${parseFloat(i.toFixed(3))}`),M(e.sn)&&!this.bitrateTest&&(this.nextLoadPosition=e.start+e.duration),this.state=w.FRAG_LOADING;let l=this.config.progressive,c;return l&&a?c=a.then(h=>!h||this.fragContextChanged(h==null?void 0:h.frag)?null:this.fragmentLoader.load(e,r)).catch(h=>this.handleFragLoadError(h)):c=Promise.all([this.fragmentLoader.load(e,l?r:void 0),a]).then(([h])=>(!l&&h&&r&&r(h),h)).catch(h=>this.handleFragLoadError(h)),this.hls.trigger(p.FRAG_LOADING,{frag:e,targetBufferTime:i}),this.fragCurrent===null?Promise.reject(new Error("frag load aborted, context changed in FRAG_LOADING")):c}doFragPartsLoad(e,t,i,r){return new Promise((n,o)=>{var a;let l=[],c=(a=i.details)==null?void 0:a.partList,h=u=>{this.fragmentLoader.loadPart(e,u,r).then(d=>{l[u.index]=d;let f=d.part;this.hls.trigger(p.FRAG_LOADED,d);let g=mr(i,e.sn,u.index+1)||hn(c,e.sn,u.index+1);if(g)h(g);else return n({frag:e,part:f,partsLoaded:l})}).catch(o)};h(t)})}handleFragLoadError(e){if("data"in e){let t=e.data;e.data&&t.details===b.INTERNAL_ABORTED?this.handleFragLoadAborted(t.frag,t.part):this.hls.trigger(p.ERROR,t)}else this.hls.trigger(p.ERROR,{type:G.OTHER_ERROR,details:b.INTERNAL_EXCEPTION,err:e,error:e,fatal:!0});return null}_handleTransmuxerFlush(e){let t=this.getCurrentContext(e);if(!t||this.state!==w.PARSING){!this.fragCurrent&&this.state!==w.STOPPED&&this.state!==w.ERROR&&(this.state=w.IDLE);return}let{frag:i,part:r,level:n}=t,o=self.performance.now();i.stats.parsing.end=o,r&&(r.stats.parsing.end=o),this.updateLevelTiming(i,r,n,e.partial)}getCurrentContext(e){let{levels:t,fragCurrent:i}=this,{level:r,sn:n,part:o}=e;if(!(t!=null&&t[r]))return this.warn(`Levels object was unset while buffering fragment ${n} of level ${r}. The current chunk will not be buffered.`),null;let a=t[r],l=o>-1?mr(a,n,o):null,c=l?l.fragment:ua(a,n,i);return c?(i&&i!==c&&(c.stats=i.stats),{frag:c,part:l,level:a}):null}bufferFragmentData(e,t,i,r,n){var o;if(!e||this.state!==w.PARSING)return;let{data1:a,data2:l}=e,c=a;if(a&&l&&(c=Te(a,l)),!((o=c)!=null&&o.length))return;let h={type:e.type,frag:t,part:i,chunkMeta:r,parent:t.type,data:c};if(this.hls.trigger(p.BUFFER_APPENDING,h),e.dropped&&e.independent&&!i){if(n)return;this.flushBufferGap(t)}}flushBufferGap(e){let t=this.media;if(!t)return;if(!Z.isBuffered(t,t.currentTime)){this.flushMainBuffer(0,e.start);return}let i=t.currentTime,r=Z.bufferInfo(t,i,0),n=e.duration,o=Math.min(this.config.maxFragLookUpTolerance*2,n*.25),a=Math.max(Math.min(e.start-o,r.end-o),i+o);e.start-a>o&&this.flushMainBuffer(a,e.start)}getFwdBufferInfo(e,t){let i=this.getLoadPosition();return M(i)?this.getFwdBufferInfoAtPos(e,i,t):null}getFwdBufferInfoAtPos(e,t,i){let{config:{maxBufferHole:r}}=this,n=Z.bufferInfo(e,t,r);if(n.len===0&&n.nextStart!==void 0){let o=this.fragmentTracker.getBufferedFrag(t,i);if(o&&n.nextStart<o.end)return Z.bufferInfo(e,t,Math.max(n.nextStart,r))}return n}getMaxBufferLength(e){let{config:t}=this,i;return e?i=Math.max(8*t.maxBufferSize/e,t.maxBufferLength):i=t.maxBufferLength,Math.min(i,t.maxMaxBufferLength)}reduceMaxBufferLength(e){let t=this.config,i=e||t.maxBufferLength;return t.maxMaxBufferLength>=i?(t.maxMaxBufferLength/=2,this.warn(`Reduce max buffer length to ${t.maxMaxBufferLength}s`),!0):!1}getAppendedFrag(e,t=$.MAIN){let i=this.fragmentTracker.getAppendedFrag(e,$.MAIN);return i&&"fragment"in i?i.fragment:i}getNextFragment(e,t){let i=t.fragments,r=i.length;if(!r)return null;let{config:n}=this,o=i[0].start,a;if(t.live){let l=n.initialLiveManifestSize;if(r<l)return this.warn(`Not enough fragments to start playback (have: ${r}, need: ${l})`),null;(!t.PTSKnown&&!this.startFragRequested&&this.startPosition===-1||e<o)&&(a=this.getInitialLiveFragment(t,i),this.startPosition=this.nextLoadPosition=a?this.hls.liveSyncPosition||a.start:e)}else e<=o&&(a=i[0]);if(!a){let l=n.lowLatencyMode?t.partEnd:t.fragmentEnd;a=this.getFragmentAtPosition(e,l,t)}return this.mapToInitFragWhenRequired(a)}isLoopLoading(e,t){let i=this.fragmentTracker.getState(e);return(i===ce.OK||i===ce.PARTIAL&&!!e.gap)&&this.nextLoadPosition>t}getNextFragmentLoopLoading(e,t,i,r,n){let o=e.gap,a=this.getNextFragment(this.nextLoadPosition,t);if(a===null)return a;if(e=a,o&&e&&!e.gap&&i.nextStart){let l=this.getFwdBufferInfoAtPos(this.mediaBuffer?this.mediaBuffer:this.media,i.nextStart,r);if(l!==null&&i.len+l.len>=n)return this.log(`buffer full after gaps in "${r}" playlist starting at sn: ${e.sn}`),null}return e}mapToInitFragWhenRequired(e){return e!=null&&e.initSegment&&!(e!=null&&e.initSegment.data)&&!this.bitrateTest?e.initSegment:e}getNextPart(e,t,i){let r=-1,n=!1,o=!0;for(let a=0,l=e.length;a<l;a++){let c=e[a];if(o=o&&!c.independent,r>-1&&i<c.start)break;let h=c.loaded;h?r=-1:(n||c.independent||o)&&c.fragment===t&&(r=a),n=h}return r}loadedEndOfParts(e,t){let i=e[e.length-1];return i&&t>i.start&&i.loaded}getInitialLiveFragment(e,t){let i=this.fragPrevious,r=null;if(i){if(e.hasProgramDateTime&&(this.log(`Live playlist, switching playlist, load frag with same PDT: ${i.programDateTime}`),r=fa(t,i.endProgramDateTime,this.config.maxFragLookUpTolerance)),!r){let n=i.sn+1;if(n>=e.startSN&&n<=e.endSN){let o=t[n-e.startSN];i.cc===o.cc&&(r=o,this.log(`Live playlist, switching playlist, load frag with next SN: ${r.sn}`))}r||(r=pa(t,i.cc),r&&this.log(`Live playlist, switching playlist, load frag with same CC: ${r.sn}`))}}else{let n=this.hls.liveSyncPosition;n!==null&&(r=this.getFragmentAtPosition(n,this.bitrateTest?e.fragmentEnd:e.edge,e))}return r}getFragmentAtPosition(e,t,i){let{config:r}=this,{fragPrevious:n}=this,{fragments:o,endSN:a}=i,{fragmentHint:l}=i,c=r.maxFragLookUpTolerance,h=i.partList,u=!!(r.lowLatencyMode&&h!=null&&h.length&&l);u&&l&&!this.bitrateTest&&(o=o.concat(l),a=l.sn);let d;if(e<t){let f=e>t-c?0:c;d=$t(n,o,e,f)}else d=o[o.length-1];if(d){let f=d.sn-i.startSN,g=this.fragmentTracker.getState(d);if((g===ce.OK||g===ce.PARTIAL&&d.gap)&&(n=d),n&&d.sn===n.sn&&(!u||h[0].fragment.sn>d.sn)&&n&&d.level===n.level){let y=o[f+1];d.sn<a&&this.fragmentTracker.getState(y)!==ce.OK?d=y:d=null}}return d}synchronizeToLiveEdge(e){let{config:t,media:i}=this;if(!i)return;let r=this.hls.liveSyncPosition,n=i.currentTime,o=e.fragments[0].start,a=e.edge,l=n>=o-t.maxFragLookUpTolerance&&n<=a;if(r!==null&&i.duration>r&&(n<r||!l)){let c=t.liveMaxLatencyDuration!==void 0?t.liveMaxLatencyDuration:t.liveMaxLatencyDurationCount*e.targetduration;(!l&&i.readyState<4||n<a-c)&&(this.loadedmetadata||(this.nextLoadPosition=r),i.readyState&&(this.warn(`Playback: ${n.toFixed(3)} is located too far from the end of live sliding playlist: ${a}, reset currentTime to : ${r.toFixed(3)}`),i.currentTime=r))}}alignPlaylists(e,t,i){let r=e.fragments.length;if(!r)return this.warn("No fragments in live playlist"),0;let n=e.fragments[0].start,o=!t,a=e.alignedSliding&&M(n);if(o||!a&&!n){let{fragPrevious:l}=this;wa(l,i,e);let c=e.fragments[0].start;return this.log(`Live playlist sliding: ${c.toFixed(2)} start-sn: ${t?t.startSN:"na"}->${e.startSN} prev-sn: ${l?l.sn:"na"} fragments: ${r}`),c}return n}waitForCdnTuneIn(e){return e.live&&e.canBlockReload&&e.partTarget&&e.tuneInGoal>Math.max(e.partHoldBack,e.partTarget*3)}setStartPosition(e,t){let i=this.startPosition;if(i<t&&(i=-1),i===-1||this.lastCurrentTime===-1){let r=this.startTimeOffset!==null,n=r?this.startTimeOffset:e.startTimeOffset;n!==null&&M(n)?(i=t+n,n<0&&(i+=e.totalduration),i=Math.min(Math.max(t,i),t+e.totalduration),this.log(`Start time offset ${n} found in ${r?"multivariant":"media"} playlist, adjust startPosition to ${i}`),this.startPosition=i):e.live?i=this.hls.liveSyncPosition||t:this.startPosition=i=0,this.lastCurrentTime=i}this.nextLoadPosition=i}getLoadPosition(){let{media:e}=this,t=0;return this.loadedmetadata&&e?t=e.currentTime:this.nextLoadPosition&&(t=this.nextLoadPosition),t}handleFragLoadAborted(e,t){this.transmuxer&&e.sn!=="initSegment"&&e.stats.aborted&&(this.warn(`Fragment ${e.sn}${t?" part "+t.index:""} of level ${e.level} was aborted`),this.resetFragmentLoading(e))}resetFragmentLoading(e){(!this.fragCurrent||!this.fragContextChanged(e)&&this.state!==w.FRAG_LOADING_WAITING_RETRY)&&(this.state=w.IDLE)}onFragmentOrKeyLoadError(e,t){if(t.chunkMeta&&!t.frag){let h=this.getCurrentContext(t.chunkMeta);h&&(t.frag=h.frag)}let i=t.frag;if(!i||i.type!==e||!this.levels)return;if(this.fragContextChanged(i)){var r;this.warn(`Frag load error must match current frag to retry ${i.url} > ${(r=this.fragCurrent)==null?void 0:r.url}`);return}let n=t.details===b.FRAG_GAP;n&&this.fragmentTracker.fragBuffered(i,!0);let o=t.errorAction,{action:a,retryCount:l=0,retryConfig:c}=o||{};if(o&&a===le.RetryRequest&&c){this.resetStartWhenNotLoaded(this.levelLastLoaded);let h=Ns(c,l);this.warn(`Fragment ${i.sn} of ${e} ${i.level} errored with ${t.details}, retrying loading ${l+1}/${c.maxNumRetry} in ${h}ms`),o.resolved=!0,this.retryDate=self.performance.now()+h,this.state=w.FRAG_LOADING_WAITING_RETRY}else if(c&&o)if(this.resetFragmentErrors(e),l<c.maxNumRetry)!n&&a!==le.RemoveAlternatePermanently&&(o.resolved=!0);else{S.warn(`${t.details} reached or exceeded max retry (${l})`);return}else(o==null?void 0:o.action)===le.SendAlternateToPenaltyBox?this.state=w.WAITING_LEVEL:this.state=w.ERROR;this.tickImmediate()}reduceLengthAndFlushBuffer(e){if(this.state===w.PARSING||this.state===w.PARSED){let t=e.parent,i=this.getFwdBufferInfo(this.mediaBuffer,t),r=i&&i.len>.5;r&&this.reduceMaxBufferLength(i.len);let n=!r;return n&&this.warn(`Buffer full error while media.currentTime is not buffered, flush ${t} buffer`),e.frag&&(this.fragmentTracker.removeFragment(e.frag),this.nextLoadPosition=e.frag.start),this.resetLoadingState(),n}return!1}resetFragmentErrors(e){e===$.AUDIO&&(this.fragCurrent=null),this.loadedmetadata||(this.startFragRequested=!1),this.state!==w.STOPPED&&(this.state=w.IDLE)}afterBufferFlushed(e,t,i){if(!e)return;let r=Z.getBuffered(e);this.fragmentTracker.detectEvictedFragments(t,r,i),this.state===w.ENDED&&this.resetLoadingState()}resetLoadingState(){this.log("Reset loading state"),this.fragCurrent=null,this.fragPrevious=null,this.state=w.IDLE}resetStartWhenNotLoaded(e){if(!this.loadedmetadata){this.startFragRequested=!1;let t=e?e.details:null;t!=null&&t.live?(this.startPosition=-1,this.setStartPosition(t,0),this.resetLoadingState()):this.nextLoadPosition=this.startPosition}}resetWhenMissingContext(e){this.warn(`The loading context changed while buffering fragment ${e.sn} of level ${e.level}. This chunk will not be buffered.`),this.removeUnbufferedFrags(),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState()}removeUnbufferedFrags(e=0){this.fragmentTracker.removeFragmentsInRange(e,1/0,this.playlistType,!1,!0)}updateLevelTiming(e,t,i,r){var n;let o=i.details;if(!o){this.warn("level.details undefined");return}if(!Object.keys(e.elementaryStreams).reduce((l,c)=>{let h=e.elementaryStreams[c];if(h){let u=h.endPTS-h.startPTS;if(u<=0)return this.warn(`Could not parse fragment ${e.sn} ${c} duration reliably (${u})`),l||!1;let d=r?0:ln(o,e,h.startPTS,h.endPTS,h.startDTS,h.endDTS);return this.hls.trigger(p.LEVEL_PTS_UPDATED,{details:o,level:i,drift:d,type:c,frag:e,start:h.startPTS,end:h.endPTS}),!0}return l},!1)&&((n=this.transmuxer)==null?void 0:n.error)===null){let l=new Error(`Found no media in fragment ${e.sn} of level ${e.level} resetting transmuxer to fallback to playlist timing`);if(i.fragmentError===0&&(i.fragmentError++,e.gap=!0,this.fragmentTracker.removeFragment(e),this.fragmentTracker.fragBuffered(e,!0)),this.warn(l.message),this.hls.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_PARSING_ERROR,fatal:!1,error:l,frag:e,reason:`Found no media in msn ${e.sn} of level "${i.url}"`}),!this.hls)return;this.resetTransmuxer()}this.state=w.PARSED,this.hls.trigger(p.FRAG_PARSED,{frag:e,part:t})}resetTransmuxer(){this.transmuxer&&(this.transmuxer.destroy(),this.transmuxer=null)}recoverWorkerError(e){e.event==="demuxerWorker"&&(this.fragmentTracker.removeAllFragments(),this.resetTransmuxer(),this.resetStartWhenNotLoaded(this.levelLastLoaded),this.resetLoadingState())}set state(e){let t=this._state;t!==e&&(this._state=e,this.log(`${t}->${e}`))}get state(){return this._state}},Ht=class{constructor(){this.chunks=[],this.dataLength=0}push(e){this.chunks.push(e),this.dataLength+=e.length}flush(){let{chunks:e,dataLength:t}=this,i;if(e.length)e.length===1?i=e[0]:i=Oa(e,t);else return new Uint8Array(0);return this.reset(),i}reset(){this.chunks.length=0,this.dataLength=0}};function Oa(s,e){let t=new Uint8Array(e),i=0;for(let r=0;r<s.length;r++){let n=s[r];t.set(n,i),i+=n.length}return t}function Ma(){return typeof __HLS_WORKER_BUNDLE__=="function"}function Fa(){let s=new self.Blob([`var exports={};var module={exports:exports};function define(f){f()};define.amd=true;(${__HLS_WORKER_BUNDLE__.toString()})(true);`],{type:"text/javascript"}),e=self.URL.createObjectURL(s);return{worker:new self.Worker(e),objectURL:e}}function Na(s){let e=new self.URL(s,self.location.href).href;return{worker:new self.Worker(e),scriptURL:e}}function Le(s="",e=9e4){return{type:s,id:-1,pid:-1,inputTimeScale:e,sequenceNumber:-1,samples:[],dropped:0}}var dt=class{constructor(){this._audioTrack=void 0,this._id3Track=void 0,this.frameIndex=0,this.cachedData=null,this.basePTS=null,this.initPTS=null,this.lastPTS=null}resetInitSegment(e,t,i,r){this._id3Track={type:"id3",id:3,pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0}}resetTimeStamp(e){this.initPTS=e,this.resetContiguity()}resetContiguity(){this.basePTS=null,this.lastPTS=null,this.frameIndex=0}canParse(e,t){return!1}appendFrame(e,t,i){}demux(e,t){this.cachedData&&(e=Te(this.cachedData,e),this.cachedData=null);let i=nt(e,0),r=i?i.length:0,n,o=this._audioTrack,a=this._id3Track,l=i?Fs(i):void 0,c=e.length;for((this.basePTS===null||this.frameIndex===0&&M(l))&&(this.basePTS=Ba(l,t,this.initPTS),this.lastPTS=this.basePTS),this.lastPTS===null&&(this.lastPTS=this.basePTS),i&&i.length>0&&a.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:Se.audioId3,duration:Number.POSITIVE_INFINITY});r<c;){if(this.canParse(e,r)){let h=this.appendFrame(o,e,r);h?(this.frameIndex++,this.lastPTS=h.sample.pts,r+=h.length,n=r):r=c}else Eo(e,r)?(i=nt(e,r),a.samples.push({pts:this.lastPTS,dts:this.lastPTS,data:i,type:Se.audioId3,duration:Number.POSITIVE_INFINITY}),r+=i.length,n=r):r++;if(r===c&&n!==c){let h=Ue(e,n);this.cachedData?this.cachedData=Te(this.cachedData,h):this.cachedData=h}}return{audioTrack:o,videoTrack:Le(),id3Track:a,textTrack:Le()}}demuxSampleAes(e,t,i){return Promise.reject(new Error(`[${this}] This demuxer does not support Sample-AES decryption`))}flush(e){let t=this.cachedData;return t&&(this.cachedData=null,this.demux(t,0)),{audioTrack:this._audioTrack,videoTrack:Le(),id3Track:this._id3Track,textTrack:Le()}}destroy(){}},Ba=(s,e,t)=>{if(M(s))return s*90;let i=t?t.baseTime*9e4/t.timescale:0;return e*9e4+i};function Ua(s,e,t,i){let r,n,o,a,l=navigator.userAgent.toLowerCase(),c=i,h=[96e3,88200,64e3,48e3,44100,32e3,24e3,22050,16e3,12e3,11025,8e3,7350];r=((e[t+2]&192)>>>6)+1;let u=(e[t+2]&60)>>>2;if(u>h.length-1){let d=new Error(`invalid ADTS sampling index:${u}`);s.emit(p.ERROR,p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_PARSING_ERROR,fatal:!0,error:d,reason:d.message});return}return o=(e[t+2]&1)<<2,o|=(e[t+3]&192)>>>6,S.log(`manifest codec:${i}, ADTS type:${r}, samplingIndex:${u}`),/firefox/i.test(l)?u>=6?(r=5,a=new Array(4),n=u-3):(r=2,a=new Array(2),n=u):l.indexOf("android")!==-1?(r=2,a=new Array(2),n=u):(r=5,a=new Array(4),i&&(i.indexOf("mp4a.40.29")!==-1||i.indexOf("mp4a.40.5")!==-1)||!i&&u>=6?n=u-3:((i&&i.indexOf("mp4a.40.2")!==-1&&(u>=6&&o===1||/vivaldi/i.test(l))||!i&&o===1)&&(r=2,a=new Array(2)),n=u)),a[0]=r<<3,a[0]|=(u&14)>>1,a[1]|=(u&1)<<7,a[1]|=o<<3,r===5&&(a[1]|=(n&14)>>1,a[2]=(n&1)<<7,a[2]|=8,a[3]=0),{config:a,samplerate:h[u],channelCount:o,codec:"mp4a.40."+r,manifestCodec:c}}function pn(s,e){return s[e]===255&&(s[e+1]&246)===240}function mn(s,e){return s[e+1]&1?7:9}function Bs(s,e){return(s[e+3]&3)<<11|s[e+4]<<3|(s[e+5]&224)>>>5}function $a(s,e){return e+5<s.length}function Kt(s,e){return e+1<s.length&&pn(s,e)}function Ga(s,e){return $a(s,e)&&pn(s,e)&&Bs(s,e)<=s.length-e}function Ha(s,e){if(Kt(s,e)){let t=mn(s,e);if(e+t>=s.length)return!1;let i=Bs(s,e);if(i<=t)return!1;let r=e+i;return r===s.length||Kt(s,r)}return!1}function yn(s,e,t,i,r){if(!s.samplerate){let n=Ua(e,t,i,r);if(!n)return;s.config=n.config,s.samplerate=n.samplerate,s.channelCount=n.channelCount,s.codec=n.codec,s.manifestCodec=n.manifestCodec,S.log(`parsed codec:${s.codec}, rate:${n.samplerate}, channels:${n.channelCount}`)}}function Tn(s){return 1024*9e4/s}function Ka(s,e){let t=mn(s,e);if(e+t<=s.length){let i=Bs(s,e)-t;if(i>0)return{headerLength:t,frameLength:i}}}function En(s,e,t,i,r){let n=Tn(s.samplerate),o=i+r*n,a=Ka(e,t),l;if(a){let{frameLength:u,headerLength:d}=a,f=d+u,g=Math.max(0,t+f-e.length);g?(l=new Uint8Array(f-d),l.set(e.subarray(t+d,e.length),0)):l=e.subarray(t+d,t+f);let m={unit:l,pts:o};return g||s.samples.push(m),{sample:m,length:f,missing:g}}let c=e.length-t;return l=new Uint8Array(c),l.set(e.subarray(t,e.length),0),{sample:{unit:l,pts:o},length:c,missing:-1}}var Et=null,Va=[32,64,96,128,160,192,224,256,288,320,352,384,416,448,32,48,56,64,80,96,112,128,160,192,224,256,320,384,32,40,48,56,64,80,96,112,128,160,192,224,256,320,32,48,56,64,80,96,112,128,144,160,176,192,224,256,8,16,24,32,40,48,56,64,80,96,112,128,144,160],Ya=[44100,48e3,32e3,22050,24e3,16e3,11025,12e3,8e3],Wa=[[0,72,144,12],[0,0,0,0],[0,72,144,12],[0,144,144,12]],qa=[0,1,1,4];function vn(s,e,t,i,r){if(t+24>e.length)return;let n=xn(e,t);if(n&&t+n.frameLength<=e.length){let o=n.samplesPerFrame*9e4/n.sampleRate,a=i+r*o,l={unit:e.subarray(t,t+n.frameLength),pts:a,dts:a};return s.config=[],s.channelCount=n.channelCount,s.samplerate=n.sampleRate,s.samples.push(l),{sample:l,length:n.frameLength,missing:0}}}function xn(s,e){let t=s[e+1]>>3&3,i=s[e+1]>>1&3,r=s[e+2]>>4&15,n=s[e+2]>>2&3;if(t!==1&&r!==0&&r!==15&&n!==3){let o=s[e+2]>>1&1,a=s[e+3]>>6,l=t===3?3-i:i===3?3:4,c=Va[l*14+r-1]*1e3,u=Ya[(t===3?0:t===2?1:2)*3+n],d=a===3?1:2,f=Wa[t][i],g=qa[i],m=f*8*g,y=Math.floor(f*c/u+o)*g;if(Et===null){let E=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);Et=E?parseInt(E[1]):0}return!!Et&&Et<=87&&i===2&&c>=224e3&&a===0&&(s[e+3]=s[e+3]|128),{sampleRate:u,channelCount:d,frameLength:y,samplesPerFrame:m}}}function Us(s,e){return s[e]===255&&(s[e+1]&224)===224&&(s[e+1]&6)!==0}function Sn(s,e){return e+1<s.length&&Us(s,e)}function ja(s,e){return Us(s,e)&&4<=s.length-e}function bn(s,e){if(e+1<s.length&&Us(s,e)){let i=xn(s,e),r=4;i!=null&&i.frameLength&&(r=i.frameLength);let n=e+r;return n===s.length||Sn(s,n)}return!1}var Wi=class extends dt{constructor(e,t){super(),this.observer=void 0,this.config=void 0,this.observer=e,this.config=t}resetInitSegment(e,t,i,r){super.resetInitSegment(e,t,i,r),this._audioTrack={container:"audio/adts",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"aac",samples:[],manifestCodec:t,duration:r,inputTimeScale:9e4,dropped:0}}static probe(e){if(!e)return!1;let t=nt(e,0),i=(t==null?void 0:t.length)||0;if(bn(e,i))return!1;for(let r=e.length;i<r;i++)if(Ha(e,i))return S.log("ADTS sync word found !"),!0;return!1}canParse(e,t){return Ga(e,t)}appendFrame(e,t,i){yn(e,this.observer,t,i,e.manifestCodec);let r=En(e,t,i,this.basePTS,this.frameIndex);if(r&&r.missing===0)return r}},za=/\/emsg[-/]ID3/i,qi=class{constructor(e,t){this.remainderData=null,this.timeOffset=0,this.config=void 0,this.videoTrack=void 0,this.audioTrack=void 0,this.id3Track=void 0,this.txtTrack=void 0,this.config=t}resetTimeStamp(){}resetInitSegment(e,t,i,r){let n=this.videoTrack=Le("video",1),o=this.audioTrack=Le("audio",1),a=this.txtTrack=Le("text",1);if(this.id3Track=Le("id3",1),this.timeOffset=0,!(e!=null&&e.byteLength))return;let l=Zr(e);if(l.video){let{id:c,timescale:h,codec:u}=l.video;n.id=c,n.timescale=a.timescale=h,n.codec=u}if(l.audio){let{id:c,timescale:h,codec:u}=l.audio;o.id=c,o.timescale=h,o.codec=u}a.id=Xr.text,n.sampleDuration=0,n.duration=o.duration=r}resetContiguity(){this.remainderData=null}static probe(e){return Io(e)}demux(e,t){this.timeOffset=t;let i=e,r=this.videoTrack,n=this.txtTrack;if(this.config.progressive){this.remainderData&&(i=Te(this.remainderData,e));let a=Fo(i);this.remainderData=a.remainder,r.samples=a.valid||new Uint8Array}else r.samples=i;let o=this.extractID3Track(r,t);return n.samples=Js(t,r),{videoTrack:r,audioTrack:this.audioTrack,id3Track:o,textTrack:this.txtTrack}}flush(){let e=this.timeOffset,t=this.videoTrack,i=this.txtTrack;t.samples=this.remainderData||new Uint8Array,this.remainderData=null;let r=this.extractID3Track(t,this.timeOffset);return i.samples=Js(e,t),{videoTrack:t,audioTrack:Le(),id3Track:r,textTrack:Le()}}extractID3Track(e,t){let i=this.id3Track;if(e.samples.length){let r=Y(e.samples,["emsg"]);r&&r.forEach(n=>{let o=Uo(n);if(za.test(o.schemeIdUri)){let a=M(o.presentationTime)?o.presentationTime/o.timeScale:t+o.presentationTimeDelta/o.timeScale,l=o.eventDuration===4294967295?Number.POSITIVE_INFINITY:o.eventDuration/o.timeScale;l<=.001&&(l=Number.POSITIVE_INFINITY);let c=o.payload;i.samples.push({data:c,len:c.byteLength,dts:a,pts:a,type:Se.emsg,duration:l})}})}return i}demuxSampleAes(e,t,i){return Promise.reject(new Error("The MP4 demuxer does not support SAMPLE-AES decryption"))}destroy(){}},An=(s,e)=>{let t=0,i=5;e+=i;let r=new Uint32Array(1),n=new Uint32Array(1),o=new Uint8Array(1);for(;i>0;){o[0]=s[e];let a=Math.min(i,8),l=8-a;n[0]=4278190080>>>24+l<<l,r[0]=(o[0]&n[0])>>l,t=t?t<<a|r[0]:r[0],e+=1,i-=a}return t},ji=class extends dt{constructor(e){super(),this.observer=void 0,this.observer=e}resetInitSegment(e,t,i,r){super.resetInitSegment(e,t,i,r),this._audioTrack={container:"audio/ac-3",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"ac3",samples:[],manifestCodec:t,duration:r,inputTimeScale:9e4,dropped:0}}canParse(e,t){return t+64<e.length}appendFrame(e,t,i){let r=Ln(e,t,i,this.basePTS,this.frameIndex);if(r!==-1)return{sample:e.samples[e.samples.length-1],length:r,missing:0}}static probe(e){if(!e)return!1;let t=nt(e,0);if(!t)return!1;let i=t.length;return e[i]===11&&e[i+1]===119&&Fs(t)!==void 0&&An(e,i)<16}};function Ln(s,e,t,i,r){if(t+8>e.length||e[t]!==11||e[t+1]!==119)return-1;let n=e[t+4]>>6;if(n>=3)return-1;let a=[48e3,44100,32e3][n],l=e[t+4]&63,h=[64,69,96,64,70,96,80,87,120,80,88,120,96,104,144,96,105,144,112,121,168,112,122,168,128,139,192,128,140,192,160,174,240,160,175,240,192,208,288,192,209,288,224,243,336,224,244,336,256,278,384,256,279,384,320,348,480,320,349,480,384,417,576,384,418,576,448,487,672,448,488,672,512,557,768,512,558,768,640,696,960,640,697,960,768,835,1152,768,836,1152,896,975,1344,896,976,1344,1024,1114,1536,1024,1115,1536,1152,1253,1728,1152,1254,1728,1280,1393,1920,1280,1394,1920][l*3+n]*2;if(t+h>e.length)return-1;let u=e[t+6]>>5,d=0;u===2?d+=2:(u&1&&u!==1&&(d+=2),u&4&&(d+=2));let f=(e[t+6]<<8|e[t+7])>>12-d&1,m=[2,1,2,3,3,4,4,5][u]+f,y=e[t+5]>>3,T=e[t+5]&7,v=new Uint8Array([n<<6|y<<1|T>>2,(T&3)<<6|u<<3|f<<2|l>>4,l<<4&224]),E=1536/a*9e4,_=i+r*E,x=e.subarray(t,t+h);return s.config=v,s.channelCount=m,s.samplerate=a,s.samples.push({unit:x,pts:_}),h}var zi=class{constructor(){this.VideoSample=null}createVideoSample(e,t,i,r){return{key:e,frame:!1,pts:t,dts:i,units:[],debug:r,length:0}}getLastNalUnit(e){var t;let i=this.VideoSample,r;if((!i||i.units.length===0)&&(i=e[e.length-1]),(t=i)!=null&&t.units){let n=i.units;r=n[n.length-1]}return r}pushAccessUnit(e,t){if(e.units.length&&e.frame){if(e.pts===void 0){let i=t.samples,r=i.length;if(r){let n=i[r-1];e.pts=n.pts,e.dts=n.dts}else{t.dropped++;return}}t.samples.push(e)}e.debug.length&&S.log(e.pts+"/"+e.dts+":"+e.debug)}},Vt=class{constructor(e){this.data=void 0,this.bytesAvailable=void 0,this.word=void 0,this.bitsAvailable=void 0,this.data=e,this.bytesAvailable=e.byteLength,this.word=0,this.bitsAvailable=0}loadWord(){let e=this.data,t=this.bytesAvailable,i=e.byteLength-t,r=new Uint8Array(4),n=Math.min(4,t);if(n===0)throw new Error("no bytes available");r.set(e.subarray(i,i+n)),this.word=new DataView(r.buffer).getUint32(0),this.bitsAvailable=n*8,this.bytesAvailable-=n}skipBits(e){let t;e=Math.min(e,this.bytesAvailable*8+this.bitsAvailable),this.bitsAvailable>e?(this.word<<=e,this.bitsAvailable-=e):(e-=this.bitsAvailable,t=e>>3,e-=t<<3,this.bytesAvailable-=t,this.loadWord(),this.word<<=e,this.bitsAvailable-=e)}readBits(e){let t=Math.min(this.bitsAvailable,e),i=this.word>>>32-t;if(e>32&&S.error("Cannot read more than 32 bits at a time"),this.bitsAvailable-=t,this.bitsAvailable>0)this.word<<=t;else if(this.bytesAvailable>0)this.loadWord();else throw new Error("no bits available");return t=e-t,t>0&&this.bitsAvailable?i<<t|this.readBits(t):i}skipLZ(){let e;for(e=0;e<this.bitsAvailable;++e)if(this.word&2147483648>>>e)return this.word<<=e,this.bitsAvailable-=e,e;return this.loadWord(),e+this.skipLZ()}skipUEG(){this.skipBits(1+this.skipLZ())}skipEG(){this.skipBits(1+this.skipLZ())}readUEG(){let e=this.skipLZ();return this.readBits(e+1)-1}readEG(){let e=this.readUEG();return 1&e?1+e>>>1:-1*(e>>>1)}readBoolean(){return this.readBits(1)===1}readUByte(){return this.readBits(8)}readUShort(){return this.readBits(16)}readUInt(){return this.readBits(32)}skipScalingList(e){let t=8,i=8,r;for(let n=0;n<e;n++)i!==0&&(r=this.readEG(),i=(t+r+256)%256),t=i===0?t:i}readSPS(){let e=0,t=0,i=0,r=0,n,o,a,l=this.readUByte.bind(this),c=this.readBits.bind(this),h=this.readUEG.bind(this),u=this.readBoolean.bind(this),d=this.skipBits.bind(this),f=this.skipEG.bind(this),g=this.skipUEG.bind(this),m=this.skipScalingList.bind(this);l();let y=l();if(c(5),d(3),l(),g(),y===100||y===110||y===122||y===244||y===44||y===83||y===86||y===118||y===128){let I=h();if(I===3&&d(1),g(),g(),d(1),u())for(o=I!==3?8:12,a=0;a<o;a++)u()&&(a<6?m(16):m(64))}g();let T=h();if(T===0)h();else if(T===1)for(d(1),f(),f(),n=h(),a=0;a<n;a++)f();g(),d(1);let v=h(),E=h(),_=c(1);_===0&&d(1),d(1),u()&&(e=h(),t=h(),i=h(),r=h());let x=[1,1];if(u()&&u())switch(l()){case 1:x=[1,1];break;case 2:x=[12,11];break;case 3:x=[10,11];break;case 4:x=[16,11];break;case 5:x=[40,33];break;case 6:x=[24,11];break;case 7:x=[20,11];break;case 8:x=[32,11];break;case 9:x=[80,33];break;case 10:x=[18,11];break;case 11:x=[15,11];break;case 12:x=[64,33];break;case 13:x=[160,99];break;case 14:x=[4,3];break;case 15:x=[3,2];break;case 16:x=[2,1];break;case 255:{x=[l()<<8|l(),l()<<8|l()];break}}return{width:Math.ceil((v+1)*16-e*2-t*2),height:(2-_)*(E+1)*16-(_?2:4)*(i+r),pixelRatio:x}}readSliceType(){return this.readUByte(),this.readUEG(),this.readUEG()}},Xi=class extends zi{parseAVCPES(e,t,i,r,n){let o=this.parseAVCNALu(e,i.data),a=this.VideoSample,l,c=!1;i.data=null,a&&o.length&&!e.audFound&&(this.pushAccessUnit(a,e),a=this.VideoSample=this.createVideoSample(!1,i.pts,i.dts,"")),o.forEach(h=>{var u;switch(h.type){case 1:{let m=!1;l=!0;let y=h.data;if(c&&y.length>4){let T=new Vt(y).readSliceType();(T===2||T===4||T===7||T===9)&&(m=!0)}if(m){var d;(d=a)!=null&&d.frame&&!a.key&&(this.pushAccessUnit(a,e),a=this.VideoSample=null)}a||(a=this.VideoSample=this.createVideoSample(!0,i.pts,i.dts,"")),a.frame=!0,a.key=m;break}case 5:l=!0,(u=a)!=null&&u.frame&&!a.key&&(this.pushAccessUnit(a,e),a=this.VideoSample=null),a||(a=this.VideoSample=this.createVideoSample(!0,i.pts,i.dts,"")),a.key=!0,a.frame=!0;break;case 6:{l=!0,tn(h.data,1,i.pts,t.samples);break}case 7:{var f,g;l=!0,c=!0;let m=h.data,T=new Vt(m).readSPS();if(!e.sps||e.width!==T.width||e.height!==T.height||((f=e.pixelRatio)==null?void 0:f[0])!==T.pixelRatio[0]||((g=e.pixelRatio)==null?void 0:g[1])!==T.pixelRatio[1]){e.width=T.width,e.height=T.height,e.pixelRatio=T.pixelRatio,e.sps=[m],e.duration=n;let v=m.subarray(1,4),E="avc1.";for(let _=0;_<3;_++){let x=v[_].toString(16);x.length<2&&(x="0"+x),E+=x}e.codec=E}break}case 8:l=!0,e.pps=[h.data];break;case 9:l=!0,e.audFound=!0,a&&this.pushAccessUnit(a,e),a=this.VideoSample=this.createVideoSample(!1,i.pts,i.dts,"");break;case 12:l=!0;break;default:l=!1,a&&(a.debug+="unknown NAL "+h.type+" ");break}a&&l&&a.units.push(h)}),r&&a&&(this.pushAccessUnit(a,e),this.VideoSample=null)}parseAVCNALu(e,t){let i=t.byteLength,r=e.naluState||0,n=r,o=[],a=0,l,c,h,u=-1,d=0;for(r===-1&&(u=0,d=t[0]&31,r=0,a=1);a<i;){if(l=t[a++],!r){r=l?0:1;continue}if(r===1){r=l?0:2;continue}if(!l)r=3;else if(l===1){if(c=a-r-1,u>=0){let f={data:t.subarray(u,c),type:d};o.push(f)}else{let f=this.getLastNalUnit(e.samples);f&&(n&&a<=4-n&&f.state&&(f.data=f.data.subarray(0,f.data.byteLength-n)),c>0&&(f.data=Te(f.data,t.subarray(0,c)),f.state=0))}a<i?(h=t[a]&31,u=a,d=h,r=0):r=-1}else r=0}if(u>=0&&r>=0){let f={data:t.subarray(u,i),type:d,state:r};o.push(f)}if(o.length===0){let f=this.getLastNalUnit(e.samples);f&&(f.data=Te(f.data,t))}return e.naluState=r,o}},Qi=class{constructor(e,t,i){this.keyData=void 0,this.decrypter=void 0,this.keyData=i,this.decrypter=new ht(t,{removePKCS7Padding:!1})}decryptBuffer(e){return this.decrypter.decrypt(e,this.keyData.key.buffer,this.keyData.iv.buffer)}decryptAacSample(e,t,i){let r=e[t].unit;if(r.length<=16)return;let n=r.subarray(16,r.length-r.length%16),o=n.buffer.slice(n.byteOffset,n.byteOffset+n.length);this.decryptBuffer(o).then(a=>{let l=new Uint8Array(a);r.set(l,16),this.decrypter.isSync()||this.decryptAacSamples(e,t+1,i)})}decryptAacSamples(e,t,i){for(;;t++){if(t>=e.length){i();return}if(!(e[t].unit.length<32)&&(this.decryptAacSample(e,t,i),!this.decrypter.isSync()))return}}getAvcEncryptedData(e){let t=Math.floor((e.length-48)/160)*16+16,i=new Int8Array(t),r=0;for(let n=32;n<e.length-16;n+=160,r+=16)i.set(e.subarray(n,n+16),r);return i}getAvcDecryptedUnit(e,t){let i=new Uint8Array(t),r=0;for(let n=32;n<e.length-16;n+=160,r+=16)e.set(i.subarray(r,r+16),n);return e}decryptAvcSample(e,t,i,r,n){let o=sn(n.data),a=this.getAvcEncryptedData(o);this.decryptBuffer(a.buffer).then(l=>{n.data=this.getAvcDecryptedUnit(o,l),this.decrypter.isSync()||this.decryptAvcSamples(e,t,i+1,r)})}decryptAvcSamples(e,t,i,r){if(e instanceof Uint8Array)throw new Error("Cannot decrypt samples of type Uint8Array");for(;;t++,i=0){if(t>=e.length){r();return}let n=e[t].units;for(;!(i>=n.length);i++){let o=n[i];if(!(o.data.length<=48||o.type!==1&&o.type!==5)&&(this.decryptAvcSample(e,t,i,r,o),!this.decrypter.isSync()))return}}}},ae=188,Ji=class s{constructor(e,t,i){this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.sampleAes=null,this.pmtParsed=!1,this.audioCodec=void 0,this.videoCodec=void 0,this._duration=0,this._pmtId=-1,this._videoTrack=void 0,this._audioTrack=void 0,this._id3Track=void 0,this._txtTrack=void 0,this.aacOverFlow=null,this.remainderData=null,this.videoParser=void 0,this.observer=e,this.config=t,this.typeSupported=i,this.videoParser=new Xi}static probe(e){let t=s.syncOffset(e);return t>0&&S.warn(`MPEG2-TS detected but first sync word found @ offset ${t}`),t!==-1}static syncOffset(e){let t=e.length,i=Math.min(ae*5,t-ae)+1,r=0;for(;r<i;){let n=!1,o=-1,a=0;for(let l=r;l<t;l+=ae)if(e[l]===71&&(t-l===ae||e[l+ae]===71)){if(a++,o===-1&&(o=l,o!==0&&(i=Math.min(o+ae*99,e.length-ae)+1)),n||(n=Zi(e,l)===0),n&&a>1&&(o===0&&a>2||l+ae>i))return o}else{if(a)return-1;break}r++}return-1}static createTrack(e,t){return{container:e==="video"||e==="audio"?"video/mp2t":void 0,type:e,id:Xr[e],pid:-1,inputTimeScale:9e4,sequenceNumber:0,samples:[],dropped:0,duration:e==="audio"?t:void 0}}resetInitSegment(e,t,i,r){this.pmtParsed=!1,this._pmtId=-1,this._videoTrack=s.createTrack("video"),this._audioTrack=s.createTrack("audio",r),this._id3Track=s.createTrack("id3"),this._txtTrack=s.createTrack("text"),this._audioTrack.segmentCodec="aac",this.aacOverFlow=null,this.remainderData=null,this.audioCodec=t,this.videoCodec=i,this._duration=r}resetTimeStamp(){}resetContiguity(){let{_audioTrack:e,_videoTrack:t,_id3Track:i}=this;e&&(e.pesData=null),t&&(t.pesData=null),i&&(i.pesData=null),this.aacOverFlow=null,this.remainderData=null}demux(e,t,i=!1,r=!1){i||(this.sampleAes=null);let n,o=this._videoTrack,a=this._audioTrack,l=this._id3Track,c=this._txtTrack,h=o.pid,u=o.pesData,d=a.pid,f=l.pid,g=a.pesData,m=l.pesData,y=null,T=this.pmtParsed,v=this._pmtId,E=e.length;if(this.remainderData&&(e=Te(this.remainderData,e),E=e.length,this.remainderData=null),E<ae&&!r)return this.remainderData=e,{audioTrack:a,videoTrack:o,id3Track:l,textTrack:c};let _=Math.max(0,s.syncOffset(e));E-=(E-_)%ae,E<e.byteLength&&!r&&(this.remainderData=new Uint8Array(e.buffer,E,e.buffer.byteLength-E));let x=0;for(let L=_;L<E;L+=ae)if(e[L]===71){let C=!!(e[L+1]&64),k=Zi(e,L),R=(e[L+3]&48)>>4,D;if(R>1){if(D=L+5+e[L+4],D===L+ae)continue}else D=L+4;switch(k){case h:C&&(u&&(n=Ke(u))&&this.videoParser.parseAVCPES(o,c,n,!1,this._duration),u={data:[],size:0}),u&&(u.data.push(e.subarray(D,L+ae)),u.size+=L+ae-D);break;case d:if(C){if(g&&(n=Ke(g)))switch(a.segmentCodec){case"aac":this.parseAACPES(a,n);break;case"mp3":this.parseMPEGPES(a,n);break;case"ac3":this.parseAC3PES(a,n);break}g={data:[],size:0}}g&&(g.data.push(e.subarray(D,L+ae)),g.size+=L+ae-D);break;case f:C&&(m&&(n=Ke(m))&&this.parseID3PES(l,n),m={data:[],size:0}),m&&(m.data.push(e.subarray(D,L+ae)),m.size+=L+ae-D);break;case 0:C&&(D+=e[D]+1),v=this._pmtId=Xa(e,D);break;case v:{C&&(D+=e[D]+1);let V=Qa(e,D,this.typeSupported,i);h=V.videoPid,h>0&&(o.pid=h,o.segmentCodec=V.segmentVideoCodec),d=V.audioPid,d>0&&(a.pid=d,a.segmentCodec=V.segmentAudioCodec),f=V.id3Pid,f>0&&(l.pid=f),y!==null&&!T&&(S.warn(`MPEG-TS PMT found at ${L} after unknown PID '${y}'. Backtracking to sync byte @${_} to parse all TS packets.`),y=null,L=_-188),T=this.pmtParsed=!0;break}case 17:case 8191:break;default:y=k;break}}else x++;if(x>0){let L=new Error(`Found ${x} TS packet/s that do not start with 0x47`);this.observer.emit(p.ERROR,p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_PARSING_ERROR,fatal:!1,error:L,reason:L.message})}o.pesData=u,a.pesData=g,l.pesData=m;let I={audioTrack:a,videoTrack:o,id3Track:l,textTrack:c};return r&&this.extractRemainingSamples(I),I}flush(){let{remainderData:e}=this;this.remainderData=null;let t;return e?t=this.demux(e,-1,!1,!0):t={videoTrack:this._videoTrack,audioTrack:this._audioTrack,id3Track:this._id3Track,textTrack:this._txtTrack},this.extractRemainingSamples(t),this.sampleAes?this.decrypt(t,this.sampleAes):t}extractRemainingSamples(e){let{audioTrack:t,videoTrack:i,id3Track:r,textTrack:n}=e,o=i.pesData,a=t.pesData,l=r.pesData,c;if(o&&(c=Ke(o))?(this.videoParser.parseAVCPES(i,n,c,!0,this._duration),i.pesData=null):i.pesData=o,a&&(c=Ke(a))){switch(t.segmentCodec){case"aac":this.parseAACPES(t,c);break;case"mp3":this.parseMPEGPES(t,c);break;case"ac3":this.parseAC3PES(t,c);break}t.pesData=null}else a!=null&&a.size&&S.log("last AAC PES packet truncated,might overlap between fragments"),t.pesData=a;l&&(c=Ke(l))?(this.parseID3PES(r,c),r.pesData=null):r.pesData=l}demuxSampleAes(e,t,i){let r=this.demux(e,i,!0,!this.config.progressive),n=this.sampleAes=new Qi(this.observer,this.config,t);return this.decrypt(r,n)}decrypt(e,t){return new Promise(i=>{let{audioTrack:r,videoTrack:n}=e;r.samples&&r.segmentCodec==="aac"?t.decryptAacSamples(r.samples,0,()=>{n.samples?t.decryptAvcSamples(n.samples,0,0,()=>{i(e)}):i(e)}):n.samples&&t.decryptAvcSamples(n.samples,0,0,()=>{i(e)})})}destroy(){this._duration=0}parseAACPES(e,t){let i=0,r=this.aacOverFlow,n=t.data;if(r){this.aacOverFlow=null;let u=r.missing,d=r.sample.unit.byteLength;if(u===-1)n=Te(r.sample.unit,n);else{let f=d-u;r.sample.unit.set(n.subarray(0,u),f),e.samples.push(r.sample),i=r.missing}}let o,a;for(o=i,a=n.length;o<a-1&&!Kt(n,o);o++);if(o!==i){let u,d=o<a-1;d?u=`AAC PES did not start with ADTS header,offset:${o}`:u="No ADTS header found in AAC PES";let f=new Error(u);if(S.warn(`parsing error: ${u}`),this.observer.emit(p.ERROR,p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_PARSING_ERROR,fatal:!1,levelRetry:d,error:f,reason:u}),!d)return}yn(e,this.observer,n,o,this.audioCodec);let l;if(t.pts!==void 0)l=t.pts;else if(r){let u=Tn(e.samplerate);l=r.sample.pts+u}else{S.warn("[tsdemuxer]: AAC PES unknown PTS");return}let c=0,h;for(;o<a;)if(h=En(e,n,o,l,c),o+=h.length,h.missing){this.aacOverFlow=h;break}else for(c++;o<a-1&&!Kt(n,o);o++);}parseMPEGPES(e,t){let i=t.data,r=i.length,n=0,o=0,a=t.pts;if(a===void 0){S.warn("[tsdemuxer]: MPEG PES unknown PTS");return}for(;o<r;)if(Sn(i,o)){let l=vn(e,i,o,a,n);if(l)o+=l.length,n++;else break}else o++}parseAC3PES(e,t){{let i=t.data,r=t.pts;if(r===void 0){S.warn("[tsdemuxer]: AC3 PES unknown PTS");return}let n=i.length,o=0,a=0,l;for(;a<n&&(l=Ln(e,i,a,r,o++))>0;)a+=l}}parseID3PES(e,t){if(t.pts===void 0){S.warn("[tsdemuxer]: ID3 PES unknown PTS");return}let i=se({},t,{type:this._videoTrack?Se.emsg:Se.audioId3,duration:Number.POSITIVE_INFINITY});e.samples.push(i)}};function Zi(s,e){return((s[e+1]&31)<<8)+s[e+2]}function Xa(s,e){return(s[e+10]&31)<<8|s[e+11]}function Qa(s,e,t,i){let r={audioPid:-1,videoPid:-1,id3Pid:-1,segmentVideoCodec:"avc",segmentAudioCodec:"aac"},n=(s[e+1]&15)<<8|s[e+2],o=e+3+n-4,a=(s[e+10]&15)<<8|s[e+11];for(e+=12+a;e<o;){let l=Zi(s,e),c=(s[e+3]&15)<<8|s[e+4];switch(s[e]){case 207:if(!i){gi("ADTS AAC");break}case 15:r.audioPid===-1&&(r.audioPid=l);break;case 21:r.id3Pid===-1&&(r.id3Pid=l);break;case 219:if(!i){gi("H.264");break}case 27:r.videoPid===-1&&(r.videoPid=l,r.segmentVideoCodec="avc");break;case 3:case 4:!t.mpeg&&!t.mp3?S.log("MPEG audio found, not supported in this browser"):r.audioPid===-1&&(r.audioPid=l,r.segmentAudioCodec="mp3");break;case 193:if(!i){gi("AC-3");break}case 129:t.ac3?r.audioPid===-1&&(r.audioPid=l,r.segmentAudioCodec="ac3"):S.log("AC-3 audio found, not supported in this browser");break;case 6:if(r.audioPid===-1&&c>0){let h=e+5,u=c;for(;u>2;){switch(s[h]){case 106:t.ac3!==!0?S.log("AC-3 audio found, not supported in this browser for now"):(r.audioPid=l,r.segmentAudioCodec="ac3");break}let f=s[h+1]+2;h+=f,u-=f}}break;case 194:case 135:S.warn("Unsupported EC-3 in M2TS found");break;case 36:S.warn("Unsupported HEVC in M2TS found");break}e+=c+5}return r}function gi(s){S.log(`${s} with AES-128-CBC encryption found in unencrypted stream`)}function Ke(s){let e=0,t,i,r,n,o,a=s.data;if(!s||s.size===0)return null;for(;a[0].length<19&&a.length>1;)a[0]=Te(a[0],a[1]),a.splice(1,1);if(t=a[0],(t[0]<<16)+(t[1]<<8)+t[2]===1){if(i=(t[4]<<8)+t[5],i&&i>s.size-6)return null;let c=t[7];c&192&&(n=(t[9]&14)*536870912+(t[10]&255)*4194304+(t[11]&254)*16384+(t[12]&255)*128+(t[13]&254)/2,c&64?(o=(t[14]&14)*536870912+(t[15]&255)*4194304+(t[16]&254)*16384+(t[17]&255)*128+(t[18]&254)/2,n-o>60*9e4&&(S.warn(`${Math.round((n-o)/9e4)}s delta between PTS and DTS, align them`),n=o)):o=n),r=t[8];let h=r+9;if(s.size<=h)return null;s.size-=h;let u=new Uint8Array(s.size);for(let d=0,f=a.length;d<f;d++){t=a[d];let g=t.byteLength;if(h)if(h>g){h-=g;continue}else t=t.subarray(h),g-=h,h=0;u.set(t,e),e+=g}return i&&(i-=r+3),{data:u,pts:n,dts:o,len:i}}return null}var es=class extends dt{resetInitSegment(e,t,i,r){super.resetInitSegment(e,t,i,r),this._audioTrack={container:"audio/mpeg",type:"audio",id:2,pid:-1,sequenceNumber:0,segmentCodec:"mp3",samples:[],manifestCodec:t,duration:r,inputTimeScale:9e4,dropped:0}}static probe(e){if(!e)return!1;let t=nt(e,0),i=(t==null?void 0:t.length)||0;if(t&&e[i]===11&&e[i+1]===119&&Fs(t)!==void 0&&An(e,i)<=16)return!1;for(let r=e.length;i<r;i++)if(bn(e,i))return S.log("MPEG Audio sync word found !"),!0;return!1}canParse(e,t){return ja(e,t)}appendFrame(e,t,i){if(this.basePTS!==null)return vn(e,t,i,this.basePTS,this.frameIndex)}},Yt=class{static getSilentFrame(e,t){switch(e){case"mp4a.40.2":if(t===1)return new Uint8Array([0,200,0,128,35,128]);if(t===2)return new Uint8Array([33,0,73,144,2,25,0,35,128]);if(t===3)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,142]);if(t===4)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,128,44,128,8,2,56]);if(t===5)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,56]);if(t===6)return new Uint8Array([0,200,0,128,32,132,1,38,64,8,100,0,130,48,4,153,0,33,144,2,0,178,0,32,8,224]);break;default:if(t===1)return new Uint8Array([1,64,34,128,163,78,230,128,186,8,0,0,0,28,6,241,193,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(t===2)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);if(t===3)return new Uint8Array([1,64,34,128,163,94,230,128,186,8,0,0,0,0,149,0,6,241,161,10,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,90,94]);break}}},ke=Math.pow(2,32)-1,ne=class s{static init(){s.types={avc1:[],avcC:[],btrt:[],dinf:[],dref:[],esds:[],ftyp:[],hdlr:[],mdat:[],mdhd:[],mdia:[],mfhd:[],minf:[],moof:[],moov:[],mp4a:[],".mp3":[],dac3:[],"ac-3":[],mvex:[],mvhd:[],pasp:[],sdtp:[],stbl:[],stco:[],stsc:[],stsd:[],stsz:[],stts:[],tfdt:[],tfhd:[],traf:[],trak:[],trun:[],trex:[],tkhd:[],vmhd:[],smhd:[]};let e;for(e in s.types)s.types.hasOwnProperty(e)&&(s.types[e]=[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]);let t=new Uint8Array([0,0,0,0,0,0,0,0,118,105,100,101,0,0,0,0,0,0,0,0,0,0,0,0,86,105,100,101,111,72,97,110,100,108,101,114,0]),i=new Uint8Array([0,0,0,0,0,0,0,0,115,111,117,110,0,0,0,0,0,0,0,0,0,0,0,0,83,111,117,110,100,72,97,110,100,108,101,114,0]);s.HDLR_TYPES={video:t,audio:i};let r=new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,12,117,114,108,32,0,0,0,1]),n=new Uint8Array([0,0,0,0,0,0,0,0]);s.STTS=s.STSC=s.STCO=n,s.STSZ=new Uint8Array([0,0,0,0,0,0,0,0,0,0,0,0]),s.VMHD=new Uint8Array([0,0,0,1,0,0,0,0,0,0,0,0]),s.SMHD=new Uint8Array([0,0,0,0,0,0,0,0]),s.STSD=new Uint8Array([0,0,0,0,0,0,0,1]);let o=new Uint8Array([105,115,111,109]),a=new Uint8Array([97,118,99,49]),l=new Uint8Array([0,0,0,1]);s.FTYP=s.box(s.types.ftyp,o,l,o,a),s.DINF=s.box(s.types.dinf,s.box(s.types.dref,r))}static box(e,...t){let i=8,r=t.length,n=r;for(;r--;)i+=t[r].byteLength;let o=new Uint8Array(i);for(o[0]=i>>24&255,o[1]=i>>16&255,o[2]=i>>8&255,o[3]=i&255,o.set(e,4),r=0,i=8;r<n;r++)o.set(t[r],i),i+=t[r].byteLength;return o}static hdlr(e){return s.box(s.types.hdlr,s.HDLR_TYPES[e])}static mdat(e){return s.box(s.types.mdat,e)}static mdhd(e,t){t*=e;let i=Math.floor(t/(ke+1)),r=Math.floor(t%(ke+1));return s.box(s.types.mdhd,new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,e&255,i>>24,i>>16&255,i>>8&255,i&255,r>>24,r>>16&255,r>>8&255,r&255,85,196,0,0]))}static mdia(e){return s.box(s.types.mdia,s.mdhd(e.timescale,e.duration),s.hdlr(e.type),s.minf(e))}static mfhd(e){return s.box(s.types.mfhd,new Uint8Array([0,0,0,0,e>>24,e>>16&255,e>>8&255,e&255]))}static minf(e){return e.type==="audio"?s.box(s.types.minf,s.box(s.types.smhd,s.SMHD),s.DINF,s.stbl(e)):s.box(s.types.minf,s.box(s.types.vmhd,s.VMHD),s.DINF,s.stbl(e))}static moof(e,t,i){return s.box(s.types.moof,s.mfhd(e),s.traf(i,t))}static moov(e){let t=e.length,i=[];for(;t--;)i[t]=s.trak(e[t]);return s.box.apply(null,[s.types.moov,s.mvhd(e[0].timescale,e[0].duration)].concat(i).concat(s.mvex(e)))}static mvex(e){let t=e.length,i=[];for(;t--;)i[t]=s.trex(e[t]);return s.box.apply(null,[s.types.mvex,...i])}static mvhd(e,t){t*=e;let i=Math.floor(t/(ke+1)),r=Math.floor(t%(ke+1)),n=new Uint8Array([1,0,0,0,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,e>>24&255,e>>16&255,e>>8&255,e&255,i>>24,i>>16&255,i>>8&255,i&255,r>>24,r>>16&255,r>>8&255,r&255,0,1,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,255,255,255,255]);return s.box(s.types.mvhd,n)}static sdtp(e){let t=e.samples||[],i=new Uint8Array(4+t.length),r,n;for(r=0;r<t.length;r++)n=t[r].flags,i[r+4]=n.dependsOn<<4|n.isDependedOn<<2|n.hasRedundancy;return s.box(s.types.sdtp,i)}static stbl(e){return s.box(s.types.stbl,s.stsd(e),s.box(s.types.stts,s.STTS),s.box(s.types.stsc,s.STSC),s.box(s.types.stsz,s.STSZ),s.box(s.types.stco,s.STCO))}static avc1(e){let t=[],i=[],r,n,o;for(r=0;r<e.sps.length;r++)n=e.sps[r],o=n.byteLength,t.push(o>>>8&255),t.push(o&255),t=t.concat(Array.prototype.slice.call(n));for(r=0;r<e.pps.length;r++)n=e.pps[r],o=n.byteLength,i.push(o>>>8&255),i.push(o&255),i=i.concat(Array.prototype.slice.call(n));let a=s.box(s.types.avcC,new Uint8Array([1,t[3],t[4],t[5],255,224|e.sps.length].concat(t).concat([e.pps.length]).concat(i))),l=e.width,c=e.height,h=e.pixelRatio[0],u=e.pixelRatio[1];return s.box(s.types.avc1,new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,l>>8&255,l&255,c>>8&255,c&255,0,72,0,0,0,72,0,0,0,0,0,0,0,1,18,100,97,105,108,121,109,111,116,105,111,110,47,104,108,115,46,106,115,0,0,0,0,0,0,0,0,0,0,0,0,0,0,24,17,17]),a,s.box(s.types.btrt,new Uint8Array([0,28,156,128,0,45,198,192,0,45,198,192])),s.box(s.types.pasp,new Uint8Array([h>>24,h>>16&255,h>>8&255,h&255,u>>24,u>>16&255,u>>8&255,u&255])))}static esds(e){let t=e.config.length;return new Uint8Array([0,0,0,0,3,23+t,0,1,0,4,15+t,64,21,0,0,0,0,0,0,0,0,0,0,0,5].concat([t]).concat(e.config).concat([6,1,2]))}static audioStsd(e){let t=e.samplerate;return new Uint8Array([0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,e.channelCount,0,16,0,0,0,0,t>>8&255,t&255,0,0])}static mp4a(e){return s.box(s.types.mp4a,s.audioStsd(e),s.box(s.types.esds,s.esds(e)))}static mp3(e){return s.box(s.types[".mp3"],s.audioStsd(e))}static ac3(e){return s.box(s.types["ac-3"],s.audioStsd(e),s.box(s.types.dac3,e.config))}static stsd(e){return e.type==="audio"?e.segmentCodec==="mp3"&&e.codec==="mp3"?s.box(s.types.stsd,s.STSD,s.mp3(e)):e.segmentCodec==="ac3"?s.box(s.types.stsd,s.STSD,s.ac3(e)):s.box(s.types.stsd,s.STSD,s.mp4a(e)):s.box(s.types.stsd,s.STSD,s.avc1(e))}static tkhd(e){let t=e.id,i=e.duration*e.timescale,r=e.width,n=e.height,o=Math.floor(i/(ke+1)),a=Math.floor(i%(ke+1));return s.box(s.types.tkhd,new Uint8Array([1,0,0,7,0,0,0,0,0,0,0,2,0,0,0,0,0,0,0,3,t>>24&255,t>>16&255,t>>8&255,t&255,0,0,0,0,o>>24,o>>16&255,o>>8&255,o&255,a>>24,a>>16&255,a>>8&255,a&255,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0,64,0,0,0,r>>8&255,r&255,0,0,n>>8&255,n&255,0,0]))}static traf(e,t){let i=s.sdtp(e),r=e.id,n=Math.floor(t/(ke+1)),o=Math.floor(t%(ke+1));return s.box(s.types.traf,s.box(s.types.tfhd,new Uint8Array([0,0,0,0,r>>24,r>>16&255,r>>8&255,r&255])),s.box(s.types.tfdt,new Uint8Array([1,0,0,0,n>>24,n>>16&255,n>>8&255,n&255,o>>24,o>>16&255,o>>8&255,o&255])),s.trun(e,i.length+16+20+8+16+8+8),i)}static trak(e){return e.duration=e.duration||4294967295,s.box(s.types.trak,s.tkhd(e),s.mdia(e))}static trex(e){let t=e.id;return s.box(s.types.trex,new Uint8Array([0,0,0,0,t>>24,t>>16&255,t>>8&255,t&255,0,0,0,1,0,0,0,0,0,0,0,0,0,1,0,1]))}static trun(e,t){let i=e.samples||[],r=i.length,n=12+16*r,o=new Uint8Array(n),a,l,c,h,u,d;for(t+=8+n,o.set([e.type==="video"?1:0,0,15,1,r>>>24&255,r>>>16&255,r>>>8&255,r&255,t>>>24&255,t>>>16&255,t>>>8&255,t&255],0),a=0;a<r;a++)l=i[a],c=l.duration,h=l.size,u=l.flags,d=l.cts,o.set([c>>>24&255,c>>>16&255,c>>>8&255,c&255,h>>>24&255,h>>>16&255,h>>>8&255,h&255,u.isLeading<<2|u.dependsOn,u.isDependedOn<<6|u.hasRedundancy<<4|u.paddingValue<<1|u.isNonSync,u.degradPrio&61440,u.degradPrio&15,d>>>24&255,d>>>16&255,d>>>8&255,d&255],12+16*a);return s.box(s.types.trun,o)}static initSegment(e){s.types||s.init();let t=s.moov(e);return Te(s.FTYP,t)}};ne.types=void 0;ne.HDLR_TYPES=void 0;ne.STTS=void 0;ne.STSC=void 0;ne.STCO=void 0;ne.STSZ=void 0;ne.VMHD=void 0;ne.SMHD=void 0;ne.STSD=void 0;ne.FTYP=void 0;ne.DINF=void 0;var _n=9e4;function $s(s,e,t=1,i=!1){let r=s*e*t;return i?Math.round(r):r}function Ja(s,e,t=1,i=!1){return $s(s,e,1/t,i)}function tt(s,e=!1){return $s(s,1e3,1/_n,e)}function Za(s,e=1){return $s(s,_n,1/e)}var el=10*1e3,Lr=1024,tl=1152,il=1536,Ve=null,pi=null,ze=class{constructor(e,t,i,r=""){if(this.observer=void 0,this.config=void 0,this.typeSupported=void 0,this.ISGenerated=!1,this._initPTS=null,this._initDTS=null,this.nextAvcDts=null,this.nextAudioPts=null,this.videoSampleDuration=null,this.isAudioContiguous=!1,this.isVideoContiguous=!1,this.videoTrackConfig=void 0,this.observer=e,this.config=t,this.typeSupported=i,this.ISGenerated=!1,Ve===null){let o=(navigator.userAgent||"").match(/Chrome\/(\d+)/i);Ve=o?parseInt(o[1]):0}if(pi===null){let n=navigator.userAgent.match(/Safari\/(\d+)/i);pi=n?parseInt(n[1]):0}}destroy(){this.config=this.videoTrackConfig=this._initPTS=this._initDTS=null}resetTimeStamp(e){S.log("[mp4-remuxer]: initPTS & initDTS reset"),this._initPTS=this._initDTS=e}resetNextTimestamp(){S.log("[mp4-remuxer]: reset next timestamp"),this.isVideoContiguous=!1,this.isAudioContiguous=!1}resetInitSegment(){S.log("[mp4-remuxer]: ISGenerated flag reset"),this.ISGenerated=!1,this.videoTrackConfig=void 0}getVideoStartPts(e){let t=!1,i=e.reduce((r,n)=>{let o=n.pts-r;return o<-4294967296?(t=!0,ye(r,n.pts)):o>0?r:n.pts},e[0].pts);return t&&S.debug("PTS rollover detected"),i}remux(e,t,i,r,n,o,a,l){let c,h,u,d,f,g,m=n,y=n,T=e.pid>-1,v=t.pid>-1,E=t.samples.length,_=e.samples.length>0,x=a&&E>0||E>1;if((!T||_)&&(!v||x)||this.ISGenerated||a){if(this.ISGenerated){var L,C,k,R;let K=this.videoTrackConfig;K&&(t.width!==K.width||t.height!==K.height||((L=t.pixelRatio)==null?void 0:L[0])!==((C=K.pixelRatio)==null?void 0:C[0])||((k=t.pixelRatio)==null?void 0:k[1])!==((R=K.pixelRatio)==null?void 0:R[1]))&&this.resetInitSegment()}else u=this.generateIS(e,t,n,o);let D=this.isVideoContiguous,V=-1,P;if(x&&(V=sl(t.samples),!D&&this.config.forceKeyFrameOnDiscontinuity))if(g=!0,V>0){S.warn(`[mp4-remuxer]: Dropped ${V} out of ${E} video samples due to a missing keyframe`);let K=this.getVideoStartPts(t.samples);t.samples=t.samples.slice(V),t.dropped+=V,y+=(t.samples[0].pts-K)/t.inputTimeScale,P=y}else V===-1&&(S.warn(`[mp4-remuxer]: No keyframe found out of ${E} video samples`),g=!1);if(this.ISGenerated){if(_&&x){let K=this.getVideoStartPts(t.samples),N=(ye(e.samples[0].pts,K)-K)/t.inputTimeScale;m+=Math.max(0,N),y+=Math.max(0,-N)}if(_){if(e.samplerate||(S.warn("[mp4-remuxer]: regenerate InitSegment as audio detected"),u=this.generateIS(e,t,n,o)),h=this.remuxAudio(e,m,this.isAudioContiguous,o,v||x||l===$.AUDIO?y:void 0),x){let K=h?h.endPTS-h.startPTS:0;t.inputTimeScale||(S.warn("[mp4-remuxer]: regenerate InitSegment as video detected"),u=this.generateIS(e,t,n,o)),c=this.remuxVideo(t,y,D,K)}}else x&&(c=this.remuxVideo(t,y,D,0));c&&(c.firstKeyFrame=V,c.independent=V!==-1,c.firstKeyFramePTS=P)}}return this.ISGenerated&&this._initPTS&&this._initDTS&&(i.samples.length&&(f=Rn(i,n,this._initPTS,this._initDTS)),r.samples.length&&(d=In(r,n,this._initPTS))),{audio:h,video:c,initSegment:u,independent:g,text:d,id3:f}}generateIS(e,t,i,r){let n=e.samples,o=t.samples,a=this.typeSupported,l={},c=this._initPTS,h=!c||r,u="audio/mp4",d,f,g;if(h&&(d=f=1/0),e.config&&n.length){switch(e.timescale=e.samplerate,e.segmentCodec){case"mp3":a.mpeg?(u="audio/mpeg",e.codec=""):a.mp3&&(e.codec="mp3");break;case"ac3":e.codec="ac-3";break}l.audio={id:"audio",container:u,codec:e.codec,initSegment:e.segmentCodec==="mp3"&&a.mpeg?new Uint8Array(0):ne.initSegment([e]),metadata:{channelCount:e.channelCount}},h&&(g=e.inputTimeScale,!c||g!==c.timescale?d=f=n[0].pts-Math.round(g*i):h=!1)}if(t.sps&&t.pps&&o.length){if(t.timescale=t.inputTimeScale,l.video={id:"main",container:"video/mp4",codec:t.codec,initSegment:ne.initSegment([t]),metadata:{width:t.width,height:t.height}},h)if(g=t.inputTimeScale,!c||g!==c.timescale){let m=this.getVideoStartPts(o),y=Math.round(g*i);f=Math.min(f,ye(o[0].dts,m)-y),d=Math.min(d,m-y)}else h=!1;this.videoTrackConfig={width:t.width,height:t.height,pixelRatio:t.pixelRatio}}if(Object.keys(l).length)return this.ISGenerated=!0,h?(this._initPTS={baseTime:d,timescale:g},this._initDTS={baseTime:f,timescale:g}):d=g=void 0,{tracks:l,initPTS:d,timescale:g}}remuxVideo(e,t,i,r){let n=e.inputTimeScale,o=e.samples,a=[],l=o.length,c=this._initPTS,h=this.nextAvcDts,u=8,d=this.videoSampleDuration,f,g,m=Number.POSITIVE_INFINITY,y=Number.NEGATIVE_INFINITY,T=!1;if(!i||h===null){let F=t*n,O=o[0].pts-ye(o[0].dts,o[0].pts);Ve&&h!==null&&Math.abs(F-O-h)<15e3?i=!0:h=F-O}let v=c.baseTime*n/c.timescale;for(let F=0;F<l;F++){let O=o[F];O.pts=ye(O.pts-v,h),O.dts=ye(O.dts-v,h),O.dts<o[F>0?F-1:F].dts&&(T=!0)}T&&o.sort(function(F,O){let z=F.dts-O.dts,W=F.pts-O.pts;return z||W}),f=o[0].dts,g=o[o.length-1].dts;let E=g-f,_=E?Math.round(E/(l-1)):d||e.inputTimeScale/30;if(i){let F=f-h,O=F>_,z=F<-1;if((O||z)&&(O?S.warn(`AVC: ${tt(F,!0)} ms (${F}dts) hole between fragments detected at ${t.toFixed(3)}`):S.warn(`AVC: ${tt(-F,!0)} ms (${F}dts) overlapping between fragments detected at ${t.toFixed(3)}`),!z||h>=o[0].pts||Ve)){f=h;let W=o[0].pts-F;if(O)o[0].dts=f,o[0].pts=W;else for(let Q=0;Q<o.length&&!(o[Q].dts>W);Q++)o[Q].dts-=F,o[Q].pts-=F;S.log(`Video: Initial PTS/DTS adjusted: ${tt(W,!0)}/${tt(f,!0)}, delta: ${tt(F,!0)} ms`)}}f=Math.max(0,f);let x=0,I=0,L=f;for(let F=0;F<l;F++){let O=o[F],z=O.units,W=z.length,Q=0;for(let ie=0;ie<W;ie++)Q+=z[ie].data.length;I+=Q,x+=W,O.length=Q,O.dts<L?(O.dts=L,L+=_/4|0||1):L=O.dts,m=Math.min(O.pts,m),y=Math.max(O.pts,y)}g=o[l-1].dts;let C=I+4*x+8,k;try{k=new Uint8Array(C)}catch(F){this.observer.emit(p.ERROR,p.ERROR,{type:G.MUX_ERROR,details:b.REMUX_ALLOC_ERROR,fatal:!1,error:F,bytes:C,reason:`fail allocating video mdat ${C}`});return}let R=new DataView(k.buffer);R.setUint32(0,C),k.set(ne.types.mdat,4);let D=!1,V=Number.POSITIVE_INFINITY,P=Number.POSITIVE_INFINITY,K=Number.NEGATIVE_INFINITY,H=Number.NEGATIVE_INFINITY;for(let F=0;F<l;F++){let O=o[F],z=O.units,W=0;for(let oe=0,ue=z.length;oe<ue;oe++){let me=z[oe],et=me.data,si=me.data.byteLength;R.setUint32(u,si),u+=4,k.set(et,u),u+=si,W+=4+si}let Q;if(F<l-1)d=o[F+1].dts-O.dts,Q=o[F+1].pts-O.pts;else{let oe=this.config,ue=F>0?O.dts-o[F-1].dts:_;if(Q=F>0?O.pts-o[F-1].pts:_,oe.stretchShortVideoTrack&&this.nextAudioPts!==null){let me=Math.floor(oe.maxBufferHole*n),et=(r?m+r*n:this.nextAudioPts)-O.pts;et>me?(d=et-ue,d<0?d=ue:D=!0,S.log(`[mp4-remuxer]: It is approximately ${et/90} ms to the next segment; using duration ${d/90} ms for the last video frame.`)):d=ue}else d=ue}let ie=Math.round(O.pts-O.dts);V=Math.min(V,d),K=Math.max(K,d),P=Math.min(P,Q),H=Math.max(H,Q),a.push(new Wt(O.key,d,W,ie))}if(a.length){if(Ve){if(Ve<70){let F=a[0].flags;F.dependsOn=2,F.isNonSync=0}}else if(pi&&H-P<K-V&&_/K<.025&&a[0].cts===0){S.warn("Found irregular gaps in sample duration. Using PTS instead of DTS to determine MP4 sample duration.");let F=f;for(let O=0,z=a.length;O<z;O++){let W=F+a[O].duration,Q=F+a[O].cts;if(O<z-1){let ie=W+a[O+1].cts;a[O].duration=ie-Q}else a[O].duration=O?a[O-1].duration:_;a[O].cts=0,F=W}}}d=D||!d?_:d,this.nextAvcDts=h=g+d,this.videoSampleDuration=d,this.isVideoContiguous=!0;let X={data1:ne.moof(e.sequenceNumber++,f,se({},e,{samples:a})),data2:k,startPTS:m/n,endPTS:(y+d)/n,startDTS:f/n,endDTS:h/n,type:"video",hasAudio:!1,hasVideo:!0,nb:a.length,dropped:e.dropped};return e.samples=[],e.dropped=0,X}getSamplesPerFrame(e){switch(e.segmentCodec){case"mp3":return tl;case"ac3":return il;default:return Lr}}remuxAudio(e,t,i,r,n){let o=e.inputTimeScale,a=e.samplerate?e.samplerate:o,l=o/a,c=this.getSamplesPerFrame(e),h=c*l,u=this._initPTS,d=e.segmentCodec==="mp3"&&this.typeSupported.mpeg,f=[],g=n!==void 0,m=e.samples,y=d?0:8,T=this.nextAudioPts||-1,v=t*o,E=u.baseTime*o/u.timescale;if(this.isAudioContiguous=i=i||m.length&&T>0&&(r&&Math.abs(v-T)<9e3||Math.abs(ye(m[0].pts-E,v)-T)<20*h),m.forEach(function(N){N.pts=ye(N.pts-E,v)}),!i||T<0){if(m=m.filter(N=>N.pts>=0),!m.length)return;n===0?T=0:r&&!g?T=Math.max(0,v):T=m[0].pts}if(e.segmentCodec==="aac"){let N=this.config.maxAudioFramesDrift;for(let q=0,X=T;q<m.length;q++){let F=m[q],O=F.pts,z=O-X,W=Math.abs(1e3*z/o);if(z<=-N*h&&g)q===0&&(S.warn(`Audio frame @ ${(O/o).toFixed(3)}s overlaps nextAudioPts by ${Math.round(1e3*z/o)} ms.`),this.nextAudioPts=T=X=O);else if(z>=N*h&&W<el&&g){let Q=Math.round(z/h);X=O-Q*h,X<0&&(Q--,X+=h),q===0&&(this.nextAudioPts=T=X),S.warn(`[mp4-remuxer]: Injecting ${Q} audio frame @ ${(X/o).toFixed(3)}s due to ${Math.round(1e3*z/o)} ms gap.`);for(let ie=0;ie<Q;ie++){let oe=Math.max(X,0),ue=Yt.getSilentFrame(e.manifestCodec||e.codec,e.channelCount);ue||(S.log("[mp4-remuxer]: Unable to get silent frame for given audio codec; duplicating last frame instead."),ue=F.unit.subarray()),m.splice(q,0,{unit:ue,pts:oe}),X+=h,q++}}F.pts=X,X+=h}}let _=null,x=null,I,L=0,C=m.length;for(;C--;)L+=m[C].unit.byteLength;for(let N=0,q=m.length;N<q;N++){let X=m[N],F=X.unit,O=X.pts;if(x!==null){let W=f[N-1];W.duration=Math.round((O-x)/l)}else if(i&&e.segmentCodec==="aac"&&(O=T),_=O,L>0){L+=y;try{I=new Uint8Array(L)}catch(W){this.observer.emit(p.ERROR,p.ERROR,{type:G.MUX_ERROR,details:b.REMUX_ALLOC_ERROR,fatal:!1,error:W,bytes:L,reason:`fail allocating audio mdat ${L}`});return}d||(new DataView(I.buffer).setUint32(0,L),I.set(ne.types.mdat,4))}else return;I.set(F,y);let z=F.byteLength;y+=z,f.push(new Wt(!0,c,z,0)),x=O}let k=f.length;if(!k)return;let R=f[f.length-1];this.nextAudioPts=T=x+l*R.duration;let D=d?new Uint8Array(0):ne.moof(e.sequenceNumber++,_/l,se({},e,{samples:f}));e.samples=[];let V=_/o,P=T/o,H={data1:D,data2:I,startPTS:V,endPTS:P,startDTS:V,endDTS:P,type:"audio",hasAudio:!0,hasVideo:!1,nb:k};return this.isAudioContiguous=!0,H}remuxEmptyAudio(e,t,i,r){let n=e.inputTimeScale,o=e.samplerate?e.samplerate:n,a=n/o,l=this.nextAudioPts,c=this._initDTS,h=c.baseTime*9e4/c.timescale,u=(l!==null?l:r.startDTS*n)+h,d=r.endDTS*n+h,f=a*Lr,g=Math.ceil((d-u)/f),m=Yt.getSilentFrame(e.manifestCodec||e.codec,e.channelCount);if(S.warn("[mp4-remuxer]: remux empty Audio"),!m){S.trace("[mp4-remuxer]: Unable to remuxEmptyAudio since we were unable to get a silent frame for given audio codec");return}let y=[];for(let T=0;T<g;T++){let v=u+T*f;y.push({unit:m,pts:v,dts:v})}return e.samples=y,this.remuxAudio(e,t,i,!1)}};function ye(s,e){let t;if(e===null)return s;for(e<s?t=-8589934592:t=8589934592;Math.abs(s-e)>4294967296;)s+=t;return s}function sl(s){for(let e=0;e<s.length;e++)if(s[e].key)return e;return-1}function Rn(s,e,t,i){let r=s.samples.length;if(!r)return;let n=s.inputTimeScale;for(let a=0;a<r;a++){let l=s.samples[a];l.pts=ye(l.pts-t.baseTime*n/t.timescale,e*n)/n,l.dts=ye(l.dts-i.baseTime*n/i.timescale,e*n)/n}let o=s.samples;return s.samples=[],{samples:o}}function In(s,e,t){let i=s.samples.length;if(!i)return;let r=s.inputTimeScale;for(let o=0;o<i;o++){let a=s.samples[o];a.pts=ye(a.pts-t.baseTime*r/t.timescale,e*r)/r}s.samples.sort((o,a)=>o.pts-a.pts);let n=s.samples;return s.samples=[],{samples:n}}var Wt=class{constructor(e,t,i,r){this.size=void 0,this.duration=void 0,this.cts=void 0,this.flags=void 0,this.duration=t,this.size=i,this.cts=r,this.flags={isLeading:0,isDependedOn:0,hasRedundancy:0,degradPrio:0,dependsOn:e?2:1,isNonSync:e?0:1}}},ts=class{constructor(){this.emitInitSegment=!1,this.audioCodec=void 0,this.videoCodec=void 0,this.initData=void 0,this.initPTS=null,this.initTracks=void 0,this.lastEndTime=null}destroy(){}resetTimeStamp(e){this.initPTS=e,this.lastEndTime=null}resetNextTimestamp(){this.lastEndTime=null}resetInitSegment(e,t,i,r){this.audioCodec=t,this.videoCodec=i,this.generateInitSegment(Do(e,r)),this.emitInitSegment=!0}generateInitSegment(e){let{audioCodec:t,videoCodec:i}=this;if(!(e!=null&&e.byteLength)){this.initTracks=void 0,this.initData=void 0;return}let r=this.initData=Zr(e);r.audio&&(t=_r(r.audio,J.AUDIO)),r.video&&(i=_r(r.video,J.VIDEO));let n={};r.audio&&r.video?n.audiovideo={container:"video/mp4",codec:t+","+i,initSegment:e,id:"main"}:r.audio?n.audio={container:"audio/mp4",codec:t,initSegment:e,id:"audio"}:r.video?n.video={container:"video/mp4",codec:i,initSegment:e,id:"main"}:S.warn("[passthrough-remuxer.ts]: initSegment does not contain moov or trak boxes."),this.initTracks=n}remux(e,t,i,r,n,o){var a,l;let{initPTS:c,lastEndTime:h}=this,u={audio:void 0,video:void 0,text:r,id3:i,initSegment:void 0};M(h)||(h=this.lastEndTime=n||0);let d=t.samples;if(!(d!=null&&d.length))return u;let f={initPTS:void 0,timescale:1},g=this.initData;if((a=g)!=null&&a.length||(this.generateInitSegment(d),g=this.initData),!((l=g)!=null&&l.length))return S.warn("[passthrough-remuxer.ts]: Failed to generate initSegment."),u;this.emitInitSegment&&(f.tracks=this.initTracks,this.emitInitSegment=!1);let m=Po(d,g),y=ko(g,d),T=y===null?n:y;(rl(c,T,n,m)||f.timescale!==c.timescale&&o)&&(f.initPTS=T-n,c&&c.timescale===1&&S.warn(`Adjusting initPTS by ${f.initPTS-c.baseTime}`),this.initPTS=c={baseTime:f.initPTS,timescale:1});let v=e?T-c.baseTime/c.timescale:h,E=v+m;Mo(g,d,c.baseTime/c.timescale),m>0?this.lastEndTime=E:(S.warn("Duration parsed from mp4 should be greater than zero"),this.resetNextTimestamp());let _=!!g.audio,x=!!g.video,I="";_&&(I+="audio"),x&&(I+="video");let L={data1:d,startPTS:v,startDTS:v,endPTS:E,endDTS:E,type:I,hasAudio:_,hasVideo:x,nb:1,dropped:0};return u.audio=L.type==="audio"?L:void 0,u.video=L.type!=="audio"?L:void 0,u.initSegment=f,u.id3=Rn(i,n,c,c),r.samples.length&&(u.text=In(r,n,c)),u}};function rl(s,e,t,i){if(s===null)return!0;let r=Math.max(i,1),n=e-s.baseTime/s.timescale;return Math.abs(n-t)>r}function _r(s,e){let t=s==null?void 0:s.codec;if(t&&t.length>4)return t;if(e===J.AUDIO){if(t==="ec-3"||t==="ac-3"||t==="alac")return t;if(t==="fLaC"||t==="Opus")return Mt(t,!1);let i="mp4a.40.5";return S.info(`Parsed audio codec "${t}" or audio object type not handled. Using "${i}"`),i}return S.warn(`Unhandled video codec "${t}"`),t==="hvc1"||t==="hev1"?"hvc1.1.6.L120.90":t==="av01"?"av01.0.04M.08":"avc1.42e01e"}var De;try{De=self.performance.now.bind(self.performance)}catch(s){S.debug("Unable to use Performance API on this environment"),De=Qe==null?void 0:Qe.Date.now}var _t=[{demux:qi,remux:ts},{demux:Ji,remux:ze},{demux:Wi,remux:ze},{demux:es,remux:ze}];_t.splice(2,0,{demux:ji,remux:ze});var qt=class{constructor(e,t,i,r,n){this.async=!1,this.observer=void 0,this.typeSupported=void 0,this.config=void 0,this.vendor=void 0,this.id=void 0,this.demuxer=void 0,this.remuxer=void 0,this.decrypter=void 0,this.probe=void 0,this.decryptionPromise=null,this.transmuxConfig=void 0,this.currentTransmuxState=void 0,this.observer=e,this.typeSupported=t,this.config=i,this.vendor=r,this.id=n}configure(e){this.transmuxConfig=e,this.decrypter&&this.decrypter.reset()}push(e,t,i,r){let n=i.transmuxing;n.executeStart=De();let o=new Uint8Array(e),{currentTransmuxState:a,transmuxConfig:l}=this;r&&(this.currentTransmuxState=r);let{contiguous:c,discontinuity:h,trackSwitch:u,accurateTimeOffset:d,timeOffset:f,initSegmentChange:g}=r||a,{audioCodec:m,videoCodec:y,defaultInitPts:T,duration:v,initSegmentData:E}=l,_=nl(o,t);if(_&&_.method==="AES-128"){let C=this.getDecrypter();if(C.isSync()){let k=C.softwareDecrypt(o,_.key.buffer,_.iv.buffer);if(i.part>-1&&(k=C.flush()),!k)return n.executeEnd=De(),mi(i);o=new Uint8Array(k)}else return this.decryptionPromise=C.webCryptoDecrypt(o,_.key.buffer,_.iv.buffer).then(k=>{let R=this.push(k,null,i);return this.decryptionPromise=null,R}),this.decryptionPromise}let x=this.needsProbing(h,u);if(x){let C=this.configureTransmuxer(o);if(C)return S.warn(`[transmuxer] ${C.message}`),this.observer.emit(p.ERROR,p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_PARSING_ERROR,fatal:!1,error:C,reason:C.message}),n.executeEnd=De(),mi(i)}(h||u||g||x)&&this.resetInitSegment(E,m,y,v,t),(h||g||x)&&this.resetInitialTimestamp(T),c||this.resetContiguity();let I=this.transmux(o,_,f,d,i),L=this.currentTransmuxState;return L.contiguous=!0,L.discontinuity=!1,L.trackSwitch=!1,n.executeEnd=De(),I}flush(e){let t=e.transmuxing;t.executeStart=De();let{decrypter:i,currentTransmuxState:r,decryptionPromise:n}=this;if(n)return n.then(()=>this.flush(e));let o=[],{timeOffset:a}=r;if(i){let u=i.flush();u&&o.push(this.push(u,null,e))}let{demuxer:l,remuxer:c}=this;if(!l||!c)return t.executeEnd=De(),[mi(e)];let h=l.flush(a);return Rt(h)?h.then(u=>(this.flushRemux(o,u,e),o)):(this.flushRemux(o,h,e),o)}flushRemux(e,t,i){let{audioTrack:r,videoTrack:n,id3Track:o,textTrack:a}=t,{accurateTimeOffset:l,timeOffset:c}=this.currentTransmuxState;S.log(`[transmuxer.ts]: Flushed fragment ${i.sn}${i.part>-1?" p: "+i.part:""} of level ${i.level}`);let h=this.remuxer.remux(r,n,o,a,c,l,!0,this.id);e.push({remuxResult:h,chunkMeta:i}),i.transmuxing.executeEnd=De()}resetInitialTimestamp(e){let{demuxer:t,remuxer:i}=this;!t||!i||(t.resetTimeStamp(e),i.resetTimeStamp(e))}resetContiguity(){let{demuxer:e,remuxer:t}=this;!e||!t||(e.resetContiguity(),t.resetNextTimestamp())}resetInitSegment(e,t,i,r,n){let{demuxer:o,remuxer:a}=this;!o||!a||(o.resetInitSegment(e,t,i,r),a.resetInitSegment(e,t,i,n))}destroy(){this.demuxer&&(this.demuxer.destroy(),this.demuxer=void 0),this.remuxer&&(this.remuxer.destroy(),this.remuxer=void 0)}transmux(e,t,i,r,n){let o;return t&&t.method==="SAMPLE-AES"?o=this.transmuxSampleAes(e,t,i,r,n):o=this.transmuxUnencrypted(e,i,r,n),o}transmuxUnencrypted(e,t,i,r){let{audioTrack:n,videoTrack:o,id3Track:a,textTrack:l}=this.demuxer.demux(e,t,!1,!this.config.progressive);return{remuxResult:this.remuxer.remux(n,o,a,l,t,i,!1,this.id),chunkMeta:r}}transmuxSampleAes(e,t,i,r,n){return this.demuxer.demuxSampleAes(e,t,i).then(o=>({remuxResult:this.remuxer.remux(o.audioTrack,o.videoTrack,o.id3Track,o.textTrack,i,r,!1,this.id),chunkMeta:n}))}configureTransmuxer(e){let{config:t,observer:i,typeSupported:r,vendor:n}=this,o;for(let d=0,f=_t.length;d<f;d++){var a;if((a=_t[d].demux)!=null&&a.probe(e)){o=_t[d];break}}if(!o)return new Error("Failed to find demuxer by probing fragment data");let l=this.demuxer,c=this.remuxer,h=o.remux,u=o.demux;(!c||!(c instanceof h))&&(this.remuxer=new h(i,t,r,n)),(!l||!(l instanceof u))&&(this.demuxer=new u(i,t,r),this.probe=u.probe)}needsProbing(e,t){return!this.demuxer||!this.remuxer||e||t}getDecrypter(){let e=this.decrypter;return e||(e=this.decrypter=new ht(this.config)),e}};function nl(s,e){let t=null;return s.byteLength>0&&(e==null?void 0:e.key)!=null&&e.iv!==null&&e.method!=null&&(t=e),t}var mi=s=>({remuxResult:{},chunkMeta:s});function Rt(s){return"then"in s&&s.then instanceof Function}var is=class{constructor(e,t,i,r,n){this.audioCodec=void 0,this.videoCodec=void 0,this.initSegmentData=void 0,this.duration=void 0,this.defaultInitPts=void 0,this.audioCodec=e,this.videoCodec=t,this.initSegmentData=i,this.duration=r,this.defaultInitPts=n||null}},ss=class{constructor(e,t,i,r,n,o){this.discontinuity=void 0,this.contiguous=void 0,this.accurateTimeOffset=void 0,this.trackSwitch=void 0,this.timeOffset=void 0,this.initSegmentChange=void 0,this.discontinuity=e,this.contiguous=t,this.accurateTimeOffset=i,this.trackSwitch=r,this.timeOffset=n,this.initSegmentChange=o}},wn={exports:{}};(function(s){var e=Object.prototype.hasOwnProperty,t="~";function i(){}Object.create&&(i.prototype=Object.create(null),new i().__proto__||(t=!1));function r(l,c,h){this.fn=l,this.context=c,this.once=h||!1}function n(l,c,h,u,d){if(typeof h!="function")throw new TypeError("The listener must be a function");var f=new r(h,u||l,d),g=t?t+c:c;return l._events[g]?l._events[g].fn?l._events[g]=[l._events[g],f]:l._events[g].push(f):(l._events[g]=f,l._eventsCount++),l}function o(l,c){--l._eventsCount===0?l._events=new i:delete l._events[c]}function a(){this._events=new i,this._eventsCount=0}a.prototype.eventNames=function(){var c=[],h,u;if(this._eventsCount===0)return c;for(u in h=this._events)e.call(h,u)&&c.push(t?u.slice(1):u);return Object.getOwnPropertySymbols?c.concat(Object.getOwnPropertySymbols(h)):c},a.prototype.listeners=function(c){var h=t?t+c:c,u=this._events[h];if(!u)return[];if(u.fn)return[u.fn];for(var d=0,f=u.length,g=new Array(f);d<f;d++)g[d]=u[d].fn;return g},a.prototype.listenerCount=function(c){var h=t?t+c:c,u=this._events[h];return u?u.fn?1:u.length:0},a.prototype.emit=function(c,h,u,d,f,g){var m=t?t+c:c;if(!this._events[m])return!1;var y=this._events[m],T=arguments.length,v,E;if(y.fn){switch(y.once&&this.removeListener(c,y.fn,void 0,!0),T){case 1:return y.fn.call(y.context),!0;case 2:return y.fn.call(y.context,h),!0;case 3:return y.fn.call(y.context,h,u),!0;case 4:return y.fn.call(y.context,h,u,d),!0;case 5:return y.fn.call(y.context,h,u,d,f),!0;case 6:return y.fn.call(y.context,h,u,d,f,g),!0}for(E=1,v=new Array(T-1);E<T;E++)v[E-1]=arguments[E];y.fn.apply(y.context,v)}else{var _=y.length,x;for(E=0;E<_;E++)switch(y[E].once&&this.removeListener(c,y[E].fn,void 0,!0),T){case 1:y[E].fn.call(y[E].context);break;case 2:y[E].fn.call(y[E].context,h);break;case 3:y[E].fn.call(y[E].context,h,u);break;case 4:y[E].fn.call(y[E].context,h,u,d);break;default:if(!v)for(x=1,v=new Array(T-1);x<T;x++)v[x-1]=arguments[x];y[E].fn.apply(y[E].context,v)}}return!0},a.prototype.on=function(c,h,u){return n(this,c,h,u,!1)},a.prototype.once=function(c,h,u){return n(this,c,h,u,!0)},a.prototype.removeListener=function(c,h,u,d){var f=t?t+c:c;if(!this._events[f])return this;if(!h)return o(this,f),this;var g=this._events[f];if(g.fn)g.fn===h&&(!d||g.once)&&(!u||g.context===u)&&o(this,f);else{for(var m=0,y=[],T=g.length;m<T;m++)(g[m].fn!==h||d&&!g[m].once||u&&g[m].context!==u)&&y.push(g[m]);y.length?this._events[f]=y.length===1?y[0]:y:o(this,f)}return this},a.prototype.removeAllListeners=function(c){var h;return c?(h=t?t+c:c,this._events[h]&&o(this,h)):(this._events=new i,this._eventsCount=0),this},a.prototype.off=a.prototype.removeListener,a.prototype.addListener=a.prototype.on,a.prefixed=t,a.EventEmitter=a,s.exports=a})(wn);var ol=wn.exports,Gs=Zn(ol),jt=class{constructor(e,t,i,r){this.error=null,this.hls=void 0,this.id=void 0,this.observer=void 0,this.frag=null,this.part=null,this.useWorker=void 0,this.workerContext=null,this.onwmsg=void 0,this.transmuxer=null,this.onTransmuxComplete=void 0,this.onFlush=void 0;let n=e.config;this.hls=e,this.id=t,this.useWorker=!!n.enableWorker,this.onTransmuxComplete=i,this.onFlush=r;let o=(h,u)=>{u=u||{},u.frag=this.frag,u.id=this.id,h===p.ERROR&&(this.error=u.error),this.hls.trigger(h,u)};this.observer=new Gs,this.observer.on(p.FRAG_DECRYPTED,o),this.observer.on(p.ERROR,o);let a=$e(n.preferManagedMediaSource)||{isTypeSupported:()=>!1},l={mpeg:a.isTypeSupported("audio/mpeg"),mp3:a.isTypeSupported('audio/mp4; codecs="mp3"'),ac3:a.isTypeSupported('audio/mp4; codecs="ac-3"')},c=navigator.vendor;if(this.useWorker&&typeof Worker!="undefined"&&(n.workerPath||Ma())){try{n.workerPath?(S.log(`loading Web Worker ${n.workerPath} for "${t}"`),this.workerContext=Na(n.workerPath)):(S.log(`injecting Web Worker for "${t}"`),this.workerContext=Fa()),this.onwmsg=d=>this.onWorkerMessage(d);let{worker:u}=this.workerContext;u.addEventListener("message",this.onwmsg),u.onerror=d=>{let f=new Error(`${d.message}  (${d.filename}:${d.lineno})`);n.enableWorker=!1,S.warn(`Error in "${t}" Web Worker, fallback to inline`),this.hls.trigger(p.ERROR,{type:G.OTHER_ERROR,details:b.INTERNAL_EXCEPTION,fatal:!1,event:"demuxerWorker",error:f})},u.postMessage({cmd:"init",typeSupported:l,vendor:c,id:t,config:JSON.stringify(n)})}catch(u){S.warn(`Error setting up "${t}" Web Worker, fallback to inline`,u),this.resetWorker(),this.error=null,this.transmuxer=new qt(this.observer,l,n,c,t)}return}this.transmuxer=new qt(this.observer,l,n,c,t)}resetWorker(){if(this.workerContext){let{worker:e,objectURL:t}=this.workerContext;t&&self.URL.revokeObjectURL(t),e.removeEventListener("message",this.onwmsg),e.onerror=null,e.terminate(),this.workerContext=null}}destroy(){if(this.workerContext)this.resetWorker(),this.onwmsg=void 0;else{let t=this.transmuxer;t&&(t.destroy(),this.transmuxer=null)}let e=this.observer;e&&e.removeAllListeners(),this.frag=null,this.observer=null,this.hls=null}push(e,t,i,r,n,o,a,l,c,h){var u,d;c.transmuxing.start=self.performance.now();let{transmuxer:f}=this,g=o?o.start:n.start,m=n.decryptdata,y=this.frag,T=!(y&&n.cc===y.cc),v=!(y&&c.level===y.level),E=y?c.sn-y.sn:-1,_=this.part?c.part-this.part.index:-1,x=E===0&&c.id>1&&c.id===(y==null?void 0:y.stats.chunkCount),I=!v&&(E===1||E===0&&(_===1||x&&_<=0)),L=self.performance.now();(v||E||n.stats.parsing.start===0)&&(n.stats.parsing.start=L),o&&(_||!I)&&(o.stats.parsing.start=L);let C=!(y&&((u=n.initSegment)==null?void 0:u.url)===((d=y.initSegment)==null?void 0:d.url)),k=new ss(T,I,l,v,g,C);if(!I||T||C){S.log(`[transmuxer-interface, ${n.type}]: Starting new transmux session for sn: ${c.sn} p: ${c.part} level: ${c.level} id: ${c.id}
        discontinuity: ${T}
        trackSwitch: ${v}
        contiguous: ${I}
        accurateTimeOffset: ${l}
        timeOffset: ${g}
        initSegmentChange: ${C}`);let R=new is(i,r,t,a,h);this.configureTransmuxer(R)}if(this.frag=n,this.part=o,this.workerContext)this.workerContext.worker.postMessage({cmd:"demux",data:e,decryptdata:m,chunkMeta:c,state:k},e instanceof ArrayBuffer?[e]:[]);else if(f){let R=f.push(e,m,c,k);Rt(R)?(f.async=!0,R.then(D=>{this.handleTransmuxComplete(D)}).catch(D=>{this.transmuxerError(D,c,"transmuxer-interface push error")})):(f.async=!1,this.handleTransmuxComplete(R))}}flush(e){e.transmuxing.start=self.performance.now();let{transmuxer:t}=this;if(this.workerContext)this.workerContext.worker.postMessage({cmd:"flush",chunkMeta:e});else if(t){let i=t.flush(e);Rt(i)||t.async?(Rt(i)||(i=Promise.resolve(i)),i.then(n=>{this.handleFlushResult(n,e)}).catch(n=>{this.transmuxerError(n,e,"transmuxer-interface flush error")})):this.handleFlushResult(i,e)}}transmuxerError(e,t,i){this.hls&&(this.error=e,this.hls.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_PARSING_ERROR,chunkMeta:t,fatal:!1,error:e,err:e,reason:i}))}handleFlushResult(e,t){e.forEach(i=>{this.handleTransmuxComplete(i)}),this.onFlush(t)}onWorkerMessage(e){let t=e.data,i=this.hls;switch(t.event){case"init":{var r;let n=(r=this.workerContext)==null?void 0:r.objectURL;n&&self.URL.revokeObjectURL(n);break}case"transmuxComplete":{this.handleTransmuxComplete(t.data);break}case"flush":{this.onFlush(t.data);break}case"workerLog":S[t.data.logType]&&S[t.data.logType](t.data.message);break;default:{t.data=t.data||{},t.data.frag=this.frag,t.data.id=this.id,i.trigger(t.event,t.data);break}}}configureTransmuxer(e){let{transmuxer:t}=this;this.workerContext?this.workerContext.worker.postMessage({cmd:"configure",config:e}):t&&t.configure(e)}handleTransmuxComplete(e){e.chunkMeta.transmuxing.end=self.performance.now(),this.onTransmuxComplete(e)}};function Cn(s,e){if(s.length!==e.length)return!1;for(let t=0;t<s.length;t++)if(!Je(s[t].attrs,e[t].attrs))return!1;return!0}function Je(s,e,t){let i=s["STABLE-RENDITION-ID"];return i&&!t?i===e["STABLE-RENDITION-ID"]:!(t||["LANGUAGE","NAME","CHARACTERISTICS","AUTOSELECT","DEFAULT","FORCED","ASSOC-LANGUAGE"]).some(r=>s[r]!==e[r])}function rs(s,e){return e.label.toLowerCase()===s.name.toLowerCase()&&(!e.language||e.language.toLowerCase()===(s.lang||"").toLowerCase())}var Rr=100,ns=class extends ut{constructor(e,t,i){super(e,t,i,"[audio-stream-controller]",$.AUDIO),this.videoBuffer=null,this.videoTrackCC=-1,this.waitingVideoCC=-1,this.bufferedTrack=null,this.switchingTrack=null,this.trackId=-1,this.waitingData=null,this.mainDetails=null,this.flushing=!1,this.bufferFlushed=!1,this.cachedTrackLoadedData=null,this._registerListeners()}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying(),this.mainDetails=null,this.bufferedTrack=null,this.switchingTrack=null}_registerListeners(){let{hls:e}=this;e.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.LEVEL_LOADED,this.onLevelLoaded,this),e.on(p.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),e.on(p.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.on(p.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.on(p.ERROR,this.onError,this),e.on(p.BUFFER_RESET,this.onBufferReset,this),e.on(p.BUFFER_CREATED,this.onBufferCreated,this),e.on(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(p.BUFFER_FLUSHED,this.onBufferFlushed,this),e.on(p.INIT_PTS_FOUND,this.onInitPtsFound,this),e.on(p.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){let{hls:e}=this;e.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.LEVEL_LOADED,this.onLevelLoaded,this),e.off(p.AUDIO_TRACKS_UPDATED,this.onAudioTracksUpdated,this),e.off(p.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.off(p.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.off(p.ERROR,this.onError,this),e.off(p.BUFFER_RESET,this.onBufferReset,this),e.off(p.BUFFER_CREATED,this.onBufferCreated,this),e.off(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(p.BUFFER_FLUSHED,this.onBufferFlushed,this),e.off(p.INIT_PTS_FOUND,this.onInitPtsFound,this),e.off(p.FRAG_BUFFERED,this.onFragBuffered,this)}onInitPtsFound(e,{frag:t,id:i,initPTS:r,timescale:n}){if(i==="main"){let o=t.cc;this.initPTS[t.cc]={baseTime:r,timescale:n},this.log(`InitPTS for cc: ${o} found from main: ${r}`),this.videoTrackCC=o,this.state===w.WAITING_INIT_PTS&&this.tick()}}startLoad(e){if(!this.levels){this.startPosition=e,this.state=w.STOPPED;return}let t=this.lastCurrentTime;this.stopLoad(),this.setInterval(Rr),t>0&&e===-1?(this.log(`Override startPosition with lastCurrentTime @${t.toFixed(3)}`),e=t,this.state=w.IDLE):(this.loadedmetadata=!1,this.state=w.WAITING_TRACK),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=e,this.tick()}doTick(){switch(this.state){case w.IDLE:this.doTickIdle();break;case w.WAITING_TRACK:{var e;let{levels:i,trackId:r}=this,n=i==null||(e=i[r])==null?void 0:e.details;if(n){if(this.waitForCdnTuneIn(n))break;this.state=w.WAITING_INIT_PTS}break}case w.FRAG_LOADING_WAITING_RETRY:{var t;let i=performance.now(),r=this.retryDate;if(!r||i>=r||(t=this.media)!=null&&t.seeking){let{levels:n,trackId:o}=this;this.log("RetryDate reached, switch back to IDLE state"),this.resetStartWhenNotLoaded((n==null?void 0:n[o])||null),this.state=w.IDLE}break}case w.WAITING_INIT_PTS:{let i=this.waitingData;if(i){let{frag:r,part:n,cache:o,complete:a}=i;if(this.initPTS[r.cc]!==void 0){this.waitingData=null,this.waitingVideoCC=-1,this.state=w.FRAG_LOADING;let l=o.flush(),c={frag:r,part:n,payload:l,networkDetails:null};this._handleFragmentLoadProgress(c),a&&super._handleFragmentLoadComplete(c)}else if(this.videoTrackCC!==this.waitingVideoCC)this.log(`Waiting fragment cc (${r.cc}) cancelled because video is at cc ${this.videoTrackCC}`),this.clearWaitingFragment();else{let l=this.getLoadPosition(),c=Z.bufferInfo(this.mediaBuffer,l,this.config.maxBufferHole);Fi(c.end,this.config.maxFragLookUpTolerance,r)<0&&(this.log(`Waiting fragment cc (${r.cc}) @ ${r.start} cancelled because another fragment at ${c.end} is needed`),this.clearWaitingFragment())}}else this.state=w.IDLE}}this.onTickEnd()}clearWaitingFragment(){let e=this.waitingData;e&&(this.fragmentTracker.removeFragment(e.frag),this.waitingData=null,this.waitingVideoCC=-1,this.state=w.IDLE)}resetLoadingState(){this.clearWaitingFragment(),super.resetLoadingState()}onTickEnd(){let{media:e}=this;e!=null&&e.readyState&&(this.lastCurrentTime=e.currentTime)}doTickIdle(){let{hls:e,levels:t,media:i,trackId:r}=this,n=e.config;if(!i&&(this.startFragRequested||!n.startFragPrefetch)||!(t!=null&&t[r]))return;let o=t[r],a=o.details;if(!a||a.live&&this.levelLastLoaded!==o||this.waitForCdnTuneIn(a)){this.state=w.WAITING_TRACK;return}let l=this.mediaBuffer?this.mediaBuffer:this.media;this.bufferFlushed&&l&&(this.bufferFlushed=!1,this.afterBufferFlushed(l,J.AUDIO,$.AUDIO));let c=this.getFwdBufferInfo(l,$.AUDIO);if(c===null)return;let{bufferedTrack:h,switchingTrack:u}=this;if(!u&&this._streamEnded(c,a)){e.trigger(p.BUFFER_EOS,{type:"audio"}),this.state=w.ENDED;return}let d=this.getFwdBufferInfo(this.videoBuffer?this.videoBuffer:this.media,$.MAIN),f=c.len,g=this.getMaxBufferLength(d==null?void 0:d.len),m=a.fragments,y=m[0].start,T=this.flushing?this.getLoadPosition():c.end;if(u&&i){let x=this.getLoadPosition();h&&!Je(u.attrs,h.attrs)&&(T=x),a.PTSKnown&&x<y&&(c.end>y||c.nextStart)&&(this.log("Alt audio track ahead of main track, seek to start of alt audio track"),i.currentTime=y+.05)}if(f>=g&&!u&&T<m[m.length-1].start)return;let v=this.getNextFragment(T,a),E=!1;if(v&&this.isLoopLoading(v,T)&&(E=!!v.gap,v=this.getNextFragmentLoopLoading(v,a,c,$.MAIN,g)),!v){this.bufferFlushed=!0;return}let _=d&&v.start>d.end+a.targetduration;if(_||!(d!=null&&d.len)&&c.len){let x=this.getAppendedFrag(v.start,$.MAIN);if(x===null||(E||(E=!!x.gap||!!_&&d.len===0),_&&!E||E&&c.nextStart&&c.nextStart<x.end))return}this.loadFragment(v,o,T)}getMaxBufferLength(e){let t=super.getMaxBufferLength();return e?Math.min(Math.max(t,e),this.config.maxMaxBufferLength):t}onMediaDetaching(){this.videoBuffer=null,this.bufferFlushed=this.flushing=!1,super.onMediaDetaching()}onAudioTracksUpdated(e,{audioTracks:t}){this.resetTransmuxer(),this.levels=t.map(i=>new Oe(i))}onAudioTrackSwitching(e,t){let i=!!t.url;this.trackId=t.id;let{fragCurrent:r}=this;r&&(r.abortRequests(),this.removeUnbufferedFrags(r.start)),this.resetLoadingState(),i?this.setInterval(Rr):this.resetTransmuxer(),i?(this.switchingTrack=t,this.state=w.IDLE,this.flushAudioIfNeeded(t)):(this.switchingTrack=null,this.bufferedTrack=t,this.state=w.STOPPED),this.tick()}onManifestLoading(){this.fragmentTracker.removeAllFragments(),this.startPosition=this.lastCurrentTime=0,this.bufferFlushed=this.flushing=!1,this.levels=this.mainDetails=this.waitingData=this.bufferedTrack=this.cachedTrackLoadedData=this.switchingTrack=null,this.startFragRequested=!1,this.trackId=this.videoTrackCC=this.waitingVideoCC=-1}onLevelLoaded(e,t){this.mainDetails=t.details,this.cachedTrackLoadedData!==null&&(this.hls.trigger(p.AUDIO_TRACK_LOADED,this.cachedTrackLoadedData),this.cachedTrackLoadedData=null)}onAudioTrackLoaded(e,t){var i;if(this.mainDetails==null){this.cachedTrackLoadedData=t;return}let{levels:r}=this,{details:n,id:o}=t;if(!r){this.warn(`Audio tracks were reset while loading level ${o}`);return}this.log(`Audio track ${o} loaded [${n.startSN},${n.endSN}]${n.lastPartSn?`[part-${n.lastPartSn}-${n.lastPartIndex}]`:""},duration:${n.totalduration}`);let a=r[o],l=0;if(n.live||(i=a.details)!=null&&i.live){this.checkLiveUpdate(n);let h=this.mainDetails;if(n.deltaUpdateFailed||!h)return;if(!a.details&&n.hasProgramDateTime&&h.hasProgramDateTime)Gt(n,h),l=n.fragments[0].start;else{var c;l=this.alignPlaylists(n,a.details,(c=this.levelLastLoaded)==null?void 0:c.details)}}a.details=n,this.levelLastLoaded=a,!this.startFragRequested&&(this.mainDetails||!n.live)&&this.setStartPosition(this.mainDetails||n,l),this.state===w.WAITING_TRACK&&!this.waitForCdnTuneIn(n)&&(this.state=w.IDLE),this.tick()}_handleFragmentLoadProgress(e){var t;let{frag:i,part:r,payload:n}=e,{config:o,trackId:a,levels:l}=this;if(!l){this.warn(`Audio tracks were reset while fragment load was in progress. Fragment ${i.sn} of level ${i.level} will not be buffered`);return}let c=l[a];if(!c){this.warn("Audio track is undefined on fragment load progress");return}let h=c.details;if(!h){this.warn("Audio track details undefined on fragment load progress"),this.removeUnbufferedFrags(i.start);return}let u=o.defaultAudioCodec||c.audioCodec||"mp4a.40.2",d=this.transmuxer;d||(d=this.transmuxer=new jt(this.hls,$.AUDIO,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)));let f=this.initPTS[i.cc],g=(t=i.initSegment)==null?void 0:t.data;if(f!==void 0){let y=r?r.index:-1,T=y!==-1,v=new ct(i.level,i.sn,i.stats.chunkCount,n.byteLength,y,T);d.push(n,g,u,"",i,r,h.totalduration,!1,v,f)}else{this.log(`Unknown video PTS for cc ${i.cc}, waiting for video PTS before demuxing audio frag ${i.sn} of [${h.startSN} ,${h.endSN}],track ${a}`);let{cache:m}=this.waitingData=this.waitingData||{frag:i,part:r,cache:new Ht,complete:!1};m.push(new Uint8Array(n)),this.waitingVideoCC=this.videoTrackCC,this.state=w.WAITING_INIT_PTS}}_handleFragmentLoadComplete(e){if(this.waitingData){this.waitingData.complete=!0;return}super._handleFragmentLoadComplete(e)}onBufferReset(){this.mediaBuffer=this.videoBuffer=null,this.loadedmetadata=!1}onBufferCreated(e,t){let i=t.tracks.audio;i&&(this.mediaBuffer=i.buffer||null),t.tracks.video&&(this.videoBuffer=t.tracks.video.buffer||null)}onFragBuffered(e,t){let{frag:i,part:r}=t;if(i.type!==$.AUDIO){if(!this.loadedmetadata&&i.type===$.MAIN){let n=this.videoBuffer||this.media;n&&Z.getBuffered(n).length&&(this.loadedmetadata=!0)}return}if(this.fragContextChanged(i)){this.warn(`Fragment ${i.sn}${r?" p: "+r.index:""} of level ${i.level} finished buffering, but was aborted. state: ${this.state}, audioSwitch: ${this.switchingTrack?this.switchingTrack.name:"false"}`);return}if(i.sn!=="initSegment"){this.fragPrevious=i;let n=this.switchingTrack;n&&(this.bufferedTrack=n,this.switchingTrack=null,this.hls.trigger(p.AUDIO_TRACK_SWITCHED,he({},n)))}this.fragBufferedComplete(i,r)}onError(e,t){var i;if(t.fatal){this.state=w.ERROR;return}switch(t.details){case b.FRAG_GAP:case b.FRAG_PARSING_ERROR:case b.FRAG_DECRYPT_ERROR:case b.FRAG_LOAD_ERROR:case b.FRAG_LOAD_TIMEOUT:case b.KEY_LOAD_ERROR:case b.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError($.AUDIO,t);break;case b.AUDIO_TRACK_LOAD_ERROR:case b.AUDIO_TRACK_LOAD_TIMEOUT:case b.LEVEL_PARSING_ERROR:!t.levelRetry&&this.state===w.WAITING_TRACK&&((i=t.context)==null?void 0:i.type)===j.AUDIO_TRACK&&(this.state=w.IDLE);break;case b.BUFFER_APPEND_ERROR:case b.BUFFER_FULL_ERROR:if(!t.parent||t.parent!=="audio")return;if(t.details===b.BUFFER_APPEND_ERROR){this.resetLoadingState();return}this.reduceLengthAndFlushBuffer(t)&&(this.bufferedTrack=null,super.flushMainBuffer(0,Number.POSITIVE_INFINITY,"audio"));break;case b.INTERNAL_EXCEPTION:this.recoverWorkerError(t);break}}onBufferFlushing(e,{type:t}){t!==J.VIDEO&&(this.flushing=!0)}onBufferFlushed(e,{type:t}){if(t!==J.VIDEO){this.flushing=!1,this.bufferFlushed=!0,this.state===w.ENDED&&(this.state=w.IDLE);let i=this.mediaBuffer||this.media;i&&(this.afterBufferFlushed(i,t,$.AUDIO),this.tick())}}_handleTransmuxComplete(e){var t;let i="audio",{hls:r}=this,{remuxResult:n,chunkMeta:o}=e,a=this.getCurrentContext(o);if(!a){this.resetWhenMissingContext(o);return}let{frag:l,part:c,level:h}=a,{details:u}=h,{audio:d,text:f,id3:g,initSegment:m}=n;if(this.fragContextChanged(l)||!u){this.fragmentTracker.removeFragment(l);return}if(this.state=w.PARSING,this.switchingTrack&&d&&this.completeAudioSwitch(this.switchingTrack),m!=null&&m.tracks){let y=l.initSegment||l;this._bufferInitSegment(h,m.tracks,y,o),r.trigger(p.FRAG_PARSING_INIT_SEGMENT,{frag:y,id:i,tracks:m.tracks})}if(d){let{startPTS:y,endPTS:T,startDTS:v,endDTS:E}=d;c&&(c.elementaryStreams[J.AUDIO]={startPTS:y,endPTS:T,startDTS:v,endDTS:E}),l.setElementaryStreamInfo(J.AUDIO,y,T,v,E),this.bufferFragmentData(d,l,c,o)}if(g!=null&&(t=g.samples)!=null&&t.length){let y=se({id:i,frag:l,details:u},g);r.trigger(p.FRAG_PARSING_METADATA,y)}if(f){let y=se({id:i,frag:l,details:u},f);r.trigger(p.FRAG_PARSING_USERDATA,y)}}_bufferInitSegment(e,t,i,r){if(this.state!==w.PARSING)return;t.video&&delete t.video;let n=t.audio;if(!n)return;n.id="audio";let o=e.audioCodec;this.log(`Init audio buffer, container:${n.container}, codecs[level/parsed]=[${o}/${n.codec}]`),o&&o.split(",").length===1&&(n.levelCodec=o),this.hls.trigger(p.BUFFER_CODECS,t);let a=n.initSegment;if(a!=null&&a.byteLength){let l={type:"audio",frag:i,part:null,chunkMeta:r,parent:i.type,data:a};this.hls.trigger(p.BUFFER_APPENDING,l)}this.tickImmediate()}loadFragment(e,t,i){let r=this.fragmentTracker.getState(e);if(this.fragCurrent=e,this.switchingTrack||r===ce.NOT_LOADED||r===ce.PARTIAL){var n;if(e.sn==="initSegment")this._loadInitSegment(e,t);else if((n=t.details)!=null&&n.live&&!this.initPTS[e.cc]){this.log(`Waiting for video PTS in continuity counter ${e.cc} of live stream before loading audio fragment ${e.sn} of level ${this.trackId}`),this.state=w.WAITING_INIT_PTS;let o=this.mainDetails;o&&o.fragments[0].start!==t.details.fragments[0].start&&Gt(t.details,o)}else this.startFragRequested=!0,super.loadFragment(e,t,i)}else this.clearTrackerIfNeeded(e)}flushAudioIfNeeded(e){let{media:t,bufferedTrack:i}=this,r=i==null?void 0:i.attrs,n=e.attrs;t&&r&&(r.CHANNELS!==n.CHANNELS||i.name!==e.name||i.lang!==e.lang)&&(this.log("Switching audio track : flushing all audio"),super.flushMainBuffer(0,Number.POSITIVE_INFINITY,"audio"),this.bufferedTrack=null)}completeAudioSwitch(e){let{hls:t}=this;this.flushAudioIfNeeded(e),this.bufferedTrack=e,this.switchingTrack=null,t.trigger(p.AUDIO_TRACK_SWITCHED,he({},e))}},os=class extends lt{constructor(e){super(e,"[audio-track-controller]"),this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0,this.registerListeners()}registerListeners(){let{hls:e}=this;e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.MANIFEST_PARSED,this.onManifestParsed,this),e.on(p.LEVEL_LOADING,this.onLevelLoading,this),e.on(p.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(p.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.on(p.ERROR,this.onError,this)}unregisterListeners(){let{hls:e}=this;e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.MANIFEST_PARSED,this.onManifestParsed,this),e.off(p.LEVEL_LOADING,this.onLevelLoading,this),e.off(p.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(p.AUDIO_TRACK_LOADED,this.onAudioTrackLoaded,this),e.off(p.ERROR,this.onError,this)}destroy(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.currentTrack=null,super.destroy()}onManifestLoading(){this.tracks=[],this.tracksInGroup=[],this.groupIds=null,this.currentTrack=null,this.trackId=-1,this.selectDefaultTrack=!0}onManifestParsed(e,t){this.tracks=t.audioTracks||[]}onAudioTrackLoaded(e,t){let{id:i,groupId:r,details:n}=t,o=this.tracksInGroup[i];if(!o||o.groupId!==r){this.warn(`Audio track with id:${i} and group:${r} not found in active group ${o==null?void 0:o.groupId}`);return}let a=o.details;o.details=t.details,this.log(`Audio track ${i} "${o.name}" lang:${o.lang} group:${r} loaded [${n.startSN}-${n.endSN}]`),i===this.trackId&&this.playlistLoaded(i,t,a)}onLevelLoading(e,t){this.switchLevel(t.level)}onLevelSwitching(e,t){this.switchLevel(t.level)}switchLevel(e){let t=this.hls.levels[e];if(!t)return;let i=t.audioGroups||null,r=this.groupIds,n=this.currentTrack;if(!i||(r==null?void 0:r.length)!==(i==null?void 0:i.length)||i!=null&&i.some(a=>(r==null?void 0:r.indexOf(a))===-1)){this.groupIds=i,this.trackId=-1,this.currentTrack=null;let a=this.tracks.filter(d=>!i||i.indexOf(d.groupId)!==-1);if(a.length)this.selectDefaultTrack&&!a.some(d=>d.default)&&(this.selectDefaultTrack=!1),a.forEach((d,f)=>{d.id=f});else if(!n&&!this.tracksInGroup.length)return;this.tracksInGroup=a;let l=this.hls.config.audioPreference;if(!n&&l){let d=_e(l,a,Ge);if(d>-1)n=a[d];else{let f=_e(l,this.tracks);n=this.tracks[f]}}let c=this.findTrackId(n);c===-1&&n&&(c=this.findTrackId(null));let h={audioTracks:a};this.log(`Updating audio tracks, ${a.length} track(s) found in group(s): ${i==null?void 0:i.join(",")}`),this.hls.trigger(p.AUDIO_TRACKS_UPDATED,h);let u=this.trackId;if(c!==-1&&u===-1)this.setAudioTrack(c);else if(a.length&&u===-1){var o;let d=new Error(`No audio track selected for current audio group-ID(s): ${(o=this.groupIds)==null?void 0:o.join(",")} track count: ${a.length}`);this.warn(d.message),this.hls.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.AUDIO_TRACK_LOAD_ERROR,fatal:!0,error:d})}}else this.shouldReloadPlaylist(n)&&this.setAudioTrack(this.trackId)}onError(e,t){t.fatal||!t.context||t.context.type===j.AUDIO_TRACK&&t.context.id===this.trackId&&(!this.groupIds||this.groupIds.indexOf(t.context.groupId)!==-1)&&(this.requestScheduled=-1,this.checkRetry(t))}get allAudioTracks(){return this.tracks}get audioTracks(){return this.tracksInGroup}get audioTrack(){return this.trackId}set audioTrack(e){this.selectDefaultTrack=!1,this.setAudioTrack(e)}setAudioOption(e){let t=this.hls;if(t.config.audioPreference=e,e){let i=this.allAudioTracks;if(this.selectDefaultTrack=!1,i.length){let r=this.currentTrack;if(r&&je(e,r,Ge))return r;let n=_e(e,this.tracksInGroup,Ge);if(n>-1){let o=this.tracksInGroup[n];return this.setAudioTrack(n),o}else if(r){let o=t.loadLevel;o===-1&&(o=t.firstAutoLevel);let a=La(e,t.levels,i,o,Ge);if(a===-1)return null;t.nextLoadLevel=a}if(e.channels||e.audioCodec){let o=_e(e,i);if(o>-1)return i[o]}}}return null}setAudioTrack(e){let t=this.tracksInGroup;if(e<0||e>=t.length){this.warn(`Invalid audio track id: ${e}`);return}this.clearTimer(),this.selectDefaultTrack=!1;let i=this.currentTrack,r=t[e],n=r.details&&!r.details.live;if(e===this.trackId&&r===i&&n||(this.log(`Switching to audio-track ${e} "${r.name}" lang:${r.lang} group:${r.groupId} channels:${r.channels}`),this.trackId=e,this.currentTrack=r,this.hls.trigger(p.AUDIO_TRACK_SWITCHING,he({},r)),n))return;let o=this.switchParams(r.url,i==null?void 0:i.details,r.details);this.loadPlaylist(o)}findTrackId(e){let t=this.tracksInGroup;for(let i=0;i<t.length;i++){let r=t[i];if(!(this.selectDefaultTrack&&!r.default)&&(!e||je(e,r,Ge)))return i}if(e){let{name:i,lang:r,assocLang:n,characteristics:o,audioCodec:a,channels:l}=e;for(let c=0;c<t.length;c++){let h=t[c];if(je({name:i,lang:r,assocLang:n,characteristics:o,audioCodec:a,channels:l},h,Ge))return c}for(let c=0;c<t.length;c++){let h=t[c];if(Je(e.attrs,h.attrs,["LANGUAGE","ASSOC-LANGUAGE","CHARACTERISTICS"]))return c}for(let c=0;c<t.length;c++){let h=t[c];if(Je(e.attrs,h.attrs,["LANGUAGE"]))return c}}return-1}loadPlaylist(e){let t=this.currentTrack;if(this.shouldLoadPlaylist(t)&&t){super.loadPlaylist();let i=t.id,r=t.groupId,n=t.url;if(e)try{n=e.addDirectives(n)}catch(o){this.warn(`Could not construct new URL with HLS Delivery Directives: ${o}`)}this.log(`loading audio-track playlist ${i} "${t.name}" lang:${t.lang} group:${r}`),this.clearTimer(),this.hls.trigger(p.AUDIO_TRACK_LOADING,{url:n,id:i,groupId:r,deliveryDirectives:e||null})}}},Ir=500,as=class extends ut{constructor(e,t,i){super(e,t,i,"[subtitle-stream-controller]",$.SUBTITLE),this.currentTrackId=-1,this.tracksBuffered=[],this.mainDetails=null,this._registerListeners()}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying(),this.mainDetails=null}_registerListeners(){let{hls:e}=this;e.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.LEVEL_LOADED,this.onLevelLoaded,this),e.on(p.ERROR,this.onError,this),e.on(p.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.on(p.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),e.on(p.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.on(p.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),e.on(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(p.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){let{hls:e}=this;e.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.LEVEL_LOADED,this.onLevelLoaded,this),e.off(p.ERROR,this.onError,this),e.off(p.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.off(p.SUBTITLE_TRACK_SWITCH,this.onSubtitleTrackSwitch,this),e.off(p.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.off(p.SUBTITLE_FRAG_PROCESSED,this.onSubtitleFragProcessed,this),e.off(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(p.FRAG_BUFFERED,this.onFragBuffered,this)}startLoad(e){this.stopLoad(),this.state=w.IDLE,this.setInterval(Ir),this.nextLoadPosition=this.startPosition=this.lastCurrentTime=e,this.tick()}onManifestLoading(){this.mainDetails=null,this.fragmentTracker.removeAllFragments()}onMediaDetaching(){this.tracksBuffered=[],super.onMediaDetaching()}onLevelLoaded(e,t){this.mainDetails=t.details}onSubtitleFragProcessed(e,t){let{frag:i,success:r}=t;if(this.fragPrevious=i,this.state=w.IDLE,!r)return;let n=this.tracksBuffered[this.currentTrackId];if(!n)return;let o,a=i.start;for(let c=0;c<n.length;c++)if(a>=n[c].start&&a<=n[c].end){o=n[c];break}let l=i.start+i.duration;o?o.end=l:(o={start:a,end:l},n.push(o)),this.fragmentTracker.fragBuffered(i),this.fragBufferedComplete(i,null)}onBufferFlushing(e,t){let{startOffset:i,endOffset:r}=t;if(i===0&&r!==Number.POSITIVE_INFINITY){let n=r-1;if(n<=0)return;t.endOffsetSubtitles=Math.max(0,n),this.tracksBuffered.forEach(o=>{for(let a=0;a<o.length;){if(o[a].end<=n){o.shift();continue}else if(o[a].start<n)o[a].start=n;else break;a++}}),this.fragmentTracker.removeFragmentsInRange(i,n,$.SUBTITLE)}}onFragBuffered(e,t){if(!this.loadedmetadata&&t.frag.type===$.MAIN){var i;(i=this.media)!=null&&i.buffered.length&&(this.loadedmetadata=!0)}}onError(e,t){let i=t.frag;(i==null?void 0:i.type)===$.SUBTITLE&&(this.fragCurrent&&this.fragCurrent.abortRequests(),this.state!==w.STOPPED&&(this.state=w.IDLE))}onSubtitleTracksUpdated(e,{subtitleTracks:t}){if(this.levels&&Cn(this.levels,t)){this.levels=t.map(i=>new Oe(i));return}this.tracksBuffered=[],this.levels=t.map(i=>{let r=new Oe(i);return this.tracksBuffered[r.id]=[],r}),this.fragmentTracker.removeFragmentsInRange(0,Number.POSITIVE_INFINITY,$.SUBTITLE),this.fragPrevious=null,this.mediaBuffer=null}onSubtitleTrackSwitch(e,t){var i;if(this.currentTrackId=t.id,!((i=this.levels)!=null&&i.length)||this.currentTrackId===-1){this.clearInterval();return}let r=this.levels[this.currentTrackId];r!=null&&r.details?this.mediaBuffer=this.mediaBufferTimeRanges:this.mediaBuffer=null,r&&this.setInterval(Ir)}onSubtitleTrackLoaded(e,t){var i;let{currentTrackId:r,levels:n}=this,{details:o,id:a}=t;if(!n){this.warn(`Subtitle tracks were reset while loading level ${a}`);return}let l=n[r];if(a>=n.length||a!==r||!l)return;this.log(`Subtitle track ${a} loaded [${o.startSN},${o.endSN}]${o.lastPartSn?`[part-${o.lastPartSn}-${o.lastPartIndex}]`:""},duration:${o.totalduration}`),this.mediaBuffer=this.mediaBufferTimeRanges;let c=0;if(o.live||(i=l.details)!=null&&i.live){let u=this.mainDetails;if(o.deltaUpdateFailed||!u)return;let d=u.fragments[0];if(!l.details)o.hasProgramDateTime&&u.hasProgramDateTime?(Gt(o,u),c=o.fragments[0].start):d&&(c=d.start,Mi(o,c));else{var h;c=this.alignPlaylists(o,l.details,(h=this.levelLastLoaded)==null?void 0:h.details),c===0&&d&&(c=d.start,Mi(o,c))}}l.details=o,this.levelLastLoaded=l,!this.startFragRequested&&(this.mainDetails||!o.live)&&this.setStartPosition(this.mainDetails||o,c),this.tick(),o.live&&!this.fragCurrent&&this.media&&this.state===w.IDLE&&($t(null,o.fragments,this.media.currentTime,0)||(this.warn("Subtitle playlist not aligned with playback"),l.details=void 0))}_handleFragmentLoadComplete(e){let{frag:t,payload:i}=e,r=t.decryptdata,n=this.hls;if(!this.fragContextChanged(t)&&i&&i.byteLength>0&&r!=null&&r.key&&r.iv&&r.method==="AES-128"){let o=performance.now();this.decrypter.decrypt(new Uint8Array(i),r.key.buffer,r.iv.buffer).catch(a=>{throw n.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.FRAG_DECRYPT_ERROR,fatal:!1,error:a,reason:a.message,frag:t}),a}).then(a=>{let l=performance.now();n.trigger(p.FRAG_DECRYPTED,{frag:t,payload:a,stats:{tstart:o,tdecrypt:l}})}).catch(a=>{this.warn(`${a.name}: ${a.message}`),this.state=w.IDLE})}}doTick(){if(!this.media){this.state=w.IDLE;return}if(this.state===w.IDLE){let{currentTrackId:e,levels:t}=this,i=t==null?void 0:t[e];if(!i||!t.length||!i.details)return;let{config:r}=this,n=this.getLoadPosition(),o=Z.bufferedInfo(this.tracksBuffered[this.currentTrackId]||[],n,r.maxBufferHole),{end:a,len:l}=o,c=this.getFwdBufferInfo(this.media,$.MAIN),h=i.details,u=this.getMaxBufferLength(c==null?void 0:c.len)+h.levelTargetDuration;if(l>u)return;let d=h.fragments,f=d.length,g=h.edge,m=null,y=this.fragPrevious;if(a<g){let T=r.maxFragLookUpTolerance,v=a>g-T?0:T;m=$t(y,d,Math.max(d[0].start,a),v),!m&&y&&y.start<d[0].start&&(m=d[0])}else m=d[f-1];if(!m)return;if(m=this.mapToInitFragWhenRequired(m),m.sn!=="initSegment"){let T=m.sn-h.startSN,v=d[T-1];v&&v.cc===m.cc&&this.fragmentTracker.getState(v)===ce.NOT_LOADED&&(m=v)}this.fragmentTracker.getState(m)===ce.NOT_LOADED&&this.loadFragment(m,i,a)}}getMaxBufferLength(e){let t=super.getMaxBufferLength();return e?Math.max(t,e):t}loadFragment(e,t,i){this.fragCurrent=e,e.sn==="initSegment"?this._loadInitSegment(e,t):(this.startFragRequested=!0,super.loadFragment(e,t,i))}get mediaBufferTimeRanges(){return new ls(this.tracksBuffered[this.currentTrackId]||[])}},ls=class{constructor(e){this.buffered=void 0;let t=(i,r,n)=>{if(r=r>>>0,r>n-1)throw new DOMException(`Failed to execute '${i}' on 'TimeRanges': The index provided (${r}) is greater than the maximum bound (${n})`);return e[r][i]};this.buffered={get length(){return e.length},end(i){return t("end",i,e.length)},start(i){return t("start",i,e.length)}}}},cs=class extends lt{constructor(e){super(e,"[subtitle-track-controller]"),this.media=null,this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0,this.queuedDefaultTrack=-1,this.asyncPollTrackChange=()=>this.pollTrackChange(0),this.useTextTrackPolling=!1,this.subtitlePollingInterval=-1,this._subtitleDisplay=!0,this.onTextTracksChanged=()=>{if(this.useTextTrackPolling||self.clearInterval(this.subtitlePollingInterval),!this.media||!this.hls.config.renderTextTracksNatively)return;let t=null,i=bt(this.media.textTracks);for(let n=0;n<i.length;n++)if(i[n].mode==="hidden")t=i[n];else if(i[n].mode==="showing"){t=i[n];break}let r=this.findTrackForTextTrack(t);this.subtitleTrack!==r&&this.setSubtitleTrack(r)},this.registerListeners()}destroy(){this.unregisterListeners(),this.tracks.length=0,this.tracksInGroup.length=0,this.currentTrack=null,this.onTextTracksChanged=this.asyncPollTrackChange=null,super.destroy()}get subtitleDisplay(){return this._subtitleDisplay}set subtitleDisplay(e){this._subtitleDisplay=e,this.trackId>-1&&this.toggleTrackModes()}registerListeners(){let{hls:e}=this;e.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.MANIFEST_PARSED,this.onManifestParsed,this),e.on(p.LEVEL_LOADING,this.onLevelLoading,this),e.on(p.LEVEL_SWITCHING,this.onLevelSwitching,this),e.on(p.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.on(p.ERROR,this.onError,this)}unregisterListeners(){let{hls:e}=this;e.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.MANIFEST_PARSED,this.onManifestParsed,this),e.off(p.LEVEL_LOADING,this.onLevelLoading,this),e.off(p.LEVEL_SWITCHING,this.onLevelSwitching,this),e.off(p.SUBTITLE_TRACK_LOADED,this.onSubtitleTrackLoaded,this),e.off(p.ERROR,this.onError,this)}onMediaAttached(e,t){this.media=t.media,this.media&&(this.queuedDefaultTrack>-1&&(this.subtitleTrack=this.queuedDefaultTrack,this.queuedDefaultTrack=-1),this.useTextTrackPolling=!(this.media.textTracks&&"onchange"in this.media.textTracks),this.useTextTrackPolling?this.pollTrackChange(500):this.media.textTracks.addEventListener("change",this.asyncPollTrackChange))}pollTrackChange(e){self.clearInterval(this.subtitlePollingInterval),this.subtitlePollingInterval=self.setInterval(this.onTextTracksChanged,e)}onMediaDetaching(){if(!this.media)return;self.clearInterval(this.subtitlePollingInterval),this.useTextTrackPolling||this.media.textTracks.removeEventListener("change",this.asyncPollTrackChange),this.trackId>-1&&(this.queuedDefaultTrack=this.trackId),bt(this.media.textTracks).forEach(t=>{We(t)}),this.subtitleTrack=-1,this.media=null}onManifestLoading(){this.tracks=[],this.groupIds=null,this.tracksInGroup=[],this.trackId=-1,this.currentTrack=null,this.selectDefaultTrack=!0}onManifestParsed(e,t){this.tracks=t.subtitleTracks}onSubtitleTrackLoaded(e,t){let{id:i,groupId:r,details:n}=t,o=this.tracksInGroup[i];if(!o||o.groupId!==r){this.warn(`Subtitle track with id:${i} and group:${r} not found in active group ${o==null?void 0:o.groupId}`);return}let a=o.details;o.details=t.details,this.log(`Subtitle track ${i} "${o.name}" lang:${o.lang} group:${r} loaded [${n.startSN}-${n.endSN}]`),i===this.trackId&&this.playlistLoaded(i,t,a)}onLevelLoading(e,t){this.switchLevel(t.level)}onLevelSwitching(e,t){this.switchLevel(t.level)}switchLevel(e){let t=this.hls.levels[e];if(!t)return;let i=t.subtitleGroups||null,r=this.groupIds,n=this.currentTrack;if(!i||(r==null?void 0:r.length)!==(i==null?void 0:i.length)||i!=null&&i.some(o=>(r==null?void 0:r.indexOf(o))===-1)){this.groupIds=i,this.trackId=-1,this.currentTrack=null;let o=this.tracks.filter(h=>!i||i.indexOf(h.groupId)!==-1);if(o.length)this.selectDefaultTrack&&!o.some(h=>h.default)&&(this.selectDefaultTrack=!1),o.forEach((h,u)=>{h.id=u});else if(!n&&!this.tracksInGroup.length)return;this.tracksInGroup=o;let a=this.hls.config.subtitlePreference;if(!n&&a){this.selectDefaultTrack=!1;let h=_e(a,o);if(h>-1)n=o[h];else{let u=_e(a,this.tracks);n=this.tracks[u]}}let l=this.findTrackId(n);l===-1&&n&&(l=this.findTrackId(null));let c={subtitleTracks:o};this.log(`Updating subtitle tracks, ${o.length} track(s) found in "${i==null?void 0:i.join(",")}" group-id`),this.hls.trigger(p.SUBTITLE_TRACKS_UPDATED,c),l!==-1&&this.trackId===-1&&this.setSubtitleTrack(l)}else this.shouldReloadPlaylist(n)&&this.setSubtitleTrack(this.trackId)}findTrackId(e){let t=this.tracksInGroup,i=this.selectDefaultTrack;for(let r=0;r<t.length;r++){let n=t[r];if(!(i&&!n.default||!i&&!e)&&(!e||je(n,e)))return r}if(e){for(let r=0;r<t.length;r++){let n=t[r];if(Je(e.attrs,n.attrs,["LANGUAGE","ASSOC-LANGUAGE","CHARACTERISTICS"]))return r}for(let r=0;r<t.length;r++){let n=t[r];if(Je(e.attrs,n.attrs,["LANGUAGE"]))return r}}return-1}findTrackForTextTrack(e){if(e){let t=this.tracksInGroup;for(let i=0;i<t.length;i++){let r=t[i];if(rs(r,e))return i}}return-1}onError(e,t){t.fatal||!t.context||t.context.type===j.SUBTITLE_TRACK&&t.context.id===this.trackId&&(!this.groupIds||this.groupIds.indexOf(t.context.groupId)!==-1)&&this.checkRetry(t)}get allSubtitleTracks(){return this.tracks}get subtitleTracks(){return this.tracksInGroup}get subtitleTrack(){return this.trackId}set subtitleTrack(e){this.selectDefaultTrack=!1,this.setSubtitleTrack(e)}setSubtitleOption(e){if(this.hls.config.subtitlePreference=e,e){let t=this.allSubtitleTracks;if(this.selectDefaultTrack=!1,t.length){let i=this.currentTrack;if(i&&je(e,i))return i;let r=_e(e,this.tracksInGroup);if(r>-1){let n=this.tracksInGroup[r];return this.setSubtitleTrack(r),n}else{if(i)return null;{let n=_e(e,t);if(n>-1)return t[n]}}}}return null}loadPlaylist(e){super.loadPlaylist();let t=this.currentTrack;if(this.shouldLoadPlaylist(t)&&t){let i=t.id,r=t.groupId,n=t.url;if(e)try{n=e.addDirectives(n)}catch(o){this.warn(`Could not construct new URL with HLS Delivery Directives: ${o}`)}this.log(`Loading subtitle playlist for id ${i}`),this.hls.trigger(p.SUBTITLE_TRACK_LOADING,{url:n,id:i,groupId:r,deliveryDirectives:e||null})}}toggleTrackModes(){let{media:e}=this;if(!e)return;let t=bt(e.textTracks),i=this.currentTrack,r;if(i&&(r=t.filter(n=>rs(i,n))[0],r||this.warn(`Unable to find subtitle TextTrack with name "${i.name}" and language "${i.lang}"`)),[].slice.call(t).forEach(n=>{n.mode!=="disabled"&&n!==r&&(n.mode="disabled")}),r){let n=this.subtitleDisplay?"showing":"hidden";r.mode!==n&&(r.mode=n)}}setSubtitleTrack(e){let t=this.tracksInGroup;if(!this.media){this.queuedDefaultTrack=e;return}if(e<-1||e>=t.length||!M(e)){this.warn(`Invalid subtitle track id: ${e}`);return}this.clearTimer(),this.selectDefaultTrack=!1;let i=this.currentTrack,r=t[e]||null;if(this.trackId=e,this.currentTrack=r,this.toggleTrackModes(),!r){this.hls.trigger(p.SUBTITLE_TRACK_SWITCH,{id:e});return}let n=!!r.details&&!r.details.live;if(e===this.trackId&&r===i&&n)return;this.log(`Switching to subtitle-track ${e}`+(r?` "${r.name}" lang:${r.lang} group:${r.groupId}`:""));let{id:o,groupId:a="",name:l,type:c,url:h}=r;this.hls.trigger(p.SUBTITLE_TRACK_SWITCH,{id:o,groupId:a,name:l,type:c,url:h});let u=this.switchParams(r.url,i==null?void 0:i.details,r.details);this.loadPlaylist(u)}},hs=class{constructor(e){this.buffers=void 0,this.queues={video:[],audio:[],audiovideo:[]},this.buffers=e}append(e,t,i){let r=this.queues[t];r.push(e),r.length===1&&!i&&this.executeNext(t)}insertAbort(e,t){this.queues[t].unshift(e),this.executeNext(t)}appendBlocker(e){let t,i=new Promise(n=>{t=n}),r={execute:t,onStart:()=>{},onComplete:()=>{},onError:()=>{}};return this.append(r,e),i}executeNext(e){let t=this.queues[e];if(t.length){let i=t[0];try{i.execute()}catch(r){S.warn(`[buffer-operation-queue]: Exception executing "${e}" SourceBuffer operation: ${r}`),i.onError(r);let n=this.buffers[e];n!=null&&n.updating||this.shiftAndExecuteNext(e)}}}shiftAndExecuteNext(e){this.queues[e].shift(),this.executeNext(e)}current(e){return this.queues[e][0]}},wr=/(avc[1234]|hvc1|hev1|dvh[1e]|vp09|av01)(?:\.[^.,]+)+/,us=class{constructor(e){this.details=null,this._objectUrl=null,this.operationQueue=void 0,this.listeners=void 0,this.hls=void 0,this.bufferCodecEventsExpected=0,this._bufferCodecEventsTotal=0,this.media=null,this.mediaSource=null,this.lastMpegAudioChunk=null,this.appendSource=void 0,this.appendErrors={audio:0,video:0,audiovideo:0},this.tracks={},this.pendingTracks={},this.sourceBuffer=void 0,this.log=void 0,this.warn=void 0,this.error=void 0,this._onEndStreaming=i=>{this.hls&&this.hls.pauseBuffering()},this._onStartStreaming=i=>{this.hls&&this.hls.resumeBuffering()},this._onMediaSourceOpen=()=>{let{media:i,mediaSource:r}=this;this.log("Media source opened"),i&&(i.removeEventListener("emptied",this._onMediaEmptied),this.updateMediaElementDuration(),this.hls.trigger(p.MEDIA_ATTACHED,{media:i,mediaSource:r})),r&&r.removeEventListener("sourceopen",this._onMediaSourceOpen),this.checkPendingTracks()},this._onMediaSourceClose=()=>{this.log("Media source closed")},this._onMediaSourceEnded=()=>{this.log("Media source ended")},this._onMediaEmptied=()=>{let{mediaSrc:i,_objectUrl:r}=this;i!==r&&S.error(`Media element src was set while attaching MediaSource (${r} > ${i})`)},this.hls=e;let t="[buffer-controller]";this.appendSource=Yo($e(e.config.preferManagedMediaSource)),this.log=S.log.bind(S,t),this.warn=S.warn.bind(S,t),this.error=S.error.bind(S,t),this._initSourceBuffer(),this.registerListeners()}hasSourceTypes(){return this.getSourceBufferTypes().length>0||Object.keys(this.pendingTracks).length>0}destroy(){this.unregisterListeners(),this.details=null,this.lastMpegAudioChunk=null,this.hls=null}registerListeners(){let{hls:e}=this;e.on(p.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.MANIFEST_PARSED,this.onManifestParsed,this),e.on(p.BUFFER_RESET,this.onBufferReset,this),e.on(p.BUFFER_APPENDING,this.onBufferAppending,this),e.on(p.BUFFER_CODECS,this.onBufferCodecs,this),e.on(p.BUFFER_EOS,this.onBufferEos,this),e.on(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.on(p.LEVEL_UPDATED,this.onLevelUpdated,this),e.on(p.FRAG_PARSED,this.onFragParsed,this),e.on(p.FRAG_CHANGED,this.onFragChanged,this)}unregisterListeners(){let{hls:e}=this;e.off(p.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.MANIFEST_PARSED,this.onManifestParsed,this),e.off(p.BUFFER_RESET,this.onBufferReset,this),e.off(p.BUFFER_APPENDING,this.onBufferAppending,this),e.off(p.BUFFER_CODECS,this.onBufferCodecs,this),e.off(p.BUFFER_EOS,this.onBufferEos,this),e.off(p.BUFFER_FLUSHING,this.onBufferFlushing,this),e.off(p.LEVEL_UPDATED,this.onLevelUpdated,this),e.off(p.FRAG_PARSED,this.onFragParsed,this),e.off(p.FRAG_CHANGED,this.onFragChanged,this)}_initSourceBuffer(){this.sourceBuffer={},this.operationQueue=new hs(this.sourceBuffer),this.listeners={audio:[],video:[],audiovideo:[]},this.appendErrors={audio:0,video:0,audiovideo:0},this.lastMpegAudioChunk=null}onManifestLoading(){this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=0,this.details=null}onManifestParsed(e,t){let i=2;(t.audio&&!t.video||!t.altAudio)&&(i=1),this.bufferCodecEventsExpected=this._bufferCodecEventsTotal=i,this.log(`${this.bufferCodecEventsExpected} bufferCodec event(s) expected`)}onMediaAttaching(e,t){let i=this.media=t.media,r=$e(this.appendSource);if(i&&r){var n;let o=this.mediaSource=new r;this.log(`created media source: ${(n=o.constructor)==null?void 0:n.name}`),o.addEventListener("sourceopen",this._onMediaSourceOpen),o.addEventListener("sourceended",this._onMediaSourceEnded),o.addEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(o.addEventListener("startstreaming",this._onStartStreaming),o.addEventListener("endstreaming",this._onEndStreaming));let a=this._objectUrl=self.URL.createObjectURL(o);if(this.appendSource)try{i.removeAttribute("src");let l=self.ManagedMediaSource;i.disableRemotePlayback=i.disableRemotePlayback||l&&o instanceof l,Cr(i),al(i,a),i.load()}catch(l){i.src=a}else i.src=a;i.addEventListener("emptied",this._onMediaEmptied)}}onMediaDetaching(){let{media:e,mediaSource:t,_objectUrl:i}=this;if(t){if(this.log("media source detaching"),t.readyState==="open")try{t.endOfStream()}catch(r){this.warn(`onMediaDetaching: ${r.message} while calling endOfStream`)}this.onBufferReset(),t.removeEventListener("sourceopen",this._onMediaSourceOpen),t.removeEventListener("sourceended",this._onMediaSourceEnded),t.removeEventListener("sourceclose",this._onMediaSourceClose),this.appendSource&&(t.removeEventListener("startstreaming",this._onStartStreaming),t.removeEventListener("endstreaming",this._onEndStreaming)),e&&(e.removeEventListener("emptied",this._onMediaEmptied),i&&self.URL.revokeObjectURL(i),this.mediaSrc===i?(e.removeAttribute("src"),this.appendSource&&Cr(e),e.load()):this.warn("media|source.src was changed by a third party - skip cleanup")),this.mediaSource=null,this.media=null,this._objectUrl=null,this.bufferCodecEventsExpected=this._bufferCodecEventsTotal,this.pendingTracks={},this.tracks={}}this.hls.trigger(p.MEDIA_DETACHED,void 0)}onBufferReset(){this.getSourceBufferTypes().forEach(e=>{this.resetBuffer(e)}),this._initSourceBuffer()}resetBuffer(e){let t=this.sourceBuffer[e];try{if(t){var i;this.removeBufferListeners(e),this.sourceBuffer[e]=void 0,(i=this.mediaSource)!=null&&i.sourceBuffers.length&&this.mediaSource.removeSourceBuffer(t)}}catch(r){this.warn(`onBufferReset ${e}`,r)}}onBufferCodecs(e,t){let i=this.getSourceBufferTypes().length,r=Object.keys(t);if(r.forEach(o=>{if(i){let l=this.tracks[o];if(l&&typeof l.buffer.changeType=="function"){var a;let{id:c,codec:h,levelCodec:u,container:d,metadata:f}=t[o],g=ir(l.codec,l.levelCodec),m=g==null?void 0:g.replace(wr,"$1"),y=ir(h,u),T=(a=y)==null?void 0:a.replace(wr,"$1");if(y&&m!==T){o.slice(0,5)==="audio"&&(y=Mt(y,this.appendSource));let v=`${d};codecs=${y}`;this.appendChangeType(o,v),this.log(`switching codec ${g} to ${y}`),this.tracks[o]={buffer:l.buffer,codec:h,container:d,levelCodec:u,metadata:f,id:c}}}}else this.pendingTracks[o]=t[o]}),i)return;let n=Math.max(this.bufferCodecEventsExpected-1,0);this.bufferCodecEventsExpected!==n&&(this.log(`${n} bufferCodec event(s) expected ${r.join(",")}`),this.bufferCodecEventsExpected=n),this.mediaSource&&this.mediaSource.readyState==="open"&&this.checkPendingTracks()}appendChangeType(e,t){let{operationQueue:i}=this,r={execute:()=>{let n=this.sourceBuffer[e];n&&(this.log(`changing ${e} sourceBuffer type to ${t}`),n.changeType(t)),i.shiftAndExecuteNext(e)},onStart:()=>{},onComplete:()=>{},onError:n=>{this.warn(`Failed to change ${e} SourceBuffer type`,n)}};i.append(r,e,!!this.pendingTracks[e])}onBufferAppending(e,t){let{hls:i,operationQueue:r,tracks:n}=this,{data:o,type:a,frag:l,part:c,chunkMeta:h}=t,u=h.buffering[a],d=self.performance.now();u.start=d;let f=l.stats.buffering,g=c?c.stats.buffering:null;f.start===0&&(f.start=d),g&&g.start===0&&(g.start=d);let m=n.audio,y=!1;a==="audio"&&(m==null?void 0:m.container)==="audio/mpeg"&&(y=!this.lastMpegAudioChunk||h.id===1||this.lastMpegAudioChunk.sn!==h.sn,this.lastMpegAudioChunk=h);let T=l.start,v={execute:()=>{if(u.executeStart=self.performance.now(),y){let E=this.sourceBuffer[a];if(E){let _=T-E.timestampOffset;Math.abs(_)>=.1&&(this.log(`Updating audio SourceBuffer timestampOffset to ${T} (delta: ${_}) sn: ${l.sn})`),E.timestampOffset=T)}}this.appendExecutor(o,a)},onStart:()=>{},onComplete:()=>{let E=self.performance.now();u.executeEnd=u.end=E,f.first===0&&(f.first=E),g&&g.first===0&&(g.first=E);let{sourceBuffer:_}=this,x={};for(let I in _)x[I]=Z.getBuffered(_[I]);this.appendErrors[a]=0,a==="audio"||a==="video"?this.appendErrors.audiovideo=0:(this.appendErrors.audio=0,this.appendErrors.video=0),this.hls.trigger(p.BUFFER_APPENDED,{type:a,frag:l,part:c,chunkMeta:h,parent:l.type,timeRanges:x})},onError:E=>{let _={type:G.MEDIA_ERROR,parent:l.type,details:b.BUFFER_APPEND_ERROR,sourceBufferName:a,frag:l,part:c,chunkMeta:h,error:E,err:E,fatal:!1};if(E.code===DOMException.QUOTA_EXCEEDED_ERR)_.details=b.BUFFER_FULL_ERROR;else{let x=++this.appendErrors[a];_.details=b.BUFFER_APPEND_ERROR,this.warn(`Failed ${x}/${i.config.appendErrorMaxRetry} times to append segment in "${a}" sourceBuffer`),x>=i.config.appendErrorMaxRetry&&(_.fatal=!0)}i.trigger(p.ERROR,_)}};r.append(v,a,!!this.pendingTracks[a])}onBufferFlushing(e,t){let{operationQueue:i}=this,r=n=>({execute:this.removeExecutor.bind(this,n,t.startOffset,t.endOffset),onStart:()=>{},onComplete:()=>{this.hls.trigger(p.BUFFER_FLUSHED,{type:n})},onError:o=>{this.warn(`Failed to remove from ${n} SourceBuffer`,o)}});t.type?i.append(r(t.type),t.type):this.getSourceBufferTypes().forEach(n=>{i.append(r(n),n)})}onFragParsed(e,t){let{frag:i,part:r}=t,n=[],o=r?r.elementaryStreams:i.elementaryStreams;o[J.AUDIOVIDEO]?n.push("audiovideo"):(o[J.AUDIO]&&n.push("audio"),o[J.VIDEO]&&n.push("video"));let a=()=>{let l=self.performance.now();i.stats.buffering.end=l,r&&(r.stats.buffering.end=l);let c=r?r.stats:i.stats;this.hls.trigger(p.FRAG_BUFFERED,{frag:i,part:r,stats:c,id:i.type})};n.length===0&&this.warn(`Fragments must have at least one ElementaryStreamType set. type: ${i.type} level: ${i.level} sn: ${i.sn}`),this.blockBuffers(a,n)}onFragChanged(e,t){this.trimBuffers()}onBufferEos(e,t){this.getSourceBufferTypes().reduce((r,n)=>{let o=this.sourceBuffer[n];return o&&(!t.type||t.type===n)&&(o.ending=!0,o.ended||(o.ended=!0,this.log(`${n} sourceBuffer now EOS`))),r&&!!(!o||o.ended)},!0)&&(this.log("Queueing mediaSource.endOfStream()"),this.blockBuffers(()=>{this.getSourceBufferTypes().forEach(n=>{let o=this.sourceBuffer[n];o&&(o.ending=!1)});let{mediaSource:r}=this;if(!r||r.readyState!=="open"){r&&this.log(`Could not call mediaSource.endOfStream(). mediaSource.readyState: ${r.readyState}`);return}this.log("Calling mediaSource.endOfStream()"),r.endOfStream()}))}onLevelUpdated(e,{details:t}){t.fragments.length&&(this.details=t,this.getSourceBufferTypes().length?this.blockBuffers(this.updateMediaElementDuration.bind(this)):this.updateMediaElementDuration())}trimBuffers(){let{hls:e,details:t,media:i}=this;if(!i||t===null||!this.getSourceBufferTypes().length)return;let n=e.config,o=i.currentTime,a=t.levelTargetDuration,l=t.live&&n.liveBackBufferLength!==null?n.liveBackBufferLength:n.backBufferLength;if(M(l)&&l>0){let c=Math.max(l,a),h=Math.floor(o/a)*a-c;this.flushBackBuffer(o,a,h)}if(M(n.frontBufferFlushThreshold)&&n.frontBufferFlushThreshold>0){let c=Math.max(n.maxBufferLength,n.frontBufferFlushThreshold),h=Math.max(c,a),u=Math.floor(o/a)*a+h;this.flushFrontBuffer(o,a,u)}}flushBackBuffer(e,t,i){let{details:r,sourceBuffer:n}=this;this.getSourceBufferTypes().forEach(a=>{let l=n[a];if(l){let c=Z.getBuffered(l);if(c.length>0&&i>c.start(0)){if(this.hls.trigger(p.BACK_BUFFER_REACHED,{bufferEnd:i}),r!=null&&r.live)this.hls.trigger(p.LIVE_BACK_BUFFER_REACHED,{bufferEnd:i});else if(l.ended&&c.end(c.length-1)-e<t*2){this.log(`Cannot flush ${a} back buffer while SourceBuffer is in ended state`);return}this.hls.trigger(p.BUFFER_FLUSHING,{startOffset:0,endOffset:i,type:a})}}})}flushFrontBuffer(e,t,i){let{sourceBuffer:r}=this;this.getSourceBufferTypes().forEach(o=>{let a=r[o];if(a){let l=Z.getBuffered(a),c=l.length;if(c<2)return;let h=l.start(c-1),u=l.end(c-1);if(i>h||e>=h&&e<=u)return;if(a.ended&&e-u<2*t){this.log(`Cannot flush ${o} front buffer while SourceBuffer is in ended state`);return}this.hls.trigger(p.BUFFER_FLUSHING,{startOffset:h,endOffset:1/0,type:o})}})}updateMediaElementDuration(){if(!this.details||!this.media||!this.mediaSource||this.mediaSource.readyState!=="open")return;let{details:e,hls:t,media:i,mediaSource:r}=this,n=e.fragments[0].start+e.totalduration,o=i.duration,a=M(r.duration)?r.duration:0;e.live&&t.config.liveDurationInfinity?(r.duration=1/0,this.updateSeekableRange(e)):(n>a&&n>o||!M(o))&&(this.log(`Updating Media Source duration to ${n.toFixed(3)}`),r.duration=n)}updateSeekableRange(e){let t=this.mediaSource,i=e.fragments;if(i.length&&e.live&&t!=null&&t.setLiveSeekableRange){let n=Math.max(0,i[0].start),o=Math.max(n,n+e.totalduration);this.log(`Media Source duration is set to ${t.duration}. Setting seekable range to ${n}-${o}.`),t.setLiveSeekableRange(n,o)}}checkPendingTracks(){let{bufferCodecEventsExpected:e,operationQueue:t,pendingTracks:i}=this,r=Object.keys(i).length;if(r&&(!e||r===2||"audiovideo"in i)){this.createSourceBuffers(i),this.pendingTracks={};let n=this.getSourceBufferTypes();if(n.length)this.hls.trigger(p.BUFFER_CREATED,{tracks:this.tracks}),n.forEach(o=>{t.executeNext(o)});else{let o=new Error("could not create source buffer for media codec(s)");this.hls.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.BUFFER_INCOMPATIBLE_CODECS_ERROR,fatal:!0,error:o,reason:o.message})}}}createSourceBuffers(e){let{sourceBuffer:t,mediaSource:i}=this;if(!i)throw Error("createSourceBuffers called when mediaSource was null");for(let n in e)if(!t[n]){var r;let o=e[n];if(!o)throw Error(`source buffer exists for track ${n}, however track does not`);let a=((r=o.levelCodec)==null?void 0:r.indexOf(","))===-1?o.levelCodec:o.codec;a&&n.slice(0,5)==="audio"&&(a=Mt(a,this.appendSource));let l=`${o.container};codecs=${a}`;this.log(`creating sourceBuffer(${l})`);try{let c=t[n]=i.addSourceBuffer(l),h=n;this.addBufferListener(h,"updatestart",this._onSBUpdateStart),this.addBufferListener(h,"updateend",this._onSBUpdateEnd),this.addBufferListener(h,"error",this._onSBUpdateError),this.appendSource&&this.addBufferListener(h,"bufferedchange",(u,d)=>{let f=d.removedRanges;f!=null&&f.length&&this.hls.trigger(p.BUFFER_FLUSHED,{type:n})}),this.tracks[n]={buffer:c,codec:a,container:o.container,levelCodec:o.levelCodec,metadata:o.metadata,id:o.id}}catch(c){this.error(`error while trying to add sourceBuffer: ${c.message}`),this.hls.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.BUFFER_ADD_CODEC_ERROR,fatal:!1,error:c,sourceBufferName:n,mimeType:l})}}}get mediaSrc(){var e;let t=((e=this.media)==null?void 0:e.firstChild)||this.media;return t==null?void 0:t.src}_onSBUpdateStart(e){let{operationQueue:t}=this;t.current(e).onStart()}_onSBUpdateEnd(e){var t;if(((t=this.mediaSource)==null?void 0:t.readyState)==="closed"){this.resetBuffer(e);return}let{operationQueue:i}=this;i.current(e).onComplete(),i.shiftAndExecuteNext(e)}_onSBUpdateError(e,t){var i;let r=new Error(`${e} SourceBuffer error. MediaSource readyState: ${(i=this.mediaSource)==null?void 0:i.readyState}`);this.error(`${r}`,t),this.hls.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.BUFFER_APPENDING_ERROR,sourceBufferName:e,error:r,fatal:!1});let n=this.operationQueue.current(e);n&&n.onError(r)}removeExecutor(e,t,i){let{media:r,mediaSource:n,operationQueue:o,sourceBuffer:a}=this,l=a[e];if(!r||!n||!l){this.warn(`Attempting to remove from the ${e} SourceBuffer, but it does not exist`),o.shiftAndExecuteNext(e);return}let c=M(r.duration)?r.duration:1/0,h=M(n.duration)?n.duration:1/0,u=Math.max(0,t),d=Math.min(i,c,h);d>u&&(!l.ending||l.ended)?(l.ended=!1,this.log(`Removing [${u},${d}] from the ${e} SourceBuffer`),l.remove(u,d)):o.shiftAndExecuteNext(e)}appendExecutor(e,t){let i=this.sourceBuffer[t];if(!i){if(!this.pendingTracks[t])throw new Error(`Attempting to append to the ${t} SourceBuffer, but it does not exist`);return}i.ended=!1,i.appendBuffer(e)}blockBuffers(e,t=this.getSourceBufferTypes()){if(!t.length){this.log("Blocking operation requested, but no SourceBuffers exist"),Promise.resolve().then(e);return}let{operationQueue:i}=this,r=t.map(n=>i.appendBlocker(n));Promise.all(r).then(()=>{e(),t.forEach(n=>{let o=this.sourceBuffer[n];o!=null&&o.updating||i.shiftAndExecuteNext(n)})})}getSourceBufferTypes(){return Object.keys(this.sourceBuffer)}addBufferListener(e,t,i){let r=this.sourceBuffer[e];if(!r)return;let n=i.bind(this,e);this.listeners[e].push({event:t,listener:n}),r.addEventListener(t,n)}removeBufferListeners(e){let t=this.sourceBuffer[e];t&&this.listeners[e].forEach(i=>{t.removeEventListener(i.event,i.listener)})}};function Cr(s){let e=s.querySelectorAll("source");[].slice.call(e).forEach(t=>{s.removeChild(t)})}function al(s,e){let t=self.document.createElement("source");t.type="video/mp4",t.src=e,s.appendChild(t)}var Dr={42:225,92:233,94:237,95:243,96:250,123:231,124:247,125:209,126:241,127:9608,128:174,129:176,130:189,131:191,132:8482,133:162,134:163,135:9834,136:224,137:32,138:232,139:226,140:234,141:238,142:244,143:251,144:193,145:201,146:211,147:218,148:220,149:252,150:8216,151:161,152:42,153:8217,154:9473,155:169,156:8480,157:8226,158:8220,159:8221,160:192,161:194,162:199,163:200,164:202,165:203,166:235,167:206,168:207,169:239,170:212,171:217,172:249,173:219,174:171,175:187,176:195,177:227,178:205,179:204,180:236,181:210,182:242,183:213,184:245,185:123,186:125,187:92,188:94,189:95,190:124,191:8764,192:196,193:228,194:214,195:246,196:223,197:165,198:164,199:9475,200:197,201:229,202:216,203:248,204:9487,205:9491,206:9495,207:9499},Dn=function(e){let t=e;return Dr.hasOwnProperty(e)&&(t=Dr[e]),String.fromCharCode(t)},ve=15,Ce=100,ll={17:1,18:3,21:5,22:7,23:9,16:11,19:12,20:14},cl={17:2,18:4,21:6,22:8,23:10,19:13,20:15},hl={25:1,26:3,29:5,30:7,31:9,24:11,27:12,28:14},ul={25:2,26:4,29:6,30:8,31:10,27:13,28:15},dl=["white","green","blue","cyan","red","yellow","magenta","black","transparent"],ds=class{constructor(){this.time=null,this.verboseLevel=0}log(e,t){if(this.verboseLevel>=e){let i=typeof t=="function"?t():t;S.log(`${this.time} [${e}] ${i}`)}}},Me=function(e){let t=[];for(let i=0;i<e.length;i++)t.push(e[i].toString(16));return t},zt=class{constructor(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1}reset(){this.foreground="white",this.underline=!1,this.italics=!1,this.background="black",this.flash=!1}setStyles(e){let t=["foreground","underline","italics","background","flash"];for(let i=0;i<t.length;i++){let r=t[i];e.hasOwnProperty(r)&&(this[r]=e[r])}}isDefault(){return this.foreground==="white"&&!this.underline&&!this.italics&&this.background==="black"&&!this.flash}equals(e){return this.foreground===e.foreground&&this.underline===e.underline&&this.italics===e.italics&&this.background===e.background&&this.flash===e.flash}copy(e){this.foreground=e.foreground,this.underline=e.underline,this.italics=e.italics,this.background=e.background,this.flash=e.flash}toString(){return"color="+this.foreground+", underline="+this.underline+", italics="+this.italics+", background="+this.background+", flash="+this.flash}},fs=class{constructor(){this.uchar=" ",this.penState=new zt}reset(){this.uchar=" ",this.penState.reset()}setChar(e,t){this.uchar=e,this.penState.copy(t)}setPenState(e){this.penState.copy(e)}equals(e){return this.uchar===e.uchar&&this.penState.equals(e.penState)}copy(e){this.uchar=e.uchar,this.penState.copy(e.penState)}isEmpty(){return this.uchar===" "&&this.penState.isDefault()}},gs=class{constructor(e){this.chars=[],this.pos=0,this.currPenState=new zt,this.cueStartTime=null,this.logger=void 0;for(let t=0;t<Ce;t++)this.chars.push(new fs);this.logger=e}equals(e){for(let t=0;t<Ce;t++)if(!this.chars[t].equals(e.chars[t]))return!1;return!0}copy(e){for(let t=0;t<Ce;t++)this.chars[t].copy(e.chars[t])}isEmpty(){let e=!0;for(let t=0;t<Ce;t++)if(!this.chars[t].isEmpty()){e=!1;break}return e}setCursor(e){this.pos!==e&&(this.pos=e),this.pos<0?(this.logger.log(3,"Negative cursor position "+this.pos),this.pos=0):this.pos>Ce&&(this.logger.log(3,"Too large cursor position "+this.pos),this.pos=Ce)}moveCursor(e){let t=this.pos+e;if(e>1)for(let i=this.pos+1;i<t+1;i++)this.chars[i].setPenState(this.currPenState);this.setCursor(t)}backSpace(){this.moveCursor(-1),this.chars[this.pos].setChar(" ",this.currPenState)}insertChar(e){e>=144&&this.backSpace();let t=Dn(e);if(this.pos>=Ce){this.logger.log(0,()=>"Cannot insert "+e.toString(16)+" ("+t+") at position "+this.pos+". Skipping it!");return}this.chars[this.pos].setChar(t,this.currPenState),this.moveCursor(1)}clearFromPos(e){let t;for(t=e;t<Ce;t++)this.chars[t].reset()}clear(){this.clearFromPos(0),this.pos=0,this.currPenState.reset()}clearToEndOfRow(){this.clearFromPos(this.pos)}getTextString(){let e=[],t=!0;for(let i=0;i<Ce;i++){let r=this.chars[i].uchar;r!==" "&&(t=!1),e.push(r)}return t?"":e.join("")}setPenStyles(e){this.currPenState.setStyles(e),this.chars[this.pos].setPenState(this.currPenState)}},rt=class{constructor(e){this.rows=[],this.currRow=ve-1,this.nrRollUpRows=null,this.lastOutputScreen=null,this.logger=void 0;for(let t=0;t<ve;t++)this.rows.push(new gs(e));this.logger=e}reset(){for(let e=0;e<ve;e++)this.rows[e].clear();this.currRow=ve-1}equals(e){let t=!0;for(let i=0;i<ve;i++)if(!this.rows[i].equals(e.rows[i])){t=!1;break}return t}copy(e){for(let t=0;t<ve;t++)this.rows[t].copy(e.rows[t])}isEmpty(){let e=!0;for(let t=0;t<ve;t++)if(!this.rows[t].isEmpty()){e=!1;break}return e}backSpace(){this.rows[this.currRow].backSpace()}clearToEndOfRow(){this.rows[this.currRow].clearToEndOfRow()}insertChar(e){this.rows[this.currRow].insertChar(e)}setPen(e){this.rows[this.currRow].setPenStyles(e)}moveCursor(e){this.rows[this.currRow].moveCursor(e)}setCursor(e){this.logger.log(2,"setCursor: "+e),this.rows[this.currRow].setCursor(e)}setPAC(e){this.logger.log(2,()=>"pacData = "+JSON.stringify(e));let t=e.row-1;if(this.nrRollUpRows&&t<this.nrRollUpRows-1&&(t=this.nrRollUpRows-1),this.nrRollUpRows&&this.currRow!==t){for(let a=0;a<ve;a++)this.rows[a].clear();let n=this.currRow+1-this.nrRollUpRows,o=this.lastOutputScreen;if(o){let a=o.rows[n].cueStartTime,l=this.logger.time;if(a!==null&&l!==null&&a<l)for(let c=0;c<this.nrRollUpRows;c++)this.rows[t-this.nrRollUpRows+c+1].copy(o.rows[n+c])}}this.currRow=t;let i=this.rows[this.currRow];if(e.indent!==null){let n=e.indent,o=Math.max(n-1,0);i.setCursor(e.indent),e.color=i.chars[o].penState.foreground}let r={foreground:e.color,underline:e.underline,italics:e.italics,background:"black",flash:!1};this.setPen(r)}setBkgData(e){this.logger.log(2,()=>"bkgData = "+JSON.stringify(e)),this.backSpace(),this.setPen(e),this.insertChar(32)}setRollUpRows(e){this.nrRollUpRows=e}rollUp(){if(this.nrRollUpRows===null){this.logger.log(3,"roll_up but nrRollUpRows not set yet");return}this.logger.log(1,()=>this.getDisplayText());let e=this.currRow+1-this.nrRollUpRows,t=this.rows.splice(e,1)[0];t.clear(),this.rows.splice(this.currRow,0,t),this.logger.log(2,"Rolling up")}getDisplayText(e){e=e||!1;let t=[],i="",r=-1;for(let n=0;n<ve;n++){let o=this.rows[n].getTextString();o&&(r=n+1,e?t.push("Row "+r+": '"+o+"'"):t.push(o.trim()))}return t.length>0&&(e?i="["+t.join(" | ")+"]":i=t.join(`
`)),i}getTextAndFormat(){return this.rows}},Xt=class{constructor(e,t,i){this.chNr=void 0,this.outputFilter=void 0,this.mode=void 0,this.verbose=void 0,this.displayedMemory=void 0,this.nonDisplayedMemory=void 0,this.lastOutputScreen=void 0,this.currRollUpRow=void 0,this.writeScreen=void 0,this.cueStartTime=void 0,this.logger=void 0,this.chNr=e,this.outputFilter=t,this.mode=null,this.verbose=0,this.displayedMemory=new rt(i),this.nonDisplayedMemory=new rt(i),this.lastOutputScreen=new rt(i),this.currRollUpRow=this.displayedMemory.rows[ve-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null,this.logger=i}reset(){this.mode=null,this.displayedMemory.reset(),this.nonDisplayedMemory.reset(),this.lastOutputScreen.reset(),this.outputFilter.reset(),this.currRollUpRow=this.displayedMemory.rows[ve-1],this.writeScreen=this.displayedMemory,this.mode=null,this.cueStartTime=null}getHandler(){return this.outputFilter}setHandler(e){this.outputFilter=e}setPAC(e){this.writeScreen.setPAC(e)}setBkgData(e){this.writeScreen.setBkgData(e)}setMode(e){e!==this.mode&&(this.mode=e,this.logger.log(2,()=>"MODE="+e),this.mode==="MODE_POP-ON"?this.writeScreen=this.nonDisplayedMemory:(this.writeScreen=this.displayedMemory,this.writeScreen.reset()),this.mode!=="MODE_ROLL-UP"&&(this.displayedMemory.nrRollUpRows=null,this.nonDisplayedMemory.nrRollUpRows=null),this.mode=e)}insertChars(e){for(let i=0;i<e.length;i++)this.writeScreen.insertChar(e[i]);let t=this.writeScreen===this.displayedMemory?"DISP":"NON_DISP";this.logger.log(2,()=>t+": "+this.writeScreen.getDisplayText(!0)),(this.mode==="MODE_PAINT-ON"||this.mode==="MODE_ROLL-UP")&&(this.logger.log(1,()=>"DISPLAYED: "+this.displayedMemory.getDisplayText(!0)),this.outputDataUpdate())}ccRCL(){this.logger.log(2,"RCL - Resume Caption Loading"),this.setMode("MODE_POP-ON")}ccBS(){this.logger.log(2,"BS - BackSpace"),this.mode!=="MODE_TEXT"&&(this.writeScreen.backSpace(),this.writeScreen===this.displayedMemory&&this.outputDataUpdate())}ccAOF(){}ccAON(){}ccDER(){this.logger.log(2,"DER- Delete to End of Row"),this.writeScreen.clearToEndOfRow(),this.outputDataUpdate()}ccRU(e){this.logger.log(2,"RU("+e+") - Roll Up"),this.writeScreen=this.displayedMemory,this.setMode("MODE_ROLL-UP"),this.writeScreen.setRollUpRows(e)}ccFON(){this.logger.log(2,"FON - Flash On"),this.writeScreen.setPen({flash:!0})}ccRDC(){this.logger.log(2,"RDC - Resume Direct Captioning"),this.setMode("MODE_PAINT-ON")}ccTR(){this.logger.log(2,"TR"),this.setMode("MODE_TEXT")}ccRTD(){this.logger.log(2,"RTD"),this.setMode("MODE_TEXT")}ccEDM(){this.logger.log(2,"EDM - Erase Displayed Memory"),this.displayedMemory.reset(),this.outputDataUpdate(!0)}ccCR(){this.logger.log(2,"CR - Carriage Return"),this.writeScreen.rollUp(),this.outputDataUpdate(!0)}ccENM(){this.logger.log(2,"ENM - Erase Non-displayed Memory"),this.nonDisplayedMemory.reset()}ccEOC(){if(this.logger.log(2,"EOC - End Of Caption"),this.mode==="MODE_POP-ON"){let e=this.displayedMemory;this.displayedMemory=this.nonDisplayedMemory,this.nonDisplayedMemory=e,this.writeScreen=this.nonDisplayedMemory,this.logger.log(1,()=>"DISP: "+this.displayedMemory.getDisplayText())}this.outputDataUpdate(!0)}ccTO(e){this.logger.log(2,"TO("+e+") - Tab Offset"),this.writeScreen.moveCursor(e)}ccMIDROW(e){let t={flash:!1};if(t.underline=e%2===1,t.italics=e>=46,t.italics)t.foreground="white";else{let i=Math.floor(e/2)-16,r=["white","green","blue","cyan","red","yellow","magenta"];t.foreground=r[i]}this.logger.log(2,"MIDROW: "+JSON.stringify(t)),this.writeScreen.setPen(t)}outputDataUpdate(e=!1){let t=this.logger.time;t!==null&&this.outputFilter&&(this.cueStartTime===null&&!this.displayedMemory.isEmpty()?this.cueStartTime=t:this.displayedMemory.equals(this.lastOutputScreen)||(this.outputFilter.newCue(this.cueStartTime,t,this.lastOutputScreen),e&&this.outputFilter.dispatchCue&&this.outputFilter.dispatchCue(),this.cueStartTime=this.displayedMemory.isEmpty()?null:t),this.lastOutputScreen.copy(this.displayedMemory))}cueSplitAtTime(e){this.outputFilter&&(this.displayedMemory.isEmpty()||(this.outputFilter.newCue&&this.outputFilter.newCue(this.cueStartTime,e,this.displayedMemory),this.cueStartTime=e))}},Qt=class{constructor(e,t,i){this.channels=void 0,this.currentChannel=0,this.cmdHistory=Pr(),this.logger=void 0;let r=this.logger=new ds;this.channels=[null,new Xt(e,t,r),new Xt(e+1,i,r)]}getHandler(e){return this.channels[e].getHandler()}setHandler(e,t){this.channels[e].setHandler(t)}addData(e,t){let i,r,n,o=!1;this.logger.time=e;for(let a=0;a<t.length;a+=2)if(r=t[a]&127,n=t[a+1]&127,!(r===0&&n===0)){if(this.logger.log(3,"["+Me([t[a],t[a+1]])+"] -> ("+Me([r,n])+")"),i=this.parseCmd(r,n),i||(i=this.parseMidrow(r,n)),i||(i=this.parsePAC(r,n)),i||(i=this.parseBackgroundAttributes(r,n)),!i&&(o=this.parseChars(r,n),o)){let l=this.currentChannel;l&&l>0?this.channels[l].insertChars(o):this.logger.log(2,"No channel found yet. TEXT-MODE?")}!i&&!o&&this.logger.log(2,"Couldn't parse cleaned data "+Me([r,n])+" orig: "+Me([t[a],t[a+1]]))}}parseCmd(e,t){let{cmdHistory:i}=this,r=(e===20||e===28||e===21||e===29)&&t>=32&&t<=47,n=(e===23||e===31)&&t>=33&&t<=35;if(!(r||n))return!1;if(kr(e,t,i))return Ye(null,null,i),this.logger.log(3,"Repeated command ("+Me([e,t])+") is dropped"),!0;let o=e===20||e===21||e===23?1:2,a=this.channels[o];return e===20||e===21||e===28||e===29?t===32?a.ccRCL():t===33?a.ccBS():t===34?a.ccAOF():t===35?a.ccAON():t===36?a.ccDER():t===37?a.ccRU(2):t===38?a.ccRU(3):t===39?a.ccRU(4):t===40?a.ccFON():t===41?a.ccRDC():t===42?a.ccTR():t===43?a.ccRTD():t===44?a.ccEDM():t===45?a.ccCR():t===46?a.ccENM():t===47&&a.ccEOC():a.ccTO(t-32),Ye(e,t,i),this.currentChannel=o,!0}parseMidrow(e,t){let i=0;if((e===17||e===25)&&t>=32&&t<=47){if(e===17?i=1:i=2,i!==this.currentChannel)return this.logger.log(0,"Mismatch channel in midrow parsing"),!1;let r=this.channels[i];return r?(r.ccMIDROW(t),this.logger.log(3,"MIDROW ("+Me([e,t])+")"),!0):!1}return!1}parsePAC(e,t){let i,r=this.cmdHistory,n=(e>=17&&e<=23||e>=25&&e<=31)&&t>=64&&t<=127,o=(e===16||e===24)&&t>=64&&t<=95;if(!(n||o))return!1;if(kr(e,t,r))return Ye(null,null,r),!0;let a=e<=23?1:2;t>=64&&t<=95?i=a===1?ll[e]:hl[e]:i=a===1?cl[e]:ul[e];let l=this.channels[a];return l?(l.setPAC(this.interpretPAC(i,t)),Ye(e,t,r),this.currentChannel=a,!0):!1}interpretPAC(e,t){let i,r={color:null,italics:!1,indent:null,underline:!1,row:e};return t>95?i=t-96:i=t-64,r.underline=(i&1)===1,i<=13?r.color=["white","green","blue","cyan","red","yellow","magenta","white"][Math.floor(i/2)]:i<=15?(r.italics=!0,r.color="white"):r.indent=Math.floor((i-16)/2)*4,r}parseChars(e,t){let i,r=null,n=null;if(e>=25?(i=2,n=e-8):(i=1,n=e),n>=17&&n<=19){let o;n===17?o=t+80:n===18?o=t+112:o=t+144,this.logger.log(2,"Special char '"+Dn(o)+"' in channel "+i),r=[o]}else e>=32&&e<=127&&(r=t===0?[e]:[e,t]);if(r){let o=Me(r);this.logger.log(3,"Char codes =  "+o.join(",")),Ye(e,t,this.cmdHistory)}return r}parseBackgroundAttributes(e,t){let i=(e===16||e===24)&&t>=32&&t<=47,r=(e===23||e===31)&&t>=45&&t<=47;if(!(i||r))return!1;let n,o={};e===16||e===24?(n=Math.floor((t-32)/2),o.background=dl[n],t%2===1&&(o.background=o.background+"_semi")):t===45?o.background="transparent":(o.foreground="black",t===47&&(o.underline=!0));let a=e<=23?1:2;return this.channels[a].setBkgData(o),Ye(e,t,this.cmdHistory),!0}reset(){for(let e=0;e<Object.keys(this.channels).length;e++){let t=this.channels[e];t&&t.reset()}this.cmdHistory=Pr()}cueSplitAtTime(e){for(let t=0;t<this.channels.length;t++){let i=this.channels[t];i&&i.cueSplitAtTime(e)}}};function Ye(s,e,t){t.a=s,t.b=e}function kr(s,e,t){return t.a===s&&t.b===e}function Pr(){return{a:null,b:null}}var qe=class{constructor(e,t){this.timelineController=void 0,this.cueRanges=[],this.trackName=void 0,this.startTime=null,this.endTime=null,this.screen=null,this.timelineController=e,this.trackName=t}dispatchCue(){this.startTime!==null&&(this.timelineController.addCues(this.trackName,this.startTime,this.endTime,this.screen,this.cueRanges),this.startTime=null)}newCue(e,t,i){(this.startTime===null||this.startTime>e)&&(this.startTime=e),this.endTime=t,this.screen=i,this.timelineController.createCaptionsTrack(this.trackName)}reset(){this.cueRanges=[],this.startTime=null}},Hs=function(){if(Qe!=null&&Qe.VTTCue)return self.VTTCue;let s=["","lr","rl"],e=["start","middle","end","left","right"];function t(a,l){if(typeof l!="string"||!Array.isArray(a))return!1;let c=l.toLowerCase();return~a.indexOf(c)?c:!1}function i(a){return t(s,a)}function r(a){return t(e,a)}function n(a,...l){let c=1;for(;c<arguments.length;c++){let h=arguments[c];for(let u in h)a[u]=h[u]}return a}function o(a,l,c){let h=this,u={enumerable:!0};h.hasBeenReset=!1;let d="",f=!1,g=a,m=l,y=c,T=null,v="",E=!0,_="auto",x="start",I=50,L="middle",C=50,k="middle";Object.defineProperty(h,"id",n({},u,{get:function(){return d},set:function(R){d=""+R}})),Object.defineProperty(h,"pauseOnExit",n({},u,{get:function(){return f},set:function(R){f=!!R}})),Object.defineProperty(h,"startTime",n({},u,{get:function(){return g},set:function(R){if(typeof R!="number")throw new TypeError("Start time must be set to a number.");g=R,this.hasBeenReset=!0}})),Object.defineProperty(h,"endTime",n({},u,{get:function(){return m},set:function(R){if(typeof R!="number")throw new TypeError("End time must be set to a number.");m=R,this.hasBeenReset=!0}})),Object.defineProperty(h,"text",n({},u,{get:function(){return y},set:function(R){y=""+R,this.hasBeenReset=!0}})),Object.defineProperty(h,"region",n({},u,{get:function(){return T},set:function(R){T=R,this.hasBeenReset=!0}})),Object.defineProperty(h,"vertical",n({},u,{get:function(){return v},set:function(R){let D=i(R);if(D===!1)throw new SyntaxError("An invalid or illegal string was specified.");v=D,this.hasBeenReset=!0}})),Object.defineProperty(h,"snapToLines",n({},u,{get:function(){return E},set:function(R){E=!!R,this.hasBeenReset=!0}})),Object.defineProperty(h,"line",n({},u,{get:function(){return _},set:function(R){if(typeof R!="number"&&R!=="auto")throw new SyntaxError("An invalid number or illegal string was specified.");_=R,this.hasBeenReset=!0}})),Object.defineProperty(h,"lineAlign",n({},u,{get:function(){return x},set:function(R){let D=r(R);if(!D)throw new SyntaxError("An invalid or illegal string was specified.");x=D,this.hasBeenReset=!0}})),Object.defineProperty(h,"position",n({},u,{get:function(){return I},set:function(R){if(R<0||R>100)throw new Error("Position must be between 0 and 100.");I=R,this.hasBeenReset=!0}})),Object.defineProperty(h,"positionAlign",n({},u,{get:function(){return L},set:function(R){let D=r(R);if(!D)throw new SyntaxError("An invalid or illegal string was specified.");L=D,this.hasBeenReset=!0}})),Object.defineProperty(h,"size",n({},u,{get:function(){return C},set:function(R){if(R<0||R>100)throw new Error("Size must be between 0 and 100.");C=R,this.hasBeenReset=!0}})),Object.defineProperty(h,"align",n({},u,{get:function(){return k},set:function(R){let D=r(R);if(!D)throw new SyntaxError("An invalid or illegal string was specified.");k=D,this.hasBeenReset=!0}})),h.displayState=void 0}return o.prototype.getCueAsHTML=function(){return self.WebVTT.convertCueToDOMTree(self,this.text)},o}(),ps=class{decode(e,t){if(!e)return"";if(typeof e!="string")throw new Error("Error - expected string data.");return decodeURIComponent(encodeURIComponent(e))}};function kn(s){function e(i,r,n,o){return(i|0)*3600+(r|0)*60+(n|0)+parseFloat(o||0)}let t=s.match(/^(?:(\d+):)?(\d{2}):(\d{2})(\.\d+)?/);return t?parseFloat(t[2])>59?e(t[2],t[3],0,t[4]):e(t[1],t[2],t[3],t[4]):null}var ms=class{constructor(){this.values=Object.create(null)}set(e,t){!this.get(e)&&t!==""&&(this.values[e]=t)}get(e,t,i){return i?this.has(e)?this.values[e]:t[i]:this.has(e)?this.values[e]:t}has(e){return e in this.values}alt(e,t,i){for(let r=0;r<i.length;++r)if(t===i[r]){this.set(e,t);break}}integer(e,t){/^-?\d+$/.test(t)&&this.set(e,parseInt(t,10))}percent(e,t){if(/^([\d]{1,3})(\.[\d]*)?%$/.test(t)){let i=parseFloat(t);if(i>=0&&i<=100)return this.set(e,i),!0}return!1}};function Pn(s,e,t,i){let r=i?s.split(i):[s];for(let n in r){if(typeof r[n]!="string")continue;let o=r[n].split(t);if(o.length!==2)continue;let a=o[0],l=o[1];e(a,l)}}var ys=new Hs(0,0,""),vt=ys.align==="middle"?"middle":"center";function fl(s,e,t){let i=s;function r(){let a=kn(s);if(a===null)throw new Error("Malformed timestamp: "+i);return s=s.replace(/^[^\sa-zA-Z-]+/,""),a}function n(a,l){let c=new ms;Pn(a,function(d,f){let g;switch(d){case"region":for(let m=t.length-1;m>=0;m--)if(t[m].id===f){c.set(d,t[m].region);break}break;case"vertical":c.alt(d,f,["rl","lr"]);break;case"line":g=f.split(","),c.integer(d,g[0]),c.percent(d,g[0])&&c.set("snapToLines",!1),c.alt(d,g[0],["auto"]),g.length===2&&c.alt("lineAlign",g[1],["start",vt,"end"]);break;case"position":g=f.split(","),c.percent(d,g[0]),g.length===2&&c.alt("positionAlign",g[1],["start",vt,"end","line-left","line-right","auto"]);break;case"size":c.percent(d,f);break;case"align":c.alt(d,f,["start",vt,"end","left","right"]);break}},/:/,/\s/),l.region=c.get("region",null),l.vertical=c.get("vertical","");let h=c.get("line","auto");h==="auto"&&ys.line===-1&&(h=-1),l.line=h,l.lineAlign=c.get("lineAlign","start"),l.snapToLines=c.get("snapToLines",!0),l.size=c.get("size",100),l.align=c.get("align",vt);let u=c.get("position","auto");u==="auto"&&ys.position===50&&(u=l.align==="start"||l.align==="left"?0:l.align==="end"||l.align==="right"?100:50),l.position=u}function o(){s=s.replace(/^\s+/,"")}if(o(),e.startTime=r(),o(),s.slice(0,3)!=="-->")throw new Error("Malformed time stamp (time stamps must be separated by '-->'): "+i);s=s.slice(3),o(),e.endTime=r(),o(),n(s,e)}function On(s){return s.replace(/<br(?: \/)?>/gi,`
`)}var Ts=class{constructor(){this.state="INITIAL",this.buffer="",this.decoder=new ps,this.regionList=[],this.cue=null,this.oncue=void 0,this.onparsingerror=void 0,this.onflush=void 0}parse(e){let t=this;e&&(t.buffer+=t.decoder.decode(e,{stream:!0}));function i(){let n=t.buffer,o=0;for(n=On(n);o<n.length&&n[o]!=="\r"&&n[o]!==`
`;)++o;let a=n.slice(0,o);return n[o]==="\r"&&++o,n[o]===`
`&&++o,t.buffer=n.slice(o),a}function r(n){Pn(n,function(o,a){},/:/)}try{let n="";if(t.state==="INITIAL"){if(!/\r\n|\n/.test(t.buffer))return this;n=i();let a=n.match(/^(ï»¿)?WEBVTT([ \t].*)?$/);if(!(a!=null&&a[0]))throw new Error("Malformed WebVTT signature.");t.state="HEADER"}let o=!1;for(;t.buffer;){if(!/\r\n|\n/.test(t.buffer))return this;switch(o?o=!1:n=i(),t.state){case"HEADER":/:/.test(n)?r(n):n||(t.state="ID");continue;case"NOTE":n||(t.state="ID");continue;case"ID":if(/^NOTE($|[ \t])/.test(n)){t.state="NOTE";break}if(!n)continue;if(t.cue=new Hs(0,0,""),t.state="CUE",n.indexOf("-->")===-1){t.cue.id=n;continue}case"CUE":if(!t.cue){t.state="BADCUE";continue}try{fl(n,t.cue,t.regionList)}catch(a){t.cue=null,t.state="BADCUE";continue}t.state="CUETEXT";continue;case"CUETEXT":{let a=n.indexOf("-->")!==-1;if(!n||a&&(o=!0)){t.oncue&&t.cue&&t.oncue(t.cue),t.cue=null,t.state="ID";continue}if(t.cue===null)continue;t.cue.text&&(t.cue.text+=`
`),t.cue.text+=n}continue;case"BADCUE":n||(t.state="ID")}}}catch(n){t.state==="CUETEXT"&&t.cue&&t.oncue&&t.oncue(t.cue),t.cue=null,t.state=t.state==="INITIAL"?"BADWEBVTT":"BADCUE"}return this}flush(){let e=this;try{if((e.cue||e.state==="HEADER")&&(e.buffer+=`

`,e.parse()),e.state==="INITIAL"||e.state==="BADWEBVTT")throw new Error("Malformed WebVTT signature.")}catch(t){e.onparsingerror&&e.onparsingerror(t)}return e.onflush&&e.onflush(),this}},gl=/\r\n|\n\r|\n|\r/g,yi=function(e,t,i=0){return e.slice(i,i+t.length)===t},pl=function(e){let t=parseInt(e.slice(-3)),i=parseInt(e.slice(-6,-4)),r=parseInt(e.slice(-9,-7)),n=e.length>9?parseInt(e.substring(0,e.indexOf(":"))):0;if(!M(t)||!M(i)||!M(r)||!M(n))throw Error(`Malformed X-TIMESTAMP-MAP: Local:${e}`);return t+=1e3*i,t+=60*1e3*r,t+=60*60*1e3*n,t},Ti=function(e){let t=5381,i=e.length;for(;i;)t=t*33^e.charCodeAt(--i);return(t>>>0).toString()};function Ks(s,e,t){return Ti(s.toString())+Ti(e.toString())+Ti(t)}var ml=function(e,t,i){let r=e[t],n=e[r.prevCC];if(!n||!n.new&&r.new){e.ccOffset=e.presentationOffset=r.start,r.new=!1;return}for(;(o=n)!=null&&o.new;){var o;e.ccOffset+=r.start-n.start,r.new=!1,r=n,n=e[r.prevCC]}e.presentationOffset=i};function yl(s,e,t,i,r,n,o){let a=new Ts,l=Re(new Uint8Array(s)).trim().replace(gl,`
`).split(`
`),c=[],h=e?Za(e.baseTime,e.timescale):0,u="00:00.000",d=0,f=0,g,m=!0;a.oncue=function(y){let T=t[i],v=t.ccOffset,E=(d-h)/9e4;if(T!=null&&T.new&&(f!==void 0?v=t.ccOffset=T.start:ml(t,i,E)),E){if(!e){g=new Error("Missing initPTS for VTT MPEGTS");return}v=E-t.presentationOffset}let _=y.endTime-y.startTime,x=ye((y.startTime+v-f)*9e4,r*9e4)/9e4;y.startTime=Math.max(x,0),y.endTime=Math.max(x+_,0);let I=y.text.trim();y.text=decodeURIComponent(encodeURIComponent(I)),y.id||(y.id=Ks(y.startTime,y.endTime,I)),y.endTime>0&&c.push(y)},a.onparsingerror=function(y){g=y},a.onflush=function(){if(g){o(g);return}n(c)},l.forEach(y=>{if(m)if(yi(y,"X-TIMESTAMP-MAP=")){m=!1,y.slice(16).split(",").forEach(T=>{yi(T,"LOCAL:")?u=T.slice(6):yi(T,"MPEGTS:")&&(d=parseInt(T.slice(7)))});try{f=pl(u)/1e3}catch(T){g=T}return}else y===""&&(m=!1);a.parse(y+`
`)}),a.flush()}var Ei="stpp.ttml.im1t",Mn=/^(\d{2,}):(\d{2}):(\d{2}):(\d{2})\.?(\d+)?$/,Fn=/^(\d*(?:\.\d*)?)(h|m|s|ms|f|t)$/,Tl={left:"start",center:"center",right:"end",start:"start",end:"end"};function Or(s,e,t,i){let r=Y(new Uint8Array(s),["mdat"]);if(r.length===0){i(new Error("Could not parse IMSC1 mdat"));return}let n=r.map(a=>Re(a)),o=Ja(e.baseTime,1,e.timescale);try{n.forEach(a=>t(El(a,o)))}catch(a){i(a)}}function El(s,e){let r=new DOMParser().parseFromString(s,"text/xml").getElementsByTagName("tt")[0];if(!r)throw new Error("Invalid ttml");let n={frameRate:30,subFrameRate:1,frameRateMultiplier:0,tickRate:0},o=Object.keys(n).reduce((u,d)=>(u[d]=r.getAttribute(`ttp:${d}`)||n[d],u),{}),a=r.getAttribute("xml:space")!=="preserve",l=Mr(vi(r,"styling","style")),c=Mr(vi(r,"layout","region")),h=vi(r,"body","[begin]");return[].map.call(h,u=>{let d=Nn(u,a);if(!d||!u.hasAttribute("begin"))return null;let f=Si(u.getAttribute("begin"),o),g=Si(u.getAttribute("dur"),o),m=Si(u.getAttribute("end"),o);if(f===null)throw Fr(u);if(m===null){if(g===null)throw Fr(u);m=f+g}let y=new Hs(f-e,m-e,d);y.id=Ks(y.startTime,y.endTime,y.text);let T=c[u.getAttribute("region")],v=l[u.getAttribute("style")],E=vl(T,v,l),{textAlign:_}=E;if(_){let x=Tl[_];x&&(y.lineAlign=x),y.align=_}return se(y,E),y}).filter(u=>u!==null)}function vi(s,e,t){let i=s.getElementsByTagName(e)[0];return i?[].slice.call(i.querySelectorAll(t)):[]}function Mr(s){return s.reduce((e,t)=>{let i=t.getAttribute("xml:id");return i&&(e[i]=t),e},{})}function Nn(s,e){return[].slice.call(s.childNodes).reduce((t,i,r)=>{var n;return i.nodeName==="br"&&r?t+`
`:(n=i.childNodes)!=null&&n.length?Nn(i,e):e?t+i.textContent.trim().replace(/\s+/g," "):t+i.textContent},"")}function vl(s,e,t){let i="http://www.w3.org/ns/ttml#styling",r=null,n=["displayAlign","textAlign","color","backgroundColor","fontSize","fontFamily"],o=s!=null&&s.hasAttribute("style")?s.getAttribute("style"):null;return o&&t.hasOwnProperty(o)&&(r=t[o]),n.reduce((a,l)=>{let c=xi(e,i,l)||xi(s,i,l)||xi(r,i,l);return c&&(a[l]=c),a},{})}function xi(s,e,t){return s&&s.hasAttributeNS(e,t)?s.getAttributeNS(e,t):null}function Fr(s){return new Error(`Could not parse ttml timestamp ${s}`)}function Si(s,e){if(!s)return null;let t=kn(s);return t===null&&(Mn.test(s)?t=xl(s,e):Fn.test(s)&&(t=Sl(s,e))),t}function xl(s,e){let t=Mn.exec(s),i=(t[4]|0)+(t[5]|0)/e.subFrameRate;return(t[1]|0)*3600+(t[2]|0)*60+(t[3]|0)+i/e.frameRate}function Sl(s,e){let t=Fn.exec(s),i=Number(t[1]);switch(t[2]){case"h":return i*3600;case"m":return i*60;case"ms":return i*1e3;case"f":return i/e.frameRate;case"t":return i/e.tickRate}return i}var Es=class{constructor(e){this.hls=void 0,this.media=null,this.config=void 0,this.enabled=!0,this.Cues=void 0,this.textTracks=[],this.tracks=[],this.initPTS=[],this.unparsedVttFrags=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.cea608Parser1=void 0,this.cea608Parser2=void 0,this.lastCc=-1,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs=Br(),this.captionsProperties=void 0,this.hls=e,this.config=e.config,this.Cues=e.config.cueHandler,this.captionsProperties={textTrack1:{label:this.config.captionsTextTrack1Label,languageCode:this.config.captionsTextTrack1LanguageCode},textTrack2:{label:this.config.captionsTextTrack2Label,languageCode:this.config.captionsTextTrack2LanguageCode},textTrack3:{label:this.config.captionsTextTrack3Label,languageCode:this.config.captionsTextTrack3LanguageCode},textTrack4:{label:this.config.captionsTextTrack4Label,languageCode:this.config.captionsTextTrack4LanguageCode}},e.on(p.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(p.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.on(p.FRAG_LOADING,this.onFragLoading,this),e.on(p.FRAG_LOADED,this.onFragLoaded,this),e.on(p.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),e.on(p.FRAG_DECRYPTED,this.onFragDecrypted,this),e.on(p.INIT_PTS_FOUND,this.onInitPtsFound,this),e.on(p.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),e.on(p.BUFFER_FLUSHING,this.onBufferFlushing,this)}destroy(){let{hls:e}=this;e.off(p.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(p.SUBTITLE_TRACKS_UPDATED,this.onSubtitleTracksUpdated,this),e.off(p.FRAG_LOADING,this.onFragLoading,this),e.off(p.FRAG_LOADED,this.onFragLoaded,this),e.off(p.FRAG_PARSING_USERDATA,this.onFragParsingUserdata,this),e.off(p.FRAG_DECRYPTED,this.onFragDecrypted,this),e.off(p.INIT_PTS_FOUND,this.onInitPtsFound,this),e.off(p.SUBTITLE_TRACKS_CLEARED,this.onSubtitleTracksCleared,this),e.off(p.BUFFER_FLUSHING,this.onBufferFlushing,this),this.hls=this.config=null,this.cea608Parser1=this.cea608Parser2=void 0}initCea608Parsers(){if(this.config.enableCEA708Captions&&(!this.cea608Parser1||!this.cea608Parser2)){let e=new qe(this,"textTrack1"),t=new qe(this,"textTrack2"),i=new qe(this,"textTrack3"),r=new qe(this,"textTrack4");this.cea608Parser1=new Qt(1,e,t),this.cea608Parser2=new Qt(3,i,r)}}addCues(e,t,i,r,n){let o=!1;for(let a=n.length;a--;){let l=n[a],c=bl(l[0],l[1],t,i);if(c>=0&&(l[0]=Math.min(l[0],t),l[1]=Math.max(l[1],i),o=!0,c/(i-t)>.5))return}if(o||n.push([t,i]),this.config.renderTextTracksNatively){let a=this.captionsTracks[e];this.Cues.newCue(a,t,i,r)}else{let a=this.Cues.newCue(null,t,i,r);this.hls.trigger(p.CUES_PARSED,{type:"captions",cues:a,track:e})}}onInitPtsFound(e,{frag:t,id:i,initPTS:r,timescale:n}){let{unparsedVttFrags:o}=this;i==="main"&&(this.initPTS[t.cc]={baseTime:r,timescale:n}),o.length&&(this.unparsedVttFrags=[],o.forEach(a=>{this.onFragLoaded(p.FRAG_LOADED,a)}))}getExistingTrack(e,t){let{media:i}=this;if(i)for(let r=0;r<i.textTracks.length;r++){let n=i.textTracks[r];if(Nr(n,{name:e,lang:t,attrs:{}}))return n}return null}createCaptionsTrack(e){this.config.renderTextTracksNatively?this.createNativeTrack(e):this.createNonNativeTrack(e)}createNativeTrack(e){if(this.captionsTracks[e])return;let{captionsProperties:t,captionsTracks:i,media:r}=this,{label:n,languageCode:o}=t[e],a=this.getExistingTrack(n,o);if(a)i[e]=a,We(i[e]),on(i[e],r);else{let l=this.createTextTrack("captions",n,o);l&&(l[e]=!0,i[e]=l)}}createNonNativeTrack(e){if(this.nonNativeCaptionsTracks[e])return;let t=this.captionsProperties[e];if(!t)return;let i=t.label,r={_id:e,label:i,kind:"captions",default:t.media?!!t.media.default:!1,closedCaptions:t.media};this.nonNativeCaptionsTracks[e]=r,this.hls.trigger(p.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:[r]})}createTextTrack(e,t,i){let r=this.media;if(r)return r.addTextTrack(e,t,i)}onMediaAttaching(e,t){this.media=t.media,this._cleanTracks()}onMediaDetaching(){let{captionsTracks:e}=this;Object.keys(e).forEach(t=>{We(e[t]),delete e[t]}),this.nonNativeCaptionsTracks={}}onManifestLoading(){this.lastCc=-1,this.lastSn=-1,this.lastPartIndex=-1,this.prevCC=-1,this.vttCCs=Br(),this._cleanTracks(),this.tracks=[],this.captionsTracks={},this.nonNativeCaptionsTracks={},this.textTracks=[],this.unparsedVttFrags=[],this.initPTS=[],this.cea608Parser1&&this.cea608Parser2&&(this.cea608Parser1.reset(),this.cea608Parser2.reset())}_cleanTracks(){let{media:e}=this;if(!e)return;let t=e.textTracks;if(t)for(let i=0;i<t.length;i++)We(t[i])}onSubtitleTracksUpdated(e,t){let i=t.subtitleTracks||[],r=i.some(n=>n.textCodec===Ei);if(this.config.enableWebVTT||r&&this.config.enableIMSC1){if(Cn(this.tracks,i)){this.tracks=i;return}if(this.textTracks=[],this.tracks=i,this.config.renderTextTracksNatively){let o=this.media,a=o?bt(o.textTracks):null;if(this.tracks.forEach((l,c)=>{let h;if(a){let u=null;for(let d=0;d<a.length;d++)if(a[d]&&Nr(a[d],l)){u=a[d],a[d]=null;break}u&&(h=u)}if(h)We(h);else{let u=Bn(l);h=this.createTextTrack(u,l.name,l.lang),h&&(h.mode="disabled")}h&&this.textTracks.push(h)}),a!=null&&a.length){let l=a.filter(c=>c!==null).map(c=>c.label);l.length&&S.warn(`Media element contains unused subtitle tracks: ${l.join(", ")}. Replace media element for each source to clear TextTracks and captions menu.`)}}else if(this.tracks.length){let o=this.tracks.map(a=>({label:a.name,kind:a.type.toLowerCase(),default:a.default,subtitleTrack:a}));this.hls.trigger(p.NON_NATIVE_TEXT_TRACKS_FOUND,{tracks:o})}}}onManifestLoaded(e,t){this.config.enableCEA708Captions&&t.captions&&t.captions.forEach(i=>{let r=/(?:CC|SERVICE)([1-4])/.exec(i.instreamId);if(!r)return;let n=`textTrack${r[1]}`,o=this.captionsProperties[n];o&&(o.label=i.name,i.lang&&(o.languageCode=i.lang),o.media=i)})}closedCaptionsForLevel(e){let t=this.hls.levels[e.level];return t==null?void 0:t.attrs["CLOSED-CAPTIONS"]}onFragLoading(e,t){this.initCea608Parsers();let{cea608Parser1:i,cea608Parser2:r,lastCc:n,lastSn:o,lastPartIndex:a}=this;if(!(!this.enabled||!i||!r)&&t.frag.type===$.MAIN){var l,c;let{cc:h,sn:u}=t.frag,d=(l=t==null||(c=t.part)==null?void 0:c.index)!=null?l:-1;u===o+1||u===o&&d===a+1||h===n||(i.reset(),r.reset()),this.lastCc=h,this.lastSn=u,this.lastPartIndex=d}}onFragLoaded(e,t){let{frag:i,payload:r}=t;if(i.type===$.SUBTITLE)if(r.byteLength){let n=i.decryptdata,o="stats"in t;if(n==null||!n.encrypted||o){let a=this.tracks[i.level],l=this.vttCCs;l[i.cc]||(l[i.cc]={start:i.start,prevCC:this.prevCC,new:!0},this.prevCC=i.cc),a&&a.textCodec===Ei?this._parseIMSC1(i,r):this._parseVTTs(t)}}else this.hls.trigger(p.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:i,error:new Error("Empty subtitle payload")})}_parseIMSC1(e,t){let i=this.hls;Or(t,this.initPTS[e.cc],r=>{this._appendCues(r,e.level),i.trigger(p.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:e})},r=>{S.log(`Failed to parse IMSC1: ${r}`),i.trigger(p.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:e,error:r})})}_parseVTTs(e){var t;let{frag:i,payload:r}=e,{initPTS:n,unparsedVttFrags:o}=this,a=n.length-1;if(!n[i.cc]&&a===-1){o.push(e);return}let l=this.hls,c=(t=i.initSegment)!=null&&t.data?Te(i.initSegment.data,new Uint8Array(r)):r;yl(c,this.initPTS[i.cc],this.vttCCs,i.cc,i.start,h=>{this._appendCues(h,i.level),l.trigger(p.SUBTITLE_FRAG_PROCESSED,{success:!0,frag:i})},h=>{let u=h.message==="Missing initPTS for VTT MPEGTS";u?o.push(e):this._fallbackToIMSC1(i,r),S.log(`Failed to parse VTT cue: ${h}`),!(u&&a>i.cc)&&l.trigger(p.SUBTITLE_FRAG_PROCESSED,{success:!1,frag:i,error:h})})}_fallbackToIMSC1(e,t){let i=this.tracks[e.level];i.textCodec||Or(t,this.initPTS[e.cc],()=>{i.textCodec=Ei,this._parseIMSC1(e,t)},()=>{i.textCodec="wvtt"})}_appendCues(e,t){let i=this.hls;if(this.config.renderTextTracksNatively){let r=this.textTracks[t];if(!r||r.mode==="disabled")return;e.forEach(n=>an(r,n))}else{let r=this.tracks[t];if(!r)return;let n=r.default?"default":"subtitles"+t;i.trigger(p.CUES_PARSED,{type:"subtitles",cues:e,track:n})}}onFragDecrypted(e,t){let{frag:i}=t;i.type===$.SUBTITLE&&this.onFragLoaded(p.FRAG_LOADED,t)}onSubtitleTracksCleared(){this.tracks=[],this.captionsTracks={}}onFragParsingUserdata(e,t){this.initCea608Parsers();let{cea608Parser1:i,cea608Parser2:r}=this;if(!this.enabled||!i||!r)return;let{frag:n,samples:o}=t;if(!(n.type===$.MAIN&&this.closedCaptionsForLevel(n)==="NONE"))for(let a=0;a<o.length;a++){let l=o[a].bytes;if(l){let c=this.extractCea608Data(l);i.addData(o[a].pts,c[0]),r.addData(o[a].pts,c[1])}}}onBufferFlushing(e,{startOffset:t,endOffset:i,endOffsetSubtitles:r,type:n}){let{media:o}=this;if(!(!o||o.currentTime<i)){if(!n||n==="video"){let{captionsTracks:a}=this;Object.keys(a).forEach(l=>Ci(a[l],t,i))}if(this.config.renderTextTracksNatively&&t===0&&r!==void 0){let{textTracks:a}=this;Object.keys(a).forEach(l=>Ci(a[l],t,r))}}}extractCea608Data(e){let t=[[],[]],i=e[0]&31,r=2;for(let n=0;n<i;n++){let o=e[r++],a=127&e[r++],l=127&e[r++];if(a===0&&l===0)continue;if((4&o)!==0){let h=3&o;(h===0||h===1)&&(t[h].push(a),t[h].push(l))}}return t}};function Bn(s){return s.characteristics&&/transcribes-spoken-dialog/gi.test(s.characteristics)&&/describes-music-and-sound/gi.test(s.characteristics)?"captions":"subtitles"}function Nr(s,e){return!!s&&s.kind===Bn(e)&&rs(e,s)}function bl(s,e,t,i){return Math.min(e,i)-Math.max(s,t)}function Br(){return{ccOffset:0,presentationOffset:0,0:{start:0,prevCC:-1,new:!0}}}var vs=class s{constructor(e){this.hls=void 0,this.autoLevelCapping=void 0,this.firstLevel=void 0,this.media=void 0,this.restrictedLevels=void 0,this.timer=void 0,this.clientRect=void 0,this.streamController=void 0,this.hls=e,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.firstLevel=-1,this.media=null,this.restrictedLevels=[],this.timer=void 0,this.clientRect=null,this.registerListeners()}setStreamController(e){this.streamController=e}destroy(){this.hls&&this.unregisterListener(),this.timer&&this.stopCapping(),this.media=null,this.clientRect=null,this.hls=this.streamController=null}registerListeners(){let{hls:e}=this;e.on(p.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),e.on(p.MEDIA_ATTACHING,this.onMediaAttaching,this),e.on(p.MANIFEST_PARSED,this.onManifestParsed,this),e.on(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(p.BUFFER_CODECS,this.onBufferCodecs,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this)}unregisterListener(){let{hls:e}=this;e.off(p.FPS_DROP_LEVEL_CAPPING,this.onFpsDropLevelCapping,this),e.off(p.MEDIA_ATTACHING,this.onMediaAttaching,this),e.off(p.MANIFEST_PARSED,this.onManifestParsed,this),e.off(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(p.BUFFER_CODECS,this.onBufferCodecs,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this)}onFpsDropLevelCapping(e,t){let i=this.hls.levels[t.droppedLevel];this.isLevelAllowed(i)&&this.restrictedLevels.push({bitrate:i.bitrate,height:i.height,width:i.width})}onMediaAttaching(e,t){this.media=t.media instanceof HTMLVideoElement?t.media:null,this.clientRect=null,this.timer&&this.hls.levels.length&&this.detectPlayerSize()}onManifestParsed(e,t){let i=this.hls;this.restrictedLevels=[],this.firstLevel=t.firstLevel,i.config.capLevelToPlayerSize&&t.video&&this.startCapping()}onLevelsUpdated(e,t){this.timer&&M(this.autoLevelCapping)&&this.detectPlayerSize()}onBufferCodecs(e,t){this.hls.config.capLevelToPlayerSize&&t.video&&this.startCapping()}onMediaDetaching(){this.stopCapping()}detectPlayerSize(){if(this.media){if(this.mediaHeight<=0||this.mediaWidth<=0){this.clientRect=null;return}let e=this.hls.levels;if(e.length){let t=this.hls,i=this.getMaxLevel(e.length-1);i!==this.autoLevelCapping&&S.log(`Setting autoLevelCapping to ${i}: ${e[i].height}p@${e[i].bitrate} for media ${this.mediaWidth}x${this.mediaHeight}`),t.autoLevelCapping=i,t.autoLevelCapping>this.autoLevelCapping&&this.streamController&&this.streamController.nextLevelSwitch(),this.autoLevelCapping=t.autoLevelCapping}}}getMaxLevel(e){let t=this.hls.levels;if(!t.length)return-1;let i=t.filter((r,n)=>this.isLevelAllowed(r)&&n<=e);return this.clientRect=null,s.getMaxLevelByMediaSize(i,this.mediaWidth,this.mediaHeight)}startCapping(){this.timer||(this.autoLevelCapping=Number.POSITIVE_INFINITY,self.clearInterval(this.timer),this.timer=self.setInterval(this.detectPlayerSize.bind(this),1e3),this.detectPlayerSize())}stopCapping(){this.restrictedLevels=[],this.firstLevel=-1,this.autoLevelCapping=Number.POSITIVE_INFINITY,this.timer&&(self.clearInterval(this.timer),this.timer=void 0)}getDimensions(){if(this.clientRect)return this.clientRect;let e=this.media,t={width:0,height:0};if(e){let i=e.getBoundingClientRect();t.width=i.width,t.height=i.height,!t.width&&!t.height&&(t.width=i.right-i.left||e.width||0,t.height=i.bottom-i.top||e.height||0)}return this.clientRect=t,t}get mediaWidth(){return this.getDimensions().width*this.contentScaleFactor}get mediaHeight(){return this.getDimensions().height*this.contentScaleFactor}get contentScaleFactor(){let e=1;if(!this.hls.config.ignoreDevicePixelRatio)try{e=self.devicePixelRatio}catch(t){}return e}isLevelAllowed(e){return!this.restrictedLevels.some(i=>e.bitrate===i.bitrate&&e.width===i.width&&e.height===i.height)}static getMaxLevelByMediaSize(e,t,i){if(!(e!=null&&e.length))return-1;let r=(a,l)=>l?a.width!==l.width||a.height!==l.height:!0,n=e.length-1,o=Math.max(t,i);for(let a=0;a<e.length;a+=1){let l=e[a];if((l.width>=o||l.height>=o)&&r(l,e[a+1])){n=a;break}}return n}},xs=class{constructor(e){this.hls=void 0,this.isVideoPlaybackQualityAvailable=!1,this.timer=void 0,this.media=null,this.lastTime=void 0,this.lastDroppedFrames=0,this.lastDecodedFrames=0,this.streamController=void 0,this.hls=e,this.registerListeners()}setStreamController(e){this.streamController=e}registerListeners(){this.hls.on(p.MEDIA_ATTACHING,this.onMediaAttaching,this)}unregisterListeners(){this.hls.off(p.MEDIA_ATTACHING,this.onMediaAttaching,this)}destroy(){this.timer&&clearInterval(this.timer),this.unregisterListeners(),this.isVideoPlaybackQualityAvailable=!1,this.media=null}onMediaAttaching(e,t){let i=this.hls.config;if(i.capLevelOnFPSDrop){let r=t.media instanceof self.HTMLVideoElement?t.media:null;this.media=r,r&&typeof r.getVideoPlaybackQuality=="function"&&(this.isVideoPlaybackQualityAvailable=!0),self.clearInterval(this.timer),this.timer=self.setInterval(this.checkFPSInterval.bind(this),i.fpsDroppedMonitoringPeriod)}}checkFPS(e,t,i){let r=performance.now();if(t){if(this.lastTime){let n=r-this.lastTime,o=i-this.lastDroppedFrames,a=t-this.lastDecodedFrames,l=1e3*o/n,c=this.hls;if(c.trigger(p.FPS_DROP,{currentDropped:o,currentDecoded:a,totalDroppedFrames:i}),l>0&&o>c.config.fpsDroppedMonitoringThreshold*a){let h=c.currentLevel;S.warn("drop FPS ratio greater than max allowed value for currentLevel: "+h),h>0&&(c.autoLevelCapping===-1||c.autoLevelCapping>=h)&&(h=h-1,c.trigger(p.FPS_DROP_LEVEL_CAPPING,{level:h,droppedLevel:c.currentLevel}),c.autoLevelCapping=h,this.streamController.nextLevelSwitch())}}this.lastTime=r,this.lastDroppedFrames=i,this.lastDecodedFrames=t}}checkFPSInterval(){let e=this.media;if(e)if(this.isVideoPlaybackQualityAvailable){let t=e.getVideoPlaybackQuality();this.checkFPS(e,t.totalVideoFrames,t.droppedVideoFrames)}else this.checkFPS(e,e.webkitDecodedFrameCount,e.webkitDroppedFrameCount)}},xt="[eme]",Jt=class s{constructor(e){this.hls=void 0,this.config=void 0,this.media=null,this.keyFormatPromise=null,this.keySystemAccessPromises={},this._requestLicenseFailureCount=0,this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},this.setMediaKeysQueue=s.CDMCleanupPromise?[s.CDMCleanupPromise]:[],this.onMediaEncrypted=this._onMediaEncrypted.bind(this),this.onWaitingForKey=this._onWaitingForKey.bind(this),this.debug=S.debug.bind(S,xt),this.log=S.log.bind(S,xt),this.warn=S.warn.bind(S,xt),this.error=S.error.bind(S,xt),this.hls=e,this.config=e.config,this.registerListeners()}destroy(){this.unregisterListeners(),this.onMediaDetached();let e=this.config;e.requestMediaKeySystemAccessFunc=null,e.licenseXhrSetup=e.licenseResponseCallback=void 0,e.drmSystems=e.drmSystemOptions={},this.hls=this.onMediaEncrypted=this.onWaitingForKey=this.keyIdToKeySessionPromise=null,this.config=null}registerListeners(){this.hls.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.on(p.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.on(p.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.on(p.MANIFEST_LOADED,this.onManifestLoaded,this)}unregisterListeners(){this.hls.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),this.hls.off(p.MEDIA_DETACHED,this.onMediaDetached,this),this.hls.off(p.MANIFEST_LOADING,this.onManifestLoading,this),this.hls.off(p.MANIFEST_LOADED,this.onManifestLoaded,this)}getLicenseServerUrl(e){let{drmSystems:t,widevineLicenseUrl:i}=this.config,r=t[e];if(r)return r.licenseUrl;if(e===ee.WIDEVINE&&i)return i;throw new Error(`no license server URL configured for key-system "${e}"`)}getServerCertificateUrl(e){let{drmSystems:t}=this.config,i=t[e];if(i)return i.serverCertificateUrl;this.log(`No Server Certificate in config.drmSystems["${e}"]`)}attemptKeySystemAccess(e){let t=this.hls.levels,i=(o,a,l)=>!!o&&l.indexOf(o)===a,r=t.map(o=>o.audioCodec).filter(i),n=t.map(o=>o.videoCodec).filter(i);return r.length+n.length===0&&n.push("avc1.42e01e"),new Promise((o,a)=>{let l=c=>{let h=c.shift();this.getMediaKeysPromise(h,r,n).then(u=>o({keySystem:h,mediaKeys:u})).catch(u=>{c.length?l(c):u instanceof ge?a(u):a(new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_NO_ACCESS,error:u,fatal:!0},u.message))})};l(e)})}requestMediaKeySystemAccess(e,t){let{requestMediaKeySystemAccessFunc:i}=this.config;if(typeof i!="function"){let r=`Configured requestMediaKeySystemAccess is not a function ${i}`;return Wr===null&&self.location.protocol==="http:"&&(r=`navigator.requestMediaKeySystemAccess is not available over insecure protocol ${location.protocol}`),Promise.reject(new Error(r))}return i(e,t)}getMediaKeysPromise(e,t,i){let r=yo(e,t,i,this.config.drmSystemOptions),n=this.keySystemAccessPromises[e],o=n==null?void 0:n.keySystemAccess;if(!o){this.log(`Requesting encrypted media "${e}" key-system access with config: ${JSON.stringify(r)}`),o=this.requestMediaKeySystemAccess(e,r);let a=this.keySystemAccessPromises[e]={keySystemAccess:o};return o.catch(l=>{this.log(`Failed to obtain access to key-system "${e}": ${l}`)}),o.then(l=>{this.log(`Access for key-system "${l.keySystem}" obtained`);let c=this.fetchServerCertificate(e);return this.log(`Create media-keys for "${e}"`),a.mediaKeys=l.createMediaKeys().then(h=>(this.log(`Media-keys created for "${e}"`),c.then(u=>u?this.setMediaKeysServerCertificate(h,e,u):h))),a.mediaKeys.catch(h=>{this.error(`Failed to create media-keys for "${e}"}: ${h}`)}),a.mediaKeys})}return o.then(()=>n.mediaKeys)}createMediaKeySessionContext({decryptdata:e,keySystem:t,mediaKeys:i}){this.log(`Creating key-system session "${t}" keyId: ${Ae.hexDump(e.keyId||[])}`);let r=i.createSession(),n={decryptdata:e,keySystem:t,mediaKeys:i,mediaKeysSession:r,keyStatus:"status-pending"};return this.mediaKeySessions.push(n),n}renewKeySession(e){let t=e.decryptdata;if(t.pssh){let i=this.createMediaKeySessionContext(e),r=this.getKeyIdString(t),n="cenc";this.keyIdToKeySessionPromise[r]=this.generateRequestWithPreferredKeySession(i,n,t.pssh,"expired")}else this.warn("Could not renew expired session. Missing pssh initData.");this.removeSession(e)}getKeyIdString(e){if(!e)throw new Error("Could not read keyId of undefined decryptdata");if(e.keyId===null)throw new Error("keyId is null");return Ae.hexDump(e.keyId)}updateKeySession(e,t){var i;let r=e.mediaKeysSession;return this.log(`Updating key-session "${r.sessionId}" for keyID ${Ae.hexDump(((i=e.decryptdata)==null?void 0:i.keyId)||[])}
      } (data length: ${t&&t.byteLength})`),r.update(t)}selectKeySystemFormat(e){let t=Object.keys(e.levelkeys||{});return this.keyFormatPromise||(this.log(`Selecting key-system from fragment (sn: ${e.sn} ${e.type}: ${e.level}) key formats ${t.join(", ")}`),this.keyFormatPromise=this.getKeyFormatPromise(t)),this.keyFormatPromise}getKeyFormatPromise(e){return new Promise((t,i)=>{let r=ni(this.config),n=e.map(zs).filter(o=>!!o&&r.indexOf(o)!==-1);return this.getKeySystemSelectionPromise(n).then(({keySystem:o})=>{let a=Xs(o);a?t(a):i(new Error(`Unable to find format for key-system "${o}"`))}).catch(i)})}loadKey(e){let t=e.keyInfo.decryptdata,i=this.getKeyIdString(t),r=`(keyId: ${i} format: "${t.keyFormat}" method: ${t.method} uri: ${t.uri})`;this.log(`Starting session for key ${r}`);let n=this.keyIdToKeySessionPromise[i];return n||(n=this.keyIdToKeySessionPromise[i]=this.getKeySystemForKeyPromise(t).then(({keySystem:o,mediaKeys:a})=>(this.throwIfDestroyed(),this.log(`Handle encrypted media sn: ${e.frag.sn} ${e.frag.type}: ${e.frag.level} using key ${r}`),this.attemptSetMediaKeys(o,a).then(()=>{this.throwIfDestroyed();let l=this.createMediaKeySessionContext({keySystem:o,mediaKeys:a,decryptdata:t});return this.generateRequestWithPreferredKeySession(l,"cenc",t.pssh,"playlist-key")}))),n.catch(o=>this.handleError(o))),n}throwIfDestroyed(e="Invalid state"){if(!this.hls)throw new Error("invalid state")}handleError(e){this.hls&&(this.error(e.message),e instanceof ge?this.hls.trigger(p.ERROR,e.data):this.hls.trigger(p.ERROR,{type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_NO_KEYS,error:e,fatal:!0}))}getKeySystemForKeyPromise(e){let t=this.getKeyIdString(e),i=this.keyIdToKeySessionPromise[t];if(!i){let r=zs(e.keyFormat),n=r?[r]:ni(this.config);return this.attemptKeySystemAccess(n)}return i}getKeySystemSelectionPromise(e){if(e.length||(e=ni(this.config)),e.length===0)throw new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_NO_CONFIGURED_LICENSE,fatal:!0},`Missing key-system license configuration options ${JSON.stringify({drmSystems:this.config.drmSystems})}`);return this.attemptKeySystemAccess(e)}_onMediaEncrypted(e){let{initDataType:t,initData:i}=e;if(this.debug(`"${e.type}" event: init data type: "${t}"`),i===null)return;let r,n;if(t==="sinf"&&this.config.drmSystems[ee.FAIRPLAY]){let h=re(new Uint8Array(i));try{let u=Os(JSON.parse(h).sinf),d=en(new Uint8Array(u));if(!d)return;r=d.subarray(8,24),n=ee.FAIRPLAY}catch(u){this.warn('Failed to parse sinf "encrypted" event message initData');return}}else{let h=Ho(i);if(h===null)return;h.version===0&&h.systemId===Yr.WIDEVINE&&h.data&&(r=h.data.subarray(8,24)),n=mo(h.systemId)}if(!n||!r)return;let o=Ae.hexDump(r),{keyIdToKeySessionPromise:a,mediaKeySessions:l}=this,c=a[o];for(let h=0;h<l.length;h++){let u=l[h],d=u.decryptdata;if(d.pssh||!d.keyId)continue;let f=Ae.hexDump(d.keyId);if(o===f||d.uri.replace(/-/g,"").indexOf(o)!==-1){c=a[f],delete a[f],d.pssh=new Uint8Array(i),d.keyId=r,c=a[o]=c.then(()=>this.generateRequestWithPreferredKeySession(u,t,i,"encrypted-event-key-match"));break}}c||(c=a[o]=this.getKeySystemSelectionPromise([n]).then(({keySystem:h,mediaKeys:u})=>{var d;this.throwIfDestroyed();let f=new ot("ISO-23001-7",o,(d=Xs(h))!=null?d:"");return f.pssh=new Uint8Array(i),f.keyId=r,this.attemptSetMediaKeys(h,u).then(()=>{this.throwIfDestroyed();let g=this.createMediaKeySessionContext({decryptdata:f,keySystem:h,mediaKeys:u});return this.generateRequestWithPreferredKeySession(g,t,i,"encrypted-event-no-match")})})),c.catch(h=>this.handleError(h))}_onWaitingForKey(e){this.log(`"${e.type}" event`)}attemptSetMediaKeys(e,t){let i=this.setMediaKeysQueue.slice();this.log(`Setting media-keys for "${e}"`);let r=Promise.all(i).then(()=>{if(!this.media)throw new Error("Attempted to set mediaKeys without media element attached");return this.media.setMediaKeys(t)});return this.setMediaKeysQueue.push(r),r.then(()=>{this.log(`Media-keys set for "${e}"`),i.push(r),this.setMediaKeysQueue=this.setMediaKeysQueue.filter(n=>i.indexOf(n)===-1)})}generateRequestWithPreferredKeySession(e,t,i,r){var n,o;let a=(n=this.config.drmSystems)==null||(o=n[e.keySystem])==null?void 0:o.generateRequest;if(a)try{let g=a.call(this.hls,t,i,e);if(!g)throw new Error("Invalid response from configured generateRequest filter");t=g.initDataType,i=e.decryptdata.pssh=g.initData?new Uint8Array(g.initData):null}catch(g){var l;if(this.warn(g.message),(l=this.hls)!=null&&l.config.debug)throw g}if(i===null)return this.log(`Skipping key-session request for "${r}" (no initData)`),Promise.resolve(e);let c=this.getKeyIdString(e.decryptdata);this.log(`Generating key-session request for "${r}": ${c} (init data type: ${t} length: ${i?i.byteLength:null})`);let h=new Gs,u=e._onmessage=g=>{let m=e.mediaKeysSession;if(!m){h.emit("error",new Error("invalid state"));return}let{messageType:y,message:T}=g;this.log(`"${y}" message event for session "${m.sessionId}" message size: ${T.byteLength}`),y==="license-request"||y==="license-renewal"?this.renewLicense(e,T).catch(v=>{this.handleError(v),h.emit("error",v)}):y==="license-release"?e.keySystem===ee.FAIRPLAY&&(this.updateKeySession(e,Ri("acknowledged")),this.removeSession(e)):this.warn(`unhandled media key message type "${y}"`)},d=e._onkeystatuseschange=g=>{if(!e.mediaKeysSession){h.emit("error",new Error("invalid state"));return}this.onKeyStatusChange(e);let y=e.keyStatus;h.emit("keyStatus",y),y==="expired"&&(this.warn(`${e.keySystem} expired for key ${c}`),this.renewKeySession(e))};e.mediaKeysSession.addEventListener("message",u),e.mediaKeysSession.addEventListener("keystatuseschange",d);let f=new Promise((g,m)=>{h.on("error",m),h.on("keyStatus",y=>{y.startsWith("usable")?g():y==="output-restricted"?m(new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_STATUS_OUTPUT_RESTRICTED,fatal:!1},"HDCP level output restricted")):y==="internal-error"?m(new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_STATUS_INTERNAL_ERROR,fatal:!0},`key status changed to "${y}"`)):y==="expired"?m(new Error("key expired while generating request")):this.warn(`unhandled key status change "${y}"`)})});return e.mediaKeysSession.generateRequest(t,i).then(()=>{var g;this.log(`Request generated for key-session "${(g=e.mediaKeysSession)==null?void 0:g.sessionId}" keyId: ${c}`)}).catch(g=>{throw new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_NO_SESSION,error:g,fatal:!1},`Error generating key-session request: ${g}`)}).then(()=>f).catch(g=>{throw h.removeAllListeners(),this.removeSession(e),g}).then(()=>(h.removeAllListeners(),e))}onKeyStatusChange(e){e.mediaKeysSession.keyStatuses.forEach((t,i)=>{this.log(`key status change "${t}" for keyStatuses keyId: ${Ae.hexDump("buffer"in i?new Uint8Array(i.buffer,i.byteOffset,i.byteLength):new Uint8Array(i))} session keyId: ${Ae.hexDump(new Uint8Array(e.decryptdata.keyId||[]))} uri: ${e.decryptdata.uri}`),e.keyStatus=t})}fetchServerCertificate(e){let t=this.config,i=t.loader,r=new i(t),n=this.getServerCertificateUrl(e);return n?(this.log(`Fetching server certificate for "${e}"`),new Promise((o,a)=>{let l={responseType:"arraybuffer",url:n},c=t.certLoadPolicy.default,h={loadPolicy:c,timeout:c.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0},u={onSuccess:(d,f,g,m)=>{o(d.data)},onError:(d,f,g,m)=>{a(new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:g,response:he({url:l.url,data:void 0},d)},`"${e}" certificate request failed (${n}). Status: ${d.code} (${d.text})`))},onTimeout:(d,f,g)=>{a(new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_SERVER_CERTIFICATE_REQUEST_FAILED,fatal:!0,networkDetails:g,response:{url:l.url,data:void 0}},`"${e}" certificate request timed out (${n})`))},onAbort:(d,f,g)=>{a(new Error("aborted"))}};r.load(l,h,u)})):Promise.resolve()}setMediaKeysServerCertificate(e,t,i){return new Promise((r,n)=>{e.setServerCertificate(i).then(o=>{this.log(`setServerCertificate ${o?"success":"not supported by CDM"} (${i==null?void 0:i.byteLength}) on "${t}"`),r(e)}).catch(o=>{n(new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_SERVER_CERTIFICATE_UPDATE_FAILED,error:o,fatal:!0},o.message))})})}renewLicense(e,t){return this.requestLicense(e,new Uint8Array(t)).then(i=>this.updateKeySession(e,new Uint8Array(i)).catch(r=>{throw new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_SESSION_UPDATE_FAILED,error:r,fatal:!0},r.message)}))}unpackPlayReadyKeyMessage(e,t){let i=String.fromCharCode.apply(null,new Uint16Array(t.buffer));if(!i.includes("PlayReadyKeyMessage"))return e.setRequestHeader("Content-Type","text/xml; charset=utf-8"),t;let r=new DOMParser().parseFromString(i,"application/xml"),n=r.querySelectorAll("HttpHeader");if(n.length>0){let h;for(let u=0,d=n.length;u<d;u++){var o,a;h=n[u];let f=(o=h.querySelector("name"))==null?void 0:o.textContent,g=(a=h.querySelector("value"))==null?void 0:a.textContent;f&&g&&e.setRequestHeader(f,g)}}let l=r.querySelector("Challenge"),c=l==null?void 0:l.textContent;if(!c)throw new Error("Cannot find <Challenge> in key message");return Ri(atob(c))}setupLicenseXHR(e,t,i,r){let n=this.config.licenseXhrSetup;return n?Promise.resolve().then(()=>{if(!i.decryptdata)throw new Error("Key removed");return n.call(this.hls,e,t,i,r)}).catch(o=>{if(!i.decryptdata)throw o;return e.open("POST",t,!0),n.call(this.hls,e,t,i,r)}).then(o=>(e.readyState||e.open("POST",t,!0),{xhr:e,licenseChallenge:o||r})):(e.open("POST",t,!0),Promise.resolve({xhr:e,licenseChallenge:r}))}requestLicense(e,t){let i=this.config.keyLoadPolicy.default;return new Promise((r,n)=>{let o=this.getLicenseServerUrl(e.keySystem);this.log(`Sending license request to URL: ${o}`);let a=new XMLHttpRequest;a.responseType="arraybuffer",a.onreadystatechange=()=>{if(!this.hls||!e.mediaKeysSession)return n(new Error("invalid state"));if(a.readyState===4)if(a.status===200){this._requestLicenseFailureCount=0;let l=a.response;this.log(`License received ${l instanceof ArrayBuffer?l.byteLength:l}`);let c=this.config.licenseResponseCallback;if(c)try{l=c.call(this.hls,a,o,e)}catch(h){this.error(h)}r(l)}else{let l=i.errorRetry,c=l?l.maxNumRetry:0;if(this._requestLicenseFailureCount++,this._requestLicenseFailureCount>c||a.status>=400&&a.status<500)n(new ge({type:G.KEY_SYSTEM_ERROR,details:b.KEY_SYSTEM_LICENSE_REQUEST_FAILED,fatal:!0,networkDetails:a,response:{url:o,data:void 0,code:a.status,text:a.statusText}},`License Request XHR failed (${o}). Status: ${a.status} (${a.statusText})`));else{let h=c-this._requestLicenseFailureCount+1;this.warn(`Retrying license request, ${h} attempts left`),this.requestLicense(e,t).then(r,n)}}},e.licenseXhr&&e.licenseXhr.readyState!==XMLHttpRequest.DONE&&e.licenseXhr.abort(),e.licenseXhr=a,this.setupLicenseXHR(a,o,e,t).then(({xhr:l,licenseChallenge:c})=>{e.keySystem==ee.PLAYREADY&&(c=this.unpackPlayReadyKeyMessage(l,c)),l.send(c)})})}onMediaAttached(e,t){if(!this.config.emeEnabled)return;let i=t.media;this.media=i,i.addEventListener("encrypted",this.onMediaEncrypted),i.addEventListener("waitingforkey",this.onWaitingForKey)}onMediaDetached(){let e=this.media,t=this.mediaKeySessions;e&&(e.removeEventListener("encrypted",this.onMediaEncrypted),e.removeEventListener("waitingforkey",this.onWaitingForKey),this.media=null),this._requestLicenseFailureCount=0,this.setMediaKeysQueue=[],this.mediaKeySessions=[],this.keyIdToKeySessionPromise={},ot.clearKeyUriToKeyIdMap();let i=t.length;s.CDMCleanupPromise=Promise.all(t.map(r=>this.removeSession(r)).concat(e==null?void 0:e.setMediaKeys(null).catch(r=>{this.log(`Could not clear media keys: ${r}`)}))).then(()=>{i&&(this.log("finished closing key sessions and clearing media keys"),t.length=0)}).catch(r=>{this.log(`Could not close sessions and clear media keys: ${r}`)})}onManifestLoading(){this.keyFormatPromise=null}onManifestLoaded(e,{sessionKeys:t}){if(!(!t||!this.config.emeEnabled)&&!this.keyFormatPromise){let i=t.reduce((r,n)=>(r.indexOf(n.keyFormat)===-1&&r.push(n.keyFormat),r),[]);this.log(`Selecting key-system from session-keys ${i.join(", ")}`),this.keyFormatPromise=this.getKeyFormatPromise(i)}}removeSession(e){let{mediaKeysSession:t,licenseXhr:i}=e;if(t){this.log(`Remove licenses and keys and close session ${t.sessionId}`),e._onmessage&&(t.removeEventListener("message",e._onmessage),e._onmessage=void 0),e._onkeystatuseschange&&(t.removeEventListener("keystatuseschange",e._onkeystatuseschange),e._onkeystatuseschange=void 0),i&&i.readyState!==XMLHttpRequest.DONE&&i.abort(),e.mediaKeysSession=e.decryptdata=e.licenseXhr=void 0;let r=this.mediaKeySessions.indexOf(e);return r>-1&&this.mediaKeySessions.splice(r,1),t.remove().catch(n=>{this.log(`Could not remove session: ${n}`)}).then(()=>t.close()).catch(n=>{this.log(`Could not close session: ${n}`)})}}};Jt.CDMCleanupPromise=void 0;var ge=class extends Error{constructor(e,t){super(t),this.data=void 0,e.error||(e.error=new Error(t)),this.data=e,e.err=e.error}},de;(function(s){s.MANIFEST="m",s.AUDIO="a",s.VIDEO="v",s.MUXED="av",s.INIT="i",s.CAPTION="c",s.TIMED_TEXT="tt",s.KEY="k",s.OTHER="o"})(de||(de={}));var Ss;(function(s){s.DASH="d",s.HLS="h",s.SMOOTH="s",s.OTHER="o"})(Ss||(Ss={}));var Be;(function(s){s.OBJECT="CMCD-Object",s.REQUEST="CMCD-Request",s.SESSION="CMCD-Session",s.STATUS="CMCD-Status"})(Be||(Be={}));var Al={[Be.OBJECT]:["br","d","ot","tb"],[Be.REQUEST]:["bl","dl","mtp","nor","nrr","su"],[Be.SESSION]:["cid","pr","sf","sid","st","v"],[Be.STATUS]:["bs","rtp"]},ft=class s{constructor(e,t){this.value=void 0,this.params=void 0,Array.isArray(e)&&(e=e.map(i=>i instanceof s?i:new s(i))),this.value=e,this.params=t}},Zt=class{constructor(e){this.description=void 0,this.description=e}},Ll="Dict";function _l(s){return Array.isArray(s)?JSON.stringify(s):s instanceof Map?"Map{}":s instanceof Set?"Set{}":typeof s=="object"?JSON.stringify(s):String(s)}function Rl(s,e,t,i){return new Error(`failed to ${s} "${_l(e)}" as ${t}`,{cause:i})}var Ur="Bare Item",Il="Boolean",wl="Byte Sequence",Cl="Decimal",Dl="Integer";function kl(s){return s<-999999999999999||999999999999999<s}var Pl=/[\x00-\x1f\x7f]+/,Ol="Token",Ml="Key";function Ie(s,e,t){return Rl("serialize",s,e,t)}function Fl(s){if(typeof s!="boolean")throw Ie(s,Il);return s?"?1":"?0"}function Nl(s){return btoa(String.fromCharCode(...s))}function Bl(s){if(ArrayBuffer.isView(s)===!1)throw Ie(s,wl);return`:${Nl(s)}:`}function Un(s){if(kl(s))throw Ie(s,Dl);return s.toString()}function Ul(s){return`@${Un(s.getTime()/1e3)}`}function $n(s,e){if(s<0)return-$n(-s,e);let t=Math.pow(10,e);if(Math.abs(s*t%1-.5)<Number.EPSILON){let r=Math.floor(s*t);return(r%2===0?r:r+1)/t}else return Math.round(s*t)/t}function $l(s){let e=$n(s,3);if(Math.floor(Math.abs(e)).toString().length>12)throw Ie(s,Cl);let t=e.toString();return t.includes(".")?t:`${t}.0`}var Gl="String";function Hl(s){if(Pl.test(s))throw Ie(s,Gl);return`"${s.replace(/\\/g,"\\\\").replace(/"/g,'\\"')}"`}function Kl(s){return s.description||s.toString().slice(7,-1)}function $r(s){let e=Kl(s);if(/^([a-zA-Z*])([!#$%&'*+\-.^_`|~\w:/]*)$/.test(e)===!1)throw Ie(e,Ol);return e}function bs(s){switch(typeof s){case"number":if(!M(s))throw Ie(s,Ur);return Number.isInteger(s)?Un(s):$l(s);case"string":return Hl(s);case"symbol":return $r(s);case"boolean":return Fl(s);case"object":if(s instanceof Date)return Ul(s);if(s instanceof Uint8Array)return Bl(s);if(s instanceof Zt)return $r(s);default:throw Ie(s,Ur)}}function As(s){if(/^[a-z*][a-z0-9\-_.*]*$/.test(s)===!1)throw Ie(s,Ml);return s}function Vs(s){return s==null?"":Object.entries(s).map(([e,t])=>t===!0?`;${As(e)}`:`;${As(e)}=${bs(t)}`).join("")}function Gn(s){return s instanceof ft?`${bs(s.value)}${Vs(s.params)}`:bs(s)}function Vl(s){return`(${s.value.map(Gn).join(" ")})${Vs(s.params)}`}function Yl(s,e={whitespace:!0}){if(typeof s!="object")throw Ie(s,Ll);let t=s instanceof Map?s.entries():Object.entries(s),i=e!=null&&e.whitespace?" ":"";return Array.from(t).map(([r,n])=>{n instanceof ft||(n=new ft(n));let o=As(r);return n.value===!0?o+=Vs(n.params):(o+="=",Array.isArray(n.value)?o+=Vl(n):o+=Gn(n)),o}).join(`,${i}`)}function Wl(s,e){return Yl(s,e)}var ql=s=>s==="ot"||s==="sf"||s==="st",jl=s=>typeof s=="number"?M(s):s!=null&&s!==""&&s!==!1;function zl(s,e){let t=new URL(s),i=new URL(e);if(t.origin!==i.origin)return s;let r=t.pathname.split("/").slice(1),n=i.pathname.split("/").slice(1,-1);for(;r[0]===n[0];)r.shift(),n.shift();for(;n.length;)n.shift(),r.unshift("..");return r.join("/")}function Xl(){try{return crypto.randomUUID()}catch(s){try{let e=URL.createObjectURL(new Blob),t=e.toString();return URL.revokeObjectURL(e),t.slice(t.lastIndexOf("/")+1)}catch(e){let t=new Date().getTime();return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,r=>{let n=(t+Math.random()*16)%16|0;return t=Math.floor(t/16),(r=="x"?n:n&3|8).toString(16)})}}}var It=s=>Math.round(s),Ql=(s,e)=>(e!=null&&e.baseUrl&&(s=zl(s,e.baseUrl)),encodeURIComponent(s)),St=s=>It(s/100)*100,Jl={br:It,d:It,bl:St,dl:St,mtp:St,nor:Ql,rtp:St,tb:It};function Zl(s,e){let t={};if(s==null||typeof s!="object")return t;let i=Object.keys(s).sort(),r=se({},Jl,e==null?void 0:e.formatters),n=e==null?void 0:e.filter;return i.forEach(o=>{if(n!=null&&n(o))return;let a=s[o],l=r[o];l&&(a=l(a,e)),!(o==="v"&&a===1)&&(o=="pr"&&a===1||jl(a)&&(ql(o)&&typeof a=="string"&&(a=new Zt(a)),t[o]=a))}),t}function Hn(s,e={}){return s?Wl(Zl(s,e),se({whitespace:!1},e)):""}function ec(s,e={}){if(!s)return{};let t=Object.entries(s),i=Object.entries(Al).concat(Object.entries((e==null?void 0:e.customHeaderMap)||{})),r=t.reduce((n,o)=>{var a,l;let[c,h]=o,u=((a=i.find(d=>d[1].includes(c)))==null?void 0:a[0])||Be.REQUEST;return(l=n[u])!=null||(n[u]={}),n[u][c]=h,n},{});return Object.entries(r).reduce((n,[o,a])=>(n[o]=Hn(a,e),n),{})}function tc(s,e,t){return se(s,ec(e,t))}var ic="CMCD";function sc(s,e={}){if(!s)return"";let t=Hn(s,e);return`${ic}=${encodeURIComponent(t)}`}var Gr=/CMCD=[^&#]+/;function rc(s,e,t){let i=sc(e,t);if(!i)return s;if(Gr.test(s))return s.replace(Gr,i);let r=s.includes("?")?"&":"?";return`${s}${r}${i}`}var Ls=class{constructor(e){this.hls=void 0,this.config=void 0,this.media=void 0,this.sid=void 0,this.cid=void 0,this.useHeaders=!1,this.includeKeys=void 0,this.initialized=!1,this.starved=!1,this.buffering=!0,this.audioBuffer=void 0,this.videoBuffer=void 0,this.onWaiting=()=>{this.initialized&&(this.starved=!0),this.buffering=!0},this.onPlaying=()=>{this.initialized||(this.initialized=!0),this.buffering=!1},this.applyPlaylistData=r=>{try{this.apply(r,{ot:de.MANIFEST,su:!this.initialized})}catch(n){S.warn("Could not generate manifest CMCD data.",n)}},this.applyFragmentData=r=>{try{let n=r.frag,o=this.hls.levels[n.level],a=this.getObjectType(n),l={d:n.duration*1e3,ot:a};(a===de.VIDEO||a===de.AUDIO||a==de.MUXED)&&(l.br=o.bitrate/1e3,l.tb=this.getTopBandwidth(a)/1e3,l.bl=this.getBufferLength(a)),this.apply(r,l)}catch(n){S.warn("Could not generate segment CMCD data.",n)}},this.hls=e;let t=this.config=e.config,{cmcd:i}=t;i!=null&&(t.pLoader=this.createPlaylistLoader(),t.fLoader=this.createFragmentLoader(),this.sid=i.sessionId||Xl(),this.cid=i.contentId,this.useHeaders=i.useHeaders===!0,this.includeKeys=i.includeKeys,this.registerListeners())}registerListeners(){let e=this.hls;e.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(p.MEDIA_DETACHED,this.onMediaDetached,this),e.on(p.BUFFER_CREATED,this.onBufferCreated,this)}unregisterListeners(){let e=this.hls;e.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(p.MEDIA_DETACHED,this.onMediaDetached,this),e.off(p.BUFFER_CREATED,this.onBufferCreated,this)}destroy(){this.unregisterListeners(),this.onMediaDetached(),this.hls=this.config=this.audioBuffer=this.videoBuffer=null,this.onWaiting=this.onPlaying=null}onMediaAttached(e,t){this.media=t.media,this.media.addEventListener("waiting",this.onWaiting),this.media.addEventListener("playing",this.onPlaying)}onMediaDetached(){this.media&&(this.media.removeEventListener("waiting",this.onWaiting),this.media.removeEventListener("playing",this.onPlaying),this.media=null)}onBufferCreated(e,t){var i,r;this.audioBuffer=(i=t.tracks.audio)==null?void 0:i.buffer,this.videoBuffer=(r=t.tracks.video)==null?void 0:r.buffer}createData(){var e;return{v:1,sf:Ss.HLS,sid:this.sid,cid:this.cid,pr:(e=this.media)==null?void 0:e.playbackRate,mtp:this.hls.bandwidthEstimate/1e3}}apply(e,t={}){se(t,this.createData());let i=t.ot===de.INIT||t.ot===de.VIDEO||t.ot===de.MUXED;this.starved&&i&&(t.bs=!0,t.su=!0,this.starved=!1),t.su==null&&(t.su=this.buffering);let{includeKeys:r}=this;r&&(t=Object.keys(t).reduce((n,o)=>(r.includes(o)&&(n[o]=t[o]),n),{})),this.useHeaders?(e.headers||(e.headers={}),tc(e.headers,t)):e.url=rc(e.url,t)}getObjectType(e){let{type:t}=e;if(t==="subtitle")return de.TIMED_TEXT;if(e.sn==="initSegment")return de.INIT;if(t==="audio")return de.AUDIO;if(t==="main")return this.hls.audioTracks.length?de.VIDEO:de.MUXED}getTopBandwidth(e){let t=0,i,r=this.hls;if(e===de.AUDIO)i=r.audioTracks;else{let n=r.maxAutoLevel,o=n>-1?n+1:r.levels.length;i=r.levels.slice(0,o)}for(let n of i)n.bitrate>t&&(t=n.bitrate);return t>0?t:NaN}getBufferLength(e){let t=this.hls.media,i=e===de.AUDIO?this.audioBuffer:this.videoBuffer;return!i||!t?NaN:Z.bufferInfo(i,t.currentTime,this.config.maxBufferHole).len*1e3}createPlaylistLoader(){let{pLoader:e}=this.config,t=this.applyPlaylistData,i=e||this.config.loader;return class{constructor(n){this.loader=void 0,this.loader=new i(n)}get stats(){return this.loader.stats}get context(){return this.loader.context}destroy(){this.loader.destroy()}abort(){this.loader.abort()}load(n,o,a){t(n),this.loader.load(n,o,a)}}}createFragmentLoader(){let{fLoader:e}=this.config,t=this.applyFragmentData,i=e||this.config.loader;return class{constructor(n){this.loader=void 0,this.loader=new i(n)}get stats(){return this.loader.stats}get context(){return this.loader.context}destroy(){this.loader.destroy()}abort(){this.loader.abort()}load(n,o,a){t(n),this.loader.load(n,o,a)}}}},nc=3e5,_s=class{constructor(e){this.hls=void 0,this.log=void 0,this.loader=null,this.uri=null,this.pathwayId=".",this.pathwayPriority=null,this.timeToLoad=300,this.reloadTimer=-1,this.updated=0,this.started=!1,this.enabled=!0,this.levels=null,this.audioTracks=null,this.subtitleTracks=null,this.penalizedPathways={},this.hls=e,this.log=S.log.bind(S,"[content-steering]:"),this.registerListeners()}registerListeners(){let e=this.hls;e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(p.MANIFEST_PARSED,this.onManifestParsed,this),e.on(p.ERROR,this.onError,this)}unregisterListeners(){let e=this.hls;e&&(e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(p.MANIFEST_PARSED,this.onManifestParsed,this),e.off(p.ERROR,this.onError,this))}startLoad(){if(this.started=!0,this.clearTimeout(),this.enabled&&this.uri){if(this.updated){let e=this.timeToLoad*1e3-(performance.now()-this.updated);if(e>0){this.scheduleRefresh(this.uri,e);return}}this.loadSteeringManifest(this.uri)}}stopLoad(){this.started=!1,this.loader&&(this.loader.destroy(),this.loader=null),this.clearTimeout()}clearTimeout(){this.reloadTimer!==-1&&(self.clearTimeout(this.reloadTimer),this.reloadTimer=-1)}destroy(){this.unregisterListeners(),this.stopLoad(),this.hls=null,this.levels=this.audioTracks=this.subtitleTracks=null}removeLevel(e){let t=this.levels;t&&(this.levels=t.filter(i=>i!==e))}onManifestLoading(){this.stopLoad(),this.enabled=!0,this.timeToLoad=300,this.updated=0,this.uri=null,this.pathwayId=".",this.levels=this.audioTracks=this.subtitleTracks=null}onManifestLoaded(e,t){let{contentSteering:i}=t;i!==null&&(this.pathwayId=i.pathwayId,this.uri=i.uri,this.started&&this.startLoad())}onManifestParsed(e,t){this.audioTracks=t.audioTracks,this.subtitleTracks=t.subtitleTracks}onError(e,t){let{errorAction:i}=t;if((i==null?void 0:i.action)===le.SendAlternateToPenaltyBox&&i.flags===Ee.MoveAllAlternatesMatchingHost){let r=this.levels,n=this.pathwayPriority,o=this.pathwayId;if(t.context){let{groupId:a,pathwayId:l,type:c}=t.context;a&&r?o=this.getPathwayForGroupId(a,c,o):l&&(o=l)}o in this.penalizedPathways||(this.penalizedPathways[o]=performance.now()),!n&&r&&(n=r.reduce((a,l)=>(a.indexOf(l.pathwayId)===-1&&a.push(l.pathwayId),a),[])),n&&n.length>1&&(this.updatePathwayPriority(n),i.resolved=this.pathwayId!==o),i.resolved||S.warn(`Could not resolve ${t.details} ("${t.error.message}") with content-steering for Pathway: ${o} levels: ${r&&r.length} priorities: ${JSON.stringify(n)} penalized: ${JSON.stringify(this.penalizedPathways)}`)}}filterParsedLevels(e){this.levels=e;let t=this.getLevelsForPathway(this.pathwayId);if(t.length===0){let i=e[0].pathwayId;this.log(`No levels found in Pathway ${this.pathwayId}. Setting initial Pathway to "${i}"`),t=this.getLevelsForPathway(i),this.pathwayId=i}return t.length!==e.length?(this.log(`Found ${t.length}/${e.length} levels in Pathway "${this.pathwayId}"`),t):e}getLevelsForPathway(e){return this.levels===null?[]:this.levels.filter(t=>e===t.pathwayId)}updatePathwayPriority(e){this.pathwayPriority=e;let t,i=this.penalizedPathways,r=performance.now();Object.keys(i).forEach(n=>{r-i[n]>nc&&delete i[n]});for(let n=0;n<e.length;n++){let o=e[n];if(o in i)continue;if(o===this.pathwayId)return;let a=this.hls.nextLoadLevel,l=this.hls.levels[a];if(t=this.getLevelsForPathway(o),t.length>0){this.log(`Setting Pathway to "${o}"`),this.pathwayId=o,un(t),this.hls.trigger(p.LEVELS_UPDATED,{levels:t});let c=this.hls.levels[a];l&&c&&this.levels&&(c.attrs["STABLE-VARIANT-ID"]!==l.attrs["STABLE-VARIANT-ID"]&&c.bitrate!==l.bitrate&&this.log(`Unstable Pathways change from bitrate ${l.bitrate} to ${c.bitrate}`),this.hls.nextLoadLevel=a);break}}}getPathwayForGroupId(e,t,i){let r=this.getLevelsForPathway(i).concat(this.levels||[]);for(let n=0;n<r.length;n++)if(t===j.AUDIO_TRACK&&r[n].hasAudioGroup(e)||t===j.SUBTITLE_TRACK&&r[n].hasSubtitleGroup(e))return r[n].pathwayId;return i}clonePathways(e){let t=this.levels;if(!t)return;let i={},r={};e.forEach(n=>{let{ID:o,"BASE-ID":a,"URI-REPLACEMENT":l}=n;if(t.some(h=>h.pathwayId===o))return;let c=this.getLevelsForPathway(a).map(h=>{let u=new te(h.attrs);u["PATHWAY-ID"]=o;let d=u.AUDIO&&`${u.AUDIO}_clone_${o}`,f=u.SUBTITLES&&`${u.SUBTITLES}_clone_${o}`;d&&(i[u.AUDIO]=d,u.AUDIO=d),f&&(r[u.SUBTITLES]=f,u.SUBTITLES=f);let g=Kn(h.uri,u["STABLE-VARIANT-ID"],"PER-VARIANT-URIS",l),m=new Oe({attrs:u,audioCodec:h.audioCodec,bitrate:h.bitrate,height:h.height,name:h.name,url:g,videoCodec:h.videoCodec,width:h.width});if(h.audioGroups)for(let y=1;y<h.audioGroups.length;y++)m.addGroupId("audio",`${h.audioGroups[y]}_clone_${o}`);if(h.subtitleGroups)for(let y=1;y<h.subtitleGroups.length;y++)m.addGroupId("text",`${h.subtitleGroups[y]}_clone_${o}`);return m});t.push(...c),Hr(this.audioTracks,i,l,o),Hr(this.subtitleTracks,r,l,o)})}loadSteeringManifest(e){let t=this.hls.config,i=t.loader;this.loader&&this.loader.destroy(),this.loader=new i(t);let r;try{r=new self.URL(e)}catch(h){this.enabled=!1,this.log(`Failed to parse Steering Manifest URI: ${e}`);return}if(r.protocol!=="data:"){let h=(this.hls.bandwidthEstimate||t.abrEwmaDefaultEstimate)|0;r.searchParams.set("_HLS_pathway",this.pathwayId),r.searchParams.set("_HLS_throughput",""+h)}let n={responseType:"json",url:r.href},o=t.steeringManifestLoadPolicy.default,a=o.errorRetry||o.timeoutRetry||{},l={loadPolicy:o,timeout:o.maxLoadTimeMs,maxRetry:a.maxNumRetry||0,retryDelay:a.retryDelayMs||0,maxRetryDelay:a.maxRetryDelayMs||0},c={onSuccess:(h,u,d,f)=>{this.log(`Loaded steering manifest: "${r}"`);let g=h.data;if(g.VERSION!==1){this.log(`Steering VERSION ${g.VERSION} not supported!`);return}this.updated=performance.now(),this.timeToLoad=g.TTL;let{"RELOAD-URI":m,"PATHWAY-CLONES":y,"PATHWAY-PRIORITY":T}=g;if(m)try{this.uri=new self.URL(m,r).href}catch(E){this.enabled=!1,this.log(`Failed to parse Steering Manifest RELOAD-URI: ${m}`);return}this.scheduleRefresh(this.uri||d.url),y&&this.clonePathways(y);let v={steeringManifest:g,url:r.toString()};this.hls.trigger(p.STEERING_MANIFEST_LOADED,v),T&&this.updatePathwayPriority(T)},onError:(h,u,d,f)=>{if(this.log(`Error loading steering manifest: ${h.code} ${h.text} (${u.url})`),this.stopLoad(),h.code===410){this.enabled=!1,this.log(`Steering manifest ${u.url} no longer available`);return}let g=this.timeToLoad*1e3;if(h.code===429){let m=this.loader;if(typeof(m==null?void 0:m.getResponseHeader)=="function"){let y=m.getResponseHeader("Retry-After");y&&(g=parseFloat(y)*1e3)}this.log(`Steering manifest ${u.url} rate limited`);return}this.scheduleRefresh(this.uri||u.url,g)},onTimeout:(h,u,d)=>{this.log(`Timeout loading steering manifest (${u.url})`),this.scheduleRefresh(this.uri||u.url)}};this.log(`Requesting steering manifest: ${r}`),this.loader.load(n,l,c)}scheduleRefresh(e,t=this.timeToLoad*1e3){this.clearTimeout(),this.reloadTimer=self.setTimeout(()=>{var i;let r=(i=this.hls)==null?void 0:i.media;if(r&&!r.ended){this.loadSteeringManifest(e);return}this.scheduleRefresh(e,this.timeToLoad*1e3)},t)}};function Hr(s,e,t,i){s&&Object.keys(e).forEach(r=>{let n=s.filter(o=>o.groupId===r).map(o=>{let a=se({},o);return a.details=void 0,a.attrs=new te(a.attrs),a.url=a.attrs.URI=Kn(o.url,o.attrs["STABLE-RENDITION-ID"],"PER-RENDITION-URIS",t),a.groupId=a.attrs["GROUP-ID"]=e[r],a.attrs["PATHWAY-ID"]=i,a});s.push(...n)})}function Kn(s,e,t,i){let{HOST:r,PARAMS:n,[t]:o}=i,a;e&&(a=o==null?void 0:o[e],a&&(s=a));let l=new self.URL(s);return r&&!a&&(l.host=r),n&&Object.keys(n).sort().forEach(c=>{c&&l.searchParams.set(c,n[c])}),l.href}var oc=/^age:\s*[\d.]+\s*$/im,ei=class{constructor(e){this.xhrSetup=void 0,this.requestTimeout=void 0,this.retryTimeout=void 0,this.retryDelay=void 0,this.config=null,this.callbacks=null,this.context=null,this.loader=null,this.stats=void 0,this.xhrSetup=e&&e.xhrSetup||null,this.stats=new Xe,this.retryDelay=0}destroy(){this.callbacks=null,this.abortInternal(),this.loader=null,this.config=null,this.context=null,this.xhrSetup=null,this.stats=null}abortInternal(){let e=this.loader;self.clearTimeout(this.requestTimeout),self.clearTimeout(this.retryTimeout),e&&(e.onreadystatechange=null,e.onprogress=null,e.readyState!==4&&(this.stats.aborted=!0,e.abort()))}abort(){var e;this.abortInternal(),(e=this.callbacks)!=null&&e.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.loader)}load(e,t,i){if(this.stats.loading.start)throw new Error("Loader can only be used once.");this.stats.loading.start=self.performance.now(),this.context=e,this.config=t,this.callbacks=i,this.loadInternal()}loadInternal(){let{config:e,context:t}=this;if(!e||!t)return;let i=this.loader=new self.XMLHttpRequest,r=this.stats;r.loading.first=0,r.loaded=0,r.aborted=!1;let n=this.xhrSetup;n?Promise.resolve().then(()=>{if(!this.stats.aborted)return n(i,t.url)}).catch(o=>(i.open("GET",t.url,!0),n(i,t.url))).then(()=>{this.stats.aborted||this.openAndSendXhr(i,t,e)}).catch(o=>{this.callbacks.onError({code:i.status,text:o.message},t,i,r)}):this.openAndSendXhr(i,t,e)}openAndSendXhr(e,t,i){e.readyState||e.open("GET",t.url,!0);let r=t.headers,{maxTimeToFirstByteMs:n,maxLoadTimeMs:o}=i.loadPolicy;if(r)for(let a in r)e.setRequestHeader(a,r[a]);t.rangeEnd&&e.setRequestHeader("Range","bytes="+t.rangeStart+"-"+(t.rangeEnd-1)),e.onreadystatechange=this.readystatechange.bind(this),e.onprogress=this.loadprogress.bind(this),e.responseType=t.responseType,self.clearTimeout(this.requestTimeout),i.timeout=n&&M(n)?n:o,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),i.timeout),e.send()}readystatechange(){let{context:e,loader:t,stats:i}=this;if(!e||!t)return;let r=t.readyState,n=this.config;if(!i.aborted&&r>=2&&(i.loading.first===0&&(i.loading.first=Math.max(self.performance.now(),i.loading.start),n.timeout!==n.loadPolicy.maxLoadTimeMs&&(self.clearTimeout(this.requestTimeout),n.timeout=n.loadPolicy.maxLoadTimeMs,this.requestTimeout=self.setTimeout(this.loadtimeout.bind(this),n.loadPolicy.maxLoadTimeMs-(i.loading.first-i.loading.start)))),r===4)){self.clearTimeout(this.requestTimeout),t.onreadystatechange=null,t.onprogress=null;let o=t.status,a=t.responseType!=="text";if(o>=200&&o<300&&(a&&t.response||t.responseText!==null)){i.loading.end=Math.max(self.performance.now(),i.loading.first);let l=a?t.response:t.responseText,c=t.responseType==="arraybuffer"?l.byteLength:l.length;if(i.loaded=i.total=c,i.bwEstimate=i.total*8e3/(i.loading.end-i.loading.first),!this.callbacks)return;let h=this.callbacks.onProgress;if(h&&h(i,e,l,t),!this.callbacks)return;let u={url:t.responseURL,data:l,code:o};this.callbacks.onSuccess(u,i,e,t)}else{let l=n.loadPolicy.errorRetry,c=i.retry,h={url:e.url,data:void 0,code:o};Ut(l,c,!1,h)?this.retry(l):(S.error(`${o} while loading ${e.url}`),this.callbacks.onError({code:o,text:t.statusText},e,t,i))}}}loadtimeout(){var e;let t=(e=this.config)==null?void 0:e.loadPolicy.timeoutRetry,i=this.stats.retry;if(Ut(t,i,!0))this.retry(t);else{var r;S.warn(`timeout while loading ${(r=this.context)==null?void 0:r.url}`);let n=this.callbacks;n&&(this.abortInternal(),n.onTimeout(this.stats,this.context,this.loader))}}retry(e){let{context:t,stats:i}=this;this.retryDelay=Ns(e,i.retry),i.retry++,S.warn(`${status?"HTTP Status "+status:"Timeout"} while loading ${t==null?void 0:t.url}, retrying ${i.retry}/${e.maxNumRetry} in ${this.retryDelay}ms`),this.abortInternal(),this.loader=null,self.clearTimeout(this.retryTimeout),this.retryTimeout=self.setTimeout(this.loadInternal.bind(this),this.retryDelay)}loadprogress(e){let t=this.stats;t.loaded=e.loaded,e.lengthComputable&&(t.total=e.total)}getCacheAge(){let e=null;if(this.loader&&oc.test(this.loader.getAllResponseHeaders())){let t=this.loader.getResponseHeader("age");e=t?parseFloat(t):null}return e}getResponseHeader(e){return this.loader&&new RegExp(`^${e}:\\s*[\\d.]+\\s*$`,"im").test(this.loader.getAllResponseHeaders())?this.loader.getResponseHeader(e):null}};function ac(){if(self.fetch&&self.AbortController&&self.ReadableStream&&self.Request)try{return new self.ReadableStream({}),!0}catch(s){}return!1}var lc=/(\d+)-(\d+)\/(\d+)/,ti=class{constructor(e){this.fetchSetup=void 0,this.requestTimeout=void 0,this.request=null,this.response=null,this.controller=void 0,this.context=null,this.config=null,this.callbacks=null,this.stats=void 0,this.loader=null,this.fetchSetup=e.fetchSetup||dc,this.controller=new self.AbortController,this.stats=new Xe}destroy(){this.loader=this.callbacks=this.context=this.config=this.request=null,this.abortInternal(),this.response=null,this.fetchSetup=this.controller=this.stats=null}abortInternal(){this.controller&&!this.stats.loading.end&&(this.stats.aborted=!0,this.controller.abort())}abort(){var e;this.abortInternal(),(e=this.callbacks)!=null&&e.onAbort&&this.callbacks.onAbort(this.stats,this.context,this.response)}load(e,t,i){let r=this.stats;if(r.loading.start)throw new Error("Loader can only be used once.");r.loading.start=self.performance.now();let n=cc(e,this.controller.signal),o=i.onProgress,a=e.responseType==="arraybuffer",l=a?"byteLength":"length",{maxTimeToFirstByteMs:c,maxLoadTimeMs:h}=t.loadPolicy;this.context=e,this.config=t,this.callbacks=i,this.request=this.fetchSetup(e,n),self.clearTimeout(this.requestTimeout),t.timeout=c&&M(c)?c:h,this.requestTimeout=self.setTimeout(()=>{this.abortInternal(),i.onTimeout(r,e,this.response)},t.timeout),self.fetch(this.request).then(u=>{this.response=this.loader=u;let d=Math.max(self.performance.now(),r.loading.start);if(self.clearTimeout(this.requestTimeout),t.timeout=h,this.requestTimeout=self.setTimeout(()=>{this.abortInternal(),i.onTimeout(r,e,this.response)},h-(d-r.loading.start)),!u.ok){let{status:f,statusText:g}=u;throw new Rs(g||"fetch, bad network response",f,u)}return r.loading.first=d,r.total=uc(u.headers)||r.total,o&&M(t.highWaterMark)?this.loadProgressively(u,r,e,t.highWaterMark,o):a?u.arrayBuffer():e.responseType==="json"?u.json():u.text()}).then(u=>{let d=this.response;if(!d)throw new Error("loader destroyed");self.clearTimeout(this.requestTimeout),r.loading.end=Math.max(self.performance.now(),r.loading.first);let f=u[l];f&&(r.loaded=r.total=f);let g={url:d.url,data:u,code:d.status};o&&!M(t.highWaterMark)&&o(r,e,u,d),i.onSuccess(g,r,e,d)}).catch(u=>{if(self.clearTimeout(this.requestTimeout),r.aborted)return;let d=u&&u.code||0,f=u?u.message:null;i.onError({code:d,text:f},e,u?u.details:null,r)})}getCacheAge(){let e=null;if(this.response){let t=this.response.headers.get("age");e=t?parseFloat(t):null}return e}getResponseHeader(e){return this.response?this.response.headers.get(e):null}loadProgressively(e,t,i,r=0,n){let o=new Ht,a=e.body.getReader(),l=()=>a.read().then(c=>{if(c.done)return o.dataLength&&n(t,i,o.flush(),e),Promise.resolve(new ArrayBuffer(0));let h=c.value,u=h.length;return t.loaded+=u,u<r||o.dataLength?(o.push(h),o.dataLength>=r&&n(t,i,o.flush(),e)):n(t,i,h,e),l()}).catch(()=>Promise.reject());return l()}};function cc(s,e){let t={method:"GET",mode:"cors",credentials:"same-origin",signal:e,headers:new self.Headers(se({},s.headers))};return s.rangeEnd&&t.headers.set("Range","bytes="+s.rangeStart+"-"+String(s.rangeEnd-1)),t}function hc(s){let e=lc.exec(s);if(e)return parseInt(e[2])-parseInt(e[1])+1}function uc(s){let e=s.get("Content-Range");if(e){let i=hc(e);if(M(i))return i}let t=s.get("Content-Length");if(t)return parseInt(t)}function dc(s,e){return new self.Request(s.url,e)}var Rs=class extends Error{constructor(e,t,i){super(e),this.code=void 0,this.details=void 0,this.code=t,this.details=i}},fc=/\s/,gc={newCue(s,e,t,i){let r=[],n,o,a,l,c,h=self.VTTCue||self.TextTrackCue;for(let d=0;d<i.rows.length;d++)if(n=i.rows[d],a=!0,l=0,c="",!n.isEmpty()){var u;for(let m=0;m<n.chars.length;m++)fc.test(n.chars[m].uchar)&&a?l++:(c+=n.chars[m].uchar,a=!1);n.cueStartTime=e,e===t&&(t+=1e-4),l>=16?l--:l++;let f=On(c.trim()),g=Ks(e,t,f);s!=null&&(u=s.cues)!=null&&u.getCueById(g)||(o=new h(e,t,f),o.id=g,o.line=d+1,o.align="left",o.position=10+Math.min(80,Math.floor(l*8/32)*10),r.push(o))}return s&&r.length&&(r.sort((d,f)=>d.line==="auto"||f.line==="auto"?0:d.line>8&&f.line>8?f.line-d.line:d.line-f.line),r.forEach(d=>an(s,d))),r}},pc={maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:null,errorRetry:null},Vn=he(he({autoStartLoad:!0,startPosition:-1,defaultAudioCodec:void 0,debug:!1,capLevelOnFPSDrop:!1,capLevelToPlayerSize:!1,ignoreDevicePixelRatio:!1,preferManagedMediaSource:!0,initialLiveManifestSize:1,maxBufferLength:30,backBufferLength:1/0,frontBufferFlushThreshold:1/0,maxBufferSize:60*1e3*1e3,maxBufferHole:.1,highBufferWatchdogPeriod:2,nudgeOffset:.1,nudgeMaxRetry:3,maxFragLookUpTolerance:.25,liveSyncDurationCount:3,liveMaxLatencyDurationCount:1/0,liveSyncDuration:void 0,liveMaxLatencyDuration:void 0,maxLiveSyncPlaybackRate:1,liveDurationInfinity:!1,liveBackBufferLength:null,maxMaxBufferLength:600,enableWorker:!0,workerPath:null,enableSoftwareAES:!0,startLevel:void 0,startFragPrefetch:!1,fpsDroppedMonitoringPeriod:5e3,fpsDroppedMonitoringThreshold:.2,appendErrorMaxRetry:3,loader:ei,fLoader:void 0,pLoader:void 0,xhrSetup:void 0,licenseXhrSetup:void 0,licenseResponseCallback:void 0,abrController:Ui,bufferController:us,capLevelController:vs,errorController:Ni,fpsController:xs,stretchShortVideoTrack:!1,maxAudioFramesDrift:1,forceKeyFrameOnDiscontinuity:!0,abrEwmaFastLive:3,abrEwmaSlowLive:9,abrEwmaFastVoD:3,abrEwmaSlowVoD:9,abrEwmaDefaultEstimate:5e5,abrEwmaDefaultEstimateMax:5e6,abrBandWidthFactor:.95,abrBandWidthUpFactor:.7,abrMaxWithRealBitrate:!1,maxStarvationDelay:4,maxLoadingDelay:4,minAutoBitrate:0,emeEnabled:!1,widevineLicenseUrl:void 0,drmSystems:{},drmSystemOptions:{},requestMediaKeySystemAccessFunc:Wr,testBandwidth:!0,progressive:!1,lowLatencyMode:!0,cmcd:void 0,enableDateRangeMetadataCues:!0,enableEmsgMetadataCues:!0,enableID3MetadataCues:!0,useMediaCapabilities:!0,certLoadPolicy:{default:pc},keyLoadPolicy:{default:{maxTimeToFirstByteMs:8e3,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"},errorRetry:{maxNumRetry:8,retryDelayMs:1e3,maxRetryDelayMs:2e4,backoff:"linear"}}},manifestLoadPolicy:{default:{maxTimeToFirstByteMs:1/0,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},playlistLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:2,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},fragLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:12e4,timeoutRetry:{maxNumRetry:4,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:6,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},steeringManifestLoadPolicy:{default:{maxTimeToFirstByteMs:1e4,maxLoadTimeMs:2e4,timeoutRetry:{maxNumRetry:2,retryDelayMs:0,maxRetryDelayMs:0},errorRetry:{maxNumRetry:1,retryDelayMs:1e3,maxRetryDelayMs:8e3}}},manifestLoadingTimeOut:1e4,manifestLoadingMaxRetry:1,manifestLoadingRetryDelay:1e3,manifestLoadingMaxRetryTimeout:64e3,levelLoadingTimeOut:1e4,levelLoadingMaxRetry:4,levelLoadingRetryDelay:1e3,levelLoadingMaxRetryTimeout:64e3,fragLoadingTimeOut:2e4,fragLoadingMaxRetry:6,fragLoadingRetryDelay:1e3,fragLoadingMaxRetryTimeout:64e3},mc()),{},{subtitleStreamController:as,subtitleTrackController:cs,timelineController:Es,audioStreamController:ns,audioTrackController:os,emeController:Jt,cmcdController:Ls,contentSteeringController:_s});function mc(){return{cueHandler:gc,enableWebVTT:!0,enableIMSC1:!0,enableCEA708Captions:!0,captionsTextTrack1Label:"English",captionsTextTrack1LanguageCode:"en",captionsTextTrack2Label:"Spanish",captionsTextTrack2LanguageCode:"es",captionsTextTrack3Label:"Unknown CC",captionsTextTrack3LanguageCode:"",captionsTextTrack4Label:"Unknown CC",captionsTextTrack4LanguageCode:"",renderTextTracksNatively:!0}}function yc(s,e){if((e.liveSyncDurationCount||e.liveMaxLatencyDurationCount)&&(e.liveSyncDuration||e.liveMaxLatencyDuration))throw new Error("Illegal hls.js config: don't mix up liveSyncDurationCount/liveMaxLatencyDurationCount and liveSyncDuration/liveMaxLatencyDuration");if(e.liveMaxLatencyDurationCount!==void 0&&(e.liveSyncDurationCount===void 0||e.liveMaxLatencyDurationCount<=e.liveSyncDurationCount))throw new Error('Illegal hls.js config: "liveMaxLatencyDurationCount" must be greater than "liveSyncDurationCount"');if(e.liveMaxLatencyDuration!==void 0&&(e.liveSyncDuration===void 0||e.liveMaxLatencyDuration<=e.liveSyncDuration))throw new Error('Illegal hls.js config: "liveMaxLatencyDuration" must be greater than "liveSyncDuration"');let t=Is(s),i=["manifest","level","frag"],r=["TimeOut","MaxRetry","RetryDelay","MaxRetryTimeout"];return i.forEach(n=>{let o=`${n==="level"?"playlist":n}LoadPolicy`,a=e[o]===void 0,l=[];r.forEach(c=>{let h=`${n}Loading${c}`,u=e[h];if(u!==void 0&&a){l.push(h);let d=t[o].default;switch(e[o]={default:d},c){case"TimeOut":d.maxLoadTimeMs=u,d.maxTimeToFirstByteMs=u;break;case"MaxRetry":d.errorRetry.maxNumRetry=u,d.timeoutRetry.maxNumRetry=u;break;case"RetryDelay":d.errorRetry.retryDelayMs=u,d.timeoutRetry.retryDelayMs=u;break;case"MaxRetryTimeout":d.errorRetry.maxRetryDelayMs=u,d.timeoutRetry.maxRetryDelayMs=u;break}}}),l.length&&S.warn(`hls.js config: "${l.join('", "')}" setting(s) are deprecated, use "${o}": ${JSON.stringify(e[o])}`)}),he(he({},t),e)}function Is(s){return s&&typeof s=="object"?Array.isArray(s)?s.map(Is):Object.keys(s).reduce((e,t)=>(e[t]=Is(s[t]),e),{}):s}function Tc(s){let e=s.loader;e!==ti&&e!==ei?(S.log("[config]: Custom loader detected, cannot enable progressive streaming"),s.progressive=!1):ac()&&(s.loader=ti,s.progressive=!0,s.enableSoftwareAES=!0,S.log("[config]: Progressive streaming enabled, using FetchLoader"))}var bi,ws=class extends lt{constructor(e,t){super(e,"[level-controller]"),this._levels=[],this._firstLevel=-1,this._maxAutoLevel=-1,this._startLevel=void 0,this.currentLevel=null,this.currentLevelIndex=-1,this.manualLevelIndex=-1,this.steering=void 0,this.onParsedComplete=void 0,this.steering=t,this._registerListeners()}_registerListeners(){let{hls:e}=this;e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.MANIFEST_LOADED,this.onManifestLoaded,this),e.on(p.LEVEL_LOADED,this.onLevelLoaded,this),e.on(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(p.FRAG_BUFFERED,this.onFragBuffered,this),e.on(p.ERROR,this.onError,this)}_unregisterListeners(){let{hls:e}=this;e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.MANIFEST_LOADED,this.onManifestLoaded,this),e.off(p.LEVEL_LOADED,this.onLevelLoaded,this),e.off(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(p.FRAG_BUFFERED,this.onFragBuffered,this),e.off(p.ERROR,this.onError,this)}destroy(){this._unregisterListeners(),this.steering=null,this.resetLevels(),super.destroy()}stopLoad(){this._levels.forEach(t=>{t.loadError=0,t.fragmentError=0}),super.stopLoad()}resetLevels(){this._startLevel=void 0,this.manualLevelIndex=-1,this.currentLevelIndex=-1,this.currentLevel=null,this._levels=[],this._maxAutoLevel=-1}onManifestLoading(e,t){this.resetLevels()}onManifestLoaded(e,t){let i=this.hls.config.preferManagedMediaSource,r=[],n={},o={},a=!1,l=!1,c=!1;t.levels.forEach(h=>{var u,d;let f=h.attrs,{audioCodec:g,videoCodec:m}=h;((u=g)==null?void 0:u.indexOf("mp4a.40.34"))!==-1&&(bi||(bi=/chrome|firefox/i.test(navigator.userAgent)),bi&&(h.audioCodec=g=void 0)),g&&(h.audioCodec=g=Mt(g,i)),((d=m)==null?void 0:d.indexOf("avc1"))===0&&(m=h.videoCodec=zo(m));let{width:y,height:T,unknownCodecs:v}=h;if(a||(a=!!(y&&T)),l||(l=!!m),c||(c=!!g),v!=null&&v.length||g&&!ci(g,"audio",i)||m&&!ci(m,"video",i))return;let{CODECS:E,"FRAME-RATE":_,"HDCP-LEVEL":x,"PATHWAY-ID":I,RESOLUTION:L,"VIDEO-RANGE":C}=f,R=`${`${I||"."}-`}${h.bitrate}-${L}-${_}-${E}-${C}-${x}`;if(n[R])if(n[R].uri!==h.url&&!h.attrs["PATHWAY-ID"]){let D=o[R]+=1;h.attrs["PATHWAY-ID"]=new Array(D+1).join(".");let V=new Oe(h);n[R]=V,r.push(V)}else n[R].addGroupId("audio",f.AUDIO),n[R].addGroupId("text",f.SUBTITLES);else{let D=new Oe(h);n[R]=D,o[R]=1,r.push(D)}}),this.filterAndSortMediaOptions(r,t,a,l,c)}filterAndSortMediaOptions(e,t,i,r,n){let o=[],a=[],l=e;if((i||r)&&n&&(l=l.filter(({videoCodec:g,videoRange:m,width:y,height:T})=>(!!g||!!(y&&T))&&na(m))),l.length===0){Promise.resolve().then(()=>{if(this.hls){t.levels.length&&this.warn(`One or more CODECS in variant not supported: ${JSON.stringify(t.levels[0].attrs)}`);let g=new Error("no level with compatible codecs found in manifest");this.hls.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.MANIFEST_INCOMPATIBLE_CODECS_ERROR,fatal:!0,url:t.url,error:g,reason:g.message})}});return}if(t.audioTracks){let{preferManagedMediaSource:g}=this.hls.config;o=t.audioTracks.filter(m=>!m.audioCodec||ci(m.audioCodec,"audio",g)),Kr(o)}t.subtitles&&(a=t.subtitles,Kr(a));let c=l.slice(0);l.sort((g,m)=>{if(g.attrs["HDCP-LEVEL"]!==m.attrs["HDCP-LEVEL"])return(g.attrs["HDCP-LEVEL"]||"")>(m.attrs["HDCP-LEVEL"]||"")?1:-1;if(i&&g.height!==m.height)return g.height-m.height;if(g.frameRate!==m.frameRate)return g.frameRate-m.frameRate;if(g.videoRange!==m.videoRange)return Ft.indexOf(g.videoRange)-Ft.indexOf(m.videoRange);if(g.videoCodec!==m.videoCodec){let y=tr(g.videoCodec),T=tr(m.videoCodec);if(y!==T)return T-y}if(g.uri===m.uri&&g.codecSet!==m.codecSet){let y=Ot(g.codecSet),T=Ot(m.codecSet);if(y!==T)return T-y}return g.averageBitrate!==m.averageBitrate?g.averageBitrate-m.averageBitrate:0});let h=c[0];if(this.steering&&(l=this.steering.filterParsedLevels(l),l.length!==c.length)){for(let g=0;g<c.length;g++)if(c[g].pathwayId===l[0].pathwayId){h=c[g];break}}this._levels=l;for(let g=0;g<l.length;g++)if(l[g]===h){var u;this._firstLevel=g;let m=h.bitrate,y=this.hls.bandwidthEstimate;if(this.log(`manifest loaded, ${l.length} level(s) found, first bitrate: ${m}`),((u=this.hls.userConfig)==null?void 0:u.abrEwmaDefaultEstimate)===void 0){let T=Math.min(m,this.hls.config.abrEwmaDefaultEstimateMax);T>y&&y===Vn.abrEwmaDefaultEstimate&&(this.hls.bandwidthEstimate=T)}break}let d=n&&!r,f={levels:l,audioTracks:o,subtitleTracks:a,sessionData:t.sessionData,sessionKeys:t.sessionKeys,firstLevel:this._firstLevel,stats:t.stats,audio:n,video:r,altAudio:!d&&o.some(g=>!!g.url)};this.hls.trigger(p.MANIFEST_PARSED,f),(this.hls.config.autoStartLoad||this.hls.forceStartLoad)&&this.hls.startLoad(this.hls.config.startPosition)}get levels(){return this._levels.length===0?null:this._levels}get level(){return this.currentLevelIndex}set level(e){let t=this._levels;if(t.length===0)return;if(e<0||e>=t.length){let h=new Error("invalid level idx"),u=e<0;if(this.hls.trigger(p.ERROR,{type:G.OTHER_ERROR,details:b.LEVEL_SWITCH_ERROR,level:e,fatal:u,error:h,reason:h.message}),u)return;e=Math.min(e,t.length-1)}let i=this.currentLevelIndex,r=this.currentLevel,n=r?r.attrs["PATHWAY-ID"]:void 0,o=t[e],a=o.attrs["PATHWAY-ID"];if(this.currentLevelIndex=e,this.currentLevel=o,i===e&&o.details&&r&&n===a)return;this.log(`Switching to level ${e} (${o.height?o.height+"p ":""}${o.videoRange?o.videoRange+" ":""}${o.codecSet?o.codecSet+" ":""}@${o.bitrate})${a?" with Pathway "+a:""} from level ${i}${n?" with Pathway "+n:""}`);let l={level:e,attrs:o.attrs,details:o.details,bitrate:o.bitrate,averageBitrate:o.averageBitrate,maxBitrate:o.maxBitrate,realBitrate:o.realBitrate,width:o.width,height:o.height,codecSet:o.codecSet,audioCodec:o.audioCodec,videoCodec:o.videoCodec,audioGroups:o.audioGroups,subtitleGroups:o.subtitleGroups,loaded:o.loaded,loadError:o.loadError,fragmentError:o.fragmentError,name:o.name,id:o.id,uri:o.uri,url:o.url,urlId:0,audioGroupIds:o.audioGroupIds,textGroupIds:o.textGroupIds};this.hls.trigger(p.LEVEL_SWITCHING,l);let c=o.details;if(!c||c.live){let h=this.switchParams(o.uri,r==null?void 0:r.details,c);this.loadPlaylist(h)}}get manualLevel(){return this.manualLevelIndex}set manualLevel(e){this.manualLevelIndex=e,this._startLevel===void 0&&(this._startLevel=e),e!==-1&&(this.level=e)}get firstLevel(){return this._firstLevel}set firstLevel(e){this._firstLevel=e}get startLevel(){if(this._startLevel===void 0){let e=this.hls.config.startLevel;return e!==void 0?e:this.hls.firstAutoLevel}return this._startLevel}set startLevel(e){this._startLevel=e}onError(e,t){t.fatal||!t.context||t.context.type===j.LEVEL&&t.context.level===this.level&&this.checkRetry(t)}onFragBuffered(e,{frag:t}){if(t!==void 0&&t.type===$.MAIN){let i=t.elementaryStreams;if(!Object.keys(i).some(n=>!!i[n]))return;let r=this._levels[t.level];r!=null&&r.loadError&&(this.log(`Resetting level error count of ${r.loadError} on frag buffered`),r.loadError=0)}}onLevelLoaded(e,t){var i;let{level:r,details:n}=t,o=this._levels[r];if(!o){var a;this.warn(`Invalid level index ${r}`),(a=t.deliveryDirectives)!=null&&a.skip&&(n.deltaUpdateFailed=!0);return}r===this.currentLevelIndex?(o.fragmentError===0&&(o.loadError=0),this.playlistLoaded(r,t,o.details)):(i=t.deliveryDirectives)!=null&&i.skip&&(n.deltaUpdateFailed=!0)}loadPlaylist(e){super.loadPlaylist();let t=this.currentLevelIndex,i=this.currentLevel;if(i&&this.shouldLoadPlaylist(i)){let r=i.uri;if(e)try{r=e.addDirectives(r)}catch(o){this.warn(`Could not construct new URL with HLS Delivery Directives: ${o}`)}let n=i.attrs["PATHWAY-ID"];this.log(`Loading level index ${t}${(e==null?void 0:e.msn)!==void 0?" at sn "+e.msn+" part "+e.part:""} with${n?" Pathway "+n:""} ${r}`),this.clearTimer(),this.hls.trigger(p.LEVEL_LOADING,{url:r,level:t,pathwayId:i.attrs["PATHWAY-ID"],id:0,deliveryDirectives:e||null})}}get nextLoadLevel(){return this.manualLevelIndex!==-1?this.manualLevelIndex:this.hls.nextAutoLevel}set nextLoadLevel(e){this.level=e,this.manualLevelIndex===-1&&(this.hls.nextAutoLevel=e)}removeLevel(e){var t;let i=this._levels.filter((r,n)=>n!==e?!0:(this.steering&&this.steering.removeLevel(r),r===this.currentLevel&&(this.currentLevel=null,this.currentLevelIndex=-1,r.details&&r.details.fragments.forEach(o=>o.level=-1)),!1));un(i),this._levels=i,this.currentLevelIndex>-1&&(t=this.currentLevel)!=null&&t.details&&(this.currentLevelIndex=this.currentLevel.details.fragments[0].level),this.hls.trigger(p.LEVELS_UPDATED,{levels:i})}onLevelsUpdated(e,{levels:t}){this._levels=t}checkMaxAutoUpdated(){let{autoLevelCapping:e,maxAutoLevel:t,maxHdcpLevel:i}=this.hls;this._maxAutoLevel!==t&&(this._maxAutoLevel=t,this.hls.trigger(p.MAX_AUTO_LEVEL_UPDATED,{autoLevelCapping:e,levels:this.levels,maxAutoLevel:t,minAutoLevel:this.hls.minAutoLevel,maxHdcpLevel:i}))}};function Kr(s){let e={};s.forEach(t=>{let i=t.groupId||"";t.id=e[i]=e[i]||0,e[i]++})}var Cs=class{constructor(e){this.config=void 0,this.keyUriToKeyInfo={},this.emeController=null,this.config=e}abort(e){for(let i in this.keyUriToKeyInfo){let r=this.keyUriToKeyInfo[i].loader;if(r){var t;if(e&&e!==((t=r.context)==null?void 0:t.frag.type))return;r.abort()}}}detach(){for(let e in this.keyUriToKeyInfo){let t=this.keyUriToKeyInfo[e];(t.mediaKeySessionContext||t.decryptdata.isCommonEncryption)&&delete this.keyUriToKeyInfo[e]}}destroy(){this.detach();for(let e in this.keyUriToKeyInfo){let t=this.keyUriToKeyInfo[e].loader;t&&t.destroy()}this.keyUriToKeyInfo={}}createKeyLoadError(e,t=b.KEY_LOAD_ERROR,i,r,n){return new xe({type:G.NETWORK_ERROR,details:t,fatal:!1,frag:e,response:n,error:i,networkDetails:r})}loadClear(e,t){if(this.emeController&&this.config.emeEnabled){let{sn:i,cc:r}=e;for(let n=0;n<t.length;n++){let o=t[n];if(r<=o.cc&&(i==="initSegment"||o.sn==="initSegment"||i<o.sn)){this.emeController.selectKeySystemFormat(o).then(a=>{o.setKeyFormat(a)});break}}}}load(e){return!e.decryptdata&&e.encrypted&&this.emeController?this.emeController.selectKeySystemFormat(e).then(t=>this.loadInternal(e,t)):this.loadInternal(e)}loadInternal(e,t){var i,r;t&&e.setKeyFormat(t);let n=e.decryptdata;if(!n){let c=new Error(t?`Expected frag.decryptdata to be defined after setting format ${t}`:"Missing decryption data on fragment in onKeyLoading");return Promise.reject(this.createKeyLoadError(e,b.KEY_LOAD_ERROR,c))}let o=n.uri;if(!o)return Promise.reject(this.createKeyLoadError(e,b.KEY_LOAD_ERROR,new Error(`Invalid key URI: "${o}"`)));let a=this.keyUriToKeyInfo[o];if((i=a)!=null&&i.decryptdata.key)return n.key=a.decryptdata.key,Promise.resolve({frag:e,keyInfo:a});if((r=a)!=null&&r.keyLoadPromise){var l;switch((l=a.mediaKeySessionContext)==null?void 0:l.keyStatus){case void 0:case"status-pending":case"usable":case"usable-in-future":return a.keyLoadPromise.then(c=>(n.key=c.keyInfo.decryptdata.key,{frag:e,keyInfo:a}))}}switch(a=this.keyUriToKeyInfo[o]={decryptdata:n,keyLoadPromise:null,loader:null,mediaKeySessionContext:null},n.method){case"ISO-23001-7":case"SAMPLE-AES":case"SAMPLE-AES-CENC":case"SAMPLE-AES-CTR":return n.keyFormat==="identity"?this.loadKeyHTTP(a,e):this.loadKeyEME(a,e);case"AES-128":return this.loadKeyHTTP(a,e);default:return Promise.reject(this.createKeyLoadError(e,b.KEY_LOAD_ERROR,new Error(`Key supplied with unsupported METHOD: "${n.method}"`)))}}loadKeyEME(e,t){let i={frag:t,keyInfo:e};if(this.emeController&&this.config.emeEnabled){let r=this.emeController.loadKey(i);if(r)return(e.keyLoadPromise=r.then(n=>(e.mediaKeySessionContext=n,i))).catch(n=>{throw e.keyLoadPromise=null,n})}return Promise.resolve(i)}loadKeyHTTP(e,t){let i=this.config,r=i.loader,n=new r(i);return t.keyLoader=e.loader=n,e.keyLoadPromise=new Promise((o,a)=>{let l={keyInfo:e,frag:t,responseType:"arraybuffer",url:e.decryptdata.uri},c=i.keyLoadPolicy.default,h={loadPolicy:c,timeout:c.maxLoadTimeMs,maxRetry:0,retryDelay:0,maxRetryDelay:0},u={onSuccess:(d,f,g,m)=>{let{frag:y,keyInfo:T,url:v}=g;if(!y.decryptdata||T!==this.keyUriToKeyInfo[v])return a(this.createKeyLoadError(y,b.KEY_LOAD_ERROR,new Error("after key load, decryptdata unset or changed"),m));T.decryptdata.key=y.decryptdata.key=new Uint8Array(d.data),y.keyLoader=null,T.loader=null,o({frag:y,keyInfo:T})},onError:(d,f,g,m)=>{this.resetLoader(f),a(this.createKeyLoadError(t,b.KEY_LOAD_ERROR,new Error(`HTTP Error ${d.code} loading key ${d.text}`),g,he({url:l.url,data:void 0},d)))},onTimeout:(d,f,g)=>{this.resetLoader(f),a(this.createKeyLoadError(t,b.KEY_LOAD_TIMEOUT,new Error("key loading timed out"),g))},onAbort:(d,f,g)=>{this.resetLoader(f),a(this.createKeyLoadError(t,b.INTERNAL_ABORTED,new Error("key loading aborted"),g))}};n.load(l,h,u)})}resetLoader(e){let{frag:t,keyInfo:i,url:r}=e,n=i.loader;t.keyLoader===n&&(t.keyLoader=null,i.loader=null),delete this.keyUriToKeyInfo[r],n&&n.destroy()}};function Yn(){return self.SourceBuffer||self.WebKitSourceBuffer}function Wn(){if(!$e())return!1;let e=Yn();return!e||e.prototype&&typeof e.prototype.appendBuffer=="function"&&typeof e.prototype.remove=="function"}function Ec(){if(!Wn())return!1;let s=$e();return typeof(s==null?void 0:s.isTypeSupported)=="function"&&(["avc1.42E01E,mp4a.40.2","av01.0.01M.08","vp09.00.50.08"].some(e=>s.isTypeSupported(at(e,"video")))||["mp4a.40.2","fLaC"].some(e=>s.isTypeSupported(at(e,"audio"))))}function vc(){var s;let e=Yn();return typeof(e==null||(s=e.prototype)==null?void 0:s.changeType)=="function"}var xc=250,wt=2,Sc=.1,bc=.05,Ds=class{constructor(e,t,i,r){this.config=void 0,this.media=null,this.fragmentTracker=void 0,this.hls=void 0,this.nudgeRetry=0,this.stallReported=!1,this.stalled=null,this.moved=!1,this.seeking=!1,this.config=e,this.media=t,this.fragmentTracker=i,this.hls=r}destroy(){this.media=null,this.hls=this.fragmentTracker=null}poll(e,t){let{config:i,media:r,stalled:n}=this;if(r===null)return;let{currentTime:o,seeking:a}=r,l=this.seeking&&!a,c=!this.seeking&&a;if(this.seeking=a,o!==e){if(this.moved=!0,a||(this.nudgeRetry=0),n!==null){if(this.stallReported){let y=self.performance.now()-n;S.warn(`playback not stuck anymore @${o}, after ${Math.round(y)}ms`),this.stallReported=!1}this.stalled=null}return}if(c||l){this.stalled=null;return}if(r.paused&&!a||r.ended||r.playbackRate===0||!Z.getBuffered(r).length){this.nudgeRetry=0;return}let h=Z.bufferInfo(r,o,0),u=h.nextStart||0;if(a){let y=h.len>wt,T=!u||t&&t.start<=o||u-o>wt&&!this.fragmentTracker.getPartialFragment(o);if(y||T)return;this.moved=!1}if(!this.moved&&this.stalled!==null){var d;if(!(h.len>0)&&!u)return;let T=Math.max(u,h.start||0)-o,v=this.hls.levels?this.hls.levels[this.hls.currentLevel]:null,_=(v==null||(d=v.details)==null?void 0:d.live)?v.details.targetduration*2:wt,x=this.fragmentTracker.getPartialFragment(o);if(T>0&&(T<=_||x)){r.paused||this._trySkipBufferHole(x);return}}let f=self.performance.now();if(n===null){this.stalled=f;return}let g=f-n;if(!a&&g>=xc&&(this._reportStall(h),!this.media))return;let m=Z.bufferInfo(r,o,i.maxBufferHole);this._tryFixBufferStall(m,g)}_tryFixBufferStall(e,t){let{config:i,fragmentTracker:r,media:n}=this;if(n===null)return;let o=n.currentTime,a=r.getPartialFragment(o);a&&(this._trySkipBufferHole(a)||!this.media)||(e.len>i.maxBufferHole||e.nextStart&&e.nextStart-o<i.maxBufferHole)&&t>i.highBufferWatchdogPeriod*1e3&&(S.warn("Trying to nudge playhead over buffer-hole"),this.stalled=null,this._tryNudgeBuffer())}_reportStall(e){let{hls:t,media:i,stallReported:r}=this;if(!r&&i){this.stallReported=!0;let n=new Error(`Playback stalling at @${i.currentTime} due to low buffer (${JSON.stringify(e)})`);S.warn(n.message),t.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.BUFFER_STALLED_ERROR,fatal:!1,error:n,buffer:e.len})}}_trySkipBufferHole(e){let{config:t,hls:i,media:r}=this;if(r===null)return 0;let n=r.currentTime,o=Z.bufferInfo(r,n,0),a=n<o.start?o.start:o.nextStart;if(a){let l=o.len<=t.maxBufferHole,c=o.len>0&&o.len<1&&r.readyState<3,h=a-n;if(h>0&&(l||c)){if(h>t.maxBufferHole){let{fragmentTracker:d}=this,f=!1;if(n===0){let g=d.getAppendedFrag(0,$.MAIN);g&&a<g.end&&(f=!0)}if(!f){let g=e||d.getAppendedFrag(n,$.MAIN);if(g){let m=!1,y=g.end;for(;y<a;){let T=d.getPartialFragment(y);if(T)y+=T.duration;else{m=!0;break}}if(m)return 0}}}let u=Math.max(a+bc,n+Sc);if(S.warn(`skipping hole, adjusting currentTime from ${n} to ${u}`),this.moved=!0,this.stalled=null,r.currentTime=u,e&&!e.gap){let d=new Error(`fragment loaded with buffer holes, seeking from ${n} to ${u}`);i.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.BUFFER_SEEK_OVER_HOLE,fatal:!1,error:d,reason:d.message,frag:e})}return u}}return 0}_tryNudgeBuffer(){let{config:e,hls:t,media:i,nudgeRetry:r}=this;if(i===null)return;let n=i.currentTime;if(this.nudgeRetry++,r<e.nudgeMaxRetry){let o=n+(r+1)*e.nudgeOffset,a=new Error(`Nudging 'currentTime' from ${n} to ${o}`);S.warn(a.message),i.currentTime=o,t.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.BUFFER_NUDGE_ON_STALL,error:a,fatal:!1})}else{let o=new Error(`Playhead still not moving while enough data buffered @${n} after ${e.nudgeMaxRetry} nudges`);S.error(o.message),t.trigger(p.ERROR,{type:G.MEDIA_ERROR,details:b.BUFFER_STALLED_ERROR,error:o,fatal:!0})}}},Ac=100,ks=class extends ut{constructor(e,t,i){super(e,t,i,"[stream-controller]",$.MAIN),this.audioCodecSwap=!1,this.gapController=null,this.level=-1,this._forceStartLoad=!1,this.altAudio=!1,this.audioOnly=!1,this.fragPlaying=null,this.onvplaying=null,this.onvseeked=null,this.fragLastKbps=0,this.couldBacktrack=!1,this.backtrackFragment=null,this.audioCodecSwitch=!1,this.videoBuffer=null,this._registerListeners()}_registerListeners(){let{hls:e}=this;e.on(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.on(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.on(p.MANIFEST_LOADING,this.onManifestLoading,this),e.on(p.MANIFEST_PARSED,this.onManifestParsed,this),e.on(p.LEVEL_LOADING,this.onLevelLoading,this),e.on(p.LEVEL_LOADED,this.onLevelLoaded,this),e.on(p.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),e.on(p.ERROR,this.onError,this),e.on(p.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.on(p.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),e.on(p.BUFFER_CREATED,this.onBufferCreated,this),e.on(p.BUFFER_FLUSHED,this.onBufferFlushed,this),e.on(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.on(p.FRAG_BUFFERED,this.onFragBuffered,this)}_unregisterListeners(){let{hls:e}=this;e.off(p.MEDIA_ATTACHED,this.onMediaAttached,this),e.off(p.MEDIA_DETACHING,this.onMediaDetaching,this),e.off(p.MANIFEST_LOADING,this.onManifestLoading,this),e.off(p.MANIFEST_PARSED,this.onManifestParsed,this),e.off(p.LEVEL_LOADED,this.onLevelLoaded,this),e.off(p.FRAG_LOAD_EMERGENCY_ABORTED,this.onFragLoadEmergencyAborted,this),e.off(p.ERROR,this.onError,this),e.off(p.AUDIO_TRACK_SWITCHING,this.onAudioTrackSwitching,this),e.off(p.AUDIO_TRACK_SWITCHED,this.onAudioTrackSwitched,this),e.off(p.BUFFER_CREATED,this.onBufferCreated,this),e.off(p.BUFFER_FLUSHED,this.onBufferFlushed,this),e.off(p.LEVELS_UPDATED,this.onLevelsUpdated,this),e.off(p.FRAG_BUFFERED,this.onFragBuffered,this)}onHandlerDestroying(){this._unregisterListeners(),super.onHandlerDestroying()}startLoad(e){if(this.levels){let{lastCurrentTime:t,hls:i}=this;if(this.stopLoad(),this.setInterval(Ac),this.level=-1,!this.startFragRequested){let r=i.startLevel;r===-1&&(i.config.testBandwidth&&this.levels.length>1?(r=0,this.bitrateTest=!0):r=i.firstAutoLevel),i.nextLoadLevel=r,this.level=i.loadLevel,this.loadedmetadata=!1}t>0&&e===-1&&(this.log(`Override startPosition with lastCurrentTime @${t.toFixed(3)}`),e=t),this.state=w.IDLE,this.nextLoadPosition=this.startPosition=this.lastCurrentTime=e,this.tick()}else this._forceStartLoad=!0,this.state=w.STOPPED}stopLoad(){this._forceStartLoad=!1,super.stopLoad()}doTick(){switch(this.state){case w.WAITING_LEVEL:{let{levels:t,level:i}=this,r=t==null?void 0:t[i],n=r==null?void 0:r.details;if(n&&(!n.live||this.levelLastLoaded===r)){if(this.waitForCdnTuneIn(n))break;this.state=w.IDLE;break}else if(this.hls.nextLoadLevel!==this.level){this.state=w.IDLE;break}break}case w.FRAG_LOADING_WAITING_RETRY:{var e;let t=self.performance.now(),i=this.retryDate;if(!i||t>=i||(e=this.media)!=null&&e.seeking){let{levels:r,level:n}=this,o=r==null?void 0:r[n];this.resetStartWhenNotLoaded(o||null),this.state=w.IDLE}}break}this.state===w.IDLE&&this.doTickIdle(),this.onTickEnd()}onTickEnd(){super.onTickEnd(),this.checkBuffer(),this.checkFragmentChanged()}doTickIdle(){let{hls:e,levelLastLoaded:t,levels:i,media:r}=this;if(t===null||!r&&(this.startFragRequested||!e.config.startFragPrefetch)||this.altAudio&&this.audioOnly)return;let n=e.nextLoadLevel;if(!(i!=null&&i[n]))return;let o=i[n],a=this.getMainFwdBufferInfo();if(a===null)return;let l=this.getLevelDetails();if(l&&this._streamEnded(a,l)){let m={};this.altAudio&&(m.type="video"),this.hls.trigger(p.BUFFER_EOS,m),this.state=w.ENDED;return}e.loadLevel!==n&&e.manualLevel===-1&&this.log(`Adapting to level ${n} from level ${this.level}`),this.level=e.nextLoadLevel=n;let c=o.details;if(!c||this.state===w.WAITING_LEVEL||c.live&&this.levelLastLoaded!==o){this.level=n,this.state=w.WAITING_LEVEL;return}let h=a.len,u=this.getMaxBufferLength(o.maxBitrate);if(h>=u)return;this.backtrackFragment&&this.backtrackFragment.start>a.end&&(this.backtrackFragment=null);let d=this.backtrackFragment?this.backtrackFragment.start:a.end,f=this.getNextFragment(d,c);if(this.couldBacktrack&&!this.fragPrevious&&f&&f.sn!=="initSegment"&&this.fragmentTracker.getState(f)!==ce.OK){var g;let y=((g=this.backtrackFragment)!=null?g:f).sn-c.startSN,T=c.fragments[y-1];T&&f.cc===T.cc&&(f=T,this.fragmentTracker.removeFragment(T))}else this.backtrackFragment&&a.len&&(this.backtrackFragment=null);if(f&&this.isLoopLoading(f,d)){if(!f.gap){let y=this.audioOnly&&!this.altAudio?J.AUDIO:J.VIDEO,T=(y===J.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;T&&this.afterBufferFlushed(T,y,$.MAIN)}f=this.getNextFragmentLoopLoading(f,c,a,$.MAIN,u)}f&&(f.initSegment&&!f.initSegment.data&&!this.bitrateTest&&(f=f.initSegment),this.loadFragment(f,o,d))}loadFragment(e,t,i){let r=this.fragmentTracker.getState(e);this.fragCurrent=e,r===ce.NOT_LOADED||r===ce.PARTIAL?e.sn==="initSegment"?this._loadInitSegment(e,t):this.bitrateTest?(this.log(`Fragment ${e.sn} of level ${e.level} is being downloaded to test bitrate and will not be buffered`),this._loadBitrateTestFrag(e,t)):(this.startFragRequested=!0,super.loadFragment(e,t,i)):this.clearTrackerIfNeeded(e)}getBufferedFrag(e){return this.fragmentTracker.getBufferedFrag(e,$.MAIN)}followingBufferedFrag(e){return e?this.getBufferedFrag(e.end+.5):null}immediateLevelSwitch(){this.abortCurrentFrag(),this.flushMainBuffer(0,Number.POSITIVE_INFINITY)}nextLevelSwitch(){let{levels:e,media:t}=this;if(t!=null&&t.readyState){let i,r=this.getAppendedFrag(t.currentTime);r&&r.start>1&&this.flushMainBuffer(0,r.start-1);let n=this.getLevelDetails();if(n!=null&&n.live){let a=this.getMainFwdBufferInfo();if(!a||a.len<n.targetduration*2)return}if(!t.paused&&e){let a=this.hls.nextLoadLevel,l=e[a],c=this.fragLastKbps;c&&this.fragCurrent?i=this.fragCurrent.duration*l.maxBitrate/(1e3*c)+1:i=0}else i=0;let o=this.getBufferedFrag(t.currentTime+i);if(o){let a=this.followingBufferedFrag(o);if(a){this.abortCurrentFrag();let l=a.maxStartPTS?a.maxStartPTS:a.start,c=a.duration,h=Math.max(o.end,l+Math.min(Math.max(c-this.config.maxFragLookUpTolerance,c*(this.couldBacktrack?.5:.125)),c*(this.couldBacktrack?.75:.25)));this.flushMainBuffer(h,Number.POSITIVE_INFINITY)}}}}abortCurrentFrag(){let e=this.fragCurrent;switch(this.fragCurrent=null,this.backtrackFragment=null,e&&(e.abortRequests(),this.fragmentTracker.removeFragment(e)),this.state){case w.KEY_LOADING:case w.FRAG_LOADING:case w.FRAG_LOADING_WAITING_RETRY:case w.PARSING:case w.PARSED:this.state=w.IDLE;break}this.nextLoadPosition=this.getLoadPosition()}flushMainBuffer(e,t){super.flushMainBuffer(e,t,this.altAudio?"video":null)}onMediaAttached(e,t){super.onMediaAttached(e,t);let i=t.media;this.onvplaying=this.onMediaPlaying.bind(this),this.onvseeked=this.onMediaSeeked.bind(this),i.addEventListener("playing",this.onvplaying),i.addEventListener("seeked",this.onvseeked),this.gapController=new Ds(this.config,i,this.fragmentTracker,this.hls)}onMediaDetaching(){let{media:e}=this;e&&this.onvplaying&&this.onvseeked&&(e.removeEventListener("playing",this.onvplaying),e.removeEventListener("seeked",this.onvseeked),this.onvplaying=this.onvseeked=null,this.videoBuffer=null),this.fragPlaying=null,this.gapController&&(this.gapController.destroy(),this.gapController=null),super.onMediaDetaching()}onMediaPlaying(){this.tick()}onMediaSeeked(){let e=this.media,t=e?e.currentTime:null;M(t)&&this.log(`Media seeked to ${t.toFixed(3)}`);let i=this.getMainFwdBufferInfo();if(i===null||i.len===0){this.warn(`Main forward buffer length on "seeked" event ${i?i.len:"empty"})`);return}this.tick()}onManifestLoading(){this.log("Trigger BUFFER_RESET"),this.hls.trigger(p.BUFFER_RESET,void 0),this.fragmentTracker.removeAllFragments(),this.couldBacktrack=!1,this.startPosition=this.lastCurrentTime=this.fragLastKbps=0,this.levels=this.fragPlaying=this.backtrackFragment=this.levelLastLoaded=null,this.altAudio=this.audioOnly=this.startFragRequested=!1}onManifestParsed(e,t){let i=!1,r=!1;t.levels.forEach(n=>{let o=n.audioCodec;o&&(i=i||o.indexOf("mp4a.40.2")!==-1,r=r||o.indexOf("mp4a.40.5")!==-1)}),this.audioCodecSwitch=i&&r&&!vc(),this.audioCodecSwitch&&this.log("Both AAC/HE-AAC audio found in levels; declaring level codec as HE-AAC"),this.levels=t.levels,this.startFragRequested=!1}onLevelLoading(e,t){let{levels:i}=this;if(!i||this.state!==w.IDLE)return;let r=i[t.level];(!r.details||r.details.live&&this.levelLastLoaded!==r||this.waitForCdnTuneIn(r.details))&&(this.state=w.WAITING_LEVEL)}onLevelLoaded(e,t){var i;let{levels:r}=this,n=t.level,o=t.details,a=o.totalduration;if(!r){this.warn(`Levels were reset while loading level ${n}`);return}this.log(`Level ${n} loaded [${o.startSN},${o.endSN}]${o.lastPartSn?`[part-${o.lastPartSn}-${o.lastPartIndex}]`:""}, cc [${o.startCC}, ${o.endCC}] duration:${a}`);let l=r[n],c=this.fragCurrent;c&&(this.state===w.FRAG_LOADING||this.state===w.FRAG_LOADING_WAITING_RETRY)&&c.level!==t.level&&c.loader&&this.abortCurrentFrag();let h=0;if(o.live||(i=l.details)!=null&&i.live){var u;if(this.checkLiveUpdate(o),o.deltaUpdateFailed)return;h=this.alignPlaylists(o,l.details,(u=this.levelLastLoaded)==null?void 0:u.details)}if(l.details=o,this.levelLastLoaded=l,this.hls.trigger(p.LEVEL_UPDATED,{details:o,level:n}),this.state===w.WAITING_LEVEL){if(this.waitForCdnTuneIn(o))return;this.state=w.IDLE}this.startFragRequested?o.live&&this.synchronizeToLiveEdge(o):this.setStartPosition(o,h),this.tick()}_handleFragmentLoadProgress(e){var t;let{frag:i,part:r,payload:n}=e,{levels:o}=this;if(!o){this.warn(`Levels were reset while fragment load was in progress. Fragment ${i.sn} of level ${i.level} will not be buffered`);return}let a=o[i.level],l=a.details;if(!l){this.warn(`Dropping fragment ${i.sn} of level ${i.level} after level details were reset`),this.fragmentTracker.removeFragment(i);return}let c=a.videoCodec,h=l.PTSKnown||!l.live,u=(t=i.initSegment)==null?void 0:t.data,d=this._getAudioCodec(a),f=this.transmuxer=this.transmuxer||new jt(this.hls,$.MAIN,this._handleTransmuxComplete.bind(this),this._handleTransmuxerFlush.bind(this)),g=r?r.index:-1,m=g!==-1,y=new ct(i.level,i.sn,i.stats.chunkCount,n.byteLength,g,m),T=this.initPTS[i.cc];f.push(n,u,d,c,i,r,l.totalduration,h,y,T)}onAudioTrackSwitching(e,t){let i=this.altAudio;if(!!!t.url){if(this.mediaBuffer!==this.media){this.log("Switching on main audio, use media.buffered to schedule main fragment loading"),this.mediaBuffer=this.media;let o=this.fragCurrent;o&&(this.log("Switching to main audio track, cancel main fragment load"),o.abortRequests(),this.fragmentTracker.removeFragment(o)),this.resetTransmuxer(),this.resetLoadingState()}else this.audioOnly&&this.resetTransmuxer();let n=this.hls;i&&(n.trigger(p.BUFFER_FLUSHING,{startOffset:0,endOffset:Number.POSITIVE_INFINITY,type:null}),this.fragmentTracker.removeAllFragments()),n.trigger(p.AUDIO_TRACK_SWITCHED,t)}}onAudioTrackSwitched(e,t){let i=t.id,r=!!this.hls.audioTracks[i].url;if(r){let n=this.videoBuffer;n&&this.mediaBuffer!==n&&(this.log("Switching on alternate audio, use video.buffered to schedule main fragment loading"),this.mediaBuffer=n)}this.altAudio=r,this.tick()}onBufferCreated(e,t){let i=t.tracks,r,n,o=!1;for(let a in i){let l=i[a];if(l.id==="main"){if(n=a,r=l,a==="video"){let c=i[a];c&&(this.videoBuffer=c.buffer)}}else o=!0}o&&r?(this.log(`Alternate track found, use ${n}.buffered to schedule main fragment loading`),this.mediaBuffer=r.buffer):this.mediaBuffer=this.media}onFragBuffered(e,t){let{frag:i,part:r}=t;if(i&&i.type!==$.MAIN)return;if(this.fragContextChanged(i)){this.warn(`Fragment ${i.sn}${r?" p: "+r.index:""} of level ${i.level} finished buffering, but was aborted. state: ${this.state}`),this.state===w.PARSED&&(this.state=w.IDLE);return}let n=r?r.stats:i.stats;this.fragLastKbps=Math.round(8*n.total/(n.buffering.end-n.loading.first)),i.sn!=="initSegment"&&(this.fragPrevious=i),this.fragBufferedComplete(i,r)}onError(e,t){var i;if(t.fatal){this.state=w.ERROR;return}switch(t.details){case b.FRAG_GAP:case b.FRAG_PARSING_ERROR:case b.FRAG_DECRYPT_ERROR:case b.FRAG_LOAD_ERROR:case b.FRAG_LOAD_TIMEOUT:case b.KEY_LOAD_ERROR:case b.KEY_LOAD_TIMEOUT:this.onFragmentOrKeyLoadError($.MAIN,t);break;case b.LEVEL_LOAD_ERROR:case b.LEVEL_LOAD_TIMEOUT:case b.LEVEL_PARSING_ERROR:!t.levelRetry&&this.state===w.WAITING_LEVEL&&((i=t.context)==null?void 0:i.type)===j.LEVEL&&(this.state=w.IDLE);break;case b.BUFFER_APPEND_ERROR:case b.BUFFER_FULL_ERROR:if(!t.parent||t.parent!=="main")return;if(t.details===b.BUFFER_APPEND_ERROR){this.resetLoadingState();return}this.reduceLengthAndFlushBuffer(t)&&this.flushMainBuffer(0,Number.POSITIVE_INFINITY);break;case b.INTERNAL_EXCEPTION:this.recoverWorkerError(t);break}}checkBuffer(){let{media:e,gapController:t}=this;if(!(!e||!t||!e.readyState)){if(this.loadedmetadata||!Z.getBuffered(e).length){let i=this.state!==w.IDLE?this.fragCurrent:null;t.poll(this.lastCurrentTime,i)}this.lastCurrentTime=e.currentTime}}onFragLoadEmergencyAborted(){this.state=w.IDLE,this.loadedmetadata||(this.startFragRequested=!1,this.nextLoadPosition=this.startPosition),this.tickImmediate()}onBufferFlushed(e,{type:t}){if(t!==J.AUDIO||this.audioOnly&&!this.altAudio){let i=(t===J.VIDEO?this.videoBuffer:this.mediaBuffer)||this.media;this.afterBufferFlushed(i,t,$.MAIN),this.tick()}}onLevelsUpdated(e,t){this.level>-1&&this.fragCurrent&&(this.level=this.fragCurrent.level),this.levels=t.levels}swapAudioCodec(){this.audioCodecSwap=!this.audioCodecSwap}seekToStartPos(){let{media:e}=this;if(!e)return;let t=e.currentTime,i=this.startPosition;if(i>=0&&t<i){if(e.seeking){this.log(`could not seek to ${i}, already seeking at ${t}`);return}let r=Z.getBuffered(e),o=(r.length?r.start(0):0)-i;o>0&&(o<this.config.maxBufferHole||o<this.config.maxFragLookUpTolerance)&&(this.log(`adjusting start position by ${o} to match buffer start`),i+=o,this.startPosition=i),this.log(`seek to target start position ${i} from current time ${t}`),e.currentTime=i}}_getAudioCodec(e){let t=this.config.defaultAudioCodec||e.audioCodec;return this.audioCodecSwap&&t&&(this.log("Swapping audio codec"),t.indexOf("mp4a.40.5")!==-1?t="mp4a.40.2":t="mp4a.40.5"),t}_loadBitrateTestFrag(e,t){e.bitrateTest=!0,this._doFragLoad(e,t).then(i=>{let{hls:r}=this;if(!i||this.fragContextChanged(e))return;t.fragmentError=0,this.state=w.IDLE,this.startFragRequested=!1,this.bitrateTest=!1;let n=e.stats;n.parsing.start=n.parsing.end=n.buffering.start=n.buffering.end=self.performance.now(),r.trigger(p.FRAG_LOADED,i),e.bitrateTest=!1})}_handleTransmuxComplete(e){var t;let i="main",{hls:r}=this,{remuxResult:n,chunkMeta:o}=e,a=this.getCurrentContext(o);if(!a){this.resetWhenMissingContext(o);return}let{frag:l,part:c,level:h}=a,{video:u,text:d,id3:f,initSegment:g}=n,{details:m}=h,y=this.altAudio?void 0:n.audio;if(this.fragContextChanged(l)){this.fragmentTracker.removeFragment(l);return}if(this.state=w.PARSING,g){if(g!=null&&g.tracks){let E=l.initSegment||l;this._bufferInitSegment(h,g.tracks,E,o),r.trigger(p.FRAG_PARSING_INIT_SEGMENT,{frag:E,id:i,tracks:g.tracks})}let T=g.initPTS,v=g.timescale;M(T)&&(this.initPTS[l.cc]={baseTime:T,timescale:v},r.trigger(p.INIT_PTS_FOUND,{frag:l,id:i,initPTS:T,timescale:v}))}if(u&&m&&l.sn!=="initSegment"){let T=m.fragments[l.sn-1-m.startSN],v=l.sn===m.startSN,E=!T||l.cc>T.cc;if(n.independent!==!1){let{startPTS:_,endPTS:x,startDTS:I,endDTS:L}=u;if(c)c.elementaryStreams[u.type]={startPTS:_,endPTS:x,startDTS:I,endDTS:L};else if(u.firstKeyFrame&&u.independent&&o.id===1&&!E&&(this.couldBacktrack=!0),u.dropped&&u.independent){let C=this.getMainFwdBufferInfo(),k=(C?C.end:this.getLoadPosition())+this.config.maxBufferHole,R=u.firstKeyFramePTS?u.firstKeyFramePTS:_;if(!v&&k<R-this.config.maxBufferHole&&!E){this.backtrack(l);return}else E&&(l.gap=!0);l.setElementaryStreamInfo(u.type,l.start,x,l.start,L,!0)}else v&&_>wt&&(l.gap=!0);l.setElementaryStreamInfo(u.type,_,x,I,L),this.backtrackFragment&&(this.backtrackFragment=l),this.bufferFragmentData(u,l,c,o,v||E)}else if(v||E)l.gap=!0;else{this.backtrack(l);return}}if(y){let{startPTS:T,endPTS:v,startDTS:E,endDTS:_}=y;c&&(c.elementaryStreams[J.AUDIO]={startPTS:T,endPTS:v,startDTS:E,endDTS:_}),l.setElementaryStreamInfo(J.AUDIO,T,v,E,_),this.bufferFragmentData(y,l,c,o)}if(m&&f!=null&&(t=f.samples)!=null&&t.length){let T={id:i,frag:l,details:m,samples:f.samples};r.trigger(p.FRAG_PARSING_METADATA,T)}if(m&&d){let T={id:i,frag:l,details:m,samples:d.samples};r.trigger(p.FRAG_PARSING_USERDATA,T)}}_bufferInitSegment(e,t,i,r){if(this.state!==w.PARSING)return;this.audioOnly=!!t.audio&&!t.video,this.altAudio&&!this.audioOnly&&delete t.audio;let{audio:n,video:o,audiovideo:a}=t;if(n){let l=e.audioCodec,c=navigator.userAgent.toLowerCase();this.audioCodecSwitch&&(l&&(l.indexOf("mp4a.40.5")!==-1?l="mp4a.40.2":l="mp4a.40.5"),n.metadata.channelCount!==1&&c.indexOf("firefox")===-1&&(l="mp4a.40.5")),l&&l.indexOf("mp4a.40.5")!==-1&&c.indexOf("android")!==-1&&n.container!=="audio/mpeg"&&(l="mp4a.40.2",this.log(`Android: force audio codec to ${l}`)),e.audioCodec&&e.audioCodec!==l&&this.log(`Swapping manifest audio codec "${e.audioCodec}" for "${l}"`),n.levelCodec=l,n.id="main",this.log(`Init audio buffer, container:${n.container}, codecs[selected/level/parsed]=[${l||""}/${e.audioCodec||""}/${n.codec}]`)}o&&(o.levelCodec=e.videoCodec,o.id="main",this.log(`Init video buffer, container:${o.container}, codecs[level/parsed]=[${e.videoCodec||""}/${o.codec}]`)),a&&this.log(`Init audiovideo buffer, container:${a.container}, codecs[level/parsed]=[${e.codecs}/${a.codec}]`),this.hls.trigger(p.BUFFER_CODECS,t),Object.keys(t).forEach(l=>{let h=t[l].initSegment;h!=null&&h.byteLength&&this.hls.trigger(p.BUFFER_APPENDING,{type:l,data:h,frag:i,part:null,chunkMeta:r,parent:i.type})}),this.tickImmediate()}getMainFwdBufferInfo(){return this.getFwdBufferInfo(this.mediaBuffer?this.mediaBuffer:this.media,$.MAIN)}backtrack(e){this.couldBacktrack=!0,this.backtrackFragment=e,this.resetTransmuxer(),this.flushBufferGap(e),this.fragmentTracker.removeFragment(e),this.fragPrevious=null,this.nextLoadPosition=e.start,this.state=w.IDLE}checkFragmentChanged(){let e=this.media,t=null;if(e&&e.readyState>1&&e.seeking===!1){let i=e.currentTime;if(Z.isBuffered(e,i)?t=this.getAppendedFrag(i):Z.isBuffered(e,i+.1)&&(t=this.getAppendedFrag(i+.1)),t){this.backtrackFragment=null;let r=this.fragPlaying,n=t.level;(!r||t.sn!==r.sn||r.level!==n)&&(this.fragPlaying=t,this.hls.trigger(p.FRAG_CHANGED,{frag:t}),(!r||r.level!==n)&&this.hls.trigger(p.LEVEL_SWITCHED,{level:n}))}}}get nextLevel(){let e=this.nextBufferedFrag;return e?e.level:-1}get currentFrag(){let e=this.media;return e?this.fragPlaying||this.getAppendedFrag(e.currentTime):null}get currentProgramDateTime(){let e=this.media;if(e){let t=e.currentTime,i=this.currentFrag;if(i&&M(t)&&M(i.programDateTime)){let r=i.programDateTime+(t-i.start)*1e3;return new Date(r)}}return null}get currentLevel(){let e=this.currentFrag;return e?e.level:-1}get nextBufferedFrag(){let e=this.currentFrag;return e?this.followingBufferedFrag(e):null}get forceStartLoad(){return this._forceStartLoad}},Ze=class s{static get version(){return"1.5.8"}static isMSESupported(){return Wn()}static isSupported(){return Ec()}static getMediaSource(){return $e()}static get Events(){return p}static get ErrorTypes(){return G}static get ErrorDetails(){return b}static get DefaultConfig(){return s.defaultConfig?s.defaultConfig:Vn}static set DefaultConfig(e){s.defaultConfig=e}constructor(e={}){this.config=void 0,this.userConfig=void 0,this.coreComponents=void 0,this.networkControllers=void 0,this.started=!1,this._emitter=new Gs,this._autoLevelCapping=-1,this._maxHdcpLevel=null,this.abrController=void 0,this.bufferController=void 0,this.capLevelController=void 0,this.latencyController=void 0,this.levelController=void 0,this.streamController=void 0,this.audioTrackController=void 0,this.subtitleTrackController=void 0,this.emeController=void 0,this.cmcdController=void 0,this._media=null,this.url=null,this.triggeringException=void 0,ao(e.debug||!1,"Hls instance");let t=this.config=yc(s.DefaultConfig,e);this.userConfig=e,t.progressive&&Tc(t);let{abrController:i,bufferController:r,capLevelController:n,errorController:o,fpsController:a}=t,l=new o(this),c=this.abrController=new i(this),h=this.bufferController=new r(this),u=this.capLevelController=new n(this),d=new a(this),f=new wi(this),g=new ki(this),m=t.contentSteeringController,y=m?new m(this):null,T=this.levelController=new ws(this,y),v=new Gi(this),E=new Cs(this.config),_=this.streamController=new ks(this,v,E);u.setStreamController(_),d.setStreamController(_);let x=[f,T,_];y&&x.splice(1,0,y),this.networkControllers=x;let I=[c,h,u,d,g,v];this.audioTrackController=this.createController(t.audioTrackController,x);let L=t.audioStreamController;L&&x.push(new L(this,v,E)),this.subtitleTrackController=this.createController(t.subtitleTrackController,x);let C=t.subtitleStreamController;C&&x.push(new C(this,v,E)),this.createController(t.timelineController,I),E.emeController=this.emeController=this.createController(t.emeController,I),this.cmcdController=this.createController(t.cmcdController,I),this.latencyController=this.createController(Pi,I),this.coreComponents=I,x.push(l);let k=l.onErrorOut;typeof k=="function"&&this.on(p.ERROR,k,l)}createController(e,t){if(e){let i=new e(this);return t&&t.push(i),i}return null}on(e,t,i=this){this._emitter.on(e,t,i)}once(e,t,i=this){this._emitter.once(e,t,i)}removeAllListeners(e){this._emitter.removeAllListeners(e)}off(e,t,i=this,r){this._emitter.off(e,t,i,r)}listeners(e){return this._emitter.listeners(e)}emit(e,t,i){return this._emitter.emit(e,t,i)}trigger(e,t){if(this.config.debug)return this.emit(e,e,t);try{return this.emit(e,e,t)}catch(i){if(S.error("An internal error happened while handling event "+e+'. Error message: "'+i.message+'". Here is a stacktrace:',i),!this.triggeringException){this.triggeringException=!0;let r=e===p.ERROR;this.trigger(p.ERROR,{type:G.OTHER_ERROR,details:b.INTERNAL_EXCEPTION,fatal:r,event:e,error:i}),this.triggeringException=!1}}return!1}listenerCount(e){return this._emitter.listenerCount(e)}destroy(){S.log("destroy"),this.trigger(p.DESTROYING,void 0),this.detachMedia(),this.removeAllListeners(),this._autoLevelCapping=-1,this.url=null,this.networkControllers.forEach(t=>t.destroy()),this.networkControllers.length=0,this.coreComponents.forEach(t=>t.destroy()),this.coreComponents.length=0;let e=this.config;e.xhrSetup=e.fetchSetup=void 0,this.userConfig=null}attachMedia(e){S.log("attachMedia"),this._media=e,this.trigger(p.MEDIA_ATTACHING,{media:e})}detachMedia(){S.log("detachMedia"),this.trigger(p.MEDIA_DETACHING,void 0),this._media=null}loadSource(e){this.stopLoad();let t=this.media,i=this.url,r=this.url=Ps.buildAbsoluteURL(self.location.href,e,{alwaysNormalize:!0});this._autoLevelCapping=-1,this._maxHdcpLevel=null,S.log(`loadSource:${r}`),t&&i&&(i!==r||this.bufferController.hasSourceTypes())&&(this.detachMedia(),this.attachMedia(t)),this.trigger(p.MANIFEST_LOADING,{url:e})}startLoad(e=-1){S.log(`startLoad(${e})`),this.started=!0,this.networkControllers.forEach(t=>{t.startLoad(e)})}stopLoad(){S.log("stopLoad"),this.started=!1,this.networkControllers.forEach(e=>{e.stopLoad()})}resumeBuffering(){this.started&&this.networkControllers.forEach(e=>{"fragmentLoader"in e&&e.startLoad(-1)})}pauseBuffering(){this.networkControllers.forEach(e=>{"fragmentLoader"in e&&e.stopLoad()})}swapAudioCodec(){S.log("swapAudioCodec"),this.streamController.swapAudioCodec()}recoverMediaError(){S.log("recoverMediaError");let e=this._media;this.detachMedia(),e&&this.attachMedia(e)}removeLevel(e){this.levelController.removeLevel(e)}get levels(){let e=this.levelController.levels;return e||[]}get currentLevel(){return this.streamController.currentLevel}set currentLevel(e){S.log(`set currentLevel:${e}`),this.levelController.manualLevel=e,this.streamController.immediateLevelSwitch()}get nextLevel(){return this.streamController.nextLevel}set nextLevel(e){S.log(`set nextLevel:${e}`),this.levelController.manualLevel=e,this.streamController.nextLevelSwitch()}get loadLevel(){return this.levelController.level}set loadLevel(e){S.log(`set loadLevel:${e}`),this.levelController.manualLevel=e}get nextLoadLevel(){return this.levelController.nextLoadLevel}set nextLoadLevel(e){this.levelController.nextLoadLevel=e}get firstLevel(){return Math.max(this.levelController.firstLevel,this.minAutoLevel)}set firstLevel(e){S.log(`set firstLevel:${e}`),this.levelController.firstLevel=e}get startLevel(){let e=this.levelController.startLevel;return e===-1&&this.abrController.forcedAutoLevel>-1?this.abrController.forcedAutoLevel:e}set startLevel(e){S.log(`set startLevel:${e}`),e!==-1&&(e=Math.max(e,this.minAutoLevel)),this.levelController.startLevel=e}get capLevelToPlayerSize(){return this.config.capLevelToPlayerSize}set capLevelToPlayerSize(e){let t=!!e;t!==this.config.capLevelToPlayerSize&&(t?this.capLevelController.startCapping():(this.capLevelController.stopCapping(),this.autoLevelCapping=-1,this.streamController.nextLevelSwitch()),this.config.capLevelToPlayerSize=t)}get autoLevelCapping(){return this._autoLevelCapping}get bandwidthEstimate(){let{bwEstimator:e}=this.abrController;return e?e.getEstimate():NaN}set bandwidthEstimate(e){this.abrController.resetEstimator(e)}get ttfbEstimate(){let{bwEstimator:e}=this.abrController;return e?e.getEstimateTTFB():NaN}set autoLevelCapping(e){this._autoLevelCapping!==e&&(S.log(`set autoLevelCapping:${e}`),this._autoLevelCapping=e,this.levelController.checkMaxAutoUpdated())}get maxHdcpLevel(){return this._maxHdcpLevel}set maxHdcpLevel(e){ra(e)&&this._maxHdcpLevel!==e&&(this._maxHdcpLevel=e,this.levelController.checkMaxAutoUpdated())}get autoLevelEnabled(){return this.levelController.manualLevel===-1}get manualLevel(){return this.levelController.manualLevel}get minAutoLevel(){let{levels:e,config:{minAutoBitrate:t}}=this;if(!e)return 0;let i=e.length;for(let r=0;r<i;r++)if(e[r].maxBitrate>=t)return r;return 0}get maxAutoLevel(){let{levels:e,autoLevelCapping:t,maxHdcpLevel:i}=this,r;if(t===-1&&e!=null&&e.length?r=e.length-1:r=t,i)for(let n=r;n--;){let o=e[n].attrs["HDCP-LEVEL"];if(o&&o<=i)return n}return r}get firstAutoLevel(){return this.abrController.firstAutoLevel}get nextAutoLevel(){return this.abrController.nextAutoLevel}set nextAutoLevel(e){this.abrController.nextAutoLevel=e}get playingDate(){return this.streamController.currentProgramDateTime}get mainForwardBufferInfo(){return this.streamController.getMainFwdBufferInfo()}setAudioOption(e){var t;return(t=this.audioTrackController)==null?void 0:t.setAudioOption(e)}setSubtitleOption(e){var t;return(t=this.subtitleTrackController)==null||t.setSubtitleOption(e),null}get allAudioTracks(){let e=this.audioTrackController;return e?e.allAudioTracks:[]}get audioTracks(){let e=this.audioTrackController;return e?e.audioTracks:[]}get audioTrack(){let e=this.audioTrackController;return e?e.audioTrack:-1}set audioTrack(e){let t=this.audioTrackController;t&&(t.audioTrack=e)}get allSubtitleTracks(){let e=this.subtitleTrackController;return e?e.allSubtitleTracks:[]}get subtitleTracks(){let e=this.subtitleTrackController;return e?e.subtitleTracks:[]}get subtitleTrack(){let e=this.subtitleTrackController;return e?e.subtitleTrack:-1}get media(){return this._media}set subtitleTrack(e){let t=this.subtitleTrackController;t&&(t.subtitleTrack=e)}get subtitleDisplay(){let e=this.subtitleTrackController;return e?e.subtitleDisplay:!1}set subtitleDisplay(e){let t=this.subtitleTrackController;t&&(t.subtitleDisplay=e)}get lowLatencyMode(){return this.config.lowLatencyMode}set lowLatencyMode(e){this.config.lowLatencyMode=e}get liveSyncPosition(){return this.latencyController.liveSyncPosition}get latency(){return this.latencyController.latency}get maxLatency(){return this.latencyController.maxLatency}get targetLatency(){return this.latencyController.targetLatency}get drift(){return this.latencyController.drift}get forceStartLoad(){return this.streamController.forceStartLoad}};Ze.defaultConfig=void 0;var Lc={data(){return{dp:null,dom:null,observe:null,videoUrl:"",videoSegments:[],videoBarrages:[],videoBarrageConfig:{},videoFlvConfig:{}}},mounted(){document.oncontextmenu=function(){event.returnValue=!1},window.YBBarrage=U,window.Hls=Ze},beforeDestroy(){this.destoryVideo()},methods:{destoryVideo(){this.dp&&(this.dp.stop(),this.dp=null)},readyWatcher(s){s&&(this.dom=document.getElementById("yb-video"+s)),(this.videoUrl||this.videoSegments&&this.videoSegments.length>0)&&this.switchVideo()},srcWatcher(s,e){this.videoUrl=s,this.dom&&s!=e&&(e&&this.destoryVideo(),s&&this.switchVideo())},segmentsWatcher(s,e){this.videoSegments=s,this.dom&&s!=e&&(e&&e.length>0&&this.destoryVideo(),s&&s.length>0&&this.switchVideo())},barragesWatcher(s){this.videoBarrages=s,this.dp&&this.dp.setConfig("barrages",s)},barrageConfigWatcher(s){this.videoBarrageConfig=s,this.dp&&this.dp.setConfig("barrageConfig",s)},flvConfigWatcher(s){this.videoFlvConfig=s,this.dp&&this.dp.setConfig("flvConfig",s)},barrageRefreshWatcher(s){s>-1&&this.dp&&this.dp.refreshBarrage()},fullscreenWatcher(s){this.dp&&(s?this.dp.lanuchFullscreen():this.dp.exitFullscreen())},stateWatcher(s){this.dp&&(s=="play"&&this.dp.play(),s=="pause"&&this.dp.pause(),s=="toggle"&&this.dp.toggle(),s=="reload"&&this.dp.reload(),s=="stop"&&this.destoryVideo())},seekTimeWatcher(s){s>-1&&this.dp&&this.dp.seek(s)},danmuWatcher(s){s&&this.dp&&this.dp.drawBarrage(s)},captureEventWatcher(s){s>-1&&this.dp&&this.dp.capture()},propWatcher(s,e){this.dp&&s&&Object.keys(s).forEach(t=>{s[t]!=e[t]&&this.dp.setConfig(t,s[t])})},switchVideo(){return Ws(this,null,function*(){try{(!this._getScriptDomByDataId("flv")||!this._getScriptDomByDataId("jsbridge"))&&(this.dom.setAttribute("class","yingbing-video yb-video yb-video-first"),yield this._initFLV(),yield this._initJsbridge(),this.dom.setAttribute("data-is-ready",!0)),A&&U&&flvjs&&Ze&&jsBridge&&(this.dp&&this.destoryVideo(),this.dp=new A({container:this.dom,src:this.videoUrl,poster:this.getData("poster"),title:this.getData("title"),formats:this.getData("formats"),controls:this.getData("controls"),autoplay:this.getData("autoplay"),loop:this.getData("loop"),mirror:this.getData("mirror"),preload:this.getData("preload"),settings:this.getData("settings"),duration:this.getData("duration"),muted:this.getData("muted"),progressShow:this.getData("progressShow"),prevBtnShow:this.getData("prevBtnShow"),nextBtnShow:this.getData("nextBtnShow"),playShow:this.getData("playShow"),timeShow:this.getData("timeShow"),volumeShow:this.getData("volumeShow"),settingShow:this.getData("settingShow"),fullscreenShow:this.getData("fullscreenShow"),fullscreen:this.getData("fullscreen"),initialTime:this.getData("initialTime"),volume:this.getData("volume"),playbackRate:this.getData("playbackRate"),longpressPlaybackRate:this.getData("longpressPlaybackRate"),objectFit:this.getData("objectFit"),crossOrigin:this.getData("crossOrigin"),pictureInPicture:this.getData("pictureInPicture"),barrages:this.videoBarrages,barrageGap:this.getData("barrageGap"),barrageShow:this.getData("barrageShow"),barrageConfig:this.videoBarrageConfig,segments:this.videoSegments,flvConfig:this.videoFlvConfig,isLive:this.getData("isLive"),enableLongpressPlaybackRate:this.getData("enableLongpressPlaybackRate"),enableDoubleToggle:this.getData("enableDoubleToggle"),enableBlob:this.getData("enableBlob")}),this.dp.on("canplay",s=>{this.triggerMethod("onCanplay",s)}),this.dp.on("canplaythrough",s=>{this.triggerMethod("onCanplaythrough",s)}),this.dp.on("loadeddata",s=>{this.triggerMethod("onLoadeddata",s)}),this.dp.on("loadedmetadata",s=>{this.triggerMethod("onLoadedmetadata",s)}),this.dp.on("loadstart",s=>{this.triggerMethod("onLoadstart",s)}),this.dp.on("play",s=>{this.triggerMethod("onPlay",s)}),this.dp.on("pause",s=>{this.triggerMethod("onPause",s)}),this.dp.on("ended",s=>{this.triggerMethod("onEnded",s)}),this.dp.on("seeking",s=>{this.triggerMethod("onSeeking",s)}),this.dp.on("seeked",s=>{this.triggerMethod("onSeeked",s)}),this.dp.on("timeupdate",s=>{this.triggerMethod("onTimeupdate",s)}),this.dp.on("waiting",s=>{this.triggerMethod("onWaiting",s)}),this.dp.on("playing",s=>{this.triggerMethod("onPlaying",s)}),this.dp.on("progress",s=>{this.triggerMethod("onProgress",s)}),this.dp.on("abort",s=>{this.triggerMethod("onAbort",s)}),this.dp.on("error",s=>{this.triggerMethod("onErr",s)}),this.dp.on("volumechange",s=>{this.triggerMethod("onVolumechange",s)}),this.dp.on("ratechange",s=>{this.triggerMethod("onRatechange",s)}),this.dp.on("durationchange",s=>{this.triggerMethod("onDurationchange",s)}),this.dp.on("enterpictureinpicture",s=>{this.triggerMethod("onEnterpictureinpicture",s)}),this.dp.on("leavepictureinpicture",s=>{this.triggerMethod("onLeavepictureinpicture",s)}),this.dp.on("fullscreenChange",s=>{this.triggerMethod("fullscreenChange",s)}),this.dp.on("captureFinish",s=>{this.triggerMethod("onCaptureFinish",s.base64)}),this.dp.on("controlsChange",s=>{this.triggerMethod("onControlsChange",s)}),this.dp.on("barrageChange",s=>{this.triggerMethod("onBarrageChange",s)}),this.dp.on("prevBtnClick",s=>{this.triggerMethod("onPrevBtnClick",s)}),this.dp.on("nextBtnClick",s=>{this.triggerMethod("onNextBtnClick",s)}),this.dp.on("seizing",s=>{this.triggerMethod("onSeizing",s)}),this.dp.on("statisticsInfo",s=>{this.triggerMethod("onStatisticsInfo",s)}),this.dp.on("loadingComplete",s=>{this.triggerMethod("onLoadingComplete",s)}),this.dp.on("recoveredEarlyEof",s=>{this.triggerMethod("onRecoveredEarlyEof",s)}),this.dp.on("mediaInfo",s=>{this.triggerMethod("onMediaInfo",s)}),this.dp.on("metadataArrived",s=>{this.triggerMethod("onMetadataArrived",s)}),this.dp.on("scriptdataArrived",s=>{this.triggerMethod("onScriptdataArrived",s)}))}catch(s){let e=document.querySelector(".yb-video-first");e&&(e.getAttribute("data-is-ready")?this.dp||this.switchVideo():(this.observer=new MutationObserver((t,i)=>{t.forEach(r=>{r.attributeName=="data-is-ready"&&(this.observer.disconnect(),this.observer=null,this.dp||this.switchVideo())})}),this.observer.observe(e,{attributes:!0,childList:!1,characterData:!1,attributeOldValue:!1})))}})},getData(s){let e=this.dom.getAttribute("data-"+s);return["true","false"].includes(e)?e!="false":/^[\d]+$/.test(e)?Number(e):e},triggerMethod(s,e){this.$ownerInstance.callMethod(s,e)},_getScriptDomByDataId(s){let e=document.getElementsByTagName("script"),t=null;for(let i=0;i<e.length;i++)if(e[i].getAttribute("data-id")==s){t=e[i];break}return t},_initHLS(){return new Promise(s=>{let e=document.createElement("SCRIPT");e.setAttribute("data-id","hls"),e.src="./uni_modules/yingbing-video/static/hls.min.js",e.onload=()=>{s()},document.body.appendChild(e)})},_initFLV(){return new Promise(s=>{let e=document.createElement("SCRIPT");e.setAttribute("data-id","flv"),e.src="./uni_modules/yingbing-video/static/flv.min.js",e.onload=()=>{s()},document.body.appendChild(e)})},_initJsbridge(){return new Promise(s=>{let e=document.createElement("SCRIPT");e.setAttribute("data-id","jsbridge"),e.src="./uni_modules/yingbing-video/static/jsbridge-mini.js",e.onload=()=>{s()},document.body.appendChild(e)})},_initYBBarrage(){return new Promise(s=>{let e=document.createElement("SCRIPT");e.setAttribute("data-id","ybbarrage"),e.src="./uni_modules/yingbing-video/static/ybbarrage.js",e.onload=()=>{s()},document.body.appendChild(e)})},_initYBPlayer(){return new Promise(s=>{let e=document.createElement("SCRIPT");e.setAttribute("data-id","ybplayer"),e.src="./uni_modules/yingbing-video/static/ybplayer.js",e.onload=()=>{s()},document.body.appendChild(e)})}}};return Jn(_c);})();
