import { IMMessage, TMsgScene } from './MsgServiceInterface';
/**
 * 调用方式:
 * ```js
 * nim.msgExtend.getThreadMsgs(options)
 * ```
 */
export interface MsgExtendServiceInterface {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 查询 消息thread 的所有内容。
     *
     * - 发送消息时，通过设置 `replyMsg` 属性，可以形成消息之间的指向关系。详情请参考: [消息扩展](https://doc.yunxin.163.com/messaging-enhanced/docs/DYzMDAyMjg?platform=web)
     * - 注意该接口传入的参数必须来源于根消息节点
     * - 返回的 msgs[id].threadMessageInfo 的 replyMsg 为父消息节点，msg[id].threadMessageInfo 的 threadMsg 为根消息节点
     *
     * @example
     * ```js
     * // 回复消息
     * nim.msg.sendText({
     *   scene: 'p2p',
     *   to: 'test',
     *   text: 'reply hello world',
     *   replyMsg: msg,
     *   done: (e, r) => console.log(e ? '发送消息失败' : '发送消息成功')
     * })
     *
     * // 输入根消息节点，查询该消息所有的回复消息
     * const res = await nim.msgExtend.getThreadMsgs({
     *   "scene": "p2p",
     *   "threadMsgFromAccount": "zk1",
     *   "threadMsgIdServer": rootMsg.idServer,
     *   "threadMsgTime": rootMsg.time,
     *   "threadMsgToAccount": "zk2",
     *   "limit": 100,
     *   "reverse": false
     * })
     * ```
     * @locale
     *
     * @locale en
     * Get threaded message list. Threaded messages refer to the messages in a threaded conversation.
     * @locale
     *
     */
    getThreadMsgs(options: GetThreadMsgsOptions): Promise<GetThreadMsgsResult>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 通过消息 idServer、from、to、scene、time查询完整消息体。
     *
     * - 常见场景为收到消息后，消息体内有消息对应的 thread 消息简略概述。通过该接口，可以查询到 thread 消息对应的源消息具体内容
     * @example
     * ```js
     * res = await nim.msgExtend.getMsgsByIdServer({
     *     reqMsgs: [
     *         {
     *             from: msg.threadMessageInfo.replyMsgFromAccount,
     *             to: msg.threadMessageInfo.replyMsgToAccount,
     *             scene: msg.scene,
     *             time: msg.threadMessageInfo.replyMsgTime,
     *             idServer: msg.threadMessageInfo.replyMsgIdServer
     *         }
     *     ]
     * })
     * ```
     * @locale
     *
     * @locale en
     * Batch query historical messages through message ID and other information. Dedicated to threaded messages.
     * @locale
     *
     */
    getMsgsByIdServer(options: GetMsgsByIdServerOptions): Promise<IMMessage[]>;
}
export interface GetThreadMsgsOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 场景
     * @locale
     *
     * @locale en
     * Scenes
     * @locale
     */
    scene: TMsgScene;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * thread根消息的from，即发送者账号
     * @locale
     *
     * @locale en
     * The sender of the root message of the threaded messages. The value is the sender's account.
     * @locale
     */
    threadMsgFromAccount: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * thread根消息的to，即接受者账号
     * @locale
     *
     * @locale en
     * The recipient of the root message of the threaded messages. The value is the recipient account.
     * @locale
     */
    threadMsgToAccount: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * thread根消息的 idServer
     * @locale
     *
     * @locale en
     * Message ID (generated by the IM server) of the root message of the threaded messages.
     * @locale
     */
    threadMsgIdServer: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * thread根消息的time
     * @locale
     *
     * @locale en
     * Time of the root message of the threaded messages.
     * @locale
     */
    threadMsgTime: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 时间戳, 开始时间, 精确到ms, 默认为0
     * @locale
     *
     * @locale en
     * Start time (accurate to ms). The default value is 0.
     * @locale
     */
    beginTime?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 时间戳, 结束时间, 精确到ms, 默认为服务器的当前时间
     * @locale
     *
     * @locale en
     * End time (accurate to ms). The default value is the current time of the server
     * @locale
     */
    endTime?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 上次查询的最后一条消息的idServer, 第一次不填
     * @locale
     *
     * @locale en
     * Message ID (generated by the IM server) of the last message that was searched for. Not required for the first time.
     * @locale
     */
    lastMsgId?: string;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 本次查询的消息数量限制, 最多100条, 默认100条
     * @locale
     *
     * @locale en
     * The limit (100) of the number of messages for the current query. The default value is also 100.
     * @locale
     */
    limit?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 默认false表示从 endTime 开始往前查找历史消息，true 表示从 beginTime 开始往后查找历史消息
     * @locale
     *
     * @locale en
     * If the value is false (default), it represents to search historical messages until the endTime ; if true, search historical messages from the beginTime.
     * @locale
     */
    reverse?: boolean;
}
export interface GetThreadMsgsResult {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 相关的消息列表
     *
     * @locale
     *
     * @locale en
     * List of related messages
     * @locale
     */
    msgs: IMMessage[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * thread 根消息
     * @locale
     *
     * @locale en
     * The root message of a threaded conversation, that is, the start message of the conversation.
     * @locale
     */
    threadMsg: IMMessage;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 获取的时间戳
     * @locale
     *
     * @locale en
     * Get timestamp
     * @locale
     */
    timetag: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息总数
     * @locale
     *
     * @locale en
     * Total number of messages
     * @locale
     */
    total: number;
}
export interface GetMsgsByIdServerOptions {
    reqMsgs: {
        scene: TMsgScene;
        from: string;
        to: string;
        idServer: string;
        time: number;
    }[];
}
