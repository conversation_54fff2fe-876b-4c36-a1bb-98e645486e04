<template>
	<custom-header style="height: 88rpx;" title="下发任务" showBack2 />
	<view class="equip_content">
		<view class="de_con">
					<scroll-view 
						class="content" 
						scroll-y 
						@refresherrefresh="onRefresh"
						@refresherpulling="onPulling"
						:refresher-enabled="true"
						:refresher-triggered="isRefreshing"
						refresher-background="#16171b"
						:refresher-threshold="30"
						refresher-default-style="black"
					>
					<view class="search-bar">
						<!-- <up-input
							class="sear_inp"
							v-model="searchText"
							placeholder="请输入任务名称"
							prefixIcon="search"
							:clearable="true"
							@change="handleSearchInput"
							@clear="handleClear"
							color="rgba(255,255,255,0.8)"
							prefixIconStyle="font-size: 22px;color: #909399"
						></up-input> -->
						<!-- <view class="filter_icon" @click="handelSearch">
							<image src="@/static/image/apply/search.png" mode=""></image>
						</view> -->
					</view>

					<view style="height:550px;magin:10rpx;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
						<!-- <view> -->
						<zb-table 
						@sort-change="sortChange" 
						:pullUpLoading="pullUpLoading" 
						:isShowLoadMore="true"
						:highlight="true" 
						:show-header="true" 
						:columns="column" 
						:fit="false"
						v-bind="$attrs"
						:stripe="true" row-key="id" @rowClick="rowClick"
						:border="false" @edit="buttonEdit" :data="data"></zb-table>

					</view>

				</scroll-view>

			
		</view>
	</view>
</template>

<script>

	import downproVue from './slot/downpro.vue';
	import holeVue from './slot/hole.vue';
	import openporeVue from './slot/openpore.vue';
	import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request';
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			downproVue,
			holeVue,
			openporeVue
		},
		
		data() {
			const baseData = {
							deviceCode: "CONSTRUC001",
							deviceName: "1号区域瓦斯抽采计划",
							deviceModel: "瓦斯抽取",
							slot: "矿区A",
							configurationState: "MEICENG003/3号煤层",
							currentState: "2023-12-23",
							creatAt: "2023-12-23",
							updateAt: "2024-12-26",
							creatTim: "2024-12-26",
							updateTim: 100,
							spic: 95,
							people: 95,
							time: "李四",
							desc: 0
						};
						const generateData = () => {
							const repeatedData = [];
							for (let i = 0; i < 10; i++) {
								repeatedData.push({
									id: i,
									...baseData
								})
							}
							return repeatedData;
						}
			return {
				currentTab: 0,
				id: null,
				dateStart:'',
				dateEnd:'',
				show: false,
				prolist: {},
				showDeleteModal: false,
				currentDeleteIndex: -1,
				title: '',
				content: '',
				searchText: '',
				column: [
					// { type:'selection', fixed:true,width:60 },
					{
						name: 'drillSite',
						label: '钻场名称',
						fixed: true,
						width: 120,
						align: 'center',
						emptyString: '--'
					},
					// {
					// 	name: 'taskName',
					// 	label: '任务名称',
					// 	width: 120,
					// 	// sorter: 'custom',
					// 	fixed: false,
					// },
					// {
					// 	name: 'curatorAccount',
					// 	label: '负责人ID',
					// 	width: 120,
					// 	// sorter: true
					// },
					{
						name: 'curatorName',
						label: '负责人',
						// sorter: true
					},
					{
						name: 'status',
						label: '状态',
						type:'first',
						// width:200,
						filters: {
							0: "进行中",
							1: "已完成"
						}
					},
					
					
					{
						name: 'createdAt',
						label: '创建时间',
						// sorter: true,
						fixed: true,
						width:200
					},
					
					{
						name: 'operation',
						type: 'operation',
						label: '操作',
						align: 'center',
						renders: [{
								name: '详情',
								class: 'edit',
								type: "primary",
								align: 'center',
								func: 'edit' // func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
							},

						]
					},
				],
				
				// data: generateData(), //表格数据
				data:[],
				data1: [],
				flag1: true,
				flag2: true,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				isShowLoadMore: true, // 是否显示"加载更多"  
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
				isRefreshing: false,  // 是否正在刷新
				isPulling: false,  // 添加下拉状态
				searchTimer: null, // 添加定时器变量
				total: 0, // 添加total字段
			}
		},
		onLoad(options) {
			this.dateStart = options.dateStart;
			this.dateEnd = options.dateEnd;
			this.handelDrill(); // 添加初始加载
			
			// 添加筛选更新事件监听
			uni.$on('updateSlotList', ({dateStart, dateEnd}) => {
				this.dateStart = dateStart;
				this.dateEnd = dateEnd;
				this.currentPage = 1; // 重置页码
				this.data = []; // 清空现有数据
				this.handelDrill(); // 重新加载数据
			});
		},
		onShow() {
		},
		methods: {

			handleSearchInput(e) {
				// console.log('搜索输入值:', e);
				// 清除之前的定时器
				if(this.searchTimer) {
					clearTimeout(this.searchTimer);
				}
				
				// 设置新的定时器，延迟300ms执行搜索
				this.searchTimer = setTimeout(() => {
					this.searchText = e; // 更新搜索文本
					this.currentPage = 1; // 重置页码
					this.data = []; // 清空数据
					this.isShowLoadMore = true; // 重置加载更多
					this.handelDrill(); // 重新加载数据
				}, 300);
			},

			// 处理清空
			handleClear() {
				this.searchText = '';
				this.currentPage = 1;
				this.data = [];
				this.isShowLoadMore = true;
				this.handelDrill();
			},

			//初始加载调取接口获取数据
			async handelDrill() {
				try {
					let drill_data = {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord: this.searchText,
						dateStart: this.dateStart,
						dateEnd: this.dateEnd
					};
					const res = await Request.post('/drilltask/get_ls', drill_data)

					if (res.status == 0) {
						console.log('返回数据', res);
						// 检查返回的数据结构
						if (res.data && Array.isArray(res.data.items)) {
							this.data = this.currentPage === 1 ? res.data.items : [...this.data, ...res.data.items];
							this.total = res.data.total || 0; // 保存总数
							this.isShowLoadMore = this.data.length < this.total; // 根据总数判断是否还有更多数据
						} else {
							// 如果数据结构不正确，设置为空数组
							this.data = [];
							this.total = 0;
							this.isShowLoadMore = false;
						}
					} else {
						uni.showToast({
							title: res.msg || '加载失败',
							icon: 'none',
							duration: 2000
						});
					}
				} catch (error) {
					console.error('加载待办列表失败:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
					// 发生错误时，确保数据为空数组
					this.data = [];
					this.total = 0;
					this.isShowLoadMore = false;
				}
			},

			change(e) {
				console.log("e:", e);
			},
			sortChange(item, sorterMode, index) {

				console.log('触发排序', item, sorterMode, index)
			},
			//筛选
				handelSearch() {
					uni.navigateTo({
						url: '/pages/apply/components/slot/search'
					})
				},
				async pullUpLoading(done) {
					if (!this.flag1) {
						return done('ok');
					}
					
					this.currentPage++; // 页码加1
					try {
						const res = await Request.post('/drilltask/get_ls', {
							page: this.currentPage,
							perPage: this.perPage,
							keyWord: this.searchText,
							dateStart: this.dateStart,
							dateEnd: this.dateEnd
						});

						if (res.status == 0) {
							if (res.data && Array.isArray(res.data.items)) {
								if (res.data.items.length > 0) {
									this.data = [...this.data, ...res.data.items];
									this.total = res.data.total || 0;
									// 判断是否还有更多数据
									if (this.data.length >= this.total) {
										done('ok');
										this.flag1 = false;
										uni.showToast({
											title: '暂无更多数据',
											icon: 'none',
											duration: 1000
										});
									} else {
										done();
									}
								} else {
									done('ok');
									this.flag1 = false;
								}
							} else {
								done('ok');
								this.flag1 = false;
							}
						} else {
							done();
							uni.showToast({
								title: res.msg || '加载失败',
								icon: 'none',
								duration: 2000
							});
						}
					} catch (error) {
						console.error('加载更多数据失败:', error);
						done();
						uni.showToast({
							title: '网络错误，请稍后重试',
							icon: 'none',
							duration: 2000
						});
					}
				},
				buttonEdit(ite, index) {
					// uni.showToast({
					// 	icon: 'none',
					// 	duration: 3000,
					// 	title: '点击编辑'
					// })
					// console.log(ite, index),
					uni.navigateTo({
						url: `/pages/apply/components/plan/detail?id=${ite.id}`
					})
				},

				rowClick(row, index) {
					// uni.showToast({
					// 	icon: 'none',
					// 	duration: 3000,
					// 	title: '单击某行'
					// })
					// console.log('单击某行', row, index)
					uni.navigateTo({
						url: `/pages/apply/components/plan/detail?id=${row.id}`
					})
				},
				// 添加下拉刷新方法
						async onRefresh() {
							this.isPulling = false;  // 开始刷新时关闭下拉状态
							this.isRefreshing = true;
							
							try {
								// 重置页码和数据
								this.currentPage = 1;
								this.flag1 = true;
								
								// 重新加载数据
								await this.handelDrill();
								
								// 提示刷新成功
								uni.showToast({
									title: '刷新成功',
									icon: 'none',
									duration: 1000
								});
							} catch (error) {
								console.error('刷新失败:', error);
								uni.showToast({
									title: '刷新失败',
									icon: 'none',
									duration: 1000
								});
							} finally {
								// 停止刷新动画
								this.isRefreshing = false;
							}
						},
						
						// 添加下拉事件处理
						onPulling(e) {
							this.isPulling = true;
						},
		},
		// 添加组件销毁时的清理
		beforeDestroy() {
			// 移除事件监听
			uni.$off('updateSlotList');
		},
	}
</script>
<style>
	page {
		background: #16171b;
	}

	.u-modal {
		width: 18rem !important;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}

	.equip_content {

		padding-top: 156rpx;
	}

	.equip_header {
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}

	.header_text {
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}

	.cont {
		display: flex;
		// align-items: center;
		justify-content: center;
	}

	.header_img {
		width: 530rpx;
		height: 244rpx;
	}
.sear_inp {
		border: none;
		padding: 12rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	}
	.search-bar {
			// padding-top: 32rpx;
			margin-bottom: 20rpx;
			display: flex;
			color: rgba(255, 255, 255, 0.8);
		}
		.filter_icon image {
				width: 28rpx;
				height: 28rpx;
		
			}
.sear_inp {
		flex: 1;
		margin-right: 10rpx;
		background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}
	.filter_icon {
			height: 42rpx;
			width: 28rpx;
			padding: 14rpx;
			border-radius: 10rpx;
			background: rgba(255, 255, 255, 0.08);
		}
	.tab_cont {
		// border-radius: 12rpx;
		// padding: 0 32rpx;
		// margin-top: 32rpx;
		// height: 680rpx;
		background: rgba(255, 255, 255, 0.04);
	}

	.tab_border {
		border-radius: 12rpx 12rpx 0 0;
		padding: 0 32rpx;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}

	.de_con {
		padding: 0 32rpx;
		padding-top: 16rpx;
		// background: rgba(255, 255, 255, 0.04);
	}

	.content {
			// padding: 0 32rpx;
			// height: calc(100vh - 188rpx);
			box-sizing: border-box;
			background: #16171b;
			position: relative;
			
			/* 自定义下拉刷新样式 */
			:deep(.uni-scroll-view-refresher) {
				width: 100% !important;
				height: 20px;
				background: #16171b;
				display: flex;
				justify-content: center;
				align-items: center;
				
				/* 隐藏默认图标 */
				.uni-scroll-view-refresher__indicator-box {
					display: none;
				}
				
				/* 自定义文本 */
				.uni-scroll-view-refresher__indicator {
					&::before {
						content: '加载中';
						color: rgba(255, 255, 255, 0.8);
						font-size: 14px;
					}
				}
			}
			
			/* 自定义刷新文本样式 */
			.refresh-text {
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 30px;
				display: flex;
				align-items: center;
				justify-content: center;
				color: rgba(255, 255, 255, 0.8);
				font-size: 14px;
				background: #16171b;
				z-index: 100;
			}
			
			/* 隐藏默认的刷新图标 */
			:deep(.uni-scroll-view-refresher) {
				.uni-scroll-view-refresher__indicator-box {
					opacity: 0;
				}
			}
		}

	.swipe-content {
		display: flex;
		// background: #fff;
		// border-radius: 12rpx;
		// margin-bottom: 16rpx;
		transition: transform 0.3s ease;
	}

	.swipe-button {
		width: 100px;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		color: white;
	}

	.delete {
		background-color: #ff9092;
	}

	.view {
		background-color: #3c9cff;
	}

	.main-content {
		flex: 1;
		background-color: rgba(255, 255, 255, 0.04);
		display: flex;
		padding: 30rpx;
		color: rgba(255, 255, 255, 0.85);
		cursor: pointer;
		user-select: none;
		-webkit-tap-highlight-color: transparent;
	}

	.swipe-content {
		position: relative;
		width: 100%;
		transition: transform 0.3s ease;
	}


	.action-buttons {
		position: absolute;
		right: -200rpx;
		top: 0;
		height: 100%;
		display: flex;
	}

	.swipe-button {
		width: 100rpx;
		height: 100%;
		display: flex;
		justify-content: center;
		align-items: center;
		color: white;
		font-size: 28rpx;
		pointer-events: auto;
	}

	.delete {
		background-color: #ff9092;
	}

	.view {
		background-color: #3c9cff;
	}

	.wait_cont {
		padding: 32rpx;
		// border-radius: 12rpx;
		background: #1f2024;
	}

	.header_flex {
		margin-top: 12rpx;
		margin-right: 138rpx;
		display: flex;
		justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}

	.wait_img {
		margin-right: 16rpx;
	}

	.icon_wh {
		width: 24rpx;
		height: 24rpx;
	}

	.modal-content {
		padding: 16rpx 12rpx;
	}

	.modal-title {
		font-size: 32rpx;
		font-weight: 500;
		color: rgba(0, 0, 0, 0.85);
		text-align: center;
		margin-bottom: 16rpx;
	}

	.modal-desc {
		font-size: 28rpx;
		color: rgba(0, 0, 0, 0.45);
		text-align: center;
	}

	/* 自定义 up-modal 样式 */
	:deep(.up-modal) {
		width: 18rem !important;
		background: #1F2024 !important;
		border-radius: 12rpx !important;
	}

	:deep(.up-modal__content) {
		padding: 0 !important;
	}

	:deep(.up-modal__footer) {
		border-top: 1rpx solid rgba(255, 255, 255, 0.0972) !important;
	}

	:deep(.up-modal__button) {
		background: transparent !important;
		color: rgba(255, 255, 255, 0.85) !important;
		font-size: 32rpx !important;
		height: 100rpx !important;
		line-height: 100rpx !important;
	}

	:deep(.up-modal__button--confirm) {
		color: #FF9092 !important;
		font-weight: 500 !important;
	}

	:deep(.up-modal__button--hover) {
		background-color: rgba(255, 255, 255, 0.04) !important;
	}

	.search-bar {
		// padding-top: 32rpx;
		margin-bottom: 20rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
		
		.sear_inp {
			flex: 1;
			margin-right: 10rpx;
			background: rgba(255, 255, 255, 0.08);
			color: rgba(255, 255, 255, 0.8);
			
			:deep(.up-input__clear) {
				color: rgba(255, 255, 255, 0.45);
			}
		}
	}
</style>