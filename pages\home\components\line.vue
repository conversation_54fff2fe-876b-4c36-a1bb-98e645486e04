<template>
	<view style="height: 750rpx; width: 100%;">
		<l-echart ref="chart"></l-echart>
	</view>
</template>

<script>
	import * as echarts from 'echarts';
	export default {
		props: {
			lineData: {
				type: Array,
				required: true
			}
		},
		data() {
			return {
				option: {
					xAxis: {
						type: 'category',
						data: [],
						axisLabel:{
							color:'rgba(255,255,255,0.85)'
						},
						splitLine: {
							lineStyle: {
								type: 'dashed'
							}
						}
					},
					yAxis: {
						type: 'value',
						axisLabel:{
							color:'rgba(255,255,255,0.85)'
						},
						splitLine: {
							lineStyle: {
								type: 'dashed',
								color:'#4f4f4f'
							}
						}
					},
					series: [{
						data: [],
						type: 'line',
						smooth: true,
						areaStyle: {
							color: 'rgba(0, 123, 255, 0.3)'
						},
						lineStyle: {
							color: '#007BFF'
						}
					}]
				},
				chartInstance: null // 保存图表实例
			}
		},
		watch: {
			lineData: {
				handler(newVal) {
					console.log('接收到新的lineData:', newVal);
					if (this.chartInstance) {
						this.renderChart();
					}
				},
				immediate: true
			}
		},
		mounted() {
			this.initChart();
		},
		methods: {
			initChart() {
				this.$refs.chart.init(echarts, chart => {
					console.log('图表初始化完成');
					this.chartInstance = chart; // 保存图表实例
					this.renderChart();
				});
			},
			renderChart() {
				if (!this.lineData || this.lineData.length === 0) {
					console.log('lineData为空，跳过渲染');
					return;
				}
				const xAxisData = this.lineData.map(item => item.date);
				const seriesData = this.lineData.map(item => item.value);
				console.log('xAxisData:', xAxisData);
				console.log('seriesData:', seriesData);
				
				this.option.xAxis.data = xAxisData;
				this.option.series[0].data = seriesData;
				
				this.chartInstance.setOption(this.option, true);
				console.log('图表已更新');
			}
		}
	}
</script>