/**
 * 
 *   Version: 10.8.30
 * 
 *   Git Hash: 53d9f639411d85eb37f1039804197c286cc2e303
 * 
 *   Created At: 4/24/2025, 7:34:14 AM
 * 
 *   Target: CHATROOM_BROWSER_SDK.js
 *   
 */

!function(t,a){"object"==typeof exports&&"undefined"!=typeof module?a(exports):"function"==typeof define&&define.amd?define(["exports"],a):a((t="undefined"!=typeof globalThis?globalThis:t||self).Chatroom={})}(this,(function(t){"use strict";var a="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function createCommonjsModule(t){var a={exports:{}};return t(a,a.exports),a.exports}var u,_,check=function(t){return t&&t.Math==Math&&t},h=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof a&&a)||function(){return this}()||Function("return this")(),fails=function(t){try{return!!t()}catch(t){return!0}},E=!fails((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),m=Function.prototype,g=m.apply,I=m.call,T="object"==typeof Reflect&&Reflect.apply||(E?I.bind(g):function(){return I.apply(g,arguments)}),N=Function.prototype,M=N.bind,O=N.call,S=E&&M.bind(O,O),R=E?function(t){return t&&S(t)}:function(t){return t&&function(){return O.apply(t,arguments)}},isCallable=function(t){return"function"==typeof t},C=!fails((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),A=Function.prototype.call,b=E?A.bind(A):function(){return A.apply(A,arguments)},k={}.propertyIsEnumerable,L=Object.getOwnPropertyDescriptor,D={f:L&&!k.call({1:2},1)?function propertyIsEnumerable(t){var a=L(this,t);return!!a&&a.enumerable}:k},createPropertyDescriptor=function(t,a){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:a}},V=R({}.toString),w=R("".slice),classofRaw=function(t){return w(V(t),8,-1)},P=Object,x=R("".split),U=fails((function(){return!P("z").propertyIsEnumerable(0)}))?function(t){return"String"==classofRaw(t)?x(t,""):P(t)}:P,isNullOrUndefined=function(t){return null==t},B=TypeError,requireObjectCoercible=function(t){if(isNullOrUndefined(t))throw B("Can't call method on "+t);return t},toIndexedObject=function(t){return U(requireObjectCoercible(t))},G="object"==typeof document&&document.all,H=void 0===G&&void 0!==G?function(t){return"object"==typeof t?null!==t:isCallable(t)||t===G}:function(t){return"object"==typeof t?null!==t:isCallable(t)},Y={},aFunction=function(t){return isCallable(t)?t:void 0},getBuiltIn=function(t,a){return arguments.length<2?aFunction(Y[t])||aFunction(h[t]):Y[t]&&Y[t][a]||h[t]&&h[t][a]},j=R({}.isPrototypeOf),K=getBuiltIn("navigator","userAgent")||"",q=h.process,W=h.Deno,z=q&&q.versions||W&&W.version,$=z&&z.v8;$&&(_=(u=$.split("."))[0]>0&&u[0]<4?1:+(u[0]+u[1])),!_&&K&&(!(u=K.match(/Edge\/(\d+)/))||u[1]>=74)&&(u=K.match(/Chrome\/(\d+)/))&&(_=+u[1]);var X=_,Q=!!Object.getOwnPropertySymbols&&!fails((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&X&&X<41})),J=Q&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,Z=Object,ee=J?function(t){return"symbol"==typeof t}:function(t){var a=getBuiltIn("Symbol");return isCallable(a)&&j(a.prototype,Z(t))},te=String,tryToString=function(t){try{return te(t)}catch(t){return"Object"}},re=TypeError,aCallable=function(t){if(isCallable(t))return t;throw re(tryToString(t)+" is not a function")},getMethod=function(t,a){var u=t[a];return isNullOrUndefined(u)?void 0:aCallable(u)},ne=TypeError,oe=Object.defineProperty,ie="__core-js_shared__",ae=h[ie]||function(t,a){try{oe(h,t,{value:a,configurable:!0,writable:!0})}catch(u){h[t]=a}return a}(ie,{}),se=createCommonjsModule((function(t){(t.exports=function(t,a){return ae[t]||(ae[t]=void 0!==a?a:{})})("versions",[]).push({version:"3.25.0",mode:"pure",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.0/LICENSE",source:"https://github.com/zloirock/core-js"})})),ce=Object,toObject=function(t){return ce(requireObjectCoercible(t))},le=R({}.hasOwnProperty),ue=Object.hasOwn||function hasOwn(t,a){return le(toObject(t),a)},de=0,_e=Math.random(),pe=R(1..toString),uid=function(t){return"Symbol("+(void 0===t?"":t)+")_"+pe(++de+_e,36)},he=se("wks"),fe=h.Symbol,Ee=fe&&fe.for,me=J?fe:fe&&fe.withoutSetter||uid,wellKnownSymbol=function(t){if(!ue(he,t)||!Q&&"string"!=typeof he[t]){var a="Symbol."+t;Q&&ue(fe,t)?he[t]=fe[t]:he[t]=J&&Ee?Ee(a):me(a)}return he[t]},ge=TypeError,Ie=wellKnownSymbol("toPrimitive"),toPrimitive=function(t,a){if(!H(t)||ee(t))return t;var u,_=getMethod(t,Ie);if(_){if(void 0===a&&(a="default"),u=b(_,t,a),!H(u)||ee(u))return u;throw ge("Can't convert object to primitive value")}return void 0===a&&(a="number"),function(t,a){var u,_;if("string"===a&&isCallable(u=t.toString)&&!H(_=b(u,t)))return _;if(isCallable(u=t.valueOf)&&!H(_=b(u,t)))return _;if("string"!==a&&isCallable(u=t.toString)&&!H(_=b(u,t)))return _;throw ne("Can't convert object to primitive value")}(t,a)},toPropertyKey=function(t){var a=toPrimitive(t,"string");return ee(a)?a:a+""},ve=h.document,Te=H(ve)&&H(ve.createElement),documentCreateElement=function(t){return Te?ve.createElement(t):{}},Ne=!C&&!fails((function(){return 7!=Object.defineProperty(documentCreateElement("div"),"a",{get:function(){return 7}}).a})),Me=Object.getOwnPropertyDescriptor,Oe={f:C?Me:function getOwnPropertyDescriptor(t,a){if(t=toIndexedObject(t),a=toPropertyKey(a),Ne)try{return Me(t,a)}catch(t){}if(ue(t,a))return createPropertyDescriptor(!b(D.f,t,a),t[a])}},Se=/#|\.prototype\./,isForced=function(t,a){var u=ye[Re(t)];return u==Ae||u!=Ce&&(isCallable(a)?fails(a):!!a)},Re=isForced.normalize=function(t){return String(t).replace(Se,".").toLowerCase()},ye=isForced.data={},Ce=isForced.NATIVE="N",Ae=isForced.POLYFILL="P",be=isForced,ke=R(R.bind),functionBindContext=function(t,a){return aCallable(t),void 0===a?t:E?ke(t,a):function(){return t.apply(a,arguments)}},Le=C&&fails((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),De=String,Ve=TypeError,anObject=function(t){if(H(t))return t;throw Ve(De(t)+" is not an object")},we=TypeError,Pe=Object.defineProperty,xe=Object.getOwnPropertyDescriptor,Ue="enumerable",Fe="configurable",Be="writable",Ge={f:C?Le?function defineProperty(t,a,u){if(anObject(t),a=toPropertyKey(a),anObject(u),"function"==typeof t&&"prototype"===a&&"value"in u&&Be in u&&!u.writable){var _=xe(t,a);_&&_.writable&&(t[a]=u.value,u={configurable:Fe in u?u.configurable:_.configurable,enumerable:Ue in u?u.enumerable:_.enumerable,writable:!1})}return Pe(t,a,u)}:Pe:function defineProperty(t,a,u){if(anObject(t),a=toPropertyKey(a),anObject(u),Ne)try{return Pe(t,a,u)}catch(t){}if("get"in u||"set"in u)throw we("Accessors not supported");return"value"in u&&(t[a]=u.value),t}},He=C?function(t,a,u){return Ge.f(t,a,createPropertyDescriptor(1,u))}:function(t,a,u){return t[a]=u,t},Ye=Oe.f,wrapConstructor=function(t){var Wrapper=function(a,u,_){if(this instanceof Wrapper){switch(arguments.length){case 0:return new t;case 1:return new t(a);case 2:return new t(a,u)}return new t(a,u,_)}return T(t,this,arguments)};return Wrapper.prototype=t.prototype,Wrapper},_export=function(t,a){var u,_,E,m,g,I,T,N,M=t.target,O=t.global,S=t.stat,C=t.proto,A=O?h:S?h[M]:(h[M]||{}).prototype,b=O?Y:Y[M]||He(Y,M,{})[M],k=b.prototype;for(E in a)u=!be(O?E:M+(S?".":"#")+E,t.forced)&&A&&ue(A,E),g=b[E],u&&(I=t.dontCallGetSet?(N=Ye(A,E))&&N.value:A[E]),m=u&&I?I:a[E],u&&typeof g==typeof m||(T=t.bind&&u?functionBindContext(m,h):t.wrap&&u?wrapConstructor(m):C&&isCallable(m)?R(m):m,(t.sham||m&&m.sham||g&&g.sham)&&He(T,"sham",!0),He(b,E,T),C&&(ue(Y,_=M+"Prototype")||He(Y,_,{}),He(Y[_],E,m),t.real&&k&&!k[E]&&He(k,E,m)))},je=Ge.f;_export({target:"Object",stat:!0,forced:Object.defineProperty!==je,sham:!C},{defineProperty:je});var Ke,qe=createCommonjsModule((function(t){var a=Y.Object,u=t.exports=function defineProperty(t,u,_){return a.defineProperty(t,u,_)};a.defineProperty.sham&&(u.sham=!0)})),We=qe,ze=getDefaultExportFromCjs(createCommonjsModule((function(t){function _defineProperties(t,a){for(var u=0;u<a.length;u++){var _=a[u];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),We(t,_.key,_)}}t.exports=function _createClass(t,a,u){return a&&_defineProperties(t.prototype,a),u&&_defineProperties(t,u),We(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports}))),$e=getDefaultExportFromCjs(createCommonjsModule((function(t){t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports}))),Xe=Math.ceil,Qe=Math.floor,Je=Math.trunc||function trunc(t){var a=+t;return(a>0?Qe:Xe)(a)},toIntegerOrInfinity=function(t){var a=+t;return a!=a||0===a?0:Je(a)},Ze=Math.max,et=Math.min,toAbsoluteIndex=function(t,a){var u=toIntegerOrInfinity(t);return u<0?Ze(u+a,0):et(u,a)},tt=Math.min,lengthOfArrayLike=function(t){return(a=t.length)>0?tt(toIntegerOrInfinity(a),9007199254740991):0;var a},createMethod$5=function(t){return function(a,u,_){var h,E=toIndexedObject(a),m=lengthOfArrayLike(E),g=toAbsoluteIndex(_,m);if(t&&u!=u){for(;m>g;)if((h=E[g++])!=h)return!0}else for(;m>g;g++)if((t||g in E)&&E[g]===u)return t||g||0;return!t&&-1}},rt={includes:createMethod$5(!0),indexOf:createMethod$5(!1)},nt={},ot=rt.indexOf,it=R([].push),objectKeysInternal=function(t,a){var u,_=toIndexedObject(t),h=0,E=[];for(u in _)!ue(nt,u)&&ue(_,u)&&it(E,u);for(;a.length>h;)ue(_,u=a[h++])&&(~ot(E,u)||it(E,u));return E},at=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],st=Object.keys||function keys(t){return objectKeysInternal(t,at)},ct=C&&!Le?Object.defineProperties:function defineProperties(t,a){anObject(t);for(var u,_=toIndexedObject(a),h=st(a),E=h.length,m=0;E>m;)Ge.f(t,u=h[m++],_[u]);return t},lt={f:ct},ut=getBuiltIn("document","documentElement"),dt=se("keys"),sharedKey=function(t){return dt[t]||(dt[t]=uid(t))},_t=sharedKey("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<script>"+t+"</"+"script>"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag("")),t.close();var a=t.parentWindow.Object;return t=null,a},NullProtoObject=function(){try{Ke=new ActiveXObject("htmlfile")}catch(t){}var t,a;NullProtoObject="undefined"!=typeof document?document.domain&&Ke?NullProtoObjectViaActiveX(Ke):((a=documentCreateElement("iframe")).style.display="none",ut.appendChild(a),a.src=String("javascript:"),(t=a.contentWindow.document).open(),t.write(scriptTag("document.F=Object")),t.close(),t.F):NullProtoObjectViaActiveX(Ke);for(var u=at.length;u--;)delete NullProtoObject.prototype[at[u]];return NullProtoObject()};nt[_t]=!0;var pt=Object.create||function create(t,a){var u;return null!==t?(EmptyConstructor.prototype=anObject(t),u=new EmptyConstructor,EmptyConstructor.prototype=null,u[_t]=t):u=NullProtoObject(),void 0===a?u:lt.f(u,a)};_export({target:"Object",stat:!0,sham:!C},{create:pt});var ht=Y.Object,ft=function create(t,a){return ht.create(t,a)},Et=String,mt=TypeError,gt=Object.setPrototypeOf||("__proto__"in{}?function(){var t,a=!1,u={};try{(t=R(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(u,[]),a=u instanceof Array}catch(t){}return function setPrototypeOf(u,_){return anObject(u),function(t){if("object"==typeof t||isCallable(t))return t;throw mt("Can't set "+Et(t)+" as a prototype")}(_),a?t(u,_):u.__proto__=_,u}}():void 0);_export({target:"Object",stat:!0},{setPrototypeOf:gt});var It=Y.Object.setPrototypeOf,vt=R([].slice),Tt=Function,Nt=R([].concat),Mt=R([].join),Ot={},construct$8=function(t,a,u){if(!ue(Ot,a)){for(var _=[],h=0;h<a;h++)_[h]="a["+h+"]";Ot[a]=Tt("C,a","return new C("+Mt(_,",")+")")}return Ot[a](t,u)},St=E?Tt.bind:function bind(t){var a=aCallable(this),u=a.prototype,_=vt(arguments,1),h=function bound(){var u=Nt(_,vt(arguments));return this instanceof h?construct$8(a,u.length,u):a.apply(t,u)};return H(u)&&(h.prototype=u),h};_export({target:"Function",proto:!0,forced:Function.bind!==St},{bind:St});var entryVirtual=function(t){return Y[t+"Prototype"]},Rt=entryVirtual("Function").bind,yt=Function.prototype,bind$1=function(t){var a=t.bind;return t===yt||j(yt,t)&&a===yt.bind?Rt:a},Ct=createCommonjsModule((function(t){function _setPrototypeOf(a,u){var _;return t.exports=_setPrototypeOf=It?bind$1(_=It).call(_):function _setPrototypeOf(t,a){return t.__proto__=a,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(a,u)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports})),At=getDefaultExportFromCjs(createCommonjsModule((function(t){t.exports=function _inheritsLoose(t,a){t.prototype=ft(a.prototype),t.prototype.constructor=t,Ct(t,a)},t.exports.__esModule=!0,t.exports.default=t.exports}))),bt=Array.isArray||function isArray(t){return"Array"==classofRaw(t)},kt=TypeError,doesNotExceedSafeInteger=function(t){if(t>9007199254740991)throw kt("Maximum allowed index exceeded");return t},createProperty=function(t,a,u){var _=toPropertyKey(a);_ in t?Ge.f(t,_,createPropertyDescriptor(0,u)):t[_]=u},Lt={};Lt[wellKnownSymbol("toStringTag")]="z";var Dt="[object z]"===String(Lt),Vt=wellKnownSymbol("toStringTag"),wt=Object,Pt="Arguments"==classofRaw(function(){return arguments}()),xt=Dt?classofRaw:function(t){var a,u,_;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(u=function(t,a){try{return t[a]}catch(t){}}(a=wt(t),Vt))?u:Pt?classofRaw(a):"Object"==(_=classofRaw(a))&&isCallable(a.callee)?"Arguments":_},Ut=R(Function.toString);isCallable(ae.inspectSource)||(ae.inspectSource=function(t){return Ut(t)});var Ft=ae.inspectSource,noop=function(){},Bt=[],Gt=getBuiltIn("Reflect","construct"),Ht=/^\s*(?:class|function)\b/,Yt=R(Ht.exec),jt=!Ht.exec(noop),Kt=function isConstructor(t){if(!isCallable(t))return!1;try{return Gt(noop,Bt,t),!0}catch(t){return!1}},qt=function isConstructor(t){if(!isCallable(t))return!1;switch(xt(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return jt||!!Yt(Ht,Ft(t))}catch(t){return!0}};qt.sham=!0;var Wt=!Gt||fails((function(){var t;return Kt(Kt.call)||!Kt(Object)||!Kt((function(){t=!0}))||t}))?qt:Kt,zt=wellKnownSymbol("species"),$t=Array,arraySpeciesCreate=function(t,a){return new(function(t){var a;return bt(t)&&(a=t.constructor,(Wt(a)&&(a===$t||bt(a.prototype))||H(a)&&null===(a=a[zt]))&&(a=void 0)),void 0===a?$t:a}(t))(0===a?0:a)},Xt=wellKnownSymbol("species"),arrayMethodHasSpeciesSupport=function(t){return X>=51||!fails((function(){var a=[];return(a.constructor={})[Xt]=function(){return{foo:1}},1!==a[t](Boolean).foo}))},Qt=wellKnownSymbol("isConcatSpreadable"),Jt=X>=51||!fails((function(){var t=[];return t[Qt]=!1,t.concat()[0]!==t})),Zt=arrayMethodHasSpeciesSupport("concat"),isConcatSpreadable=function(t){if(!H(t))return!1;var a=t[Qt];return void 0!==a?!!a:bt(t)};_export({target:"Array",proto:!0,arity:1,forced:!Jt||!Zt},{concat:function concat(t){var a,u,_,h,E,m=toObject(this),g=arraySpeciesCreate(m,0),I=0;for(a=-1,_=arguments.length;a<_;a++)if(isConcatSpreadable(E=-1===a?m:arguments[a]))for(h=lengthOfArrayLike(E),doesNotExceedSafeInteger(I+h),u=0;u<h;u++,I++)u in E&&createProperty(g,I,E[u]);else doesNotExceedSafeInteger(I+1),createProperty(g,I++,E);return g.length=I,g}});var er,tr,rr,nr=String,toString=function(t){if("Symbol"===xt(t))throw TypeError("Cannot convert a Symbol value to a string");return nr(t)},or=at.concat("length","prototype"),ir={f:Object.getOwnPropertyNames||function getOwnPropertyNames(t){return objectKeysInternal(t,or)}},ar=Array,sr=Math.max,arraySliceSimple=function(t,a,u){for(var _=lengthOfArrayLike(t),h=toAbsoluteIndex(a,_),E=toAbsoluteIndex(void 0===u?_:u,_),m=ar(sr(E-h,0)),g=0;h<E;h++,g++)createProperty(m,g,t[h]);return m.length=g,m},cr=ir.f,lr="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],ur={f:function getOwnPropertyNames(t){return lr&&"Window"==classofRaw(t)?function(t){try{return cr(t)}catch(t){return arraySliceSimple(lr)}}(t):cr(toIndexedObject(t))}},dr={f:Object.getOwnPropertySymbols},defineBuiltIn=function(t,a,u,_){return _&&_.enumerable?t[a]=u:He(t,a,u),t},_r={f:wellKnownSymbol},pr=Ge.f,wellKnownSymbolDefine=function(t){var a=Y.Symbol||(Y.Symbol={});ue(a,t)||pr(a,t,{value:_r.f(t)})},symbolDefineToPrimitive=function(){var t=getBuiltIn("Symbol"),a=t&&t.prototype,u=a&&a.valueOf,_=wellKnownSymbol("toPrimitive");a&&!a[_]&&defineBuiltIn(a,_,(function(t){return b(u,this)}),{arity:1})},hr=Dt?{}.toString:function toString(){return"[object "+xt(this)+"]"},fr=Ge.f,Er=wellKnownSymbol("toStringTag"),setToStringTag=function(t,a,u,_){if(t){var h=u?t:t.prototype;ue(h,Er)||fr(h,Er,{configurable:!0,value:a}),_&&!Dt&&He(h,"toString",hr)}},mr=h.WeakMap,gr=isCallable(mr)&&/native code/.test(String(mr)),Ir="Object already initialized",vr=h.TypeError,Tr=h.WeakMap;if(gr||ae.state){var Nr=ae.state||(ae.state=new Tr),Mr=R(Nr.get),Or=R(Nr.has),Sr=R(Nr.set);er=function(t,a){if(Or(Nr,t))throw vr(Ir);return a.facade=t,Sr(Nr,t,a),a},tr=function(t){return Mr(Nr,t)||{}},rr=function(t){return Or(Nr,t)}}else{var Rr=sharedKey("state");nt[Rr]=!0,er=function(t,a){if(ue(t,Rr))throw vr(Ir);return a.facade=t,He(t,Rr,a),a},tr=function(t){return ue(t,Rr)?t[Rr]:{}},rr=function(t){return ue(t,Rr)}}var yr={set:er,get:tr,has:rr,enforce:function(t){return rr(t)?tr(t):er(t,{})},getterFor:function(t){return function(a){var u;if(!H(a)||(u=tr(a)).type!==t)throw vr("Incompatible receiver, "+t+" required");return u}}},Cr=R([].push),createMethod$4=function(t){var a=1==t,u=2==t,_=3==t,h=4==t,E=6==t,m=7==t,g=5==t||E;return function(I,T,N,M){for(var O,S,R=toObject(I),C=U(R),A=functionBindContext(T,N),b=lengthOfArrayLike(C),k=0,L=M||arraySpeciesCreate,D=a?L(I,b):u||m?L(I,0):void 0;b>k;k++)if((g||k in C)&&(S=A(O=C[k],k,R),t))if(a)D[k]=S;else if(S)switch(t){case 3:return!0;case 5:return O;case 6:return k;case 2:Cr(D,O)}else switch(t){case 4:return!1;case 7:Cr(D,O)}return E?-1:_||h?h:D}},Ar={forEach:createMethod$4(0),map:createMethod$4(1),filter:createMethod$4(2),some:createMethod$4(3),every:createMethod$4(4),find:createMethod$4(5),findIndex:createMethod$4(6),filterReject:createMethod$4(7)},br=Ar.forEach,kr=sharedKey("hidden"),Lr="Symbol",Dr=yr.set,Vr=yr.getterFor(Lr),wr=Object.prototype,Pr=h.Symbol,xr=Pr&&Pr.prototype,Ur=h.TypeError,Fr=h.QObject,Br=Oe.f,Gr=Ge.f,Hr=ur.f,Yr=D.f,jr=R([].push),Kr=se("symbols"),qr=se("op-symbols"),Wr=se("wks"),zr=!Fr||!Fr.prototype||!Fr.prototype.findChild,$r=C&&fails((function(){return 7!=pt(Gr({},"a",{get:function(){return Gr(this,"a",{value:7}).a}})).a}))?function(t,a,u){var _=Br(wr,a);_&&delete wr[a],Gr(t,a,u),_&&t!==wr&&Gr(wr,a,_)}:Gr,wrap$1=function(t,a){var u=Kr[t]=pt(xr);return Dr(u,{type:Lr,tag:t,description:a}),C||(u.description=a),u},Xr=function defineProperty(t,a,u){t===wr&&Xr(qr,a,u),anObject(t);var _=toPropertyKey(a);return anObject(u),ue(Kr,_)?(u.enumerable?(ue(t,kr)&&t[kr][_]&&(t[kr][_]=!1),u=pt(u,{enumerable:createPropertyDescriptor(0,!1)})):(ue(t,kr)||Gr(t,kr,createPropertyDescriptor(1,{})),t[kr][_]=!0),$r(t,_,u)):Gr(t,_,u)},Qr=function defineProperties(t,a){anObject(t);var u=toIndexedObject(a),_=st(u).concat($getOwnPropertySymbols(u));return br(_,(function(a){C&&!b(Jr,u,a)||Xr(t,a,u[a])})),t},Jr=function propertyIsEnumerable(t){var a=toPropertyKey(t),u=b(Yr,this,a);return!(this===wr&&ue(Kr,a)&&!ue(qr,a))&&(!(u||!ue(this,a)||!ue(Kr,a)||ue(this,kr)&&this[kr][a])||u)},Zr=function getOwnPropertyDescriptor(t,a){var u=toIndexedObject(t),_=toPropertyKey(a);if(u!==wr||!ue(Kr,_)||ue(qr,_)){var h=Br(u,_);return!h||!ue(Kr,_)||ue(u,kr)&&u[kr][_]||(h.enumerable=!0),h}},en=function getOwnPropertyNames(t){var a=Hr(toIndexedObject(t)),u=[];return br(a,(function(t){ue(Kr,t)||ue(nt,t)||jr(u,t)})),u},$getOwnPropertySymbols=function(t){var a=t===wr,u=Hr(a?qr:toIndexedObject(t)),_=[];return br(u,(function(t){!ue(Kr,t)||a&&!ue(wr,t)||jr(_,Kr[t])})),_};Q||(Pr=function Symbol(){if(j(xr,this))throw Ur("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?toString(arguments[0]):void 0,a=uid(t),setter=function(t){this===wr&&b(setter,qr,t),ue(this,kr)&&ue(this[kr],a)&&(this[kr][a]=!1),$r(this,a,createPropertyDescriptor(1,t))};return C&&zr&&$r(wr,a,{configurable:!0,set:setter}),wrap$1(a,t)},xr=Pr.prototype,defineBuiltIn(xr,"toString",(function toString(){return Vr(this).tag})),defineBuiltIn(Pr,"withoutSetter",(function(t){return wrap$1(uid(t),t)})),D.f=Jr,Ge.f=Xr,lt.f=Qr,Oe.f=Zr,ir.f=ur.f=en,dr.f=$getOwnPropertySymbols,_r.f=function(t){return wrap$1(wellKnownSymbol(t),t)},C&&Gr(xr,"description",{configurable:!0,get:function description(){return Vr(this).description}})),_export({global:!0,constructor:!0,wrap:!0,forced:!Q,sham:!Q},{Symbol:Pr}),br(st(Wr),(function(t){wellKnownSymbolDefine(t)})),_export({target:Lr,stat:!0,forced:!Q},{useSetter:function(){zr=!0},useSimple:function(){zr=!1}}),_export({target:"Object",stat:!0,forced:!Q,sham:!C},{create:function create(t,a){return void 0===a?pt(t):Qr(pt(t),a)},defineProperty:Xr,defineProperties:Qr,getOwnPropertyDescriptor:Zr}),_export({target:"Object",stat:!0,forced:!Q},{getOwnPropertyNames:en}),symbolDefineToPrimitive(),setToStringTag(Pr,Lr),nt[kr]=!0;var tn=Q&&!!Symbol.for&&!!Symbol.keyFor,rn=se("string-to-symbol-registry"),nn=se("symbol-to-string-registry");_export({target:"Symbol",stat:!0,forced:!tn},{for:function(t){var a=toString(t);if(ue(rn,a))return rn[a];var u=getBuiltIn("Symbol")(a);return rn[a]=u,nn[u]=a,u}});var an=se("symbol-to-string-registry");_export({target:"Symbol",stat:!0,forced:!tn},{keyFor:function keyFor(t){if(!ee(t))throw TypeError(tryToString(t)+" is not a symbol");if(ue(an,t))return an[t]}});var sn=getBuiltIn("JSON","stringify"),cn=R(/./.exec),ln=R("".charAt),un=R("".charCodeAt),dn=R("".replace),_n=R(1..toString),pn=/[\uD800-\uDFFF]/g,hn=/^[\uD800-\uDBFF]$/,fn=/^[\uDC00-\uDFFF]$/,En=!Q||fails((function(){var t=getBuiltIn("Symbol")();return"[null]"!=sn([t])||"{}"!=sn({a:t})||"{}"!=sn(Object(t))})),mn=fails((function(){return'"\\udf06\\ud834"'!==sn("\udf06\ud834")||'"\\udead"'!==sn("\udead")})),stringifyWithSymbolsFix=function(t,a){var u=vt(arguments),_=a;if((H(a)||void 0!==t)&&!ee(t))return bt(a)||(a=function(t,a){if(isCallable(_)&&(a=b(_,this,t,a)),!ee(a))return a}),u[1]=a,T(sn,null,u)},fixIllFormed=function(t,a,u){var _=ln(u,a-1),h=ln(u,a+1);return cn(hn,t)&&!cn(fn,h)||cn(fn,t)&&!cn(hn,_)?"\\u"+_n(un(t,0),16):t};sn&&_export({target:"JSON",stat:!0,arity:3,forced:En||mn},{stringify:function stringify(t,a,u){var _=vt(arguments),h=T(En?stringifyWithSymbolsFix:sn,null,_);return mn&&"string"==typeof h?dn(h,pn,fixIllFormed):h}});var gn=!Q||fails((function(){dr.f(1)}));_export({target:"Object",stat:!0,forced:gn},{getOwnPropertySymbols:function getOwnPropertySymbols(t){var a=dr.f;return a?a(toObject(t)):[]}}),wellKnownSymbolDefine("asyncIterator"),wellKnownSymbolDefine("hasInstance"),wellKnownSymbolDefine("isConcatSpreadable"),wellKnownSymbolDefine("iterator"),wellKnownSymbolDefine("match"),wellKnownSymbolDefine("matchAll"),wellKnownSymbolDefine("replace"),wellKnownSymbolDefine("search"),wellKnownSymbolDefine("species"),wellKnownSymbolDefine("split"),wellKnownSymbolDefine("toPrimitive"),symbolDefineToPrimitive(),wellKnownSymbolDefine("toStringTag"),setToStringTag(getBuiltIn("Symbol"),"Symbol"),wellKnownSymbolDefine("unscopables"),setToStringTag(h.JSON,"JSON",!0);var In,vn,Tn,Nn=Y.Symbol,Mn={},On=Function.prototype,Sn=C&&Object.getOwnPropertyDescriptor,Rn=ue(On,"name"),yn={EXISTS:Rn,PROPER:Rn&&"something"===function something(){}.name,CONFIGURABLE:Rn&&(!C||C&&Sn(On,"name").configurable)},Cn=!fails((function(){function F(){}return F.prototype.constructor=null,Object.getPrototypeOf(new F)!==F.prototype})),An=sharedKey("IE_PROTO"),bn=Object,kn=bn.prototype,Ln=Cn?bn.getPrototypeOf:function(t){var a=toObject(t);if(ue(a,An))return a[An];var u=a.constructor;return isCallable(u)&&a instanceof u?u.prototype:a instanceof bn?kn:null},Dn=wellKnownSymbol("iterator"),Vn=!1;[].keys&&("next"in(Tn=[].keys())?(vn=Ln(Ln(Tn)))!==Object.prototype&&(In=vn):Vn=!0);var wn=!H(In)||fails((function(){var t={};return In[Dn].call(t)!==t}));In=wn?{}:pt(In),isCallable(In[Dn])||defineBuiltIn(In,Dn,(function(){return this}));var Pn={IteratorPrototype:In,BUGGY_SAFARI_ITERATORS:Vn},xn=Pn.IteratorPrototype,returnThis$1=function(){return this},Un=yn.PROPER,Fn=Pn.BUGGY_SAFARI_ITERATORS,Bn=wellKnownSymbol("iterator"),Gn="keys",Hn="values",Yn="entries",returnThis=function(){return this},iteratorDefine=function(t,a,u,_,h,E,m){!function(t,a,u,_){var h=a+" Iterator";t.prototype=pt(xn,{next:createPropertyDescriptor(+!_,u)}),setToStringTag(t,h,!1,!0),Mn[h]=returnThis$1}(u,a,_);var g,I,T,getIterationMethod=function(t){if(t===h&&R)return R;if(!Fn&&t in O)return O[t];switch(t){case Gn:return function keys(){return new u(this,t)};case Hn:return function values(){return new u(this,t)};case Yn:return function entries(){return new u(this,t)}}return function(){return new u(this)}},N=a+" Iterator",M=!1,O=t.prototype,S=O[Bn]||O["@@iterator"]||h&&O[h],R=!Fn&&S||getIterationMethod(h),C="Array"==a&&O.entries||S;if(C&&(g=Ln(C.call(new t)))!==Object.prototype&&g.next&&(setToStringTag(g,N,!0,!0),Mn[N]=returnThis),Un&&h==Hn&&S&&S.name!==Hn&&(M=!0,R=function values(){return b(S,this)}),h)if(I={values:getIterationMethod(Hn),keys:E?R:getIterationMethod(Gn),entries:getIterationMethod(Yn)},m)for(T in I)(Fn||M||!(T in O))&&defineBuiltIn(O,T,I[T]);else _export({target:a,proto:!0,forced:Fn||M},I);return m&&O[Bn]!==R&&defineBuiltIn(O,Bn,R,{name:h}),Mn[a]=R,I};Ge.f;var jn="Array Iterator",Kn=yr.set,qn=yr.getterFor(jn);iteratorDefine(Array,"Array",(function(t,a){Kn(this,{type:jn,target:toIndexedObject(t),index:0,kind:a})}),(function(){var t=qn(this),a=t.target,u=t.kind,_=t.index++;return!a||_>=a.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==u?{value:_,done:!1}:"values"==u?{value:a[_],done:!1}:{value:[_,a[_]],done:!1}}),"values"),Mn.Arguments=Mn.Array;var Wn=wellKnownSymbol("toStringTag");for(var zn in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var $n=h[zn],Xn=$n&&$n.prototype;Xn&&xt(Xn)!==Wn&&He(Xn,Wn,zn),Mn[zn]=Mn.Array}var Qn=Nn;wellKnownSymbolDefine("asyncDispose"),wellKnownSymbolDefine("dispose"),wellKnownSymbolDefine("matcher"),wellKnownSymbolDefine("metadataKey"),wellKnownSymbolDefine("observable"),wellKnownSymbolDefine("metadata"),wellKnownSymbolDefine("patternMatch"),wellKnownSymbolDefine("replaceAll");var Jn=Qn,Zn=R("".charAt),eo=R("".charCodeAt),to=R("".slice),createMethod$3=function(t){return function(a,u){var _,h,E=toString(requireObjectCoercible(a)),m=toIntegerOrInfinity(u),g=E.length;return m<0||m>=g?t?"":void 0:(_=eo(E,m))<55296||_>56319||m+1===g||(h=eo(E,m+1))<56320||h>57343?t?Zn(E,m):_:t?to(E,m,m+2):h-56320+(_-55296<<10)+65536}},ro={codeAt:createMethod$3(!1),charAt:createMethod$3(!0)},no=ro.charAt,oo="String Iterator",io=yr.set,ao=yr.getterFor(oo);iteratorDefine(String,"String",(function(t){io(this,{type:oo,string:toString(t),index:0})}),(function next(){var t,a=ao(this),u=a.string,_=a.index;return _>=u.length?{value:void 0,done:!0}:(t=no(u,_),a.index+=t.length,{value:t,done:!1})}));var so=_r.f("iterator"),co=createCommonjsModule((function(t){function _typeof(a){return t.exports=_typeof="function"==typeof Jn&&"symbol"==typeof so?function(t){return typeof t}:function(t){return t&&"function"==typeof Jn&&t.constructor===Jn&&t!==Jn.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(a)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports})),lo=fails((function(){Ln(1)}));_export({target:"Object",stat:!0,forced:lo,sham:!Cn},{getPrototypeOf:function getPrototypeOf(t){return Ln(toObject(t))}});var uo=Y.Object.getPrototypeOf,arrayMethodIsStrict=function(t,a){var u=[][t];return!!u&&fails((function(){u.call(null,a||function(){return 1},1)}))},_o=Ar.forEach,po=arrayMethodIsStrict("forEach")?[].forEach:function forEach(t){return _o(this,t,arguments.length>1?arguments[1]:void 0)};_export({target:"Array",proto:!0,forced:[].forEach!=po},{forEach:po});var ho=entryVirtual("Array").forEach,fo=Array.prototype,Eo={DOMTokenList:!0,NodeList:!0},forEach$1=function(t){var a=t.forEach;return t===fo||j(fo,t)&&a===fo.forEach||ue(Eo,xt(t))?ho:a},mo=R([].concat),go=getBuiltIn("Reflect","ownKeys")||function ownKeys(t){var a=ir.f(anObject(t)),u=dr.f;return u?mo(a,u(t)):a},Io=Error,vo=R("".replace),To=String(Io("zxcasd").stack),No=/\n\s*at [^:]*:[^\n]*/,Mo=No.test(To),errorStackClear=function(t,a){if(Mo&&"string"==typeof t&&!Io.prepareStackTrace)for(;a--;)t=vo(t,No,"");return t},installErrorCause=function(t,a){H(a)&&"cause"in a&&He(t,"cause",a.cause)},Oo=wellKnownSymbol("iterator"),So=Array.prototype,isArrayIteratorMethod=function(t){return void 0!==t&&(Mn.Array===t||So[Oo]===t)},Ro=wellKnownSymbol("iterator"),getIteratorMethod$5=function(t){if(!isNullOrUndefined(t))return getMethod(t,Ro)||getMethod(t,"@@iterator")||Mn[xt(t)]},yo=TypeError,getIterator=function(t,a){var u=arguments.length<2?getIteratorMethod$5(t):a;if(aCallable(u))return anObject(b(u,t));throw yo(tryToString(t)+" is not iterable")},iteratorClose=function(t,a,u){var _,h;anObject(t);try{if(!(_=getMethod(t,"return"))){if("throw"===a)throw u;return u}_=b(_,t)}catch(t){h=!0,_=t}if("throw"===a)throw u;if(h)throw _;return anObject(_),u},Co=TypeError,Result=function(t,a){this.stopped=t,this.result=a},Ao=Result.prototype,iterate=function(t,a,u){var _,h,E,m,g,I,T,N=u&&u.that,M=!(!u||!u.AS_ENTRIES),O=!(!u||!u.IS_RECORD),S=!(!u||!u.IS_ITERATOR),R=!(!u||!u.INTERRUPTED),C=functionBindContext(a,N),stop=function(t){return _&&iteratorClose(_,"normal",t),new Result(!0,t)},callFn=function(t){return M?(anObject(t),R?C(t[0],t[1],stop):C(t[0],t[1])):R?C(t,stop):C(t)};if(O)_=t.iterator;else if(S)_=t;else{if(!(h=getIteratorMethod$5(t)))throw Co(tryToString(t)+" is not iterable");if(isArrayIteratorMethod(h)){for(E=0,m=lengthOfArrayLike(t);m>E;E++)if((g=callFn(t[E]))&&j(Ao,g))return g;return new Result(!1)}_=getIterator(t,h)}for(I=O?t.next:_.next;!(T=b(I,_)).done;){try{g=callFn(T.value)}catch(t){iteratorClose(_,"throw",t)}if("object"==typeof g&&g&&j(Ao,g))return g}return new Result(!1)},normalizeStringArgument=function(t,a){return void 0===t?arguments.length<2?"":a:toString(t)},bo=!fails((function(){var t=Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",createPropertyDescriptor(1,7)),7!==t.stack)})),ko=wellKnownSymbol("toStringTag"),Lo=Error,Do=[].push,Vo=function AggregateError(t,a){var u,_=arguments.length>2?arguments[2]:void 0,h=j(wo,this);gt?u=gt(Lo(),h?Ln(this):wo):(u=h?this:pt(wo),He(u,ko,"Error")),void 0!==a&&He(u,"message",normalizeStringArgument(a)),bo&&He(u,"stack",errorStackClear(u.stack,1)),installErrorCause(u,_);var E=[];return iterate(t,Do,{that:E}),He(u,"errors",E),u};gt?gt(Vo,Lo):function(t,a,u){for(var _=go(a),h=Ge.f,E=Oe.f,m=0;m<_.length;m++){var g=_[m];ue(t,g)||u&&ue(u,g)||h(t,g,E(a,g))}}(Vo,Lo,{name:!0});var wo=Vo.prototype=pt(Lo.prototype,{constructor:createPropertyDescriptor(1,Vo),message:createPropertyDescriptor(1,""),name:createPropertyDescriptor(1,"AggregateError")});_export({global:!0,constructor:!0,arity:2},{AggregateError:Vo});var Po,xo,Uo,Fo,Bo="process"==classofRaw(h.process),Go=wellKnownSymbol("species"),setSpecies=function(t){var a=getBuiltIn(t),u=Ge.f;C&&a&&!a[Go]&&u(a,Go,{configurable:!0,get:function(){return this}})},Ho=TypeError,anInstance=function(t,a){if(j(a,t))return t;throw Ho("Incorrect invocation")},Yo=TypeError,aConstructor=function(t){if(Wt(t))return t;throw Yo(tryToString(t)+" is not a constructor")},jo=wellKnownSymbol("species"),speciesConstructor=function(t,a){var u,_=anObject(t).constructor;return void 0===_||isNullOrUndefined(u=anObject(_)[jo])?a:aConstructor(u)},Ko=TypeError,validateArgumentsLength=function(t,a){if(t<a)throw Ko("Not enough arguments");return t},qo=/(?:ipad|iphone|ipod).*applewebkit/i.test(K),Wo=h.setImmediate,zo=h.clearImmediate,$o=h.process,Xo=h.Dispatch,Qo=h.Function,Jo=h.MessageChannel,Zo=h.String,ei=0,ti={},ri="onreadystatechange";try{Po=h.location}catch(t){}var run=function(t){if(ue(ti,t)){var a=ti[t];delete ti[t],a()}},runner=function(t){return function(){run(t)}},listener=function(t){run(t.data)},post=function(t){h.postMessage(Zo(t),Po.protocol+"//"+Po.host)};Wo&&zo||(Wo=function setImmediate(t){validateArgumentsLength(arguments.length,1);var a=isCallable(t)?t:Qo(t),u=vt(arguments,1);return ti[++ei]=function(){T(a,void 0,u)},xo(ei),ei},zo=function clearImmediate(t){delete ti[t]},Bo?xo=function(t){$o.nextTick(runner(t))}:Xo&&Xo.now?xo=function(t){Xo.now(runner(t))}:Jo&&!qo?(Fo=(Uo=new Jo).port2,Uo.port1.onmessage=listener,xo=functionBindContext(Fo.postMessage,Fo)):h.addEventListener&&isCallable(h.postMessage)&&!h.importScripts&&Po&&"file:"!==Po.protocol&&!fails(post)?(xo=post,h.addEventListener("message",listener,!1)):xo=ri in documentCreateElement("script")?function(t){ut.appendChild(documentCreateElement("script")).onreadystatechange=function(){ut.removeChild(this),run(t)}}:function(t){setTimeout(runner(t),0)});var ni,oi,ii,ai,si,ci,li,ui,di={set:Wo,clear:zo},_i=/ipad|iphone|ipod/i.test(K)&&void 0!==h.Pebble,pi=/web0s(?!.*chrome)/i.test(K),hi=Oe.f,fi=di.set,Ei=h.MutationObserver||h.WebKitMutationObserver,mi=h.document,gi=h.process,Ii=h.Promise,vi=hi(h,"queueMicrotask"),Ti=vi&&vi.value;Ti||(ni=function(){var t,a;for(Bo&&(t=gi.domain)&&t.exit();oi;){a=oi.fn,oi=oi.next;try{a()}catch(t){throw oi?ai():ii=void 0,t}}ii=void 0,t&&t.enter()},qo||Bo||pi||!Ei||!mi?!_i&&Ii&&Ii.resolve?((li=Ii.resolve(void 0)).constructor=Ii,ui=functionBindContext(li.then,li),ai=function(){ui(ni)}):Bo?ai=function(){gi.nextTick(ni)}:(fi=functionBindContext(fi,h),ai=function(){fi(ni)}):(si=!0,ci=mi.createTextNode(""),new Ei(ni).observe(ci,{characterData:!0}),ai=function(){ci.data=si=!si}));var Ni=Ti||function(t){var a={fn:t,next:void 0};ii&&(ii.next=a),oi||(oi=a,ai()),ii=a},perform=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},Queue=function(){this.head=null,this.tail=null};Queue.prototype={add:function(t){var a={item:t,next:null};this.head?this.tail.next=a:this.head=a,this.tail=a},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}};var Mi,Oi,Si=Queue,Ri=h.Promise,yi="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,Ci=!yi&&!Bo&&"object"==typeof window&&"object"==typeof document,Ai=Ri&&Ri.prototype,bi=wellKnownSymbol("species"),ki=!1,Li=isCallable(h.PromiseRejectionEvent),Di=be("Promise",(function(){var t=Ft(Ri),a=t!==String(Ri);if(!a&&66===X)return!0;if(!Ai.catch||!Ai.finally)return!0;if(!X||X<51||!/native code/.test(t)){var u=new Ri((function(t){t(1)})),FakePromise=function(t){t((function(){}),(function(){}))};if((u.constructor={})[bi]=FakePromise,!(ki=u.then((function(){}))instanceof FakePromise))return!0}return!a&&(Ci||yi)&&!Li})),Vi={CONSTRUCTOR:Di,REJECTION_EVENT:Li,SUBCLASSING:ki},wi=TypeError,PromiseCapability=function(t){var a,u;this.promise=new t((function(t,_){if(void 0!==a||void 0!==u)throw wi("Bad Promise constructor");a=t,u=_})),this.resolve=aCallable(a),this.reject=aCallable(u)},Pi={f:function(t){return new PromiseCapability(t)}},xi=di.set,Ui="Promise",Fi=Vi.CONSTRUCTOR,Bi=Vi.REJECTION_EVENT,Gi=yr.getterFor(Ui),Hi=yr.set,Yi=Ri&&Ri.prototype,ji=Ri,Ki=Yi,qi=h.TypeError,Wi=h.document,zi=h.process,$i=Pi.f,Xi=$i,Qi=!!(Wi&&Wi.createEvent&&h.dispatchEvent),Ji="unhandledrejection",isThenable=function(t){var a;return!(!H(t)||!isCallable(a=t.then))&&a},callReaction=function(t,a){var u,_,h,E=a.value,m=1==a.state,g=m?t.ok:t.fail,I=t.resolve,T=t.reject,N=t.domain;try{g?(m||(2===a.rejection&&onHandleUnhandled(a),a.rejection=1),!0===g?u=E:(N&&N.enter(),u=g(E),N&&(N.exit(),h=!0)),u===t.promise?T(qi("Promise-chain cycle")):(_=isThenable(u))?b(_,u,I,T):I(u)):T(E)}catch(t){N&&!h&&N.exit(),T(t)}},notify=function(t,a){t.notified||(t.notified=!0,Ni((function(){for(var u,_=t.reactions;u=_.get();)callReaction(u,t);t.notified=!1,a&&!t.rejection&&onUnhandled(t)})))},dispatchEvent=function(t,a,u){var _,E;Qi?((_=Wi.createEvent("Event")).promise=a,_.reason=u,_.initEvent(t,!1,!0),h.dispatchEvent(_)):_={promise:a,reason:u},!Bi&&(E=h["on"+t])?E(_):t===Ji&&function(t,a){var u=h.console;u&&u.error&&(1==arguments.length?u.error(t):u.error(t,a))}("Unhandled promise rejection",u)},onUnhandled=function(t){b(xi,h,(function(){var a,u=t.facade,_=t.value;if(isUnhandled(t)&&(a=perform((function(){Bo?zi.emit("unhandledRejection",_,u):dispatchEvent(Ji,u,_)})),t.rejection=Bo||isUnhandled(t)?2:1,a.error))throw a.value}))},isUnhandled=function(t){return 1!==t.rejection&&!t.parent},onHandleUnhandled=function(t){b(xi,h,(function(){var a=t.facade;Bo?zi.emit("rejectionHandled",a):dispatchEvent("rejectionhandled",a,t.value)}))},bind=function(t,a,u){return function(_){t(a,_,u)}},internalReject=function(t,a,u){t.done||(t.done=!0,u&&(t=u),t.value=a,t.state=2,notify(t,!0))},internalResolve=function(t,a,u){if(!t.done){t.done=!0,u&&(t=u);try{if(t.facade===a)throw qi("Promise can't be resolved itself");var _=isThenable(a);_?Ni((function(){var u={done:!1};try{b(_,a,bind(internalResolve,u,t),bind(internalReject,u,t))}catch(a){internalReject(u,a,t)}})):(t.value=a,t.state=1,notify(t,!1))}catch(a){internalReject({done:!1},a,t)}}};Fi&&(Ki=(ji=function Promise(t){anInstance(this,Ki),aCallable(t),b(Mi,this);var a=Gi(this);try{t(bind(internalResolve,a),bind(internalReject,a))}catch(t){internalReject(a,t)}}).prototype,(Mi=function Promise(t){Hi(this,{type:Ui,done:!1,notified:!1,parent:!1,reactions:new Si,rejection:!1,state:0,value:void 0})}).prototype=defineBuiltIn(Ki,"then",(function then(t,a){var u=Gi(this),_=$i(speciesConstructor(this,ji));return u.parent=!0,_.ok=!isCallable(t)||t,_.fail=isCallable(a)&&a,_.domain=Bo?zi.domain:void 0,0==u.state?u.reactions.add(_):Ni((function(){callReaction(_,u)})),_.promise})),Oi=function(){var t=new Mi,a=Gi(t);this.promise=t,this.resolve=bind(internalResolve,a),this.reject=bind(internalReject,a)},Pi.f=$i=function(t){return t===ji||undefined===t?new Oi(t):Xi(t)}),_export({global:!0,constructor:!0,wrap:!0,forced:Fi},{Promise:ji}),setToStringTag(ji,Ui,!1,!0),setSpecies(Ui);var Zi=wellKnownSymbol("iterator"),ea=!1;try{var ta=0,ra={next:function(){return{done:!!ta++}},return:function(){ea=!0}};ra[Zi]=function(){return this},Array.from(ra,(function(){throw 2}))}catch(t){}var checkCorrectnessOfIteration=function(t,a){if(!a&&!ea)return!1;var u=!1;try{var _={};_[Zi]=function(){return{next:function(){return{done:u=!0}}}},t(_)}catch(t){}return u},na=Vi.CONSTRUCTOR||!checkCorrectnessOfIteration((function(t){Ri.all(t).then(void 0,(function(){}))}));_export({target:"Promise",stat:!0,forced:na},{all:function all(t){var a=this,u=Pi.f(a),_=u.resolve,h=u.reject,E=perform((function(){var u=aCallable(a.resolve),E=[],m=0,g=1;iterate(t,(function(t){var I=m++,T=!1;g++,b(u,a,t).then((function(t){T||(T=!0,E[I]=t,--g||_(E))}),h)})),--g||_(E)}));return E.error&&h(E.value),u.promise}});var oa=Vi.CONSTRUCTOR;Ri&&Ri.prototype,_export({target:"Promise",proto:!0,forced:oa,real:!0},{catch:function(t){return this.then(void 0,t)}}),_export({target:"Promise",stat:!0,forced:na},{race:function race(t){var a=this,u=Pi.f(a),_=u.reject,h=perform((function(){var h=aCallable(a.resolve);iterate(t,(function(t){b(h,a,t).then(u.resolve,_)}))}));return h.error&&_(h.value),u.promise}}),_export({target:"Promise",stat:!0,forced:Vi.CONSTRUCTOR},{reject:function reject(t){var a=Pi.f(this);return b(a.reject,void 0,t),a.promise}});var promiseResolve=function(t,a){if(anObject(t),H(a)&&a.constructor===t)return a;var u=Pi.f(t);return(0,u.resolve)(a),u.promise},ia=Vi.CONSTRUCTOR,aa=getBuiltIn("Promise"),sa=!ia;_export({target:"Promise",stat:!0,forced:!0},{resolve:function resolve(t){return promiseResolve(sa&&this===aa?Ri:this,t)}}),_export({target:"Promise",stat:!0},{allSettled:function allSettled(t){var a=this,u=Pi.f(a),_=u.resolve,h=u.reject,E=perform((function(){var u=aCallable(a.resolve),h=[],E=0,m=1;iterate(t,(function(t){var g=E++,I=!1;m++,b(u,a,t).then((function(t){I||(I=!0,h[g]={status:"fulfilled",value:t},--m||_(h))}),(function(t){I||(I=!0,h[g]={status:"rejected",reason:t},--m||_(h))}))})),--m||_(h)}));return E.error&&h(E.value),u.promise}});var ca="No one promise resolved";_export({target:"Promise",stat:!0},{any:function any(t){var a=this,u=getBuiltIn("AggregateError"),_=Pi.f(a),h=_.resolve,E=_.reject,m=perform((function(){var _=aCallable(a.resolve),m=[],g=0,I=1,T=!1;iterate(t,(function(t){var N=g++,M=!1;I++,b(_,a,t).then((function(t){M||T||(T=!0,h(t))}),(function(t){M||T||(M=!0,m[N]=t,--I||E(new u(m,ca)))}))})),--I||E(new u(m,ca))}));return m.error&&E(m.value),_.promise}});var la=Ri&&Ri.prototype,ua=!!Ri&&fails((function(){la.finally.call({then:function(){}},(function(){}))}));_export({target:"Promise",proto:!0,real:!0,forced:ua},{finally:function(t){var a=speciesConstructor(this,getBuiltIn("Promise")),u=isCallable(t);return this.then(u?function(u){return promiseResolve(a,t()).then((function(){return u}))}:t,u?function(u){return promiseResolve(a,t()).then((function(){throw u}))}:t)}});var da=Y.Promise;_export({target:"Promise",stat:!0,forced:!0},{try:function(t){var a=Pi.f(this),u=perform(t);return(u.error?a.reject:a.resolve)(u.value),a.promise}});var _a=da,pa=R([].reverse),ha=[1,2];_export({target:"Array",proto:!0,forced:String(ha)===String(ha.reverse())},{reverse:function reverse(){return bt(this)&&(this.length=this.length),pa(this)}});var fa=entryVirtual("Array").reverse,Ea=Array.prototype,reverse=function(t){var a=t.reverse;return t===Ea||j(Ea,t)&&a===Ea.reverse?fa:a},ma=arrayMethodHasSpeciesSupport("slice"),ga=wellKnownSymbol("species"),Ia=Array,va=Math.max;_export({target:"Array",proto:!0,forced:!ma},{slice:function slice(t,a){var u,_,h,E=toIndexedObject(this),m=lengthOfArrayLike(E),g=toAbsoluteIndex(t,m),I=toAbsoluteIndex(void 0===a?m:a,m);if(bt(E)&&(u=E.constructor,(Wt(u)&&(u===Ia||bt(u.prototype))||H(u)&&null===(u=u[ga]))&&(u=void 0),u===Ia||void 0===u))return vt(E,g,I);for(_=new(void 0===u?Ia:u)(va(I-g,0)),h=0;g<I;g++,h++)g in E&&createProperty(_,h,E[g]);return _.length=h,_}});var Ta=entryVirtual("Array").slice,Na=Array.prototype,slice=function(t){var a=t.slice;return t===Na||j(Na,t)&&a===Na.slice?Ta:a},Ma=createCommonjsModule((function(t){var a=co.default;function _regeneratorRuntime(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
t.exports=_regeneratorRuntime=function _regeneratorRuntime(){return u},t.exports.__esModule=!0,t.exports.default=t.exports;var u={},_=Object.prototype,h=_.hasOwnProperty,E="function"==typeof Jn?Jn:{},m=E.iterator||"@@iterator",g=E.asyncIterator||"@@asyncIterator",I=E.toStringTag||"@@toStringTag";function define(t,a,u){return We(t,a,{value:u,enumerable:!0,configurable:!0,writable:!0}),t[a]}try{define({},"")}catch(t){define=function define(t,a,u){return t[a]=u}}function wrap(t,a,u,_){var h=a&&a.prototype instanceof Generator?a:Generator,E=ft(h.prototype),m=new Context(_||[]);return E._invoke=function(t,a,u){var _="suspendedStart";return function(h,E){if("executing"===_)throw new Error("Generator is already running");if("completed"===_){if("throw"===h)throw E;return doneResult()}for(u.method=h,u.arg=E;;){var m=u.delegate;if(m){var g=maybeInvokeDelegate(m,u);if(g){if(g===T)continue;return g}}if("next"===u.method)u.sent=u._sent=u.arg;else if("throw"===u.method){if("suspendedStart"===_)throw _="completed",u.arg;u.dispatchException(u.arg)}else"return"===u.method&&u.abrupt("return",u.arg);_="executing";var I=tryCatch(t,a,u);if("normal"===I.type){if(_=u.done?"completed":"suspendedYield",I.arg===T)continue;return{value:I.arg,done:u.done}}"throw"===I.type&&(_="completed",u.method="throw",u.arg=I.arg)}}}(t,u,m),E}function tryCatch(t,a,u){try{return{type:"normal",arg:t.call(a,u)}}catch(t){return{type:"throw",arg:t}}}u.wrap=wrap;var T={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var N={};define(N,m,(function(){return this}));var M=uo&&uo(uo(values([])));M&&M!==_&&h.call(M,m)&&(N=M);var O=GeneratorFunctionPrototype.prototype=Generator.prototype=ft(N);function defineIteratorMethods(t){var a;forEach$1(a=["next","throw","return"]).call(a,(function(a){define(t,a,(function(t){return this._invoke(a,t)}))}))}function AsyncIterator(t,u){function invoke(_,E,m,g){var I=tryCatch(t[_],t,E);if("throw"!==I.type){var T=I.arg,N=T.value;return N&&"object"==a(N)&&h.call(N,"__await")?u.resolve(N.__await).then((function(t){invoke("next",t,m,g)}),(function(t){invoke("throw",t,m,g)})):u.resolve(N).then((function(t){T.value=t,m(T)}),(function(t){return invoke("throw",t,m,g)}))}g(I.arg)}var _;this._invoke=function(t,a){function callInvokeWithMethodAndArg(){return new u((function(u,_){invoke(t,a,u,_)}))}return _=_?_.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}}function maybeInvokeDelegate(t,a){var u=t.iterator[a.method];if(void 0===u){if(a.delegate=null,"throw"===a.method){if(t.iterator.return&&(a.method="return",a.arg=void 0,maybeInvokeDelegate(t,a),"throw"===a.method))return T;a.method="throw",a.arg=new TypeError("The iterator does not provide a 'throw' method")}return T}var _=tryCatch(u,t.iterator,a.arg);if("throw"===_.type)return a.method="throw",a.arg=_.arg,a.delegate=null,T;var h=_.arg;return h?h.done?(a[t.resultName]=h.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=void 0),a.delegate=null,T):h:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,T)}function pushTryEntry(t){var a={tryLoc:t[0]};1 in t&&(a.catchLoc=t[1]),2 in t&&(a.finallyLoc=t[2],a.afterLoc=t[3]),this.tryEntries.push(a)}function resetTryEntry(t){var a=t.completion||{};a.type="normal",delete a.arg,t.completion=a}function Context(t){this.tryEntries=[{tryLoc:"root"}],forEach$1(t).call(t,pushTryEntry,this),this.reset(!0)}function values(t){if(t){var a=t[m];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var u=-1,_=function next(){for(;++u<t.length;)if(h.call(t,u))return next.value=t[u],next.done=!1,next;return next.value=void 0,next.done=!0,next};return _.next=_}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,define(O,"constructor",GeneratorFunctionPrototype),define(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,I,"GeneratorFunction"),u.isGeneratorFunction=function(t){var a="function"==typeof t&&t.constructor;return!!a&&(a===GeneratorFunction||"GeneratorFunction"===(a.displayName||a.name))},u.mark=function(t){return It?It(t,GeneratorFunctionPrototype):(t.__proto__=GeneratorFunctionPrototype,define(t,I,"GeneratorFunction")),t.prototype=ft(O),t},u.awrap=function(t){return{__await:t}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,g,(function(){return this})),u.AsyncIterator=AsyncIterator,u.async=function(t,a,_,h,E){void 0===E&&(E=_a);var m=new AsyncIterator(wrap(t,a,_,h),E);return u.isGeneratorFunction(a)?m:m.next().then((function(t){return t.done?t.value:m.next()}))},defineIteratorMethods(O),define(O,I,"Generator"),define(O,m,(function(){return this})),define(O,"toString",(function(){return"[object Generator]"})),u.keys=function(t){var a=[];for(var u in t)a.push(u);return reverse(a).call(a),function next(){for(;a.length;){var u=a.pop();if(u in t)return next.value=u,next.done=!1,next}return next.done=!0,next}},u.values=values,Context.prototype={constructor:Context,reset:function reset(t){var a;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,forEach$1(a=this.tryEntries).call(a,resetTryEntry),!t)for(var u in this)"t"===u.charAt(0)&&h.call(this,u)&&!isNaN(+slice(u).call(u,1))&&(this[u]=void 0)},stop:function stop(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function dispatchException(t){if(this.done)throw t;var a=this;function handle(u,_){return E.type="throw",E.arg=t,a.next=u,_&&(a.method="next",a.arg=void 0),!!_}for(var u=this.tryEntries.length-1;u>=0;--u){var _=this.tryEntries[u],E=_.completion;if("root"===_.tryLoc)return handle("end");if(_.tryLoc<=this.prev){var m=h.call(_,"catchLoc"),g=h.call(_,"finallyLoc");if(m&&g){if(this.prev<_.catchLoc)return handle(_.catchLoc,!0);if(this.prev<_.finallyLoc)return handle(_.finallyLoc)}else if(m){if(this.prev<_.catchLoc)return handle(_.catchLoc,!0)}else{if(!g)throw new Error("try statement without catch or finally");if(this.prev<_.finallyLoc)return handle(_.finallyLoc)}}}},abrupt:function abrupt(t,a){for(var u=this.tryEntries.length-1;u>=0;--u){var _=this.tryEntries[u];if(_.tryLoc<=this.prev&&h.call(_,"finallyLoc")&&this.prev<_.finallyLoc){var E=_;break}}E&&("break"===t||"continue"===t)&&E.tryLoc<=a&&a<=E.finallyLoc&&(E=null);var m=E?E.completion:{};return m.type=t,m.arg=a,E?(this.method="next",this.next=E.finallyLoc,T):this.complete(m)},complete:function complete(t,a){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&a&&(this.next=a),T},finish:function finish(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.finallyLoc===t)return this.complete(u.completion,u.afterLoc),resetTryEntry(u),T}},catch:function _catch(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.tryLoc===t){var _=u.completion;if("throw"===_.type){var h=_.arg;resetTryEntry(u)}return h}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(t,a,u){return this.delegate={iterator:values(t),resultName:a,nextLoc:u},"next"===this.method&&(this.arg=void 0),T}},u}t.exports=_regeneratorRuntime,t.exports.__esModule=!0,t.exports.default=t.exports})),Oa=Ma(),Sa=Oa;try{regeneratorRuntime=Oa}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=Oa:Function("r","regeneratorRuntime = r")(Oa)}var Ra=Object.assign,ya=Object.defineProperty,Ca=R([].concat),Aa=!Ra||fails((function(){if(C&&1!==Ra({b:1},Ra(ya({},"a",{enumerable:!0,get:function(){ya(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},a={},u=Symbol(),_="abcdefghijklmnopqrst";return t[u]=7,_.split("").forEach((function(t){a[t]=t})),7!=Ra({},t)[u]||st(Ra({},a)).join("")!=_}))?function assign(t,a){for(var u=toObject(t),_=arguments.length,h=1,E=dr.f,m=D.f;_>h;)for(var g,I=U(arguments[h++]),T=E?Ca(st(I),E(I)):st(I),N=T.length,M=0;N>M;)g=T[M++],C&&!b(m,I,g)||(u[g]=I[g]);return u}:Ra;_export({target:"Object",stat:!0,arity:2,forced:Object.assign!==Aa},{assign:Aa});var ba=Y.Object.assign,ka=D.f,La=R(ka),Da=R([].push),createMethod$2=function(t){return function(a){for(var u,_=toIndexedObject(a),h=st(_),E=h.length,m=0,g=[];E>m;)u=h[m++],C&&!La(_,u)||Da(g,t?[u,_[u]]:_[u]);return g}},Va={entries:createMethod$2(!0),values:createMethod$2(!1)}.values;_export({target:"Object",stat:!0},{values:function values(t){return Va(t)}});var wa=Y.Object.values,Pa=entryVirtual("Array").concat,xa=Array.prototype,concat=function(t){var a=t.concat;return t===xa||j(xa,t)&&a===xa.concat?Pa:a};function __rest(t,a){var u={};for(var _ in t)Object.prototype.hasOwnProperty.call(t,_)&&a.indexOf(_)<0&&(u[_]=t[_]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var h=0;for(_=Object.getOwnPropertySymbols(t);h<_.length;h++)a.indexOf(_[h])<0&&Object.prototype.propertyIsEnumerable.call(t,_[h])&&(u[_[h]]=t[_[h]])}return u}function __awaiter(t,a,u,_){return new(u||(u=Promise))((function(h,E){function fulfilled(t){try{step(_.next(t))}catch(t){E(t)}}function rejected(t){try{step(_.throw(t))}catch(t){E(t)}}function step(t){t.done?h(t.value):function adopt(t){return t instanceof u?t:new u((function(a){a(t)}))}(t.value).then(fulfilled,rejected)}step((_=_.apply(t,a||[])).next())}))}_export({global:!0},{globalThis:h});var Ua=h,Fa=Date,Ba=R(Fa.prototype.getTime);_export({target:"Date",stat:!0},{now:function now(){return Ba(new Fa)}});var Ga=Y.Date.now,Ha=rt.includes,Ya=fails((function(){return!Array(1).includes()}));_export({target:"Array",proto:!0,forced:Ya},{includes:function includes(t){return Ha(this,t,arguments.length>1?arguments[1]:void 0)}});var ja=entryVirtual("Array").includes,Ka=wellKnownSymbol("match"),qa=TypeError,notARegexp=function(t){if(function(t){var a;return H(t)&&(void 0!==(a=t[Ka])?!!a:"RegExp"==classofRaw(t))}(t))throw qa("The method doesn't accept regular expressions");return t},Wa=wellKnownSymbol("match"),za=R("".indexOf);_export({target:"String",proto:!0,forced:!function(t){var a=/./;try{"/./"[t](a)}catch(u){try{return a[Wa]=!1,"/./"[t](a)}catch(t){}}return!1}("includes")},{includes:function includes(t){return!!~za(toString(requireObjectCoercible(this)),toString(notARegexp(t)),arguments.length>1?arguments[1]:void 0)}});var $a=entryVirtual("String").includes,Xa=Array.prototype,Qa=String.prototype,includes=function(t){var a=t.includes;return t===Xa||j(Xa,t)&&a===Xa.includes?ja:"string"==typeof t||t===Qa||j(Qa,t)&&a===Qa.includes?$a:a},Ja=fails((function(){st(1)}));_export({target:"Object",stat:!0,forced:Ja},{keys:function keys(t){return st(toObject(t))}});var Za=Y.Object.keys,es=Ar.map,ts=arrayMethodHasSpeciesSupport("map");_export({target:"Array",proto:!0,forced:!ts},{map:function map(t){return es(this,t,arguments.length>1?arguments[1]:void 0)}});var rs=entryVirtual("Array").map,ns=Array.prototype,map$7=function(t){var a=t.map;return t===ns||j(ns,t)&&a===ns.map?rs:a},os=/MSIE .\./.test(K),is=h.Function,wrap=function(t){return os?function(a,u){var _=validateArgumentsLength(arguments.length,1)>2,h=isCallable(a)?a:is(a),E=_?vt(arguments,2):void 0;return t(_?function(){T(h,this,E)}:h,u)}:t},as={setTimeout:wrap(h.setTimeout),setInterval:wrap(h.setInterval)},ss=as.setInterval;_export({global:!0,bind:!0,forced:h.setInterval!==ss},{setInterval:ss});var cs=as.setTimeout;_export({global:!0,bind:!0,forced:h.setTimeout!==cs},{setTimeout:cs});var ls=Y.setTimeout,us=Ar.filter,ds=arrayMethodHasSpeciesSupport("filter");_export({target:"Array",proto:!0,forced:!ds},{filter:function filter(t){return us(this,t,arguments.length>1?arguments[1]:void 0)}});var _s=entryVirtual("Array").filter,ps=Array.prototype,filter=function(t){var a=t.filter;return t===ps||j(ps,t)&&a===ps.filter?_s:a},hs=createCommonjsModule((function(t,a){t.exports=function(){function _regeneratorRuntime(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
_regeneratorRuntime=function _regeneratorRuntime(){return t};var t={},a=Object.prototype,u=a.hasOwnProperty,_="function"==typeof Jn?Jn:{},h=_.iterator||"@@iterator",E=_.asyncIterator||"@@asyncIterator",m=_.toStringTag||"@@toStringTag";function define(t,a,u){return We(t,a,{value:u,enumerable:!0,configurable:!0,writable:!0}),t[a]}try{define({},"")}catch(t){define=function define(t,a,u){return t[a]=u}}function wrap(t,a,u,_){var h=a&&a.prototype instanceof Generator?a:Generator,E=ft(h.prototype),m=new Context(_||[]);return E._invoke=function(t,a,u){var _="suspendedStart";return function(h,E){if("executing"===_)throw new Error("Generator is already running");if("completed"===_){if("throw"===h)throw E;return doneResult()}for(u.method=h,u.arg=E;;){var m=u.delegate;if(m){var I=maybeInvokeDelegate(m,u);if(I){if(I===g)continue;return I}}if("next"===u.method)u.sent=u._sent=u.arg;else if("throw"===u.method){if("suspendedStart"===_)throw _="completed",u.arg;u.dispatchException(u.arg)}else"return"===u.method&&u.abrupt("return",u.arg);_="executing";var T=tryCatch(t,a,u);if("normal"===T.type){if(_=u.done?"completed":"suspendedYield",T.arg===g)continue;return{value:T.arg,done:u.done}}"throw"===T.type&&(_="completed",u.method="throw",u.arg=T.arg)}}}(t,u,m),E}function tryCatch(t,a,u){try{return{type:"normal",arg:t.call(a,u)}}catch(t){return{type:"throw",arg:t}}}t.wrap=wrap;var g={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var I={};define(I,h,(function(){return this}));var T=uo&&uo(uo(values([])));T&&T!==a&&u.call(T,h)&&(I=T);var N=GeneratorFunctionPrototype.prototype=Generator.prototype=ft(I);function defineIteratorMethods(t){var a;forEach$1(a=["next","throw","return"]).call(a,(function(a){define(t,a,(function(t){return this._invoke(a,t)}))}))}function AsyncIterator(t,a){function invoke(_,h,E,m){var g=tryCatch(t[_],t,h);if("throw"!==g.type){var I=g.arg,T=I.value;return T&&"object"==typeof T&&u.call(T,"__await")?a.resolve(T.__await).then((function(t){invoke("next",t,E,m)}),(function(t){invoke("throw",t,E,m)})):a.resolve(T).then((function(t){I.value=t,E(I)}),(function(t){return invoke("throw",t,E,m)}))}m(g.arg)}var _;this._invoke=function(t,u){function callInvokeWithMethodAndArg(){return new a((function(a,_){invoke(t,u,a,_)}))}return _=_?_.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}}function maybeInvokeDelegate(t,a){var u=t.iterator[a.method];if(void 0===u){if(a.delegate=null,"throw"===a.method){if(t.iterator.return&&(a.method="return",a.arg=void 0,maybeInvokeDelegate(t,a),"throw"===a.method))return g;a.method="throw",a.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var _=tryCatch(u,t.iterator,a.arg);if("throw"===_.type)return a.method="throw",a.arg=_.arg,a.delegate=null,g;var h=_.arg;return h?h.done?(a[t.resultName]=h.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=void 0),a.delegate=null,g):h:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function pushTryEntry(t){var a={tryLoc:t[0]};1 in t&&(a.catchLoc=t[1]),2 in t&&(a.finallyLoc=t[2],a.afterLoc=t[3]),this.tryEntries.push(a)}function resetTryEntry(t){var a=t.completion||{};a.type="normal",delete a.arg,t.completion=a}function Context(t){this.tryEntries=[{tryLoc:"root"}],forEach$1(t).call(t,pushTryEntry,this),this.reset(!0)}function values(t){if(t){var a=t[h];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var _=-1,E=function next(){for(;++_<t.length;)if(u.call(t,_))return next.value=t[_],next.done=!1,next;return next.value=void 0,next.done=!0,next};return E.next=E}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,define(N,"constructor",GeneratorFunctionPrototype),define(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,m,"GeneratorFunction"),t.isGeneratorFunction=function(t){var a="function"==typeof t&&t.constructor;return!!a&&(a===GeneratorFunction||"GeneratorFunction"===(a.displayName||a.name))},t.mark=function(t){return It?It(t,GeneratorFunctionPrototype):(t.__proto__=GeneratorFunctionPrototype,define(t,m,"GeneratorFunction")),t.prototype=ft(N),t},t.awrap=function(t){return{__await:t}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,E,(function(){return this})),t.AsyncIterator=AsyncIterator,t.async=function(a,u,_,h,E){void 0===E&&(E=_a);var m=new AsyncIterator(wrap(a,u,_,h),E);return t.isGeneratorFunction(u)?m:m.next().then((function(t){return t.done?t.value:m.next()}))},defineIteratorMethods(N),define(N,m,"Generator"),define(N,h,(function(){return this})),define(N,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var a=[];for(var u in t)a.push(u);return reverse(a).call(a),function next(){for(;a.length;){var u=a.pop();if(u in t)return next.value=u,next.done=!1,next}return next.done=!0,next}},t.values=values,Context.prototype={constructor:Context,reset:function reset(t){var a;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,forEach$1(a=this.tryEntries).call(a,resetTryEntry),!t)for(var _ in this)"t"===_.charAt(0)&&u.call(this,_)&&!isNaN(+slice(_).call(_,1))&&(this[_]=void 0)},stop:function stop(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function dispatchException(t){if(this.done)throw t;var a=this;function handle(u,_){return E.type="throw",E.arg=t,a.next=u,_&&(a.method="next",a.arg=void 0),!!_}for(var _=this.tryEntries.length-1;_>=0;--_){var h=this.tryEntries[_],E=h.completion;if("root"===h.tryLoc)return handle("end");if(h.tryLoc<=this.prev){var m=u.call(h,"catchLoc"),g=u.call(h,"finallyLoc");if(m&&g){if(this.prev<h.catchLoc)return handle(h.catchLoc,!0);if(this.prev<h.finallyLoc)return handle(h.finallyLoc)}else if(m){if(this.prev<h.catchLoc)return handle(h.catchLoc,!0)}else{if(!g)throw new Error("try statement without catch or finally");if(this.prev<h.finallyLoc)return handle(h.finallyLoc)}}}},abrupt:function abrupt(t,a){for(var _=this.tryEntries.length-1;_>=0;--_){var h=this.tryEntries[_];if(h.tryLoc<=this.prev&&u.call(h,"finallyLoc")&&this.prev<h.finallyLoc){var E=h;break}}E&&("break"===t||"continue"===t)&&E.tryLoc<=a&&a<=E.finallyLoc&&(E=null);var m=E?E.completion:{};return m.type=t,m.arg=a,E?(this.method="next",this.next=E.finallyLoc,g):this.complete(m)},complete:function complete(t,a){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&a&&(this.next=a),g},finish:function finish(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.finallyLoc===t)return this.complete(u.completion,u.afterLoc),resetTryEntry(u),g}},catch:function _catch(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.tryLoc===t){var _=u.completion;if("throw"===_.type){var h=_.arg;resetTryEntry(u)}return h}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(t,a,u){return this.delegate={iterator:values(t),resultName:a,nextLoc:u},"next"===this.method&&(this.arg=void 0),g}},t}function _typeof(t){return _typeof="function"==typeof Jn&&"symbol"==typeof so?function(t){return typeof t}:function(t){return t&&"function"==typeof Jn&&t.constructor===Jn&&t!==Jn.prototype?"symbol":typeof t},_typeof(t)}function _classCallCheck(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,a){for(var u=0;u<a.length;u++){var _=a[u];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),We(t,_.key,_)}}function _createClass(t,a,u){return a&&_defineProperties(t.prototype,a),u&&_defineProperties(t,u),We(t,"prototype",{writable:!1}),t}function __awaiter(t,a,u,_){function adopt(t){return t instanceof u?t:new u((function(a){a(t)}))}return new(u||(u=_a))((function(u,h){function fulfilled(t){try{step(_.next(t))}catch(t){h(t)}}function rejected(t){try{step(_.throw(t))}catch(t){h(t)}}function step(t){t.done?u(t.value):adopt(t.value).then(fulfilled,rejected)}step((_=_.apply(t,a||[])).next())}))}var t={isDataReportEnable:!0,maxSize:100,msgListMaxSize:1e3,cacheMaxSize:1e3,maxDelay:3e5,maxInterval:3e4,minInterval:1e4,timeout:5e3,autoStart:!0,loginFailIgnoreInterval:72e5},a=12,u=8e3,_=function emptyFn(){},h=function(){function Reporter(a){_classCallCheck(this,Reporter),this.isUploadEnable=!0,this.serverAllowUpload=!1,this.initConfigLoaded=!1,this.loading=!1,this.isDestroyed=!1,this.reportConfig=t,this.configPath="dispatcher/req",this.dataReportPath="statics/report/common/form",this.traceMsgCache={},this.reqRetryCount=0,this.highPriorityMsgList=[],this.msgList=[],this.lowPriorityMsgList=[],this.cacheMsgList=[],this.lastReportTime=Ga(),this.timer=null,this.endedAsyncMsgByModule={},this.lastFailLogin={},this.setConfig(a),this.reportConfig.isDataReportEnable&&this.reportConfig.autoStart&&this.initUploadConfig()}return _createClass(Reporter,[{key:"setConfig",value:function setConfig(t){var a=ba({},this.reportConfig.common,t.common);this.reportConfig=ba({},this.reportConfig,t),this.reportConfig.common=a,this.reportConfig.common.sdk_type||(this.reportConfig.common.sdk_type="im")}},{key:"reportImmediately",value:function reportImmediately(t,a){var u=this;this.reportConfig.isDataReportEnable&&this.reportConfig.request(t,ba({dataType:"json",method:"POST",timeout:this.reportConfig.timeout},a)).catch((function(t){var a,_;null===(_=null===(a=u.reportConfig)||void 0===a?void 0:a.logger)||void 0===_||_.warn("Reporter immediately upload failed",t)}))}},{key:"report",value:function report(a,u){var _=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(_.priority||(_.priority=this.getEventPriority(a,u)),this.reportConfig.isDataReportEnable&&a){if("login"===a&&!1===u.succeed&&u.process_id){var h=this.lastFailLogin[u.process_id]||0;if(u.start_time-h<t.loginFailIgnoreInterval)return;this.lastFailLogin[u.process_id]=u.start_time}var E=Ga();"HIGH"===_.priority?this.highPriorityMsgList.push({module:a,msg:u,createTime:E}):"NORMAL"===_.priority?this.msgList.push({module:a,msg:u,createTime:E}):"LOW"===_.priority&&this.lowPriorityMsgList.push({module:a,msg:u,createTime:E}),this.highPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.highPriorityMsgList.shift(),this.msgList.length>this.reportConfig.msgListMaxSize&&this.msgList.shift(),this.lowPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.lowPriorityMsgList.shift(),this.doReport()}}},{key:"reportTraceStart",value:function reportTraceStart(t,a){if(this.reportConfig.isDataReportEnable&&t&&!this.traceMsgCache[t]){var u=ba(ba({start_time:Ga()},a),{extension:[]});this.traceMsgCache[t]=u}}},{key:"reportTraceUpdate",value:function reportTraceUpdate(t){}},{key:"reportTraceUpdateV2",value:function reportTraceUpdateV2(t,a,u){var _,h=this;if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[t]){var E=this.traceMsgCache[t].extension,m=E.length,g=(new Date).getTime();0===m?a.duration=g-this.traceMsgCache[t].start_time:E[m-1].end_time?a.duration=g-E[m-1].end_time:a.duration=g-this.traceMsgCache[t].start_time,E.push(ba({end_time:g},a));var I=E.length-1;(null==u?void 0:u.asyncParams)&&((_=this.traceMsgCache[t]).asyncPromiseArray||(_.asyncPromiseArray=[]),this.traceMsgCache[t].asyncPromiseArray.push(u.asyncParams.then((function(a){h.traceMsgCache[t]&&h.traceMsgCache[t].extension[I]&&ba(h.traceMsgCache[t].extension[I],a)}))))}}},{key:"reportTraceEnd",value:function reportTraceEnd(t){var a,u,_=this,h=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[t])if("nos"!==t||!1===h){"boolean"==typeof h?this.traceMsgCache[t].succeed=!!h:this.traceMsgCache[t].state=h,this.traceMsgCache[t].duration=Ga()-this.traceMsgCache[t].start_time,forEach$1(a=this.traceMsgCache[t].extension).call(a,(function(t){delete t.end_time}));var E=this.traceMsgCache[t];if(this.traceMsgCache[t]=null,E.asyncPromiseArray){(u=this.endedAsyncMsgByModule)[t]||(u[t]=[]),this.endedAsyncMsgByModule[t].push(E);var m=function asyncCallback(){var a;_.endedAsyncMsgByModule[t]&&includes(a=_.endedAsyncMsgByModule[t]).call(a,E)&&(delete E.asyncPromiseArray,_.report(t,E,{priority:_.getEventPriority(t,E)}))};_a.all(E.asyncPromiseArray).then(m).catch(m)}else this.report(t,E,{priority:this.getEventPriority(t,E)})}else this.traceMsgCache[t]=null}},{key:"getEventPriority",value:function getEventPriority(t,a){if("exceptions"===t){if(0===a.action)return"HIGH";if(2===a.action)return"HIGH";if(1===a.action&&0!==a.exception_service)return"HIGH"}else{if("msgReceive"===t)return"LOW";if("nim_api_trace"===t)return"LOW"}return"NORMAL"}},{key:"reportTraceCancel",value:function reportTraceCancel(t){this.reportConfig.isDataReportEnable&&(this.endedAsyncMsgByModule[t]=[],this.traceMsgCache[t]=null)}},{key:"pause",value:function pause(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!1)}},{key:"restore",value:function restore(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!0,this.initConfigLoaded||this.initUploadConfig())}},{key:"destroy",value:function destroy(){var t,a=this;this.reportConfig.isDataReportEnable&&(forEach$1(t=Za(this.traceMsgCache)).call(t,(function(t){a.reportTraceEnd(t,1)})),null!==this.timer&&clearTimeout(this.timer),this.setConfig=_,this.report=_,this.reportTraceStart=_,this.reportTraceUpdate=_,this.reportTraceEnd=_,this.pause=_,this.restore=_,this.destroy=_,this.reqRetryCount=0,this.cacheMsgList=[],this.traceMsgCache={},this.lowPriorityMsgList=[],this.msgList=[],this.highPriorityMsgList=[],this.reportConfig={},this.isDestroyed=!0)}},{key:"initUploadConfig",value:function initUploadConfig(){var t,_;return __awaiter(this,void 0,void 0,_regeneratorRuntime().mark((function _callee(){var h,E,m,g,I,T=this;return _regeneratorRuntime().wrap((function _callee$(N){for(var M;;)switch(N.prev=N.next){case 0:if(!this.loading){N.next=2;break}return N.abrupt("return");case 2:this.loading=!0,h=this.reportConfig.common||{},E=map$7(M=this.reportConfig.compassDataEndpoint.split(",")).call(M,(function(t){var a;return concat(a="".concat(t,"/")).call(a,T.configPath)})),m=_regeneratorRuntime().mark((function _loop(m){return _regeneratorRuntime().wrap((function _loop$(g){for(;;)switch(g.prev=g.next){case 0:if(!T.initConfigLoaded&&!T.isDestroyed){g.next=2;break}return g.abrupt("return","break");case 2:return g.prev=2,g.next=5,T.reportConfig.request(E[m],{method:"GET",dataType:"json",params:{deviceId:h.dev_id,sdkVer:h.sdk_ver,platform:h.platform,appkey:h.app_key},timeout:T.reportConfig.timeout}).then((function(t){var a,u;if(!T.isDestroyed){if(200===t.status&&t.data&&200===t.data.code){T.initConfigLoaded=!0;var _=t.data.data||{};T.reportConfig.maxSize=_.maxSize>1e3?1e3:_.maxSize,T.reportConfig.maxInterval=_.maxInterval>1e4?1e4:_.maxInterval,T.reportConfig.maxInterval=_.maxInterval<10?10:_.maxInterval,T.reportConfig.minInterval=_.minInterval<2?2:_.minInterval,T.reportConfig.maxDelay=_.maxDelay||300,T.reportConfig.maxInterval=1e3*T.reportConfig.maxInterval,T.reportConfig.minInterval=1e3*T.reportConfig.minInterval,T.reportConfig.maxDelay=1e3*T.reportConfig.maxDelay,_.endpoint?T.dataReportEndpoint=_.endpoint:T.dataReportEndpoint=E[m],T.serverAllowUpload=!0,T.loading=!1,T.reportHeartBeat()}else 200===t.status&&(T.initConfigLoaded=!0);null===(u=null===(a=T.reportConfig)||void 0===a?void 0:a.logger)||void 0===u||u.log("Get reporter upload config success")}})).catch((function(t){var _,h;T.isDestroyed||(T.loading=!1,null===(h=null===(_=T.reportConfig)||void 0===_?void 0:_.logger)||void 0===h||h.error("Get reporter upload config failed",t),T.reqRetryCount<a&&(T.reqRetryCount++,ls((function(){T.isDestroyed||T.initUploadConfig()}),u)))}));case 5:g.next=14;break;case 7:if(g.prev=7,g.t0=g.catch(2),!T.isDestroyed){g.next=11;break}return g.abrupt("return",{v:void 0});case 11:T.loading=!1,null===(_=null===(t=T.reportConfig)||void 0===t?void 0:t.logger)||void 0===_||_.error("Exec reporter request failed",g.t0),T.reqRetryCount<a&&(T.reqRetryCount++,ls((function(){T.isDestroyed||T.initUploadConfig()}),u));case 14:case"end":return g.stop()}}),_loop,null,[[2,7]])})),g=0;case 7:if(!(g<E.length)){N.next=17;break}return N.delegateYield(m(g),"t0",9);case 9:if("break"!==(I=N.t0)){N.next=12;break}return N.abrupt("break",17);case 12:if("object"!==_typeof(I)){N.next=14;break}return N.abrupt("return",I.v);case 14:g++,N.next=7;break;case 17:case"end":return N.stop()}}),_callee,this)})))}},{key:"reportHeartBeat",value:function reportHeartBeat(){var t=this;this.isDestroyed||(this.timer=ls((function(){t.reportHeartBeat()}),this.reportConfig.minInterval),this.doReport())}},{key:"doReport",value:function doReport(){if(!this.isDestroyed){var t=this.highPriorityMsgList.length+this.msgList.length+this.lowPriorityMsgList.length+this.cacheMsgList.length>2*this.reportConfig.maxSize?this.reportConfig.minInterval:this.reportConfig.maxInterval;Ga()-this.lastReportTime>=t&&this.upload()}}},{key:"getUploadMsg",value:function getUploadMsg(){var t,a,u,_,h,E,m=this,g={},I=Ga();this.highPriorityMsgList=filter(t=this.highPriorityMsgList).call(t,(function(t){return I-t.createTime<m.reportConfig.maxDelay})),this.msgList=filter(a=this.msgList).call(a,(function(t){return I-t.createTime<m.reportConfig.maxDelay})),this.lowPriorityMsgList=filter(u=this.lowPriorityMsgList).call(u,(function(t){return I-t.createTime<m.reportConfig.maxDelay})),this.cacheMsgList=filter(_=this.cacheMsgList).call(_,(function(t){return I-t.createTime<m.reportConfig.maxDelay}));var T=slice(h=this.highPriorityMsgList).call(h,0,this.reportConfig.maxSize);if(this.highPriorityMsgList=slice(E=this.highPriorityMsgList).call(E,T.length),T.length<this.reportConfig.maxSize){var N,M,O=this.reportConfig.maxSize-T.length;T=concat(T).call(T,slice(N=this.msgList).call(N,0,O)),this.msgList=slice(M=this.msgList).call(M,O)}if(T.length<this.reportConfig.maxSize){var S,R,C=this.reportConfig.maxSize-T.length;T=concat(T).call(T,slice(S=this.lowPriorityMsgList).call(S,0,C)),this.lowPriorityMsgList=slice(R=this.lowPriorityMsgList).call(R,C)}if(T.length<this.reportConfig.maxSize){var A,b,k=this.reportConfig.maxSize-T.length;T=concat(T).call(T,slice(A=this.cacheMsgList).call(A,0,k)),this.cacheMsgList=slice(b=this.cacheMsgList).call(b,k)}return forEach$1(T).call(T,(function(t){g[t.module]?g[t.module].push(t.msg):g[t.module]=[t.msg]})),{uploadMsgArr:T,uploadMsg:g}}},{key:"upload",value:function upload(){var t,a,u=this;if(this.isUploadEnable&&this.serverAllowUpload&&!(this.lastReportTime&&Ga()-this.lastReportTime<this.reportConfig.minInterval)){var _=this.getUploadMsg(),h=_.uploadMsgArr,E=_.uploadMsg;if(h.length){this.lastReportTime=Ga();try{var m,g=concat(m="".concat(this.dataReportEndpoint,"/")).call(m,this.dataReportPath);this.reportConfig.request(g,{dataType:"json",method:"POST",data:{common:this.reportConfig.common,event:E},headers:{sdktype:"im"},timeout:this.reportConfig.timeout}).catch((function(t){var a,_,E,m;u.cacheMsgList=slice(a=concat(_=u.cacheMsgList).call(_,h)).call(a,0,u.reportConfig.cacheMaxSize),null===(m=null===(E=u.reportConfig)||void 0===E?void 0:E.logger)||void 0===m||m.warn("Reporter upload failed",t)}))}catch(u){null===(a=null===(t=this.reportConfig)||void 0===t?void 0:t.logger)||void 0===a||a.warn("Exec reporter request failed",u)}clearTimeout(this.timer),this.reportHeartBeat()}}}}]),Reporter}();return h}()}));_export({target:"Array",stat:!0},{isArray:bt});var fs=Y.Array.isArray;function isPlainObject(t){return null!=t&&"object"==typeof t&&uo(t)==Object.prototype}function merge$1(t,a){var u=isPlainObject(t)||fs(t),_=isPlainObject(a)||fs(a);if(u&&_){for(var h in a){var E=merge$1(t[h],a[h]);void 0!==E&&(t[h]=E)}return t}return a}var Es={setLogger:function setLogger(t){throw new Error("setLogger not implemented.")},platform:"",WebSocket:function(){function AdapterSocket(t,a){throw this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this.binaryType="",new Error("Method not implemented.")}var t=AdapterSocket.prototype;return t.close=function close(t,a){throw new Error("Method not implemented.")},t.send=function send(t){throw new Error("Method not implemented.")},t.onclose=function onclose(t){throw new Error("Method not implemented.")},t.onerror=function onerror(t){throw new Error("Method not implemented.")},t.onmessage=function onmessage(t){throw new Error("Method not implemented.")},t.onopen=function onopen(t){throw new Error("Method not implemented.")},AdapterSocket}(),localStorage:{},request:function request(t,a){throw new Error("request not implemented.")},uploadFile:function uploadFile(t){throw new Error("uploadFile not implemented.")},getSystemInfo:function getSystemInfo(){throw new Error("getSystemInfo not implemented.")},getFileUploadInformation:function getFileUploadInformation(t){throw new Error("getFileUploadInformation not implemented.")},envPayload:{},net:{getNetworkStatus:function getNetworkStatus(){return _a.resolve({net_type:0,net_connect:!0})},onNetworkStatusChange:function onNetworkStatusChange(t){},offNetworkStatusChange:function offNetworkStatusChange(){}},logStorage:function(){function AdapterLogStorageImpl(t){}var t=AdapterLogStorageImpl.prototype;return t.open=function open(){return _a.resolve()},t.close=function close(){},t.addLogs=function addLogs(t){return _a.resolve()},t.extractLogs=function extractLogs(){return _a.resolve()},AdapterLogStorageImpl}()};var ms=Ar.findIndex,gs="findIndex",Is=!0;gs in[]&&Array(1).findIndex((function(){Is=!1})),_export({target:"Array",proto:!0,forced:Is},{findIndex:function findIndex(t){return ms(this,t,arguments.length>1?arguments[1]:void 0)}});var vs=entryVirtual("Array").findIndex,Ts=Array.prototype,findIndex=function(t){var a=t.findIndex;return t===Ts||j(Ts,t)&&a===Ts.findIndex?vs:a};Y.JSON||(Y.JSON={stringify:JSON.stringify});var Ns=function stringify(t,a,u){return T(Y.JSON.stringify,null,arguments)},Ms=Ns,Os=fails((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),Ss=Object.isExtensible,Rs=fails((function(){Ss(1)}))||Os?function isExtensible(t){return!!H(t)&&((!Os||"ArrayBuffer"!=classofRaw(t))&&(!Ss||Ss(t)))}:Ss,ys=!fails((function(){return Object.isExtensible(Object.preventExtensions({}))})),Cs=createCommonjsModule((function(t){var a=Ge.f,u=!1,_=uid("meta"),h=0,setMetadata=function(t){a(t,_,{value:{objectID:"O"+h++,weakData:{}}})},E=t.exports={enable:function(){E.enable=function(){},u=!0;var t=ir.f,a=R([].splice),h={};h[_]=1,t(h).length&&(ir.f=function(u){for(var h=t(u),E=0,m=h.length;E<m;E++)if(h[E]===_){a(h,E,1);break}return h},_export({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:ur.f}))},fastKey:function(t,a){if(!H(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!ue(t,_)){if(!Rs(t))return"F";if(!a)return"E";setMetadata(t)}return t[_].objectID},getWeakData:function(t,a){if(!ue(t,_)){if(!Rs(t))return!0;if(!a)return!1;setMetadata(t)}return t[_].weakData},onFreeze:function(t){return ys&&u&&Rs(t)&&!ue(t,_)&&setMetadata(t),t}};nt[_]=!0})),As=Ge.f,bs=Ar.forEach,ks=yr.set,Ls=yr.getterFor,defineBuiltIns=function(t,a,u){for(var _ in a)u&&u.unsafe&&t[_]?t[_]=a[_]:defineBuiltIn(t,_,a[_],u);return t},Ds=Ge.f,Vs=Cs.fastKey,ws=yr.set,Ps=yr.getterFor,xs={getConstructor:function(t,a,u,_){var h=t((function(t,h){anInstance(t,E),ws(t,{type:a,index:pt(null),first:void 0,last:void 0,size:0}),C||(t.size=0),isNullOrUndefined(h)||iterate(h,t[_],{that:t,AS_ENTRIES:u})})),E=h.prototype,m=Ps(a),define=function(t,a,u){var _,h,E=m(t),g=getEntry(t,a);return g?g.value=u:(E.last=g={index:h=Vs(a,!0),key:a,value:u,previous:_=E.last,next:void 0,removed:!1},E.first||(E.first=g),_&&(_.next=g),C?E.size++:t.size++,"F"!==h&&(E.index[h]=g)),t},getEntry=function(t,a){var u,_=m(t),h=Vs(a);if("F"!==h)return _.index[h];for(u=_.first;u;u=u.next)if(u.key==a)return u};return defineBuiltIns(E,{clear:function clear(){for(var t=m(this),a=t.index,u=t.first;u;)u.removed=!0,u.previous&&(u.previous=u.previous.next=void 0),delete a[u.index],u=u.next;t.first=t.last=void 0,C?t.size=0:this.size=0},delete:function(t){var a=this,u=m(a),_=getEntry(a,t);if(_){var h=_.next,E=_.previous;delete u.index[_.index],_.removed=!0,E&&(E.next=h),h&&(h.previous=E),u.first==_&&(u.first=h),u.last==_&&(u.last=E),C?u.size--:a.size--}return!!_},forEach:function forEach(t){for(var a,u=m(this),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);a=a?a.next:u.first;)for(_(a.value,a.key,this);a&&a.removed;)a=a.previous},has:function has(t){return!!getEntry(this,t)}}),defineBuiltIns(E,u?{get:function get(t){var a=getEntry(this,t);return a&&a.value},set:function set(t,a){return define(this,0===t?0:t,a)}}:{add:function add(t){return define(this,t=0===t?0:t,t)}}),C&&Ds(E,"size",{get:function(){return m(this).size}}),h},setStrong:function(t,a,u){var _=a+" Iterator",h=Ps(a),E=Ps(_);iteratorDefine(t,a,(function(t,a){ws(this,{type:_,target:t,state:h(t),kind:a,last:void 0})}),(function(){for(var t=E(this),a=t.kind,u=t.last;u&&u.removed;)u=u.previous;return t.target&&(t.last=u=u?u.next:t.state.first)?"keys"==a?{value:u.key,done:!1}:"values"==a?{value:u.value,done:!1}:{value:[u.key,u.value],done:!1}:(t.target=void 0,{value:void 0,done:!0})}),u?"entries":"values",!u,!0),setSpecies(a)}};!function(t,a,u){var _,E=-1!==t.indexOf("Map"),m=-1!==t.indexOf("Weak"),g=E?"set":"add",I=h[t],T=I&&I.prototype,N={};if(C&&isCallable(I)&&(m||T.forEach&&!fails((function(){(new I).entries().next()})))){var M=(_=a((function(a,u){ks(anInstance(a,M),{type:t,collection:new I}),null!=u&&iterate(u,a[g],{that:a,AS_ENTRIES:E})}))).prototype,O=Ls(t);bs(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(t){var a="add"==t||"set"==t;!(t in T)||m&&"clear"==t||He(M,t,(function(u,_){var h=O(this).collection;if(!a&&m&&!H(u))return"get"==t&&void 0;var E=h[t](0===u?0:u,_);return a?this:E}))})),m||As(M,"size",{configurable:!0,get:function(){return O(this).collection.size}})}else _=u.getConstructor(a,t,E,g),Cs.enable();setToStringTag(_,t,!1,!0),N[t]=_,_export({global:!0,forced:!0},N),m||u.setStrong(_,t,E)}("Map",(function(t){return function Map(){return t(this,arguments.length?arguments[0]:void 0)}}),xs);var Us=Y.Map,Fs=[].push;_export({target:"Map",stat:!0,forced:!0},{from:function from(t){var a,u,_,h,E=arguments.length,m=E>1?arguments[1]:void 0;return aConstructor(this),(a=void 0!==m)&&aCallable(m),isNullOrUndefined(t)?new this:(u=[],a?(_=0,h=functionBindContext(m,E>2?arguments[2]:void 0),iterate(t,(function(t){b(Fs,u,h(t,_++))}))):iterate(t,Fs,{that:u}),new this(u))}});_export({target:"Map",stat:!0,forced:!0},{of:function of(){return new this(vt(arguments))}});_export({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function deleteAll(){for(var t,a=anObject(this),u=aCallable(a.delete),_=!0,h=0,E=arguments.length;h<E;h++)t=b(u,a,arguments[h]),_=_&&t;return!!_}});_export({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function emplace(t,a){var u,_,h=anObject(this),E=aCallable(h.get),m=aCallable(h.has),g=aCallable(h.set);return b(m,h,t)?(u=b(E,h,t),"update"in a&&(u=a.update(u,t,h),b(g,h,t,u)),u):(_=a.insert(t,h),b(g,h,t,_),_)}});var Bs=getIterator;_export({target:"Map",proto:!0,real:!0,forced:!0},{every:function every(t){var a=anObject(this),u=Bs(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return!iterate(u,(function(t,u,h){if(!_(u,t,a))return h()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{filter:function filter(t){var a=anObject(this),u=Bs(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0),h=new(speciesConstructor(a,getBuiltIn("Map"))),E=aCallable(h.set);return iterate(u,(function(t,u){_(u,t,a)&&b(E,h,t,u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),h}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{find:function find(t){var a=anObject(this),u=Bs(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,h){if(_(u,t,a))return h(u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function findKey(t){var a=anObject(this),u=Bs(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,h){if(_(u,t,a))return h(t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var Gs=R([].push);_export({target:"Map",stat:!0,forced:!0},{groupBy:function groupBy(t,a){aCallable(a);var u=getIterator(t),_=new this,h=aCallable(_.has),E=aCallable(_.get),m=aCallable(_.set);return iterate(u,(function(t){var u=a(t);b(h,_,u)?Gs(b(E,_,u),t):b(m,_,u,[t])}),{IS_ITERATOR:!0}),_}});_export({target:"Map",proto:!0,real:!0,forced:!0},{includes:function includes(t){return iterate(Bs(anObject(this)),(function(a,u,_){if((h=u)===(E=t)||h!=h&&E!=E)return _();var h,E}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Map",stat:!0,forced:!0},{keyBy:function keyBy(t,a){var u=new this;aCallable(a);var _=aCallable(u.set);return iterate(t,(function(t){b(_,u,a(t),t)})),u}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function keyOf(t){return iterate(Bs(anObject(this)),(function(a,u,_){if(u===t)return _(a)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function mapKeys(t){var a=anObject(this),u=Bs(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0),h=new(speciesConstructor(a,getBuiltIn("Map"))),E=aCallable(h.set);return iterate(u,(function(t,u){b(E,h,_(u,t,a),u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),h}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function mapValues(t){var a=anObject(this),u=Bs(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0),h=new(speciesConstructor(a,getBuiltIn("Map"))),E=aCallable(h.set);return iterate(u,(function(t,u){b(E,h,t,_(u,t,a))}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),h}}),_export({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function merge(t){for(var a=anObject(this),u=aCallable(a.set),_=arguments.length,h=0;h<_;)iterate(arguments[h++],u,{that:a,AS_ENTRIES:!0});return a}});var Hs=TypeError;_export({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function reduce(t){var a=anObject(this),u=Bs(a),_=arguments.length<2,h=_?void 0:arguments[1];if(aCallable(t),iterate(u,(function(u,E){_?(_=!1,h=E):h=t(h,E,u,a)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),_)throw Hs("Reduce of empty map with no initial value");return h}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{some:function some(t){var a=anObject(this),u=Bs(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,h){if(_(u,t,a))return h()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var Ys=TypeError;_export({target:"Map",proto:!0,real:!0,forced:!0},{update:function update(t,a){var u=anObject(this),_=aCallable(u.get),h=aCallable(u.has),E=aCallable(u.set),m=arguments.length;aCallable(a);var g=b(h,u,t);if(!g&&m<3)throw Ys("Updating absent value");var I=g?b(_,u,t):aCallable(m>2?arguments[2]:void 0)(t,u);return b(E,u,t,a(I,t,u)),u}});var js=TypeError,Ks=function upsert(t,a){var u,_=anObject(this),h=aCallable(_.get),E=aCallable(_.has),m=aCallable(_.set),g=arguments.length>2?arguments[2]:void 0;if(!isCallable(a)&&!isCallable(g))throw js("At least one callback required");return b(E,_,t)?(u=b(h,_,t),isCallable(a)&&(u=a(u),b(m,_,t,u))):isCallable(g)&&(u=g(),b(m,_,t,u)),u};_export({target:"Map",proto:!0,real:!0,forced:!0},{upsert:Ks}),_export({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:Ks});var qs=Us,Ws=createCommonjsModule((function(t){function _getPrototypeOf(a){var u;return t.exports=_getPrototypeOf=It?bind$1(u=uo).call(u):function _getPrototypeOf(t){return t.__proto__||uo(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(a)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports})),zs=rt.indexOf,$s=R([].indexOf),Xs=!!$s&&1/$s([1],1,-0)<0,Qs=arrayMethodIsStrict("indexOf");_export({target:"Array",proto:!0,forced:Xs||!Qs},{indexOf:function indexOf(t){var a=arguments.length>1?arguments[1]:void 0;return Xs?$s(this,t,a)||0:zs(this,t,a)}});var Js=entryVirtual("Array").indexOf,Zs=Array.prototype,indexOf=function(t){var a=t.indexOf;return t===Zs||j(Zs,t)&&a===Zs.indexOf?Js:a},ec=createCommonjsModule((function(t){t.exports=function _isNativeFunction(t){var a;return-1!==indexOf(a=Function.toString.call(t)).call(a,"[native code]")},t.exports.__esModule=!0,t.exports.default=t.exports})),tc=getBuiltIn("Reflect","construct"),rc=Object.prototype,nc=[].push,oc=fails((function(){function F(){}return!(tc((function(){}),[],F)instanceof F)})),ic=!fails((function(){tc((function(){}))})),ac=oc||ic;_export({target:"Reflect",stat:!0,forced:ac,sham:ac},{construct:function construct(t,a){aConstructor(t),anObject(a);var u=arguments.length<3?t:aConstructor(arguments[2]);if(ic&&!oc)return tc(t,a,u);if(t==u){switch(a.length){case 0:return new t;case 1:return new t(a[0]);case 2:return new t(a[0],a[1]);case 3:return new t(a[0],a[1],a[2]);case 4:return new t(a[0],a[1],a[2],a[3])}var _=[null];return T(nc,_,a),new(T(St,t,_))}var h=u.prototype,E=pt(H(h)?h:rc),m=T(t,E,a);return H(m)?m:E}});var sc=Y.Reflect.construct,cc=createCommonjsModule((function(t){t.exports=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!sc)return!1;if(sc.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(sc(Boolean,[],(function(){}))),!0}catch(t){return!1}},t.exports.__esModule=!0,t.exports.default=t.exports})),lc=createCommonjsModule((function(t){function _construct(a,u,_){var h;cc()?(t.exports=_construct=bind$1(h=sc).call(h),t.exports.__esModule=!0,t.exports.default=t.exports):(t.exports=_construct=function _construct(t,a,u){var _=[null];_.push.apply(_,a);var h=new(bind$1(Function).apply(t,_));return u&&Ct(h,u.prototype),h},t.exports.__esModule=!0,t.exports.default=t.exports);return _construct.apply(null,arguments)}t.exports=_construct,t.exports.__esModule=!0,t.exports.default=t.exports})),uc=createCommonjsModule((function(t){function _wrapNativeSuper(a){var u="function"==typeof qs?new qs:void 0;return t.exports=_wrapNativeSuper=function _wrapNativeSuper(t){if(null===t||!ec(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==u){if(u.has(t))return u.get(t);u.set(t,Wrapper)}function Wrapper(){return lc(t,arguments,Ws(this).constructor)}return Wrapper.prototype=ft(t.prototype,{constructor:{value:Wrapper,enumerable:!1,writable:!0,configurable:!0}}),Ct(Wrapper,t)},t.exports.__esModule=!0,t.exports.default=t.exports,_wrapNativeSuper(a)}t.exports=_wrapNativeSuper,t.exports.__esModule=!0,t.exports.default=t.exports})),dc=getDefaultExportFromCjs(uc),_c=TypeError,createMethod$1=function(t){return function(a,u,_,h){aCallable(u);var E=toObject(a),m=U(E),g=lengthOfArrayLike(E),I=t?g-1:0,T=t?-1:1;if(_<2)for(;;){if(I in m){h=m[I],I+=T;break}if(I+=T,t?I<0:g<=I)throw _c("Reduce of empty array with no initial value")}for(;t?I>=0:g>I;I+=T)I in m&&(h=u(h,m[I],I,E));return h}},pc={left:createMethod$1(!1),right:createMethod$1(!0)}.left,hc=arrayMethodIsStrict("reduce");_export({target:"Array",proto:!0,forced:!hc||!Bo&&X>79&&X<83},{reduce:function reduce(t){var a=arguments.length;return pc(this,t,a,a>1?arguments[1]:void 0)}});var fc,Ec,mc,gc,Ic,vc,Tc,Nc,Mc,Oc,Sc,Rc,yc,Cc,Ac,bc,kc,Lc,Dc,Vc,wc,Pc,xc,Uc,Fc,Bc,Gc,Hc,Yc,jc,Kc,qc,Wc,zc,$c,Xc,Qc,Jc,Zc,el,tl,rl,nl,ol,il,al,sl,cl,ll,ul,dl,_l,pl,hl=entryVirtual("Array").reduce,fl=Array.prototype,reduce=function(t){var a=t.reduce;return t===fl||j(fl,t)&&a===fl.reduce?hl:a};!function(t){t[t.V2NIM_DATA_SYNC_TYPE_LEVEL_FULL=0]="V2NIM_DATA_SYNC_TYPE_LEVEL_FULL",t[t.V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC=1]="V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC"}(fc||(fc={})),function(t){t[t.V2NIM_DATA_SYNC_TYPE_MAIN=1]="V2NIM_DATA_SYNC_TYPE_MAIN",t[t.V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER=2]="V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER",t[t.V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER=3]="V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER"}(Ec||(Ec={})),function(t){t[t.V2NIM_DATA_SYNC_STATE_WAITING=1]="V2NIM_DATA_SYNC_STATE_WAITING",t[t.V2NIM_DATA_SYNC_STATE_SYNCING=2]="V2NIM_DATA_SYNC_STATE_SYNCING",t[t.V2NIM_DATA_SYNC_STATE_COMPLETED=3]="V2NIM_DATA_SYNC_STATE_COMPLETED"}(mc||(mc={})),function(t){t[t.V2NIM_CONVERSATION_TYPE_UNKNOWN=0]="V2NIM_CONVERSATION_TYPE_UNKNOWN",t[t.V2NIM_CONVERSATION_TYPE_P2P=1]="V2NIM_CONVERSATION_TYPE_P2P",t[t.V2NIM_CONVERSATION_TYPE_TEAM=2]="V2NIM_CONVERSATION_TYPE_TEAM",t[t.V2NIM_CONVERSATION_TYPE_SUPER_TEAM=3]="V2NIM_CONVERSATION_TYPE_SUPER_TEAM"}(gc||(gc={})),function(t){t[t.V2NIM_MESSAGE_STATUS_DEFAULT=0]="V2NIM_MESSAGE_STATUS_DEFAULT",t[t.V2NIM_MESSAGE_STATUS_REVOKE=1]="V2NIM_MESSAGE_STATUS_REVOKE",t[t.V2NIM_MESSAGE_STATUS_BACKFILL=2]="V2NIM_MESSAGE_STATUS_BACKFILL"}(Ic||(Ic={})),function(t){t[t.V2NIM_FRIEND_MODE_TYPE_ADD=1]="V2NIM_FRIEND_MODE_TYPE_ADD",t[t.V2NIM_FRIEND_MODE_TYPE_APPLY=2]="V2NIM_FRIEND_MODE_TYPE_APPLY"}(vc||(vc={})),function(t){t[t.V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED=1]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED",t[t.V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED"}(Tc||(Tc={})),function(t){t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT=0]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED=1]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED=3]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD=4]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD"}(Nc||(Nc={})),function(t){t[t.V2NIM_FRIEND_DELETION_TYPE_BY_SELF=1]="V2NIM_FRIEND_DELETION_TYPE_BY_SELF",t[t.V2NIM_FRIEND_DELETION_TYPE_BY_PEER=2]="V2NIM_FRIEND_DELETION_TYPE_BY_PEER"}(Mc||(Mc={})),function(t){t[t.V2NIM_FRIEND_VERIFY_TYPE_ADD=1]="V2NIM_FRIEND_VERIFY_TYPE_ADD",t[t.V2NIM_FRIEND_VERIFY_TYPE_APPLY=2]="V2NIM_FRIEND_VERIFY_TYPE_APPLY",t[t.V2NIM_FRIEND_VERIFY_TYPE_ACCEPT=3]="V2NIM_FRIEND_VERIFY_TYPE_ACCEPT",t[t.V2NIM_FRIEND_VERIFY_TYPE_REJECT=4]="V2NIM_FRIEND_VERIFY_TYPE_REJECT"}(Oc||(Oc={})),function(t){t[t.V2NIM_LOGIN_AUTH_TYPE_DEFAULT=0]="V2NIM_LOGIN_AUTH_TYPE_DEFAULT",t[t.V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN=1]="V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN",t[t.V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY=2]="V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY"}(Sc||(Sc={})),function(t){t[t.V2NIM_LOGIN_STATUS_LOGOUT=0]="V2NIM_LOGIN_STATUS_LOGOUT",t[t.V2NIM_LOGIN_STATUS_LOGINED=1]="V2NIM_LOGIN_STATUS_LOGINED",t[t.V2NIM_LOGIN_STATUS_LOGINING=2]="V2NIM_LOGIN_STATUS_LOGINING",t[t.V2NIM_LOGIN_STATUS_UNLOGIN=3]="V2NIM_LOGIN_STATUS_UNLOGIN"}(Rc||(Rc={})),function(t){t[t.V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN=0]="V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN",t[t.V2NIM_LOGIN_CLIENT_TYPE_ANDROID=1]="V2NIM_LOGIN_CLIENT_TYPE_ANDROID",t[t.V2NIM_LOGIN_CLIENT_TYPE_IOS=2]="V2NIM_LOGIN_CLIENT_TYPE_IOS",t[t.V2NIM_LOGIN_CLIENT_TYPE_PC=4]="V2NIM_LOGIN_CLIENT_TYPE_PC",t[t.V2NIM_LOGIN_CLIENT_TYPE_WP=8]="V2NIM_LOGIN_CLIENT_TYPE_WP",t[t.V2NIM_LOGIN_CLIENT_TYPE_WEB=16]="V2NIM_LOGIN_CLIENT_TYPE_WEB",t[t.V2NIM_LOGIN_CLIENT_TYPE_RESTFUL=32]="V2NIM_LOGIN_CLIENT_TYPE_RESTFUL",t[t.V2NIM_LOGIN_CLIENT_TYPE_MAC_OS=64]="V2NIM_LOGIN_CLIENT_TYPE_MAC_OS",t[t.V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS=65]="V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS"}(yc||(yc={})),function(t){t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE=1]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE",t[t.V2NIM_KICKED_OFFLINE_REASON_SERVER=2]="V2NIM_KICKED_OFFLINE_REASON_SERVER",t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT=3]="V2NIM_KICKED_OFFLINE_REASON_CLIENT",t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY=4]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY"}(Cc||(Cc={})),function(t){t[t.V2NIM_LOGIN_CLIENT_CHANGE_LIST=1]="V2NIM_LOGIN_CLIENT_CHANGE_LIST",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGIN=2]="V2NIM_LOGIN_CLIENT_CHANGE_LOGIN",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT=3]="V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT"}(Ac||(Ac={})),function(t){t[t.V2NIM_CONNECT_STATUS_DISCONNECTED=0]="V2NIM_CONNECT_STATUS_DISCONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTED=1]="V2NIM_CONNECT_STATUS_CONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTING=2]="V2NIM_CONNECT_STATUS_CONNECTING",t[t.V2NIM_CONNECT_STATUS_WAITING=3]="V2NIM_CONNECT_STATUS_WAITING"}(bc||(bc={})),function(t){t[t.NIM_MESSAGE_AI_STREAM_STATUS_STREAMING=-1]="NIM_MESSAGE_AI_STREAM_STATUS_STREAMING",t[t.NIM_MESSAGE_AI_STREAM_STATUS_NONE=0]="NIM_MESSAGE_AI_STREAM_STATUS_NONE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER=1]="NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER",t[t.NIM_MESSAGE_AI_STREAM_STATUS_CANCEL=2]="NIM_MESSAGE_AI_STREAM_STATUS_CANCEL",t[t.NIM_MESSAGE_AI_STREAM_STATUS_UPDATE=3]="NIM_MESSAGE_AI_STREAM_STATUS_UPDATE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE=4]="NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION=5]="NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION"}(kc||(kc={})),function(t){t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT=0]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT",t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE=1]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE",t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE=2]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE"}(Lc||(Lc={})),function(t){t[t.V2NIM_MESSAGE_AI_REGEN_OP_UPDATE=1]="V2NIM_MESSAGE_AI_REGEN_OP_UPDATE",t[t.V2NIM_MESSAGE_AI_REGEN_OP_NEW=2]="V2NIM_MESSAGE_AI_REGEN_OP_NEW"}(Dc||(Dc={})),function(t){t[t.V2NIM_MESSAGE_AI_STATUS_UNKNOW=0]="V2NIM_MESSAGE_AI_STATUS_UNKNOW",t[t.V2NIM_MESSAGE_AI_STATUS_AT=1]="V2NIM_MESSAGE_AI_STATUS_AT",t[t.V2NIM_MESSAGE_AI_STATUS_RESPONSE=2]="V2NIM_MESSAGE_AI_STATUS_RESPONSE"}(Vc||(Vc={})),function(t){t[t.V2NIM_MESSAGE_TYPE_INVALID=-1]="V2NIM_MESSAGE_TYPE_INVALID",t[t.V2NIM_MESSAGE_TYPE_TEXT=0]="V2NIM_MESSAGE_TYPE_TEXT",t[t.V2NIM_MESSAGE_TYPE_IMAGE=1]="V2NIM_MESSAGE_TYPE_IMAGE",t[t.V2NIM_MESSAGE_TYPE_AUDIO=2]="V2NIM_MESSAGE_TYPE_AUDIO",t[t.V2NIM_MESSAGE_TYPE_VIDEO=3]="V2NIM_MESSAGE_TYPE_VIDEO",t[t.V2NIM_MESSAGE_TYPE_LOCATION=4]="V2NIM_MESSAGE_TYPE_LOCATION",t[t.V2NIM_MESSAGE_TYPE_NOTIFICATION=5]="V2NIM_MESSAGE_TYPE_NOTIFICATION",t[t.V2NIM_MESSAGE_TYPE_FILE=6]="V2NIM_MESSAGE_TYPE_FILE",t[t.V2NIM_MESSAGE_TYPE_AVCHAT=7]="V2NIM_MESSAGE_TYPE_AVCHAT",t[t.V2NIM_MESSAGE_TYPE_TIPS=10]="V2NIM_MESSAGE_TYPE_TIPS",t[t.V2NIM_MESSAGE_TYPE_ROBOT=11]="V2NIM_MESSAGE_TYPE_ROBOT",t[t.V2NIM_MESSAGE_TYPE_CALL=12]="V2NIM_MESSAGE_TYPE_CALL",t[t.V2NIM_MESSAGE_TYPE_CUSTOM=100]="V2NIM_MESSAGE_TYPE_CUSTOM"}(wc||(wc={})),function(t){t[t.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR=0]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR",t[t.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND=1]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND"}(Pc||(Pc={})),function(t){t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED=-1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE=0]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK=1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE=2]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO=3]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS=4]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS=5]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER=6]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER=7]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER=8]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT=9]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER=10]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE=401]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK=402]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE=403]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO=404]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS=405]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS=410]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER=406]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER=407]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER=408]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT=411]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER=409]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER"}(xc||(xc={})),function(t){t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN=0]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS=1]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED=2]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING=3]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING"}(Uc||(Uc={})),function(t){t[t.V2NIM_MESSAGE_SENDING_STATE_UNKNOWN=0]="V2NIM_MESSAGE_SENDING_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED=1]="V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED",t[t.V2NIM_MESSAGE_SENDING_STATE_FAILED=2]="V2NIM_MESSAGE_SENDING_STATE_FAILED",t[t.V2NIM_MESSAGE_SENDING_STATE_SENDING=3]="V2NIM_MESSAGE_SENDING_STATE_SENDING"}(Fc||(Fc={})),function(t){t[t.V2NIM_QUERY_DIRECTION_DESC=0]="V2NIM_QUERY_DIRECTION_DESC",t[t.V2NIM_QUERY_DIRECTION_ASC=1]="V2NIM_QUERY_DIRECTION_ASC"}(Bc||(Bc={})),function(t){t[t.V2NIM_CLEAR_HISTORY_MODE_ALL=0]="V2NIM_CLEAR_HISTORY_MODE_ALL",t[t.V2NIM_CLEAR_HISTORY_MODE_LOCAL=1]="V2NIM_CLEAR_HISTORY_MODE_LOCAL"}(Gc||(Gc={})),function(t){t[t.V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED=0]="V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED",t[t.V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY=1]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY=2]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY=3]="V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY=4]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY=5]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY"}(Hc||(Hc={})),function(t){t[t.V2NIM_MESSAGE_PIN_STATE_NOT_PINNED=0]="V2NIM_MESSAGE_PIN_STATE_NOT_PINNED",t[t.V2NIM_MESSAGE_PIN_STATE_PINNED=1]="V2NIM_MESSAGE_PIN_STATE_PINNED",t[t.V2NIM_MESSAGE_PIN_STATE_UPDATED=2]="V2NIM_MESSAGE_PIN_STATE_UPDATED"}(Yc||(Yc={})),function(t){t[t.V2NIM_QUICK_COMMENT_STATE_ADD=1]="V2NIM_QUICK_COMMENT_STATE_ADD",t[t.V2NIM_QUICK_COMMENT_STATE_REMOVE=2]="V2NIM_QUICK_COMMENT_STATE_REMOVE"}(jc||(jc={})),function(t){t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_NONE=0]="V2NIM_CLIENT_ANTISPAM_OPERATE_NONE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE=1]="V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD=2]="V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD=3]="V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD"}(Kc||(Kc={})),function(t){t[t.V2NIM_SORT_ORDER_DESC=0]="V2NIM_SORT_ORDER_DESC",t[t.V2NIM_SORT_ORDER_ASC=1]="V2NIM_SORT_ORDER_ASC"}(qc||(qc={})),function(t){t[t.P2P_DELETE_MSG=7]="P2P_DELETE_MSG",t[t.TEAM_DELETE_MSG=8]="TEAM_DELETE_MSG",t[t.SUPERTEAM_DELETE_MSG=12]="SUPERTEAM_DELETE_MSG",t[t.P2P_ONE_WAY_DELETE_MSG=13]="P2P_ONE_WAY_DELETE_MSG",t[t.TEAM_ONE_WAY_DELETE_MSG=14]="TEAM_ONE_WAY_DELETE_MSG",t[t.CUSTOM_P2P_MSG=100]="CUSTOM_P2P_MSG",t[t.CUSTOM_TEAM_MSG=101]="CUSTOM_TEAM_MSG",t[t.CUSTOM_SUPERTEAM_MSG=103]="CUSTOM_SUPERTEAM_MSG"}(Wc||(Wc={})),function(t){t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF",t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_ON=1]="V2NIM_TEAM_MESSAGE_MUTE_MODE_ON",t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON=2]="V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON"}(zc||(zc={})),function(t){t[t.V2NIM_P2P_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_P2P_MESSAGE_MUTE_MODE_OFF",t[t.V2NIM_P2P_MESSAGE_MUTE_MODE_ON=1]="V2NIM_P2P_MESSAGE_MUTE_MODE_ON"}($c||($c={})),function(t){t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL=0]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL",t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL=1]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL",t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER"}(Xc||(Xc={})),function(t){t[t.V2NIM_TEAM_TYPE_INVALID=0]="V2NIM_TEAM_TYPE_INVALID",t[t.V2NIM_TEAM_TYPE_ADVANCED=1]="V2NIM_TEAM_TYPE_ADVANCED",t[t.V2NIM_TEAM_TYPE_SUPER=2]="V2NIM_TEAM_TYPE_SUPER"}(Qc||(Qc={})),function(t){t[t.V2NIM_TEAM_JOIN_MODE_FREE=0]="V2NIM_TEAM_JOIN_MODE_FREE",t[t.V2NIM_TEAM_JOIN_MODE_APPLY=1]="V2NIM_TEAM_JOIN_MODE_APPLY",t[t.V2NIM_TEAM_JOIN_MODE_INVITE=2]="V2NIM_TEAM_JOIN_MODE_INVITE"}(Jc||(Jc={})),function(t){t[t.V2NIM_TEAM_AGREE_MODE_AUTH=0]="V2NIM_TEAM_AGREE_MODE_AUTH",t[t.V2NIM_TEAM_AGREE_MODE_NO_AUTH=1]="V2NIM_TEAM_AGREE_MODE_NO_AUTH"}(Zc||(Zc={})),function(t){t[t.V2NIM_TEAM_INVITE_MODE_MANAGER=0]="V2NIM_TEAM_INVITE_MODE_MANAGER",t[t.V2NIM_TEAM_INVITE_MODE_ALL=1]="V2NIM_TEAM_INVITE_MODE_ALL"}(el||(el={})),function(t){t[t.V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER",t[t.V2NIM_TEAM_UPDATE_INFO_MODE_ALL=1]="V2NIM_TEAM_UPDATE_INFO_MODE_ALL"}(tl||(tl={})),function(t){t[t.V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN=0]="V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN",t[t.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL=1]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL",t[t.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL=3]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL"}(rl||(rl={})),function(t){t[t.V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER",t[t.V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL=1]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL"}(nl||(nl={})),function(t){t[t.V2NIM_TEAM_MEMBER_ROLE_NORMAL=0]="V2NIM_TEAM_MEMBER_ROLE_NORMAL",t[t.V2NIM_TEAM_MEMBER_ROLE_OWNER=1]="V2NIM_TEAM_MEMBER_ROLE_OWNER",t[t.V2NIM_TEAM_MEMBER_ROLE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_MANAGER"}(ol||(ol={})),function(t){t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION=0]="V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION=1]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION=2]="V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION=3]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION"}(il||(il={})),function(t){t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_INIT=0]="V2NIM_TEAM_JOIN_ACTION_STATUS_INIT",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED=1]="V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED=2]="V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED=3]="V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED"}(al||(al={})),function(t){t[t.teamApply=0]="teamApply",t[t.teamApplyReject=1]="teamApplyReject",t[t.teamInvite=2]="teamInvite",t[t.teamInviteReject=3]="teamInviteReject",t[t.tlistUpdate=4]="tlistUpdate",t[t.superTeamApply=15]="superTeamApply",t[t.superTeamApplyReject=16]="superTeamApplyReject",t[t.superTeamInvite=17]="superTeamInvite",t[t.superTeamInviteReject=18]="superTeamInviteReject"}(sl||(sl={})),function(t){t[t.V2NIM_AI_MODEL_TYPE_UNKNOW=0]="V2NIM_AI_MODEL_TYPE_UNKNOW",t[t.V2NIM_AI_MODEL_TYPE_QWEN=1]="V2NIM_AI_MODEL_TYPE_QWEN",t[t.V2NIM_AI_MODEL_TYPE_AZURE=2]="V2NIM_AI_MODEL_TYPE_AZURE",t[t.V2NIM_AI_MODEL_TYPE_PRIVATE=3]="V2NIM_AI_MODEL_TYPE_PRIVATE"}(cl||(cl={})),function(t){t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE=0]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL=2]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE=4]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION=5]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION"}(ll||(ll={})),function(t){t.V2NIM_AI_MODEL_ROLE_TYPE_SYSTEM="system",t.V2NIM_AI_MODEL_ROLE_TYPE_USER="user",t.V2NIM_AI_MODEL_ROLE_TYPE_ASSISTANT="assistant"}(ul||(ul={})),function(t){t[t.V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN=0]="V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CLOSE=1]="V2NIM_SIGNALLING_EVENT_TYPE_CLOSE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_JOIN=2]="V2NIM_SIGNALLING_EVENT_TYPE_JOIN",t[t.V2NIM_SIGNALLING_EVENT_TYPE_INVITE=3]="V2NIM_SIGNALLING_EVENT_TYPE_INVITE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE=4]="V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_REJECT=5]="V2NIM_SIGNALLING_EVENT_TYPE_REJECT",t[t.V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT=6]="V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT",t[t.V2NIM_SIGNALLING_EVENT_TYPE_LEAVE=7]="V2NIM_SIGNALLING_EVENT_TYPE_LEAVE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CONTROL=8]="V2NIM_SIGNALLING_EVENT_TYPE_CONTROL"}(dl||(dl={})),function(t){t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO=1]="V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO",t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO=2]="V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO",t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM=3]="V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM"}(_l||(_l={})),function(t){t[t.V2NIM_USER_STATUS_TYPE_UNKNOWN=0]="V2NIM_USER_STATUS_TYPE_UNKNOWN",t[t.V2NIM_USER_STATUS_TYPE_LOGIN=1]="V2NIM_USER_STATUS_TYPE_LOGIN",t[t.V2NIM_USER_STATUS_TYPE_LOGOUT=2]="V2NIM_USER_STATUS_TYPE_LOGOUT",t[t.V2NIM_USER_STATUS_TYPE_DISCONNECT=3]="V2NIM_USER_STATUS_TYPE_DISCONNECT"}(pl||(pl={}));var El={V2NIM_ERROR_CODE_UNKNOWN:{code:0,message:"unknown error"},V2NIM_ERROR_CODE_SUCCESS:{code:200,message:"success"},V2NIM_ERROR_CODE_HANDSHAKE:{code:201,message:"handshake error"},V2NIM_ERROR_CODE_REQUEST_TEMPERARY_FORBIDDEN:{code:398,message:"request temprary forbidden"},V2NIM_ERROR_CODE_SERVER_UNIT_ERROR:{code:399,message:"server unit error"},V2NIM_ERROR_CODE_FORBIDDEN:{code:403,message:"forbidden"},V2NIM_ERROR_CODE_NOT_FOUND:{code:404,message:"not found"},V2NIM_ERROR_CODE_PARAMETER_ERROR:{code:414,message:"parameter error"},V2NIM_ERROR_CODE_RATE_LIMIT_REACHED:{code:416,message:"rate limit reached"},V2NIM_ERROR_CODE_MULTI_LOGIN_FORBIDDEN:{code:417,message:"multi login forbidden"},V2NIM_ERROR_CODE_SERVER_INTERNAL_ERROR:{code:500,message:"server internal error"},V2NIM_ERROR_CODE_SERVER_BUSY:{code:503,message:"server busy"},V2NIM_ERROR_CODE_APP_UNREACHABLE:{code:511,message:"app server unreachable"},V2NIM_ERROR_CODE_SERVICE_UNAVAILABLE:{code:514,message:"service unavailable"},V2NIM_ERROR_CODE_PROTOCOL_BLACKHOLE_FILTERED:{code:599,message:"protocol filtered by blackhole rule"},V2NIM_ERROR_CODE_NO_PERMISSION:{code:997,message:"appid has no permission to call the protocol"},V2NIM_ERROR_CODE_UNPACK_ERROR:{code:998,message:"unpack error"},V2NIM_ERROR_CODE_PACK_ERROR:{code:999,message:"pack error"},V2NIM_ERROR_CODE_IM_DISABLED:{code:101301,message:"IM disabled"},V2NIM_ERROR_CODE_SERVICE_ADDRESS_INVALID:{code:101302,message:"service address invalid"},V2NIM_ERROR_CODE_APPKEY_NOT_EXIST:{code:101303,message:"appkey not exist"},V2NIM_ERROR_CODE_BUNDLEID_CHECK_FAILED:{code:101304,message:"bundleid check failed"},V2NIM_ERROR_CODE_APPKEY_BLOCKED:{code:101403,message:"appkey blocked"},V2NIM_ERROR_CODE_INVALID_TOKEN:{code:102302,message:"invalid token"},V2NIM_ERROR_CODE_ROBOT_NOT_ALLOWED:{code:102303,message:"robot not allowed"},V2NIM_ERROR_CODE_ACCOUNT_NOT_EXIST:{code:102404,message:"account not exist"},V2NIM_ERROR_CODE_ACCOUNT_CHAT_BANNED:{code:102421,message:"account chat banned"},V2NIM_ERROR_CODE_ACCOUNT_BANNED:{code:102422,message:"account banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_BLOCK_LIST:{code:102426,message:"account in block list"},V2NIM_ERROR_CODE_USER_PROFILE_NOT_EXIST:{code:103404,message:"user profile not exist"},V2NIM_ERROR_CODE_USER_PROFILE_HIT_ANTISPAM:{code:103451,message:"user profile hit antispam"},V2NIM_ERROR_CODE_PEER_FRIEND_LIMIT:{code:104301,message:"peer friend limit"},V2NIM_ERROR_CODE_FRIEND_APPLICATION_NOT_EXIST:{code:104302,message:"friend application not exist"},V2NIM_ERROR_CODE_FRIEND_NOT_EXIST:{code:104404,message:"friend not exist"},V2NIM_ERROR_CODE_FRIEND_ALREADY_EXIST:{code:104405,message:"friend already exist"},V2NIM_ERROR_CODE_SELF_FRIEND_OPERATION_NOT_ALLOWED:{code:104429,message:"self friend operation not allowed"},V2NIM_ERROR_CODE_FRIEND_LIMIT:{code:104435,message:"friend limit"},V2NIM_ERROR_CODE_FRIEND_OPERATION_RATE_LIMIT:{code:104449,message:"friend operation rate limit"},V2NIM_ERROR_CODE_FRIEND_HIT_ANTISPAM:{code:104451,message:"friend hit antispam"},V2NIM_ERROR_CODE_SELF_MUTE_OPERATION_NOT_ALLOWED:{code:105429,message:"self mute operation not allowed"},V2NIM_ERROR_CODE_MUTE_LIST_LIMIT:{code:105435,message:"mute list limit"},V2NIM_ERROR_CODE_SELF_BLOCK_LIST_OPERATION_NOT_ALLOWED:{code:106429,message:"self block list operation not allowed"},V2NIM_ERROR_CODE_BLOCK_LIST_LIMIT:{code:106435,message:"block list limit"},V2NIM_ERROR_CODE_REVOKE_THIRD_PARTY_MESSAGE_NOT_ALLOWED:{code:107301,message:"revoke third party message not allowed"},V2NIM_ERROR_CODE_SHORT_TO_LONG_URL_FAILED:{code:107307,message:"short to long URL failed"},V2NIM_ERROR_CODE_URL_INVALID:{code:107308,message:"URL invalid"},V2NIM_ERROR_CODE_DURATION_OUT_OF_RANGE:{code:107309,message:"duration out of range"},V2NIM_ERROR_CODE_GET_FILE_META_INFO_FAILED:{code:107310,message:"get file meta info failed"},V2NIM_ERROR_CODE_AUDIO_FILE_SIZE_LIMIT:{code:107311,message:"audio file size limit"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_TIMEOUT:{code:107312,message:"voice to text timeout"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FAILED:{code:107313,message:"voice to text failed"},V2NIM_ERROR_CODE_REVOKE_EXCEED_TIME_LIMIT:{code:107314,message:"revoke message exceed time limit"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_NOT_ALLOWED:{code:107315,message:"revoke specific message not allowed"},V2NIM_ERROR_CODE_FORCE_PUSH_LIST_LIMIT:{code:107316,message:"force push list limit"},V2NIM_ERROR_CODE_TEAM_MESSAGE_RECEIPT_RATE_LIMIT:{code:107317,message:"team message receipt rate limit"},V2NIM_ERROR_CODE_SNAPSHOT_NOT_EXIST:{code:107318,message:"snapshot not exist"},V2NIM_ERROR_CODE_PIN_LIMIT:{code:107319,message:"pin limit"},V2NIM_ERROR_CODE_PIN_NOT_EXIST:{code:107320,message:"pin not exist"},V2NIM_ERROR_CODE_QUICK_COMMENT_LIMIT:{code:107321,message:"quick comment limit"},V2NIM_ERROR_CODE_PIN_ALREADY_EXIST:{code:107322,message:"pin already exist"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FUNCTION_DISABLED:{code:107333,message:"voice to text function disabled"},V2NIM_ERROR_CODE_CLOUD_SEARCH_FUNCTION_DISABLED:{code:107334,message:"cloud search function disabled"},V2NIM_ERROR_CODE_ONE_WAY_DELETE_FUNCTION_DISABLED:{code:107335,message:"one-way delete function disabled"},V2NIM_ERRPR_CODE_ONEWAY_DELETION_NOT_ALLOW_FOR_TARGET_MESSAGES:{code:107338,message:"one-way deletion is not allowed for target messages"},V2NIM_ERRPR_CODE_SENDER_CANNOT_INCLUDED_IN_TARGET_LIST:{code:107339,message:"The message sender cannot be included in the target list"},V2NIM_ERROR_CODE_ROBOT_CANNOT_SEND_TARGET_MESSAGE:{code:107340,message:"Robot can not send target message"},V2NIM_ERROR_CODE_PIN_TARGET_MESSAGE_NOT_ALLOWED:{code:107345,message:"Pin target message is not allowed"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_REPLY:{code:107346,message:"Target message not allowed reply"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_QUICK_COMMENT:{code:107347,message:"Target message not allowed quick comment"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_TO_SELF_NOT_ALLOWED:{code:107429,message:"revoke message to self not allowed"},V2NIM_ERROR_CODE_APP_CHAT_BANNED:{code:107410,message:"app chat banned"},V2NIM_ERROR_CODE_QUICK_COMMENT_FUNCTION_DISABLED:{code:107326,message:"quick comment function disabled"},V2NIM_ERROR_CODE_PIN_FUNCTION_DISABLED:{code:107327,message:"PIN function disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_FUNCTION_DISABLED:{code:107324,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_P2P_READ_RECEIPT_FUNCTION_DISABLED:{code:107325,message:"read receipt for p2p messages function disabled"},V2NIM_ERROR_CODE_RATE_LIMIT_FOR_MESSAGING_REACHED:{code:107323,message:"rate limit for messaging reached"},V2NIM_ERROR_CODE_MESSAGE_HIT_ANTISPAM:{code:107451,message:"message hit antispam"},V2NIM_ERROR_CODE_MESSAGE_NOT_EXIST:{code:107404,message:"message not exist"},V2NIM_ERROR_CODE_UNSENDING_MESSAGE_EXPIRED:{code:107406,message:"unsending message expired"},V2NIM_ERROR_CODE_TEAM_MARK_READ_FAILED:{code:107302,message:"sending message failed for marking message read failed for too many team members"},V2NIM_ERROR_CODE_SENDER_OR_MANAGER_PERMISSION_ONLY_REVOKE:{code:107303,message:"only sender or manager can revoke message"},V2NIM_ERROR_CODE_DELETE_SELF_MESSAGE_NOT_ALLOWED:{code:107328,message:"delete self message not allowed"},V2NIM_ERROR_CODE_NOT_CHATBOT_ACCOUNT:{code:107329,message:"is not chatbot account"},V2NIM_ERROR_CODE_MESSAGE_SENSE_REQUIRED:{code:107330,message:"sender or receiver must sense message"},V2NIM_ERROR_CODE_HIGH_PRIORITY_MESSAGE_RATE_LIMIT:{code:107304,message:"rate limit of high-priority messages exceeded"},ACK_MESSAGE_BE_HIGH_PRIORITY:{code:107305,message:"ack message should be high-priority"},V2NIM_ERROR_CODE_DUPLICATE_CLIENT_MESSAGE_ID:{code:107306,message:"duplicate client message ID"},V2NIM_ERROR_CODE_INVALID_TIME_RANGE:{code:107439,message:"invalid time range"},V2NIM_ERROR_CODE_NOT_ADVANCED_TEAM:{code:108302,message:"not advanced team"},V2NIM_ERROR_CODE_TEAM_MANAGER_LIMIT:{code:108303,message:"team manager limit"},V2NIM_ERROR_CODE_JOINED_TEAM_LIMIT:{code:108305,message:"joined team limit"},V2NIM_ERROR_CODE_TEAM_NORMAL_MEMBER_CHAT_BANNED:{code:108306,message:"team normal member chat banned"},V2NIM_ERROR_CODE_INVITED_ACCOUNT_NOT_FRIEND:{code:108307,message:"invited account not friend"},V2NIM_ERROR_CODE_REJECT_ALL_TEAM_APPLICATIONS:{code:108308,message:"reject all team applications"},V2NIM_ERROR_CODE_TARGETING_MESSAGE_FOR_TEAM_DISABLED:{code:108318,message:"Targeting messages for group chat is disabled"},V2NIM_ERROR_CODE_INCLUSIVE_AS_FALSE_NOT_ALLOWED_FOR_SUPER_TEAM:{code:108319,message:'Setting "inclusive" to false for super teams is not allowed'},V2NIM_ERROR_CODE_CANNOT_MAKE_SUPER_TEAM_MESSAGE_VISIBLE_TO_NEW_MEMBERS:{code:108320,message:"Cannot make super team targeted messages visible to new members"},V2NIM_ERROR_CODE_CANNOT_ALLOW_TARGETED_MESSAGES_INCLUSIVE_TO_NEW_MEMBERS:{code:108321,message:"Cannot allow targeted messages inclusive to new members"},V2NIM_ERROR_CODE_TEAM_NOT_EXIST:{code:108404,message:"team not exist"},V2NIM_ERROR_CODE_TEAM_ALREADY_CHAT_BANNED:{code:108420,message:"team already chat banned"},V2NIM_ERROR_CODE_ALL_TEAM_MEMBER_CHAT_BANNED:{code:108423,message:"all team member chat banned"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT:{code:108434,message:"extended super team limit"},V2NIM_ERROR_CODE_CREATED_TEAM_LIMIT:{code:108435,message:"created team limit"},V2NIM_ERROR_CODE_TEAM_INVITATION_LIMIT:{code:108437,message:"team invitation limit"},V2NIM_ERROR_CODE_TEAM_HIT_ANTISPAM:{code:108451,message:"team hit antispam"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT_NOT_CONFIGURED:{code:108304,message:"extended super team limit not configured"},V2NIM_ERROR_CODE_SUPER_TEAM_SERVICE_DISABLED:{code:108311,message:"super team service disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_RECORD_NOT_FOUND:{code:108301,message:"read receipt record for the team message not found"},V2NIM_ERROR_CODE_NOT_MANAGER:{code:108430,message:"unable to assign owner manager"},V2NIM_ERROR_CODE_ONLINE_MEMBER_COUNT_DISABLED:{code:108406,message:"number of online users service disabled"},V2NIM_ERROR_CODE_TRANSFER_DISABLED:{code:108310,message:"unable to transfer the ownership to owner"},V2NIM_ERROR_CODE_CREATE_TEAM_DISABLED:{code:108309,message:"unable to create team with more than %s people"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_CREATE_FAILED:{code:108313,message:"/ extended super team creation failed，use open api to create the team"},V2NIM_ERROR_CODE_TEAM_MESSAGE_READ_RECEIPT_DISABLED:{code:108312,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_RETRY:{code:108449,message:"an error occurred, try again"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_NOT_TEAM_MEMBER:{code:109301,message:"list of chat banned users contains non team members"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_OPERATOR:{code:109303,message:"list of chat banned users contains the operator"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_TEAM_OWNER:{code:109304,message:"list of chat banned users contains the team owner"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_MANAGER_NOT_ALLOWED:{code:109305,message:"operation on team manager not allowed"},V2NIM_ERROR_CODE_NO_TEAM_INVITE_PERMISSION:{code:109306,message:"no team invite permission"},V2NIM_ERROR_CODE_TEAM_OWNER_QUIT_NOT_ALLOWED:{code:109307,message:"team owner quit not allowed"},V2NIM_ERROR_CODE_TEAM_OWNER_IN_KICK_LIST:{code:109308,message:"list of kicked user contains the team owner"},V2NIM_ERROR_CODE_INVITE_ROBOT_ACCOUNT_NOT_ALLOWED:{code:109309,message:"invite robot account not allowed"},V2NIM_ERROR_CODE_KICK_OPERATOR_NOT_ALLOWED:{code:109310,message:"kick operator not allowed"},V2NIM_ERROR_CODE_TEAM_MEMBER_ALREADY_EXIST:{code:109311,message:"team member already exist"},V2NIM_ERROR_CODE_TEAM_INVITATION_OR_APPLICATION_NOT_EXIST:{code:109313,message:"team invitation or application not exist"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_OWNER_NOT_ALLOWED:{code:109314,message:"operation on team owner not allowed"},V2NIM_ERROR_CODE_FORCED_PUSH_LIST_INCLUDES_NON_TARGETED_ACCOUNTS:{code:109318,message:"The forced push list includes non-targeted accounts"},V2NIM_ERROR_CODE_TEAM_MEMBER_NOT_EXIST:{code:109404,message:"team member not exist"},V2NIM_ERROR_CODE_TEAM_MEMBER_CHAT_BANNED:{code:109424,message:"team member chat banned"},V2NIM_ERROR_CODE_TEAM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:109427,message:"team owner operation permission required"},V2NIM_ERROR_CODE_TEAM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:109432,message:"team owner or manager operation permission required"},V2NIM_ERROR_CODE_TEAM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:109449,message:"team member concurrent operation failed"},V2NIM_ERROR_CODE_TEAM_MEMBER_HIT_ANTISPAM:{code:109451,message:"team member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_AND_ACCOUNT_MISMATCH:{code:110302,message:"conversation and account mismatch"},V2NIM_ERROR_CODE_CONVERSATION_STICK_TOP_LIMIT:{code:110303,message:"conversation stick top limit"},V2NIM_ERROR_CODE_CONVERSATION_BELONGED_GROUP_LIMIT:{code:110304,message:"conversation belonged group limit"},V2NIM_ERROR_CODE_CONVERSATION_IS_NOT_STICK_TOP:{code:110305,message:"conversation is not stick top"},V2NIM_ERROR_CODE_STICK_TOP_DISABLED:{code:110306,message:"conversation stick top disabled"},V2NIM_ERROR_CODE_CONVERSATION_NOT_EXIST:{code:110404,message:"conversation not exist"},V2NIM_ERROR_CODE_CHATROOM_LINK_UNAVAILABLE:{code:113304,message:"chatroom link unavailable"},V2NIM_ERROR_CODE_IM_CONNECTION_ABNORMAL:{code:113305,message:"IM connection abnormal"},V2NIM_ERROR_CODE_CHATROOM_NOT_EXIST:{code:113404,message:"chatroom not exist"},V2NIM_ERROR_CODE_CHATROOM_CLOSED:{code:113406,message:"chatroom closed"},V2NIM_ERROR_CODE_CHATROOM_REPEATED_OPERATION:{code:113409,message:"chatroom repeated operation"},V2NIM_ERROR_CODE_CHATROOM_DISABLED:{code:113410,message:"chatroom disabled"},V2NIM_ERROR_CODE_ALL_CHATROOM_MEMBER_CHAT_BANNED:{code:113423,message:"all chatroom member chat banned"},V2NIM_ERROR_CODE_CHATROOM_HIT_ANTISPAM:{code:113451,message:"chatroom hit antispam"},V2NIM_ERROR_CODE_ANONYMOUS_MEMBER_FORBIDDEN:{code:114303,message:"anonymous member forbidden"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_NOT_EXIST:{code:114404,message:"chatroom member not exist"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_REPEATED_OPERATION:{code:114405,message:"chatroom member repeated operation"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CHAT_BANNED:{code:114421,message:"chatroom member chat banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_CHATROOM_BLOCK_LIST:{code:114426,message:"account in chatroom block list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:114427,message:"chatroom owner operation permission required"},V2NIM_ERROR_CODE_SELF_IN_CHATROOM_MEMBER_OPERATION_LIST:{code:114429,message:"self in chatroom member operation list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:114432,message:"chatroom owner or manager operation permission required"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_LIMIT:{code:114437,message:"chatroom member limit"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:114449,message:"chatroom member concurrent operation failed"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_HIT_ANTISPAM:{code:114451,message:"chatroom member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_NOT_EXIST:{code:116404,message:"conversation group not exist"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_LIMIT:{code:116435,message:"conversation group limit"},V2NIM_ERROR_CODE_CONVERSATIONS_IN_GROUP_LIMIT:{code:116437,message:"conversations in group limit"},V2NIM_ERROR_CODE_COLLECTION_LIMIT:{code:189301,message:"collection limit"},V2NIM_ERROR_CODE_COLLECTION_NOT_EXIST:{code:189302,message:"collection not exist"},V2NIM_ERROR_CODE_COLLECTION_CONCURRENT_OPERATION_FAILED:{code:189449,message:"collection concurrent operation failed"},V2NIM_ERROR_CODE_INTERNAL:{code:190001,message:"internal error"},V2NIM_ERROR_CODE_ILLEGAL_STATE:{code:190002,message:"illegal state"},V2NIM_ERROR_CODE_MISUSE:{code:191001,message:"misuse"},V2NIM_ERROR_CODE_CANCELLED:{code:191002,message:"operation cancelled"},V2NIM_ERROR_CODE_CALLBACK_FAILED:{code:191003,message:"callback failed"},V2NIM_ERROR_CODE_INVALID_PARAMETER:{code:191004,message:"invalid parameter"},V2NIM_ERROR_CODE_TIMEOUT:{code:191005,message:"timeout"},V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST:{code:191006,message:"resource not exist"},V2NIM_ERROR_CODE_RESOURCE_ALREADY_EXIST:{code:191007,message:"resource already exist"},V2NIM_ERROR_CODE_CONNECT_FAILED:{code:192001,message:"connect failed"},V2NIM_ERROR_CODE_CONNECT_TIMEOUT:{code:192002,message:"connect timeout"},V2NIM_ERROR_CODE_DISCONNECT:{code:192003,message:"disconnect"},V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT:{code:192004,message:"protocol timeout"},V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED:{code:192005,message:"protocol send failed"},V2NIM_ERROR_CODE_REQUEST_FAILED:{code:192006,message:"request failed"},V2NIM_ERROR_CODE_FILE_NOT_FOUND:{code:194001,message:"file not found"},V2NIM_ERROR_CODE_FILE_CREATE_FAILED:{code:194002,message:"file create failed"},V2NIM_ERROR_CODE_FILE_OPEN_FAILED:{code:194003,message:"file open failed"},V2NIM_ERROR_CODE_FILE_WRITE_FAILED:{code:194004,message:"file write failed"},V2NIM_ERROR_CODE_FILE_READ_FAILED:{code:194005,message:"file read failed"},V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:{code:194006,message:"file upload failed"},V2NIM_ERROR_CODE_FILE_DOWNLOAD_FAILED:{code:194007,message:"file download failed"},V2NIM_ERROR_CODE_CLIENT_ANTISPAM:{code:195001,message:"client anti-spam"},V2NIM_ERROR_CODE_SERVER_ANTISPAM:{code:195002,message:"server anti-spam"},V2NIM_ERROR_CODE_STREAM_OUTPUT_STOPPED:{code:189318,message:"Streaming text response stopped"},V2NIM_ERROR_CODE_STREAM_OUTPUT_GENERATED:{code:189319,message:"Streaming text response generated"},V2NIM_ERROR_CODE_STREAM_OUTPUT_ABORTED:{code:189320,message:"Streaming text response aborted due to exception"},V2NIM_ERROR_CODE_INTERRUPTION_REJECTED:{code:189321,message:"Non-streaming messages cannot be interrupted"}},ml=Za(El),gl=reduce(ml).call(ml,(function(t,a){var u=El[a];return t[a]=u.code,t}),{}),Il=reduce(ml).call(ml,(function(t,a){var u=El[a];return t[u.code]=u.message,t}),{}),vl=function(t){function V2NIMErrorImpl(a){var u;return(u=t.call(this,a.desc)||this).name="V2NIMError",u.code=a.code||0,u.desc=a.desc||Il[u.code]||Ol[u.code]||"",u.message=u.desc,u.detail=a.detail||{},u}return At(V2NIMErrorImpl,t),V2NIMErrorImpl.prototype.toString=function toString(){var t,a=this.name+"\n code: "+this.code+'\n message: "'+this.message+'"\n detail: '+(this.detail?Ms(this.detail):"");return(null===(t=null==this?void 0:this.detail)||void 0===t?void 0:t.rawError)&&(a+="\n rawError: "+this.detail.rawError.message),a},V2NIMErrorImpl}(dc(Error));var Tl=function(t){function ValidateError(a,u,_){var h;return void 0===u&&(u={}),(h=t.call(this,{code:gl.V2NIM_ERROR_CODE_PARAMETER_ERROR,detail:{reason:a,rules:_,data:u}})||this).name="validateError",h.message=h.message+"\n"+Ms(h.detail,null,2),h.data=u,h.rules=_,h}return At(ValidateError,t),ValidateError}(vl),Nl=function(t){function ValidateErrorV2(a){var u,_,h,E;return(u=t.call(this,{code:gl.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:null===(_=a.detail)||void 0===_?void 0:_.reason,rules:null===(h=a.detail)||void 0===h?void 0:h.rules,data:null===(E=a.detail)||void 0===E?void 0:E.data}})||this).name="ValidateErrorV2",u}return At(ValidateErrorV2,t),ValidateErrorV2}(vl),Ml=function(t){function UploadError(a){var u;return(u=t.call(this,ba({code:400},a))||this).desc=u.desc||"upload file error",u.message=u.desc,u.name="uploadError",u}return At(UploadError,t),UploadError}(vl),Ol={200:null,406:null,808:null,810:null,302:"The user name or password is incorrect.",405:"Parameter length too long",408:"Client request timed out",415:"Client network unavailable",422:"Account disabled",508:"Expiration date",509:"Invalid",7101:"Be pulled black",700:"Partial failure of batch operation",801:"The number of people in the team has reached the upper limit",802:"No permission",803:"The team does not exist or has not changed",804:"The user is not in the team",805:"Team type mismatch",806:"The number of teams created has reached the limit",807:"Team member not valid",809:"Already in the team",811:"The number of accounts in the forced push list exceeds the limit",812:"The team is muted",813:"Due to the limited number of team, some pull people successfully",814:"Disable team message read service",815:"Maximum number of team administrators",816:"Batch operation partial failure",9102:"Channel failure",9103:"This call has been answered / rejected at another end",10201:"Signaling: target NIM client is offline",10202:"Signaling: push is unreachable",10404:"Signaling: channel not exists",10405:"Signaling: channel already exists",10406:"Signaling: member of channel not exists",10407:"Signaling: member of channel already exists",10408:"Signaling: the invitation request does not exist or has expired",10409:"Signaling: the invitation request has been rejected",10410:"Signaling: the invitation request has been accepted",10414:"Signaling: request parameter error",10417:"Signaling: uid conflict",10419:"Signaling: the number of members of channel exceed the limit",10420:"Signaling: member is already in the channel on other client",10700:"Signaling: phased success",13002:"Abnormal chatroom status",13003:"In the blacklist",13004:"In the mute list",13006:"All members are muted, and only the administrator can speak"},Sl=["error","warn","log","debug"],Rl=function emptyFunc(){},yl=["off","error","warn","log","debug"],Cl=function(){function Logger(t,a){void 0===a&&(a={}),this.storageArr=[],this.debugLevel="off",this.timer=0,this.strategies={debug:{name:"debg",func:console.log},log:{name:"info",func:console.log},warn:{name:"warn",func:console.warn},error:{name:"erro",func:console.error}},this.debug=Rl,this.log=Rl,this.warn=Rl,this.error=Rl,this.iid=Math.round(1e3*Math.random()),this.debugLevel=includes(yl).call(yl,t)?t:"off",a.debugLevel&&(this.debugLevel=includes(yl).call(yl,a.debugLevel)?a.debugLevel:this.debugLevel),this.logStorage=!1===a.storageEnable?null:new Es.logStorage(null==a?void 0:a.storageName),this.setOptions(a),this.setLogFunc(this.debugLevel),this.setTimer(),this.open()}var t=Logger.prototype;return t.getDebugMode=function getDebugMode(){return"debug"===this.debugLevel},t.open=function open(t){var a=this;this.logStorage&&this.logStorage.open(t).then((function(){a.log("Logger::open success")})).catch((function(t){a.warn("Logger::open failed",t)}))},t.setOptions=function setOptions(t){if(t&&t.logFunc){var a=t.logFunc;for(var u in a){var _=u,h=a[_];h&&(this.strategies[_].func=h)}}},t.setLogFunc=function setLogFunc(t,a){var u=this;void 0===a&&(a="log");var _=findIndex(Sl).call(Sl,(function(a){return a===t})),h=findIndex(Sl).call(Sl,(function(t){return t===a}));forEach$1(Sl).call(Sl,(function(t,a){u[t]=function(){if(!(a>_&&a>h)){var u=slice(Array.prototype).call(arguments),E=this.strategies[t],m=this.formatArgs(u,E.name);a<=h&&this.logStorage&&this.prepareSaveLog(m,t),a<=_&&E.func(m)}}}))},t.extractLogs=function extractLogs(){var t;return this.logStorage?null===(t=this.logStorage)||void 0===t?void 0:t.extractLogs():_a.resolve("")},t.prepareSaveLog=function prepareSaveLog(t,a){this.storageArr.push({text:t,level:a,time:Ga(),iid:this.iid}),this.timer||this.setTimer(),this.storageArr.length>=100&&(this.triggerTimer(),this.storageArr=[])},t.saveLogs=function saveLogs(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var t;return Sa.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.logStorage){a.next=2;break}return a.abrupt("return");case 2:return t=this.storageArr,this.storageArr=[],a.prev=4,a.next=7,this.logStorage.addLogs(t);case 7:a.next=11;break;case 9:a.prev=9,a.t0=a.catch(4);case 11:case"end":return a.stop()}}),_callee,this,[[4,9]])})))},t.clearTimer=function clearTimer(){this.timer&&clearTimeout(this.timer),this.timer=0},t.setTimer=function setTimer(){var t;this.clearTimer(),this.timer=ls(bind$1(t=this.triggerTimer).call(t,this),5e3)},t.triggerTimer=function triggerTimer(){this.clearTimer(),this.saveLogs()},t.formatArgs=function formatArgs(t,a){var u=new Date;return"[NIM "+this.iid+" "+a+" "+(u.getMonth()+1+"-"+u.getDate()+" "+u.getHours()+":"+u.getMinutes()+":"+u.getSeconds()+":"+u.getMilliseconds())+"] "+map$7(t).call(t,(function(t){return t instanceof vl?t.toString():t instanceof Error?t&&t.message?t.message:t:"object"==typeof t?Ms(t):t})).join(" ")},t.destroy=function destroy(){this.debug=Rl,this.log=Rl,this.warn=Rl,this.error=Rl,this.saveLogs(),this.clearTimer(),this.storageArr=[],this.logStorage&&this.logStorage.close()},Logger}(),Al="https://abt-online.netease.im/v1/api/abt/client/getExperimentInfo",bl="imElite_sdk_abtest_web",kl="https://statistic.live.126.net,https://statistic-overseas.yunxinfw.com",callWithSafeIterationClosing=function(t,a,u,_){try{return _?a(anObject(u)[0],u[1]):a(u)}catch(a){iteratorClose(t,"throw",a)}},Ll=Array,Dl=!checkCorrectnessOfIteration((function(t){Array.from(t)}));_export({target:"Array",stat:!0,forced:Dl},{from:function from(t){var a=toObject(t),u=Wt(this),_=arguments.length,h=_>1?arguments[1]:void 0,E=void 0!==h;E&&(h=functionBindContext(h,_>2?arguments[2]:void 0));var m,g,I,T,N,M,O=getIteratorMethod$5(a),S=0;if(!O||this===Ll&&isArrayIteratorMethod(O))for(m=lengthOfArrayLike(a),g=u?new this(m):Ll(m);m>S;S++)M=E?h(a[S],S):a[S],createProperty(g,S,M);else for(N=(T=getIterator(a,O)).next,g=u?new this:[];!(I=b(N,T)).done;S++)M=E?callWithSafeIterationClosing(T,h,[I.value,S],!0):I.value,createProperty(g,S,M);return g.length=S,g}});var Vl=Y.Array.from,wl=getIteratorMethod$5,Pl=Backoff;function Backoff(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}Backoff.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var a=Math.random(),u=Math.floor(a*this.jitter*t);t=0==(1&Math.floor(10*a))?t-u:t+u}return 0|Math.min(t,this.max)},Backoff.prototype.reset=function(){this.attempts=0},Backoff.prototype.setMin=function(t){this.ms=t},Backoff.prototype.setMax=function(t){this.max=t},Backoff.prototype.setJitter=function(t){this.jitter=t};var xl=createCommonjsModule((function(t){var a=Object.prototype.hasOwnProperty,u="~";function Events(){}function EE(t,a,u){this.fn=t,this.context=a,this.once=u||!1}function addListener(t,a,_,h,E){if("function"!=typeof _)throw new TypeError("The listener must be a function");var m=new EE(_,h||t,E),g=u?u+a:a;return t._events[g]?t._events[g].fn?t._events[g]=[t._events[g],m]:t._events[g].push(m):(t._events[g]=m,t._eventsCount++),t}function clearEvent(t,a){0==--t._eventsCount?t._events=new Events:delete t._events[a]}function EventEmitter(){this._events=new Events,this._eventsCount=0}Object.create&&(Events.prototype=Object.create(null),(new Events).__proto__||(u=!1)),EventEmitter.prototype.eventNames=function eventNames(){var t,_,h=[];if(0===this._eventsCount)return h;for(_ in t=this._events)a.call(t,_)&&h.push(u?_.slice(1):_);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(t)):h},EventEmitter.prototype.listeners=function listeners(t){var a=u?u+t:t,_=this._events[a];if(!_)return[];if(_.fn)return[_.fn];for(var h=0,E=_.length,m=new Array(E);h<E;h++)m[h]=_[h].fn;return m},EventEmitter.prototype.listenerCount=function listenerCount(t){var a=u?u+t:t,_=this._events[a];return _?_.fn?1:_.length:0},EventEmitter.prototype.emit=function emit(t,a,_,h,E,m){var g=u?u+t:t;if(!this._events[g])return!1;var I,T,N=this._events[g],M=arguments.length;if(N.fn){switch(N.once&&this.removeListener(t,N.fn,void 0,!0),M){case 1:return N.fn.call(N.context),!0;case 2:return N.fn.call(N.context,a),!0;case 3:return N.fn.call(N.context,a,_),!0;case 4:return N.fn.call(N.context,a,_,h),!0;case 5:return N.fn.call(N.context,a,_,h,E),!0;case 6:return N.fn.call(N.context,a,_,h,E,m),!0}for(T=1,I=new Array(M-1);T<M;T++)I[T-1]=arguments[T];N.fn.apply(N.context,I)}else{var O,S=N.length;for(T=0;T<S;T++)switch(N[T].once&&this.removeListener(t,N[T].fn,void 0,!0),M){case 1:N[T].fn.call(N[T].context);break;case 2:N[T].fn.call(N[T].context,a);break;case 3:N[T].fn.call(N[T].context,a,_);break;case 4:N[T].fn.call(N[T].context,a,_,h);break;default:if(!I)for(O=1,I=new Array(M-1);O<M;O++)I[O-1]=arguments[O];N[T].fn.apply(N[T].context,I)}}return!0},EventEmitter.prototype.on=function on(t,a,u){return addListener(this,t,a,u,!1)},EventEmitter.prototype.once=function once(t,a,u){return addListener(this,t,a,u,!0)},EventEmitter.prototype.removeListener=function removeListener(t,a,_,h){var E=u?u+t:t;if(!this._events[E])return this;if(!a)return clearEvent(this,E),this;var m=this._events[E];if(m.fn)m.fn!==a||h&&!m.once||_&&m.context!==_||clearEvent(this,E);else{for(var g=0,I=[],T=m.length;g<T;g++)(m[g].fn!==a||h&&!m[g].once||_&&m[g].context!==_)&&I.push(m[g]);I.length?this._events[E]=1===I.length?I[0]:I:clearEvent(this,E)}return this},EventEmitter.prototype.removeAllListeners=function removeAllListeners(t){var a;return t?(a=u?u+t:t,this._events[a]&&clearEvent(this,a)):(this._events=new Events,this._eventsCount=0),this},EventEmitter.prototype.off=EventEmitter.prototype.removeListener,EventEmitter.prototype.addListener=EventEmitter.prototype.on,EventEmitter.prefixed=u,EventEmitter.EventEmitter=EventEmitter,t.exports=EventEmitter})),Ul="\t\n\v\f\r                　\u2028\u2029\ufeff",Fl=R("".replace),Bl="["+Ul+"]",Gl=RegExp("^"+Bl+Bl+"*"),Hl=RegExp(Bl+Bl+"*$"),createMethod=function(t){return function(a){var u=toString(requireObjectCoercible(a));return 1&t&&(u=Fl(u,Gl,"")),2&t&&(u=Fl(u,Hl,"")),u}},Yl={start:createMethod(1),end:createMethod(2),trim:createMethod(3)},jl=Yl.trim,Kl=h.parseInt,ql=h.Symbol,Wl=ql&&ql.iterator,zl=/^[+-]?0x/i,$l=R(zl.exec),Xl=8!==Kl(Ul+"08")||22!==Kl(Ul+"0x16")||Wl&&!fails((function(){Kl(Object(Wl))}))?function parseInt(t,a){var u=jl(toString(t));return Kl(u,a>>>0||($l(zl,u)?16:10))}:Kl;_export({global:!0,forced:parseInt!=Xl},{parseInt:Xl});var Ql=Y.parseInt;function get(t,a){if("object"!=typeof t||null===t)return t;for(var u=(a=a||"").split("."),_=0;_<u.length;_++){var h=u[_],E=t[h],m=indexOf(h).call(h,"["),g=indexOf(h).call(h,"]");if(-1!==m&&-1!==g&&m<g){var I=slice(h).call(h,0,m),T=Ql(slice(h).call(h,m+1,g));E=t[I],E=fs(E)?E[T]:void 0}if(null==E)return E;t=E}return t}var Jl=ro.codeAt;_export({target:"String",proto:!0},{codePointAt:function codePointAt(t){return Jl(this,t)}});var Zl=entryVirtual("String").codePointAt,eu=String.prototype,codePointAt=function(t){var a=t.codePointAt;return"string"==typeof t||t===eu||j(eu,t)&&a===eu.codePointAt?Zl:a},tu=RangeError,ru=String.fromCharCode,nu=String.fromCodePoint,ou=R([].join),iu=!!nu&&1!=nu.length;_export({target:"String",stat:!0,arity:1,forced:iu},{fromCodePoint:function fromCodePoint(t){for(var a,u=[],_=arguments.length,h=0;_>h;){if(a=+arguments[h++],toAbsoluteIndex(a,1114111)!==a)throw tu(a+" is not a valid code point");u[h]=a<65536?ru(a):ru(55296+((a-=65536)>>10),a%1024+56320)}return ou(u,"")}});var au=Y.String.fromCodePoint,su=Ar.some,cu=arrayMethodIsStrict("some");_export({target:"Array",proto:!0,forced:!cu},{some:function some(t){return su(this,t,arguments.length>1?arguments[1]:void 0)}});var lu=entryVirtual("Array").some,uu=Array.prototype,some=function(t){var a=t.some;return t===uu||j(uu,t)&&a===uu.some?lu:a},du=TypeError,_u=Object.getOwnPropertyDescriptor,pu=C&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,a){if(bt(t)&&!_u(t,"length").writable)throw du("Cannot set read only .length");return t.length=a}:function(t,a){return t.length=a},hu=TypeError,deletePropertyOrThrow=function(t,a){if(!delete t[a])throw hu("Cannot delete property "+tryToString(a)+" of "+tryToString(t))},fu=arrayMethodHasSpeciesSupport("splice"),Eu=Math.max,mu=Math.min;_export({target:"Array",proto:!0,forced:!fu},{splice:function splice(t,a){var u,_,h,E,m,g,I=toObject(this),T=lengthOfArrayLike(I),N=toAbsoluteIndex(t,T),M=arguments.length;for(0===M?u=_=0:1===M?(u=0,_=T-N):(u=M-2,_=mu(Eu(toIntegerOrInfinity(a),0),T-N)),doesNotExceedSafeInteger(T+u-_),h=arraySpeciesCreate(I,_),E=0;E<_;E++)(m=N+E)in I&&createProperty(h,E,I[m]);if(h.length=_,u<_){for(E=N;E<T-_;E++)g=E+u,(m=E+_)in I?I[g]=I[m]:deletePropertyOrThrow(I,g);for(E=T;E>T-_+u;E--)deletePropertyOrThrow(I,E-1)}else if(u>_)for(E=T-_;E>N;E--)g=E+u-1,(m=E+_-1)in I?I[g]=I[m]:deletePropertyOrThrow(I,g);for(E=0;E<u;E++)I[E+N]=arguments[E+2];return pu(I,T-_+u),h}});var gu=entryVirtual("Array").splice,Iu=Array.prototype,splice=function(t){var a=t.splice;return t===Iu||j(Iu,t)&&a===Iu.splice?gu:a},vu=function abs(t){var a;if(void 0!==t)return(a=BigNumber(t)).sign=1,a},Tu=function isArray(t){return"[object Array]"===Object.prototype.toString.call(t)},Nu=function isValidType(t){var a;return some(a=["number"==typeof t,"string"==typeof t&&t.length>0,Tu(t)&&t.length>0,t instanceof BigNumber]).call(a,(function(t){return!0===t}))},Mu="Invalid Number",Ou="Invalid Number - Division By Zero";function BigNumber(t){var a;if(!(this instanceof BigNumber))return new BigNumber(t);if(this.number=[],this.sign=1,this.rest=0,Nu(t)){if(Tu(t)){for((t.length&&"-"===t[0]||"+"===t[0])&&(this.sign="+"===t[0]?1:-1,t.shift(0)),a=t.length-1;a>=0;a--)if(!this.addDigit(t[a]))return}else for("-"!==(t=t.toString()).charAt(0)&&"+"!==t.charAt(0)||(this.sign="+"===t.charAt(0)?1:-1,t=t.substring(1)),a=t.length-1;a>=0;a--)if(!this.addDigit(Ql(t.charAt(a),10)))return}else this.number=Mu}BigNumber.prototype.addDigit=function(t){return function testDigit(t){return/^\d$/.test(t)}(t)?(this.number.push(t),this):(this.number=Mu,!1)},BigNumber.prototype._compare=function(t){var a,u;if(!Nu(t))return null;if(a=BigNumber(t),this.sign!==a.sign)return this.sign;if(this.number.length>a.number.length)return this.sign;if(this.number.length<a.number.length)return-1*this.sign;for(u=this.number.length-1;u>=0;u--){if(this.number[u]>a.number[u])return this.sign;if(this.number[u]<a.number[u])return-1*this.sign}return 0},BigNumber.prototype.gt=function(t){return this._compare(t)>0},BigNumber.prototype.gte=function(t){return this._compare(t)>=0},BigNumber.prototype.equals=function(t){return 0===this._compare(t)},BigNumber.prototype.lte=function(t){return this._compare(t)<=0},BigNumber.prototype.lt=function(t){return this._compare(t)<0},BigNumber.prototype.subtract=function(t){var a;return void 0===t?this:(a=BigNumber(t),this.sign!==a.sign?(this.number=BigNumber._add(this,a),this):(this.sign=this.lt(a)?-1:1,this.number=vu(this).lt(vu(a))?BigNumber._subtract(a,this):BigNumber._subtract(this,a),this))},BigNumber._add=function(t,a){var u,_=0,h=Math.max(t.number.length,a.number.length);for(u=0;u<h||_>0;u++)t.number[u]=(_+=(t.number[u]||0)+(a.number[u]||0))%10,_=Math.floor(_/10);return t.number},BigNumber._subtract=function(t,a){var u,_,h=0,E=t.number.length;for(u=0;u<E;u++)t.number[u]-=(a.number[u]||0)+h,t.number[u]+=10*(h=t.number[u]<0?1:0);for(u=0,E=t.number.length-1;0===t.number[E-u]&&E-u>0;)u++;u>0&&splice(_=t.number).call(_,-u);return t.number},BigNumber.prototype.multiply=function(t){if(void 0===t)return this;var a,u,_=BigNumber(t),h=0,E=[];if(this.isZero()||_.isZero())return BigNumber(0);for(this.sign*=_.sign,a=0;a<this.number.length;a++)for(h=0,u=0;u<_.number.length||h>0;u++)E[a+u]=(h+=(E[a+u]||0)+this.number[a]*(_.number[u]||0))%10,h=Math.floor(h/10);return this.number=E,this},BigNumber.prototype.divide=function(t){if(void 0===t)return this;var a,u,_=BigNumber(t),h=[],E=BigNumber(0);if(_.isZero())return this.number=Ou,this;if(this.isZero())return this.rest=BigNumber(0),this;if(this.sign*=_.sign,_.sign=1,1===_.number.length&&1===_.number[0])return this.rest=BigNumber(0),this;for(a=this.number.length-1;a>=0;a--)for(E.multiply(10),E.number[0]=this.number[a],h[a]=0;_.lte(E);)h[a]++,E.subtract(_);for(a=0,u=h.length-1;0===h[u-a]&&u-a>0;)a++;return a>0&&splice(h).call(h,-a),this.rest=E,this.number=h,this},BigNumber.prototype.mod=function(t){return this.divide(t).rest},BigNumber.prototype.isZero=function(){var t;for(t=0;t<this.number.length;t++)if(0!==this.number[t])return!1;return!0},BigNumber.prototype.toString=function(){var t,a="";if("string"==typeof this.number)return this.number;for(t=this.number.length-1;t>=0;t--)a+=this.number[t];return this.sign>0?a:"-"+a};var Su=Math.pow(2,32);function varintToBytes(t){for(var a=new Uint8Array(5),u=new DataView(a.buffer),_=0;0!=(4294967168&t);)u.setUint8(_++,127&t|128),t>>>=7;return u.setUint8(_++,127&t),slice(a).call(a,0,_)}function encodeText(t){if("function"==typeof TextEncoder)return(new TextEncoder).encode(t);var a=function textEncoder(t){for(var a=[],u=t.length,_=0;_<u;){var h=codePointAt(t).call(t,_),E=0,m=0;for(h<=127?(E=0,m=0):h<=2047?(E=6,m=192):h<=65535?(E=12,m=224):h<=2097151&&(E=18,m=240),a.push(m|h>>E),E-=6;E>=0;)a.push(128|h>>E&63),E-=6;_+=h>=65536?2:1}return a}(t);return new Uint8Array(a)}function decodeText(t){return"function"==typeof TextDecoder?new TextDecoder("utf-8").decode(t):function textDecoder(t){for(var a="",u=0;u<t.length;){var _=t[u],h=0,E=0;if(_<=127?(h=0,E=255&_):_<=223?(h=1,E=31&_):_<=239?(h=2,E=15&_):_<=244&&(h=3,E=7&_),t.length-u-h>0)for(var m=0;m<h;)E=E<<6|63&(_=t[u+m+1]),m+=1;else E=65533,h=t.length-u;a+=au(E),u+=h+1}return a}(t)}var Ru=function(){function Unpack(t){this.offset=0,this.buffer=new Uint8Array(t),this.view=new DataView(t)}var t=Unpack.prototype;return t.checkBufferBoundaryAccess=function checkBufferBoundaryAccess(){return this.offset>=this.buffer.byteLength},t.length=function length(){var t;return(null===(t=this.view)||void 0===t?void 0:t.byteLength)||0},t.getBuffer=function getBuffer(){return this.view.buffer},t.getOffset=function getOffset(){return this.offset},t.popRaw=function popRaw(t){try{var a,u=slice(a=this.buffer).call(a,this.offset,this.offset+t);return this.offset+=t,u}catch(t){throw new Error("UnpackException raw "+(t&&t.message))}},t.popByte=function popByte(){try{var t=this.view.getUint8(this.offset);return this.offset+=1,t}catch(t){throw new Error("UnpackException byte "+(t&&t.message))}},t.popVarbin=function popVarbin(){return this.popRaw(this.popVarInt())},t.popString=function popString(){try{return decodeText(this.popVarbin())}catch(t){throw new Error("UnpackException string "+(t&&t.message))}},t.popInt=function popInt(){try{var t=this.view.getUint32(this.offset,!0);return this.offset+=4,t}catch(t){throw new Error("UnpackException int "+(t&&t.message))}},t.popVarInt=function popVarInt(){var t=1,a=0,u=0,_=0;do{if(a+=(127&(u=this.popByte()))*t,t*=128,(_+=1)>5)throw new Error("Variable length quantity is too long")}while(0!=(128&u));return a},t.popLong=function popLong(){try{var t,a=function getBigUint64(t,a){void 0===a&&(a=!1);var u=new DataView(t.buffer),_=a?[4,0]:[0,4],h=_[0],E=_[1],m=u.getUint32(h,a),g=u.getUint32(E,a);return m>0?m*Su+g:g}(slice(t=this.buffer).call(t,this.offset,this.offset+8),!0);return this.offset+=8,Number(a)}catch(t){throw new Error("UnpackException long "+(t&&t.message))}},t.popShort=function popShort(){try{var t=this.view.getUint16(this.offset,!0);return this.offset+=2,t}catch(t){throw new Error("UnpackException short "+(t&&t.message))}},t.popBoolean=function popBoolean(){return this.popByte()>0},t.toString=function toString(){return Vl(new Uint8Array(this.buffer)).toString()},t.reset=function reset(){this.offset=0,this.buffer=null,this.view=null},Unpack}(),yu=function(){function PacketDecoder(t){this.packetLength=0,this.serviceId=0,this.commandId=0,this.serialId=0,this.tag=0,this.resCode=200,this.innerHeader=null,this.msgId=0,this.bodyArr=[],this.unpack=new Ru(t)}var t=PacketDecoder.prototype;return t.reset=function reset(){this.innerHeader=null,this.bodyArr=[],this.unpack.reset()},t.getBodyDetail=function getBodyDetail(){return this.bodyArr.join("")},t.unmarshalHeader=function unmarshalHeader(){var t,a=this._unmarshalHeader();this.packetLength=a.packetLength,this.serviceId=a.serviceId,this.commandId=a.commandId,this.serialId=a.serialId,this.tag=a.tag,this.resCode=a.resCode,4===a.serviceId&&includes(t=[1,2,10,11]).call(t,a.commandId)&&(this.msgId=this.unmarshalLong(),this.innerHeader=this._unmarshalHeader())},t._unmarshalHeader=function _unmarshalHeader(){var t=this.unpack.popVarInt(),a=this.unpack.popByte(),u=this.unpack.popByte(),_=this.unpack.popShort(),h=this.unpack.popByte(),E=200;return this.hasRescode(h)&&(E=this.unpack.popShort()),{packetLength:t,serviceId:a,commandId:u,serialId:_,tag:h,resCode:E}},t.hasRescode=function hasRescode(t){return 0!=((t=t||this.tag)&PacketDecoder.RES_CODE)},t.getHeader=function getHeader(){return{packetLength:this.packetLength,sid:this.serviceId,cid:this.commandId,ser:this.serialId,code:this.resCode}},t.getInnerHeader=function getInnerHeader(){return this.innerHeader?{sid:this.innerHeader.serviceId,cid:this.innerHeader.commandId}:null},t.unmarshalProperty=function unmarshalProperty(){var t=this.unpack.popVarInt(),a={};this.bodyArr.push("\nProperty("+t+") {");for(var u=0;u<t;u++){var _=this.unpack.popVarInt();this.bodyArr.push(_+":");var h=this.unpack.popString();this.bodyArr.push('"'+h.length+" "+this.unpack.getOffset()+'",'),a[_]=h}return this.bodyArr.push("},"),a},t.unmarshalPropertyArray=function unmarshalPropertyArray(){var t=this.unpack.popVarInt(),a=[];this.bodyArr.push("\nPropertyArray("+t+") [");for(var u=0;u<t;u++)a.push(this.unmarshalProperty());return this.bodyArr.push("],"),a},t.unmarshalLong=function unmarshalLong(){var t=this.unpack.popLong();return this.bodyArr.push("\nLong:"+t),t},t.unmarshalLongArray=function unmarshalLongArray(){var t=this.unpack.popVarInt(),a=[];this.bodyArr.push("\nLongArray "+t+":");for(var u=0;u<t;u++){var _=this.unpack.popLong();this.bodyArr.push(_+","),a.push(_)}return a},t.unmarshalStrArray=function unmarshalStrArray(){var t=this.unpack.popVarInt(),a=[];this.bodyArr.push("\nStrArray "+t+":");for(var u=0;u<t;u++){var _=this.unpack.popString();this.bodyArr.push(_+","),a.push(_)}return a},t.unmarshalStrLongMap=function unmarshalStrLongMap(){var t=this.unpack.popVarInt(),a={};this.bodyArr.push("\nStrLongMap "+t+":");for(var u=0;u<t;u++){var _=this.unpack.popString();this.bodyArr.push(_+",");var h=this.unpack.popLong();this.bodyArr.push(h+";"),a[_]=h}return a},t.unmarshalStrStrMap=function unmarshalStrStrMap(){var t=this.unpack.popVarInt(),a={};this.bodyArr.push("\nStrStrMap "+t+":");for(var u=0;u<t;u++){var _=this.unpack.popString();this.bodyArr.push(_+",");var h=this.unpack.popString();this.bodyArr.push(h+";"),a[_]=h}return a},t.unmarshalLongLongMap=function unmarshalLongLongMap(){var t=this.unpack.popVarInt(),a={};this.bodyArr.push("\nStrLongLongMap "+t+":");for(var u=0;u<t;u++){var _=this.unpack.popLong();this.bodyArr.push(_+",");var h=this.unpack.popLong();this.bodyArr.push(h+";"),a[_]=h}return{m_map:a}},t.unmarshalKVArray=function unmarshalKVArray(){var t=this.unpack.popVarInt(),a=[];this.bodyArr.push("\nKVArray "+t+":");for(var u=0;u<t;u++)a.push(this.unmarshalStrStrMap());return a},t.unmarshal=function unmarshal(t){var a,u=this,_=ba(ba({},this.getHeader()),{r:[]});if(this.innerHeader&&(_.r[0]=this.msgId,_.r[1]={body:[],headerPacket:this.getInnerHeader()}),!includes(a=[200,406,808,810,7101]).call(a,_.code))return Ms(_);if(this.packetLength>0&&this.packetLength>this.unpack.length())throw new Error("UnpackException packetLength("+this.packetLength+") greater than bufferLength("+this.unpack.length()+")");var h=[];return t&&forEach$1(t).call(t,(function(t){if(!u.unpack.checkBufferBoundaryAccess())switch(t.type){case"PropertyArray":h.push(u.unmarshalPropertyArray());break;case"Property":h.push(u.unmarshalProperty());break;case"Byte":h.push(u.unpack.popByte());break;case"Int":h.push(u.unpack.popInt());break;case"Bool":h.push(u.unpack.popBoolean());break;case"Long":h.push(u.unmarshalLong());break;case"LongArray":h.push(u.unmarshalLongArray());break;case"String":h.push(u.unpack.popString());break;case"StrArray":h.push(u.unmarshalStrArray());break;case"StrStrMap":h.push(u.unmarshalStrStrMap());break;case"StrLongMap":h.push(u.unmarshalStrLongMap());break;case"LongLongMap":h.push(u.unmarshalLongLongMap());break;case"KVArray":h.push(u.unmarshalKVArray())}})),this.innerHeader?_.r[1].body=h:_.r=h,Ms(_)},PacketDecoder}();function getPromiseWithAbort(t){var a={},u=new _a((function(t,u){a.abort=u}));return a.promise=_a.race([t,u]),a}yu.RES_CODE=2,_a.reject;var Cu=function(){function PromiseManager(){this.abortFns=[]}var t=PromiseManager.prototype;return t.add=function add(t){var a=getPromiseWithAbort(t);return this.abortFns.push(a.abort),a.promise},t.clear=function clear(t){var a;forEach$1(a=this.abortFns).call(a,(function(a){return a(t||new vl({code:gl.V2NIM_ERROR_CODE_CANCELLED,detail:{reason:"Aborted"}}))})),this.abortFns=[]},t.destroy=function destroy(){this.clear()},PromiseManager}(),Au={tolerantRTT:3e3,bestRTT:100,maxChances:5,enable:!0},bu={timestamp:0,rtt:0,baseClock:0,baseTime:0},ku=function(){function TimeOrigin(t,a,u){void 0===u&&(u="getServerTime"),this.serverOrigin=bu,this.config=Au,this.isSettingNTP=!1,this.currentChance=0,this.failedDelay=2e3,this.successDelay=3e5,this.timer=0,this.cmdName="getServerTime",this.core=t,this.logger=t.logger,this.promiseManager=new Cu,this.cmdName=u,a&&this.setOptions(a)}var t=TimeOrigin.prototype;return t.setOptions=function setOptions(t){this.config=ba({},Au,this.config,t)},t.reset=function reset(){this.timer&&clearTimeout(this.timer),this.promiseManager.clear(),this.serverOrigin=bu,this.currentChance=0},t.setOriginTimetick=function setOriginTimetick(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var t,a,u,_,h,E,m,g,I,T;return Sa.wrap((function _callee$(N){for(;;)switch(N.prev=N.next){case 0:if(this.config.enable){N.next=2;break}return N.abrupt("return");case 2:if(!this.isSettingNTP){N.next=4;break}return N.abrupt("return");case 4:if(!(this.currentChance>=this.config.maxChances)){N.next=6;break}return N.abrupt("return");case 6:if(t=get(this.core,"auth.status"),a=get(this.core,"status"),u=get(this.core,"V2NIMLoginService.lifeCycle.loginStatus"),"logined"===t||"logined"===a||1===u){N.next=11;break}return N.abrupt("return");case 11:return this.isSettingNTP=!0,this.currentChance++,this.timer&&clearTimeout(this.timer),this.timer=0,_="TimeOrigin::setOriginTimetick:",h=Ga(),this.core.logger.debug(_+" getServerTime start, times "+this.currentChance),N.prev=18,N.next=21,this.promiseManager.add(this.core.sendCmd(this.cmdName));case 21:m=N.sent,E=get(m,"content.time"),this.isSettingNTP=!1,N.next=33;break;case 26:return N.prev=26,N.t0=N.catch(18),g=N.t0,this.isSettingNTP=!1,this.logger.warn(_+" Calculate Delay time, getServerTime error",g),g.code!==gl.V2NIM_ERROR_CODE_CANCELLED&&(clearTimeout(this.timer),this.timer=ls(bind$1(I=this.setOriginTimetick).call(I,this),this.failedDelay)),N.abrupt("return");case 33:if(E){N.next=37;break}return this.core.logger.warn(_+" Calculate Delay time incorrect format"),this.config.enable=!1,N.abrupt("return");case 37:T=Ga()-h,this.doSet(E,T);case 39:case"end":return N.stop()}}),_callee,this,[[18,26]])})))},t.doSet=function doSet(t,a){var u,_="TimeOrigin::setOriginTimetick:";if(a>this.config.tolerantRTT)this.logger.warn(_+" denied RTT:"+a),clearTimeout(this.timer),this.timer=ls(bind$1(u=this.setOriginTimetick).call(u,this),this.failedDelay);else if(a>this.config.bestRTT){var h;this.serverOrigin.rtt&&a>=this.serverOrigin.rtt?this.logger.warn(_+" ignore RTT:"+a):(this.setServerOrigin(a,t),this.logger.log(_+" accept reluctantly RTT:"+a)),clearTimeout(this.timer),this.timer=ls(bind$1(h=this.setOriginTimetick).call(h,this),this.failedDelay)}else{var E;this.setServerOrigin(a,t),this.logger.debug(_+" accept best RTT:"+a),this.currentChance=0,clearTimeout(this.timer),this.timer=ls(bind$1(E=this.setOriginTimetick).call(E,this),this.successDelay)}},t.getNTPTime=function getNTPTime(t){if(void 0===t&&(t=this.getTimeNode()),this.checkNodeReliable(t)){var a=Math.floor(t.time-this.serverOrigin.baseTime);return this.serverOrigin.timestamp+a}return Ga()},t.checkNodeReliable=function checkNodeReliable(t){if(void 0===t&&(t=this.getTimeNode()),this.serverOrigin.timestamp){if(0===this.serverOrigin.baseClock)return!0;var a=t.clock-this.serverOrigin.baseClock,u=t.time-this.serverOrigin.baseTime;return Math.abs(u-a)<500}return!1},t.checkPerformance=function checkPerformance(){return"BROWSER"===Es.platform&&!("undefined"==typeof performance||!performance.now)},TimeOrigin.checkPerformance=function checkPerformance(){return"BROWSER"===Es.platform&&!("undefined"==typeof performance||!performance.now)},t.getTimeNode=function getTimeNode(){return{clock:this.checkPerformance()?performance.now():0,time:Ga()}},TimeOrigin.getTimeNode=function getTimeNode(){return{clock:TimeOrigin.checkPerformance()?performance.now():0,time:Ga()}},t.setServerOrigin=function setServerOrigin(t,a){this.serverOrigin={timestamp:a+Math.floor(t/2),rtt:t,baseClock:this.checkPerformance()?performance.now():0,baseTime:Ga()}},TimeOrigin}();function _createForOfIteratorHelperLoose$3(t,a){var u,_=void 0!==Jn&&wl(t)||t["@@iterator"];if(_)return bind$1(u=(_=_.call(t)).next).call(u,_);if(fs(t)||(_=function _unsupportedIterableToArray$3(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$3(t,a);var _=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Vl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?_arrayLikeToArray$3(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){_&&(t=_);var h=0;return function(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$3(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=Array(a);u<a;u++)_[u]=t[u];return _}var Lu={},Du={};function createCmd(t,a,u,_){var h=Lu[t];if(!h)return u.error("createCmd:: can not find cmd config: ",t),null;var E,m={SER:a,SID:h.sid,CID:h.cid,Q:[]};h.params&&_&&forEach$1(E=h.params).call(E,(function(t){var a=_[t.name];if(null!=a){var u=t.type,h=t.reflectMapper,E=t.select;switch(t.type){case"PropertyArray":u="ArrayMable",a=map$7(a).call(a,(function(t){return{t:"Property",v:h?serialize(t,h,E):t}}));break;case"Property":a=h?serialize(a,h,E):a;break;case"Bool":a=a?"true":"false"}m.Q.push({t:u,v:a})}}));return{packet:m,hasPacketResponse:"boolean"!=typeof h.hasPacketResponse||h.hasPacketResponse,hasPacketTimer:"boolean"!=typeof h.hasPacketTimer||h.hasPacketTimer}}function parseCmd(t,a){var u,_;try{_=JSON.parse(t)}catch(u){return void a.error('Parse command error:"'+t+'"')}var h=_.sid+"_"+_.cid,E=_.r;if(includes(u=["4_1","4_2","4_10","4_11"]).call(u,h)){var m=_.r[1].headerPacket;h=m.sid+"_"+m.cid,_.sid=m.sid,_.cid=m.cid,E=_.r[1].body}var g=Du[h],I=[];if(g){for(var T,N=_createForOfIteratorHelperLoose$3(g);!(T=N()).done;){var M=T.value;I.push(parseEachCmd(_,M.config,M.cmd,E,a))}return I}a.error("parseCmd:: mapper not exist",h,_.code)}function parseEachCmd(t,a,u,_,h){var E,m={cmd:u,raw:t,error:null,service:null==a?void 0:a.service,content:{},__receiveTimeNode:ku.getTimeNode()};if(!u||!a)return m.notFound=!0,m;(18===a.sid||a.sid>=26&&a.sid<100)&&(t.code=function toReadableCode(t){if("number"!=typeof t||t!=t)throw new vl({code:gl.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"Read code failed",rawData:""+t}});if(t<0||t>=0&&t<1e3||t>=2e4&&t<=20099)return t;var a=(65535&t)>>9;a-=a<=38?1:2;return 1e5+1e3*a+(511&t)}(t.code));var g,I=function genCmdError(t,a){var u=Il[t],_=Ol[t];return null===_?null:new vl({code:t,desc:u||_||t,detail:{cmd:a,timetag:Ga()}})}(t.code,u);if(m.error=I,m.error){if(m.error.detail.cmd=u,!(null===(E=null==a?void 0:a.ignoreErrCodes)||void 0===E?void 0:includes(E).call(E,t.code)))return m;h.warn("parseCmd:: ignore error ",m.error),m.error.detail.ignore=!0}a.response&&forEach$1(g=a.response).call(g,(function(t,a){var u=_[a],h=t.type,E=t.name,g=t.reflectMapper;if(void 0!==u)switch(h){case"Property":m.content[E]=g?deserialize(u,g):u;break;case"PropertyArray":m.content[E]=map$7(u).call(u,(function(t){return g?deserialize(t,g):t}));break;case"Int":case"Long":case"Byte":m.content[E]=+u;break;case"Bool":m.content[E]="true"===u||!0===u||1===u;break;default:m.content[E]=u}}));return m}function serialize(t,a,u){var _={};for(var h in t=function flattenObjByMapper(t,a){var u={};for(var _ in a){for(var h,E=a[_],m="number"==typeof E?_:E.access?E.access:_,g=t,I=_createForOfIteratorHelperLoose$3(m.split("."));!(h=I()).done;){var T=h.value;if(void 0===g[T]||null===g[T]){g=void 0;break}g=g[T]}void 0!==g&&(u[m]=g)}return u}(t,a),a){var E=a[h],m="number"==typeof E?h:E.access?E.access:h;if(!u||includes(u).call(u,h))if(m in t){if("number"==typeof E)_[E]=t[m];else if("object"==typeof E)if(E.converter){var g=E.converter(t[m],t);void 0!==g&&(_[E.id]=g)}else _[E.id]=t[m]}else"object"==typeof E&&E.def&&("function"==typeof E.def?_[E.id]=E.def(t):_[E.id]=E.def)}return _}function deserialize(t,a){var u={};for(var _ in t){var h=a[_];if("string"==typeof h)u[h]=t[_];else if("object"==typeof h&&"prop"in h){var E=h.access?h.access:h.prop;if(h.converter){var m=h.converter(t[_],t);void 0!==m&&(u[E]=m)}else h.type&&"number"===h.type?u[E]=+t[_]:h.type&&"boolean"===h.type?u[E]=!("0"===t[_]||!t[_]):u[E]=t[_]}}for(var g in a){var I=a[g];if(I&&void 0!==I.def){var T=I.access?I.access:I.prop;T in u||("function"==typeof I.def?u[T]=I.def(t):u[T]=I.def)}}return u=function unflattenObj(t){var a={},u=function _loop(u){var _=u.split(".");reduce(_).call(_,(function(a,h,E){return a[h]||(a[h]=isNaN(Number(_[E+1]))?_.length-1==E?t[u]:{}:[])}),a)};for(var _ in t)u(_);return a}(u),u}function registerParser(t){for(var a in ba(Lu,t.cmdConfig),t.cmdMap){var u=t.cmdMap[a],_=t.cmdConfig[u];if(_)if(fs(Du[a])){for(var h,E=!1,m=_createForOfIteratorHelperLoose$3(Du[a]);!(h=m()).done;){var g=h.value;if(g.cmd===u&&g.config.service===_.service){E=!0;break}}E||Du[a].push({config:_,cmd:u})}else Du[a]=[{config:_,cmd:u}]}}function invertSerializeItem(t){var a={};for(var u in t){var _=t[u];"number"==typeof _?a[_]=u:"object"==typeof _&&(a[_.id]={prop:u,type:_.retType,access:_.retAccess?_.retAccess:_.access?_.access:u,def:_.retDef,converter:_.retConverter})}return a}function boolToInt(t){return t?1:0}function objectToJSONString(t){if(t&&"object"==typeof t)try{return Ms(t)}catch(t){return}}function stringToJSONObject(t){if(t&&"string"==typeof t)try{return JSON.parse(t)}catch(t){return}}var Vu=function(){function Pack(){this.offset=0,this.pageSize=1024,this.capacity=1048576,this.buffer=new Uint8Array(this.pageSize),this.view=new DataView(this.buffer.buffer)}var t=Pack.prototype;return t.reset=function reset(){this.offset=0,this.buffer=null,this.view=null},t.size=function size(){return this.offset},t.getBuffer=function getBuffer(){var t;return slice(t=this.buffer).call(t,0,this.offset).buffer},t.ensureCapacity=function ensureCapacity(t){var a=this.offset+t;if(a>this.capacity)throw new Error("PackException over limit");if(a>this.buffer.byteLength){var u=Math.ceil(a/this.pageSize)*this.pageSize,_=new Uint8Array(u);_.set(this.buffer),this.buffer=_,this.view=new DataView(this.buffer.buffer)}},t.putRaw=function putRaw(t){this.ensureCapacity(t.length);try{this.buffer.set(t,this.offset),this.offset+=t.length}catch(t){throw new Error("PackException raw")}},t.putByte=function putByte(t){this.ensureCapacity(1);try{this.view.setUint8(this.offset++,t)}catch(t){throw new Error("PackException byte")}},t.putString=function putString(t){try{var a=encodeText(t);this.putVarbin(a)}catch(t){throw new Error("PackException string")}},t.putInt=function putInt(t){this.ensureCapacity(4);try{this.view.setInt32(this.offset,t,!0),this.offset+=4}catch(t){throw new Error("PackException int")}},t.putVarInt=function putVarInt(t){var a=varintToBytes(t);this.putRaw(a)},t.putBoolean=function putBoolean(t){this.ensureCapacity(1);try{this.view.setUint8(this.offset++,t?1:0)}catch(t){throw new Error("PackException boolean")}},t.putLong=function putLong(t){this.ensureCapacity(8);try{var a=function setBigUint64(t,a){void 0===a&&(a=!1);var u=new Uint8Array(8),_=new DataView(u.buffer),h=Number(t>Su-1?t/Su:0),E=Number(4294967295&t),m=a?[4,0]:[0,4],g=m[0],I=m[1];return _.setUint32(g,h,a),_.setUint32(I,E,a),u}(t,!0);this.buffer.set(a,this.offset),this.offset+=8}catch(t){throw new Error("PackException long")}},t.putStringAsLong=function putStringAsLong(t){this.ensureCapacity(8);try{var a=function setBigUint64ForNumberOverflow(t,a){var u,_;void 0===a&&(a=!1);var h=new Uint8Array(8),E=new DataView(h.buffer),m=reverse(u=BigNumber(t).divide(Su).number).call(u).join(""),g=reverse(_=BigNumber(t).mod(Su).number).call(_).join(""),I=Number(m),T=Number(g),N=a?[4,0]:[0,4],M=N[0],O=N[1];return E.setUint32(M,I,a),E.setUint32(O,T,a),h}(t,!0);this.buffer.set(a,this.offset),this.offset+=8}catch(t){throw new Error("PackException stringAsLong")}},t.putShort=function putShort(t){this.ensureCapacity(2);try{this.view.setInt16(this.offset,t,!0),this.offset+=2}catch(t){throw new Error("PackException short")}},t.putVarbin=function putVarbin(t){if(!t)return this.ensureCapacity(1),this.putVarInt(0);if(t.byteLength>Math.pow(2,31)-2)throw new Error("PackException varbin. too long");var a=varintToBytes(t.length);this.ensureCapacity(a.length+t.length);try{this.buffer.set(a,this.offset),this.offset+=a.length,this.buffer.set(t,this.offset),this.offset+=t.length}catch(t){throw new Error("PackException varbin")}},Pack}();function isConvertibleToNumber(t){if("number"!=typeof t){if(null==t)return!1;t=Number(t)}if(isNaN(t))throw new Error("Number type conversion error");return!0}function isUndefinedOrNull(t){return null==t}var wu=function(){function PacketEncoder(t,a,u){this.pack=new Vu,this.packetLength=0,this.serviceId=0,this.commandId=0,this.serialId=0,this.tag=0,this.serviceId=t,this.commandId=a,this.serialId=u}var t=PacketEncoder.prototype;return t.marshalHeader=function marshalHeader(){this.pack.putVarInt(this.packetLength),this.pack.putByte(this.serviceId),this.pack.putByte(this.commandId),this.pack.putShort(this.serialId),this.pack.putByte(this.tag)},t.marshalProperty=function marshalProperty(t){var a=this,u=Za(t),_=filter(u).call(u,(function(t){return!isUndefinedOrNull(t)}));this.pack.putVarInt(_.length),forEach$1(_).call(_,(function(u){a.pack.putVarInt(Number(u)),fs(t[u])||"[object Object]"===Object.prototype.toString.call(t[u])?a.pack.putString(Ms(t[u])):a.pack.putString(String(t[u]))}))},t.marshalPropertyArray=function marshalPropertyArray(t){var a=this,u=t.length;this.pack.putVarInt(u),forEach$1(t).call(t,(function(t){a.marshalProperty(null==t?void 0:t.v)}))},t.marshalStrArray=function marshalStrArray(t){var a=this,u=filter(t).call(t,(function(t){return!isUndefinedOrNull(t)})),_=u.length;this.pack.putVarInt(_),forEach$1(u).call(u,(function(t){a.pack.putString(String(t))}))},t.marshalLongArray=function marshalLongArray(t){var a=this,u=filter(t).call(t,(function(t){return isConvertibleToNumber(t)})),_=u.length;this.pack.putVarInt(_),forEach$1(u).call(u,(function(t){a.putLong(t)}))},t.marshalStrStrMap=function marshalStrStrMap(t){var a=this,u=Za(t),_=filter(u).call(u,(function(a){return!isUndefinedOrNull(t[a])&&!isUndefinedOrNull(a)}));this.pack.putVarInt(_.length),forEach$1(_).call(_,(function(u){a.pack.putString(String(u)),a.pack.putString(String(t[u]))}))},t.marshalStrLongMap=function marshalStrLongMap(t){var a=this,u=Za(t),_=filter(u).call(u,(function(a){return isConvertibleToNumber(t[a])&&!isUndefinedOrNull(a)}));this.pack.putVarInt(_.length),forEach$1(_).call(_,(function(u){a.pack.putString(String(u)),a.putLong(t[u])}))},t.marshalLongLongMap=function marshalLongLongMap(t){var a=this,u=Za(t),_=filter(u).call(u,(function(a){var u=Number(a);return isConvertibleToNumber(u)&&isConvertibleToNumber(t[u])}));this.pack.putVarInt(_.length),forEach$1(_).call(_,(function(u){var _=Number(u);a.putLong(_),a.putLong(t[_])}))},t.marshalKVArray=function marshalKVArray(t){var a=this,u=t.length;this.pack.putVarInt(u),forEach$1(t).call(t,(function(t){a.marshalStrStrMap(t)}))},t.putLong=function putLong(t){"string"==typeof t&&t.length>15?this.pack.putStringAsLong(t):this.pack.putLong(Number(t))},t.marshal=function marshal(t,a){var u=this;return this.marshalHeader(),a&&forEach$1(a).call(a,(function(a,_){var h,E=a.type,m=null===(h=t[_])||void 0===h?void 0:h.v;if(!isUndefinedOrNull(m))switch(E){case"PropertyArray":u.marshalPropertyArray(m);break;case"Property":u.marshalProperty(m);break;case"Byte":if(!isConvertibleToNumber(m))return;u.pack.putByte(Number(m));break;case"Int":if(!isConvertibleToNumber(m))return;u.pack.putInt(Number(m));break;case"Bool":"false"===m?m=!1:"true"===m&&(m=!0),u.pack.putBoolean(m);break;case"Long":if(!isConvertibleToNumber(m))return;u.putLong(m);break;case"LongArray":u.marshalLongArray(m);break;case"String":u.pack.putString(String(m));break;case"StrArray":u.marshalStrArray(m);break;case"StrStrMap":u.marshalStrStrMap(m);break;case"StrLongMap":u.marshalStrLongMap(m);break;case"LongLongMap":u.marshalLongLongMap(m);break;case"KVArray":u.marshalKVArray(m)}})),this.pack.getBuffer()},t.reset=function reset(){this.pack.reset()},PacketEncoder}(),Pu=Math.min,xu=[].lastIndexOf,Uu=!!xu&&1/[1].lastIndexOf(1,-0)<0,Fu=arrayMethodIsStrict("lastIndexOf"),Bu=Uu||!Fu?function lastIndexOf(t){if(Uu)return T(xu,this,arguments)||0;var a=toIndexedObject(this),u=lengthOfArrayLike(a),_=u-1;for(arguments.length>1&&(_=Pu(_,toIntegerOrInfinity(arguments[1]))),_<0&&(_=u+_);_>=0;_--)if(_ in a&&a[_]===t)return _||0;return-1}:xu;_export({target:"Array",proto:!0,forced:Bu!==[].lastIndexOf},{lastIndexOf:Bu});var Gu,Hu=entryVirtual("Array").lastIndexOf,Yu=Array.prototype,lastIndexOf=function(t){var a=t.lastIndexOf;return t===Yu||j(Yu,t)&&a===Yu.lastIndexOf?Hu:a},ju=yn.PROPER,Ku=Yl.trim;_export({target:"String",proto:!0,forced:(Gu="trim",fails((function(){return!!Ul[Gu]()||"​᠎"!=="​᠎"[Gu]()||ju&&Ul[Gu].name!==Gu})))},{trim:function trim(){return Ku(this)}});var qu=entryVirtual("String").trim,Wu=String.prototype,trim$1=function(t){var a=t.trim;return"string"==typeof t||t===Wu||j(Wu,t)&&a===Wu.trim?qu:a};var zu,$u=(zu=function _s4(){return(65536*(1+Math.random())|0).toString(16).substring(1)},function(){return zu()+zu()+zu()+zu()+zu()+zu()+zu()+zu()});function assignOptions(t,a){return function assignWith(t,a,u,_){for(var h in t=t||{},u=u||{},_=_||function(){},a=a||{}){var E=_(t[h],a[h]);t[h]=void 0===E?a[h]:E}for(var m in u){var g=_(t[m],u[m]);t[m]=void 0===g?u[m]:g}return t}({},t,a,(function(t,a){return void 0===a?t:a}))}function getFileExtension(t){var a=lastIndexOf(t).call(t,"."),u=a>-1?slice(t).call(t,a+1):"";return/^\d+$/.test(trim$1(u).call(u))&&(u=""),u}var Xu,Qu=function(t){function BaseWebsocket(a,u,_){var h;return(h=t.call(this)||this).websocket=null,h.socketConnectTimer=0,h.linkSSL=!0,h.url="",h.core=a,h.url=u,h.linkSSL=_,h.status="disconnected",h.logger=a.logger,h.connect(),h}At(BaseWebsocket,t);var a=BaseWebsocket.prototype;return a.connect=function connect(){"connecting"!==this.status&&"connected"!==this.status?(this.status="connecting",this._createWebsocket((this.linkSSL?"wss":"ws")+"://"+this.url+"/websocket")):this.logger.warn("imsocket::socket is connecting or connected",this.status)},a.close=function close(){if(this.status="disconnected",this.websocket){this.logger.log("imsocket:: close websocket");try{this.websocket.close()}catch(t){this.logger.warn("imsocket::attempt to close websocket error",t)}this.clean(),this.emit("disconnect")}},a.clean=function clean(){this.status="disconnected",clearTimeout(this.socketConnectTimer),this.websocket&&(this.socketUrl=void 0,this.websocket.onmessage=null,this.websocket.onopen=null,this.websocket.onerror=null,this.websocket.onclose=null,this.websocket=null)},a.onConnect=function onConnect(){this.status="connected",this.emit("connect"),clearTimeout(this.socketConnectTimer)},a._createWebsocket=function _createWebsocket(t){var a,u=this;this.socketConnectTimer=ls((function(){u.logger.error("imsocket::Websocket connect timeout. url: ",t),u.emit("connectFailed",new vl({code:"v2"===get(u.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:415,detail:{reason:"imsocket::Websocket connect timeout. url: "+t}}))}),this.core.options.socketConnectTimeout||8e3),this.socketUrl=t,this.websocket=new Es.WebSocket(t),this.websocket.binaryType="arraybuffer",this.websocket.onmessage=bind$1(a=this.onMessage).call(a,this),this.websocket.onclose=function(t){t=t||{},u.logger.log("imsocket::Websocket onclose done "+t.wasClean+"/"+t.code+"/"+t.reason),"connected"===u.status?(u.clean(),u.emit("disconnect")):(u.clean(),u.emit("connectFailed",new vl({code:"v2"===get(u.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_CONNECT_FAILED:414,detail:{reason:"imsocket::Websocket onclose done"}})))},this.websocket.onerror=function(t){u.logger.error("imsocket::Websocket onerror",t),"connected"===u.status?(u.clean(),u.emit("disconnect")):(u.clean(),u.emit("connectFailed",new vl({code:"v2"===get(u.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_CONNECT_FAILED:414,detail:{reason:"imsocket::Websocket onerror."}})))},this.websocket.onopen=function(){u.onConnect()}},a.onMessage=function onMessage(t){if(t.data){var a=new yu(t.data),u={sid:-1,cid:-1,ser:-1,packetLength:-1},_=null;try{a.unmarshalHeader(),u=a.getHeader(),_=a.getInnerHeader()}catch(a){this.reportBinaryError({err:a,sid:_?_.sid:null==u?void 0:u.sid,cid:_?_.cid:null==u?void 0:u.cid,rawBuf:t.data,type:"decode"})}var h=_?_.sid:u.sid,E=_?_.cid:u.cid,m=h+"_"+E,g=Du[m];if(g&&g.length>0){var I,T=g[0].config;try{I=a.unmarshal(T.response)}catch(_){var N=a.getBodyDetail();this.reportBinaryError({err:_,rawBuf:t.data,sid:h,cid:E,parseDetail:N,type:"decode"}),a.reset();var M=ba(ba({},u),{sid:h,cid:E,code:gl.V2NIM_ERROR_CODE_UNPACK_ERROR});return this.logger.error('imsocket::onMessage "'+M.sid+"_"+M.cid+'", ser '+M.ser+", packetLength "+M.packetLength+" unmarshal error. "+N+" \n",_),void this.emit("message",Ms(M))}this.emit("message",I)}else this.core.logger.warn("imsocket::onMessage cmd not found",m);a.reset()}},a.send=function send(t,a,u,_,h){var E,m,g=new wu(t,a,u),I=Lu[_],T="";try{T=Ms(h),m=g.marshal(JSON.parse(T),I.params)}catch(_){throw this.reportBinaryError({err:_,sid:t,cid:a,rawStr:T,type:"encode"}),g.reset(),new vl({code:gl.V2NIM_ERROR_CODE_PACK_ERROR,detail:{reason:t+"-"+a+", ser "+u+" marshal error",rawError:_}})}null===(E=this.websocket)||void 0===E||E.send(m),g.reset()},a.reportBinaryError=function reportBinaryError(t){var a,u,_,h=t.err,E=t.rawStr,m=t.sid,g=t.cid,I=t.type,T=t.parseDetail,N=t.rawBuf;if(N){try{_=function arrayBufferToBase64(t){if("function"!=typeof btoa)return"";for(var a="",u=new Uint8Array(t),_=u.byteLength,h=0;h<_;h++)a+=String.fromCharCode(u[h]);return u=null,btoa(a)}(N)}catch(t){_="reportBinaryError::arrayBufferToBase64 parsing failed, error: "+(null==t?void 0:t.message)+", sid: "+m+", cid: "+g,this.core.logger.error(_)}N=null}this.core.reporter.reportTraceStart("exceptions",{user_id:null===(a=this.core.auth)||void 0===a?void 0:a.account,trace_id:null===(u=this.core.clientSocket.socket)||void 0===u?void 0:u.sessionId,start_time:Ga(),action:2,exception_service:9});var M=h?(h.message+";;;"||h.code+";;;")+(T?"parseDetail: "+T+";;;":"")+(E?" rawStr: "+E:"")+(_?" rawBuf: "+_:""):"";this.core.reporter.reportTraceUpdateV2("exceptions",{code:"encode"===I?gl.V2NIM_ERROR_CODE_PACK_ERROR:gl.V2NIM_ERROR_CODE_UNPACK_ERROR,description:M,operation_type:"encode"===I?3:4,target:m+"-"+g},{asyncParams:Es.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1)},BaseWebsocket}(xl);function _createForOfIteratorHelperLoose$2(t,a){var u,_=void 0!==Jn&&wl(t)||t["@@iterator"];if(_)return bind$1(u=(_=_.call(t)).next).call(u,_);if(fs(t)||(_=function _unsupportedIterableToArray$2(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$2(t,a);var _=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Vl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?_arrayLikeToArray$2(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){_&&(t=_);var h=0;return function(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$2(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=Array(a);u<a;u++)_[u]=t[u];return _}!function(t){t[t.ACTIVE=1]="ACTIVE",t[t.KICKED=2]="KICKED",t[t.OFFLINE=3]="OFFLINE"}(Xu||(Xu={}));var Ju,Zu=function(){function V2BinaryClientSocket(t){this.isReconnect=!1,this.packetTimeout=8e3,this.linkSSL=!0,this.packetSer=1,this.backoff=new Pl({max:8e3,min:1600,jitter:.01}),this.sendingCmdMap=new qs,this.pingTimer=0,this.hasNetworkListener=!1,this.core=t,this.auth=t.auth,this.logger=t.logger,this.reporter=t.reporter,this.timerManager=t.timerManager,this.eventBus=t.eventBus,this.setListener()}var t=V2BinaryClientSocket.prototype;return t.setListener=function setListener(){var t=this;this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLoginSucc",(function(){t.isReconnect=!0}))},t.setSessionId=function setSessionId(t){this.socket&&(this.socket.sessionId=t)},t.setLinkSSL=function setLinkSSL(t){this.linkSSL=t},t.connect=function connect(t,a){var u,_;return void 0===a&&(a=!1),__awaiter(this,void 0,void 0,Sa.mark((function _callee(){var h,E,m,g;return Sa.wrap((function _callee$(I){for(;;)switch(I.prev=I.next){case 0:if(this.isReconnect=a,1!==(h=this.core.auth.getConnectStatus())){I.next=7;break}return E="clientSocket::connect status is "+h+", and would not repeat connect",m=new vl({code:gl.V2NIM_ERROR_CODE_ILLEGAL_STATE,detail:{reason:E}}),this.logger.warn(E),I.abrupt("return",_a.reject(m));case 7:return this.auth.lifeCycle.processEvent("connect"),I.prev=8,I.next=11,this.auth.doLoginStepsManager.add(this.doConnect(t));case 11:this.logger.log("clientSocketV2:: connect success with link url: "+t+", isReconnect: "+a),this.core.reporter.reportTraceUpdateV2("login",{operation_type:"TCP",target:t,code:200,mixlink:!0,succeed:!0},{asyncParams:Es.net.getNetworkStatus()}),this.auth.lifeCycle.processEvent("connectSucc"),I.next=28;break;case 16:if(I.prev=16,I.t0=I.catch(8),g=I.t0,this.core.reporter.reportTraceUpdateV2("login",{operation_type:"TCP",target:t,code:g.code||0,description:"connectFailed:"+g.message,mixlink:!0,succeed:!1},{asyncParams:Es.net.getNetworkStatus()}),g.code!==gl.V2NIM_ERROR_CODE_CANCELLED&&g.code!==gl.V2NIM_ERROR_CODE_TIMEOUT){I.next=25;break}throw null===(u=this.socket)||void 0===u||u.close(),null===(_=this.socket)||void 0===_||_.removeAllListeners(),this.socket=void 0,I.t0;case 25:throw this.logger.warn("clientSocketV2::connect failed with link url: "+t,g),this.auth.lifeCycle.processEvent("connectFail",g),I.t0;case 28:case"end":return I.stop()}}),_callee,this,[[8,16]])})))},t.doConnect=function doConnect(t){var a=this,u=!1;return new _a((function(_,h){var E;a.socket=new Qu(a.core,t,a.linkSSL),a.socket.on("connect",(function(){a.logger.log("clientSocketV2::socket on connect",t),a.core.reporterHookLinkKeep.start(),a.core.reporterHookLinkKeep.update({code:0,description:"connection begin",operation_type:0,target:t}),u=!0,_()})),a.socket.on("message",bind$1(E=a.onMessage).call(E,a)),a.socket.on("disconnect",(function(_){return __awaiter(a,void 0,void 0,Sa.mark((function _callee2(){return Sa.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return u=!0,this.logger.log("clientSocketV2::socket on disconnect "+t,_),a.next=4,this.core.reporterHookLinkKeep.update({code:(null==_?void 0:_.code)||0,description:(null==_?void 0:_.reason)||"socket on disconnect",operation_type:1,target:t});case 4:this.core.reporterHookLinkKeep.end(!1),this.doDisconnect(Xu.OFFLINE,"SocketOnDisconnect");case 6:case"end":return a.stop()}}),_callee2,this)})))})),a.socket.on("connectFailed",(function(_){u?a.ping():(a.logger.error("clientSocketV2::connectFailed:"+t+", reason:"+(_&&_.message)),a.cleanSocket()),u=!0,h(_)}))}))},t.cleanSocket=function cleanSocket(){this.socket&&("function"==typeof this.socket.removeAllListeners&&this.socket.removeAllListeners(),"function"==typeof this.socket.close&&this.socket.close(),this.socket=void 0)},t.resetSocketConfig=function resetSocketConfig(){this.backoff.reset(),this.initOnlineListener()},t.doDisconnect=function doDisconnect(t,a){if(this.logger.log("clientSocketV2::doDisconnect: type "+t+", reason ",a),0!==this.core.auth.getConnectStatus()){var u={1:"close",2:"kicked",3:"broken"}[t]||"";this.markAllCmdInvaild(new vl({code:gl.V2NIM_ERROR_CODE_DISCONNECT,detail:{reason:"Packet timeout due to instance disconnect",disconnect_reason:u}})),this.timerManager.destroy(),clearTimeout(this.pingTimer),this.cleanSocket(),t===Xu.ACTIVE||t===Xu.KICKED?this.destroyOnlineListener():t===Xu.OFFLINE&&(this.auth.lifeCycle.processEvent("connectionBroken",new vl({code:gl.V2NIM_ERROR_CODE_DISCONNECT,detail:{reason:"connection broken due to internal reasons"}})),this.logger.log("clientSocketV2::doDisconnect: pending reconnect "+this.isReconnect),this.isReconnect&&this.auth.lifeCycle.processEvent("waiting"))}else this.logger.warn("clientSocketV2::doDisconnect: already disconnected")},t.sendCmd=function sendCmd(t,a,u){var _=this,h=this.core.auth.getLoginStatus(),E=["v2Login","login","chatroomLogin","v2ChatroomLogin"],m={cmd:t};if(1!==h&&!includes(E).call(E,t))return this.logger.warn("clientSocketV2::NIM login status is "+h+", so can not sendCmd "+t),_a.reject(new vl({code:gl.V2NIM_ERROR_CODE_ILLEGAL_STATE,detail:ba({reason:"Can not sendCmd due to no logined"},m)}));var g="heartbeat"!==t,I=g?this.packetSer++:0,T=createCmd(t,I,this.logger,a);if(!T){var N=new vl({code:gl.V2NIM_ERROR_CODE_INTERNAL,detail:ba(ba({},m),{reason:"SendCmd::createCmd error: "+I+" "+t})});return this.logger.error(N),_a.reject(N)}var M=T.packet,O=T.hasPacketResponse,S=T.hasPacketTimer,R=Ms(M);g&&(this.logger.getDebugMode()?this.logger.debug("clientSocketV2::sendCmd: "+M.SID+"_"+M.CID+","+t+",ser:"+I,R):this.logger.log("clientSocketV2::sendCmd: "+M.SID+"_"+M.CID+","+t+",ser:"+I));var C=(new Date).getTime();return new _a((function(h,E){O&&_.sendingCmdMap.set(I,{cmd:t,params:a,callback:[h,E],timer:S?ls((function(){var a=new vl({code:gl.V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT,detail:ba({ser:I,reason:"Packet Timeout: ser "+I+" cmd "+t,timetag:(new Date).getTime()},m)});_.markCmdInvalid(I,a,t)}),u&&u.timeout?u.timeout:_.packetTimeout):null});try{_.socket.send(M.SID,M.CID,I,t,M.Q),O||h(M)}catch(a){var g=new vl({code:gl.V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED,detail:ba({ser:I,reason:"Unable to send packet"+(a&&a.message?": "+a.message:""),timetag:(new Date).getTime(),rawError:a},m)});_.markCmdInvalid(I,g,t),E(g)}})).catch((function(t){return __awaiter(_,void 0,void 0,Sa.mark((function _callee3(){var a,u;return Sa.wrap((function _callee3$(_){for(;;)switch(_.prev=_.next){case 0:if(a=t,u=[gl.V2NIM_ERROR_CODE_DISCONNECT,gl.V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT,gl.V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED],includes(u).call(u,a.code)){_.next=4;break}return _.abrupt("return",_a.reject(a));case 4:return this.reportSendCmdFailed(a,{sid:M.SID,cid:M.CID,ser:I},C),_.abrupt("return",_a.reject(a));case 6:case"end":return _.stop()}}),_callee3,this)})))}))},t.reportSendCmdFailed=function reportSendCmdFailed(t,a,u){var _;this.reporter.reportTraceStart("exceptions",{user_id:this.core.auth.getLoginUser(),trace_id:null===(_=this.socket)||void 0===_?void 0:_.sessionId,start_time:u,action:2,exception_service:6});var h=get(t,"detail.disconnect_reason")||"",E=t.code===gl.V2NIM_ERROR_CODE_DISCONNECT?Ms({disconnect_reason:h}):t.detail.reason;this.reporter.reportTraceUpdateV2("exceptions",{code:t.code,description:E,operation_type:1,target:a.sid+"-"+a.cid,context:""+a.ser},{asyncParams:Es.net.getNetworkStatus()}),this.reporter.reportTraceEnd("exceptions",1)},t.onMessage=function onMessage(t){var a=parseCmd(t,this.logger);if(a){var u=a[0],_=u.raw.ser;"heartbeat"!==u.cmd&&(this.logger.getDebugMode()?this.logger.debug("clientSocketV2::recvCmd "+u.raw.sid+"_"+u.raw.cid+","+u.cmd+",ser:"+_,t):this.logger.log("clientSocketV2::recvCmd "+u.raw.sid+"_"+u.raw.cid+","+u.cmd+",ser:"+_+",code:"+u.raw.code));for(var h,E=_createForOfIteratorHelperLoose$2(a);!(h=E()).done;){var m=h.value;if(m.error&&this.logger.error("clientSocketV2::onMessage packet error",m.raw.sid+"_"+m.raw.cid+", ser:"+_+",",m.error),m.notFound)return void this.logger.warn("clientSocketV2::onMessage packet not found",m.raw.sid+"_"+m.raw.cid+", ser:"+_);this.packetHandler(m)}}},t.packetHandler=function packetHandler(t){var a,u,_,h,E=this;if(t){var m=t.raw.ser,g=this.sendingCmdMap.get(m);if(g&&g.cmd===t.cmd){var I=g.callback,T=g.timer,N=g.params;if(clearTimeout(T),t.params=N,this.sendingCmdMap.delete(m),"heartbeat"===t.cmd)return void I[0]();var M=null===(u=null===(a=this.core[t.service])||void 0===a?void 0:a.process)||void 0===u?void 0:u.call(a,t);M&&"function"==typeof M.then?M.then((function(t){I[0](t)})).catch((function(t){I[1](t)})):(this.logger.log("clientSocketV2::handlerFn without promise",t.service,t.cmd),I[0](t))}else{var O=null===(h=null===(_=this.core[t.service])||void 0===_?void 0:_.process)||void 0===h?void 0:h.call(_,t);O&&"function"==typeof O.then&&O.catch((function(t){E.logger.error("clientSocketV2::no obj cache, no process handler",t)}))}}},t.markCmdInvalid=function markCmdInvalid(t,a,u){var _=this.sendingCmdMap.get(t);if(_){var h=_.callback,E=_.timer;E&&clearTimeout(E),this.sendingCmdMap.delete(t),this.logger.warn("clientSocketV2::packet "+t+", "+u+" is invalid:",a),h[1](a)}},t.markAllCmdInvaild=function markAllCmdInvaild(t){var a,u=this;this.logger.log("markAllCmdInvaild",t),forEach$1(a=this.sendingCmdMap).call(a,(function(a){var _=a.callback,h=a.timer,E=a.cmd;u.logger.log("clientSocketV2::markAllCmdInvaild:cmd "+E),h&&clearTimeout(h),_[1](t)})),this.sendingCmdMap.clear()},t.ping=function ping(){var t;return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){var a=this;return Sa.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return clearTimeout(this.pingTimer),u.prev=1,u.next=4,this.sendCmd("heartbeat");case 4:u.next=20;break;case 6:if(u.prev=6,u.t0=u.catch(1),u.t0.code!==gl.V2NIM_ERROR_CODE_DISCONNECT){u.next=11;break}return u.abrupt("return");case 11:return u.next=13,this.testHeartBeat5Timeout();case 13:if(!u.sent){u.next=20;break}return u.next=17,this.core.reporterHookLinkKeep.update({code:0,description:"Heartbeat-discovered link failure",operation_type:1,target:null===(t=this.socket)||void 0===t?void 0:t.url});case 17:return this.core.reporterHookLinkKeep.end(!0),this.doDisconnect(Xu.OFFLINE,"PingError"),u.abrupt("return");case 20:this.pingTimer=ls((function(){a.ping()}),3e4);case 21:case"end":return u.stop()}}),_callee4,this,[[1,6]])})))},t.testHeartBeat5Timeout=function testHeartBeat5Timeout(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){var t;return Sa.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:clearTimeout(this.pingTimer),t=0;case 2:if(!(t<5)){a.next=15;break}return a.prev=3,a.next=6,this.sendCmd("heartbeat",{},{timeout:3e3});case 6:return a.abrupt("return",!1);case 9:a.prev=9,a.t0=a.catch(3),this.logger.log("clientSocketV2::test heartbeat "+t+" Timeout");case 12:t++,a.next=2;break;case 15:return a.abrupt("return",!0);case 16:case"end":return a.stop()}}),_callee5,this,[[3,9]])})))},t.initOnlineListener=function initOnlineListener(){var t=this;this.hasNetworkListener||(this.logger.log("clientSocketV2::onlineListener:init"),this.hasNetworkListener=!0,Es.net.onNetworkStatusChange((function(a){t.logger.log("clientSocketV2::onlineListener:network change",a);var u=t.auth.getConnectStatus(),_=t.auth.getLoginStatus();a.isConnected&&1===_?t.ping():a.isConnected&&3===u?(t.logger.log("clientSocketV2::onlineListener:online and connectStatus is waiting, do reLogin"),t.auth.reconnect.clearReconnectTimer(),t.auth.reconnect.doReLogin()):a.isConnected||t.doDisconnect(Xu.OFFLINE,"OfflineListener")})))},t.destroyOnlineListener=function destroyOnlineListener(){this.logger.log("clientSocketV2::onlineListener:destroy"),Es.net.offNetworkStatusChange(),this.hasNetworkListener=!1},V2BinaryClientSocket}(),ed=["disconnect","connect","heartbeat","message","json","event","ack","error","noop"],td=["transport not supported","client not handshaken","unauthorized"],rd=["reconnect"],nd=function(t){function BaseWebsocket(a,u,_){var h;return(h=t.call(this)||this).websocket=null,h.socketConnectTimer=0,h.url="",h.linkSSL=!0,h.core=a,h.url=u,h.linkSSL=_,h.status="disconnected",h.logger=a.logger,h.connect(),h}At(BaseWebsocket,t);var a=BaseWebsocket.prototype;return a.connect=function connect(){var t=this;"connecting"!==this.status&&"connected"!==this.status?(this.status="connecting",this.core.adapters.request((this.linkSSL?"https":"http")+"://"+this.url+"/socket.io/1/?t="+Ga(),{method:"GET",dataType:"text",timeout:this.core.options.xhrConnectTimeout||8e3},{exception_service:6}).then((function(a){if("connecting"===t.status){var u=a.data.split(":"),_=u[0];return u[1],t.sessionId=_,t.logger.log("imsocket::XHR success. status "+t.status+", "+("connecting"===t.status?"continue websocket connection":"stop websocket connection")),t._createWebsocket((t.linkSSL?"wss":"ws")+"://"+t.url+"/socket.io/1/websocket/"+_)}})).catch((function(a){if("connecting"===t.status){var u='imsocket::XHR fail. raw message: "'+(a=a||{}).message+'", code: "'+a.code+'"',_=a.code;_="v2"===get(t.core,"options.apiVersion")?a.code===gl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT?gl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:gl.V2NIM_ERROR_CODE_CONNECT_FAILED:408===a.code?408:415;var h=new vl({code:_,detail:{reason:u,rawError:a}});t.logger.error(u),t.status="disconnected",t.emit("handshakeFailed",h)}}))):this.logger.warn("imsocket::socket is connecting or connected",this.status)},a.close=function close(){if(this.status="disconnected",this.websocket){this.logger.log("imsocket:: close websocket");try{this.websocket.send(this.encodePacket({type:"disconnect"}))}catch(t){this.logger.warn("imsocket::attempt to send encodePacket error",t)}try{this.websocket.close()}catch(t){this.logger.warn("imsocket::attempt to close websocket error",t)}this.clean(),this.emit("disconnect",{code:0,reason:"Active close websocket"})}},a.clean=function clean(){this.status="disconnected",clearTimeout(this.socketConnectTimer),this.websocket&&(this.socketUrl=void 0,this.websocket.onmessage=null,this.websocket.onopen=null,this.websocket.onerror=null,this.websocket.onclose=null,this.websocket=null)},a.onConnect=function onConnect(){this.status="connected",this.emit("connect"),clearTimeout(this.socketConnectTimer)},a._createWebsocket=function _createWebsocket(t){var a,u=this;this.socketConnectTimer=ls((function(){u.logger.error("imsocket::Websocket connect timeout. url: ",u.socketUrl),u.emit("handshakeFailed",new vl({code:"v2"===get(u.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:415,detail:{reason:"imsocket::Websocket connect timeout. url: "+u.socketUrl}}))}),this.core.options.socketConnectTimeout||8e3),this.socketUrl=t,this.websocket=new Es.WebSocket(t),this.websocket.onmessage=bind$1(a=this.onMessage).call(a,this),this.websocket.onclose=function(t){t=t||{},u.logger.log("imsocket::Websocket onclose done "+t.wasClean+"/"+t.code+"/"+t.reason),u.clean(),u.emit("disconnect",{code:t.code||0,reason:t.reason})},this.websocket.onerror=function(t){u.logger.error("imsocket::Websocket onerror",t),"logined"===u.core.status&&u.core.clientSocket.ping()}},a.onMessage=function onMessage(t){var a,u=this.decodePacket(t.data);if(u)switch(u.type){case"connect":this.onConnect();break;case"disconnect":this.close(),this.emit("disconnect",{code:0,reason:"MessageEvent type disconnect"});break;case"message":case"json":this.emit("message",u.data);break;case"event":u.name&&this.emit(u.name,u.args);break;case"error":"unauthorized"===u.reason?this.emit("connect_failed",u.reason):this.emit("error",u.reason),this.logger.error("imsocket::Websocket connect failed, onmessage type error. url: ",this.socketUrl),clearTimeout(this.socketConnectTimer),this.emit("handshakeFailed",new vl({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_CONNECT_FAILED:408,detail:{reason:"imsocket::Websocket connect failed, onMessage socket error. url: "+this.socketUrl}}));break;case"heartbeat":null===(a=this.websocket)||void 0===a||a.send(this.encodePacket({type:"heartbeat"}));break;default:this.logger.warn("imsocket::Websocket no handler type",u.type)}},a.encodePacket=function encodePacket(t){var a,u,_=t.type,h=t.id,E=void 0===h?"":h,m=t.endpoint,g=void 0===m?"":m,I=t.ack,T=null;if(!_)return"";switch(_){case"error":a=t.reason?indexOf(td).call(td,t.reason):"",u=t.advice?indexOf(rd).call(rd,t.advice):"",""===a&&""===u||(T=a+(""!==u?"+"+u:""));break;case"message":""!==t.data&&(T=t.data);break;case"event":a={name:t.name},a=t.args&&t.args.length?{name:t.name,args:t.args}:{name:t.name},T=Ms(a);break;case"json":T=Ms(t.data);break;case"connect":t.qs&&(T=t.qs);break;case"ack":T=t.ackId+(t.args&&t.args.length?"+"+Ms(t.args):"")}var N=[indexOf(ed).call(ed,_),E+("data"===I?"+":""),g];return null!=T&&N.push(T),N.join(":")},a.decodePacket=function decodePacket(t){if(t)if("�"!=t.charAt(0)){var a=t.match(/([^:]+):([0-9]+)?(\+)?:([^:]+)?:?([\s\S]*)?/);if(a){var u,_=a[1],h=a[2],E=a[3],m=a[4],g=a[5],I={type:ed[+_],endpoint:m};switch(h&&(I.id=h,I.ack=!E||"data"),I.type){case"error":u=g.split("+"),I.reason=td[+u[0]]||"";break;case"message":I.data=g||"";break;case"connect":I.qs=g||"";break;case"event":try{var T=JSON.parse(g);I.name=T.name,I.args=T.args}catch(t){this.logger.error("imsocket::parseData::type::event error",t)}I.args=I.args||[];break;case"json":try{I.data=JSON.parse(g)}catch(t){this.logger.error("imsocket::parseData::type::json error",t)}break;case"ack":if((u=g.match(/^([0-9]+)(\+)?(.*)/))&&(I.ackId=u[1],I.args=[],u[3]))try{I.args=u[3]?JSON.parse(u[3]):[]}catch(t){this.logger.error("imsocket::parseData::type::ack error",t)}}return I}}else this.logger.error("imsocket::unrecognize dataStr",slice(t).call(t,0,20))},a.send=function send(t){var a,u={data:t,type:"message",endpoint:""};null===(a=this.websocket)||void 0===a||a.send(this.encodePacket(u))},BaseWebsocket}(xl);function _createForOfIteratorHelperLoose$1(t,a){var u,_=void 0!==Jn&&wl(t)||t["@@iterator"];if(_)return bind$1(u=(_=_.call(t)).next).call(u,_);if(fs(t)||(_=function _unsupportedIterableToArray$1(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$1(t,a);var _=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Vl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?_arrayLikeToArray$1(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){_&&(t=_);var h=0;return function(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$1(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=Array(a);u<a;u++)_[u]=t[u];return _}!function(t){t[t.ACTIVE=1]="ACTIVE",t[t.KICKED=2]="KICKED",t[t.OFFLINE=3]="OFFLINE"}(Ju||(Ju={}));var od=function(){function V2ClientSocket(t){this.isReconnect=!1,this.packetTimeout=8e3,this.linkSSL=!0,this.packetSer=1,this.backoff=new Pl({max:8e3,min:1600,jitter:.01}),this.sendingCmdMap=new qs,this.pingTimer=0,this.hasNetworkListener=!1,this.core=t,this.auth=t.auth,this.logger=t.logger,this.reporter=t.reporter,this.timerManager=t.timerManager,this.eventBus=t.eventBus,this.setListener()}var t=V2ClientSocket.prototype;return t.setListener=function setListener(){var t=this;this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLoginSucc",(function(){t.isReconnect=!0}))},t.setSessionId=function setSessionId(t){this.socket&&(this.socket.sessionId=t)},t.setLinkSSL=function setLinkSSL(t){this.linkSSL=t},t.connect=function connect(t,a){var u,_;return void 0===a&&(a=!1),__awaiter(this,void 0,void 0,Sa.mark((function _callee(){var h,E,m,g;return Sa.wrap((function _callee$(I){for(;;)switch(I.prev=I.next){case 0:if(this.isReconnect=a,1!==(h=this.core.auth.getConnectStatus())){I.next=7;break}return E="clientSocket::connect status is "+h+", and would not repeat connect",m=new vl({code:gl.V2NIM_ERROR_CODE_ILLEGAL_STATE,detail:{reason:E}}),this.logger.warn(E),I.abrupt("return",_a.reject(m));case 7:return this.auth.lifeCycle.processEvent("connect"),I.prev=8,I.next=11,this.auth.doLoginStepsManager.add(this.doConnect(t));case 11:this.logger.log("clientSocketV2:: connect success with link url: "+t+", isReconnect: "+a),this.core.reporter.reportTraceUpdateV2("login",{operation_type:"TCP",target:t,code:200,mixlink:!0,succeed:!0},{asyncParams:Es.net.getNetworkStatus()}),this.auth.lifeCycle.processEvent("connectSucc"),I.next=28;break;case 16:if(I.prev=16,I.t0=I.catch(8),g=I.t0,this.core.reporter.reportTraceUpdateV2("login",{operation_type:"TCP",target:t,code:g.code||0,description:"connectFailed:"+g.message,mixlink:!0,succeed:!1},{asyncParams:Es.net.getNetworkStatus()}),g.code!==gl.V2NIM_ERROR_CODE_CANCELLED&&g.code!==gl.V2NIM_ERROR_CODE_TIMEOUT){I.next=25;break}throw null===(u=this.socket)||void 0===u||u.close(),null===(_=this.socket)||void 0===_||_.removeAllListeners(),this.socket=void 0,I.t0;case 25:throw this.logger.warn("clientSocketV2::connect failed with link url: "+t,g),this.auth.lifeCycle.processEvent("connectFail",g),I.t0;case 28:case"end":return I.stop()}}),_callee,this,[[8,16]])})))},t.doConnect=function doConnect(t){var a=this,u=!1;return new _a((function(_,h){var E;a.socket=new nd(a.core,t,a.linkSSL),a.socket.on("connect",(function(){a.logger.log("clientSocketV2::socket on connect",t),a.core.reporterHookLinkKeep.start(),a.core.reporterHookLinkKeep.update({code:0,description:"connection begin",operation_type:0,target:t}),u=!0,_()})),a.socket.on("message",bind$1(E=a.onMessage).call(E,a)),a.socket.on("disconnect",(function(_){return __awaiter(a,void 0,void 0,Sa.mark((function _callee2(){return Sa.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return u=!0,this.logger.log("clientSocketV2::socket on disconnect",_),a.next=4,this.core.reporterHookLinkKeep.update({code:(null==_?void 0:_.code)||0,description:(null==_?void 0:_.reason)||"socket on disconnect",operation_type:1,target:t});case 4:this.core.reporterHookLinkKeep.end(!1),this.doDisconnect(Ju.OFFLINE,"SocketOnDisconnect");case 6:case"end":return a.stop()}}),_callee2,this)})))})),a.socket.on("handshakeFailed",(function(t){u?a.ping():(a.logger.error('clientSocketV2::handshake failed: "'+(t&&t.message)+'"'),a.cleanSocket()),u=!0,h(t)}))}))},t.cleanSocket=function cleanSocket(){this.socket&&("function"==typeof this.socket.removeAllListeners&&this.socket.removeAllListeners(),"function"==typeof this.socket.close&&this.socket.close(),this.socket=void 0)},t.resetSocketConfig=function resetSocketConfig(){this.backoff.reset(),this.initOnlineListener()},t.doDisconnect=function doDisconnect(t,a){if(this.logger.log("clientSocketV2::doDisconnect: type "+t+", reason ",a),0!==this.core.auth.getConnectStatus()){var u={1:"close",2:"kicked",3:"broken"}[t]||"";this.markAllCmdInvaild(new vl({code:gl.V2NIM_ERROR_CODE_DISCONNECT,detail:{reason:"Packet timeout due to instance disconnect",disconnect_reason:u}})),this.timerManager.destroy(),clearTimeout(this.pingTimer),this.cleanSocket(),t===Ju.ACTIVE||t===Ju.KICKED?this.destroyOnlineListener():t===Ju.OFFLINE&&(this.auth.lifeCycle.processEvent("connectionBroken",new vl({code:gl.V2NIM_ERROR_CODE_DISCONNECT,detail:{reason:"connection broken due to internal reasons"}})),this.logger.log("clientSocketV2::doDisconnect: pending reconnect "+this.isReconnect),this.isReconnect&&this.auth.lifeCycle.processEvent("waiting"))}else this.logger.warn("clientSocketV2::doDisconnect: already disconnected")},t.sendCmd=function sendCmd(t,a,u){var _=this,h=this.core.auth.getLoginStatus(),E=["v2Login","login","chatroomLogin","v2ChatroomLogin"],m={cmd:t};if(1!==h&&!includes(E).call(E,t))return this.logger.warn("clientSocketV2::NIM login status is "+h+", so can not sendCmd "+t),_a.reject(new vl({code:gl.V2NIM_ERROR_CODE_ILLEGAL_STATE,detail:ba({reason:"Can not sendCmd due to no logined"},m)}));var g="heartbeat"!==t,I=g?this.packetSer++:0,T=createCmd(t,I,this.logger,a);if(!T){var N=new vl({code:gl.V2NIM_ERROR_CODE_INTERNAL,detail:ba(ba({},m),{reason:"SendCmd::createCmd error: "+I+" "+t})});return this.logger.error(N),_a.reject(N)}var M=T.packet,O=T.hasPacketResponse,S=T.hasPacketTimer,R=Ms(M);g&&(this.logger.getDebugMode()?this.logger.debug("clientSocketV2::sendCmd: "+M.SID+"_"+M.CID+","+t+",ser:"+I,R):this.logger.log("clientSocketV2::sendCmd: "+M.SID+"_"+M.CID+","+t+",ser:"+I));var C=(new Date).getTime();return new _a((function(h,E){O&&_.sendingCmdMap.set(I,{cmd:t,params:a,callback:[h,E],timer:S?ls((function(){var a=new vl({code:gl.V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT,detail:ba({ser:I,reason:"Packet Timeout: ser "+I+" cmd "+t,timetag:(new Date).getTime()},m)});_.markCmdInvalid(I,a,t)}),u&&u.timeout?u.timeout:_.packetTimeout):null});try{_.socket.send(R),O||h(M)}catch(a){var g=new vl({code:gl.V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED,detail:ba({ser:I,reason:"Unable to send packet"+(a&&a.message?": "+a.message:""),timetag:(new Date).getTime(),rawError:a},m)});_.markCmdInvalid(I,g,t),E(g)}})).catch((function(t){return __awaiter(_,void 0,void 0,Sa.mark((function _callee3(){var a,u,_,h,E;return Sa.wrap((function _callee3$(m){for(;;)switch(m.prev=m.next){case 0:if(u=t,_=[gl.V2NIM_ERROR_CODE_DISCONNECT,gl.V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT,gl.V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED],includes(_).call(_,u.code)){m.next=4;break}return m.abrupt("return",_a.reject(u));case 4:return this.reporter.reportTraceStart("exceptions",{user_id:this.core.auth.getLoginUser(),trace_id:null===(a=this.socket)||void 0===a?void 0:a.sessionId,start_time:C,action:2,exception_service:6}),h=get(u,"detail.disconnect_reason")||"",E=u.code===gl.V2NIM_ERROR_CODE_DISCONNECT?Ms({disconnect_reason:h}):u.detail.reason,this.reporter.reportTraceUpdateV2("exceptions",{code:u.code,description:E,operation_type:1,target:M.SID+"-"+M.CID,context:""+M.SER},{asyncParams:Es.net.getNetworkStatus()}),this.reporter.reportTraceEnd("exceptions",1),m.abrupt("return",_a.reject(u));case 10:case"end":return m.stop()}}),_callee3,this)})))}))},t.onMessage=function onMessage(t){var a=parseCmd(t,this.logger);if(a)for(var u,_=_createForOfIteratorHelperLoose$1(a);!(u=_()).done;){var h=u.value,E=h.raw.ser;if(h.error&&this.logger.error("clientSocketV2::onMessage packet error",h.raw.sid+"_"+h.raw.cid+", ser:"+E+",",h.error),h.notFound)return void this.logger.warn("clientSocketV2::onMessage packet not found",h.raw.sid+"_"+h.raw.cid+", ser:"+E);"heartbeat"!==h.cmd&&(this.logger.getDebugMode()?this.logger.debug("clientSocketV2::recvCmd "+h.raw.sid+"_"+h.raw.cid+","+h.cmd+",ser:"+E,h.content):this.logger.log("clientSocketV2::recvCmd "+h.raw.sid+"_"+h.raw.cid+","+h.cmd+",ser:"+E+";code:"+h.raw.code)),this.packetHandler(h)}},t.packetHandler=function packetHandler(t){var a,u,_,h,E=this;if(t){var m=t.raw.ser,g=this.sendingCmdMap.get(m);if(g&&g.cmd===t.cmd){var I=g.callback,T=g.timer,N=g.params;if(clearTimeout(T),t.params=N,this.sendingCmdMap.delete(m),"heartbeat"===t.cmd)return void I[0]();var M=null===(u=null===(a=this.core[t.service])||void 0===a?void 0:a.process)||void 0===u?void 0:u.call(a,t);M&&"function"==typeof M.then?M.then((function(t){I[0](t)})).catch((function(t){I[1](t)})):(this.logger.log("clientSocketV2::handlerFn without promise",t.service,t.cmd),I[0](t))}else{var O=null===(h=null===(_=this.core[t.service])||void 0===_?void 0:_.process)||void 0===h?void 0:h.call(_,t);O&&"function"==typeof O.then&&O.catch((function(t){E.logger.error("clientSocketV2::no obj cache, no process handler",t)}))}}},t.markCmdInvalid=function markCmdInvalid(t,a,u){var _=this.sendingCmdMap.get(t);if(_){var h=_.callback,E=_.timer;E&&clearTimeout(E),this.sendingCmdMap.delete(t),this.logger.warn("clientSocketV2::packet "+t+", "+u+" is invalid:",a),h[1](a)}},t.markAllCmdInvaild=function markAllCmdInvaild(t){var a,u=this;this.logger.log("markAllCmdInvaild",t),forEach$1(a=this.sendingCmdMap).call(a,(function(a){var _=a.callback,h=a.timer,E=a.cmd;u.logger.log("clientSocketV2::markAllCmdInvaild:cmd "+E),h&&clearTimeout(h),_[1](t)})),this.sendingCmdMap.clear()},t.ping=function ping(){var t;return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){var a=this;return Sa.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return clearTimeout(this.pingTimer),u.prev=1,u.next=4,this.sendCmd("heartbeat");case 4:u.next=20;break;case 6:if(u.prev=6,u.t0=u.catch(1),u.t0.code!==gl.V2NIM_ERROR_CODE_DISCONNECT){u.next=11;break}return u.abrupt("return");case 11:return u.next=13,this.testHeartBeat5Timeout();case 13:if(!u.sent){u.next=20;break}return u.next=17,this.core.reporterHookLinkKeep.update({code:0,description:"Heartbeat-discovered link failure",operation_type:1,target:null===(t=this.socket)||void 0===t?void 0:t.url});case 17:return this.core.reporterHookLinkKeep.end(!0),this.doDisconnect(Ju.OFFLINE,"PingError"),u.abrupt("return");case 20:this.pingTimer=ls((function(){a.ping()}),3e4);case 21:case"end":return u.stop()}}),_callee4,this,[[1,6]])})))},t.testHeartBeat5Timeout=function testHeartBeat5Timeout(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){var t;return Sa.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:clearTimeout(this.pingTimer),t=0;case 2:if(!(t<5)){a.next=15;break}return a.prev=3,a.next=6,this.sendCmd("heartbeat",{},{timeout:3e3});case 6:return a.abrupt("return",!1);case 9:a.prev=9,a.t0=a.catch(3),this.logger.log("clientSocketV2::test heartbeat "+t+" Timeout");case 12:t++,a.next=2;break;case 15:return a.abrupt("return",!0);case 16:case"end":return a.stop()}}),_callee5,this,[[3,9]])})))},t.initOnlineListener=function initOnlineListener(){var t=this;this.hasNetworkListener||(this.logger.log("clientSocketV2::onlineListener:init"),this.hasNetworkListener=!0,Es.net.onNetworkStatusChange((function(a){t.logger.log("clientSocketV2::onlineListener:network change",a);var u=t.auth.getConnectStatus(),_=t.auth.getLoginStatus();a.isConnected&&1===_?t.ping():a.isConnected&&3===u?(t.logger.log("clientSocketV2::onlineListener:online and connectStatus is waiting, do reLogin"),t.auth.reconnect.clearReconnectTimer(),t.auth.reconnect.doReLogin()):a.isConnected||t.doDisconnect(Ju.OFFLINE,"OfflineListener")})))},t.destroyOnlineListener=function destroyOnlineListener(){this.logger.log("clientSocketV2::onlineListener:destroy"),Es.net.offNetworkStatusChange(),this.hasNetworkListener=!1},V2ClientSocket}();function _createForOfIteratorHelperLoose(t,a){var u,_=void 0!==Jn&&wl(t)||t["@@iterator"];if(_)return bind$1(u=(_=_.call(t)).next).call(u,_);if(fs(t)||(_=function _unsupportedIterableToArray(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray(t,a);var _=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Vl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?_arrayLikeToArray(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){_&&(t=_);var h=0;return function(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=Array(a);u<a;u++)_[u]=t[u];return _}var id=function(){function TimerManager(){this.timerList=[],this.id=1,this.timer=null,this.timeout=0}var t=TimerManager.prototype;return t.addTimer=function addTimer(t,a,u){void 0===a&&(a=0),void 0===u&&(u=1);var _=(new Date).getTime(),h=this.id;return this.timerList.push({id:h,loop:u,count:0,timeout:_+a,interval:a,callback:t}),this.id++,this.checkTimer(_),h},t.checkTimer=function checkTimer(t){if(void 0===t&&(t=(new Date).getTime()),this.removeFinished(),0!==this.timerList.length||null==this.timer){for(var a,u,_=0,h=_createForOfIteratorHelperLoose(this.timerList);!(a=h()).done;){var E=a.value;(0===_||_>E.timeout)&&(_=E.timeout)}if(0!==this.timerList.length)if(null===this.timer||_<this.timeout||this.timeout<t)this.timer=ls(bind$1(u=this.nowTime).call(u,this),_-t),this.timeout=_}},t.nowTime=function nowTime(){for(var t,nowTime=(new Date).getTime(),a=_createForOfIteratorHelperLoose(this.timerList);!(t=a()).done;){var u=t.value;nowTime>=u.timeout&&(u.callback(),u.count++,u.timeout=nowTime+u.interval)}this.clerTime(),this.checkTimer(nowTime)},t.clerTime=function clerTime(){null!==this.timer&&(clearTimeout(this.timer),this.timer=null)},t.deleteTimer=function deleteTimer(t){for(var a=this.timerList.length-1;a>=0;a--){var u;if(this.timerList[a].id===t)splice(u=this.timerList).call(u,a,1)}},t.removeFinished=function removeFinished(){for(var t=this.timerList.length-1;t>=0;t--){var a,u=this.timerList[t];if(u.loop>=0&&u.count>=u.loop)splice(a=this.timerList).call(a,t,1)}},t.destroy=function destroy(){this.clerTime(),this.timerList=[],this.id=1,this.timer=null},TimerManager}(),ad=function(){function CoreAdapters(t){this.lastSuccUploadHost="",this.core=t}var t=CoreAdapters.prototype;return t.getFileUploadInformation=function getFileUploadInformation(t){return Es.getFileUploadInformation(t)},t.request=function request(t,a,u){var _=this,h=(new Date).getTime(),E=(null==u?void 0:u.exception_service)||0;return Es.request(t,a).catch((function(u){var m,g,I,T,N=u;throw _.core.reporter.reportTraceStart("exceptions",{user_id:_.core.options.account||(null===(g=null===(m=_.core)||void 0===m?void 0:m.auth)||void 0===g?void 0:g.account),trace_id:null===(T=null===(I=_.core.clientSocket)||void 0===I?void 0:I.socket)||void 0===T?void 0:T.sessionId,start_time:h,action:1,exception_service:E}),_.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof N.code?N.code:0,description:N.message||""+N.code,operation_type:0,target:t,context:a?Ms(a):""},{asyncParams:Es.net.getNetworkStatus()}),_.core.reporter.reportTraceEnd("exceptions",1),u}))},t.uploadFile=function uploadFile(t){var a,u,_,h;return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var E,m,g,I,T,N,M,O,S,R,C,A,b,k;return Sa.wrap((function _callee$(L){for(;;)switch(L.prev=L.next){case 0:g="BROWSER"===Es.platform,I=g?t.chunkUploadHostBackupList:t.commonUploadHostBackupList,T=g?t.chunkUploadHost:t.commonUploadHost,N=indexOf(I).call(I,T),M=-1===N?concat(E=[T]).call(E,I):concat(m=[T]).call(m,slice(I).call(I,0,N),slice(I).call(I,N+1)),O=Math.max(indexOf(M).call(M,this.lastSuccUploadHost),0),S=null,R=0;case 8:if(!(R<M.length)){L.next=32;break}return C=(new Date).getTime(),A=M[(R+O)%M.length],L.prev=11,L.next=14,Es.uploadFile(ba(ba({},t),g?{chunkUploadHost:A}:{commonUploadHost:A}));case 14:return b=L.sent,this.lastSuccUploadHost=A,L.abrupt("return",b);case 19:if(L.prev=19,L.t0=L.catch(11),this.core.cloudStorage.nos.nosErrorCount--,S=L.t0,k=L.t0,this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(u=null===(a=this.core)||void 0===a?void 0:a.auth)||void 0===u?void 0:u.account),trace_id:null===(h=null===(_=this.core.clientSocket)||void 0===_?void 0:_.socket)||void 0===h?void 0:h.sessionId,start_time:C,action:1,exception_service:3}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof k.code?k.code:0,description:k.message||""+k.code,operation_type:1,target:A},{asyncParams:Es.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),!L.t0||L.t0.code!==gl.V2NIM_ERROR_CODE_CANCELLED&&10499!==L.t0.errCode){L.next=29;break}throw L.t0;case 29:R++,L.next=8;break;case 32:throw S;case 33:case"end":return L.stop()}}),_callee,this,[[11,19]])})))},CoreAdapters}(),sd=function(){function ABTest(t,a){this.abtInfo={},this.core=t,this.config=assignOptions({isAbtestEnable:!0,abtestUrl:Al,abtestProjectKey:bl},a)}var t=ABTest.prototype;return t.setOptions=function setOptions(t){this.config=assignOptions(this.config,t)},t.abtRequest=function abtRequest(){var t,a;return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var u;return Sa.wrap((function _callee$(_){for(;;)switch(_.prev=_.next){case 0:if(this.config.isAbtestEnable){_.next=2;break}return _.abrupt("return");case 2:if(!this.abtInfo.experiments){_.next=4;break}return _.abrupt("return");case 4:if(this.config.abtestUrl){_.next=6;break}return _.abrupt("return");case 6:return _.prev=6,_.next=9,this.core.adapters.request(this.config.abtestUrl,{method:"POST",dataType:"json",headers:{sdktype:"ABTest"},data:{clientInfo:{projectKey:this.config.abtestProjectKey,appKey:this.core.options.appkey,osType:"Web",sdkVersion:"10.8.30",deviceId:this.core.config.deviceId},useLocalCache:!0}},{exception_service:7});case 9:u=_.sent,_.next=15;break;case 12:_.prev=12,_.t0=_.catch(6),this.core.logger.warn("ABTest request failed");case 15:this.abtInfo=(null===(a=null===(t=null==u?void 0:u.data)||void 0===t?void 0:t.data)||void 0===a?void 0:a.abtInfo)||{};case 16:case"end":return _.stop()}}),_callee,this,[[6,12]])})))},ABTest}();function pickBy(t,a){t=t||{},a=a||function(){return!0};var u={};for(var _ in t)a(t[_])&&(u[_]=t[_]);return u}function formatQueueElementsFromKVObject(t){var a;return map$7(a=Za(t)).call(a,(function(a){return{key:a,value:t[a]}}))}function formatQueueElementsFromElements(t){return t&&t.length>0?map$7(t).call(t,(function(t){return{key:t.key,value:t.value,accountId:t.accid,nick:t.nick}})):[]}function formatMessage(t,a,u){return a.isSelf=a.senderId===u,delete a.resend,0!==a.messageType&&10!==a.messageType||(a.text=a.text||a.attachment,delete a.attachment),5===a.messageType?a.attachment=function formatNotificationAttachment(t,a){var u,_,h,E,m,g,I,T,N,M,O,S,R,C,A,b,k,L,D={type:"number"==typeof cd[t.id]?cd[t.id]:t.id,targetIds:null===(u=t.data)||void 0===u?void 0:u.target,targetNicks:null===(_=t.data)||void 0===_?void 0:_.tarNick,targetTag:null===(h=t.data)||void 0===h?void 0:h.targetTag,operatorId:null===(E=t.data)||void 0===E?void 0:E.operator,operatorNick:null===(m=t.data)||void 0===m?void 0:m.opeNick,notificationExtension:null===(g=t.data)||void 0===g?void 0:g.ext,tags:null===(I=t.data)||void 0===I?void 0:I.tags,messageClientId:null===(T=t.data)||void 0===T?void 0:T.msgId,messageTime:null===(N=t.data)||void 0===N?void 0:N.msgTime,chatBanned:void 0!==(null===(M=t.data)||void 0===M?void 0:M.muted)?Boolean(null===(O=t.data)||void 0===O?void 0:O.muted):void 0,tempChatBanned:void 0!==(null===(S=t.data)||void 0===S?void 0:S.tempMuted)?Boolean(null===(R=t.data)||void 0===R?void 0:R.tempMuted):void 0,previousRole:null===(C=t.data)||void 0===C?void 0:C.previousRole,tempChatBannedDuration:"number"==typeof(null===(A=t.data)||void 0===A?void 0:A.muteTtl)?t.data.muteTtl:null===(b=t.data)||void 0===b?void 0:b.muteDuration};14===(D=pickBy(D,(function(t){return void 0!==t}))).type?D.tempChatBanned=!0:15===D.type&&(D.tempChatBanned=!1);if(null===(k=t.data)||void 0===k?void 0:k.member){var V=t.data.member;D.currentMember=pickBy({roomId:a,accountId:V.accountId,memberRole:V.memberRole,memberLevel:V.memberLevel,roomNick:V.nick,roomAvatar:V.avatar,serverExtension:V.ext,isOnline:!!V.onlineStat,blocked:!!V.blockList,chatBanned:!!V.chatBanned,tempChatBanned:!!V.tempChatBanned,tempChatBannedDuration:"number"==typeof V.muteTtl?V.muteTtl:V.muteDuration,tags:stringToJSONObject(V.tags),notifyTargetTags:V.notifyTargetTags,enterTime:V.enterTime,updateTime:V.updateTime,valid:void 0===V.valid||!!V.valid,multiEnterInfo:formatMultiEnterInfo(V.onlineList)},(function(t){return void 0!==t}))}if(null===(L=t.data)||void 0===L?void 0:L.queueChange){var w=function formatNotificationAttachmentForQueue(t){try{var a=JSON.parse(t);if("OFFER"===a._e)return{elements:[{key:a.key,value:a.content}],queueChangeType:1};if("POLL"===a._e)return{elements:[{key:a.key,value:a.content}],queueChangeType:2};if("DROP"===a._e)return{elements:[],queueChangeType:3};if("BATCH_UPDATE"===a._e)return{elements:formatQueueElementsFromKVObject(a.kvObject),queueChangeType:5};if("PARTCLEAR"===a._e)return{elements:formatQueueElementsFromKVObject(a.kvObject),queueChangeType:4};if("BATCH_OFFER"===a._e)return{elements:formatQueueElementsFromElements(a.elements),queueChangeType:6}}catch(t){}return{elements:[],queueChangeType:0}}(t.data.queueChange),P=w.elements,x=w.queueChangeType;D.elements=P,x>0&&(D.queueChangeType=x)}return D.raw=t.raw,D}(a.attachment,a.roomId):100===a.messageType&&(a=function formatCustomAttachment(t,a){var u,_,h;if("string"==typeof(null===(u=a.attachment)||void 0===u?void 0:u.raw)&&(null===(h=null===(_=t.V2NIMChatroomMessageService)||void 0===_?void 0:_.customAttachmentParsers)||void 0===h?void 0:h.length)>0){var E=a.subType||0,m=t.V2NIMChatroomMessageService.customAttachmentParsers,g=a.attachment.raw;some(m).call(m,(function(u){try{var _=u(E,g);if(isPlainObject(_))return _.raw=g,a.attachment=_,!0}catch(a){return t.logger.warn("customAttachmentParser: subType "+E+", raw: "+g+". parse error with "+a),!1}}))}return a}(t,a)),a}function formatMultiEnterInfo(t){if(t&&"string"==typeof t)try{var a=JSON.parse(t);return map$7(a).call(a,(function(t){return{roomNick:t.room_nick,roomAvatar:t.room_avatar,enterTime:t.enter_time,clientType:t.client_type}}))}catch(t){return}}var cd={301:0,302:1,303:2,304:3,305:4,306:5,312:6,313:7,314:8,315:9,316:10,317:11,320:11,324:11,318:12,319:13,321:14,322:15,323:16,325:17,326:18};function attachmentToRaw(t,a){if(!a)return"";switch(t){case 100:return a.raw||"";case 1:case 3:case 2:case 6:return function mediaAttachmentToRaw(t){var a=t,u=a.width,_=a.height,h=a.duration;a.path,a.file,a.raw,a.ctx,a.payload,a.bucketName,a.objectName,a.token;var E=a.ext,m=__rest(a,["width","height","duration","path","file","raw","ctx","payload","bucketName","objectName","token","ext"]),g="string"==typeof E&&"."===E[0]?slice(E).call(E,1):E;return Ms(ba(ba(ba(ba(ba({},m),void 0===E?{}:{ext:g}),void 0===u?{}:{w:u}),void 0===_?{}:{h:_}),void 0===h?{}:{dur:h}))}(a);case 4:return function locationAttachmentToRaw(t){return Ms({lat:t.latitude,lng:t.longitude,title:t.address})}(a);case 12:return function callAttachmentToRaw(t){t.raw;var a=__rest(t,["raw"]);try{var u;return Ms(ba(ba({},a),{durations:map$7(u=t.durations).call(u,(function(t){return{accid:t.accountId,duration:t.duration}}))}))}catch(a){return Ms(t)}}(a);default:return"string"==typeof a?a:Ms(a)}}function rawToAttachment(t,a){var u;try{switch(u=JSON.parse(t),a){case 100:return{raw:t};case 4:return function locationRawToAttachment(t,a){return{latitude:a.lat,longitude:a.lng,address:a.title,raw:t}}(t,u);case 2:case 3:case 1:case 6:return function mediaRawToAttachment(t,a){var u=a.w,_=a.h,h=a.dur,E=a.ext,m=__rest(a,["w","h","dur","ext"]),g="string"==typeof E&&"."!==E[0]?"."+E:E;return ba(ba(ba(ba(ba(ba({},m),void 0===E?{}:{ext:g}),void 0===u?{}:{width:u}),void 0===_?{}:{height:_}),void 0===h?{}:{duration:h}),{raw:t})}(t,u);case 12:return function callRawToAttachment(t,a){var u;return ba(ba({},a),{durations:map$7(u=a.durations).call(u,(function(t){return{accountId:t.accid,duration:t.duration}})),raw:t})}(t,u);default:return"object"==typeof u&&u?ba(ba({},u),{raw:t}):{raw:t}}}catch(a){return"object"==typeof u&&u?ba(ba({},u),{raw:t}):{raw:t}}}var ld,ud,dd,_d,pd,hd,fd,Ed,md,gd,Id,vd,Td,Nd,Md,Od,Sd,Rd="V2NIMChatroomMemberService",yd={"36_15":"v2ChatroomUpdateSelfMemberInfo","36_16":"v2ChatroomGetMemberByIds","36_17":"v2ChatroomKickMember","36_19":"v2ChatroomSetMemberTempChatBanned","36_41":"v2ChatroomGetMemberListByTag","36_32":"v2ChatroomGetMemberCountByTag","36_37":"v2ChatroomGetMemberListByOption","36_38":"v2ChatroomUpdateMemberRole","36_39":"v2ChatroomSetMemberBlockedStatus","36_40":"v2ChatroomSetMemberChatBannedStatus","13_101":"v2ChatroomOnMemberTagUpdated"},Cd={roomId:1,accountId:2,memberRole:{id:3,retType:"number"},memberLevel:{id:4,retDef:0,retType:"number"},roomNick:5,roomAvatar:6,serverExtension:7,isOnline:{id:8,retType:"boolean"},enterTime:{id:10,retType:"number"},blocked:{id:12,retType:"boolean"},chatBanned:{id:13,retType:"boolean"},valid:{id:14,retDef:!0,retType:"boolean"},updateTime:{id:15,retDef:0,retType:"number"},tempChatBanned:{id:16,retType:"boolean",retDef:!1},tempChatBannedDuration:{id:17,retType:"number"},tags:{id:18,converter:objectToJSONString,retConverter:stringToJSONObject},notifyTargetTags:19,multiEnterInfo:{id:20,retConverter:formatMultiEnterInfo}},Ad={memberRoles:{id:1,converter:function converter(t){return t.join(",")}},onlyBlocked:{id:2,converter:boolToInt},onlyChatBanned:{id:3,converter:boolToInt},onlyOnline:{id:4,converter:boolToInt},pageToken:5,limit:6},bd={v2ChatroomGetMemberByIds:{sid:36,cid:16,service:Rd,params:[{type:"StrArray",name:"accountIds"}],response:[{type:"PropertyArray",name:"datas",reflectMapper:invertSerializeItem(Cd)}]},v2ChatroomGetMemberListByOption:{sid:36,cid:37,service:Rd,params:[{type:"Property",name:"tag",reflectMapper:Ad}],response:[{type:"Int",name:"hasMore"},{type:"String",name:"pageToken"},{type:"PropertyArray",name:"datas",reflectMapper:invertSerializeItem(Cd)}]},v2ChatroomUpdateSelfMemberInfo:{sid:36,cid:15,service:Rd,params:[{type:"Property",name:"tag",reflectMapper:Cd},{type:"Bool",name:"notificationEnabled"},{type:"String",name:"notificationExtension"},{type:"Bool",name:"persistence"},{type:"Property",name:"antispamConfig",reflectMapper:{antispamBusinessId:1}}]},v2ChatroomSetMemberTempChatBanned:{sid:36,cid:19,service:Rd,params:[{type:"String",name:"accountId"},{type:"Long",name:"tempChatBannedDuration"},{type:"Bool",name:"notificationEnabled"},{type:"String",name:"notificationExtension"}]},v2ChatroomGetMemberListByTag:{sid:36,cid:41,service:Rd,params:[{type:"Property",name:"tag",reflectMapper:{tag:1,pageToken:2,limit:3}}],response:[{type:"Int",name:"hasMore"},{type:"String",name:"pageToken"},{type:"PropertyArray",name:"datas",reflectMapper:invertSerializeItem(Cd)}]},v2ChatroomGetMemberCountByTag:{sid:36,cid:32,service:Rd,params:[{type:"String",name:"tag"}],response:[{type:"Long",name:"data"}]},v2ChatroomKickMember:{sid:36,cid:17,service:Rd,params:[{type:"String",name:"accountId"},{type:"String",name:"notificationExtension"}]},v2ChatroomUpdateMemberRole:{sid:36,cid:38,service:Rd,params:[{type:"Property",name:"tag",reflectMapper:{accountId:1,memberRole:2,memberLevel:3,notificationExtension:4}}],response:[{type:"Property",name:"data",reflectMapper:invertSerializeItem(Cd)}]},v2ChatroomSetMemberBlockedStatus:{sid:36,cid:39,service:Rd,params:[{type:"String",name:"accountId"},{type:"Bool",name:"blocked"},{type:"String",name:"notificationExtension"}],response:[{type:"Property",name:"data",reflectMapper:invertSerializeItem(Cd)}]},v2ChatroomSetMemberChatBannedStatus:{sid:36,cid:40,service:Rd,params:[{type:"String",name:"accountId"},{type:"Bool",name:"chatBanned"},{type:"String",name:"notificationExtension"}],response:[{type:"Property",name:"data",reflectMapper:invertSerializeItem(Cd)}]},v2ChatroomOnMemberTagUpdated:{sid:36,cid:101,service:Rd,response:[{type:"Property",name:"data",reflectMapper:{1:"tag"}}]}},kd="V2NIMChatroomLoginService",Ld={"1_2":"heartbeat","36_2":"v2ChatroomLogin","13_3":"v2ChatroomBeKicked","36_4":"v2ChatroomLogout"},Dd={roomId:1,roomName:3,announcement:4,liveUrl:5,isValidRoom:{id:9,retType:"boolean"},serverExtension:12,queueLevelMode:{id:16,retType:"number"},creatorAccountId:100,onlineUserCount:{id:101,retType:"number"},chatBanned:{id:102,retType:"boolean"}},Vd={enabled:{id:1,retType:"boolean"},cdnUrls:{id:2,retConverter:function retConverter(t){if("string"==typeof t&&""!==t)return t.split("|")}},timestamp:{id:3,retType:"number"},pollingIntervalSeconds:{id:4,retType:"number"},decryptType:{id:5,retType:"number"},decryptKey:6,pollingTimeoutMillis:{id:7,retType:"number"}},wd={heartbeat:{sid:1,cid:2,service:kd},v2ChatroomLogin:{sid:36,cid:2,service:kd,params:[{type:"Byte",name:"type"},{type:"Property",name:"chatroomLogin",reflectMapper:{appkey:1,account:2,deviceId:3,chatroomId:5,appLogin:8,chatroomNick:20,chatroomAvatar:21,serverExtension:22,notificationExtension:23,clientSession:26,isAnonymous:{id:38,converter:function converter(t){return+t}},tags:{id:39,converter:function converter(t){if(fs(t)&&t.length>0)return Ms(t)}},notifyTargetTags:40,authType:41,loginExt:42,x:43,y:44,z:45,distance:46,antiSpamBusinessId:47}},{type:"Property",name:"chatroomIMLogin",reflectMapper:{clientType:3,os:4,sdkVersion:6,appLogin:8,protocolVersion:9,pushTokenName:10,pushToken:11,deviceId:13,appkey:18,account:19,browser:24,clientSession:26,deviceInfo:32,customTag:38,customClientType:39,sdkHumanVersion:40,hostEnv:41,userAgent:42,libEnv:44,isReactNative:{id:112,converter:function converter(t){return+t}},authType:115,loginExt:116,token:1e3}}],response:[{type:"Property",name:"chatroomInfo",reflectMapper:invertSerializeItem(Dd)},{type:"Property",name:"chatroomMember",reflectMapper:invertSerializeItem(Cd)},{type:"Property",name:"chatroomCdnInfo",reflectMapper:invertSerializeItem(Vd)}]},v2ChatroomLogout:{sid:36,cid:4,service:kd,params:[]},v2ChatroomBeKicked:{sid:13,cid:3,service:kd,response:[{type:"Int",name:"kickedReason"},{type:"String",name:"serverExtension"}]}};!function(t){t[t.V2NIM_LOGIN_AUTH_TYPE_DEFAULT=0]="V2NIM_LOGIN_AUTH_TYPE_DEFAULT",t[t.V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN=1]="V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN",t[t.V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY=2]="V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY"}(ld||(ld={})),function(t){t[t.V2NIM_LOGIN_STATUS_LOGOUT=0]="V2NIM_LOGIN_STATUS_LOGOUT",t[t.V2NIM_LOGIN_STATUS_LOGINED=1]="V2NIM_LOGIN_STATUS_LOGINED",t[t.V2NIM_LOGIN_STATUS_LOGINING=2]="V2NIM_LOGIN_STATUS_LOGINING",t[t.V2NIM_LOGIN_STATUS_UNLOGIN=3]="V2NIM_LOGIN_STATUS_UNLOGIN"}(ud||(ud={})),function(t){t[t.V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN=0]="V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN",t[t.V2NIM_LOGIN_CLIENT_TYPE_ANDROID=1]="V2NIM_LOGIN_CLIENT_TYPE_ANDROID",t[t.V2NIM_LOGIN_CLIENT_TYPE_IOS=2]="V2NIM_LOGIN_CLIENT_TYPE_IOS",t[t.V2NIM_LOGIN_CLIENT_TYPE_PC=4]="V2NIM_LOGIN_CLIENT_TYPE_PC",t[t.V2NIM_LOGIN_CLIENT_TYPE_WP=8]="V2NIM_LOGIN_CLIENT_TYPE_WP",t[t.V2NIM_LOGIN_CLIENT_TYPE_WEB=16]="V2NIM_LOGIN_CLIENT_TYPE_WEB",t[t.V2NIM_LOGIN_CLIENT_TYPE_RESTFUL=32]="V2NIM_LOGIN_CLIENT_TYPE_RESTFUL",t[t.V2NIM_LOGIN_CLIENT_TYPE_MAC_OS=64]="V2NIM_LOGIN_CLIENT_TYPE_MAC_OS",t[t.V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS=65]="V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS"}(dd||(dd={})),function(t){t[t.V2NIM_LOGIN_CLIENT_CHANGE_LIST=1]="V2NIM_LOGIN_CLIENT_CHANGE_LIST",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGIN=2]="V2NIM_LOGIN_CLIENT_CHANGE_LOGIN",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT=3]="V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT"}(_d||(_d={})),function(t){t[t.V2NIM_CONNECT_STATUS_DISCONNECTED=0]="V2NIM_CONNECT_STATUS_DISCONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTED=1]="V2NIM_CONNECT_STATUS_CONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTING=2]="V2NIM_CONNECT_STATUS_CONNECTING",t[t.V2NIM_CONNECT_STATUS_WAITING=3]="V2NIM_CONNECT_STATUS_WAITING"}(pd||(pd={})),function(t){t[t.V2NIM_CHATROOM_KICKED_REASON_UNKNOWN=-1]="V2NIM_CHATROOM_KICKED_REASON_UNKNOWN",t[t.V2NIM_CHATROOM_KICKED_REASON_CHATROOM_INVALID=1]="V2NIM_CHATROOM_KICKED_REASON_CHATROOM_INVALID",t[t.V2NIM_CHATROOM_KICKED_REASON_BY_MANAGER=2]="V2NIM_CHATROOM_KICKED_REASON_BY_MANAGER",t[t.V2NIM_CHATROOM_KICKED_REASON_BY_CONFLICT_LOGIN=3]="V2NIM_CHATROOM_KICKED_REASON_BY_CONFLICT_LOGIN",t[t.V2NIM_CHATROOM_KICKED_REASON_SILENTLY=4]="V2NIM_CHATROOM_KICKED_REASON_SILENTLY",t[t.V2NIM_CHATROOM_KICKED_REASON_BE_BLOCKED=5]="V2NIM_CHATROOM_KICKED_REASON_BE_BLOCKED"}(hd||(hd={})),function(t){t[t.V2NIM_CHATROOM_MEMBER_ROLE_NORMAL=0]="V2NIM_CHATROOM_MEMBER_ROLE_NORMAL",t[t.V2NIM_CHATROOM_MEMBER_ROLE_CREATOR=1]="V2NIM_CHATROOM_MEMBER_ROLE_CREATOR",t[t.V2NIM_CHATROOM_MEMBER_ROLE_MANAGER=2]="V2NIM_CHATROOM_MEMBER_ROLE_MANAGER",t[t.V2NIM_CHATROOM_MEMBER_ROLE_NORMAL_GUEST=3]="V2NIM_CHATROOM_MEMBER_ROLE_NORMAL_GUEST",t[t.V2NIM_CHATROOM_MEMBER_ROLE_ANONYMOUS_GUEST=4]="V2NIM_CHATROOM_MEMBER_ROLE_ANONYMOUS_GUEST",t[t.V2NIM_CHATROOM_MEMBER_ROLE_VIRTUAL=5]="V2NIM_CHATROOM_MEMBER_ROLE_VIRTUAL"}(fd||(fd={})),function(t){t[t.V2NIM_MESSAGE_SENDING_STATE_UNKNOWN=0]="V2NIM_MESSAGE_SENDING_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED=1]="V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED",t[t.V2NIM_MESSAGE_SENDING_STATE_FAILED=2]="V2NIM_MESSAGE_SENDING_STATE_FAILED",t[t.V2NIM_MESSAGE_SENDING_STATE_SENDING=3]="V2NIM_MESSAGE_SENDING_STATE_SENDING"}(Ed||(Ed={})),function(t){t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN=0]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS=1]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED=2]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING=3]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING"}(md||(md={})),function(t){t[t.V2NIM_MESSAGE_TYPE_INVALID=-1]="V2NIM_MESSAGE_TYPE_INVALID",t[t.V2NIM_MESSAGE_TYPE_TEXT=0]="V2NIM_MESSAGE_TYPE_TEXT",t[t.V2NIM_MESSAGE_TYPE_IMAGE=1]="V2NIM_MESSAGE_TYPE_IMAGE",t[t.V2NIM_MESSAGE_TYPE_AUDIO=2]="V2NIM_MESSAGE_TYPE_AUDIO",t[t.V2NIM_MESSAGE_TYPE_VIDEO=3]="V2NIM_MESSAGE_TYPE_VIDEO",t[t.V2NIM_MESSAGE_TYPE_LOCATION=4]="V2NIM_MESSAGE_TYPE_LOCATION",t[t.V2NIM_MESSAGE_TYPE_NOTIFICATION=5]="V2NIM_MESSAGE_TYPE_NOTIFICATION",t[t.V2NIM_MESSAGE_TYPE_FILE=6]="V2NIM_MESSAGE_TYPE_FILE",t[t.V2NIM_MESSAGE_TYPE_AVCHAT=7]="V2NIM_MESSAGE_TYPE_AVCHAT",t[t.V2NIM_MESSAGE_TYPE_TIPS=10]="V2NIM_MESSAGE_TYPE_TIPS",t[t.V2NIM_MESSAGE_TYPE_ROBOT=11]="V2NIM_MESSAGE_TYPE_ROBOT",t[t.V2NIM_MESSAGE_TYPE_CALL=12]="V2NIM_MESSAGE_TYPE_CALL",t[t.V2NIM_MESSAGE_TYPE_CUSTOM=100]="V2NIM_MESSAGE_TYPE_CUSTOM"}(gd||(gd={})),function(t){t[t.V2NIM_QUERY_DIRECTION_DESC=0]="V2NIM_QUERY_DIRECTION_DESC",t[t.V2NIM_QUERY_DIRECTION_ASC=1]="V2NIM_QUERY_DIRECTION_ASC"}(Id||(Id={})),function(t){t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_ENTER=0]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_ENTER",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_EXIT=1]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_EXIT",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_BLOCK_ADDED=2]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_BLOCK_ADDED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_BLOCK_REMOVED=3]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_BLOCK_REMOVED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_CHAT_BANNED_ADDED=4]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_CHAT_BANNED_ADDED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_CHAT_BANNED_REMOVED=5]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_CHAT_BANNED_REMOVED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_ROOM_INFO_UPDATED=6]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_ROOM_INFO_UPDATED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_KICKED=7]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_KICKED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_TEMP_CHAT_BANNED_ADDED=8]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_TEMP_CHAT_BANNED_ADDED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_TEMP_CHAT_BANNED_REMOVED=9]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_TEMP_CHAT_BANNED_REMOVED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_INFO_UPDATED=10]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MEMBER_INFO_UPDATED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_QUEUE_CHANGE=11]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_QUEUE_CHANGE",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_CHAT_BANNED=12]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_CHAT_BANNED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_CHAT_BANNED_REMOVED=13]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_CHAT_BANNED_REMOVED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAG_TEMP_CHAT_BANNED_ADDED=14]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAG_TEMP_CHAT_BANNED_ADDED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAG_TEMP_CHAT_BANNED_REMOVED=15]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAG_TEMP_CHAT_BANNED_REMOVED",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MESSAGE_REVOKE=16]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_MESSAGE_REVOKE",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAGS_UPDATE=17]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_TAGS_UPDATE",t[t.V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_ROLE_UPDATE=18]="V2NIM_CHATROOM_MESSAGE_NOTIFICATION_TYPE_ROLE_UPDATE"}(vd||(vd={})),function(t){t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_NONE=0]="V2NIM_CLIENT_ANTISPAM_OPERATE_NONE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE=1]="V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD=2]="V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD=3]="V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD"}(Td||(Td={})),function(t){t[t.V2NIM_CHATROOM_STATUS_DISCONNECTED=0]="V2NIM_CHATROOM_STATUS_DISCONNECTED",t[t.V2NIM_CHATROOM_STATUS_WAITING=1]="V2NIM_CHATROOM_STATUS_WAITING",t[t.V2NIM_CHATROOM_STATUS_CONNECTING=2]="V2NIM_CHATROOM_STATUS_CONNECTING",t[t.V2NIM_CHATROOM_STATUS_CONNECTED=3]="V2NIM_CHATROOM_STATUS_CONNECTED",t[t.V2NIM_CHATROOM_STATUS_ENTERING=4]="V2NIM_CHATROOM_STATUS_ENTERING",t[t.V2NIM_CHATROOM_STATUS_ENTERED=5]="V2NIM_CHATROOM_STATUS_ENTERED",t[t.V2NIM_CHATROOM_STATUS_EXITED=6]="V2NIM_CHATROOM_STATUS_EXITED"}(Nd||(Nd={})),function(t){t.off="off",t.error="error",t.warn="warn",t.log="log",t.debug="debug"}(Md||(Md={})),function(t){t[t.V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_UNKNOWN=0]="V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_UNKNOWN",t[t.V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_OFFER=1]="V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_OFFER",t[t.V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_POLL=2]="V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_POLL",t[t.V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_DROP=3]="V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_DROP",t[t.V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_PARTCLEAR=4]="V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_PARTCLEAR",t[t.V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_BATCH_UPDATE=5]="V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_BATCH_UPDATE",t[t.V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_BATCH_OFFER=6]="V2NIM_CHATROOM_QUEUE_CHANGE_TYPE_BATCH_OFFER"}(Od||(Od={})),function(t){t[t.V2NIM_CHATROOM_QUEUE_LEVEL_MODE_ANY=0]="V2NIM_CHATROOM_QUEUE_LEVEL_MODE_ANY",t[t.V2NIM_CHATROOM_QUEUE_LEVEL_MODE_MANAGER=1]="V2NIM_CHATROOM_QUEUE_LEVEL_MODE_MANAGER"}(Sd||(Sd={}));var Pd={V2NIM_ERROR_CODE_UNKNOWN:{code:0,message:"unknown error"},V2NIM_ERROR_CODE_SUCCESS:{code:200,message:"success"},V2NIM_ERROR_CODE_HANDSHAKE:{code:201,message:"handshake error"},V2NIM_ERROR_CODE_REQUEST_TEMPERARY_FORBIDDEN:{code:398,message:"request temprary forbidden"},V2NIM_ERROR_CODE_SERVER_UNIT_ERROR:{code:399,message:"server unit error"},V2NIM_ERROR_CODE_FORBIDDEN:{code:403,message:"forbidden"},V2NIM_ERROR_CODE_NOT_FOUND:{code:404,message:"not found"},V2NIM_ERROR_CODE_PARAMETER_ERROR:{code:414,message:"parameter error"},V2NIM_ERROR_CODE_RATE_LIMIT_REACHED:{code:416,message:"rate limit reached"},V2NIM_ERROR_CODE_MULTI_LOGIN_FORBIDDEN:{code:417,message:"multi login forbidden"},V2NIM_ERROR_CODE_SERVER_INTERNAL_ERROR:{code:500,message:"server internal error"},V2NIM_ERROR_CODE_SERVER_BUSY:{code:503,message:"server busy"},V2NIM_ERROR_CODE_APP_UNREACHABLE:{code:511,message:"app server unreachable"},V2NIM_ERROR_CODE_SERVICE_UNAVAILABLE:{code:514,message:"service unavailable"},V2NIM_ERROR_CODE_PROTOCOL_BLACKHOLE_FILTERED:{code:599,message:"protocol filtered by blackhole rule"},V2NIM_ERROR_CODE_NO_PERMISSION:{code:997,message:"appid has no permission to call the protocol"},V2NIM_ERROR_CODE_UNPACK_ERROR:{code:998,message:"unpack error"},V2NIM_ERROR_CODE_PACK_ERROR:{code:999,message:"pack error"},V2NIM_ERROR_CODE_IM_DISABLED:{code:101301,message:"IM disabled"},V2NIM_ERROR_CODE_SERVICE_ADDRESS_INVALID:{code:101302,message:"service address invalid"},V2NIM_ERROR_CODE_APPKEY_NOT_EXIST:{code:101303,message:"appkey not exist"},V2NIM_ERROR_CODE_BUNDLEID_CHECK_FAILED:{code:101304,message:"bundleid check failed"},V2NIM_ERROR_CODE_APPKEY_BLOCKED:{code:101403,message:"appkey blocked"},V2NIM_ERROR_CODE_INVALID_TOKEN:{code:102302,message:"invalid token"},V2NIM_ERROR_CODE_ROBOT_NOT_ALLOWED:{code:102303,message:"robot not allowed"},V2NIM_ERROR_CODE_ACCOUNT_NOT_EXIST:{code:102404,message:"account not exist"},V2NIM_ERROR_CODE_ACCOUNT_CHAT_BANNED:{code:102421,message:"account chat banned"},V2NIM_ERROR_CODE_ACCOUNT_BANNED:{code:102422,message:"account banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_BLOCK_LIST:{code:102426,message:"account in block list"},V2NIM_ERROR_CODE_USER_PROFILE_NOT_EXIST:{code:103404,message:"user profile not exist"},V2NIM_ERROR_CODE_USER_PROFILE_HIT_ANTISPAM:{code:103451,message:"user profile hit antispam"},V2NIM_ERROR_CODE_PEER_FRIEND_LIMIT:{code:104301,message:"peer friend limit"},V2NIM_ERROR_CODE_FRIEND_APPLICATION_NOT_EXIST:{code:104302,message:"friend application not exist"},V2NIM_ERROR_CODE_FRIEND_NOT_EXIST:{code:104404,message:"friend not exist"},V2NIM_ERROR_CODE_FRIEND_ALREADY_EXIST:{code:104405,message:"friend already exist"},V2NIM_ERROR_CODE_SELF_FRIEND_OPERATION_NOT_ALLOWED:{code:104429,message:"self friend operation not allowed"},V2NIM_ERROR_CODE_FRIEND_LIMIT:{code:104435,message:"friend limit"},V2NIM_ERROR_CODE_FRIEND_OPERATION_RATE_LIMIT:{code:104449,message:"friend operation rate limit"},V2NIM_ERROR_CODE_FRIEND_HIT_ANTISPAM:{code:104451,message:"friend hit antispam"},V2NIM_ERROR_CODE_SELF_MUTE_OPERATION_NOT_ALLOWED:{code:105429,message:"self mute operation not allowed"},V2NIM_ERROR_CODE_MUTE_LIST_LIMIT:{code:105435,message:"mute list limit"},V2NIM_ERROR_CODE_SELF_BLOCK_LIST_OPERATION_NOT_ALLOWED:{code:106429,message:"self block list operation not allowed"},V2NIM_ERROR_CODE_BLOCK_LIST_LIMIT:{code:106435,message:"block list limit"},V2NIM_ERROR_CODE_REVOKE_THIRD_PARTY_MESSAGE_NOT_ALLOWED:{code:107301,message:"revoke third party message not allowed"},V2NIM_ERROR_CODE_SHORT_TO_LONG_URL_FAILED:{code:107307,message:"short to long URL failed"},V2NIM_ERROR_CODE_URL_INVALID:{code:107308,message:"URL invalid"},V2NIM_ERROR_CODE_DURATION_OUT_OF_RANGE:{code:107309,message:"duration out of range"},V2NIM_ERROR_CODE_GET_FILE_META_INFO_FAILED:{code:107310,message:"get file meta info failed"},V2NIM_ERROR_CODE_AUDIO_FILE_SIZE_LIMIT:{code:107311,message:"audio file size limit"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_TIMEOUT:{code:107312,message:"voice to text timeout"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FAILED:{code:107313,message:"voice to text failed"},V2NIM_ERROR_CODE_REVOKE_EXCEED_TIME_LIMIT:{code:107314,message:"revoke message exceed time limit"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_NOT_ALLOWED:{code:107315,message:"revoke specific message not allowed"},V2NIM_ERROR_CODE_FORCE_PUSH_LIST_LIMIT:{code:107316,message:"force push list limit"},V2NIM_ERROR_CODE_TEAM_MESSAGE_RECEIPT_RATE_LIMIT:{code:107317,message:"team message receipt rate limit"},V2NIM_ERROR_CODE_SNAPSHOT_NOT_EXIST:{code:107318,message:"snapshot not exist"},V2NIM_ERROR_CODE_PIN_LIMIT:{code:107319,message:"pin limit"},V2NIM_ERROR_CODE_PIN_NOT_EXIST:{code:107320,message:"pin not exist"},V2NIM_ERROR_CODE_QUICK_COMMENT_LIMIT:{code:107321,message:"quick comment limit"},V2NIM_ERROR_CODE_PIN_ALREADY_EXIST:{code:107322,message:"pin already exist"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FUNCTION_DISABLED:{code:107333,message:"voice to text function disabled"},V2NIM_ERROR_CODE_CLOUD_SEARCH_FUNCTION_DISABLED:{code:107334,message:"cloud search function disabled"},V2NIM_ERROR_CODE_ONE_WAY_DELETE_FUNCTION_DISABLED:{code:107335,message:"one-way delete function disabled"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_TO_SELF_NOT_ALLOWED:{code:107429,message:"revoke message to self not allowed"},V2NIM_ERROR_CODE_APP_CHAT_BANNED:{code:107410,message:"app chat banned"},V2NIM_ERROR_CODE_QUICK_COMMENT_FUNCTION_DISABLED:{code:107326,message:"quick comment function disabled"},V2NIM_ERROR_CODE_PIN_FUNCTION_DISABLED:{code:107327,message:"PIN function disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_FUNCTION_DISABLED:{code:107324,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_P2P_READ_RECEIPT_FUNCTION_DISABLED:{code:107325,message:"read receipt for p2p messages function disabled"},V2NIM_ERROR_CODE_RATE_LIMIT_FOR_MESSAGING_REACHED:{code:107323,message:"rate limit for messaging reached"},V2NIM_ERROR_CODE_MESSAGE_HIT_ANTISPAM:{code:107451,message:"message hit antispam"},V2NIM_ERROR_CODE_MESSAGE_NOT_EXIST:{code:107404,message:"message not exist"},V2NIM_ERROR_CODE_UNSENDING_MESSAGE_EXPIRED:{code:107406,message:"unsending message expired"},V2NIM_ERROR_CODE_TEAM_MARK_READ_FAILED:{code:107302,message:"sending message failed for marking message read failed for too many team members"},V2NIM_ERROR_CODE_SENDER_OR_MANAGER_PERMISSION_ONLY_REVOKE:{code:107303,message:"only sender or manager can revoke message"},V2NIM_ERROR_CODE_DELETE_SELF_MESSAGE_NOT_ALLOWED:{code:107328,message:"delete self message not allowed"},V2NIM_ERROR_CODE_NOT_CHATBOT_ACCOUNT:{code:107329,message:"is not chatbot account"},V2NIM_ERROR_CODE_MESSAGE_SENSE_REQUIRED:{code:107330,message:"sender or receiver must sense message"},V2NIM_ERROR_CODE_HIGH_PRIORITY_MESSAGE_RATE_LIMIT:{code:107304,message:"rate limit of high-priority messages exceeded"},ACK_MESSAGE_BE_HIGH_PRIORITY:{code:107305,message:"ack message should be high-priority"},V2NIM_ERROR_CODE_DUPLICATE_CLIENT_MESSAGE_ID:{code:107306,message:"duplicate client message ID"},V2NIM_ERROR_CODE_INVALID_TIME_RANGE:{code:107439,message:"invalid time range"},V2NIM_ERROR_CODE_NOT_ADVANCED_TEAM:{code:108302,message:"not advanced team"},V2NIM_ERROR_CODE_TEAM_MANAGER_LIMIT:{code:108303,message:"team manager limit"},V2NIM_ERROR_CODE_JOINED_TEAM_LIMIT:{code:108305,message:"joined team limit"},V2NIM_ERROR_CODE_TEAM_NORMAL_MEMBER_CHAT_BANNED:{code:108306,message:"team normal member chat banned"},V2NIM_ERROR_CODE_INVITED_ACCOUNT_NOT_FRIEND:{code:108307,message:"invited account not friend"},V2NIM_ERROR_CODE_REJECT_ALL_TEAM_APPLICATIONS:{code:108308,message:"reject all team applications"},V2NIM_ERROR_CODE_TEAM_NOT_EXIST:{code:108404,message:"team not exist"},V2NIM_ERROR_CODE_TEAM_ALREADY_CHAT_BANNED:{code:108420,message:"team already chat banned"},V2NIM_ERROR_CODE_ALL_TEAM_MEMBER_CHAT_BANNED:{code:108423,message:"all team member chat banned"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT:{code:108434,message:"extended super team limit"},V2NIM_ERROR_CODE_CREATED_TEAM_LIMIT:{code:108435,message:"created team limit"},V2NIM_ERROR_CODE_TEAM_INVITATION_LIMIT:{code:108437,message:"team invitation limit"},V2NIM_ERROR_CODE_TEAM_HIT_ANTISPAM:{code:108451,message:"team hit antispam"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT_NOT_CONFIGURED:{code:108304,message:"extended super team limit not configured"},V2NIM_ERROR_CODE_SUPER_TEAM_SERVICE_DISABLED:{code:108311,message:"super team service disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_RECORD_NOT_FOUND:{code:108301,message:"read receipt record for the team message not found"},V2NIM_ERROR_CODE_NOT_MANAGER:{code:108430,message:"unable to assign owner manager"},V2NIM_ERROR_CODE_ONLINE_MEMBER_COUNT_DISABLED:{code:108406,message:"number of online users service disabled"},V2NIM_ERROR_CODE_TRANSFER_DISABLED:{code:108310,message:"unable to transfer the ownership to owner"},V2NIM_ERROR_CODE_CREATE_TEAM_DISABLED:{code:108309,message:"unable to create team with more than %s people"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_CREATE_FAILED:{code:108313,message:"/ extended super team creation failed，use open api to create the team"},V2NIM_ERROR_CODE_TEAM_MESSAGE_READ_RECEIPT_DISABLED:{code:108312,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_RETRY:{code:108449,message:"an error occurred, try again"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_NOT_TEAM_MEMBER:{code:109301,message:"list of chat banned users contains non team members"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_OPERATOR:{code:109303,message:"list of chat banned users contains the operator"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_TEAM_OWNER:{code:109304,message:"list of chat banned users contains the team owner"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_MANAGER_NOT_ALLOWED:{code:109305,message:"operation on team manager not allowed"},V2NIM_ERROR_CODE_NO_TEAM_INVITE_PERMISSION:{code:109306,message:"no team invite permission"},V2NIM_ERROR_CODE_TEAM_OWNER_QUIT_NOT_ALLOWED:{code:109307,message:"team owner quit not allowed"},V2NIM_ERROR_CODE_TEAM_OWNER_IN_KICK_LIST:{code:109308,message:"list of kicked user contains the team owner"},V2NIM_ERROR_CODE_INVITE_ROBOT_ACCOUNT_NOT_ALLOWED:{code:109309,message:"invite robot account not allowed"},V2NIM_ERROR_CODE_KICK_OPERATOR_NOT_ALLOWED:{code:109310,message:"kick operator not allowed"},V2NIM_ERROR_CODE_TEAM_MEMBER_ALREADY_EXIST:{code:109311,message:"team member already exist"},V2NIM_ERROR_CODE_TEAM_MEMBER_CAN_NOT_MODIFY_SELF:{code:109312,message:"operation on self not allowed"},V2NIM_ERROR_CODE_TEAM_INVITATION_OR_APPLICATION_NOT_EXIST:{code:109313,message:"team invitation or application not exist"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_OWNER_NOT_ALLOWED:{code:109314,message:"operation on team owner not allowed"},V2NIM_ERROR_CODE_TEAM_MEMBER_NOT_EXIST:{code:109404,message:"team member not exist"},V2NIM_ERROR_CODE_TEAM_MEMBER_CHAT_BANNED:{code:109424,message:"team member chat banned"},V2NIM_ERROR_CODE_TEAM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:109427,message:"team owner operation permission required"},V2NIM_ERROR_CODE_TEAM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:109432,message:"team owner or manager operation permission required"},V2NIM_ERROR_CODE_TEAM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:109449,message:"team member concurrent operation failed"},V2NIM_ERROR_CODE_TEAM_MEMBER_HIT_ANTISPAM:{code:109451,message:"team member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_AND_ACCOUNT_MISMATCH:{code:110302,message:"conversation and account mismatch"},V2NIM_ERROR_CODE_CONVERSATION_STICK_TOP_LIMIT:{code:110303,message:"conversation stick top limit"},V2NIM_ERROR_CODE_CONVERSATION_BELONGED_GROUP_LIMIT:{code:110304,message:"conversation belonged group limit"},V2NIM_ERROR_CODE_CONVERSATION_NOT_EXIST:{code:110404,message:"conversation not exist"},V2NIM_ERROR_CODE_CHATROOM_LINK_UNAVAILABLE:{code:113304,message:"chatroom link unavailable"},V2NIM_ERROR_CODE_IM_CONNECTION_ABNORMAL:{code:113305,message:"IM connection abnormal"},V2NIM_ERROR_CODE_CHATROOM_NOT_EXIST:{code:113404,message:"chatroom not exist"},V2NIM_ERROR_CODE_CHATROOM_CLOSED:{code:113406,message:"chatroom closed"},V2NIM_ERROR_CODE_CHATROOM_REPEATED_OPERATION:{code:113409,message:"chatroom repeated operation"},V2NIM_ERROR_CODE_CHATROOM_DISABLED:{code:113410,message:"chatroom disabled"},V2NIM_ERROR_CODE_ALL_CHATROOM_MEMBER_CHAT_BANNED:{code:113423,message:"all chatroom member chat banned"},V2NIM_ERROR_CODE_CHATROOM_HIT_ANTISPAM:{code:113451,message:"chatroom hit antispam"},V2NIM_ERROR_CODE_ANONYMOUS_MEMBER_FORBIDDEN:{code:114303,message:"anonymous member forbidden"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_NOT_EXIST:{code:114404,message:"chatroom member not exist"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_REPEATED_OPERATION:{code:114405,message:"chatroom member repeated operation"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CHAT_BANNED:{code:114421,message:"chatroom member chat banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_CHATROOM_BLOCK_LIST:{code:114426,message:"account in chatroom block list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:114427,message:"chatroom owner operation permission required"},V2NIM_ERROR_CODE_SELF_IN_CHATROOM_MEMBER_OPERATION_LIST:{code:114429,message:"self in chatroom member operation list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:114432,message:"chatroom owner or manager operation permission required"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_LIMIT:{code:114437,message:"chatroom member limit"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:114449,message:"chatroom member concurrent operation failed"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_HIT_ANTISPAM:{code:114451,message:"chatroom member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_NOT_EXIST:{code:116404,message:"conversation group not exist"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_LIMIT:{code:116435,message:"conversation group limit"},V2NIM_ERROR_CODE_CONVERSATIONS_IN_GROUP_LIMIT:{code:116437,message:"conversations in group limit"},V2NIM_ERROR_CODE_COLLECTION_LIMIT:{code:189301,message:"collection limit"},V2NIM_ERROR_CODE_COLLECTION_NOT_EXIST:{code:189302,message:"collection not exist"},V2NIM_ERROR_CODE_COLLECTION_CONCURRENT_OPERATION_FAILED:{code:189449,message:"collection concurrent operation failed"},V2NIM_ERROR_CODE_INTERNAL:{code:190001,message:"internal error"},V2NIM_ERROR_CODE_ILLEGAL_STATE:{code:190002,message:"illegal state"},V2NIM_ERROR_CODE_MISUSE:{code:191001,message:"misuse"},V2NIM_ERROR_CODE_CANCELLED:{code:191002,message:"operation cancelled"},V2NIM_ERROR_CODE_CALLBACK_FAILED:{code:191003,message:"callback failed"},V2NIM_ERROR_CODE_INVALID_PARAMETER:{code:191004,message:"invalid parameter"},V2NIM_ERROR_CODE_TIMEOUT:{code:191005,message:"timeout"},V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST:{code:191006,message:"resource not exist"},V2NIM_ERROR_CODE_RESOURCE_ALREADY_EXIST:{code:191007,message:"resource already exist"},V2NIM_ERROR_CODE_CONNECT_FAILED:{code:192001,message:"connect failed"},V2NIM_ERROR_CODE_CONNECT_TIMEOUT:{code:192002,message:"connect timeout"},V2NIM_ERROR_CODE_DISCONNECT:{code:192003,message:"disconnect"},V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT:{code:192004,message:"protocol timeout"},V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED:{code:192005,message:"protocol send failed"},V2NIM_ERROR_CODE_REQUEST_FAILED:{code:192006,message:"request failed"},V2NIM_ERROR_CODE_FILE_NOT_FOUND:{code:194001,message:"file not found"},V2NIM_ERROR_CODE_FILE_CREATE_FAILED:{code:194002,message:"file create failed"},V2NIM_ERROR_CODE_FILE_OPEN_FAILED:{code:194003,message:"file open failed"},V2NIM_ERROR_CODE_FILE_WRITE_FAILED:{code:194004,message:"file write failed"},V2NIM_ERROR_CODE_FILE_READ_FAILED:{code:194005,message:"file read failed"},V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:{code:194006,message:"file upload failed"},V2NIM_ERROR_CODE_FILE_DOWNLOAD_FAILED:{code:194007,message:"file download failed"},V2NIM_ERROR_CODE_CLIENT_ANTISPAM:{code:195001,message:"client anti-spam"},V2NIM_ERROR_CODE_SERVER_ANTISPAM:{code:195002,message:"server anti-spam"}},xd=Za(Pd),Ud=reduce(xd).call(xd,(function(t,a){var u=Pd[a];return t[a]=u.code,t}),{}),Fd=reduce(xd).call(xd,(function(t,a){var u=Pd[a];return t[u.code]=u.message,t}),{}),Bd=Object.freeze({__proto__:null,V2NIMErrorCode:Ud,V2NIMErrorDesc:Fd,get V2NIMLoginAuthType(){return ld},get V2NIMLoginStatus(){return ud},get V2NIMLoginClientType(){return dd},get V2NIMLoginClientChange(){return _d},get V2NIMConnectStatus(){return pd},get V2NIMChatroomKickedReason(){return hd},get V2NIMChatroomMemberRole(){return fd},get V2NIMMessageSendingState(){return Ed},get V2NIMMessageAttachmentUploadState(){return md},get V2NIMMessageType(){return gd},get V2NIMQueryDirection(){return Id},get V2NIMChatroomMessageNotificationType(){return vd},get V2NIMClientAntispamOperateType(){return Td},get V2NIMChatroomStatus(){return Nd},get V2NIMChatroomQueueLevelMode(){return Sd}}),Gd=function(){function V2NIMLoginReconnect(t){this.currenRetryCount=0,this.reconnectTimer=0,this.backoffIntervals=[1e3,2e3,3e3],this.currReconnectInterval=0,this.core=t,this.auth=t.auth}var t=V2NIMLoginReconnect.prototype;return t.reset=function reset(){this.currenRetryCount=0,this.reconnectTimer&&clearTimeout(this.reconnectTimer)},t.clearReconnectTimer=function clearReconnectTimer(){this.reconnectTimer&&clearTimeout(this.reconnectTimer)},t.attempToReLogin=function attempToReLogin(){var t=this,a=this.backoffIntervals[this.currReconnectInterval];return this.currReconnectInterval=(this.currReconnectInterval+1)%this.backoffIntervals.length,this.currenRetryCount++,this.core.logger.log("reconnect::reconnect timer is about to be set, delay "+a+" ms, current retry count is "+this.currenRetryCount),this.clearReconnectTimer(),this.reconnectTimer=ls((function(){t.core.logger.log("reconnect::reconnect timer is now triggered");var a=t.auth.getConnectStatus();3===a?t.doReLogin():t.core.logger.warn("reconnect::reconnect timer is over because connect status now is "+a)}),a),!0},t.doReLogin=function doReLogin(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var t,a,u=this;return Sa.wrap((function _callee$(_){for(;;)switch(_.prev=_.next){case 0:return this.auth.connectParams.forceMode=!1,_.prev=1,_.next=4,this.auth.updateDynamicParamters(!1);case 4:_.next=9;break;case 6:return _.prev=6,_.t0=_.catch(1),_.abrupt("return",this.auth.lifeCycle.processEvent("waiting"));case 9:return t=this.core.timeOrigin.getTimeNode(),this.auth.originLoginPromise=this.auth.doLogin(!0),_.prev=11,_.next=14,this.auth.previousLoginManager.add(this.auth.originLoginPromise);case 14:this.currReconnectInterval=0,this.auth.reportLoginSucc(t),_.next=29;break;case 18:if(_.prev=18,_.t1=_.catch(11),a=_.t1,this.core.logger.warn("reconnect::try login but failed due to",a),this.auth.reportLoginFail(t,a),!this.auth.checkLoginTerminalCode(a&&a.code)){_.next=28;break}return this.auth.clientSocket.doDisconnect(Xu.ACTIVE,"ReloginTerminated}"),this.auth.lifeCycle.processEvent("exited",a),_.abrupt("return");case 28:a&&a.code===Ud.V2NIM_ERROR_CODE_SERVER_UNIT_ERROR?this.auth.updateLinkAddress().then((function(){u.auth.lifeCycle.processEvent("waiting")})).catch((function(t){u.auth.lifeCycle.processEvent("reconnectFail",t)})):this.auth.lifeCycle.processEvent("waiting");case 29:case"end":return _.stop()}}),_callee,this,[[1,6],[11,18]])})))},V2NIMLoginReconnect}(),Hd=function(){function V2NIMLoginAuthenticator(t){this.lastLoginClientKey="__NIM_LAST_LOGIN_CLIENT__",this.loginClients=[],this.loginClientOfThisConnection={},this.core=t,this.auth=t.auth}var t=V2NIMLoginAuthenticator.prototype;return t.verifyAuthentication=function verifyAuthentication(t){var a,u,_,h,E,m,g,I,T,N,M,O,S,R;return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var C,A,b,k,L,D,V,w,P,x;return Sa.wrap((function _callee$(U){for(;;)switch(U.prev=U.next){case 0:return A=Es.getSystemInfo(),b={clientType:16,os:A.os,sdkVersion:100830,appLogin:t?0:1,protocolVersion:1,deviceId:this.auth.deviceId,appkey:this.auth.appkey,account:this.auth.account,browser:A.browser,clientSession:this.auth.clientSession,customClientType:this.core.options.customClientType,sdkHumanVersion:"10.8.30",userAgent:this.core.options.loginSDKTypeParamCompat?"Native/10.8.30":slice(C=A.userAgent.replace("{{appkey}}",this.auth.appkey)).call(C,0,299),libEnv:this.core.options.loginSDKTypeParamCompat?void 0:A.libEnv,hostEnv:this.core.options.loginSDKTypeParamCompat?0:A.hostEnvEnum,authType:this.auth.authType,loginExt:this.auth.loginExt,token:this.auth.token},k=ba(ba({appkey:this.auth.appkey,account:this.auth.account,deviceId:this.auth.deviceId,chatroomId:this.auth.roomId,appLogin:t?0:1,chatroomNick:null===(a=this.auth.enterParams)||void 0===a?void 0:a.roomNick,chatroomAvatar:(null===(u=this.auth.enterParams)||void 0===u?void 0:u.roomAvatar)||"",serverExtension:null===(_=this.auth.enterParams)||void 0===_?void 0:_.serverExtension,notificationExtension:null===(h=this.auth.enterParams)||void 0===h?void 0:h.notificationExtension,clientSession:this.auth.clientSession,isAnonymous:this.auth.isAnonymous,tags:null===(m=null===(E=this.auth.enterParams)||void 0===E?void 0:E.tagConfig)||void 0===m?void 0:m.tags,notifyTargetTags:null===(I=null===(g=this.auth.enterParams)||void 0===g?void 0:g.tagConfig)||void 0===I?void 0:I.notifyTargetTags,authType:this.auth.authType,loginExt:this.auth.loginExt},null===(N=null===(T=this.auth.enterParams)||void 0===T?void 0:T.locationConfig)||void 0===N?void 0:N.locationInfo),{distance:null===(O=null===(M=this.auth.enterParams)||void 0===M?void 0:M.locationConfig)||void 0===O?void 0:O.distance,antiSpamBusinessId:null===(R=null===(S=this.auth.enterParams)||void 0===S?void 0:S.antispamConfig)||void 0===R?void 0:R.antispamBusinessId}),U.prev=3,this.auth.lifeCycle.processEvent("loginStart"),U.next=7,this.auth.doLoginStepsManager.add(this.auth.clientSocket.sendCmd("v2ChatroomLogin",{type:1,chatroomLogin:k,chatroomIMLogin:b}));case 7:L=U.sent,U.next=18;break;case 10:if(U.prev=10,U.t0=U.catch(3),D=U.t0,this.core.reporter.reportTraceUpdateV2("login",{operation_type:"protocol",target:"26-3",code:D.code||0,succeed:!1,description:D.message},{asyncParams:Es.net.getNetworkStatus()}),D.code!==Ud.V2NIM_ERROR_CODE_CANCELLED&&D.code!==Ud.V2NIM_ERROR_CODE_TIMEOUT){U.next=16;break}throw D;case 16:throw this.processLoginFailed(D),D;case 18:return V=L.content,w=V.chatroomInfo,P=V.chatroomMember,x=V.chatroomCdnInfo,this.core.V2NIMChatroomMessageService.cdnUtil.setOptions(x),U.abrupt("return",{chatroom:w,selfMember:P});case 21:case"end":return U.stop()}}),_callee,this,[[3,10]])})))},t.processLoginFailed=function processLoginFailed(t){this.auth.clientSocket.doDisconnect(Xu.ACTIVE,t),this.checkLoginTerminalCode(t.code)&&(this.auth.authenticator.reset(),this.auth.authenticator.clearLastLoginClient()),this.auth.lifeCycle.processEvent("loginFail",t)},t.changeLoginClient=function changeLoginClient(t,a){},t.checkAutoLogin=function checkAutoLogin(t){if(t)return!1;var a=Es.localStorage.getItem(this.lastLoginClientKey);if(!a)return!1;var u="",_="";try{var h=JSON.parse(a);u=get(h,"clientId"),_=get(h,"account")}catch(t){return!1}return u===this.auth.deviceId&&_===this.auth.account},t.checkLoginTerminalCode=function checkLoginTerminalCode(t){var a=[Ud.V2NIM_ERROR_CODE_CANCELLED,Ud.V2NIM_ERROR_CODE_TIMEOUT,Ud.V2NIM_ERROR_CODE_HANDSHAKE,302,317,Ud.V2NIM_ERROR_CODE_FORBIDDEN,Ud.V2NIM_ERROR_CODE_NOT_FOUND,Ud.V2NIM_ERROR_CODE_PARAMETER_ERROR,Ud.V2NIM_ERROR_CODE_MULTI_LOGIN_FORBIDDEN,422,Ud.V2NIM_ERROR_CODE_IM_DISABLED,Ud.V2NIM_ERROR_CODE_APPKEY_NOT_EXIST,Ud.V2NIM_ERROR_CODE_BUNDLEID_CHECK_FAILED,Ud.V2NIM_ERROR_CODE_APPKEY_BLOCKED,Ud.V2NIM_ERROR_CODE_INVALID_TOKEN,Ud.V2NIM_ERROR_CODE_ROBOT_NOT_ALLOWED,Ud.V2NIM_ERROR_CODE_ACCOUNT_NOT_EXIST,Ud.V2NIM_ERROR_CODE_ACCOUNT_BANNED,Ud.V2NIM_ERROR_CODE_SERVICE_ADDRESS_INVALID,Ud.V2NIM_ERROR_CODE_CHATROOM_DISABLED,Ud.V2NIM_ERROR_CODE_CHATROOM_NOT_EXIST,Ud.V2NIM_ERROR_CODE_CHATROOM_CLOSED,Ud.V2NIM_ERROR_CODE_ACCOUNT_IN_CHATROOM_BLOCK_LIST];return includes(a).call(a,t)},t.reset=function reset(){this.loginClients=[],this.loginClientOfThisConnection={}},t.clearLastLoginClient=function clearLastLoginClient(){Es.localStorage.removeItem(this.lastLoginClientKey)},V2NIMLoginAuthenticator}(),Yd=function(){function V2NIMLoginLifeCycle(t){this.name="V2NIMLoginLifeCycle",this.chatroomStatus=6,this.entered=!1,this.core=t,this.auth=t.auth,this.logger=t.logger}var t=V2NIMLoginLifeCycle.prototype;return t.processEvent=function processEvent(t,a,u){var _=this.getConnectStatus();switch(t){case"connect":this.logger.log(this.name+"::connecting"),this.setChatroomStatus(2);break;case"connectSucc":this.logger.log(this.name+"::connect success"),this.setChatroomStatus(3);break;case"connectFail":this.logger.log(this.name+"::connect fail",a),this.setChatroomStatus(0,a);break;case"connectionBroken":this.logger.log(this.name+"::connectionBroken",a),this.setChatroomStatus(0,a);break;case"loginStart":this.logger.log(this.name+"::login start"),this.setChatroomStatus(4);break;case"loginSucc":this.logger.log(this.name+"::login success, verify authentication success"),this.setChatroomStatus(5),this.core.eventBus.emit("V2NIMLoginService/loginLifeCycleLoginSucc",u);break;case"loginFail":this.logger.log(this.name+"::login fail due to verify authentication failed:",a),this.setChatroomStatus(0,a);break;case"logout":this.logger.log(this.name+"::logout"),this.setChatroomStatus(6),this.core.eventBus.emit("V2NIMLoginService/loginLifeCycleLogout");break;case"kicked":this.logger.log(this.name+"::kicked",u),this.setChatroomStatus(6,a),this.core.eventBus.emit("V2NIMLoginService/loginLifeCycleKicked");break;case"reconnectFail":this.logger.log(this.name+"::reconnect fail",a),this.setChatroomStatus(6,a),this.core.eventBus.emit("V2NIMLoginService/loginLifeCycleLogout");break;case"exited":if(this.logger.log(this.name+"::exited, isEntered: "+this.entered,a),6===this.chatroomStatus)return;this.setChatroomStatus(this.entered?6:0,a);break;case"waiting":this.logger.log(this.name+"::waiting to reconnect"),this.setChatroomStatus(1),2!==_&&this.auth.reconnect.attempToReLogin()}},t.getConnectStatus=function getConnectStatus(){switch(this.chatroomStatus){case 6:case 0:return 0;case 5:case 4:case 3:return 1;case 2:return 2;case 1:return 3}},t.getLoginStatus=function getLoginStatus(){switch(this.chatroomStatus){case 1:return 3;case 6:case 0:return 0;case 3:case 2:case 4:return 2;case 5:return 1}},t.setChatroomStatus=function setChatroomStatus(t,a){5===t&&(this.entered=!0),this.chatroomStatus!==t&&(this.chatroomStatus=t,6===t&&this.core._clearModuleData(),this.core.emit("onChatroomStatus",t,a),6===t&&this.entered&&(this.entered=!1,this.core.emit("onChatroomExited",a)))},V2NIMLoginLifeCycle}(),jd=entryVirtual("Array").values,Kd=Array.prototype,qd={DOMTokenList:!0,NodeList:!0},values=function(t){var a=t.values;return t===Kd||j(Kd,t)&&a===Kd.values||ue(qd,xt(t))?jd:a},Wd=Ar.every,zd=arrayMethodIsStrict("every");_export({target:"Array",proto:!0,forced:!zd},{every:function every(t){return Wd(this,t,arguments.length>1?arguments[1]:void 0)}});var $d=entryVirtual("Array").every,Xd=Array.prototype,every=function(t){var a=t.every;return t===Xd||j(Xd,t)&&a===Xd.every?$d:a};function replacer(t,a){return a instanceof RegExp?"__REGEXP "+a.toString():a}function validate(t,a,u,_){var h;void 0===a&&(a={}),void 0===_&&(_=!1);var E={};return forEach$1(h=Za(t)).call(h,(function(h){var m=t[h].type,g=u?"In "+u+", ":"";if(null==a){var I=g+"param is null or undefined";throw _?new Nl({detail:{reason:I,data:{key:h},rules:"required"}}):new Tl(I,{key:h},"required")}if(void 0===a[h]){if(!1===t[h].required)return void(E[h]=a[h]);var T=g+"param '"+h+"' is required";throw _?new Nl({detail:{reason:T,data:{key:h},rules:"required"}}):new Tl(T,{key:h},"required")}var N=Qd[m];if(N&&!N(a,h,t[h],_)){var M=g+"param '"+h+"' unexpected",O={key:h,value:a[h]};throw _?new Nl({detail:{reason:M,data:O,rules:Ms(t[h],replacer)}}):new Tl(M,O,Ms(t[h],replacer))}E[h]=a[h]})),E}var Qd={string:function string(t,a,u){var _=u.allowEmpty,h=u.max,E=u.min,m=u.regExp,g=t[a];return"string"==typeof g&&((!1!==_||""!==g)&&(!("number"==typeof h&&g.length>h)&&(!("number"==typeof E&&g.length<E)&&!(function isRegExp(t){return"[object RegExp]"===Object.prototype.toString.call(t)}(m)&&!m.test(g)))))},number:function number(t,a,u){var _=u.min,h=u.max,E=t[a];return"number"==typeof E&&(!("number"==typeof _&&E<_)&&!("number"==typeof h&&E>h))},boolean:function boolean(t,a){return"boolean"==typeof t[a]},file:function file(t,a){return!0},enum:function _enum(t,a,u){var _=values(u),h=t[a];return!_||indexOf(_).call(_,h)>-1},jsonstr:function jsonstr(t,a){try{var u=JSON.parse(t[a]);return"object"==typeof u&&null!==u}catch(t){return!1}},func:function func(t,a){return"function"==typeof t[a]},array:function array(t,a,u,_){void 0===_&&(_=!1);var h=u.itemType,E=u.itemRules,m=u.rules,g=u.min,I=u.max,T=values(u),N=t[a];if(!fs(N))return!1;if("number"==typeof I&&N.length>I)return!1;if("number"==typeof g&&N.length<g)return!1;if(E)forEach$1(N).call(N,(function(t,u){var h,m;validate(((h={})[u]=E,h),((m={})[u]=t,m),a+"["+u+"]",_)}));else if(m)forEach$1(N).call(N,(function(t,u){return validate(m,t,a+"["+u+"]",_)}));else if("enum"===h){if(T&&function difference(t,a){return a=a||[],filter(t=t||[]).call(t,(function(t){return-1===indexOf(a).call(a,t)}))}(N,T).length)return!1}else if(h&&!every(N).call(N,(function(t){return typeof t===h})))return!1;return!0},object:function object(t,a,u,_){void 0===_&&(_=!1);var h=u.rules,E=u.allowEmpty,m=t[a];if("object"!=typeof m||null===m)return!1;if(h){var g=Za(h),I=Za(m),T=filter(I).call(I,(function(t){return indexOf(g).call(g,t)>-1}));if(!1===E&&0===T.length)return!1;validate(h,m,a,_)}return!0}},Jd={updateParams:{type:"object",allowEmpty:!1,rules:{roomName:{type:"string",required:!1,allowEmpty:!1},announcement:{type:"string",required:!1},liveUrl:{type:"string",required:!1},serverExtension:{type:"string",required:!1},notificationEnabled:{type:"boolean",required:!1},notificationExtension:{type:"string",required:!1}}},antispamConfig:{type:"object",required:!1,rules:{antispamBusinessId:{type:"string",required:!1}}}},Zd={locationInfo:{type:"object",rules:{x:{type:"number"},y:{type:"number"},z:{type:"number"}}},distance:{type:"number"}},e_={updateParams:{type:"object",allowEmpty:!1,rules:{tags:{type:"array",required:!1,itemType:"string"},notifyTargetTags:{type:"string",required:!1},notificationEnabled:{type:"boolean",required:!1},notificationExtension:{type:"string",required:!1}}}},t_={targetTag:{type:"string",required:!0,allowEmpty:!1},notifyTargetTags:{type:"string",required:!1},duration:{type:"number",required:!1},notificationEnabled:{type:"boolean",required:!1},notificationExtension:{type:"string",required:!1}},r_={roomId:{allowEmpty:!1,type:"string"},enterParams:{type:"object",required:!0,rules:{anonymousMode:{required:!1,type:"boolean"},accountId:{required:!1,type:"string",allowEmpty:!1},token:{required:!1,type:"string",allowEmpty:!1},roomNick:{required:!1,type:"string"},roomAvatar:{required:!1,type:"string"},loginOption:{type:"object",required:!1,rules:{authType:{type:"enum",required:!1,values:[0,1,2]},tokenProvider:{required:!1,type:"func"},loginExtensionProvider:{required:!1,type:"func"}}},linkProvider:{type:"func"},serverExtension:{type:"string",required:!1,allowEmpty:!1},notificationExtension:{type:"string",required:!1,allowEmpty:!1},tagConfig:{type:"object",required:!1,rules:{notifyTargetTags:{type:"string",required:!1},tags:{type:"array",required:!1,itemType:"string"}}},locationConfig:{type:"object",required:!1,rules:Zd},antispamConfig:{type:"object",required:!1,rules:{antispamBusinessId:{type:"string",required:!1,allowEmpty:!1}}}}}},n_=function validateLinkAddressArray(t){if(!fs(t))throw new vl({code:400,message:"linkAddressArray must be an array"});if(0===t.length)throw new vl({code:400,message:"linkAddressArray must not be empty"});forEach$1(t).call(t,(function(t){if(!t)throw new vl({code:400,message:"linkAddress must not be empty"})}))},o_=function(t){function V2ChatroomService(a,u){var _;return(_=t.call(this)||this).name=a,_.logger=u.logger,_.core=u,_}At(V2ChatroomService,t);var a=V2ChatroomService.prototype;return a.emit=function emit(a){for(var u=this,_=arguments.length,h=new Array(_>1?_-1:0),E=1;E<_;E++)h[E-1]=arguments[E];this.logger.debug(this.name+"::emit event: '"+a.toString()+"',",void 0!==h[0]?h[0]:"",void 0!==h[1]?h[1]:"",void 0!==h[2]?h[2]:"");try{var m,g,I=(m=t.prototype.emit).call.apply(m,concat(g=[this,a]).call(g,h));return I}catch(t){return ls((function(){throw u.logger.error(u.name+"::emit throw error in setTimeout. event: "+a.toString()+". Error",t),t}),0),!1}},a.process=function process(t){var a=this[t.cmd+"Handler"];if("function"==typeof a){if(t.error)return this.logger.error(t.cmd+"::recvError",t.error),_a.reject(t.error);try{var u=a.call(this,t);return _a.resolve(u)}catch(t){return _a.reject(t)}}var _=get(t,"error.detail.ignore");return t.error&&!_?_a.reject(t.error):_a.resolve(t)},V2ChatroomService}(xl),i_=function(t){function V2NIMChatroomLoginServiceImpl(a){var u;return(u=t.call(this,"V2NIMChatroomLoginService",a)||this).roomId="",u.token="",u.loginExt="",u.authType=0,u.linkAddressArray=[],u.currLinkIdx=-1,u.isAnonymous=!1,u.processId="",u.connectParams={forceMode:!1},registerParser({cmdMap:Ld,cmdConfig:wd}),a.auth=$e(u),u.previousLoginManager=new Cu,u.doLoginStepsManager=new Cu,u.loginTimerManager=new id,u.lifeCycle=new Yd(a),u.reconnect=new Gd(a),u.authenticator=new Hd(a),u}At(V2NIMChatroomLoginServiceImpl,t);var a=V2NIMChatroomLoginServiceImpl.prototype;return a.getNextLink=function getNextLink(){return this.currLinkIdx=(this.currLinkIdx+1)%this.linkAddressArray.length,this.linkAddressArray[this.currLinkIdx]},a.getCurrLink=function getCurrLink(){return this.linkAddressArray[this.currLinkIdx]},a.reset=function reset(){this.roomId="",this.token="",this.loginExt="",this.processId="",this.reconnect.reset(),this.authenticator.reset(),this.authenticator.clearLastLoginClient()},a.login=function login(t,a,u){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var _,h=this;return Sa.wrap((function _callee$(E){for(;;)switch(E.prev=E.next){case 0:if(validate(r_,{appkey:t,roomId:a,enterParams:u},"",!0),0!==this.getLoginStatus()){E.next=5;break}this.logger.log("V2NIMChatroomLoginService::login:allowLogin. appkey:"+t+";roomId:"+a+";accountId:"+u.accountId+" "),E.next=11;break;case 5:if(1!==this.getLoginStatus()){E.next=9;break}return E.abrupt("return",this.smoothForLogined(t,a,u));case 9:if(2!==this.getLoginStatus()){E.next=11;break}return E.abrupt("return",this.smoothForLogining(t,a,u));case 11:return this.processId=$u(),E.next=14,this.setLoginParams(t,a,u);case 14:return this.loginTimerManager.destroy(),this.loginTimerManager.addTimer((function(){var t=new vl({code:Ud.V2NIM_ERROR_CODE_TIMEOUT,detail:{reason:"Login API timeout"}});h.doLoginStepsManager.clear(t),h.previousLoginManager.clear(t),h.originLoginPromise=void 0,h.lifeCycle.processEvent("exited",t)}),u.timeout?1e3*u.timeout:6e4,1),E.prev=16,E.next=19,this.multiTryDoLogin();case 19:return _=E.sent,this.core.emit("onChatroomEntered"),this.loginTimerManager.destroy(),E.abrupt("return",_);case 25:throw E.prev=25,E.t0=E.catch(16),this.loginTimerManager.destroy(),E.t0;case 29:case"end":return E.stop()}}),_callee,this,[[16,25]])})))},a.multiTryDoLogin=function multiTryDoLogin(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var a,u,_,h,E;return Sa.wrap((function _callee2$(m){for(;;)switch(m.prev=m.next){case 0:a=this.core.timeOrigin.getTimeNode(),u=new vl({code:Ud.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"loginFailed"}}),_=0;case 3:if(!(_<this.linkAddressArray.length)){m.next=32;break}return h="V2NIMChatroomLoginService:: "+(_+1)+"th login attempt.",_>0?this.logger.warn(h):this.logger.log(h),m.prev=6,this.originLoginPromise=t||this.doLogin(!1),t=void 0,m.next=11,this.previousLoginManager.add(this.originLoginPromise);case 11:return E=m.sent,this.doLoginStepsManager.clear(),this.previousLoginManager.clear(),this.originLoginPromise=void 0,this.reportLoginSucc(a),m.abrupt("return",E);case 19:if(m.prev=19,m.t0=m.catch(6),u=m.t0||u,this.logger.error("V2NIMChatroomLoginService::login failed, times of login try: "+_+", err.code: "+(null==u?void 0:u.code)+', err.message: "'+(null==u?void 0:u.message)+'"'),this.reportLoginFail(a,u),this.reconnect.clearReconnectTimer(),!(this.checkLoginTerminalCode(u&&u.code)||u&&u.code===Ud.V2NIM_ERROR_CODE_SERVER_UNIT_ERROR)){m.next=29;break}throw this.lifeCycle.processEvent("exited",u),u;case 29:_++,m.next=3;break;case 32:throw this.lifeCycle.processEvent("exited",u),u;case 34:case"end":return m.stop()}}),_callee2,this,[[6,19]])})))},a.doLogin=function doLogin(t){var a;return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var u,_;return Sa.wrap((function _callee3$(h){for(;;)switch(h.prev=h.next){case 0:return u=!!t||this.authenticator.checkAutoLogin(this.connectParams.forceMode),h.next=3,this.doLoginStepsManager.add(this.clientSocket.connect(this.getNextLink(),t));case 3:return h.next=5,this.doLoginStepsManager.add(this.authenticator.verifyAuthentication(u));case 5:return _=h.sent,this.lifeCycle.processEvent("loginSucc",void 0,ba(ba({},_),{isReconnect:t})),this.clientSocket.resetSocketConfig(),this.reconnect.reset(),this.clientSocket.ping(),this.core.abtest.abtRequest(),this.core.V2NIMClientAntispamUtil.downloadLocalAntiSpamVocabs(),h.prev=12,h.next=15,this.core.cloudStorage.init(null===(a=_.selfMember)||void 0===a?void 0:a.enterTime);case 15:h.next=20;break;case 17:h.prev=17,h.t0=h.catch(12),this.logger.warn("doLogin::cloudStorage init error",h.t0);case 20:return this.prevLoginResult=_,h.abrupt("return",_);case 22:case"end":return h.stop()}}),_callee3,this,[[12,17]])})))},a.reportLoginSucc=function reportLoginSucc(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){var a,u,_,h;return Sa.wrap((function _callee4$(E){for(;;)switch(E.prev=E.next){case 0:return a=this.core.timeOrigin.getNTPTime(),u=Ga()-t.time,this.core.timeOrigin.checkNodeReliable(t)&&(u=a-this.core.timeOrigin.getNTPTime(t)),E.next=5,Es.net.getNetworkStatus();case 5:_=E.sent,h=_.net_connect,this.core.reporter.report("chatroomLogin",{accid:this.account,roomId:this.roomId,serverIps:this.linkAddressArray,currentServerIp:this.getCurrLink(),rt:u,result:200,failReason:"",time:a,net_connect:h,binary_websocket:this.core.config.binaryWebsocket});case 8:case"end":return E.stop()}}),_callee4,this)})))},a.reportLoginFail=function reportLoginFail(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){var u,_,h,E,m,g;return Sa.wrap((function _callee5$(I){for(;;)switch(I.prev=I.next){case 0:if(u=get(a,"code")||get(a,"detail.rawError.code")||0,_=get(a,"detail.rawError.message")||get(a,"message")||"login failed",u!==Ud.V2NIM_ERROR_CODE_CANCELLED){I.next=4;break}return I.abrupt("return");case 4:return h=this.core.timeOrigin.getNTPTime(),E=Ga()-t.time,this.core.timeOrigin.checkNodeReliable(t)&&(E=h-this.core.timeOrigin.getNTPTime(t)),I.next=9,Es.net.getNetworkStatus();case 9:m=I.sent,g=m.net_connect,this.core.reporter.report("chatroomLogin",{accid:this.account,roomId:this.roomId,serverIps:this.linkAddressArray,currentServerIp:this.getCurrLink(),rt:E,result:u,failReason:_,time:h,net_connect:g,binary_websocket:this.core.config.binaryWebsocket});case 12:case"end":return I.stop()}}),_callee5,this)})))},a.smoothForLogined=function smoothForLogined(t,a,u){return __awaiter(this,void 0,void 0,Sa.mark((function _callee6(){var _;return Sa.wrap((function _callee6$(h){for(;;)switch(h.prev=h.next){case 0:if(_=this.checkIsSameLogin(t,a,u),this.logger.warn("V2NIMChatroomLoginService::smoothForLogined:Logined, isSameLogin "+_),!_){h.next=6;break}return h.abrupt("return",this.prevLoginResult);case 6:return h.next=8,this.logout();case 8:return h.abrupt("return",this.login(t,a,u));case 9:case"end":return h.stop()}}),_callee6,this)})))},a.smoothForLogining=function smoothForLogining(t,a,u){return __awaiter(this,void 0,void 0,Sa.mark((function _callee7(){var _;return Sa.wrap((function _callee7$(h){for(;;)switch(h.prev=h.next){case 0:if(_=this.checkIsSameLogin(t,a,u),this.previousLoginManager.clear(),this.reconnect.reset(),!_){h.next=14;break}if(this.originLoginPromise){h.next=6;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"NoPreviousLoginExists"}});case 6:return this.reconnect.reset(),h.next=9,_a.resolve();case 9:return h.next=11,this.multiTryDoLogin(this.originLoginPromise);case 11:return h.abrupt("return",h.sent);case 14:return this.doLoginStepsManager.clear(),this.clientSocket.doDisconnect(Xu.ACTIVE,"Aborted"),this.reset(),this.lifeCycle.processEvent("logout",new vl({code:Ud.V2NIM_ERROR_CODE_DISCONNECT,detail:{reason:"disconnect due to logout. aborted previous login."}})),h.next=20,_a.resolve();case 20:return h.abrupt("return",this.login(t,a,u));case 21:case"end":return h.stop()}}),_callee7,this)})))},a.checkIsSameLogin=function checkIsSameLogin(t,a,u){var _,h,E,m,g,I,T,N=void 0!==u.anonymousMode&&u.anonymousMode,M=(null===(_=u.loginOption)||void 0===_?void 0:_.authType)||0,O=Ms((null===(E=null===(h=this.enterParams)||void 0===h?void 0:h.tagConfig)||void 0===E?void 0:E.tags)||[]),S=Ms((null===(m=u.tagConfig)||void 0===m?void 0:m.tags)||[]),R=Ms((null===(g=this.enterParams)||void 0===g?void 0:g.locationConfig)||{}),C=Ms(u.locationConfig||{});return this.appkey===t&&this.roomId===a&&this.authType===M&&this.isAnonymous===N&&this.account===u.accountId&&(null===(I=this.enterParams)||void 0===I?void 0:I.roomNick)===u.roomNick&&(null===(T=this.enterParams)||void 0===T?void 0:T.roomAvatar)===u.roomAvatar&&O===S&&R===C},a.logout=function logout(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee8(){var t,a,u;return Sa.wrap((function _callee8$(_){for(;;)switch(_.prev=_.next){case 0:this.doLoginStepsManager.clear(),this.previousLoginManager.clear(),this.loginTimerManager.destroy(),this.originLoginPromise=void 0,t=this.getConnectStatus(),a=this.getLoginStatus(),u=new vl({code:Ud.V2NIM_ERROR_CODE_DISCONNECT,detail:{reason:"disconnect due to logout"}}),_.t0=a,_.next=1===_.t0?10:2===_.t0?23:3===_.t0?26:0===_.t0?30:32;break;case 10:return _.prev=10,_.next=13,this.clientSocket.sendCmd("v2ChatroomLogout",void 0,{timeout:1e3});case 13:this.clientSocket.doDisconnect(Xu.ACTIVE,"UserActiveDisconnect"),this.lifeCycle.processEvent("logout",u),_.next=22;break;case 17:_.prev=17,_.t1=_.catch(10),this.logger.error("Instance::disconnect sendCmd:logout error",_.t1),this.clientSocket.doDisconnect(Xu.ACTIVE,"UserActiveDisconnect"),this.lifeCycle.processEvent("logout",u);case 22:return _.abrupt("break",34);case 23:return this.clientSocket.doDisconnect(Xu.ACTIVE,"UserActiveDisconnect"),this.lifeCycle.processEvent("logout",u),_.abrupt("break",34);case 26:return this.clientSocket.doDisconnect(Xu.ACTIVE,"UserActiveDisconnect"),this.core._clearModuleData(),this.lifeCycle.processEvent("logout",u),_.abrupt("break",34);case 30:throw this.core._clearModuleData(),new vl({code:Ud.V2NIM_ERROR_CODE_ILLEGAL_STATE,detail:{reason:"Illegal logout. loginStatus "+a+". connectStatus "+t}});case 32:throw this.core._clearModuleData(),new vl({code:Ud.V2NIM_ERROR_CODE_ILLEGAL_STATE,detail:{reason:"Illegal logout. illegal status: loginStatus "+a+". connectStatus "+t}});case 34:case"end":return _.stop()}}),_callee8,this,[[10,17]])})))},a.getConnectStatus=function getConnectStatus(){return this.lifeCycle.getConnectStatus()},a.getLoginStatus=function getLoginStatus(){return this.lifeCycle.getLoginStatus()},a.getLoginUser=function getLoginUser(){return this.account},a.getRoomId=function getRoomId(){return this.roomId},a.checkLoginTerminalCode=function checkLoginTerminalCode(t){return this.authenticator.checkLoginTerminalCode(t)},a.updateLinkAddress=function updateLinkAddress(){var t;return __awaiter(this,void 0,void 0,Sa.mark((function _callee9(){return Sa.wrap((function _callee9$(a){for(;;)switch(a.prev=a.next){case 0:if(!(null===(t=this.enterParams)||void 0===t?void 0:t.linkProvider)){a.next=12;break}return a.prev=1,a.next=4,this.enterParams.linkProvider(this.account,this.roomId);case 4:this.linkAddressArray=a.sent,this.currLinkIdx=-1,n_(this.linkAddressArray),a.next=12;break;case 9:throw a.prev=9,a.t0=a.catch(1),new vl({code:Ud.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"linkProvider error: "+a.t0}});case 12:case"end":return a.stop()}}),_callee9,this,[[1,9]])})))},a.updateDynamicParamters=function updateDynamicParamters(t){var a,u,_,h;return __awaiter(this,void 0,void 0,Sa.mark((function _callee10(){return Sa.wrap((function _callee10$(E){for(;;)switch(E.prev=E.next){case 0:if(this.enterParams){E.next=2;break}return E.abrupt("return");case 2:if(!t){E.next=5;break}return E.next=5,this.updateLinkAddress();case 5:if(0===this.authType||!(null===(u=null===(a=this.enterParams)||void 0===a?void 0:a.loginOption)||void 0===u?void 0:u.tokenProvider)){E.next=21;break}return E.prev=6,E.next=9,this.enterParams.loginOption.tokenProvider(this.appkey,this.roomId,this.account);case 9:this.token=E.sent,E.next=15;break;case 12:throw E.prev=12,E.t0=E.catch(6),new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"tokenProvider error: "+E.t0}});case 15:if(null!==this.token&&void 0!==this.token){E.next=19;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_CALLBACK_FAILED,detail:{reason:"tokenProvider should not return null or undefined when authType === 1 or authType === 2"}});case 19:if(this.token||1!==this.authType){E.next=21;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_CALLBACK_FAILED,detail:{reason:"tokenProvider should not return empty string when authType === 1"}});case 21:if(!(null===(h=null===(_=this.enterParams)||void 0===_?void 0:_.loginOption)||void 0===h?void 0:h.loginExtensionProvider)){E.next=33;break}return E.prev=22,E.next=25,this.enterParams.loginOption.loginExtensionProvider(this.appkey,this.roomId,this.account);case 25:this.loginExt=E.sent,E.next=31;break;case 28:throw E.prev=28,E.t1=E.catch(22),new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"loginExtensionProvider error: "+E.t1}});case 31:if(null!==this.loginExt&&void 0!==this.loginExt||2!==this.authType){E.next=33;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_CALLBACK_FAILED,detail:{reason:"loginExtensionProvider should not return null or undefined when authType === 2"}});case 33:case"end":return E.stop()}}),_callee10,this,[[6,12],[22,28]])})))},a.setLoginParams=function setLoginParams(t,a,u){var _,h;return __awaiter(this,void 0,void 0,Sa.mark((function _callee11(){return Sa.wrap((function _callee11$(E){for(;;)switch(E.prev=E.next){case 0:if(this.reset(),this.roomId=a,this.enterParams=u,this.core.options.appkey=t,this.core.options.tags=(null===(_=u.tagConfig)||void 0===_?void 0:_.tags)||[],u.token&&(this.token=u.token),u.anonymousMode&&!u.accountId?this.core.options.account="nimanon_"+$u():this.core.options.account=u.accountId,this.isAnonymous=void 0!==u.anonymousMode&&u.anonymousMode,!this.isAnonymous||u.roomNick){E.next=14;break}if(void 0!==u.roomNick){E.next=13;break}u.roomNick=this.core.options.account,E.next=14;break;case 13:throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"roomNick is required when anonymousMode is true"}});case 14:return this.authType=(null===(h=u.loginOption)||void 0===h?void 0:h.authType)||0,E.next=17,this.updateDynamicParamters(!0);case 17:case"end":return E.stop()}}),_callee11,this)})))},a.v2LoginHandler=function v2LoginHandler(t){if(t.error)throw this.clientSocket.doDisconnect(Xu.ACTIVE,t.error),t.error;return t},a.v2LoginClientChangeHandler=function v2LoginClientChangeHandler(t){this.authenticator.changeLoginClient(Ql(t.content.state),t.content.datas)},a.nimLoginClientChangeHandler=function nimLoginClientChangeHandler(t){this.authenticator.changeLoginClient(Ql(t.content.state),t.content.datas)},a.v2ChatroomBeKickedHandler=function v2ChatroomBeKickedHandler(t){var a=t.content,u=a.kickedReason,_=a.serverExtension;this.clientSocket.doDisconnect(Xu.KICKED,u),this.core._clearModuleData(),this.lifeCycle.processEvent("kicked",new vl({code:Ud.V2NIM_ERROR_CODE_DISCONNECT,detail:{reason:"disconnect due to kicked"}}),u),this.core.emit("onChatroomKicked",{kickedReason:u,serverExtension:_})},ze(V2NIMChatroomLoginServiceImpl,[{key:"clientSocket",get:function get(){return this.core.clientSocket}},{key:"account",get:function get(){return this.core.options.account}},{key:"appkey",get:function get(){return this.core.options.appkey}},{key:"deviceId",get:function get(){return this.core.config.deviceId}},{key:"clientSession",get:function get(){return this.core.config.clientSession}}]),V2NIMChatroomLoginServiceImpl}(o_),a_={user_id:"",trace_id:"",action:7,exception_service:6,duration:0,start_time:0,state:1,extension:[]},s_=function(){function ReporterHookLinkKeep(t,a){this.traceData=a_,this.core=t,this.traceData=ba({},a_,a),this.traceData.extension=[]}var t=ReporterHookLinkKeep.prototype;return t.reset=function reset(){this.traceData=ba({},a_),this.traceData.extension=[]},t.start=function start(){var t,a;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(a=null===(t=this.core.clientSocket)||void 0===t?void 0:t.socket)||void 0===a?void 0:a.sessionId)||"",this.traceData.start_time=(new Date).getTime()},t.update=function update(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var a,u,_;return Sa.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,Es.net.getNetworkStatus();case 2:a=h.sent,u=a.net_type,_=a.net_connect,this.traceData.extension.push(ba({code:0,foreground:!0,foreg_backg_switch:!1,net_type:u,net_connect:_},t));case 6:case"end":return h.stop()}}),_callee,this)})))},t.end=function end(t){var a=this.traceData.extension[0],u=this.traceData.extension[1];if(a&&0===a.operation_type&&u&&1===u.operation_type){var _=a.net_type!==u.net_type||a.net_connect!==u.net_connect;if(t||!_)return this.traceData.duration=(new Date).getTime()-this.traceData.start_time,this.core.reporter.report("exceptions",this.traceData),void this.reset();this.reset()}else this.reset()},ReporterHookLinkKeep}(),c_="V2NIMChatroomInfoService",l_={"36_13":"v2GetChatroomInfo","36_14":"v2UpdateChatroomInfo","36_30":"v2SetTempChatBannedByTag","13_33":"v2UpdateChatroomLocation","36_34":"v2UpdateChatroomTags"},u_={tags:{id:1,converter:function converter(t){return Ms(t)}},notifyTargetTags:2,notificationEnabled:{id:3,converter:function converter(t){return+t}},notificationExtension:4},d_={v2GetChatroomInfo:{sid:36,cid:13,service:c_,response:[{type:"Property",name:"data",reflectMapper:invertSerializeItem(Dd)}]},v2UpdateChatroomInfo:{sid:36,cid:14,service:c_,params:[{type:"Property",name:"chatroom",reflectMapper:Dd},{type:"Bool",name:"notificationEnabled"},{type:"String",name:"notificationExtension"}]},v2SetTempChatBannedByTag:{sid:36,cid:30,service:c_,params:[{type:"Property",name:"tag",reflectMapper:{targetTag:1,duration:2,notificationEnabled:{id:3,converter:function converter(t){return+t}},notificationExtension:4,notifyTargetTags:5}}]},v2UpdateChatroomLocation:{sid:13,cid:33,service:c_,params:[{type:"Property",name:"tag",reflectMapper:{x:1,y:2,z:3,distance:4}}]},v2UpdateChatroomTags:{sid:36,cid:34,service:c_,params:[{type:"Property",name:"tag",reflectMapper:u_}]}},__=function(){function Service(t,a){this.name=t,this.core=a,this.name=t,this.logger=a.logger,this.core=a}return Service.prototype.process=function process(t){var a=this[t.cmd+"Handler"];if("function"==typeof a)return a.call(this,t);var u=get(t,"error.detail.ignore");return t.error&&!u?_a.reject(t.error):_a.resolve(t)},Service}(),p_=function(){function V2NIMChatroomInfoModel(){this.chatroomInfo=null}return V2NIMChatroomInfoModel.prototype.reset=function reset(){this.chatroomInfo=null},V2NIMChatroomInfoModel}();function pick(t,a){t=t||{};var u={};return forEach$1(a=a||[]).call(a,(function(a){void 0!==t[a]&&(u[a]=t[a])})),u}var h_,f_,E_=function(t){function V2NIMChatroomInfoServiceImpl(a){var u;return u=t.call(this,"V2NIMChatroomInfoService",a)||this,registerParser({cmdMap:l_,cmdConfig:d_}),u.model=new p_,u.setListener(),u}At(V2NIMChatroomInfoServiceImpl,t);var a=V2NIMChatroomInfoServiceImpl.prototype;return a.setListener=function setListener(){var t=this;this.core.eventBus.on("V2NIMChatroomMessageService/onReceiveNotification",(function(a,u){return __awaiter(t,void 0,void 0,Sa.mark((function _callee(){var t,_,h,E,m,g;return Sa.wrap((function _callee$(I){for(;;)switch(I.prev=I.next){case 0:I.t0=a.attachment.type,I.next=6===I.t0?3:13;break;case 3:return h=null===(t=u.data.roomInfo)||void 0===t?void 0:t.extension,E=null===(_=u.data.roomInfo)||void 0===_?void 0:_.queueLevel,delete(m=ba(ba(ba({},u.data.roomInfo||{}),void 0!==h?{serverExtension:h}:{}),void 0!==E?{queueLevelMode:E}:{})).extension,delete m.queueLevel,I.next=10,this._updateChatroomInfo(m);case 10:return g=I.sent,this.core.V2NIMChatroomService.emit("onChatroomInfoUpdated",g),I.abrupt("break",13);case 13:case"end":return I.stop()}}),_callee,this)})))}))},a.reset=function reset(){this.model.reset()},a.getChatroomInfo=function getChatroomInfo(){return this.model.chatroomInfo},a.updateChatroomInfo=function updateChatroomInfo(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var u;return Sa.wrap((function _callee2$(_){for(;;)switch(_.prev=_.next){case 0:if(validate(Jd,{updateParams:t,antispamConfig:a},"",!0),t.announcement||t.liveUrl||t.roomName||t.serverExtension){_.next=3;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"updateParams.announcement, updateParams.liveUrl, updateParams.roomName, updateParams.serverExtension 至少有一个不为空"}});case 3:return u=void 0===t.notificationEnabled||t.notificationEnabled,_.next=6,this.core.sendCmd("v2UpdateChatroomInfo",{chatroom:t,notificationEnabled:u,notificationExtension:t.notificationExtension||""});case 6:this._updateChatroomInfo(pick(t,["announcement","liveUrl","roomName","serverExtension"]));case 7:case"end":return _.stop()}}),_callee2,this)})))},a.updateChatroomLocationInfo=function updateChatroomLocationInfo(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){return Sa.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:return validate(Zd,t,"",!0),a.next=3,this.core.sendCmd("v2UpdateChatroomLocation",{tag:ba(ba({},t.locationInfo),{distance:t.distance})});case 3:case"end":return a.stop()}}),_callee3,this)})))},a.updateChatroomTags=function updateChatroomTags(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){return Sa.wrap((function _callee4$(a){for(;;)switch(a.prev=a.next){case 0:if(validate(e_,{updateParams:t},"",!0),t.tags||void 0!==t.notifyTargetTags){a.next=3;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"updateParams.tags, updateParams.notifyTargetTags 至少有一个不为空"}});case 3:return t.notificationEnabled=void 0===t.notificationEnabled||t.notificationEnabled,a.next=6,this.core.sendCmd("v2UpdateChatroomTags",{tag:t});case 6:case"end":return a.stop()}}),_callee4,this)})))},a.setTempChatBannedByTag=function setTempChatBannedByTag(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){return Sa.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:return validate(t_,t,"",!0),t.notificationEnabled=void 0===t.notificationEnabled||t.notificationEnabled,a.next=4,this.core.sendCmd("v2SetTempChatBannedByTag",{tag:t});case 4:case"end":return a.stop()}}),_callee5,this)})))},a._updateChatroomInfo=function _updateChatroomInfo(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee6(){return Sa.wrap((function _callee6$(a){for(;;)switch(a.prev=a.next){case 0:if(!this.model.chatroomInfo){a.next=4;break}ba(this.model.chatroomInfo,t),a.next=6;break;case 4:return a.next=6,this._getChatroomInfoAsync();case 6:return a.abrupt("return",this.model.chatroomInfo);case 7:case"end":return a.stop()}}),_callee6,this)})))},a._getChatroomInfoAsync=function _getChatroomInfoAsync(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee7(){var t;return Sa.wrap((function _callee7$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("v2GetChatroomInfo");case 2:t=a.sent,this._setChatroomInfo(t.content.data);case 4:case"end":return a.stop()}}),_callee7,this)})))},a._setChatroomInfo=function _setChatroomInfo(t){this.model.chatroomInfo=t},V2NIMChatroomInfoServiceImpl}(__),m_={file:{md5:"$(Etag)",size:"$(ObjectSize)"},image:{md5:"$(Etag)",size:"$(ObjectSize)",w:"$(ImageInfo.Width)",h:"$(ImageInfo.Height)",orientation:"$(ImageInfo.Orientation)"},audio:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Audio.Duration)"},video:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Video.Duration)",w:"$(AVinfo.Video.Width)",h:"$(AVinfo.Video.Height)"}},g_={accessKeyId:"",secretAccessKey:"",sessionToken:"",region:"",maxRetries:0,bucket:"",objectName:"",token:"",shortUrl:""};function getUploadResponseFormat(t){return void 0===t&&(t="file"),Ms(m_[t]||{}).replace(/"/gi,'\\"')}!function(t){t[t.nos=1]="nos",t[t.s3=2]="s3"}(h_||(h_={})),function(t){t[t.dontNeed=-1]="dontNeed",t[t.time=2]="time",t[t.urls=3]="urls"}(f_||(f_={}));var I_={chunkUploadHost:"https://wannos-web.127.net",chunkUploadHostBackupList:["https://fileup.chatnos.com","https://oss.chatnos.com"],commonUploadHost:"https://fileup.chatnos.com",commonUploadHostBackupList:["https://oss.chatnos.com"],chunkMaxSize:4194304e4,commonMaxSize:104857600,uploadReplaceFormat:"https://{host}/{object}",cdn:{defaultCdnDomain:"nim-nosdn.netease.im",cdnDomain:"",bucket:"",objectNamePrefix:""},downloadUrl:"https://{bucket}-nosdn.netease.im/{object}",downloadHostList:["nos.netease.com"],nosCdnEnable:!0,isNeedToGetUploadPolicyFromServer:!0},v_=function(){function NOS(t,a){this.nosCdnHostTimer=0,this.nosErrorCount=0,this.core=t,this.cloudStorage=a}var t=NOS.prototype;return t.reset=function reset(){this.nosErrorCount=0},t.getNosAccessToken=function getNosAccessToken(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var a,u,_;return Sa.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("getNosAccessToken",{tag:t});case 2:return a=h.sent,u=get(a,"content.nosAccessTokenTag.token"),_=t.url,h.abrupt("return",{token:u,url:-1!==indexOf(_).call(_,"?")?_+"&token="+u:_+"?token="+u});case 6:case"end":return h.stop()}}),_callee,this)})))},t.deleteNosAccessToken=function deleteNosAccessToken(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){return Sa.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("deleteNosAccessToken",{tag:t});case 2:case"end":return a.stop()}}),_callee2,this)})))},t.nosUpload=function nosUpload(t,a){var u,_,h,E,m,g,I,T;return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var N,M,O,S,R,C,A,b,k,L,D,V,w,P,x,U,B,G,H;return Sa.wrap((function _callee3$(Y){for(;;)switch(Y.prev=Y.next){case 0:if(N=get(this.core,"config.cdn.bucket"),M={tag:t.nosScenes||N||"nim"},t.nosSurvivalTime&&(M.expireSec=t.nosSurvivalTime),O=this.core.adapters.getFileUploadInformation(t),!(!a&&!O)){Y.next=18;break}return Y.prev=6,Y.next=9,this.core.sendCmd("getNosToken",{responseBody:getUploadResponseFormat(t.type),nosToken:M});case 9:S=Y.sent,Y.next=18;break;case 12:if(Y.prev=12,Y.t0=Y.catch(6),this.core.logger.error("uploadFile:: getNosToken error",Y.t0),!(Y.t0 instanceof vl)){Y.next=17;break}throw Y.t0;case 17:throw new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getNosToken error",rawError:Y.t0,curProvider:1}});case 18:return R=this.config.uploadReplaceFormat.replace("{host}",this.config.cdn.cdnDomain||this.config.cdn.defaultCdnDomain).replace("{object}",O?null===(u=O.uploadInfo)||void 0===u?void 0:u.objectName:a?null==a?void 0:a.objectName:S.content.nosToken.objectName),C="",a&&a.shortUrl&&(C=a.shortUrl),(null===(E=null===(h=null===(_=null==O?void 0:O.uploadInfo)||void 0===_?void 0:_.payload)||void 0===h?void 0:h.mixStoreToken)||void 0===E?void 0:E.shortUrl)&&(C=O.uploadInfo.payload.mixStoreToken.shortUrl),A=C||R,Y.prev=23,k=O?{token:null===(m=null==O?void 0:O.uploadInfo)||void 0===m?void 0:m.token,bucket:null===(g=null==O?void 0:O.uploadInfo)||void 0===g?void 0:g.bucketName,objectName:null===(I=null==O?void 0:O.uploadInfo)||void 0===I?void 0:I.objectName}:a||S.content.nosToken,this.core.logger.log("uploadFile:: uploadFile params",{nosToken:k,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,platform:Es.platform}),L="BROWSER"===Es.platform?this.config.chunkUploadHost:this.config.commonUploadHost+"/"+(k&&k.bucket),this.core.reporterHookCloudStorage.update({remote_addr:L,operation_type:a?2:0}),Y.next=30,this.core.adapters.uploadFile(ba(ba(ba({},t),{nosToken:k,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,maxSize:t.maxSize||this.config.chunkMaxSize}),a?{payload:{mixStoreToken:a}}:{}));case 30:b=Y.sent,Y.next=65;break;case 33:if(Y.prev=33,Y.t1=Y.catch(23),this.core.logger.error("uploadFile::nos uploadFile error:",Y.t1),D="v2"===get(this.core,"options.apiVersion"),Y.t1.code!==gl.V2NIM_ERROR_CODE_CANCELLED&&10499!==Y.t1.errCode){Y.next=39;break}throw new Ml({code:D?gl.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:get(Y.t1,"message")||"Request abort",rawError:Y.t1,curProvider:1}});case 39:if(!D||Y.t1.errCode!==gl.V2NIM_ERROR_CODE_FILE_OPEN_FAILED){Y.next=41;break}throw new vl({code:gl.V2NIM_ERROR_CODE_FILE_OPEN_FAILED,detail:{reason:get(Y.t1,"message")||"Read file failed",rawError:Y.t1,curProvider:1}});case 41:return Y.next=43,Es.net.getNetworkStatus();case 43:if(V=Y.sent,!1!==V.net_connect){Y.next=47;break}throw new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:Y.t1,curProvider:1}});case 47:if(!a){Y.next=64;break}if(!(this.nosErrorCount<=0)){Y.next=60;break}Y.prev=49,this.cloudStorage.mixStorage._addCircuitTimer(),Y.next=56;break;case 53:throw Y.prev=53,Y.t2=Y.catch(49),new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:Y.t2,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.cloudStorage.mixStorage.mixStorePolicy,file:t.file||t.filePath}});case 56:return this.nosErrorCount=get(this.cloudStorage,"mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry"),Y.abrupt("return",this.cloudStorage._uploadFile(t));case 60:return this.nosErrorCount--,Y.abrupt("return",this.nosUpload(t,a));case 62:Y.next=65;break;case 64:throw new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"NOS attempts failed",rawError:Y.t1,curProvider:1}});case 65:if(w=null==b?void 0:b.type,(P=w&&indexOf(w).call(w,"/")>-1?slice(w).call(w,0,indexOf(w).call(w,"/")):"")||(P=t.type||""),(x={image:"imageInfo",video:"vinfo",audio:"vinfo"})[P]){Y.next=71;break}return Y.abrupt("return",ba({url:A},b));case 71:return Y.prev=71,Y.next=74,this.core.adapters.request(R+"?"+x[P],{method:"GET",dataType:"json",timeout:5e3},{exception_service:3});case 74:U=Y.sent,Y.next=81;break;case 77:return Y.prev=77,Y.t3=Y.catch(71),this.core.logger.error("uploadFile:: fetch file info error",Y.t3),Y.abrupt("return",ba({url:A},b));case 81:if(!U){Y.next=88;break}return B=U.data,G="imageInfo"===x[P]?B:null===(T=null==B?void 0:B.GetVideoInfo)||void 0===T?void 0:T.VideoInfo,H={url:A,name:b.name,size:b.size,ext:b.ext,w:null==G?void 0:G.Width,h:null==G?void 0:G.Height,orientation:null==G?void 0:G.Orientation,dur:null==G?void 0:G.Duration,audioCodec:null==G?void 0:G.AudioCodec,videoCodec:null==G?void 0:G.VideoCodec,container:null==G?void 0:G.Container},Y.abrupt("return",pickBy(H,(function(t){return void 0!==t})));case 88:return Y.abrupt("return",ba({url:A},b));case 89:case"end":return Y.stop()}}),_callee3,this,[[6,12],[23,33],[49,53],[71,77]])})))},t._getNosCdnHost=function _getNosCdnHost(){var t;return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){var a,u,_,h=this;return Sa.wrap((function _callee4$(E){for(;;)switch(E.prev=E.next){case 0:return E.prev=0,E.next=3,this.core.sendCmd("getNosCdnHost");case 3:a=E.sent,E.next=10;break;case 6:return E.prev=6,E.t0=E.catch(0),this.core.logger.error("getNosCdnHost::error",E.t0),E.abrupt("return");case 10:if(a){E.next=12;break}return E.abrupt("return");case 12:u=null===(t=null==a?void 0:a.content)||void 0===t?void 0:t.nosConfigTag,0!==(_=Ql(null==u?void 0:u.expire))&&u.cdnDomain?-1===_?(this.config.cdn.bucket=u.bucket,this.config.cdn.cdnDomain=u.cdnDomain,this.config.cdn.objectNamePrefix=u.objectNamePrefix):(this.config.cdn.bucket=u.bucket,this.config.cdn.cdnDomain=u.cdnDomain,this.config.cdn.objectNamePrefix=u.objectNamePrefix,this.nosCdnHostTimer=this.core.timerManager.addTimer((function(){h._getNosCdnHost()}),1e3*_)):(this.config.cdn.bucket="",this.config.cdn.cdnDomain="",this.config.cdn.objectNamePrefix="");case 15:case"end":return E.stop()}}),_callee4,this,[[0,6]])})))},ze(NOS,[{key:"config",get:function get(){return this.cloudStorage.config}}]),NOS}();function invert(t){t=t||{};var a={};for(var u in t)a[t[u]]=u;return a}var T_={"6_2":"getNosToken","6_22":"getOriginUrl","6_24":"getNosAccessToken","6_25":"deleteNosAccessToken","6_26":"getNosCdnHost","6_27":"getGrayscaleConfig","6_28":"getMixStorePolicy","6_29":"getMixStoreToken","6_30":"getFileAuthToken"},N_={nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},mixStoreTokenReqTag:{provider:0,tokenCount:1,nosSurvivalTime:2,tag:3,returnBody:4,policyVersion:5},nosConfigTag:{bucket:1,cdnDomain:2,expire:3,objectNamePrefix:4},grayConfigTag:{config:0,ttl:1},mixStorePolicyTag:{providers:0,ttl:1,mixEnable:2,nosPolicy:3,s3Policy:4,policyVersion:5},mixStoreTokenResTag:{provider:0,accessKeyId:1,secretAccessKey:2,sessionToken:3,token:4,expireTime:5,bucket:6,objectName:7,fileExpireSec:8,tag:9,shortUrl:10,region:11},nosSafeUrlTag:{safeUrl:0,originUrl:1},mixStoreAuthTokenReqTag:{type:1,urls:2},mixStoreAuthTokenResTag:{type:1,tokens:2,token:3,ttl:4},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3}},M_={getNosToken:{sid:6,cid:2,service:"cloudStorage",params:[{type:"String",name:"responseBody"},{type:"Property",name:"nosToken",entity:"nosToken",reflectMapper:N_.nosToken}],response:[{type:"Property",name:"nosToken",reflectMapper:invert(N_.nosToken)}]},getOriginUrl:{sid:6,cid:22,service:"cloudStorage",params:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:N_.nosSafeUrlTag}],response:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:invert(N_.nosSafeUrlTag)}]},getNosCdnHost:{sid:6,cid:26,service:"cloudStorage",response:[{type:"Property",name:"nosConfigTag",reflectMapper:invert(N_.nosConfigTag)}]},getGrayscaleConfig:{sid:6,cid:27,service:"cloudStorage",params:[{type:"Property",name:"config"}],response:[{type:"Property",name:"grayConfigTag",reflectMapper:invert(N_.grayConfigTag)}]},getMixStorePolicy:{sid:6,cid:28,service:"cloudStorage",params:[{type:"LongArray",name:"supportType"}],response:[{type:"Property",name:"mixStorePolicyTag",reflectMapper:invert(N_.mixStorePolicyTag)}]},getMixStoreToken:{sid:6,cid:29,service:"cloudStorage",params:[{type:"Property",name:"mixStoreTokenReqTag",reflectMapper:N_.mixStoreTokenReqTag}],response:[{type:"Property",name:"mixStoreTokenResTag",reflectMapper:invert(N_.mixStoreTokenResTag)}]},getFileAuthToken:{sid:6,cid:30,service:"cloudStorage",params:[{type:"Property",name:"mixStoreAuthTokenReqTag",reflectMapper:N_.mixStoreAuthTokenReqTag}],response:[{type:"Property",name:"mixStoreAuthTokenResTag",reflectMapper:invert(N_.mixStoreAuthTokenResTag)}]},getNosAccessToken:{sid:6,cid:24,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:N_.nosAccessTokenTag}],response:[{type:"Property",name:"tag",reflectMapper:invert(N_.nosAccessTokenTag)}]},deleteNosAccessToken:{sid:6,cid:25,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:N_.nosAccessTokenTag}]}},O_=function(){function MixStorage(t,a){this.GRAYKEY="AllGrayscaleConfig",this.MIXSTOREKEY="AllMixStorePolicy",this.grayConfig={mixStoreEnable:!1,timeStamp:0,ttl:0},this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10,this.circuitTimer=0,this.core=t,this.cloudStorage=a,this.logger=t.logger}var t=MixStorage.prototype;return t.reset=function reset(){this.grayConfig=null,this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10},t.getGrayscaleConfig=function getGrayscaleConfig(t,a){var u;return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var _,h;return Sa.wrap((function _callee$(E){for(;;)switch(E.prev=E.next){case 0:if(Es.localStorage)try{Es.localStorage.getItem&&Es.localStorage.getItem(this.GRAYKEY)&&(this.grayConfig=JSON.parse(Es.localStorage.getItem(this.GRAYKEY))[t])}catch(t){Es.localStorage.getItem(this.GRAYKEY)&&this.core.logger.error("uploadFile:: JSON.parse grayscaleConfig error ",t)}if(this.grayConfig&&!(this.grayConfig.timeStamp+1e3*this.grayConfig.ttl<a)){E.next=17;break}return E.next=4,this.core.sendCmd("getGrayscaleConfig",{config:{}});case 4:if(!(_=E.sent).content||!_.content.grayConfigTag){E.next=16;break}this.logger.log("uploadFile::getAppGrayConfigRequest success ");try{this.grayConfig=JSON.parse(_.content.grayConfigTag.config),this.grayConfig.ttl=JSON.parse(_.content.grayConfigTag.ttl)}catch(t){this.logger.error("getGrayscaleConfig error",t)}if(this.grayConfig){E.next=10;break}return E.abrupt("return");case 10:h=Es.localStorage.getItem(this.GRAYKEY)?JSON.parse(Es.localStorage.getItem(this.GRAYKEY)):{},this.grayConfig.timeStamp=(new Date).getTime(),h[t]=this.grayConfig,Es.localStorage.setItem(this.GRAYKEY,Ms(h)),E.next=17;break;case 16:this.logger.log("uploadFile:: result grayConfig:",_.content);case 17:if(!(null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable)){E.next=20;break}return E.next=20,this._getMixStorePolicy(t);case 20:case"end":return E.stop()}}),_callee,this)})))},t._getMixStorePolicy=function _getMixStorePolicy(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var a,u,_,h,E,m,g;return Sa.wrap((function _callee2$(I){for(;;)switch(I.prev=I.next){case 0:if(a=(new Date).getTime(),Es.localStorage)try{this.mixStorePolicy=JSON.parse(Es.localStorage.getItem(this.MIXSTOREKEY))[t],this.curProvider=Ql(this.mixStorePolicy.providers[0]),this.mixStorePolicy.timeStamp&&this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl>a&&(_=this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl-a,this.core.timerManager.addTimer(bind$1(u=this._getMixStorePolicy).call(u,this,t),_))}catch(a){Es.localStorage.getItem(this.MIXSTOREKEY)&&JSON.parse(Es.localStorage.getItem(this.MIXSTOREKEY))[t]&&this.core.logger.error("uploadFile:: JSON.parse mixStorePolicy error ",a)}if(this.mixStorePolicy&&!(this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl<=a)){I.next=31;break}return I.prev=3,I.next=6,this.core.sendCmd("getMixStorePolicy",{supportType:this.cloudStorage.aws.s3?[1,2]:[1]});case 6:E=I.sent,m=E.content.mixStorePolicyTag,this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.mixStorePolicy.policyVersion=m.policyVersion,this.mixStorePolicy.ttl=Number(m.ttl),this.mixStorePolicy.providers=m.providers.split(","),this.circuitTimer&&this.core.timerManager.deleteTimer(this.circuitTimer),this.curProvider=Ql(this.mixStorePolicy.providers[0]),this.mixStorePolicy.nosPolicy=m.nosPolicy?JSON.parse(m.nosPolicy):null,this.mixStorePolicy.s3Policy=m.s3Policy?JSON.parse(m.s3Policy):null,null===this.mixStorePolicy.s3Policy?this.mixStorePolicy.providers=["1"]:null===this.mixStorePolicy.nosPolicy?this.mixStorePolicy.providers=["2"]:this.mixStorePolicy.providers=this.mixStorePolicy.s3Policy.priority<this.mixStorePolicy.nosPolicy.priority?["2","1"]:["1","2"],this.core.timerManager.addTimer(bind$1(h=this._getMixStorePolicy).call(h,this,t),1e3*this.mixStorePolicy.ttl),g=Es.localStorage.getItem(this.MIXSTOREKEY)?JSON.parse(Es.localStorage.getItem(this.MIXSTOREKEY)):{},this.mixStorePolicy.timeStamp=(new Date).getTime(),g[t]=this.mixStorePolicy,Es.localStorage.setItem(this.MIXSTOREKEY,Ms(g)),I.next=31;break;case 24:if(I.prev=24,I.t0=I.catch(3),this.logger.error("getMixStorePolicy error",I.t0),0!==this.mixStoreErrorCount){I.next=29;break}throw new Error("getMixStorePolicy all count error");case 29:this._getMixStorePolicy(t),this.mixStoreErrorCount--;case 31:this.mixStorePolicy.nosPolicy&&(this.cloudStorage.nos.nosErrorCount=this.mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry);case 32:case"end":return I.stop()}}),_callee2,this,[[3,24]])})))},t._addCircuitTimer=function _addCircuitTimer(){var t=this,a=this.mixStorePolicy.providers,u=a[(indexOf(a).call(a,String(this.curProvider))+1)%a.length];if(!u)throw new Error("uploadFile nextProvider error");if(u===a[0])throw new Error("uploadFile all policy fail");if(this.logger.log("uploadFile:: upload policy will change,now policy:"+this.curProvider+" nextProvider:"+u),this.curProvider=Ql(u),this.mixStorePolicy.nosPolicy&&this.mixStorePolicy.s3Policy){var _=this.mixStorePolicy[1===this.curProvider?"nosPolicy":"s3Policy"].uploadConfig.retryPolicy.circuit;if(!_||0===_)throw new Error("uploadFile circuit error");this.circuitTimer=this.core.timerManager.addTimer((function(){t.logger.log("uploadFile:: upload policy will change,now policy:"+t.curProvider+" nextProvider:"+Ql(t.mixStorePolicy.providers[0])),t.curProvider=Ql(t.mixStorePolicy.providers[0]),t.core.timerManager.deleteTimer(t.circuitTimer)}),1e3*_)}throw new Error("uploadFile will not retry again")},t.getFileAuthToken=function getFileAuthToken(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var a;return Sa.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,this.core.sendCmd("getFileAuthToken",{mixStoreAuthTokenReqTag:t});case 2:return a=u.sent,u.abrupt("return",a.content.mixStoreAuthTokenResTag);case 4:case"end":return u.stop()}}),_callee3,this)})))},MixStorage}(),S_=Yl.trim,R_=R("".charAt),y_=h.parseFloat,C_=h.Symbol,A_=C_&&C_.iterator,b_=1/y_(Ul+"-0")!=-1/0||A_&&!fails((function(){y_(Object(A_))}))?function parseFloat(t){var a=S_(toString(t)),u=y_(a);return 0===u&&"-"==R_(a,0)?-0:u}:y_;_export({global:!0,forced:parseFloat!=b_},{parseFloat:b_});var k_=Y.parseFloat,L_=-1,D_=function(){function AWS(t,a){this.s3=null,this.core=t,this.cloudStorage=a,this.logger=t.logger}var t=AWS.prototype;return t.s3Upload=function s3Upload(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var u,_,h,E,m,g,I,T,N,M,O,S,R,C,A=this;return Sa.wrap((function _callee2$(b){for(;;)switch(b.prev=b.next){case 0:if(L_+=1,!t.file){b.next=5;break}u=t.file,b.next=20;break;case 5:if("string"!=typeof t.fileInput){b.next=15;break}if(this.logger.warn("fileInput will abandon,Please use file or filepath"),!((_=document.getElementById(t.fileInput))&&_.files&&_.files[0])){b.next=12;break}u=_.files[0],b.next=13;break;case 12:throw new Error("Can not get file from fileInput");case 13:b.next=20;break;case 15:if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0])){b.next=19;break}u=t.fileInput.files[0],b.next=20;break;case 19:throw new Error("Can not get file from fileInput "+t.fileInput);case 20:if(this.mixStorePolicy.s3Policy){b.next=22;break}throw new Error("dont get s3 policy");case 22:return h={accessKeyId:a.accessKeyId,secretAccessKey:a.secretAccessKey,sessionToken:a.sessionToken,region:a.region,maxRetries:this.mixStorePolicy.s3Policy.uploadConfig.retryPolicy.retry},E=this.s3,m=decodeURIComponent(a.bucket),g=decodeURIComponent(a.objectName),I=u,T="https://"+m+".s3.amazonaws.com/"+g,N={},(M=this.mixStorePolicy.s3Policy)&&M.uploadConfig&&fs(M.uploadConfig.uploadUrl)&&M.uploadConfig.uploadUrl.length>0&&(O=M.uploadConfig.uploadUrl.length,L_%=O,N.endpoint=M.uploadConfig.uploadUrl[L_],N.s3ForcePathStyle=!0,T=N.endpoint+"/"+m+"/"+g),this.core.reporterHookCloudStorage.update({remote_addr:T,operation_type:1}),(S=new E(N)).config.update(h),R={Bucket:m,Key:g,Body:I,Metadata:{token:a.token},ContentType:I.type||"application/octet-stream"},this.core.logger.log("uploadFile:: s3 upload params:",R),(C=S.upload(R)).on("httpUploadProgress",(function(a){var u=k_((a.loaded/a.total).toFixed(2));t.onUploadProgress&&t.onUploadProgress({total:a.total,loaded:a.loaded,percentage:u,percentageText:Math.round(100*u)+"%"})})),b.abrupt("return",new _a((function(u,_){var h=(new Date).getTime();C.send((function(E,T){return __awaiter(A,void 0,void 0,Sa.mark((function _callee(){var N,M,O,S,R,C,A,b;return Sa.wrap((function _callee$(k){for(;;)switch(k.prev=k.next){case 0:if(!E||"RequestAbortedError"!==E.code){k.next=5;break}this.logger.error("uploadFile:","api::s3:upload file abort.",E),_(new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:"S3RequestAbortedError",rawError:E,curProvider:2}})),k.next=41;break;case 5:if(!E){k.next=26;break}return this.logger.error("uploadFile:","api::s3:upload file failed.",E),this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(M=null===(N=this.core)||void 0===N?void 0:N.auth)||void 0===M?void 0:M.account),trace_id:null===(O=this.core.clientSocket.socket)||void 0===O?void 0:O.sessionId,start_time:h,action:1,exception_service:4}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof E.status?E.status:"number"==typeof E.code?E.code:0,description:E.message||""+E.code,operation_type:1,target:Ms({bucket:m,object:g})},{asyncParams:Es.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),k.next=12,Es.net.getNetworkStatus();case 12:if(S=k.sent,!1!==S.net_connect){k.next=16;break}return k.abrupt("return",_(new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:E,curProvider:this.cloudStorage.mixStorage.curProvider}})));case 16:k.prev=16,this.cloudStorage.mixStorage._addCircuitTimer(),k.next=23;break;case 20:return k.prev=20,k.t0=k.catch(16),k.abrupt("return",_(new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:k.t0,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.mixStorePolicy,file:t.file||t.filePath}})));case 23:u(this.cloudStorage._uploadFile(t)),k.next=41;break;case 26:if(R=(R=(R=this.mixStorePolicy.s3Policy.cdnSchema).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn)).replace("{objectName}",T.Key),C={size:I.size,name:I.name,url:a.shortUrl?a.shortUrl:R,ext:I.name.split(".")[1]||"unknown"},A=t.type||"",(b={image:"imageInfo"})[A]){k.next=36;break}return k.abrupt("return",u(C));case 36:return k.t1=u,k.next=39,this.getS3FileInfo({url:R,infoSuffix:b[A],s3Result:C});case 39:return k.t2=k.sent,k.abrupt("return",(0,k.t1)(k.t2));case 41:case"end":return k.stop()}}),_callee,this,[[16,20]])})))})),t.onUploadStart&&t.onUploadStart(C)})));case 39:case"end":return b.stop()}}),_callee2,this)})))},t.getS3FileInfo=function getS3FileInfo(t){var a;return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var u,_,h,E,m,g,I;return Sa.wrap((function _callee3$(T){for(;;)switch(T.prev=T.next){case 0:return u=t.url,_=t.infoSuffix,h=t.s3Result,T.prev=1,T.next=4,this.core.adapters.request(u+"?"+_,{method:"GET",dataType:"text",timeout:5e3},{exception_service:3});case 4:E=T.sent,T.next=11;break;case 7:return T.prev=7,T.t0=T.catch(1),this.core.logger.error("uploadFile:: fetch file info error",T.t0),T.abrupt("return",h);case 11:if(!E){T.next=18;break}return m=E.data,g="imageInfo"===_?m:null===(a=null==m?void 0:m.GetVideoInfo)||void 0===a?void 0:a.VideoInfo,I=ba(ba({},h),{w:null==g?void 0:g.Width,h:null==g?void 0:g.Height,orientation:null==g?void 0:g.Orientation,dur:null==g?void 0:g.Duration,audioCodec:null==g?void 0:g.AudioCodec,videoCodec:null==g?void 0:g.VideoCodec,container:null==g?void 0:g.Container}),T.abrupt("return",pickBy(I,(function(t){return void 0!==t})));case 18:return this.core.logger.error("uploadFile:: fetch s3 file info no result",u+"?"+_),T.abrupt("return",h);case 20:case"end":return T.stop()}}),_callee3,this,[[1,7]])})))},ze(AWS,[{key:"mixStorePolicy",get:function get(){return this.cloudStorage.mixStorage.mixStorePolicy}}]),AWS}(),V_=function(){function CloudStorageService(t,a){void 0===a&&(a={}),this.config={},this.uploadTaskMap={},this.name="cloudStorage",this.logger=t.logger,this.core=t,this.nos=new v_(t,this),this.mixStorage=new O_(t,this),this.aws=new D_(t,this),registerParser({cmdMap:T_,cmdConfig:M_}),this.setOptions(a),this.setListeners()}var t=CloudStorageService.prototype;return t.setOptions=function setOptions(t){void 0===t&&(t={});var a=t.storageKeyPrefix||"NIMClient";this.mixStorage.GRAYKEY=a+"-AllGrayscaleConfig",this.mixStorage.MIXSTOREKEY=a+"-AllMixStorePolicy";var u=t.s3,_=__rest(t,["s3"]),h=ba({},I_,this.config);if(_&&Object.prototype.hasOwnProperty.call(_,"cdn")){var E=ba(ba({},h.cdn),_.cdn);this.config=ba({},h,_),this.config.cdn=E}else this.config=ba({},h,_);u&&(this.aws.s3=u)},t.setListeners=function setListeners(){var t,a,u,_;this.core.eventBus.on("kicked",bind$1(t=this._clearUnCompleteTask).call(t,this)),this.core.eventBus.on("disconnect",bind$1(a=this._clearUnCompleteTask).call(a,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLogout",bind$1(u=this._clearUnCompleteTask).call(u,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleKicked",bind$1(_=this._clearUnCompleteTask).call(_,this))},t._clearUnCompleteTask=function _clearUnCompleteTask(){var t,a=this;forEach$1(t=Za(this.uploadTaskMap)).call(t,(function(t){var u=a.uploadTaskMap[t];u&&u.abort&&u.abort()})),this.uploadTaskMap={}},t.init=function init(t){return void 0===t&&(t=Ga()),__awaiter(this,void 0,void 0,Sa.mark((function _callee(){return Sa.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.mixStorage.reset(),this.nos.reset(),!this.config.isNeedToGetUploadPolicyFromServer){a.next=5;break}return a.next=5,this.mixStorage.getGrayscaleConfig(this.core.options.appkey,t);case 5:return a.next=7,this.nos._getNosCdnHost();case 7:case"end":return a.stop()}}),_callee,this)})))},t.processCallback=function processCallback(t,a){var u=this,_=t.onUploadProgress,h=t.onUploadDone,E=t.onUploadStart;return{onUploadStart:"function"==typeof E?function(t){u.uploadTaskMap[a]=t;try{E(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadStart execute error",t)}}:function(t){u.uploadTaskMap[a]=t},onUploadProgress:"function"==typeof _?function(t){u.core.reporterHookCloudStorage.update({transferred_size:t.loaded,full_size:t.total});try{_(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadProgress execute error",t)}}:function(t){u.core.reporterHookCloudStorage.update({transferred_size:t.loaded,full_size:t.total})},onUploadDone:"function"==typeof h?function(t){u.core.reporterHookCloudStorage.end(0);try{h(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadDone execute error",t)}}:function(){u.core.reporterHookCloudStorage.end(0)},taskKey:a}},t.uploadFile=function uploadFile(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var a,u,_,h,E,m,g,I;return Sa.wrap((function _callee2$(T){for(;;)switch(T.prev=T.next){case 0:if(validate({maxSize:{type:"number",required:!1},type:{type:"enum",values:["file","image","audio","video"]}},t),t.fileInput||t.file||t.filePath){T.next=3;break}throw new Error("uploadFile needs target file object or a filePath");case 3:if(!t.type||"file"===t.type){T.next=7;break}if(!(a=get(t,"file.type"))||"string"!=typeof a||-1!==indexOf(a).call(a,t.type)){T.next=7;break}throw new Error('The meta type "'+a+'" does not match "'+t.type+'"');case 7:return this.core.reporterHookCloudStorage.start(),t.file?this.core.reporterHookCloudStorage.update({full_size:t.file.size}):"string"==typeof t.fileInput?(u=document.getElementById(t.fileInput))&&u.files&&u.files[0]&&this.core.reporterHookCloudStorage.update({full_size:u.files[0].size}):t.fileInput&&t.fileInput.files&&t.fileInput.files[0]&&this.core.reporterHookCloudStorage.update({full_size:t.fileInput.files[0].size}),_=$u(),h=this.processCallback(t,_),E=h.onUploadStart,m=h.onUploadProgress,g=h.onUploadDone,t.onUploadStart=E,t.onUploadProgress=m,t.onUploadDone=g,I=null,T.prev=15,T.next=18,this._uploadFile(t);case 18:I=T.sent,t.md5&&(I.md5=t.md5),delete this.uploadTaskMap[_],T.next=28;break;case 23:throw T.prev=23,T.t0=T.catch(15),delete this.uploadTaskMap[_],this.core.reporterHookCloudStorage.end((T.t0&&T.t0.code)===gl.V2NIM_ERROR_CODE_CANCELLED?3:1),T.t0;case 28:return I&&(I.size=void 0===I.size?void 0:Number(I.size),I.w=void 0===I.w?void 0:Number(I.w),I.h=void 0===I.h?void 0:Number(I.h),I.dur=void 0===I.dur?void 0:Number(I.dur)),I.url=decodeURIComponent(I.url),t.onUploadDone({size:I.size,name:I.name,url:I.url,ext:I.name.split(".")[1]||"unknown"}),T.abrupt("return",I);case 32:case"end":return T.stop()}}),_callee2,this,[[15,23]])})))},t._uploadFile=function _uploadFile(t){var a,u;return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var _,h,E,m;return Sa.wrap((function _callee3$(g){for(;;)switch(g.prev=g.next){case 0:if(get(this.mixStorage,"grayConfig.mixStoreEnable")&&get(this.mixStorage,"mixStorePolicy.providers.length")){g.next=3;break}return this.logger.log("uploadFile:: uploadFile begin, use old nos"),g.abrupt("return",this.nos.nosUpload(t));case 3:if(this.logger.log("uploadFile::_uploadFile, grayConfig enable:"+get(this.mixStorage,"grayConfig.mixStoreEnable")+" curProvider:"+get(this.mixStorage,"curProvider")),_=this.core.adapters.getFileUploadInformation(t),h=!0,_?!1===_.complete&&2===this.mixStorage.curProvider&&(h=!1):h=!1,this.aws.s3||(this.mixStorage.curProvider=1),E=g_,h){g.next=23;break}return g.prev=10,g.next=13,this.core.sendCmd("getMixStoreToken",{mixStoreTokenReqTag:{provider:this.mixStorage.curProvider,tokenCount:1,tag:"qchat",nosSurvivalTime:t.nosSurvivalTime,returnBody:getUploadResponseFormat(t.type),policyVersion:this.mixStorage.mixStorePolicy.policyVersion}});case 13:m=g.sent,E=m.content.mixStoreTokenResTag,g.next=23;break;case 17:if(g.prev=17,g.t0=g.catch(10),this.core.logger.error("uploadFile:: getMixStoreToken error",g.t0),!(g.t0 instanceof vl)){g.next=22;break}throw g.t0;case 22:throw new Ml({code:"v2"===get(this.core,"options.apiVersion")?gl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getMixStoreToken error",rawError:g.t0,curProvider:this.mixStorage.curProvider,mixStorePolicy:this.mixStorage.mixStorePolicy}});case 23:if(h){g.next=27;break}return g.abrupt("return",2===this.mixStorage.curProvider?this.aws.s3Upload(t,E):this.nos.nosUpload(t,E));case 27:return g.abrupt("return",this.nos.nosUpload(t,null===(u=null===(a=null==_?void 0:_.uploadInfo)||void 0===a?void 0:a.payload)||void 0===u?void 0:u.mixStoreToken));case 28:case"end":return g.stop()}}),_callee3,this,[[10,17]])})))},t.getThumbUrl=function getThumbUrl(t,a){var u,_,h,E,m;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),t;var g=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);g[0],g[1],g[2],g[3],g[4];var I=g[5];if(g[6],g[7],null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable){var T=this._getUrlType(t);if(2===T&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.imagethumb"))return(null===(h=null===(_=this.mixStorePolicy.s3Policy)||void 0===_?void 0:_.thumbPolicy)||void 0===h?void 0:h.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",I).replace("{x}",a.width.toString()).replace("{y}",a.height.toString());if(1===T&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.imagethumb"))return(null===(m=null===(E=this.mixStorePolicy.nosPolicy)||void 0===E?void 0:E.thumbPolicy)||void 0===m?void 0:m.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",I).replace("{x}",a.width.toString()).replace("{y}",a.height.toString())}return includes(t).call(t,"?")?t+"&imageView&thumbnail="+a.width+"x"+a.height:t+"?imageView&thumbnail="+a.width+"x"+a.height},t.getVideoCoverUrl=function getVideoCoverUrl(t,a){var u,_,h,E,m;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),t;var g=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);g[0],g[1],g[2],g[3],g[4];var I=g[5];if(g[6],g[7],null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable){var T=this._getUrlType(t);if(2===T&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.vframe"))return(null===(h=null===(_=this.mixStorePolicy.s3Policy)||void 0===_?void 0:_.thumbPolicy)||void 0===h?void 0:h.vframe).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",I).replace("{x}",a.width.toString()).replace("{y}",a.height.toString()).replace("{offset}","0").replace("{type}","png");if(1===T&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.vframe"))return(null===(m=null===(E=this.mixStorePolicy.nosPolicy)||void 0===E?void 0:E.thumbPolicy)||void 0===m?void 0:m.vframe).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",I).replace("{x}",a.width.toString()).replace("{y}",a.height.toString()).replace("{offset}","0").replace("{type}","png")}return includes(t).call(t,"?")?t+"&vframe&offset=0&resize="+a.width+"x"+a.height+"&type=png":t+"?vframe&offset=0&resize="+a.width+"x"+a.height+"&type=png"},t.getPrivateUrl=function getPrivateUrl(t){var a;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),"";var u=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);u[0];var _=u[1];u[2];var h=u[3];u[4];var E=u[5];if(u[6],u[7],null===(a=this.grayConfig)||void 0===a?void 0:a.mixStoreEnable){var m=this._getUrlType(t);return 2===m&&this.mixStorePolicy.s3Policy&&(t=this.mixStorePolicy.s3Policy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",E)),1===m&&this.mixStorePolicy.nosPolicy&&(t=this.mixStorePolicy.nosPolicy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",E)),t}var g=this.config,I=g.downloadUrl,T=g.downloadHostList,N=g.nosCdnEnable,M=this.config.cdn.cdnDomain,O=this.config.cdn.objectNamePrefix?decodeURIComponent(this.config.cdn.objectNamePrefix):"",S=decodeURIComponent(E),R=indexOf(S).call(S,O);if(M&&R>-1&&N)return""+_+M+"/"+slice(S).call(S,R);if(includes(T).call(T,h)&&includes(E).call(E,"/")){var C=indexOf(E).call(E,"/"),A=E.substring(0,C),b=E.substring(C+1);return I.replace("{bucket}",A).replace("{object}",b)}var k=filter(T).call(T,(function(t){return"string"==typeof h&&includes(h).call(h,t)}))[0],L=k?h.replace(k,"").replace(/\W/g,""):null;return L?I.replace("{bucket}",L).replace("{object}",E):t},t.getOriginUrl=function getOriginUrl(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){var a;return Sa.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:if("string"==typeof t&&includes(t).call(t,"_im_url=1")){u.next=2;break}return u.abrupt("return",t);case 2:return u.next=4,this.core.sendCmd("getOriginUrl",{nosSafeUrlTag:{safeUrl:t}});case 4:return a=u.sent,u.abrupt("return",a.content.nosSafeUrlTag.originUrl);case 6:case"end":return u.stop()}}),_callee4,this)})))},t.getFileToken=function getFileToken(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){var a,u,_,h,E,m=this;return Sa.wrap((function _callee5$(g){for(;;)switch(g.prev=g.next){case 0:if(validate({type:{type:"number",min:2,max:3},urls:{type:"array",required:!1,itemType:"string"}},t),a=this.mixStorePolicy.nosPolicy?this.mixStorePolicy.nosPolicy.authPolicy.policyType:null,u=this.mixStorePolicy.s3Policy?this.mixStorePolicy.s3Policy.authPolicy.policyType:null,a!==String(-1)||u!==String(-1)){g.next=8;break}throw this.logger.error("don't need token"),new Error("don't need token");case 8:if(2!==t.type){g.next=17;break}if(!(a&&indexOf(a).call(a,String(2))>=0||u&&indexOf(u).call(u,String(2))>0)){g.next=13;break}return g.abrupt("return",this.mixStorage.getFileAuthToken(t));case 13:throw this.logger.error("don't support time token "),new Error("don't support type time token ");case 15:g.next=33;break;case 17:if(t.urls&&t.urls.length){g.next=20;break}throw this.logger.error("urls is required when urls token"),new Error("urls is required when urls token");case 20:if(h=[],E=[],forEach$1(_=t.urls).call(_,(function(t){var a=m._getUrlType(t);1===a&&E.push(t),2===a&&h.push(t)})),(!u||0!==h.length&&indexOf(u).call(u,String(3))<0)&&(this.logger.warn("s3 url don't support url token"),h=[]),(!a||0!==E.length&&indexOf(a).call(a,String(3))<0)&&(this.logger.warn("nos url don't support url token"),E=[]),0!==h.length||0!==E.length){g.next=30;break}throw this.logger.error("not support urls"),new Error("not support urls");case 30:if(0!==h.length&&0!==E.length){g.next=33;break}return t.urls=Ms(t.urls),g.abrupt("return",this.mixStorage.getFileAuthToken(t));case 33:case"end":return g.stop()}}),_callee5,this)})))},t._getUrlType=function _getUrlType(t){var a,u;return this.mixStorePolicy.nosPolicy&&some(a=this.mixStorePolicy.nosPolicy.dlcdns).call(a,(function(a){return indexOf(t).call(t,a)>=0}))?1:this.mixStorePolicy.s3Policy&&some(u=this.mixStorePolicy.s3Policy.dlcdns).call(u,(function(a){return indexOf(t).call(t,a)>=0}))?2:null},t.getNosAccessToken=function getNosAccessToken(t){return validate({url:{type:"string",allowEmpty:!1}},t),this.nos.getNosAccessToken(t)},t.deleteNosAccessToken=function deleteNosAccessToken(t){return validate({token:{type:"string",allowEmpty:!1}},t),this.nos.deleteNosAccessToken(t)},t.process=function process(t){var a=get(t,"error.detail.ignore");return t.error&&!a?_a.reject(t.error):_a.resolve(t)},ze(CloudStorageService,[{key:"grayConfig",get:function get(){return this.mixStorage.grayConfig}},{key:"mixStorePolicy",get:function get(){return this.mixStorage.mixStorePolicy}}]),CloudStorageService}();function getFileOrPath(t){var a="object"==typeof t?t:void 0,u="string"==typeof t?t:void 0;if(!a&&!u)throw new vl({code:gl.V2NIM_ERROR_CODE_MISUSE,detail:{reason:"getFileOrPath::incorrect file and path"}});if("string"==typeof u)if(0===indexOf(u).call(u,"nim-external")){var _=document.getElementById(u);if(!(_&&_.files&&_.files[0]))throw new vl({code:gl.V2NIM_ERROR_CODE_FILE_NOT_FOUND,detail:{reason:"getFileOrPath::file not exist: "+u}});a=_.files[0]}else if("BROWSER"===Es.platform)throw new vl({code:gl.V2NIM_ERROR_CODE_MISUSE,detail:{reason:"getFileOrPath::incorrect path: "+u}});if("object"==typeof a&&void 0===a.size)throw new vl({code:gl.V2NIM_ERROR_CODE_MISUSE,detail:{reason:"getFileOrPath::file no size"}});return{file:a,path:u}}var w_=function(t){function V2Service(a,u){var _;return(_=t.call(this)||this).name=a,_.logger=u.logger,_.core=u,_}At(V2Service,t);var a=V2Service.prototype;return a.checkV2=function checkV2(){var t=this.core.options.apiVersion;if("v2"===t)return!0;throw new vl({code:gl.V2NIM_ERROR_CODE_MISUSE,detail:{reason:'The version "'+t+'" of client is not supported.'}})},a.checkLogin=function checkLogin(){if(0===this.core.V2NIMLoginService.getLoginStatus())throw new vl({code:gl.V2NIM_ERROR_CODE_ILLEGAL_STATE,detail:{reason:"Client logout."}})},a.emit=function emit(a){for(var u=this,_=arguments.length,h=new Array(_>1?_-1:0),E=1;E<_;E++)h[E-1]=arguments[E];this.logger.debug(this.name+"::emit event: '"+a.toString()+"',",void 0!==h[0]?h[0]:"",void 0!==h[1]?h[1]:"",void 0!==h[2]?h[2]:"");try{var m,g,I=(m=t.prototype.emit).call.apply(m,concat(g=[this,a]).call(g,h));return I}catch(t){return ls((function(){throw u.logger.error(u.name+"::emit throw error in setTimeout. event: "+a.toString()+". Error",t),t}),0),!1}},a.process=function process(t){var a=this[t.cmd+"Handler"],u=this.handler&&this.handler[t.cmd+"Handler"];if("function"==typeof a||"function"==typeof u){if(t.error)return this.logger.error(t.cmd+"::recvError",t.error),_a.reject(t.error);try{var _=a?a.call(this,t):u.call(this.handler,t);return _a.resolve(_)}catch(t){return _a.reject(t)}}var h=get(t,"error.detail.ignore");return t.error&&!h?_a.reject(t.error):_a.resolve(t)},V2Service}(xl),P_={attachment:{type:"object",rules:{url:{type:"string",allowEmpty:!1}}},thumbSize:{type:"object",rules:{width:{type:"number",required:!1,min:0},height:{type:"number",required:!1,min:0}}}},x_=function(t){function V2NIMStorageUtil(a){var u;return(u=t.call(this,"V2NIMStorageUtil",a)||this).core=a,u}At(V2NIMStorageUtil,t);var a=V2NIMStorageUtil.prototype;return a.imageThumbUrl=function imageThumbUrl(t,a){return t+"?imageView&thumbnail="+a+"z"+a},a.videoCoverUrl=function videoCoverUrl(t,a){return t+"?vframe&offset="+a},a.getImageThumbUrl=function getImageThumbUrl(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var u,_;return Sa.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return this.checkV2(),validate(P_,{attachment:u=t,thumbSize:a},"",!0),a.width=a.width||0,a.height=a.height||0,0===a.width&&0===a.height&&(a.width=150),_=u.url,h.prev=7,h.next=10,this.core.V2NIMStorageService.shortUrlToLong(u.url);case 10:_=h.sent,h.next=16;break;case 13:h.prev=13,h.t0=h.catch(7),this.core.logger.warn("shortUrlToLong error:",h.t0);case 16:return h.abrupt("return",{url:this.core.cloudStorage.getThumbUrl(_,a)});case 17:case"end":return h.stop()}}),_callee,this,[[7,13]])})))},a.getVideoCoverUrl=function getVideoCoverUrl(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var u,_;return Sa.wrap((function _callee2$(h){for(;;)switch(h.prev=h.next){case 0:return this.checkV2(),validate(P_,{attachment:u=t,thumbSize:a},"",!0),a.width=a.width||0,a.height=a.height||0,0===a.width&&0===a.height&&(a.width=150),_=u.url,h.prev=7,h.next=10,this.core.V2NIMStorageService.shortUrlToLong(u.url);case 10:_=h.sent,h.next=16;break;case 13:h.prev=13,h.t0=h.catch(7),this.core.logger.warn("shortUrlToLong error:",h.t0);case 16:return h.abrupt("return",{url:this.core.cloudStorage.getVideoCoverUrl(_,a)});case 17:case"end":return h.stop()}}),_callee2,this,[[7,13]])})))},V2NIMStorageUtil}(w_);var U_=function(t){function V2NIMStorageServiceImpl(a){var u;return(u=t.call(this,"V2NIMStorageService",a)||this).sceneMap={nim_default_profile_icon:{sceneName:"nim_default_profile_icon",expireTime:0},nim_default_im:{sceneName:"nim_default_im",expireTime:0},nim_system_nos_scene:{sceneName:"nim_system_nos_scene",expireTime:0},nim_security:{sceneName:"nim_security",expireTime:0}},u.uploadingMessageInfo={},u.core=a,u.core._registerDep(V_,"cloudStorage"),u.core._registerDep(x_,"V2NIMStorageUtil"),u}At(V2NIMStorageServiceImpl,t);var a=V2NIMStorageServiceImpl.prototype;return a.addCustomStorageScene=function addCustomStorageScene(t,a){return this.checkV2(),validate({sceneName:{type:"string",allowEmpty:!1},expireTime:{type:"number",min:0}},{sceneName:t,expireTime:a},"",!0),this.sceneMap[t]={sceneName:t,expireTime:a},{sceneName:t,expireTime:a}},a.getStorageSceneList=function getStorageSceneList(){return this.checkV2(),wa(this.sceneMap)},a.getStorageScene=function getStorageScene(t){return t&&this.sceneMap[t]||this.sceneMap.nim_default_im},a.hasStorageScene=function hasStorageScene(t){return void 0!==this.sceneMap[t]},a.createUploadFileTask=function createUploadFileTask(t){var a;if(this.checkV2(),"string"==typeof t.fileObj&&0===indexOf(a=t.fileObj).call(a,"nim-external")){var u=document.getElementById(t.fileObj);u&&u.files&&u.files[0]&&(t.fileObj=u.files[0])}return{taskId:$u(),uploadParams:t}},a.uploadFile=function uploadFile(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var u;return Sa.wrap((function _callee$(_){for(;;)switch(_.prev=_.next){case 0:return this.checkV2(),validate({taskId:{type:"string",allowEmpty:!1}},t,"fileTask",!0),_.next=4,this._uploadFile(t,a);case 4:return u=_.sent,_.abrupt("return",u[0]);case 6:case"end":return _.stop()}}),_callee,this)})))},a.uploadFileWithMetaInfo=function uploadFileWithMetaInfo(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var u;return Sa.wrap((function _callee2$(_){for(;;)switch(_.prev=_.next){case 0:return this.checkV2(),validate({taskId:{type:"string",allowEmpty:!1}},t,"fileTask",!0),_.next=4,this._uploadFile(t,a);case 4:return u=_.sent,_.abrupt("return",(h=u[1],E=void 0,m=void 0,g=void 0,I=void 0,T=void 0,N=void 0,M=void 0,O=void 0,S=void 0,R=void 0,C=void 0,A=void 0,E=h.url,m=h.name,g=h.size,I=h.ext,T=h.md5,N=h.h,M=h.w,O=h.orientation,S=h.dur,R=h.audioCodec,C=h.videoCodec,A=h.container,JSON.parse(Ms({url:E,name:m,size:g,ext:I,md5:T,height:N,width:M,orientation:O,duration:S,audioCodec:R,videoCodec:C,container:A}))));case 6:case"end":return _.stop()}var h,E,m,g,I,T,N,M,O,S,R,C,A}),_callee2,this)})))},a._uploadFile=function _uploadFile(t,a,u){var _;return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var h,E,m,g,I,T,N,M,O,S,R=this;return Sa.wrap((function _callee3$(C){for(;;)switch(C.prev=C.next){case 0:if(this.core.cloudStorage&&this.core.cloudStorage.uploadFile){C.next=2;break}throw new Error('Service "cloudStorage" does not exist');case 2:if(h=t.uploadParams,E=t.taskId,m=getFileOrPath(h.fileObj),g=m.file,I=m.path,T=(u||{}).fileType,!this.uploadingMessageInfo[E]){C.next=7;break}throw new vl({code:gl.V2NIM_ERROR_CODE_RESOURCE_ALREADY_EXIST,detail:{reason:"V2NIMStorageService.uploadFile: repeat upload"}});case 7:if(C.prev=7,N={},g?N.file=g:I&&(0===(null==I?void 0:indexOf(I).call(I,"nim-external"))?N.fileInput=I:N.filePath=I),M=this.getStorageScene(h.sceneName),N.nosScenes=M.sceneName,N.nosSurvivalTime=M.expireTime,N.type=1===T?"image":2===T?"audio":3===T?"video":"file",!N.file||!this.core.pluginMap["browser-md5-file"]){C.next=19;break}return C.next=17,this.getFileMd5(this.core.pluginMap["browser-md5-file"],E,N.file);case 17:O=C.sent,N.md5=O;case 19:return N.onUploadProgress=function(t){"function"==typeof a&&a(Math.round(100*t.percentage))},N.onUploadStart=function(t){var a;if(null===(a=R.uploadingMessageInfo[E])||void 0===a?void 0:a.abort)return t.abort(),void delete R.uploadingMessageInfo[E];R.uploadingMessageInfo[E]={abort:!1,task:t}},this.uploadingMessageInfo[E]={abort:!1},C.next=24,this.core.cloudStorage.uploadFile(N);case 24:if(S=C.sent,!(null===(_=this.uploadingMessageInfo[E])||void 0===_?void 0:_.abort)){C.next=27;break}throw new vl({code:gl.V2NIM_ERROR_CODE_CANCELLED,detail:{reason:"upload file aborted"}});case 27:return delete this.uploadingMessageInfo[E],C.abrupt("return",[S.url,S]);case 31:throw C.prev=31,C.t0=C.catch(7),delete this.uploadingMessageInfo[E],this.core.logger.error("sendFile:: upload File error or abort.",C.t0),C.t0;case 36:case"end":return C.stop()}}),_callee3,this,[[7,31]])})))},a.cancelUploadFile=function cancelUploadFile(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){return Sa.wrap((function _callee4$(a){for(;;)switch(a.prev=a.next){case 0:return this.checkV2(),a.next=3,this._cancelUploadFile(t.taskId);case 3:case"end":return a.stop()}}),_callee4,this)})))},a._cancelUploadFile=function _cancelUploadFile(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){var a;return Sa.wrap((function _callee5$(u){for(;;)switch(u.prev=u.next){case 0:if(this.checkV2(),!(null==(a=this.uploadingMessageInfo[t])?void 0:a.task)){u.next=16;break}return u.prev=3,this.logger.log("V2NIMStorageService.cancelUploadFile: uploadInfo task exist"),u.next=7,a.task.abort();case 7:delete this.uploadingMessageInfo[t],u.next=14;break;case 10:u.prev=10,u.t0=u.catch(3),delete this.uploadingMessageInfo[t],this.core.logger.error("cancelMessageAttachmentUpload::abort error.",u.t0);case 14:u.next=22;break;case 16:if(!a){u.next=21;break}this.logger.log("V2NIMStorageService.cancelUploadFile: uploadInfo task not exist"),a.abort=!0,u.next=22;break;case 21:throw new vl({code:gl.V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST,detail:{reason:"V2NIMStorageService.cancelUploadFile: uploadInfo not exist"}});case 22:case"end":return u.stop()}}),_callee5,this,[[3,10]])})))},a.getFileMd5=function getFileMd5(t,a,u){return __awaiter(this,void 0,void 0,Sa.mark((function _callee6(){var _=this;return Sa.wrap((function _callee6$(h){for(;;)switch(h.prev=h.next){case 0:return h.abrupt("return",new _a((function(h,E){var m,g=new t;(null===(m=_.uploadingMessageInfo[a])||void 0===m?void 0:m.abort)?E(new vl({code:gl.V2NIM_ERROR_CODE_CANCELLED,detail:{reason:"upload file aborted"}})):_.uploadingMessageInfo[a]={abort:!1,task:g};try{g.md5(u,(function(t,a){"aborted"===t?E(new vl({code:gl.V2NIM_ERROR_CODE_CANCELLED,detail:{reason:t}})):t?E(new vl({code:gl.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"md5 calculate error in callback",rawError:t}})):h(a)}))}catch(t){E(new vl({code:gl.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"md5 calculate error",rawError:t}}))}})));case 1:case"end":return h.stop()}}),_callee6)})))},a.shortUrlToLong=function shortUrlToLong(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee7(){return Sa.wrap((function _callee7$(a){for(;;)switch(a.prev=a.next){case 0:return this.checkV2(),a.abrupt("return",this.core.cloudStorage.getOriginUrl(t));case 2:case"end":return a.stop()}}),_callee7,this)})))},a.getImageThumbUrl=function getImageThumbUrl(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee8(){return Sa.wrap((function _callee8$(u){for(;;)switch(u.prev=u.next){case 0:return u.abrupt("return",this.core.V2NIMStorageUtil.getImageThumbUrl(t,a));case 1:case"end":return u.stop()}}),_callee8,this)})))},a.getVideoCoverUrl=function getVideoCoverUrl(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee9(){return Sa.wrap((function _callee9$(u){for(;;)switch(u.prev=u.next){case 0:return u.abrupt("return",this.core.V2NIMStorageUtil.getVideoCoverUrl(t,a));case 1:case"end":return u.stop()}}),_callee9,this)})))},V2NIMStorageServiceImpl}(w_),F_=function(){function FileUtil(t,a){this.core=t,this.service=a}var t=FileUtil.prototype;return t.doSendFile=function doSendFile(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var u,_,h,E,m,g,I,T;return Sa.wrap((function _callee$(N){for(;;)switch(N.prev=N.next){case 0:return u=t.attachment,N.prev=1,N.next=4,this.core.V2NIMStorageService._uploadFile({taskId:t.messageClientId,uploadParams:{fileObj:(null==u?void 0:u.file)||(null==u?void 0:u.path),sceneName:null==u?void 0:u.sceneName}},a,{fileType:t.messageType});case 4:for(I in(h=N.sent)[0],E=h[1],m=ba(ba({},u),{uploadState:1}),E.w&&(m.width=m.width||E.w),E.h&&(m.height=m.height||E.h),E.dur&&(m.duration=m.duration||E.dur),m.ext=m.ext&&-1===indexOf(_=m.ext).call(_,".")?"."+m.ext:m.ext,g=["w","h","dur","ext","name"],E)includes(g).call(g,I)||(m[I]=E[I]);m.raw,m.file,m.path,T=__rest(m,["raw","file","path"]),t.attachment=JSON.parse(Ms(T)),t.attachment&&(t.attachment.raw=attachmentToRaw(t.messageType,t.attachment)),N.next=23;break;case 19:throw N.prev=19,N.t0=N.catch(1),t.attachment&&(t.attachment.uploadState=2),N.t0;case 23:case"end":return N.stop()}}),_callee,this,[[1,19]])})))},t.cancelMessageAttachmentUpload=function cancelMessageAttachmentUpload(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var a;return Sa.wrap((function _callee2$(u){for(;;)switch(u.prev=u.next){case 0:if(validate({messageClientId:{type:"string",allowEmpty:!1}},t,"",!0),includes(a=[2,6,1,3]).call(a,t.messageType)){u.next=3;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_MISUSE,detail:{reason:"cancelMessageAttachmentUpload: messageType "+t.messageType+" incorrect"}});case 3:if(2!==t.sendingState&&1!==t.sendingState){u.next=5;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST,detail:{reason:"cancelMessageAttachmentUpload: message is already failed or succeeded"}});case 5:return u.next=7,this.core.V2NIMStorageService._cancelUploadFile(t.messageClientId);case 7:case"end":return u.stop()}}),_callee2,this)})))},FileUtil}(),B_=function(){function SendUtil(t,a){this.msgs=[],this.maxIdCount=100,this.core=t,this.service=a}var t=SendUtil.prototype;return t.reset=function reset(){this.msgs=[]},t.prepareMessage=function prepareMessage(t,a){void 0===a&&(a={});var u=this.checkIfResend(t),_=this.generateSendMessage({message:t,params:a,resend:u}),h=this.checkIfAntispam(a,_),E=h.clientAntispamResult,m=h.text;return _.text=m,_.clientAntispamHit=!!E&&3===E.operateType,{messageBeforeSend:_,clientAntispamResult:E}},t.doSendMessage=function doSendMessage(t,a,u,_){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var h,E,m,g,I;return Sa.wrap((function _callee$(T){for(;;)switch(T.prev=T.next){case 0:if(h={enabled:!1},!a.attachment||"object"!=typeof a.attachment||!("uploadState"in a.attachment)||a.attachment.url||0!==a.attachment.uploadState&&2!==a.attachment.uploadState){T.next=26;break}return E=Ga(),T.prev=3,a.attachmentUploadState=3,a.attachment.uploadState=3,this.core.V2NIMChatroomService.emit("onSendMessage",a),T.next=9,this.service.fileUtil.doSendFile(a,_);case 9:a.attachmentUploadState=1,a.attachment.uploadState=1,this.core.V2NIMChatroomService.emit("onSendMessage",a),T.next=23;break;case 14:throw T.prev=14,T.t0=T.catch(3),a.attachmentUploadState=2,a.attachment.uploadState=2,a.sendingState=2,this.core.V2NIMChatroomService.emit("onSendMessage",a),h.attachUploadDuration=Ga()-E,this.doMsgSendReport(t,h,a,T.t0),T.t0;case 23:h.attachUploadDuration=Ga()-E,T.next=27;break;case 26:this.core.V2NIMChatroomService.emit("onSendMessage",a);case 27:return this.cacheMsg(a),this.core.timeOrigin.checkNodeReliable(t)&&(h.apiCallingTime=this.core.timeOrigin.getNTPTime(t),h.sendTime=this.core.timeOrigin.getNTPTime(),a.__clientExt={statistics:h}),T.prev=29,T.next=32,this.core.clientSocket.sendCmd("v2ChatroomSendMessage",{tag:a});case 32:m=T.sent,T.next=41;break;case 35:throw T.prev=35,T.t1=T.catch(29),a.sendingState=2,this.core.V2NIMChatroomService.emit("onSendMessage",a),this.doMsgSendReport(t,h,a,T.t1),T.t1;case 41:return g=formatMessage(this.core,ba(ba(ba({},a),m.content.data),{sendingState:1}),this.core.account),this.doMsgSendReport(t,h,a),I=g.antispamResult,delete g.antispamResult,delete g.__clientExt,this.core.V2NIMChatroomService.emit("onSendMessage",g),T.abrupt("return",ba(ba({message:g},I?{antispamResult:I}:{}),u?{clientAntispamResult:u}:{}));case 48:case"end":return T.stop()}}),_callee,this,[[3,14],[29,35]])})))},t.doMsgSendReport=function doMsgSendReport(t,a,u,_){a.apiCallingTime=this.core.timeOrigin.getNTPTime(t),a.sendTime=this.core.timeOrigin.getNTPTime();var h=this.core.timeOrigin.getNTPTime(),E=get(_,"detail.reason");this.core.reporter.report("msgSend",{clientId:u.messageClientId,msgTime:u.createTime,fromAccid:u.senderId,type:4,roomId:u.roomId,result:_?_.code:200,failReason:E||(null==_?void 0:_.message)||"",rt:h-a.apiCallingTime,apiCallingTime:a.apiCallingTime,sendTime:a.sendTime,attachUploadDuration:a.attachUploadDuration,apiCallbackTime:h})},t.doMsgReceiveReport=function doMsgReceiveReport(t,a){if(t.senderId!==this.core.account){var u=get(t,"__clientExt.statistics.apiCallingTime")||0,_=get(t,"__clientExt.statistics.sendTime")||0,h=get(t,"__clientExt.statistics.attachUploadDuration")||0,E=this.core.timeOrigin.getNTPTime(),m=t.createTime,g=this.core.timeOrigin.checkNodeReliable(a.__receiveTimeNode)?this.core.timeOrigin.getNTPTime(a.__receiveTimeNode):E;this.core.reporter.report("msgReceive",{clientId:t.messageClientId,receiveTime:g,serverTime:m,apiCallingTime:u,sendTime:_,attachUploadDuration:h,callbackTime:E,preHandleTime:E,fromAccid:t.senderId,type:4,roomId:t.roomId,result:200,failReason:"",rt:E-m})}},t.cacheMsg=function cacheMsg(t){this.msgs.push({messageClientId:t.messageClientId,senderId:t.senderId}),this.msgs.length>this.maxIdCount&&this.msgs.shift()},t.checkIfResend=function checkIfResend(t){var a;return some(a=this.msgs).call(a,(function(a){return a.messageClientId===t.messageClientId}))},t.generateSendMessage=function generateSendMessage(t){var a,u,_,h=t.message,E=t.params,m=t.resend,g={};(h.locationInfo||E.locationInfo)&&(g.x=(null===(a=h.locationInfo||E.locationInfo)||void 0===a?void 0:a.x)||0,g.y=(null===(u=h.locationInfo||E.locationInfo)||void 0===u?void 0:u.y)||0,g.z=(null===(_=h.locationInfo||E.locationInfo)||void 0===_?void 0:_.z)||0);var I=ba(ba(ba(ba(ba({},h),E),{messageConfig:ba(ba({},h.messageConfig),E.messageConfig),routeConfig:ba(ba({},h.routeConfig),E.routeConfig),antispamConfig:ba(ba({},h.antispamConfig),E.antispamConfig)}),h.attachment?{attachment:ba({},h.attachment)}:{}),{locationInfo:g,resend:m,sendingState:3});return 0!==I.messageType&&10!==I.messageType||(I.attachment=I.text),I},t.checkIfAntispam=function checkIfAntispam(t,a){var u,_=a.text;if(t.clientAntispamEnabled&&100!==a.messageType&&a.text)if(1===(u=this.core.V2NIMClientAntispamUtil.checkTextAntispam(a.text||"",t.clientAntispamReplace)).operateType)_=u.replacedText;else if(2===u.operateType)throw this.core.V2NIMChatroomService.emit("onSendMessage",ba(ba({},a),{sendingState:2})),new vl({code:Ud.V2NIM_ERROR_CODE_CLIENT_ANTISPAM,detail:{reason:"sendMessage: text intercepted by client antispam"}});return{clientAntispamResult:u,text:_}},SendUtil}(),G_=[2,7,12,100,6,1,-1,4,5,11,0,10,3],H_={message:{type:"object",rules:{messageClientId:{type:"string",allowEmpty:!1},senderId:{type:"string",allowEmpty:!1},roomId:{type:"string",allowEmpty:!1},text:{type:"string",required:!1},attachment:{type:"object",required:!1,rules:{file:{type:"file",required:!1}}}}}},Y_={params:{type:"object",required:!1,rules:{messageConfig:{type:"object",required:!1,rules:{readReceiptEnabled:{type:"boolean",required:!1},lastMessageUpdateEnabled:{type:"boolean",required:!1},historyEnabled:{type:"boolean",required:!1},roamingEnabled:{type:"boolean",required:!1},onlineSyncEnabled:{type:"boolean",required:!1},offlineEnabled:{type:"boolean",required:!1},unreadEnabled:{type:"boolean",required:!1}}},routeConfig:{type:"object",required:!1,rules:{routeEnabled:{type:"boolean",required:!1},routeEnvironment:{type:"string",required:!1}}},antiSpamConfig:{type:"object",required:!1,rules:{antispamEnabled:{type:"boolean",required:!1},antispamBusinessId:{type:"string",required:!1},antispamCustomMessage:{type:"string",required:!1},antispamCheating:{type:"string",required:!1},antispamExtension:{type:"string",required:!1}}},receiverIds:{type:"array",required:!1},notifyTargetTags:{type:"string",required:!1},locationInfo:{type:"object",required:!1,rules:{x:{type:"number"},y:{type:"number"},z:{type:"number"}}}}}},j_={sceneName:{type:"string",required:!1},name:{type:"string",required:!1}},K_=ba(ba({},j_),{duration:{type:"number",required:!1}}),q_=ba(ba({},K_),{width:{type:"number",required:!1},height:{type:"number",required:!1}}),W_=ba(ba({},j_),{width:{type:"number",required:!1},height:{type:"number",required:!1}}),z_={option:{type:"object",rules:{direction:{type:"enum",values:[1,0],required:!1},messageTypes:{type:"array",required:!1,itemType:"enum",values:G_},beginTime:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}}}},$_={messageOption:{type:"object",rules:{tags:{type:"array",min:1,itemType:"string"},direction:{type:"enum",values:[1,0],required:!1},messageTypes:{type:"array",required:!1,itemType:"enum",values:G_},beginTime:{type:"number",min:0,required:!1},endTime:{type:"number",min:0,required:!1},limit:{type:"number",min:1,required:!1}}}},X_="V2NIMChatroomMessageService",Q_={"36_6":"v2ChatroomSendMessage","36_9":"v2ChatroomGetMessageList","36_35":"v2ChatroomMessageAck","36_36":"v2ChatroomGetMessageListByTag","13_7":"v2ChatroomOnMessage","29_17":"v2ChatroomDownloadLocalAntiSpamVocabs","6_23":"v2ChatroomGetServerTime","13_99":"v2ChatroomUpdateCDNInfo"},J_={version:1,md5:2,nosurl:3,thesaurus:4},Z_={messageClientId:1,messageType:{id:2,retType:"number"},attachment:{id:3,converter:function converter(t,a){return attachmentToRaw(a.messageType,t)},retConverter:function retConverter(t,a){return rawToAttachment(t,Number(a[2]))}},serverExtension:4,resend:{id:5,converter:boolToInt,retType:"boolean"},userInfoTimestamp:{id:6,access:"userInfoConfig.userInfoTimestamp",retType:"number"},senderNick:{id:7,access:"userInfoConfig.senderNick"},senderAvatar:{id:8,access:"userInfoConfig.senderAvatar"},senderExtension:{id:9,access:"userInfoConfig.senderExtension"},antispamCustomMessageEnabled:{id:10,def:function def(t){return get(t,"antispamConfig.antispamCustomMessage")?1:void 0},retConverter:function retConverter(){}},antispamCustomMessage:{id:11,access:"antispamConfig.antispamCustomMessage"},historyEnabled:{id:12,access:"messageConfig.historyEnabled",converter:function converter(t){return t?0:1},retConverter:function retConverter(t){return!Ql(t)}},text:13,antiSpamBusinessId:{id:14,access:"antispamConfig.antispamBusinessId"},clientAntispamHit:{id:15,access:"clientAntispamHit",converter:boolToInt,retType:"boolean"},antispamEnabled:{id:16,access:"antispamConfig.antispamEnabled",converter:boolToInt,retType:"boolean"},createTime:{id:20,retType:"number"},senderId:21,roomId:22,senderClientType:{id:23,retType:"number"},highPriority:{id:25,access:"messageConfig.highPriority",converter:boolToInt,retType:"boolean"},callbackExtension:27,subType:{id:28,retType:"number"},antispamCheating:{id:29,access:"antispamConfig.antispamCheating"},routeEnvironment:{id:30,access:"routeConfig.routeEnvironment"},notifyTargetTags:31,antispamExtension:{id:32,access:"antispamConfig.antispamExtension"},antispamResult:33,x:{id:34,access:"locationInfo.x",retType:"number"},y:{id:35,access:"locationInfo.y",retType:"number"},z:{id:36,access:"locationInfo.z",retType:"number"},receiverIds:{id:37,converter:objectToJSONString},__clientExt:{id:39,converter:objectToJSONString,retConverter:stringToJSONObject},routeEnabled:{id:100,access:"routeConfig.routeEnabled",converter:boolToInt,retType:"boolean",retDef:!0}},ep={v2ChatroomSendMessage:{sid:36,cid:6,service:X_,params:[{type:"Property",name:"tag",reflectMapper:Z_}],response:[{type:"Property",name:"data",reflectMapper:invertSerializeItem(Z_)}]},v2ChatroomOnMessage:{sid:13,cid:7,service:X_,response:[{type:"Property",name:"data",reflectMapper:invertSerializeItem(Z_)}]},v2ChatroomGetMessageList:{sid:36,cid:9,service:X_,params:[{type:"Long",name:"beginTime"},{type:"Int",name:"limit"},{type:"Bool",name:"reverse"},{type:"LongArray",name:"messageTypes"}],response:[{type:"PropertyArray",name:"datas",reflectMapper:invertSerializeItem(Z_)}]},v2ChatroomMessageAck:{sid:36,cid:35,service:X_,params:[{type:"Property",name:"tag",reflectMapper:{messageClientId:1,roomId:2}}]},v2ChatroomGetMessageListByTag:{sid:36,cid:36,service:X_,params:[{type:"Property",name:"tag",reflectMapper:{tags:1,messageTypes:2,beginTime:3,endTime:4,limit:5,reverse:6}}],response:[{type:"PropertyArray",name:"datas",reflectMapper:invertSerializeItem(Z_)}]},v2ChatroomDownloadLocalAntiSpamVocabs:{sid:29,cid:17,service:X_,params:[{type:"Property",name:"tag",reflectMapper:J_}],response:[{type:"Property",name:"data",reflectMapper:invertSerializeItem(J_)}]},v2ChatroomGetServerTime:{sid:6,cid:23,service:X_,response:[{type:"Long",name:"time"}]},v2ChatroomUpdateCDNInfo:{sid:13,cid:99,service:X_,response:[{type:"Property",name:"chatroomCdnInfo",reflectMapper:invertSerializeItem(Vd)}]}},tp=Y.setInterval,rp=rp||function(t){var a;"undefined"!=typeof window&&window.crypto&&(a=window.crypto),"undefined"!=typeof self&&self.crypto&&(a=self.crypto),void 0!==Ua&&Ua.crypto&&(a=Ua.crypto),!a&&"undefined"!=typeof window&&window.msCrypto&&(a=window.msCrypto),!a&&"undefined"!=typeof global&&global.crypto&&(a=global.crypto);var u=function cryptoSecureRandomInt(){if(a){if("function"==typeof a.getRandomValues)try{return a.getRandomValues(new Uint32Array(1))[0]}catch(t){}if("function"==typeof a.randomBytes)try{return a.randomBytes(4).readInt32LE()}catch(t){}}throw new Error("Native crypto module could not be used to get secure random number.")},_=ft||function(){function F(){}return function(t){var a;return F.prototype=t,a=new F,F.prototype=null,a}}(),h={},E=h.lib={},m=E.Base={extend:function extend(t){var a=_(this);return t&&a.mixIn(t),a.hasOwnProperty("init")&&this.init!==a.init||(a.init=function(){a.$super.init.apply(this,arguments)}),a.init.prototype=a,a.$super=this,a},create:function create(){var t=this.extend();return t.init.apply(t,arguments),t},init:function init(){},mixIn:function mixIn(t){for(var a in t)t.hasOwnProperty(a)&&(this[a]=t[a]);t.hasOwnProperty("toString")&&(this.toString=t.toString)},clone:function clone(){return this.init.prototype.extend(this)}},g=E.WordArray=m.extend({init:function init(t,a){t=this.words=t||[],this.sigBytes=null!=a?a:4*t.length},toString:function toString(t){return(t||T).stringify(this)},concat:function concat(t){var a=this.words,u=t.words,_=this.sigBytes,h=t.sigBytes;if(this.clamp(),_%4)for(var E=0;E<h;E++){var m=u[E>>>2]>>>24-E%4*8&255;a[_+E>>>2]|=m<<24-(_+E)%4*8}else for(var g=0;g<h;g+=4)a[_+g>>>2]=u[g>>>2];return this.sigBytes+=h,this},clamp:function clamp(){var a=this.words,u=this.sigBytes;a[u>>>2]&=4294967295<<32-u%4*8,a.length=t.ceil(u/4)},clone:function clone(){var t,clone=m.clone.call(this);return clone.words=slice(t=this.words).call(t,0),clone},random:function random(t){for(var a=[],_=0;_<t;_+=4)a.push(u());return new g.init(a,t)}}),I=h.enc={},T=I.Hex={stringify:function stringify(t){for(var a=t.words,u=t.sigBytes,_=[],h=0;h<u;h++){var E=a[h>>>2]>>>24-h%4*8&255;_.push((E>>>4).toString(16)),_.push((15&E).toString(16))}return _.join("")},parse:function parse(t){for(var a=t.length,u=[],_=0;_<a;_+=2)u[_>>>3]|=Ql(t.substr(_,2),16)<<24-_%8*4;return new g.init(u,a/2)}},N=I.Latin1={stringify:function stringify(t){for(var a=t.words,u=t.sigBytes,_=[],h=0;h<u;h++){var E=a[h>>>2]>>>24-h%4*8&255;_.push(String.fromCharCode(E))}return _.join("")},parse:function parse(t){for(var a=t.length,u=[],_=0;_<a;_++)u[_>>>2]|=(255&t.charCodeAt(_))<<24-_%4*8;return new g.init(u,a)}},M=I.Utf8={stringify:function stringify(t){try{return decodeURIComponent(escape(N.stringify(t)))}catch(t){throw new Error("Malformed UTF-8 data")}},parse:function parse(t){return N.parse(unescape(encodeURIComponent(t)))}},O=E.BufferedBlockAlgorithm=m.extend({reset:function reset(){this._data=new g.init,this._nDataBytes=0},_append:function _append(t){var a;"string"==typeof t&&(t=M.parse(t)),concat(a=this._data).call(a,t),this._nDataBytes+=t.sigBytes},_process:function _process(a){var u,_=this._data,h=_.words,E=_.sigBytes,m=this.blockSize,I=E/(4*m),T=(I=a?t.ceil(I):t.max((0|I)-this._minBufferSize,0))*m,N=t.min(4*T,E);if(T){for(var M=0;M<T;M+=m)this._doProcessBlock(h,M);u=splice(h).call(h,0,T),_.sigBytes-=N}return new g.init(u,N)},clone:function clone(){var clone=m.clone.call(this);return clone._data=this._data.clone(),clone},_minBufferSize:0});E.Hasher=O.extend({cfg:m.extend(),init:function init(t){this.cfg=this.cfg.extend(t),this.reset()},reset:function reset(){O.reset.call(this),this._doReset()},update:function update(t){return this._append(t),this._process(),this},finalize:function finalize(t){return t&&this._append(t),this._doFinalize()},blockSize:16,_createHelper:function _createHelper(t){return function(a,u){return new t.init(u).finalize(a)}},_createHmacHelper:function _createHmacHelper(t){return function(a,u){return new S.HMAC.init(t,u).finalize(a)}}});var S=h.algo={};return h}(Math),np=rp,op=np.lib,ip=op.Base,ap=op.WordArray,sp=np.algo,cp=sp.MD5,lp=sp.EvpKDF=ip.extend({cfg:ip.extend({keySize:4,hasher:cp,iterations:1}),init:function init(t){this.cfg=this.cfg.extend(t)},compute:function compute(t,a){for(var u,_=this.cfg,h=_.hasher.create(),E=ap.create(),m=E.words,g=_.keySize,I=_.iterations;m.length<g;){u&&h.update(u),u=h.update(t).finalize(a),h.reset();for(var T=1;T<I;T++)u=h.finalize(u),h.reset();concat(E).call(E,u)}return E.sigBytes=4*g,E}});np.EvpKDF=function(t,a,u){return lp.create(u).compute(t,a)},rp.EvpKDF;var up=rp,dp=up.lib.WordArray;up.enc.Base64={stringify:function stringify(t){var a=t.words,u=t.sigBytes,_=this._map;t.clamp();for(var h=[],E=0;E<u;E+=3)for(var m=(a[E>>>2]>>>24-E%4*8&255)<<16|(a[E+1>>>2]>>>24-(E+1)%4*8&255)<<8|a[E+2>>>2]>>>24-(E+2)%4*8&255,g=0;g<4&&E+.75*g<u;g++)h.push(_.charAt(m>>>6*(3-g)&63));var I=_.charAt(64);if(I)for(;h.length%4;)h.push(I);return h.join("")},parse:function parse(t){var a=t.length,u=this._map,_=this._reverseMap;if(!_){_=this._reverseMap=[];for(var h=0;h<u.length;h++)_[u.charCodeAt(h)]=h}var E=u.charAt(64);if(E){var m=indexOf(t).call(t,E);-1!==m&&(a=m)}return function parseLoop(t,a,u){for(var _=[],h=0,E=0;E<a;E++)if(E%4){var m=u[t.charCodeAt(E-1)]<<E%4*2|u[t.charCodeAt(E)]>>>6-E%4*2;_[h>>>2]|=m<<24-h%4*8,h++}return dp.create(_,h)}(t,a,_)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="};var _p=rp.enc.Base64;!function(t){t.lib.Cipher||function(){var a=t,u=a.lib,_=u.Base,h=u.WordArray,E=u.BufferedBlockAlgorithm,m=a.enc;m.Utf8;var g=m.Base64,I=a.algo.EvpKDF,T=u.Cipher=E.extend({cfg:_.extend(),createEncryptor:function createEncryptor(t,a){return this.create(this._ENC_XFORM_MODE,t,a)},createDecryptor:function createDecryptor(t,a){return this.create(this._DEC_XFORM_MODE,t,a)},init:function init(t,a,u){this.cfg=this.cfg.extend(u),this._xformMode=t,this._key=a,this.reset()},reset:function reset(){E.reset.call(this),this._doReset()},process:function process(t){return this._append(t),this._process()},finalize:function finalize(t){return t&&this._append(t),this._doFinalize()},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function selectCipherStrategy(t){return"string"==typeof t?k:A}return function(t){return{encrypt:function encrypt(a,u,_){return selectCipherStrategy(u).encrypt(t,a,u,_)},decrypt:function decrypt(a,u,_){return selectCipherStrategy(u).decrypt(t,a,u,_)}}}}()});u.StreamCipher=T.extend({_doFinalize:function _doFinalize(){return this._process(!0)},blockSize:1});var N=a.mode={},M=u.BlockCipherMode=_.extend({createEncryptor:function createEncryptor(t,a){return this.Encryptor.create(t,a)},createDecryptor:function createDecryptor(t,a){return this.Decryptor.create(t,a)},init:function init(t,a){this._cipher=t,this._iv=a}}),O=N.CBC=function(){var t=M.extend();function xorBlock(t,a,u){var _,h=this._iv;h?(_=h,this._iv=void 0):_=this._prevBlock;for(var E=0;E<u;E++)t[a+E]^=_[E]}return t.Encryptor=t.extend({processBlock:function processBlock(t,a){var u=this._cipher,_=u.blockSize;xorBlock.call(this,t,a,_),u.encryptBlock(t,a),this._prevBlock=slice(t).call(t,a,a+_)}}),t.Decryptor=t.extend({processBlock:function processBlock(t,a){var u=this._cipher,_=u.blockSize,h=slice(t).call(t,a,a+_);u.decryptBlock(t,a),xorBlock.call(this,t,a,_),this._prevBlock=h}}),t}(),S=(a.pad={}).Pkcs7={pad:function pad(t,a){for(var u=4*a,_=u-t.sigBytes%u,E=_<<24|_<<16|_<<8|_,m=[],g=0;g<_;g+=4)m.push(E);var I=h.create(m,_);concat(t).call(t,I)},unpad:function unpad(t){var a=255&t.words[t.sigBytes-1>>>2];t.sigBytes-=a}};u.BlockCipher=T.extend({cfg:T.cfg.extend({mode:O,padding:S}),reset:function reset(){var t;T.reset.call(this);var a=this.cfg,u=a.iv,_=a.mode;this._xformMode==this._ENC_XFORM_MODE?t=_.createEncryptor:(t=_.createDecryptor,this._minBufferSize=1),this._mode&&this._mode.__creator==t?this._mode.init(this,u&&u.words):(this._mode=t.call(_,this,u&&u.words),this._mode.__creator=t)},_doProcessBlock:function _doProcessBlock(t,a){this._mode.processBlock(t,a)},_doFinalize:function _doFinalize(){var t,a=this.cfg.padding;return this._xformMode==this._ENC_XFORM_MODE?(a.pad(this._data,this.blockSize),t=this._process(!0)):(t=this._process(!0),a.unpad(t)),t},blockSize:4});var R=u.CipherParams=_.extend({init:function init(t){this.mixIn(t)},toString:function toString(t){return(t||this.formatter).stringify(this)}}),C=(a.format={}).OpenSSL={stringify:function stringify(t){var a,u,_,E=t.ciphertext,m=t.salt;m?a=concat(u=concat(_=h.create([1398893684,1701076831])).call(_,m)).call(u,E):a=E;return a.toString(g)},parse:function parse(t){var a,u=g.parse(t),_=u.words;return 1398893684==_[0]&&1701076831==_[1]&&(a=h.create(slice(_).call(_,2,4)),splice(_).call(_,0,4),u.sigBytes-=16),R.create({ciphertext:u,salt:a})}},A=u.SerializableCipher=_.extend({cfg:_.extend({format:C}),encrypt:function encrypt(t,a,u,_){_=this.cfg.extend(_);var h=t.createEncryptor(u,_),E=h.finalize(a),m=h.cfg;return R.create({ciphertext:E,key:u,iv:m.iv,algorithm:t,mode:m.mode,padding:m.padding,blockSize:t.blockSize,formatter:_.format})},decrypt:function decrypt(t,a,u,_){return _=this.cfg.extend(_),a=this._parse(a,_.format),t.createDecryptor(u,_).finalize(a.ciphertext)},_parse:function _parse(t,a){return"string"==typeof t?a.parse(t,this):t}}),b=(a.kdf={}).OpenSSL={execute:function execute(t,a,u,_){var E;_||(_=h.random(8));var m=I.create({keySize:a+u}).compute(t,_),g=h.create(slice(E=m.words).call(E,a),4*u);return m.sigBytes=4*a,R.create({key:m,iv:g,salt:_})}},k=u.PasswordBasedCipher=A.extend({cfg:A.cfg.extend({kdf:b}),encrypt:function encrypt(t,a,u,_){var h=(_=this.cfg.extend(_)).kdf.execute(u,t.keySize,t.ivSize);_.iv=h.iv;var E=A.encrypt.call(this,t,a,h.key,_);return E.mixIn(h),E},decrypt:function decrypt(t,a,u,_){_=this.cfg.extend(_),a=this._parse(a,_.format);var h=_.kdf.execute(u,t.keySize,t.ivSize,a.salt);return _.iv=h.iv,A.decrypt.call(this,t,a,h.key,_)}})}()}(rp);var pp=rp,hp=pp.lib.BlockCipher,fp=pp.algo,Ep=[],mp=[],gp=[],Ip=[],vp=[],Tp=[],Np=[],Mp=[],Op=[],Sp=[];!function(){for(var t=[],a=0;a<256;a++)t[a]=a<128?a<<1:a<<1^283;var u=0,_=0;for(a=0;a<256;a++){var h=_^_<<1^_<<2^_<<3^_<<4;h=h>>>8^255&h^99,Ep[u]=h,mp[h]=u;var E=t[u],m=t[E],g=t[m],I=257*t[h]^16843008*h;gp[u]=I<<24|I>>>8,Ip[u]=I<<16|I>>>16,vp[u]=I<<8|I>>>24,Tp[u]=I;I=16843009*g^65537*m^257*E^16843008*u;Np[h]=I<<24|I>>>8,Mp[h]=I<<16|I>>>16,Op[h]=I<<8|I>>>24,Sp[h]=I,u?(u=E^t[t[t[g^E]]],_^=t[t[_]]):u=_=1}}();var Rp=[0,1,2,4,8,16,32,64,128,27,54],yp=fp.AES=hp.extend({_doReset:function _doReset(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var t=this._keyPriorReset=this._key,a=t.words,u=t.sigBytes/4,_=4*((this._nRounds=u+6)+1),h=this._keySchedule=[],E=0;E<_;E++)E<u?h[E]=a[E]:(I=h[E-1],E%u?u>6&&E%u==4&&(I=Ep[I>>>24]<<24|Ep[I>>>16&255]<<16|Ep[I>>>8&255]<<8|Ep[255&I]):(I=Ep[(I=I<<8|I>>>24)>>>24]<<24|Ep[I>>>16&255]<<16|Ep[I>>>8&255]<<8|Ep[255&I],I^=Rp[E/u|0]<<24),h[E]=h[E-u]^I);for(var m=this._invKeySchedule=[],g=0;g<_;g++){E=_-g;if(g%4)var I=h[E];else I=h[E-4];m[g]=g<4||E<=4?I:Np[Ep[I>>>24]]^Mp[Ep[I>>>16&255]]^Op[Ep[I>>>8&255]]^Sp[Ep[255&I]]}}},encryptBlock:function encryptBlock(t,a){this._doCryptBlock(t,a,this._keySchedule,gp,Ip,vp,Tp,Ep)},decryptBlock:function decryptBlock(t,a){var u=t[a+1];t[a+1]=t[a+3],t[a+3]=u,this._doCryptBlock(t,a,this._invKeySchedule,Np,Mp,Op,Sp,mp);u=t[a+1];t[a+1]=t[a+3],t[a+3]=u},_doCryptBlock:function _doCryptBlock(t,a,u,_,h,E,m,g){for(var I=this._nRounds,T=t[a]^u[0],N=t[a+1]^u[1],M=t[a+2]^u[2],O=t[a+3]^u[3],S=4,R=1;R<I;R++){var C=_[T>>>24]^h[N>>>16&255]^E[M>>>8&255]^m[255&O]^u[S++],A=_[N>>>24]^h[M>>>16&255]^E[O>>>8&255]^m[255&T]^u[S++],b=_[M>>>24]^h[O>>>16&255]^E[T>>>8&255]^m[255&N]^u[S++],k=_[O>>>24]^h[T>>>16&255]^E[N>>>8&255]^m[255&M]^u[S++];T=C,N=A,M=b,O=k}C=(g[T>>>24]<<24|g[N>>>16&255]<<16|g[M>>>8&255]<<8|g[255&O])^u[S++],A=(g[N>>>24]<<24|g[M>>>16&255]<<16|g[O>>>8&255]<<8|g[255&T])^u[S++],b=(g[M>>>24]<<24|g[O>>>16&255]<<16|g[T>>>8&255]<<8|g[255&N])^u[S++],k=(g[O>>>24]<<24|g[T>>>16&255]<<16|g[N>>>8&255]<<8|g[255&M])^u[S++];t[a]=C,t[a+1]=A,t[a+2]=b,t[a+3]=k},keySize:8});pp.AES=hp._createHelper(yp);var Cp,Ap=rp.AES;rp.mode.ECB=((Cp=rp.lib.BlockCipherMode.extend()).Encryptor=Cp.extend({processBlock:function processBlock(t,a){this._cipher.encryptBlock(t,a)}}),Cp.Decryptor=Cp.extend({processBlock:function processBlock(t,a){this._cipher.decryptBlock(t,a)}}),Cp);var bp=rp.mode.ECB,kp=rp.enc.Utf8,Lp=rp.pad.Pkcs7;var Dp={enabled:!1,cdnUrls:[],timestamp:Ga(),pollingIntervalSeconds:5,pollingTimeoutMillis:1e3},Vp=invertSerializeItem(Z_),wp=function(){function CDNUtil(t,a){this.config=Dp,this.lastSuccessTimestamp=0,this.pollingTimer=0,this.msgBufferInterval=300,this.emitTimer=0,this.core=t,this.service=a,this.promiseManager=new Cu}var t=CDNUtil.prototype;return t.reset=function reset(){this.config=Dp,this.lastSuccessTimestamp=0,this.pollingTimer&&clearInterval(this.pollingTimer),this.pollingTimer=0,this.emitTimer&&clearInterval(this.emitTimer),this.emitTimer=0},t.setOptions=function setOptions(t){this.config=ba({},this.config,t),this.core.logger.log("CDNUtil::setOptions",this.config),this.polling()},t.polling=function polling(){var t;(this.pollingTimer&&clearInterval(this.pollingTimer),this.pollingTimer=0,this.config.enabled)&&(this.pollingTimer=tp(bind$1(t=this.fetchMsgs).call(t,this),1e3*this.config.pollingIntervalSeconds))},t.fetchMsgs=function fetchMsgs(t){return void 0===t&&(t=0),__awaiter(this,void 0,void 0,Sa.mark((function _callee(){var a,u,_,h,E,m;return Sa.wrap((function _callee$(g){for(;;)switch(g.prev=g.next){case 0:if((a=this.config.cdnUrls)&&a.length>0){g.next=3;break}return g.abrupt("return");case 3:if(u=a.shift()){g.next=6;break}return g.abrupt("return");case 6:if(this.config.cdnUrls.push(u),_=this.core.timeOrigin.getNTPTime(),_-=_%(1e3*this.config.pollingIntervalSeconds),this.lastSuccessTimestamp!==_){g.next=11;break}return g.abrupt("return");case 11:return u=u.replace("#time",""+_),h=this.config.pollingTimeoutMillis||1e3*this.config.pollingIntervalSeconds/2,this.core.logger.log("CDNUtil::fetchMsgs start:",u),E={},g.prev=15,g.next=18,this.promiseManager.add(this.core.adapters.request(u,{method:"GET",dataType:"json",timeout:h},{exception_service:8}));case 18:E=g.sent,g.next=31;break;case 21:if(g.prev=21,g.t0=g.catch(15),this.core.logger.warn("CDNUtil::fetchMsgs failed:",g.t0),(m=g.t0).code!==Ud.V2NIM_ERROR_CODE_CANCELLED){g.next=27;break}return g.abrupt("return");case 27:if(!(t>=3)){g.next=29;break}return g.abrupt("return");case 29:return 404===m.code&&this.core.timeOrigin.setOriginTimetick(),g.abrupt("return",this.fetchMsgs(t+1));case 31:this.requestSuccess(E,_);case 32:case"end":return g.stop()}}),_callee,this,[[15,21]])})))},t.requestSuccess=function requestSuccess(t,a){var u=this;if(this.lastSuccessTimestamp>a)return this.core.logger.warn("CDNUtil::fetchMsgs:ignore",this.lastSuccessTimestamp,a),void(this.lastSuccessTimestamp=0);this.lastSuccessTimestamp=a;var _=get(t,"data.data");if(_){if(t.data.ptm&&(this.config.pollingTimeoutMillis=t.data.ptm),t.data.pis){var h=this.config.pollingIntervalSeconds;this.config.pollingIntervalSeconds=t.data.pis,t.data.pis!==h&&this.polling()}var E=!0===t.data.e?this.decryptAES(_,this.config.decryptKey):_,m=this.formatMessages(E);m=function uniqBy(t,a){t=t||[],a=a||"";for(var u=[],_=[],h=0;h<t.length;h++){var E=t[h][a];-1===indexOf(_).call(_,E)&&(_.push(E),u.push(t[h]))}return u}(m,"messageClientId"),m=filter(m).call(m,(function(t){return!u.service.sendUtil.checkIfResend(t)&&(u.service.sendUtil.cacheMsg(t),!0)})),this.core.logger.log("CDNUtil::fetchMsgs success at "+a+", msg.length: "+m.length),this.emitSmoothly(m,1e3*t.data.c)}},t.decryptAES=function decryptAES(t,a){if(!t||!a)return"[]";try{return Ap.decrypt(t,_p.parse(a),{mode:bp,padding:Lp}).toString(kp)}catch(t){var u=t;throw new vl({code:Ud.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"Decrypt AES failed",rawError:u}})}},t.formatMessages=function formatMessages(t){var a=this,u=[];try{u=JSON.parse(t)}catch(a){var _=a;throw new vl({code:Ud.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"JSON parse error",rawData:t,rawError:_}})}return map$7(u).call(u,(function(t){return formatMessage(a.core,deserialize(t,Vp),a.core.account)}))},t.emitSmoothly=function emitSmoothly(t,a){var u=this;if(t&&t.length>0){var _=Math.ceil(a/this.msgBufferInterval),h=Math.ceil(t.length*this.msgBufferInterval/a);this.core.logger.log("CDNUtil::emitSmoothly total length: "+t.length+", group length: "+h+", times: "+_),this.emitTimer&&clearInterval(this.emitTimer),this.emitTimer=0,this.emitTimer=tp((function(){var a=splice(t).call(t,0,h);if(0===a.length)return u.emitTimer&&clearInterval(u.emitTimer),void(u.emitTimer=0);u.core.V2NIMChatroomService.emit("onReceiveMessages",a)}),this.msgBufferInterval)}},CDNUtil}(),Pp=function(t){function V2NIMChatroomMessageServiceImpl(a){var u;return(u=t.call(this,"V2NIMChatroomMessageService",a)||this).customAttachmentParsers=[],registerParser({cmdMap:Q_,cmdConfig:ep}),u.fileUtil=new F_(u.core,$e(u)),u.sendUtil=new B_(u.core,$e(u)),u.cdnUtil=new wp(u.core,$e(u)),u.setListener(),u}At(V2NIMChatroomMessageServiceImpl,t);var a=V2NIMChatroomMessageServiceImpl.prototype;return a.reset=function reset(t){this.sendUtil.reset(),this.cdnUtil.reset(),this.core.V2NIMClientAntispamUtil.reset(t)},a.setListener=function setListener(){var t=this;this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLoginSucc",(function(){t.core.timeOrigin.setOriginTimetick()})),this.core.eventBus.on("V2NIMChatroomMessageService/onReceiveNotification",(function(a,u){16===get(a,"attachment.type")&&t.core.V2NIMChatroomService.emit("onMessageRevokedNotification",u.data.msgId,u.data.msgTime)}))},a.getMessageList=function getMessageList(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var a,u,_=this;return Sa.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return validate(z_,{option:t},"",!0),h.next=3,this.core.clientSocket.sendCmd("v2ChatroomGetMessageList",ba(ba({beginTime:0,limit:100},t),{reverse:1===t.direction}));case 3:return a=h.sent,u=a.content.datas,map$7(u).call(u,(function(t){return formatMessage(_.core,t,_.core.account)})),h.abrupt("return",u);case 7:case"end":return h.stop()}}),_callee,this)})))},a.getMessageListByTag=function getMessageListByTag(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var a,u,_=this;return Sa.wrap((function _callee2$(h){for(;;)switch(h.prev=h.next){case 0:if(validate($_,{messageOption:t},"",!0),!("number"==typeof(null==t?void 0:t.beginTime)&&"number"==typeof(null==t?void 0:t.endTime)&&t.beginTime>t.endTime)){h.next=3;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"beginTime must be less than endTime"}});case 3:return h.next=5,this.core.clientSocket.sendCmd("v2ChatroomGetMessageListByTag",{tag:ba(ba({},t),{tags:Ms(t.tags),messageType:t.messageTypes?Ms(t.messageTypes):void 0,reverse:1===t.direction?0:1})});case 5:return a=h.sent,u=a.content.datas,map$7(u).call(u,(function(t){return formatMessage(_.core,t,_.core.account)})),h.abrupt("return",u);case 9:case"end":return h.stop()}}),_callee2,this)})))},a.sendMessage=function sendMessage(t,a,u){return void 0===a&&(a={}),__awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var _,h,E,m,g;return Sa.wrap((function _callee3$(I){for(;;)switch(I.prev=I.next){case 0:if(validate(H_,{message:t},"",!0),validate(Y_,{params:a},"",!0),t.senderId===this.core.account){I.next=4;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_MISUSE,detail:{reason:"message.senderId must be self"}});case 4:return _=this.core.timeOrigin.getTimeNode(),h=this.sendUtil.prepareMessage(t,a),E=h.messageBeforeSend,m=h.clientAntispamResult,I.next=8,this.sendUtil.doSendMessage(_,E,m,u);case 8:return g=I.sent,I.abrupt("return",g);case 10:case"end":return I.stop()}}),_callee3,this)})))},a.registerCustomAttachmentParser=function registerCustomAttachmentParser(t){var a;"function"==typeof t&&-1===indexOf(a=this.customAttachmentParsers).call(a,t)&&this.customAttachmentParsers.unshift(t)},a.unregisterCustomAttachmentParser=function unregisterCustomAttachmentParser(t){var a,u,_=indexOf(a=this.customAttachmentParsers).call(a,t);_>-1&&splice(u=this.customAttachmentParsers).call(u,_,1)},a.v2ChatroomMessageAck=function v2ChatroomMessageAck(t,a){this.core.clientSocket.sendCmd("v2ChatroomMessageAck",{tag:{messageClientId:t,roomId:a}})},a.v2ChatroomOnMessageHandler=function v2ChatroomOnMessageHandler(t){var a=t.content.data,u=a.attachment,_=formatMessage(this.core,a,this.core.account);this.sendUtil.checkIfResend(_)||(5===a.messageType?this.core.eventBus.emit("V2NIMChatroomMessageService/onReceiveNotification",_,u):this.sendUtil.cacheMsg(_),this.sendUtil.doMsgReceiveReport(_,t),delete _.__clientExt,this.core.V2NIMChatroomService.emit("onReceiveMessages",[_]))},a.v2ChatroomUpdateCDNInfoHandler=function v2ChatroomUpdateCDNInfoHandler(t){var a=t.content.chatroomCdnInfo;this.cdnUtil.setOptions(a)},V2NIMChatroomMessageServiceImpl}(__),xp=function(){function V2NIMChatroomMessageCreatorImpl(t){this.name="V2NIMChatroomMessageCreator",this.defaultNosSceneName="nim_default_im",this.core=t}var t=V2NIMChatroomMessageCreatorImpl.prototype;return t.createMessage=function createMessage(t,a){return ba(ba(ba({messageClientId:$u(),createTime:this.core.timeOrigin.getNTPTime(),senderId:this.core.auth.getLoginUser(),roomId:this.core.auth.getRoomId(),isSelf:!0,sendingState:0,messageType:t,senderClientType:16},a),a.attachment?{attachment:ba(ba({},a.attachment),{raw:attachmentToRaw(t,a.attachment)})}:{}),{messageConfig:ba({historyEnabled:!0,highPriority:!1},a.messageConfig),routeConfig:ba({routeEnabled:!0},a.routeConfig),antispamConfig:ba({antispamEnabled:!0},a.antispamConfig)})},t.createTextMessage=function createTextMessage(t){return validate({text:{type:"string",allowEmpty:!1}},{text:t},"",!0),this.createMessage(0,{text:t})},t.createImageMessage=function createImageMessage(t,a,u,_,h){validate(W_,{name:a,sceneName:u,width:_,height:h},"",!0);var E=this.createGenericFileMessageAttachment(t,a,u,void 0,_,h,"jpeg");return this.createMessage(1,{attachment:E,attachmentUploadState:0})},t.createAudioMessage=function createAudioMessage(t,a,u,_){validate(K_,{name:a,sceneName:u,duration:_},"",!0);var h=this.createGenericFileMessageAttachment(t,a,u,_,void 0,void 0,"aac");return this.createMessage(2,{attachment:h,attachmentUploadState:0})},t.createVideoMessage=function createVideoMessage(t,a,u,_,h,E){validate(q_,{name:a,sceneName:u,duration:_,width:h,height:E},"",!0);var m=this.createGenericFileMessageAttachment(t,a,u,_,h,E,"mp4");return this.createMessage(3,{attachment:m,attachmentUploadState:0})},t.createFileMessage=function createFileMessage(t,a,u){validate(j_,{name:a,sceneName:u},"",!0);var _=this.createGenericFileMessageAttachment(t,a,u,void 0,void 0,void 0,"txt");return this.createMessage(6,{attachment:_,attachmentUploadState:0})},t.createGenericFileMessageAttachment=function createGenericFileMessageAttachment(t,a,u,_,h,E,m){if(u=u||this.defaultNosSceneName,!this.core.V2NIMStorageService.hasStorageScene(u))throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"sceneName: "+u+" has not been added"}});var g=getFileOrPath(t),I=g.file,T=g.path,N=ba(ba(ba({name:a,uploadState:0,sceneName:u||this.defaultNosSceneName},_?{duration:_}:{}),h?{width:h}:{}),E?{height:E}:{});if(I){var M,O=lastIndexOf(M=I.name).call(M,"."),S=-1===O?I.name:I.name.substring(0,O);N.name=N.name||S,N.size=I.size,N.ext="."+(getFileExtension(I.name)||getFileExtension(a||"")||m)}else if(T){var R=lastIndexOf(T).call(T,"/"),C=lastIndexOf(T).call(T,"."),A=-1===C?T.substring(R+1):T.substring(R+1,C);N.name=N.name||A,N.ext="."+(getFileExtension(T)||getFileExtension(a||"")||m)}return N=JSON.parse(Ms(N)),T?N.path=T:I&&(N.file=I),N},t.createLocationMessage=function createLocationMessage(t,a,u){return validate({latitude:{type:"number",allowEmpty:!1},longitude:{type:"number",allowEmpty:!1},address:{type:"string",allowEmpty:!1}},{latitude:t,longitude:a,address:u},"",!0),this.createMessage(4,{attachment:{latitude:t,longitude:a,address:u}})},t.createCustomMessage=function createCustomMessage(t){return validate({rawAttachment:{type:"string"}},{rawAttachment:t},"",!0),this.createMessage(100,{attachment:{raw:t}})},t.createCustomMessageWithAttachment=function createCustomMessageWithAttachment(t,a){return validate({raw:{type:"string"}},t,"attachment",!0),validate({subType:{type:"number",min:0,required:!1}},{subType:a},"",!0),this.createMessage(100,a?{attachment:t,subType:a}:{attachment:t})},t.createForwardMessage=function createForwardMessage(t){var a=[11,5,7,10];if(includes(a).call(a,t.messageType))return null;var u={messageClientId:$u(),messageType:t.messageType};return t.text&&(u.text=t.text),t.attachment&&(u.attachment=t.attachment),t.attachment&&"uploadState"in t.attachment&&(u.attachmentUploadState=t.attachment.uploadState),this.createMessage(t.messageType,u)},t.createTipsMessage=function createTipsMessage(t){return validate({text:{type:"string",allowEmpty:!1}},{text:t},"",!0),this.createMessage(10,{text:t})},V2NIMChatroomMessageCreatorImpl}(),Up={option:{type:"object",rules:{memberRoles:{type:"array",itemType:"enum",values:[4,1,2,0,3,5],required:!1},onlyBlocked:{type:"boolean",required:!1},onlyChatBanned:{type:"boolean",required:!1},onlyOnline:{type:"boolean",required:!1},pageToken:{type:"string",required:!1},limit:{type:"number",min:1,required:!1}}}},Fp={accountId:{type:"string",allowEmpty:!1},updateParams:{type:"object",rules:{memberRole:{type:"enum",values:[2,0,3]},memberLevel:{type:"number",min:0,required:!1},notificationExtension:{type:"string",required:!1}}}},Bp={accountId:{type:"string",allowEmpty:!1},blocked:{type:"boolean"},notificationExtension:{type:"string",required:!1}},Gp={accountId:{type:"string",allowEmpty:!1},chatBanned:{type:"boolean"},notificationExtension:{type:"string",required:!1}},Hp={updateParams:{type:"object",rules:{roomNick:{type:"string",allowEmpty:!1,required:!1},roomAvatar:{type:"string",required:!1},serverExtension:{type:"string",required:!1},notificationEnabled:{type:"boolean",required:!1},notificationExtension:{type:"string",required:!1},persistence:{type:"boolean",required:!1}}},antispamConfig:{type:"object",required:!1,rules:{antispamBusinessId:{type:"string",required:!1}}}},Yp={accountId:{type:"string",allowEmpty:!1},tempChatBannedDuration:{type:"number",min:0},notificationEnabled:{type:"boolean"},notificationExtension:{type:"string",required:!1}},jp={option:{type:"object",rules:{tag:{type:"string",allowEmpty:!1},pageToken:{type:"string",required:!1},limit:{type:"number",min:1,required:!1}}}},Kp=function(t){function V2NIMChatroomMemberServiceImpl(a){var u;return u=t.call(this,"V2NIMChatroomMemberService",a)||this,registerParser({cmdMap:yd,cmdConfig:bd}),u}At(V2NIMChatroomMemberServiceImpl,t);var a=V2NIMChatroomMemberServiceImpl.prototype;return a.getMemberListByOption=function getMemberListByOption(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var a;return Sa.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return validate(Up,{option:t},"",!0),u.next=3,this.core.clientSocket.sendCmd("v2ChatroomGetMemberListByOption",{tag:t});case 3:return a=u.sent,u.abrupt("return",{finished:!a.content.hasMore,pageToken:a.content.pageToken,memberList:a.content.datas});case 5:case"end":return u.stop()}}),_callee,this)})))},a.updateMemberRole=function updateMemberRole(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){return Sa.wrap((function _callee2$(u){for(;;)switch(u.prev=u.next){case 0:return validate(Fp,{accountId:t,updateParams:a},"",!0),u.next=3,this.core.sendCmd("v2ChatroomUpdateMemberRole",{tag:ba(ba({notificationExtension:""},a),{accountId:t})});case 3:case"end":return u.stop()}}),_callee2,this)})))},a.setMemberBlockedStatus=function setMemberBlockedStatus(t,a,u){return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var _;return Sa.wrap((function _callee3$(h){for(;;)switch(h.prev=h.next){case 0:return validate(Bp,_={accountId:t,blocked:a,notificationExtension:u},"",!0),h.next=4,this.core.sendCmd("v2ChatroomSetMemberBlockedStatus",_);case 4:case"end":return h.stop()}}),_callee3,this)})))},a.setMemberChatBannedStatus=function setMemberChatBannedStatus(t,a,u){return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){var _;return Sa.wrap((function _callee4$(h){for(;;)switch(h.prev=h.next){case 0:return validate(Gp,_={accountId:t,chatBanned:a,notificationExtension:u},"",!0),h.next=4,this.core.sendCmd("v2ChatroomSetMemberChatBannedStatus",_);case 4:case"end":return h.stop()}}),_callee4,this)})))},a.setMemberTempChatBanned=function setMemberTempChatBanned(t,a,u,_){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){var h;return Sa.wrap((function _callee5$(E){for(;;)switch(E.prev=E.next){case 0:return validate(Yp,h={accountId:t,tempChatBannedDuration:a,notificationEnabled:u,notificationExtension:_},"",!0),h.notificationExtension=h.notificationExtension||"",E.next=5,this.core.sendCmd("v2ChatroomSetMemberTempChatBanned",h);case 5:case"end":return E.stop()}}),_callee5,this)})))},a.updateSelfMemberInfo=function updateSelfMemberInfo(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee6(){return Sa.wrap((function _callee6$(u){for(;;)switch(u.prev=u.next){case 0:if(validate(Hp,{updateParams:t,antispamConfig:a},"",!0),void 0!==t.roomAvatar||void 0!==t.roomNick||void 0!==t.serverExtension){u.next=3;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"updateSelfMemberInfo: nothing to update"}});case 3:return u.next=5,this.core.clientSocket.sendCmd("v2ChatroomUpdateSelfMemberInfo",{tag:t,notificationEnabled:"boolean"!=typeof t.notificationEnabled||t.notificationEnabled,notificationExtension:t.notificationExtension||"",persistence:"boolean"==typeof t.persistence&&t.persistence,antispamConfig:a});case 5:case"end":return u.stop()}}),_callee6,this)})))},a.getMemberByIds=function getMemberByIds(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee7(){var a;return Sa.wrap((function _callee7$(u){for(;;)switch(u.prev=u.next){case 0:return validate({accountIds:{type:"array",itemType:"string",min:1}},{accountIds:t},"",!0),u.next=3,this.core.clientSocket.sendCmd("v2ChatroomGetMemberByIds",{accountIds:t});case 3:return a=u.sent,u.abrupt("return",a.content.datas);case 5:case"end":return u.stop()}}),_callee7,this)})))},a.kickMember=function kickMember(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee8(){return Sa.wrap((function _callee8$(u){for(;;)switch(u.prev=u.next){case 0:return validate({accountId:{type:"string",allowEmpty:!1}},{accountId:t},"",!0),validate({notificationExtension:{type:"string",required:!1}},{notificationExtension:a},"",!0),u.next=4,this.core.clientSocket.sendCmd("v2ChatroomKickMember",{accountId:t,notificationExtension:a});case 4:case"end":return u.stop()}}),_callee8,this)})))},a.getMemberListByTag=function getMemberListByTag(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee9(){var a;return Sa.wrap((function _callee9$(u){for(;;)switch(u.prev=u.next){case 0:return validate(jp,{option:t},"",!0),u.next=3,this.core.clientSocket.sendCmd("v2ChatroomGetMemberListByTag",{tag:ba({limit:100},t)});case 3:return a=u.sent,u.abrupt("return",{finished:!a.content.hasMore,pageToken:a.content.pageToken,memberList:a.content.datas});case 5:case"end":return u.stop()}}),_callee9,this)})))},a.getMemberCountByTag=function getMemberCountByTag(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee10(){var a;return Sa.wrap((function _callee10$(u){for(;;)switch(u.prev=u.next){case 0:return validate({tag:{type:"string",allowEmpty:!1}},{tag:t},"",!0),u.next=3,this.core.clientSocket.sendCmd("v2ChatroomGetMemberCountByTag",{tag:t});case 3:return a=u.sent,u.abrupt("return",a.content.data);case 5:case"end":return u.stop()}}),_callee10,this)})))},a.v2ChatroomOnMemberTagUpdatedHandler=function v2ChatroomOnMemberTagUpdatedHandler(t){var a;if(null===(a=t.content.data)||void 0===a?void 0:a.tag){var u=JSON.parse(t.content.data.tag);this.core.V2NIMChatroomService.emit("onChatroomTagsUpdated",u)}},V2NIMChatroomMemberServiceImpl}(__),qp=function(){function V2NIMChatroomServiceEventImpl(t,a){this.core=t,this.service=a,this.logger=this.core.logger}var t=V2NIMChatroomServiceEventImpl.prototype;return t.setListener=function setListener(){var t;this.core.eventBus.on("V2NIMChatroomMessageService/onReceiveNotification",bind$1(t=this.onReceiveNotification).call(t,this))},t.onReceiveNotification=function onReceiveNotification(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var u,_,h,E,m,g,I,T,N,M,O,S;return Sa.wrap((function _callee$(R){for(;;)switch(R.prev=R.next){case 0:u=t.attachment,_=get(t,"attachment.type"),R.t0=_,R.next=0===R.t0?5:7===R.t0?9:1===R.t0?11:3===R.t0?14:4===R.t0?15:5===R.t0?17:18===R.t0?19:8===R.t0?21:14===R.t0?23:9===R.t0?25:15===R.t0?27:12===R.t0?29:13===R.t0?32:17===R.t0?35:10===R.t0?38:42;break;case 5:return(h=t.attachment.currentMember)&&this.service.emit("onChatroomMemberEnter",h),u.operatorId!==this.core.account&&this.core.V2NIMChatroomInfoService.model.chatroomInfo&&this.core.V2NIMChatroomInfoService.model.chatroomInfo.onlineUserCount++,R.abrupt("break",42);case 9:if(fs(u.targetIds))for(E=0;E<u.targetIds.length;E++)this.service.emit("onChatroomMemberExit",u.targetIds[E]);return R.abrupt("break",42);case 11:return this.service.emit("onChatroomMemberExit",u.operatorId),u.operatorId!==this.core.account&&this.core.V2NIMChatroomInfoService.model.chatroomInfo&&this.core.V2NIMChatroomInfoService.model.chatroomInfo.onlineUserCount--,R.abrupt("break",42);case 14:return R.abrupt("break",42);case 15:return u.targetIds&&includes(m=u.targetIds).call(m,this.core.account)&&this.service.emit("onSelfChatBannedUpdated",!0),R.abrupt("break",42);case 17:return u.targetIds&&includes(g=u.targetIds).call(g,this.core.account)&&this.service.emit("onSelfChatBannedUpdated",!1),R.abrupt("break",42);case 19:return"currentMember"in u&&"previousRole"in u&&this.service.emit("onChatroomMemberRoleUpdated",u.previousRole,u.currentMember),R.abrupt("break",42);case 21:return includes(I=t.attachment.targetIds).call(I,this.core.account)&&(T=a.data.muteDuration,this.service.emit("onSelfTempChatBannedUpdated",!0,T)),R.abrupt("break",42);case 23:return includes(N=this.core.tags).call(N,a.data.targetTag)&&(M=a.data.muteDuration,this.service.emit("onSelfTempChatBannedUpdated",!0,M)),R.abrupt("break",42);case 25:return includes(O=t.attachment.targetIds).call(O,this.core.account)&&this.service.emit("onSelfTempChatBannedUpdated",!1,0),R.abrupt("break",42);case 27:return this.service.emit("onSelfTempChatBannedUpdated",!1,0),R.abrupt("break",42);case 29:return this.service.emit("onChatroomChatBannedUpdated",!0),this.core.V2NIMChatroomInfoService.model.chatroomInfo&&(this.core.V2NIMChatroomInfoService.model.chatroomInfo.chatBanned=!0),R.abrupt("break",42);case 32:return this.service.emit("onChatroomChatBannedUpdated",!1),this.core.V2NIMChatroomInfoService.model.chatroomInfo&&(this.core.V2NIMChatroomInfoService.model.chatroomInfo.chatBanned=!1),R.abrupt("break",42);case 35:return this.core.options.tags=a.data.tags||[],this.service.emit("onChatroomTagsUpdated",a.data.tags),R.abrupt("break",42);case 38:return(S=t.attachment.currentMember).memberLevel=S.memberLevel||0,this.service.emit("onChatroomMemberInfoUpdated",S),R.abrupt("break",42);case 42:case"end":return R.stop()}}),_callee,this)})))},t.beforeEmit=function beforeEmit(t){for(var a=this.service.name+"::emit "+t.toString(),u=arguments.length,_=new Array(u>1?u-1:0),h=1;h<u;h++)_[h-1]=arguments[h];if("onSendMessage"===t){var E=_[0];this.logger.log(""+a,E.messageClientId+";createTime:"+E.createTime+";","sendingState:"+E.sendingState+";attachmentUploadState:"+(E.attachmentUploadState||0))}else if("onReceiveMessages"===t){var m=_[0];this.logger.log(""+a,map$7(m).call(m,(function(t){return t.messageClientId+";createTime:"+t.createTime})))}else if("onChatroomMemberEnter"===t||"onChatroomMemberInfoUpdated"===t){var g=_[0];this.logger.log(""+a,"accountId:"+g.accountId)}else if("onChatroomMemberRoleUpdated"===t){var I=_[1];this.logger.log(""+a,_[0],"accountId:"+I.accountId+";memberRole:"+I.memberRole)}else{var T,N;(T=this.logger).log.apply(T,concat(N=[""+a]).call(N,_))}},V2NIMChatroomServiceEventImpl}(),Wp=function(t){function V2NIMChatroomServiceImpl(a){var u;return(u=t.call(this,"V2NIMChatroomService",a)||this).event=new qp(a,$e(u)),u.setListener(),u}At(V2NIMChatroomServiceImpl,t);var a=V2NIMChatroomServiceImpl.prototype;return a.setListener=function setListener(){this.event.setListener()},a.emit=function emit(a){for(var u,_,h,E,m=arguments.length,g=new Array(m>1?m-1:0),I=1;I<m;I++)g[I-1]=arguments[I];return(u=this.event).beforeEmit.apply(u,concat(_=[a]).call(_,g)),(h=t.prototype.emit).call.apply(h,concat(E=[this,a]).call(E,g))},a.sendMessage=function sendMessage(t,a,u){return this.core.V2NIMChatroomMessageService.sendMessage(t,a,u)},a.cancelMessageAttachmentUpload=function cancelMessageAttachmentUpload(t){return this.core.V2NIMChatroomMessageService.fileUtil.cancelMessageAttachmentUpload(t)},a.registerCustomAttachmentParser=function registerCustomAttachmentParser(t){this.core.V2NIMChatroomMessageService.registerCustomAttachmentParser(t)},a.unregisterCustomAttachmentParser=function unregisterCustomAttachmentParser(t){this.core.V2NIMChatroomMessageService.unregisterCustomAttachmentParser(t)},a.getMessageList=function getMessageList(t){return this.core.V2NIMChatroomMessageService.getMessageList(t)},a.getMessageListByTag=function getMessageListByTag(t){return this.core.V2NIMChatroomMessageService.getMessageListByTag(t)},a.getMemberListByOption=function getMemberListByOption(t){return this.core.V2NIMChatroomMemberService.getMemberListByOption(t)},a.updateMemberRole=function updateMemberRole(t,a){return this.core.V2NIMChatroomMemberService.updateMemberRole(t,a)},a.setMemberBlockedStatus=function setMemberBlockedStatus(t,a,u){return this.core.V2NIMChatroomMemberService.setMemberBlockedStatus(t,a,u)},a.setMemberChatBannedStatus=function setMemberChatBannedStatus(t,a,u){return this.core.V2NIMChatroomMemberService.setMemberChatBannedStatus(t,a,u)},a.setMemberTempChatBanned=function setMemberTempChatBanned(t,a,u,_){return this.core.V2NIMChatroomMemberService.setMemberTempChatBanned(t,a,u,_)},a.updateSelfMemberInfo=function updateSelfMemberInfo(t,a){return this.core.V2NIMChatroomMemberService.updateSelfMemberInfo(t,a)},a.getMemberByIds=function getMemberByIds(t){return this.core.V2NIMChatroomMemberService.getMemberByIds(t)},a.kickMember=function kickMember(t,a){return this.core.V2NIMChatroomMemberService.kickMember(t,a)},a.getMemberListByTag=function getMemberListByTag(t){return this.core.V2NIMChatroomMemberService.getMemberListByTag(t)},a.getMemberCountByTag=function getMemberCountByTag(t){return this.core.V2NIMChatroomMemberService.getMemberCountByTag(t)},a.getChatroomInfo=function getChatroomInfo(){return this.core.V2NIMChatroomInfoService.getChatroomInfo()},a.updateChatroomInfo=function updateChatroomInfo(t,a){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){return Sa.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return u.abrupt("return",this.core.V2NIMChatroomInfoService.updateChatroomInfo(t,a));case 1:case"end":return u.stop()}}),_callee,this)})))},a.updateChatroomLocationInfo=function updateChatroomLocationInfo(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){return Sa.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",this.core.V2NIMChatroomInfoService.updateChatroomLocationInfo(t));case 1:case"end":return a.stop()}}),_callee2,this)})))},a.updateChatroomTags=function updateChatroomTags(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){return Sa.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",this.core.V2NIMChatroomInfoService.updateChatroomTags(t));case 1:case"end":return a.stop()}}),_callee3,this)})))},a.setTempChatBannedByTag=function setTempChatBannedByTag(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){return Sa.wrap((function _callee4$(a){for(;;)switch(a.prev=a.next){case 0:return a.abrupt("return",this.core.V2NIMChatroomInfoService.setTempChatBannedByTag(t));case 1:case"end":return a.stop()}}),_callee4,this)})))},V2NIMChatroomServiceImpl}(o_),zp=entryVirtual("Array").keys,$p=Array.prototype,Xp={DOMTokenList:!0,NodeList:!0},keys=function(t){var a=t.keys;return t===$p||j($p,t)&&a===$p.keys||ue(Xp,xt(t))?zp:a},Qp=function(){function V2NIMClientAntispamUtilImpl(t,a){this.config={enable:!1},this.core=t,a&&this.setOptions(a)}var t=V2NIMClientAntispamUtilImpl.prototype;return t.setOptions=function setOptions(t){this.config=ba(this.config,t)},t.reset=function reset(t){"destroy"===t&&(this.vocabInfo=void 0)},t.downloadLocalAntiSpamVocabs=function downloadLocalAntiSpamVocabs(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var t;return Sa.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.config.enable){a.next=2;break}return a.abrupt("return");case 2:if(!this.vocabInfo){a.next=4;break}return a.abrupt("return");case 4:return a.prev=4,a.next=7,this.core.sendCmd("v2ChatroomDownloadLocalAntiSpamVocabs",{tag:{version:0,md5:""}});case 7:t=a.sent,this.vocabInfo=ba(ba({},t.content.data),{thesaurus:JSON.parse(t.content.data.thesaurus).thesaurus}),a.next=14;break;case 11:a.prev=11,a.t0=a.catch(4),this.core.logger.warn("V2NIMLocalAntispamUtil::downloadLocalAntiSpamVocabs error",a.t0);case 14:case"end":return a.stop()}}),_callee,this,[[4,11]])})))},t.checkTextAntispam=function checkTextAntispam(t,a){if(void 0===a&&(a="**"),!this.config.enable)return{operateType:0,replacedText:t};if(validate({text:{type:"string",required:!0,allowEmpty:!1},replace:{type:"string"}},{text:t,replace:a},"",!0),!this.vocabInfo)return{operateType:0,replacedText:t};for(var u=t,_=0;_<this.vocabInfo.thesaurus.length;_++){var h=this.filterContent(u,this.vocabInfo.thesaurus[_],a);if(u=h.replacedText,2===h.operateType||3===h.operateType)return h}return{operateType:u===t?0:1,replacedText:u}},t.filterContent=function filterContent(t,a,u){for(var _=0;_<keys(a).length;_++){var h=keys(a)[_],E=h.match||a.match,m=h.operate||a.operate,g=void 0;try{g=this.matchContent(t,h.key,E,m,u)}catch(t){}if(g&&(t=g.replacedText,2===g.operateType||3===g.operateType))return g}return{operateType:1,replacedText:t}},t.matchContent=function matchContent(t,a,u,_,h){var E=!1,m=null;if(1===u){if(indexOf(t).call(t,a)>=0){E=!0;var g=a.replace(/[.*+?^${}()|[\]\\]/g,"\\$&");m=new RegExp(g,"g")}}else 2===u&&(m=new RegExp(a,"g")).test(t)&&(E=!0);if(E&&m)switch(_){case 1:return{operateType:1,replacedText:t.replace(m,h)};case 2:return{operateType:2,replacedText:t};case 3:return{operateType:3,replacedText:t}}return{operateType:0,replacedText:t}},V2NIMClientAntispamUtilImpl}(),Jp={initParams:{type:"object",rules:{appkey:{type:"string",allowEmpty:!1},customClientType:{type:"number",required:!1},isFixedDeviceId:{type:"boolean",required:!1},debugLevel:{type:"enum",values:["off","error","warn","log","debug"],required:!1}}},otherParams:{type:"object",rules:{cloudStorageConfig:{type:"object",required:!1,rules:{commonUploadHost:{type:"string",required:!1},commonUploadHostBackupList:{type:"array",required:!1,itemType:"string"},chunkUploadHost:{type:"string",required:!1},uploadReplaceFormat:{type:"string",required:!1},downloadUrl:{type:"string",required:!1},downloadHostList:{type:"array",required:!1},nosCdnEnable:{type:"boolean",required:!1},storageKeyPrefix:{type:"string",required:!1},isNeedToGetUploadPolicyFromServer:{type:"boolean",required:!1},cdn:{type:"object",required:!1,allowEmpty:!1,rules:{defaultCdnDomain:{type:"string",required:!1},cdnDomain:{type:"string",required:!1},bucket:{type:"string",required:!1},objectNamePrefix:{type:"string",required:!1}}}}},reporterConfig:{type:"object",allowEmpty:!1,required:!1,rules:{enableCompass:{type:"boolean",required:!1},compassDataEndpoint:{type:"string",required:!1},isDataReportEnable:{type:"boolean",required:!1}}},abtestConfig:{type:"object",allowEmpty:!1,required:!1,rules:{isAbtestEnable:{type:"boolean",required:!1},abtestUrl:{type:"string",required:!1}}}}}},Zp={user_id:"",trace_id:"",action:0,state:0,duration:0,start_time:0,offset:0,full_size:0,transferred_size:0,operation_type:0,remote_addr:""},eh="ReporterHook::setMonitorForResources:",th=function(){function ReporterHookCloudStorage(t,a){this.traceData=Zp,this.core=t,this.traceData=ba({},Zp,a)}var t=ReporterHookCloudStorage.prototype;return t.reset=function reset(){this.traceData=ba({},Zp)},t.start=function start(){var t,a;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(a=null===(t=this.core.clientSocket)||void 0===t?void 0:t.socket)||void 0===a?void 0:a.sessionId)||"",this.traceData.start_time="timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Ga()},t.update=function update(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){return Sa.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.traceData.user_id){a.next=2;break}return a.abrupt("return");case 2:this.core.logger.log(eh+" upload update",t),ba(this.traceData,t);case 4:case"end":return a.stop()}}),_callee,this)})))},t.end=function end(t){this.traceData.user_id&&(this.core.logger.log(eh+" upload end cause of "+t),this.traceData.state=t,this.traceData.duration=("timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Ga())-this.traceData.start_time,this.core.reporter.report("nim_sdk_resources",this.traceData),this.traceData=Zp)},ReporterHookCloudStorage}();function getIsDataReportEnable(t){var a,u,_=!0;return"boolean"==typeof(null===(a=null==t?void 0:t.reporterConfig)||void 0===a?void 0:a.enableCompass)?_=t.reporterConfig.enableCompass:"boolean"==typeof(null===(u=null==t?void 0:t.reporterConfig)||void 0===u?void 0:u.isDataReportEnable)&&(_=t.reporterConfig.isDataReportEnable),_}var rh="V2NIMChatroomQueueService",nh={"36_20":"v2ChatroomQueueOffer","36_21":"v2ChatroomQueuePoll","36_22":"v2ChatroomQueueList","36_23":"v2ChatroomQueuePeek","36_24":"v2ChatroomQueueDrop","36_25":"v2ChatroomQueueInit","36_26":"v2ChatroomQueueBatchUpdate"},oh={v2ChatroomQueueOffer:{sid:36,cid:20,service:rh,params:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"},{type:"Bool",name:"transient"},{type:"String",name:"elementOwnerAccountId"}]},v2ChatroomQueuePoll:{sid:36,cid:21,service:rh,params:[{type:"String",name:"elementKey"}],response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},v2ChatroomQueueList:{sid:36,cid:22,service:rh,params:[],response:[{type:"KVArray",name:"datas"}]},v2ChatroomQueuePeek:{sid:36,cid:23,service:rh,params:[],response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},v2ChatroomQueueDrop:{sid:36,cid:24,service:rh,params:[]},v2ChatroomQueueInit:{sid:36,cid:25,service:rh,params:[{type:"Int",name:"size"}]},v2ChatroomQueueBatchUpdate:{sid:36,cid:26,service:rh,params:[{type:"StrStrMap",name:"keyValues"},{type:"Bool",name:"notificationEnabled"},{type:"String",name:"notificationExtension"}],response:[{type:"StrArray",name:"datas"}]}},ih={elementKey:{type:"string",required:!0,allowEmpty:!1},elementValue:{type:"string",required:!0,allowEmpty:!1},transient:{type:"boolean",required:!1},elementOwnerAccountId:{type:"string",required:!1,allowEmpty:!1}},ah={elements:{type:"array",min:1,max:100,rules:{key:{type:"string",required:!0,allowEmpty:!1},value:{type:"string",required:!0,allowEmpty:!1}},required:!0},notificationEnabled:{type:"boolean",required:!1},notificationExtension:{type:"string",required:!1}},sh=function(t){function V2NIMChatroomQueueServiceImpl(a){var u;return u=t.call(this,"V2NIMChatroomQueueService",a)||this,registerParser({cmdMap:nh,cmdConfig:oh}),u.setListeners(),u}At(V2NIMChatroomQueueServiceImpl,t);var a=V2NIMChatroomQueueServiceImpl.prototype;return a.setListeners=function setListeners(){var t=this;this.core.eventBus.on("V2NIMChatroomMessageService/onReceiveNotification",(function(a){return __awaiter(t,void 0,void 0,Sa.mark((function _callee(){var t,u,_,h,E;return Sa.wrap((function _callee$(m){for(;;)switch(m.prev=m.next){case 0:m.t0=a.attachment.type,m.next=11===m.t0?3:5;break;case 3:try{t=JSON.parse(a.attachment.raw),"OFFER"===(u=JSON.parse(t.data.queueChange))._e?this.emit("onChatroomQueueOffered",{key:u.key,value:u.content}):"POLL"===u._e?this.emit("onChatroomQueuePolled",{key:u.key,value:u.content}):"DROP"===u._e?this.emit("onChatroomQueueDropped"):"BATCH_UPDATE"===u._e?(_=formatQueueElementsFromKVObject(u.kvObject)).length>0&&this.emit("onChatroomQueueBatchUpdated",_):"PARTCLEAR"===u._e?(h=formatQueueElementsFromKVObject(u.kvObject)).length>0&&this.emit("onChatroomQueuePartCleared",h):"BATCH_OFFER"===u._e&&(E=formatQueueElementsFromElements(u.elements)).length>0&&this.emit("onChatroomQueueBatchOffered",E)}catch(t){this.logger.error("V2NIMChatroomQueueServiceImpl json parse error",t," raw = ",a.attachment.raw)}return m.abrupt("break",5);case 5:case"end":return m.stop()}}),_callee,this)})))}))},a.emit=function emit(a){for(var u,_,h,E,m=this.name+"::emit "+a.toString(),g=arguments.length,I=new Array(g>1?g-1:0),T=1;T<g;T++)I[T-1]=arguments[T];return(u=this.logger).log.apply(u,concat(_=[""+m]).call(_,I)),(h=t.prototype.emit).call.apply(h,concat(E=[this,a]).call(E,I))},a.queueOffer=function queueOffer(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){return Sa.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return validate(ih,t,"",!0),a.next=3,this.core.sendCmd("v2ChatroomQueueOffer",t);case 3:case"end":return a.stop()}}),_callee2,this)})))},a.queuePoll=function queuePoll(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee3(){var a;return Sa.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return t="string"==typeof t?t:"",u.next=3,this.core.sendCmd("v2ChatroomQueuePoll",{elementKey:t});case 3:return a=u.sent,u.abrupt("return",{key:a.content.elementKey,value:a.content.elementValue});case 5:case"end":return u.stop()}}),_callee3,this)})))},a.queueList=function queueList(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee4(){var t;return Sa.wrap((function _callee4$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("v2ChatroomQueueList");case 2:return t=a.sent,a.abrupt("return",(u=t.content.datas)&&u.length>0?map$7(u).call(u,(function(t){var a=Za(t)[0];return{key:a,value:t[a]}})):[]);case 4:case"end":return a.stop()}var u}),_callee4,this)})))},a.queuePeek=function queuePeek(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee5(){var t;return Sa.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("v2ChatroomQueuePeek");case 2:return t=a.sent,a.abrupt("return",{key:t.content.elementKey,value:t.content.elementValue});case 4:case"end":return a.stop()}}),_callee5,this)})))},a.queueDrop=function queueDrop(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee6(){return Sa.wrap((function _callee6$(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.core.sendCmd("v2ChatroomQueueDrop");case 2:case"end":return t.stop()}}),_callee6,this)})))},a.queueInit=function queueInit(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee7(){return Sa.wrap((function _callee7$(a){for(;;)switch(a.prev=a.next){case 0:return validate({size:{type:"number",min:0,max:1e3}},{size:t},"",!0),a.next=3,this.core.sendCmd("v2ChatroomQueueInit",{size:t});case 3:case"end":return a.stop()}}),_callee7,this)})))},a.queueBatchUpdate=function queueBatchUpdate(t,a,u){return void 0===a&&(a=!0),__awaiter(this,void 0,void 0,Sa.mark((function _callee8(){var _,h;return Sa.wrap((function _callee8$(E){for(;;)switch(E.prev=E.next){case 0:return validate(ah,{elements:t,notificationEnabled:a,notificationExtension:u},"",!0),_=reduce(t).call(t,(function(t,a){return t[a.key]=a.value,t}),{}),E.next=4,this.core.sendCmd("v2ChatroomQueueBatchUpdate",{keyValues:_,notificationEnabled:a,notificationExtension:u});case 4:return h=E.sent,E.abrupt("return",h.content.datas);case 6:case"end":return E.stop()}}),_callee8,this)})))},V2NIMChatroomQueueServiceImpl}(o_),ch=1,lh={},uh=function(t){function V2NIMChatroomClient(a,u){var _,h,E,m;void 0===u&&(u={}),(_=t.call(this)||this).pluginMap={},_.eventBus=new xl,_.options={appkey:"",account:"",tags:[],debugLevel:"debug",xhrConnectTimeout:8e3,socketConnectTimeout:8e3,apiVersion:"v2",isFixedDeviceId:!1,loginSDKTypeParamCompat:!1,binaryWebsocket:!0},_.config={deviceId:"",clientSession:"",binaryWebsocket:!0},_.options.appkey=a.appkey,_.options.customClientType=a.customClientType,_.options.isFixedDeviceId=a.isFixedDeviceId,_.options.loginSDKTypeParamCompat=a.loginSDKTypeParamCompat,_.instanceId=ch,ch+=1,_.logger=new Cl(a.debugLevel||"debug",u.loggerConfig),_.timerManager=new id,_.adapters=new ad($e(_)),_.timeOrigin=new ku($e(_),{},"v2ChatroomGetServerTime"),_.reporterHookLinkKeep=new s_($e(_)),_.reporterHookCloudStorage=new th($e(_)),"boolean"==typeof a.binaryWebsocket&&(_.options.binaryWebsocket=a.binaryWebsocket),_.options.isFixedDeviceId?(_.config.deviceId=Es.localStorage.getItem("__CHATROOM_DEVC_ID__")||$u(),_.config.clientSession=Es.localStorage.getItem("__CHATROOM_CLIENT_SESSION_ID__")||$u(),Es.localStorage.setItem("__CHATROOM_DEVC_ID__",_.config.deviceId),Es.localStorage.setItem("__CHATROOM_CLIENT_SESSION_ID__",_.config.clientSession)):(_.config.deviceId=$u(),_.config.clientSession=$u()),_.abtest=new sd($e(_),{isAbtestEnable:void 0===(null===(h=u.abtestConfig)||void 0===h?void 0:h.isAbtestEnable)||(null===(E=u.abtestConfig)||void 0===E?void 0:E.isAbtestEnable),abtestUrl:(null===(m=u.abtestConfig)||void 0===m?void 0:m.abtestUrl)||Al,abtestProjectKey:bl});var g=Es.getSystemInfo(),I=function getCompassDataEndpoint(t,a){var u,_,h=null===(u=null==a?void 0:a.reporterConfig)||void 0===u?void 0:u.compassDataEndpoint,E=null===(_=null==a?void 0:a.reporterConfig)||void 0===_?void 0:_.reportConfigUrl;if(h)return h;if(E){var m=E.match(/^https:\/\/([^/]+)\/*/);return fs(m)&&m.length>=1?"https://"+m[1]:(t.error("Invalid reportConfigUrl: "+E),kl)}return kl}(_.logger,u);return _.reporter=new hs(ba(ba({},I?{compassDataEndpoint:I}:{}),{isDataReportEnable:getIsDataReportEnable(u),common:{app_key:a.appkey,dev_id:_.config.deviceId,platform:"Web",sdk_ver:"10.8.30",env:"online",os_name:g.os,os_ver:g.osVer,lib_env:g.libEnv,host_env:g.hostEnv,host_env_ver:g.hostEnvVer,manufactor:g.manufactor,model:g.model,v2:!0},request:Es.request,logger:_.logger,autoStart:!0})),Es.setLogger(_.logger),_.auth=new i_($e(_)),_.V2NIMChatroomLoginService=_.auth,!1!==_.options.binaryWebsocket&&"function"==typeof Uint8Array?(_.config.binaryWebsocket=!0,_.clientSocket=new Zu($e(_))):(_.config.binaryWebsocket=!1,_.clientSocket=new od($e(_))),_.cloudStorage=new V_($e(_),ba({storageKeyPrefix:"V2NIMChatroomClient"},u.cloudStorageConfig)),_.V2NIMChatroomQueueService=new sh($e(_)),_.V2NIMChatroomInfoService=new E_($e(_)),_.V2NIMStorageService=new U_($e(_)),_.V2NIMStorageUtil=new x_($e(_)),_.V2NIMChatroomMessageService=new Pp($e(_)),_.V2NIMChatroomMessageCreator=new xp($e(_)),_.V2NIMClientAntispamUtil=new Qp($e(_),u.V2NIMClientAntispamUtilConfig),_.V2NIMChatroomMemberService=new Kp($e(_)),_.V2NIMChatroomService=new Wp($e(_)),_.logger.log("NIM chatroom init, version:10.8.30, sdk version:100830, appkey:"+a.appkey),_}At(V2NIMChatroomClient,t),V2NIMChatroomClient.getInstanceList=function getInstanceList(){return wa(lh)},V2NIMChatroomClient.getInstance=function getInstance(t){return validate({instanceId:{type:"number",allowEmpty:!1}},{instanceId:t},"",!0),lh[t]},V2NIMChatroomClient.newInstance=function newInstance(t,a){void 0===a&&(a={}),validate(Jp,{initParams:t,otherParams:a},"",!0);var u=new V2NIMChatroomClient(t,a);return lh[u.instanceId]=u,u},V2NIMChatroomClient.destroyInstance=function destroyInstance(t){var a=this.getInstance(t);if(a)return delete lh[t],a._exitAsync().then((function(){a._clear()})).catch((function(){a._clear()}))},V2NIMChatroomClient.destroyAll=function destroyAll(){for(var t in lh)this.destroyInstance(Number(t))};var a=V2NIMChatroomClient.prototype;return a.getInstanceId=function getInstanceId(){return this.instanceId},a._clearModuleData=function _clearModuleData(t){void 0===t&&(t="logout");var a=wa(this);forEach$1(a).call(a,(function(a){a&&"function"==typeof a.reset&&a.reset(t)}))},a._removeAllModuleListeners=function _removeAllModuleListeners(){var t=wa(this);forEach$1(t).call(t,(function(t){t&&"function"==typeof t.removeAllListeners&&t.removeAllListeners()}))},a._clear=function _clear(){this.removeAllListeners(),this.eventBus.removeAllListeners(),this.logger.destroy(),this.reporter.destroy(),this.timerManager.destroy(),this._clearModuleData("destroy"),this._removeAllModuleListeners()},a.enter=function enter(t,a){var u;return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var _,h;return Sa.wrap((function _callee$(E){for(;;)switch(E.prev=E.next){case 0:if(validate(r_,{roomId:t,enterParams:a},"",!0),a.accountId||a.anonymousMode){E.next=3;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"accountId is required"}});case 3:if(_=get(a,"loginOption.authType")||0,a.loginOption=a.loginOption||{authType:_},0!==_){E.next=8;break}if(a.anonymousMode||a.token){E.next=8;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"token is required when authType == 0"}});case 8:if(1!==_){E.next=11;break}if(null===(u=a.loginOption)||void 0===u?void 0:u.tokenProvider){E.next=11;break}throw new vl({code:Ud.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:"tokenProvider is required when authType == 1"}});case 11:return E.next=13,this.auth.login(this.options.appkey,t,a);case 13:return h=E.sent,this.V2NIMChatroomInfoService._setChatroomInfo(h.chatroom),E.abrupt("return",h);case 16:case"end":return E.stop()}}),_callee,this)})))},a._exitAsync=function _exitAsync(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){return Sa.wrap((function _callee2$(t){for(;;)switch(t.prev=t.next){case 0:return this.auth.reset(),t.prev=1,t.next=4,this.auth.logout();case 4:return t.abrupt("return");case 7:return t.prev=7,t.t0=t.catch(1),t.abrupt("return",_a.resolve());case 10:case"end":return t.stop()}}),_callee2,this,[[1,7]])})))},a.exit=function exit(){this._exitAsync()},a.sendCmd=function sendCmd(t,a,u){return this.clientSocket.sendCmd(t,a,u)},a.getChatroomInfo=function getChatroomInfo(){return this.V2NIMChatroomInfoService.getChatroomInfo()},a._registerDep=function _registerDep(t,a){},a.emit=function emit(a){for(var u,_,h,E,m="core::emit "+a.toString(),g=arguments.length,I=new Array(g>1?g-1:0),T=1;T<g;T++)I[T-1]=arguments[T];return(u=this.logger).log.apply(u,concat(_=[""+m]).call(_,I)),(h=t.prototype.emit).call.apply(h,concat(E=[this,a]).call(E,I))},ze(V2NIMChatroomClient,[{key:"account",get:function get(){return this.options.account}},{key:"tags",get:function get(){return this.options.tags}},{key:"status",get:function get(){return 5===this.auth.lifeCycle.chatroomStatus?"logined":""}}]),V2NIMChatroomClient}(xl);uh.sdkVersion=100830,uh.sdkVersionFormat="10.8.30";var dh=createCommonjsModule((function(t,a){t.exports=function(){function object2String(t){if(t){var a,u="";return forEach$1(a=Za(t)).call(a,(function(a,_){u+=0===_?"?":"&",u+=a+"="+t[a]})),u}return""}var t=function(t){function V2NIMError(a,u,_,h){var E;return(E=t.call(this,_)||this).source=a,E.code=u,E.desc=_,E.detail=h||{},E}return At(V2NIMError,t),V2NIMError}(dc(Error));function request(a,u){void 0===u&&(u={dataType:"json",method:"GET",timeout:5e3});var _="text"===u.dataType?"text/plain; charset=UTF-8":"application/json; charset=UTF-8",h="GET"===u.method?object2String(u.params):"";return new _a((function(E,m){if(window.XMLHttpRequest){var g,I=new XMLHttpRequest;if(I.onreadystatechange=function(){if(4===I.readyState)if(200===I.status){try{g=JSON.parse(I.response||"{}")}catch(t){g=I.response}E({status:I.status,data:g})}else ls((function(){m(new t(1,I.status,"readyState: "+I.readyState+"; statusText: "+I.statusText))}),0)},I.open(u.method,""+a+h),I.timeout=u.timeout||5e3,I.setRequestHeader("Content-Type",_),u.headers)for(var T in u.headers)I.setRequestHeader(T,u.headers[T]);I.ontimeout=function(a){m(new t(1,408,a&&a.message?a.message:"request timeout"))},I.send(Ms(u.data))}else m(new t(2,10400,"request no suppout"))}))}return request}()})),_h=Math.floor,mergeSort=function(t,a){var u=t.length,_=_h(u/2);return u<8?insertionSort(t,a):merge(t,mergeSort(arraySliceSimple(t,0,_),a),mergeSort(arraySliceSimple(t,_),a),a)},insertionSort=function(t,a){for(var u,_,h=t.length,E=1;E<h;){for(_=E,u=t[E];_&&a(t[_-1],u)>0;)t[_]=t[--_];_!==E++&&(t[_]=u)}return t},merge=function(t,a,u,_){for(var h=a.length,E=u.length,m=0,g=0;m<h||g<E;)t[m+g]=m<h&&g<E?_(a[m],u[g])<=0?a[m++]:u[g++]:m<h?a[m++]:u[g++];return t},ph=mergeSort,hh=K.match(/firefox\/(\d+)/i),fh=!!hh&&+hh[1],Eh=/MSIE|Trident/.test(K),mh=K.match(/AppleWebKit\/(\d+)\./),gh=!!mh&&+mh[1],Ih=[],vh=R(Ih.sort),Th=R(Ih.push),Nh=fails((function(){Ih.sort(void 0)})),Mh=fails((function(){Ih.sort(null)})),Oh=arrayMethodIsStrict("sort"),Sh=!fails((function(){if(X)return X<70;if(!(fh&&fh>3)){if(Eh)return!0;if(gh)return gh<603;var t,a,u,_,h="";for(t=65;t<76;t++){switch(a=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:u=3;break;case 68:case 71:u=4;break;default:u=2}for(_=0;_<47;_++)Ih.push({k:a+_,v:u})}for(Ih.sort((function(t,a){return a.v-t.v})),_=0;_<Ih.length;_++)a=Ih[_].k.charAt(0),h.charAt(h.length-1)!==a&&(h+=a);return"DGBEFHACIJK"!==h}}));_export({target:"Array",proto:!0,forced:Nh||!Mh||!Oh||!Sh},{sort:function sort(t){void 0!==t&&aCallable(t);var a=toObject(this);if(Sh)return void 0===t?vh(a):vh(a,t);var u,_,h=[],E=lengthOfArrayLike(a);for(_=0;_<E;_++)_ in a&&Th(h,a[_]);for(ph(h,function(t){return function(a,u){return void 0===u?-1:void 0===a?1:void 0!==t?+t(a,u)||0:toString(a)>toString(u)?1:-1}}(t)),u=lengthOfArrayLike(h),_=0;_<u;)a[_]=h[_++];for(;_<E;)deletePropertyOrThrow(a,_++);return a}});var Rh=entryVirtual("Array").sort,yh=Array.prototype,sort=function(t){var a=t.sort;return t===yh||j(yh,t)&&a===yh.sort?Rh:a},Ch=createCommonjsModule((function(t,a){self,t.exports=function(){var t={d:function d(a,u){for(var _ in u)t.o(u,_)&&!t.o(a,_)&&We(a,_,{enumerable:!0,get:u[_]})},o:function o(t,a){return Object.prototype.hasOwnProperty.call(t,a)}},a={};t.d(a,{default:function _default(){return M}});var u=function e(t){for(var a in function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.directUploadAddr="https://wanproxy-web.127.net",this.retryCount=4,this.trunkSize=4194304,this.trunkUploadTimeout=5e4,this.getOffsetTimeout=1e4,this.version="1.0",this.enableCache=!0,this.logger=console,this.onError=function(t){},this.onProgress=function(t){},this.onUploadProgress=function(t){},this.onComplete=function(t){},t)this[a]=t[a]};function n(t,a){var u=void 0!==Jn&&wl(t)||t["@@iterator"];if(!u){if(fs(t)||(u=function(t,a){if(t){var u;if("string"==typeof t)return r(t,a);var _=slice(u=Object.prototype.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Vl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?r(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){u&&(t=u);var _=0,h=function i(){};return{s:h,n:function n(){return _>=t.length?{done:!0}:{done:!1,value:t[_++]}},e:function e(t){throw t},f:h}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var E,m=!0,g=!1;return{s:function s(){u=u.call(t)},n:function n(){var t=u.next();return m=t.done,t},e:function e(t){g=!0,E=t},f:function f(){try{m||null==u.return||u.return()}finally{if(g)throw E}}}}function r(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=new Array(a);u<a;u++)_[u]=t[u];return _}var _={privateObj:{},setItem:function setItem(t,a){_.privateObj[t]=a},getItem:function getItem(t){return _.privateObj[t]},removeItem:function removeItem(t){delete _.privateObj[t]},getKeys:function getKeys(){return Za(_.privateObj)}},h={getFileKey:function getFileKey(t){var a=t.size.toString(),u=t.lastModified.toString();return"_NosUploader_"+t.name+slice(a).call(a,a.length-5)+slice(u).call(u,u.length-5)},getFileInfo:function getFileInfo(t){var a=_.getItem(t);if(!a)return null;try{return JSON.parse(a)}catch(t){return null}},initFile:function initFile(t,a,u){h.clearExpiredInfo();var E=this.getFileKey(a),m={ctx:void 0!==t.ctx?t.ctx:"",bucket:t.bucketName,obj:t.objectName,token:t.token,modifyAt:Ga(),end:!1};return t.payload&&(m.payload=t.payload),u&&_.setItem(E,Ms(m)),E},setUploadContext:function setUploadContext(t,a,u){var h=this.getFileInfo(t);h&&(h.ctx=a,u&&_.setItem(t,Ms(h)))},setComplete:function setComplete(t,a){var u=this.getFileInfo(t);u&&(u.modifyAt=Ga(),u.end=!0,a&&_.setItem(t,Ms(u)))},getUploadContext:function getUploadContext(t){var a=this.getFileInfo(t);return a?a.ctx:""},removeFileInfo:function removeFileInfo(t){0===indexOf(t).call(t,"_NosUploader_")&&_.removeItem(t)},clearExpiredInfo:function clearExpiredInfo(){var t,a="function"==typeof _.getKeys?_.getKeys():Za(_),u=Ga(),E=[],m=n(a);try{for(m.s();!(t=m.n()).done;){var g=t.value;if(0===indexOf(g).call(g,"_NosUploader_")){var I=h.getFileInfo(g);null===I||u-I.modifyAt>M.expireTime?_.removeItem(g):E.push({fileInfo:I,key:g})}}}catch(t){m.e(t)}finally{m.f()}if(E.length>M.maxFileCache){var T,N,O=n(slice(T=sort(E).call(E,(function(t,a){return a.fileInfo.modifyAt-t.fileInfo.modifyAt}))).call(T,M.maxFileCache));try{for(O.s();!(N=O.n()).done;){var S,R=N.value;0===indexOf(S=R.key).call(S,"_NosUploader_")&&_.removeItem(R.key)}}catch(t){O.e(t)}finally{O.f()}}}},E=h;function c(t){return(c="function"==typeof Jn&&"symbol"==typeof so?function(t){return typeof t}:function(t){return t&&"function"==typeof Jn&&t.constructor===Jn&&t!==Jn.prototype?"symbol":typeof t})(t)}function s(t,a){return!a||"object"!==c(a)&&"function"!=typeof a?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):a}function f(t){var a="function"==typeof qs?new qs:void 0;return(f=function f(t){var u,_;if(null===t||(_=t,-1===indexOf(u=Function.toString.call(_)).call(u,"[native code]")))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==a){if(a.has(t))return a.get(t);a.set(t,n)}function n(){return l(t,arguments,y(this).constructor)}return n.prototype=ft(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),d(n,t)})(t)}function l(t,a,u){return(l=p()?sc:function(t,a,u){var _=[null];_.push.apply(_,a);var h=new(bind$1(Function).apply(t,_));return u&&d(h,u.prototype),h}).apply(null,arguments)}function p(){if("undefined"==typeof Reflect||!sc)return!1;if(sc.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(sc(Boolean,[],(function(){}))),!0}catch(t){return!1}}function d(t,a){return(d=It||function(t,a){return t.__proto__=a,t})(t,a)}function y(t){return(y=It?uo:function(t){return t.__proto__||uo(t)})(t)}var m=function(t){!function(t,a){if("function"!=typeof a&&null!==a)throw new TypeError("Super expression must either be null or a function");t.prototype=ft(a&&a.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),a&&d(t,a)}(r,t);var a,u,_=(a=r,u=p(),function(){var t,_=y(a);if(u){var h=y(this).constructor;t=sc(_,arguments,h)}else t=_.apply(this,arguments);return s(this,t)});function r(t,a){var u;return function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,r),(u=_.call(this,"NosUploadError:"+t)).errCode=a,u.errMsg=t,u}return r}(f(Error)),g=function e(t,a,u){if("uploading"===t.uploadState){var _=t.config,h=t.param,g=E.getUploadContext(t.fileKey);if(!g)return u(0);var I=new XMLHttpRequest,T=_.directUploadAddr+"/".concat(h.bucketName)+"/".concat(encodeURIComponent(h.objectName))+"?uploadContext"+"&context=".concat(g)+"&version=".concat(_.version);I.onreadystatechange=function(){var h;if("abort"!==t.uploadState&&4===I.readyState){var g,N,M,O;try{O=JSON.parse(I.responseText)}catch(t){O={errMsg:"JsonParseError in getOffset. xhr.status = "+I.status+". xhr.responseText: "+I.responseText,errCode:500}}200===I.status?O.errCode?t.config.onError(new m(O.errMsg,O.errCode)):u(O.offset):I.status.toString().match(/^5/)?e(t,a-1,u):a>0?("function"==typeof(null===(h=_.logger)||void 0===h?void 0:h.error)&&_.logger.error(concat(g="getOffset(".concat(T,") error. retry after 3 seconds. ")).call(g,(new Date).toTimeString())),ls((function(){e(t,a-1,u)}),3500)):I.status?(E.removeFileInfo(t.fileKey),_.onError(new m(concat(N=concat(M="getOffset(".concat(T,") error: ")).call(M,I.status," ")).call(N,I.statusText)))):_.onError(new m("getOffset(".concat(T,") error. no Error Code")))}},I.open("get",T),I.setRequestHeader("x-nos-token",h.token),I.timeout=_.getOffsetTimeout,I.send()}},I=function e(t,a,u,_){if("uploading"===t.uploadState){var h=t.param,g=t.config,I=slice(File.prototype),T=void 0!==h.ctx?h.ctx:"",N=a+g.trunkSize>=t.file.size,M=N?t.file.size:a+g.trunkSize,O=new XMLHttpRequest,S=g.directUploadAddr+"/".concat(h.bucketName)+"/".concat(encodeURIComponent(h.objectName));if(O.upload.onprogress=function(u){if("abort"!==t.uploadState){var _=0;u.lengthComputable?(_=(a+u.loaded)/t.file.size,g.onProgress(_),g.onUploadProgress({loaded:u.loaded,total:t.file.size,percentage:_,percentageText:(100*_).toFixed(2)+"%"})):g.onError(new m("browser does not support query upload progress"))}},O.onreadystatechange=function(){var h,I;if("abort"!==t.uploadState&&4===O.readyState){var T,M,R,C;try{C=JSON.parse(O.responseText)}catch(t){"function"==typeof(null===(h=g.logger)||void 0===h?void 0:h.error)&&g.logger.error("JsonParseError in uploadTrunk. xhr.status = "+O.status+". xhr.responseText: "+O.responseText,t),C={errMsg:"JsonParseError in uploadTrunk. xhr.status = "+O.status+". xhr.responseText: "+O.responseText}}200===O.status?(t.setContext(C.context),N?(_(),t.setComplete()):e(t,C.offset,g.retryCount,_)):O.status.toString().match(/^5/)?u>0?e(t,a,u-1,_):(E.removeFileInfo(t.fileKey),g.onError(new m(C.errMsg,C.errCode))):u>0?("function"==typeof(null===(I=g.logger)||void 0===I?void 0:I.error)&&g.logger.error(concat(T="uploadTrunk(".concat(S,") error. retry after 3 seconds. ")).call(T,(new Date).toTimeString())),ls((function(){e(t,a,u-1,_)}),3500)):O.status?(E.removeFileInfo(t.fileKey),g.onError(new m(concat(M=concat(R="uploadTrunk(".concat(S,") error: ")).call(R,O.status," ")).call(M,O.statusText)))):g.onError(new m("uploadTrunk(".concat(S,") error. no Error Code. Please check your network")))}},O.open("post",S+"?offset=".concat(a)+"&complete=".concat(N)+"&context=".concat(T)+"&version=".concat(g.version)),O.setRequestHeader("x-nos-token",h.token),h.md5&&O.setRequestHeader("content-md5",h.md5),t.file.type&&O.setRequestHeader("content-type",t.file.type),O.timeout=g.trunkUploadTimeout,"undefined"!=typeof FileReader){var R=new FileReader;R.addEventListener("load",(function(t){var a;(null===(a=null==t?void 0:t.target)||void 0===a?void 0:a.result)instanceof ArrayBuffer&&t.target.result.byteLength>0?O.send(t.target.result):g.onError(new m("Read ArrayBuffer failed",194003))})),R.addEventListener("error",(function(t){var a=t.target.error;g.onError(new m("Read ArrayBuffer error. ".concat(a.toString()),194003))})),R.readAsArrayBuffer(I.call(t.file,a,M))}else O.send(I.call(t.file,a,M))}};function v(t,a){for(var u=0;u<a.length;u++){var _=a[u];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),We(t,_.key,_)}}var T=function(){function e(t,a,u){!function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.uploadState="paused",this.config=u,this.file=t,this.param=a,this.fileKey=E.initFile(a,t,this.config.enableCache),this.resume()}var t;return(t=[{key:"resume",value:function value(){var t=this;if("uploading"!==this.uploadState){this.setUploadState("uploading");var a=this.config;g(this,a.retryCount,(function(u){I(t,u,a.retryCount,(function(){t.setUploadState("ended"),"function"==typeof a.onComplete&&a.onComplete(t.param)}))}))}}},{key:"pause",value:function value(){this.setUploadState("paused")}},{key:"abort",value:function value(){"ended"!==this.uploadState&&"abort"!==this.uploadState&&(this.setUploadState("abort"),this.config.onError(new m("Upload Aborted",10499)))}},{key:"setUploadState",value:function value(t){t!==this.uploadState&&(this.uploadState=t)}},{key:"setContext",value:function value(t){E.setUploadContext(this.fileKey,t,this.config.enableCache),this.param.ctx=t}},{key:"setComplete",value:function value(){E.setComplete(this.fileKey,this.config.enableCache),this.setUploadState("ended")}}])&&v(e.prototype,t),e}(),N={maxFileCache:1/0,expireTime:864e5,getFileUploadInformation:function getFileUploadInformation(t){var a=E.getFileKey(t),u=E.getFileInfo(a);return null===u?null:Ga()-u.modifyAt>N.expireTime?(E.removeFileInfo(a),null):{uploadInfo:ba({bucketName:u.bucket,objectName:u.obj,token:u.token,ctx:u.ctx},u.payload?{payload:u.payload}:{}),complete:u.end}},setMaxFileCache:function setMaxFileCache(t){N.maxFileCache=t},setExpireTime:function setExpireTime(t){N.expireTime=t},printCaches:function printCaches(){if("undefined"!=typeof localStorage)for(var t=0,a=Za(localStorage);t<a.length;t++){var u=a[t],_=E.getFileInfo(u);_&&console.log(_,"modifiedAt",new Date(_.modifyAt).toTimeString())}},createConfig:function createConfig(t){return new u(t)},createTask:function createTask(t,a,u){return new T(t,a,u)}},M=N;return a.default}()})),Ah={debug:function debug(){},log:function log(){},warn:function warn(){},error:function error(){}};function setLogger(t){Ah=t}function isMobile(){if(!navigator||!navigator.userAgent)return!1;var t=[/Android/i,/webOS/i,/iPhone/i,/iPad/i,/iPod/i,/BlackBerry/i,/Windows Phone/i];return some(t).call(t,(function(t){return navigator.userAgent.match(t)}))}function isElectron(){var t;return!(!navigator||!navigator.userAgent)&&("object"==typeof navigator&&"string"==typeof navigator.userAgent&&indexOf(t=navigator.userAgent).call(t,"Electron")>=0)}function isBrowser(){return navigator&&navigator.userAgent}function uploadFileFn(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var a,u,_,h,E,m,g;return Sa.wrap((function _callee$(I){for(;;)switch(I.prev=I.next){case 0:if(h=Ah,t.fileInput||t.file){I.next=3;break}throw new Error("File not exist");case 3:if(!t.file){I.next=7;break}E=t.file,I.next=21;break;case 7:if("string"!=typeof t.fileInput){I.next=16;break}if(!((m=document.getElementById(t.fileInput))&&m.files&&m.files[0])){I.next=13;break}E=m.files[0],I.next=14;break;case 13:throw new Error("Can not get file from fileInput");case 14:I.next=21;break;case 16:if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0])){I.next=20;break}E=t.fileInput.files[0],I.next=21;break;case 20:throw new Error("Can not get file from fileInput "+t.fileInput);case 21:if(!(t.maxSize&&E.size>t.maxSize)){I.next=23;break}throw new Error("The file exceeds maxSize limit. maxSize: "+t.maxSize+", get "+E.size);case 23:return I.next=25,new _a((function(a,u){var _,m=Ch.getFileUploadInformation(E),g=Ch.createConfig({enableCache:!0,retryCount:0,directUploadAddr:t.chunkUploadHost,onError:function onError(t){u(t)},onUploadProgress:t.onUploadProgress||function(){},onComplete:function onComplete(t){a(t)}});if(m)if(m.complete)t.onUploadProgress&&t.onUploadProgress({total:E.size,loaded:E.size,percentage:1,percentageText:"100%"}),a(m.uploadInfo);else{_=Ch.createTask(E,m.uploadInfo,g);try{t.onUploadStart&&t.onUploadStart(_)}catch(t){h.error("Adapter uploadFile: options.onUploadStart error",t&&t.message),_.abort(),u(t)}}else{_=Ch.createTask(E,ba(ba({bucketName:t.nosToken.bucket,objectName:decodeURIComponent(t.nosToken.objectName),token:t.nosToken.token},t.md5?{md5:t.md5}:{}),t.payload?{payload:t.payload}:{}),g);try{t.onUploadStart&&t.onUploadStart(_)}catch(t){h.error("Adapter uploadFile: options.onUploadStart error",t&&t.message),_.abort(),u(t)}}}));case 25:return(g=I.sent).name=E.name,g.size=E.size,g.type=E.type,g.ext=lastIndexOf(a=g.name).call(a,".")>-1?slice(u=g.name).call(u,lastIndexOf(_=g.name).call(_,".")+1).toLowerCase():"",I.abrupt("return",g);case 31:case"end":return I.stop()}}),_callee)})))}function getFileUploadInformationFn(t){var a;if(t.file)a=t.file;else if("string"==typeof t.fileInput){var u=document.getElementById(t.fileInput);if(!(u&&u.files&&u.files[0]))throw new Error("Can not get file from fileInput");a=u.files[0]}else{if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0]))throw new Error("Can not get file from fileInput "+t.fileInput);a=t.fileInput.files[0]}return Ch.getFileUploadInformation(a)}
/*!
	 * Platform.js v1.3.6
	 * Copyright 2014-2020 Benjamin Tan
	 * Copyright 2011-2013 John-David Dalton
	 * Available under MIT license
	 */var bh=createCommonjsModule((function(t,u){(function(){var _={function:!0,object:!0}[typeof window]&&window||this,h=u,E=t&&!t.nodeType&&t,m=h&&E&&"object"==typeof a&&a;!m||m.global!==m&&m.window!==m&&m.self!==m||(_=m);var g=Math.pow(2,53)-1,I=/\bOpera/,T=Object.prototype,N=T.hasOwnProperty,M=T.toString;function capitalize(t){return(t=String(t)).charAt(0).toUpperCase()+t.slice(1)}function format(t){return t=trim(t),/^(?:webOS|i(?:OS|P))/.test(t)?t:capitalize(t)}function forOwn(t,a){for(var u in t)N.call(t,u)&&a(t[u],u,t)}function getClassOf(t){return null==t?capitalize(t):M.call(t).slice(8,-1)}function qualify(t){return String(t).replace(/([ -])(?!$)/g,"$1?")}function reduce(t,a){var u=null;return function each(t,a){var u=-1,_=t?t.length:0;if("number"==typeof _&&_>-1&&_<=g)for(;++u<_;)a(t[u],u,t);else forOwn(t,a)}(t,(function(_,h){u=a(u,_,h,t)})),u}function trim(t){return String(t).replace(/^ +| +$/g,"")}var O=function parse(t){var a=_,u=t&&"object"==typeof t&&"String"!=getClassOf(t);u&&(a=t,t=null);var h=a.navigator||{},E=h.userAgent||"";t||(t=E);var m,g,T=u?!!h.likeChrome:/\bChrome\b/.test(t)&&!/internal|\n/i.test(M.toString()),N="Object",O=u?N:"ScriptBridgingProxyObject",S=u?N:"Environment",R=u&&a.java?"JavaPackage":getClassOf(a.java),C=u?N:"RuntimeObject",A=/\bJava/.test(R)&&a.java,b=A&&getClassOf(a.environment)==S,k=A?"a":"α",L=A?"b":"β",D=a.document||{},V=a.operamini||a.opera,w=I.test(w=u&&V?V["[[Class]]"]:getClassOf(V))?w:V=null,P=t,x=[],U=null,B=t==E,G=B&&V&&"function"==typeof V.version&&V.version(),H=function getLayout(a){return reduce(a,(function(a,u){return a||RegExp("\\b"+(u.pattern||qualify(u))+"\\b","i").exec(t)&&(u.label||u)}))}([{label:"EdgeHTML",pattern:"Edge"},"Trident",{label:"WebKit",pattern:"AppleWebKit"},"iCab","Presto","NetFront","Tasman","KHTML","Gecko"]),Y=function getName(a){return reduce(a,(function(a,u){return a||RegExp("\\b"+(u.pattern||qualify(u))+"\\b","i").exec(t)&&(u.label||u)}))}(["Adobe AIR","Arora","Avant Browser","Breach","Camino","Electron","Epiphany","Fennec","Flock","Galeon","GreenBrowser","iCab","Iceweasel","K-Meleon","Konqueror","Lunascape","Maxthon",{label:"Microsoft Edge",pattern:"(?:Edge|Edg|EdgA|EdgiOS)"},"Midori","Nook Browser","PaleMoon","PhantomJS","Raven","Rekonq","RockMelt",{label:"Samsung Internet",pattern:"SamsungBrowser"},"SeaMonkey",{label:"Silk",pattern:"(?:Cloud9|Silk-Accelerated)"},"Sleipnir","SlimBrowser",{label:"SRWare Iron",pattern:"Iron"},"Sunrise","Swiftfox","Vivaldi","Waterfox","WebPositive",{label:"Yandex Browser",pattern:"YaBrowser"},{label:"UC Browser",pattern:"UCBrowser"},"Opera Mini",{label:"Opera Mini",pattern:"OPiOS"},"Opera",{label:"Opera",pattern:"OPR"},"Chromium","Chrome",{label:"Chrome",pattern:"(?:HeadlessChrome)"},{label:"Chrome Mobile",pattern:"(?:CriOS|CrMo)"},{label:"Firefox",pattern:"(?:Firefox|Minefield)"},{label:"Firefox for iOS",pattern:"FxiOS"},{label:"IE",pattern:"IEMobile"},{label:"IE",pattern:"MSIE"},"Safari"]),j=getProduct([{label:"BlackBerry",pattern:"BB10"},"BlackBerry",{label:"Galaxy S",pattern:"GT-I9000"},{label:"Galaxy S2",pattern:"GT-I9100"},{label:"Galaxy S3",pattern:"GT-I9300"},{label:"Galaxy S4",pattern:"GT-I9500"},{label:"Galaxy S5",pattern:"SM-G900"},{label:"Galaxy S6",pattern:"SM-G920"},{label:"Galaxy S6 Edge",pattern:"SM-G925"},{label:"Galaxy S7",pattern:"SM-G930"},{label:"Galaxy S7 Edge",pattern:"SM-G935"},"Google TV","Lumia","iPad","iPod","iPhone","Kindle",{label:"Kindle Fire",pattern:"(?:Cloud9|Silk-Accelerated)"},"Nexus","Nook","PlayBook","PlayStation Vita","PlayStation","TouchPad","Transformer",{label:"Wii U",pattern:"WiiU"},"Wii","Xbox One",{label:"Xbox 360",pattern:"Xbox"},"Xoom"]),K=function getManufacturer(a){return reduce(a,(function(a,u,_){return a||(u[j]||u[/^[a-z]+(?: +[a-z]+\b)*/i.exec(j)]||RegExp("\\b"+qualify(_)+"(?:\\b|\\w*\\d)","i").exec(t))&&_}))}({Apple:{iPad:1,iPhone:1,iPod:1},Alcatel:{},Archos:{},Amazon:{Kindle:1,"Kindle Fire":1},Asus:{Transformer:1},"Barnes & Noble":{Nook:1},BlackBerry:{PlayBook:1},Google:{"Google TV":1,Nexus:1},HP:{TouchPad:1},HTC:{},Huawei:{},Lenovo:{},LG:{},Microsoft:{Xbox:1,"Xbox One":1},Motorola:{Xoom:1},Nintendo:{"Wii U":1,Wii:1},Nokia:{Lumia:1},Oppo:{},Samsung:{"Galaxy S":1,"Galaxy S2":1,"Galaxy S3":1,"Galaxy S4":1},Sony:{PlayStation:1,"PlayStation Vita":1},Xiaomi:{Mi:1,Redmi:1}}),q=function getOS(a){return reduce(a,(function(a,u){var _=u.pattern||qualify(u);return!a&&(a=RegExp("\\b"+_+"(?:/[\\d.]+|[ \\w.]*)","i").exec(t))&&(a=function cleanupOS(t,a,u){var _={"10.0":"10",6.4:"10 Technical Preview",6.3:"8.1",6.2:"8",6.1:"Server 2008 R2 / 7","6.0":"Server 2008 / Vista",5.2:"Server 2003 / XP 64-bit",5.1:"XP",5.01:"2000 SP1","5.0":"2000","4.0":"NT","4.90":"ME"};return a&&u&&/^Win/i.test(t)&&!/^Windows Phone /i.test(t)&&(_=_[/[\d.]+$/.exec(t)])&&(t="Windows "+_),t=String(t),a&&u&&(t=t.replace(RegExp(a,"i"),u)),format(t.replace(/ ce$/i," CE").replace(/\bhpw/i,"web").replace(/\bMacintosh\b/,"Mac OS").replace(/_PowerPC\b/i," OS").replace(/\b(OS X) [^ \d]+/i,"$1").replace(/\bMac (OS X)\b/,"$1").replace(/\/(\d)/," $1").replace(/_/g,".").replace(/(?: BePC|[ .]*fc[ \d.]+)$/i,"").replace(/\bx86\.64\b/gi,"x86_64").replace(/\b(Windows Phone) OS\b/,"$1").replace(/\b(Chrome OS \w+) [\d.]+\b/,"$1").split(" on ")[0])}(a,_,u.label||u)),a}))}(["Windows Phone","KaiOS","Android","CentOS",{label:"Chrome OS",pattern:"CrOS"},"Debian",{label:"DragonFly BSD",pattern:"DragonFly"},"Fedora","FreeBSD","Gentoo","Haiku","Kubuntu","Linux Mint","OpenBSD","Red Hat","SuSE","Ubuntu","Xubuntu","Cygwin","Symbian OS","hpwOS","webOS ","webOS","Tablet OS","Tizen","Linux","Mac OS X","Macintosh","Mac","Windows 98;","Windows "]);function getProduct(a){return reduce(a,(function(a,u){var _=u.pattern||qualify(u);return!a&&(a=RegExp("\\b"+_+" *\\d+[.\\w_]*","i").exec(t)||RegExp("\\b"+_+" *\\w+-[\\w]*","i").exec(t)||RegExp("\\b"+_+"(?:; *(?:[a-z]+[_-])?[a-z]+\\d+|[^ ();-]*)","i").exec(t))&&((a=String(u.label&&!RegExp(_,"i").test(u.label)?u.label:a).split("/"))[1]&&!/[\d.]+/.test(a[0])&&(a[0]+=" "+a[1]),u=u.label||u,a=format(a[0].replace(RegExp(_,"i"),u).replace(RegExp("; *(?:"+u+"[_-])?","i")," ").replace(RegExp("("+u+")[-_.]?(\\w)","i"),"$1 $2"))),a}))}function getVersion(a){return reduce(a,(function(a,u){return a||(RegExp(u+"(?:-[\\d.]+/|(?: for [\\w-]+)?[ /-])([\\d.]+[^ ();/_-]*)","i").exec(t)||0)[1]||null}))}if(H&&(H=[H]),/\bAndroid\b/.test(q)&&!j&&(m=/\bAndroid[^;]*;(.*?)(?:Build|\) AppleWebKit)\b/i.exec(t))&&(j=trim(m[1]).replace(/^[a-z]{2}-[a-z]{2};\s*/i,"")||null),K&&!j?j=getProduct([K]):K&&j&&(j=j.replace(RegExp("^("+qualify(K)+")[-_.\\s]","i"),K+" ").replace(RegExp("^("+qualify(K)+")[-_.]?(\\w)","i"),K+" $2")),(m=/\bGoogle TV\b/.exec(j))&&(j=m[0]),/\bSimulator\b/i.test(t)&&(j=(j?j+" ":"")+"Simulator"),"Opera Mini"==Y&&/\bOPiOS\b/.test(t)&&x.push("running in Turbo/Uncompressed mode"),"IE"==Y&&/\blike iPhone OS\b/.test(t)?(K=(m=parse(t.replace(/like iPhone OS/,""))).manufacturer,j=m.product):/^iP/.test(j)?(Y||(Y="Safari"),q="iOS"+((m=/ OS ([\d_]+)/i.exec(t))?" "+m[1].replace(/_/g,"."):"")):"Konqueror"==Y&&/^Linux\b/i.test(q)?q="Kubuntu":K&&"Google"!=K&&(/Chrome/.test(Y)&&!/\bMobile Safari\b/i.test(t)||/\bVita\b/.test(j))||/\bAndroid\b/.test(q)&&/^Chrome/.test(Y)&&/\bVersion\//i.test(t)?(Y="Android Browser",q=/\bAndroid\b/.test(q)?q:"Android"):"Silk"==Y?(/\bMobi/i.test(t)||(q="Android",x.unshift("desktop mode")),/Accelerated *= *true/i.test(t)&&x.unshift("accelerated")):"UC Browser"==Y&&/\bUCWEB\b/.test(t)?x.push("speed mode"):"PaleMoon"==Y&&(m=/\bFirefox\/([\d.]+)\b/.exec(t))?x.push("identifying as Firefox "+m[1]):"Firefox"==Y&&(m=/\b(Mobile|Tablet|TV)\b/i.exec(t))?(q||(q="Firefox OS"),j||(j=m[1])):!Y||(m=!/\bMinefield\b/i.test(t)&&/\b(?:Firefox|Safari)\b/.exec(Y))?(Y&&!j&&/[\/,]|^[^(]+?\)/.test(t.slice(t.indexOf(m+"/")+8))&&(Y=null),(m=j||K||q)&&(j||K||/\b(?:Android|Symbian OS|Tablet OS|webOS)\b/.test(q))&&(Y=/[a-z]+(?: Hat)?/i.exec(/\bAndroid\b/.test(q)?q:m)+" Browser")):"Electron"==Y&&(m=(/\bChrome\/([\d.]+)\b/.exec(t)||0)[1])&&x.push("Chromium "+m),G||(G=getVersion(["(?:Cloud9|CriOS|CrMo|Edge|Edg|EdgA|EdgiOS|FxiOS|HeadlessChrome|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\d.]+$)|UCBrowser|YaBrowser)","Version",qualify(Y),"(?:Firefox|Minefield|NetFront)"])),(m=("iCab"==H&&parseFloat(G)>3?"WebKit":/\bOpera\b/.test(Y)&&(/\bOPR\b/.test(t)?"Blink":"Presto"))||/\b(?:Midori|Nook|Safari)\b/i.test(t)&&!/^(?:Trident|EdgeHTML)$/.test(H)&&"WebKit"||!H&&/\bMSIE\b/i.test(t)&&("Mac OS"==q?"Tasman":"Trident")||"WebKit"==H&&/\bPlayStation\b(?! Vita\b)/i.test(Y)&&"NetFront")&&(H=[m]),"IE"==Y&&(m=(/; *(?:XBLWP|ZuneWP)(\d+)/i.exec(t)||0)[1])?(Y+=" Mobile",q="Windows Phone "+(/\+$/.test(m)?m:m+".x"),x.unshift("desktop mode")):/\bWPDesktop\b/i.test(t)?(Y="IE Mobile",q="Windows Phone 8.x",x.unshift("desktop mode"),G||(G=(/\brv:([\d.]+)/.exec(t)||0)[1])):"IE"!=Y&&"Trident"==H&&(m=/\brv:([\d.]+)/.exec(t))&&(Y&&x.push("identifying as "+Y+(G?" "+G:"")),Y="IE",G=m[1]),B){if(function isHostType(t,a){var u=null!=t?typeof t[a]:"number";return!(/^(?:boolean|number|string|undefined)$/.test(u)||"object"==u&&!t[a])}(a,"global"))if(A&&(P=(m=A.lang.System).getProperty("os.arch"),q=q||m.getProperty("os.name")+" "+m.getProperty("os.version")),b){try{G=a.require("ringo/engine").version.join("."),Y="RingoJS"}catch(t){(m=a.system)&&m.global.system==a.system&&(Y="Narwhal",q||(q=m[0].os||null))}Y||(Y="Rhino")}else"object"==typeof a.process&&!a.process.browser&&(m=a.process)&&("object"==typeof m.versions&&("string"==typeof m.versions.electron?(x.push("Node "+m.versions.node),Y="Electron",G=m.versions.electron):"string"==typeof m.versions.nw&&(x.push("Chromium "+G,"Node "+m.versions.node),Y="NW.js",G=m.versions.nw)),Y||(Y="Node.js",P=m.arch,q=m.platform,G=(G=/[\d.]+/.exec(m.version))?G[0]:null));else getClassOf(m=a.runtime)==O?(Y="Adobe AIR",q=m.flash.system.Capabilities.os):getClassOf(m=a.phantom)==C?(Y="PhantomJS",G=(m=m.version||null)&&m.major+"."+m.minor+"."+m.patch):"number"==typeof D.documentMode&&(m=/\bTrident\/(\d+)/i.exec(t))?(G=[G,D.documentMode],(m=+m[1]+4)!=G[1]&&(x.push("IE "+G[1]+" mode"),H&&(H[1]=""),G[1]=m),G="IE"==Y?String(G[1].toFixed(1)):G[0]):"number"==typeof D.documentMode&&/^(?:Chrome|Firefox)\b/.test(Y)&&(x.push("masking as "+Y+" "+G),Y="IE",G="11.0",H=["Trident"],q="Windows");q=q&&format(q)}if(G&&(m=/(?:[ab]|dp|pre|[ab]\d+pre)(?:\d+\+?)?$/i.exec(G)||/(?:alpha|beta)(?: ?\d)?/i.exec(t+";"+(B&&h.appMinorVersion))||/\bMinefield\b/i.test(t)&&"a")&&(U=/b/i.test(m)?"beta":"alpha",G=G.replace(RegExp(m+"\\+?$"),"")+("beta"==U?L:k)+(/\d+\+?/.exec(m)||"")),"Fennec"==Y||"Firefox"==Y&&/\b(?:Android|Firefox OS|KaiOS)\b/.test(q))Y="Firefox Mobile";else if("Maxthon"==Y&&G)G=G.replace(/\.[\d.]+/,".x");else if(/\bXbox\b/i.test(j))"Xbox 360"==j&&(q=null),"Xbox 360"==j&&/\bIEMobile\b/.test(t)&&x.unshift("mobile mode");else if(!/^(?:Chrome|IE|Opera)$/.test(Y)&&(!Y||j||/Browser|Mobi/.test(Y))||"Windows CE"!=q&&!/Mobi/i.test(t))if("IE"==Y&&B)try{null===a.external&&x.unshift("platform preview")}catch(t){x.unshift("embedded")}else(/\bBlackBerry\b/.test(j)||/\bBB10\b/.test(t))&&(m=(RegExp(j.replace(/ +/g," *")+"/([.\\d]+)","i").exec(t)||0)[1]||G)?(q=((m=[m,/BB10/.test(t)])[1]?(j=null,K="BlackBerry"):"Device Software")+" "+m[0],G=null):this!=forOwn&&"Wii"!=j&&(B&&V||/Opera/.test(Y)&&/\b(?:MSIE|Firefox)\b/i.test(t)||"Firefox"==Y&&/\bOS X (?:\d+\.){2,}/.test(q)||"IE"==Y&&(q&&!/^Win/.test(q)&&G>5.5||/\bWindows XP\b/.test(q)&&G>8||8==G&&!/\bTrident\b/.test(t)))&&!I.test(m=parse.call(forOwn,t.replace(I,"")+";"))&&m.name&&(m="ing as "+m.name+((m=m.version)?" "+m:""),I.test(Y)?(/\bIE\b/.test(m)&&"Mac OS"==q&&(q=null),m="identify"+m):(m="mask"+m,Y=w?format(w.replace(/([a-z])([A-Z])/g,"$1 $2")):"Opera",/\bIE\b/.test(m)&&(q=null),B||(G=null)),H=["Presto"],x.push(m));else Y+=" Mobile";(m=(/\bAppleWebKit\/([\d.]+\+?)/i.exec(t)||0)[1])&&(m=[parseFloat(m.replace(/\.(\d)$/,".0$1")),m],"Safari"==Y&&"+"==m[1].slice(-1)?(Y="WebKit Nightly",U="alpha",G=m[1].slice(0,-1)):G!=m[1]&&G!=(m[2]=(/\bSafari\/([\d.]+\+?)/i.exec(t)||0)[1])||(G=null),m[1]=(/\b(?:Headless)?Chrome\/([\d.]+)/i.exec(t)||0)[1],537.36==m[0]&&537.36==m[2]&&parseFloat(m[1])>=28&&"WebKit"==H&&(H=["Blink"]),B&&(T||m[1])?(H&&(H[1]="like Chrome"),m=m[1]||((m=m[0])<530?1:m<532?2:m<532.05?3:m<533?4:m<534.03?5:m<534.07?6:m<534.1?7:m<534.13?8:m<534.16?9:m<534.24?10:m<534.3?11:m<535.01?12:m<535.02?"13+":m<535.07?15:m<535.11?16:m<535.19?17:m<536.05?18:m<536.1?19:m<537.01?20:m<537.11?"21+":m<537.13?23:m<537.18?24:m<537.24?25:m<537.36?26:"Blink"!=H?"27":"28")):(H&&(H[1]="like Safari"),m=(m=m[0])<400?1:m<500?2:m<526?3:m<533?4:m<534?"4+":m<535?5:m<537?6:m<538?7:m<601?8:m<602?9:m<604?10:m<606?11:m<608?12:"12"),H&&(H[1]+=" "+(m+="number"==typeof m?".x":/[.+]/.test(m)?"":"+")),"Safari"==Y&&(!G||parseInt(G)>45)?G=m:"Chrome"==Y&&/\bHeadlessChrome/i.test(t)&&x.unshift("headless")),"Opera"==Y&&(m=/\bzbov|zvav$/.exec(q))?(Y+=" ",x.unshift("desktop mode"),"zvav"==m?(Y+="Mini",G=null):Y+="Mobile",q=q.replace(RegExp(" *"+m+"$"),"")):"Safari"==Y&&/\bChrome\b/.exec(H&&H[1])?(x.unshift("desktop mode"),Y="Chrome Mobile",G=null,/\bOS X\b/.test(q)?(K="Apple",q="iOS 4.3+"):q=null):/\bSRWare Iron\b/.test(Y)&&!G&&(G=getVersion("Chrome")),G&&0==G.indexOf(m=/[\d.]+$/.exec(q))&&t.indexOf("/"+m+"-")>-1&&(q=trim(q.replace(m,""))),q&&-1!=q.indexOf(Y)&&!RegExp(Y+" OS").test(q)&&(q=q.replace(RegExp(" *"+qualify(Y)+" *"),"")),H&&!/\b(?:Avant|Nook)\b/.test(Y)&&(/Browser|Lunascape|Maxthon/.test(Y)||"Safari"!=Y&&/^iOS/.test(q)&&/\bSafari\b/.test(H[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|SRWare Iron|Vivaldi|Web)/.test(Y)&&H[1])&&(m=H[H.length-1])&&x.push(m),x.length&&(x=["("+x.join("; ")+")"]),K&&j&&j.indexOf(K)<0&&x.push("on "+K),j&&x.push((/^on /.test(x[x.length-1])?"":"on ")+j),q&&(m=/ ([\d.+]+)$/.exec(q),g=m&&"/"==q.charAt(q.length-m[0].length-1),q={architecture:32,family:m&&!g?q.replace(m[0],""):q,version:m?m[1]:null,toString:function(){var t=this.version;return this.family+(t&&!g?" "+t:"")+(64==this.architecture?" 64-bit":"")}}),(m=/\b(?:AMD|IA|Win|WOW|x86_|x)64\b/i.exec(P))&&!/\bi686\b/i.test(P)?(q&&(q.architecture=64,q.family=q.family.replace(RegExp(" *"+m),"")),Y&&(/\bWOW64\b/i.test(t)||B&&/\w(?:86|32)$/.test(h.cpuClass||h.platform)&&!/\bWin64; x64\b/i.test(t))&&x.unshift("32-bit")):q&&/^OS X/.test(q.family)&&"Chrome"==Y&&parseFloat(G)>=39&&(q.architecture=64),t||(t=null);var W={};return W.description=t,W.layout=H&&H[0],W.manufacturer=K,W.name=Y,W.prerelease=U,W.product=j,W.ua=t,W.version=Y&&G,W.os=q||{architecture:null,family:null,version:null,toString:function(){return"null"}},W.parse=parse,W.toString=function toStringPlatform(){return this.description||""},W.version&&x.unshift(G),W.name&&x.unshift(Y),q&&Y&&(q!=String(q).split(" ")[0]||q!=Y.split(" ")[0]&&!j)&&x.push(j?"("+q+")":"on "+q),x.length&&(W.description=x.join(" ")),W}();h&&E?forOwn(O,(function(t,a){h[a]=t})):_.platform=O}).call(a)}));function getSystemInfoFn(){var t,a,u=bh.version||"";if(isElectron())try{var _=navigator.userAgent.match(/Electron\/([\d.]+\d+)/);_&&_[1]&&"string"==typeof _[1]&&(u=_[1])}catch(t){}return{os:(null===(t=bh.os)||void 0===t?void 0:t.family)||"",osVer:(null===(a=bh.os)||void 0===a?void 0:a.version)||"",browser:bh.name||"",browserVer:bh.version||"",libEnv:"BROWSER",hostEnv:isElectron()?"Electron":isMobile()?"H5":isBrowser()?"BROWSER":"Unset",hostEnvEnum:isElectron()?5:isMobile()?101:isBrowser()?100:0,hostEnvVer:u,userAgent:navigator&&navigator.userAgent,model:u,manufactor:bh.name||""}}var kh=null,Lh=null,Dh={getNetworkStatus:function getNetworkStatus(){return _a.resolve({net_type:0,net_connect:"undefined"==typeof navigator||"boolean"!=typeof navigator.onLine||navigator.onLine})},onNetworkStatusChange:function onNetworkStatusChange(t){kh=function onlineListener(){t({isConnected:!0,networkType:0})},Lh=function offlineListener(){t({isConnected:!1,networkType:0})},window.addEventListener("online",kh),window.addEventListener("offline",Lh)},offNetworkStatusChange:function offNetworkStatusChange(){kh&&window.removeEventListener("online",kh),Lh&&window.removeEventListener("offline",Lh),kh=null,Lh=null}},Vh=Ar.find,wh="find",Ph=!0;wh in[]&&Array(1).find((function(){Ph=!1})),_export({target:"Array",proto:!0,forced:Ph},{find:function find(t){return Vh(this,t,arguments.length>1?arguments[1]:void 0)}});var xh=entryVirtual("Array").find,Uh=Array.prototype,find=function(t){var a=t.find;return t===Uh||j(Uh,t)&&a===Uh.find?xh:a},Fh="log",Bh=function(){function IDB(t,a){this.db=null,this.stores=[],this.name=t,this.version=a}var t=IDB.prototype;return t.setName=function setName(t){this.name=t},t.getDB=function getDB(){if(!this.db)throw new Error("DB not ready");return this.db},t.getStore=function getStore(t){var a,u=find(a=this.stores).call(a,(function(a){return a.storeName===t}));if(!u)throw new Error("LogStorage: store not found. "+t);return u},t.open=function open(){var t=this,a=window.indexedDB.open(this.name,this.version);return new _a((function(u,_){a.onerror=function(t){var a=t.target;_(a.error)},a.onsuccess=function(a){var _,h,E=a.target;t.db=E.result,t.db.removeEventListener("close",bind$1(_=t.triggerDBCloseEvt).call(_,t)),t.db.addEventListener("close",bind$1(h=t.triggerDBCloseEvt).call(h,t)),t.stores.push(new Gh(Fh,t)),u()},a.onupgradeneeded=function(a){var u=a.target;t.upgradeDBBySchema(u)}}))},t.triggerDBCloseEvt=function triggerDBCloseEvt(){try{this.db&&this.db.close(),this.db=null}catch(t){}this.open()},t.upgradeDBBySchema=function upgradeDBBySchema(t){var a=t.result,u=t.transaction&&a.objectStoreNames.contains(Fh)?t.transaction.objectStore(Fh):a.createObjectStore(Fh,{keyPath:"id",autoIncrement:!0});try{u.index("time")}catch(t){u.createIndex("time","time",{unique:!1})}},t.close=function close(){var t;this.db&&(this.db.removeEventListener("close",bind$1(t=this.triggerDBCloseEvt).call(t,this)),this.db.close(),this.stores=[],this.db=null)},IDB}(),Gh=function(){function IDBStore(t,a){this.idb=null,this.storeName=t,this.idb=a}var t=IDBStore.prototype;return t.getDB=function getDB(){if(!this.idb)throw new Error("DB not ready");return this.idb.getDB()},t.getStoreName=function getStoreName(){return this.storeName},t.bulkCreate=function bulkCreate(t){var a=this.getDB(),u=this.getStoreName(),_=a.transaction(u,"readwrite"),h=_.objectStore(u);return forEach$1(t).call(t,(function(t){h.add(t)})),new _a((function(t,a){_.oncomplete=function(){t()},_.onerror=function(t){var u=t.target;a(u.error)},_.onabort=function(t){var u=t.target;u.error instanceof Error?a(u.error):a(new Error("TransactionAborted"))}}))},t.bulkDelete=function bulkDelete(t){var a=t.keyName,u=t.lower,_=t.upper,h=t.lowerOpen,E=void 0!==h&&h,m=t.upperOpen,g=void 0!==m&&m,I=IDBKeyRange.bound(u,_,E,g),T=this.getDB(),N=this.getStoreName(),M=T.transaction(N,"readwrite"),O=M.objectStore(N).index(a).openCursor(I),S=0;return O.onsuccess=function(t){var a=t.target.result;a&&(a.delete(),S++,a.continue())},new _a((function(t,a){M.oncomplete=function(){t(S)},M.onerror=function(t){var u=t.target;a(u.error)},M.onabort=function(t){var u=t.target;u.error instanceof Error?a(u.error):a(new Error("TransactionAborted"))}}))},t.readAllAndClear=function readAllAndClear(){var t=this.getDB(),a=this.getStoreName(),u=t.transaction(a,"readwrite").objectStore(a);if(!u.getAll)throw new Error("IDBExtract not support");var _=u.getAll();return new _a((function(t,a){_.onsuccess=function(a){var _=a.target;u.clear(),t(_.result)},_.onerror=function(t){var u=t.target;a(u.error)}}))},IDBStore}(),Hh=function(){function LogStorageImpl(t){void 0===t&&(t="nim-logs"),this.idb=new Bh(t,1)}var t=LogStorageImpl.prototype;return t.open=function open(t){return __awaiter(this,void 0,void 0,Sa.mark((function _callee(){var a;return Sa.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return t&&this.idb.setName(t),u.next=3,this.idb.open();case 3:return a=this.idb.getStore(Fh),u.prev=4,u.next=7,a.bulkDelete({keyName:"time",lower:0,upper:Ga()-2592e5});case 7:u.next=11;break;case 9:u.prev=9,u.t0=u.catch(4);case 11:case"end":return u.stop()}}),_callee,this,[[4,9]])})))},t.close=function close(){this.idb.close()},t.addLogs=function addLogs(t){return this.idb.getStore(Fh).bulkCreate(t)},t.extractLogs=function extractLogs(){return __awaiter(this,void 0,void 0,Sa.mark((function _callee2(){var t,a,u,_,h;return Sa.wrap((function _callee2$(E){for(;;)switch(E.prev=E.next){case 0:return a=this.idb.getStore(Fh),E.next=3,a.readAllAndClear();case 3:if(0!==(u=E.sent).length){E.next=6;break}return E.abrupt("return","");case 6:return _=reduce(u).call(u,(function(t,a){var u=a.iid;return t[u]||(t[u]=[]),t[u].push(a),t}),{}),h=map$7(t=Za(_)).call(t,(function(t){var a=_[t];return"==========iid:"+t+"==========\n "+map$7(a).call(a,(function(t){return t.text})).join("\n")})).join("\n"),E.abrupt("return",new File([h],"nim-logs.txt",{type:"text/plain"}));case 9:case"end":return E.stop()}}),_callee2,this)})))},LogStorageImpl}();!function setAdapters(t){merge$1(Es,t())}((function getAdapter(){return{setLogger:setLogger,platform:"BROWSER",localStorage:window.localStorage,request:dh,WebSocket:window.WebSocket,uploadFile:uploadFileFn,getFileUploadInformation:getFileUploadInformationFn,getSystemInfo:getSystemInfoFn,net:Dh,logStorage:Hh}})),t.V2NIMChatroomConst=Bd,t.default=uh,Object.defineProperty(t,"__esModule",{value:!0})}));
