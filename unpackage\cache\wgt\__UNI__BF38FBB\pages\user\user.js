"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var M=Object.create;var k=Object.defineProperty;var N=Object.getOwnPropertyDescriptor;var z=Object.getOwnPropertyNames;var D=Object.getPrototypeOf,A=Object.prototype.hasOwnProperty;var R=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports);var O=(e,t,i,s)=>{if(t&&typeof t=="object"||typeof t=="function")for(let o of z(t))!A.call(e,o)&&o!==i&&k(e,o,{get:()=>t[o],enumerable:!(s=N(t,o))||s.enumerable});return e};var x=(e,t,i)=>(i=e!=null?M(D(e)):{},O(t||!e||!e.__esModule?k(i,"default",{value:e,enumerable:!0}):i,e));var w=(e,t,i)=>new Promise((s,o)=>{var a=c=>{try{p(i.next(c))}catch(h){o(h)}},l=c=>{try{p(i.throw(c))}catch(h){o(h)}},p=c=>c.done?s(c.value):Promise.resolve(c.value).then(a,l);p((i=i.apply(e,t)).next())});var g=R((ne,v)=>{v.exports=Vue});var oe=x(g());function C(e,t,...i){uni.__log__?uni.__log__(e,t,...i):console[e].apply(console,[...i,t])}var W="https://diytflservtest.eykj.cn",j="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb3JwX3Byb2R1Y3QiOiJjYW55aW4iLCJ0eXBlcyI6ImlzdiIsImNvcnBpZCI6ImRpbmdiOTYxNGRmOTQzNDJmNTcwYTEzMjBkY2IyNWU5MTM1MSIsImNvcnBfbmFtZSI6Ilx1NGUwMFx1NGUwMFx1NzlkMVx1NjI4MFx1NTE4NVx1OTBlOFx1NWYwMFx1NTNkMVx1NWU3M1x1NTNmMCIsInVzZXJpZCI6IjMzMzYzNTMzMjEyNDIxMDY2NCIsIm5hbWUiOiJcdTVmMjBcdTYzMmZcdTUzMTcxIiwic3RhZmZfbmFtZSI6Ilx1NWYyMFx1NjMyZlx1NTMxNzEiLCJzdGFmZmlkIjoiMzMzNjM1MzMyMTI0MjEwNjY0IiwiZGVwdF9pZF9saXN0IjoiWzI5OTkzOTAxNywgNDg1Mzg5NTMwLCA0ODYzNzAwMTgsIDY2NDk0NjI5OV0iLCJwYXJlbnRfaWRfbGlzdCI6WzEsMjk5OTM5MDE3XSwiZGluaW5naGFsbF9pZCI6OTMsImRpbmluZ2hhbGxfdGl0bGUiOiJcdTk5MTBcdTUzODUtXHU2YmI1IiwiYWRtaW4iOjF9.mLy2KnEIjCrp7Y-UHA8SrtBjfbdR7W2EvQtBhlE44bk",F="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBKPfRCrkJ367IiaaxUjR0xIyO68pMwPEVWL/OqI78dtFPPW+zQQ1wb7YGk+pMJV2oa64cC5xZCLzPNHV6LaZe4hIxlxrcGed3aB1cLLNz0ujj1OplHD8PA2Hwlcz1bOo6U7VsQA2tYaXS9xGlBGgqNoGD3KHtDNYWiApA8FyJYwIDAQAB",S={COMMONAPI:W,publicKey:F,token:j},f="_mallStore";function G(e,t,i){uni.setStorageSync(e,t);var s=parseInt(i);if(s>0){var o=Date.parse(new Date);o=o/1e3+s,uni.setStorageSync(e+f,o+"")}else uni.removeStorageSync(e+f)}function Y(e,t){var i=parseInt(uni.getStorageSync(e+f));if(i&&parseInt(i)<Date.parse(new Date)/1e3)return t||!1;var s=uni.getStorageSync(e);return s||((t==null||t=="")&&(t=!1),t)}function L(e){uni.removeStorageSync(e),uni.removeStorageSync(e+f)}function J(){uni.clearStorageSync()}var P={put:G,get:Y,remove:L,clear:J};function U(e,t){try{uni.setStorageSync(e,t)}catch(i){}}function E(e){try{let t=uni.getStorageSync(e);if(t)return t}catch(t){}}var u={setData:U,getData:E};function V(e){var t=null;if(e){var i=e.split(".");if(i.length>1?t=S[i[0]][i[1]]||null:t=S[e]||null,t==null){let s=P.get("web_config");s&&(i.length>1?t=s[i[0]][i[1]]||null:t=s[e]||null)}}return t}function Q(e,t,i){return i=u.getData("Authorization"),e=this.config("COMMONAPI")+e,new Promise((s,o)=>{uni.request({url:e,data:t,method:"POST",header:{Authorization:i,serviceType:3},success:function(a){s.call(self,a.data)},fail:function(a){o.call(self,a)},complete:function(){}})})}function Z(e,t,i){return i=u.getData("Authorization"),e="https://fileapitest.eykj.cn"+e,new Promise((s,o)=>{uni.request({url:e,data:t,method:"POST",header:{Authorization:i},success:function(a){s.call(self,a.data)},fail:function(a){o.call(self,a)},complete:function(){}})})}function H(e,t,i){return i=u.getData("Authorization"),e=this.config("COMMONAPI")+e,new Promise((s,o)=>{uni.request({url:e,data:t,method:"GET",header:{Authorization:i,serviceType:3},success:function(a){s.call(self,a.data)},fail:function(a){o.call(self,a)}})})}var T={config:V,get:H,post:Q,postimg:Z};var n=x(g());var d=(e,t)=>{let i=e.__vccOpts||e;for(let[s,o]of t)i[s]=o;return i};var y="/static/image/left.png",q={header_cont:{"":{position:"fixed",top:0,left:0,width:100,zIndex:100}},"custom-header":{"":{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",paddingTop:"76rpx",height:"80rpx",backgroundColor:"rgba(255,255,255,0.0362)",position:"relative"}},left:{".custom-header ":{position:"absolute",height:"48rpx",width:"48rpx",left:"12rpx"}},iconfont:{"":{zIndex:100,height:"48rpx",width:"48rpx"}},title:{".custom-header ":{fontSize:"34rpx",fontWeight:"600",flex:1,color:"#ffffff",textAlign:"center"}},right:{".custom-header ":{position:"absolute",color:"#ffffff",right:15}},"icon-arrowleft":{"":{fontSize:20,color:"#ffffff",lineHeight:50}}},X={props:{title:{type:String,default:""},showBack:{type:Boolean,default:!1},showBack2:{type:Boolean,default:!1},showBack3:{type:Boolean,default:!1},rightContent:{type:String,default:""},rightType:{type:String,default:""},rightClick:{type:Function,default:null}},methods:{handleBack(){uni.navigateBack({delta:1})},handleBack2(){uni.switchTab({url:"/pages/apply/apply"})},handleBack3(){uni.switchTab({url:"/pages/user/user"})},handleRightClick(){this.rightClick&&typeof this.rightClick=="function"&&this.rightClick()}}};function K(e,t,i,s,o,a){return(0,n.openBlock)(),(0,n.createElementBlock)("view",{class:"header_cont",renderWhole:!0},[(0,n.createElementVNode)("view",{class:"custom-header"},[i.showBack?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:0,class:"left"},[(0,n.createElementVNode)("u-image",{onClick:t[0]||(t[0]=(...l)=>a.handleBack&&a.handleBack(...l)),class:"iconfont",src:y,mode:""})])):(0,n.createCommentVNode)("",!0),i.showBack2?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:1,class:"left"},[(0,n.createElementVNode)("u-image",{onClick:t[1]||(t[1]=(...l)=>a.handleBack2&&a.handleBack2(...l)),class:"iconfont",src:y,mode:""})])):(0,n.createCommentVNode)("",!0),i.showBack3?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:2,class:"left"},[(0,n.createElementVNode)("u-image",{onClick:t[2]||(t[2]=(...l)=>a.handleBack3&&a.handleBack3(...l)),class:"iconfont",src:y,mode:""})])):(0,n.createCommentVNode)("",!0),(0,n.createElementVNode)("view",{class:"title"},[(0,n.createElementVNode)("u-text",null,(0,n.toDisplayString)(i.title),1)]),i.rightContent||e.$slots.right?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:3,class:"right"},[i.rightType=="text"?((0,n.openBlock)(),(0,n.createElementBlock)("u-text",{key:0,onClick:t[3]||(t[3]=(...l)=>a.handleRightClick&&a.handleRightClick(...l))},(0,n.toDisplayString)(i.rightContent),1)):i.rightType=="slot"?((0,n.openBlock)(),(0,n.createElementBlock)("view",{key:1},[(0,n.renderSlot)(e.$slots,"right")])):(0,n.createCommentVNode)("",!0)])):(0,n.createCommentVNode)("",!0)])])}var _=d(X,[["render",K],["styles",[q]]]);var r=x(g());var B="/static/image/BG.png";var b="/static/image/user/right.png",$="/static/image/user/setting.png",ee={container:{"":{flex:1,position:"relative"}},"background-image":{"":{position:"absolute",top:0,left:0}},"content-wrapper":{"":{position:"absolute",top:0,left:0,right:0,bottom:0,flex:1}},content:{"":{paddingTop:"52rpx",paddingLeft:"26rpx",paddingRight:"26rpx"}},header:{"":{paddingTop:"80rpx",fontSize:"34rpx",fontWeight:"600",textAlign:"center",display:"flex",justifyContent:"center",alignItems:"center","!color":"#ff6600"}},"user-info-card":{"":{paddingTop:"32rpx",paddingRight:"32rpx",paddingBottom:"32rpx",paddingLeft:"32rpx",backgroundColor:"rgba(255,255,255,0.0362)",borderWidth:"1rpx",borderStyle:"solid",borderColor:"rgba(255,255,255,0.0972)",borderRadius:"12rpx",flexDirection:"row",alignItems:"center",justifyContent:"space-between"}},"user-info":{"":{flexDirection:"row",alignItems:"center"}},"avatar-content":{"":{width:"104rpx",height:"104rpx",paddingTop:"4rpx",paddingRight:"4rpx",paddingBottom:"4rpx",paddingLeft:"4rpx",backgroundColor:"rgba(84,146,247,0.6)",borderRadius:"16rpx"}},avatar:{"":{width:"96rpx",height:"96rpx",borderRadius:"16rpx"}},"info-content":{"":{marginLeft:"24rpx"}},"content-name":{"":{flexDirection:"row",alignItems:"center",marginBottom:"24rpx"}},username:{"":{fontSize:"40rpx",marginRight:"28rpx",color:"rgba(255,255,255,0.85)"}},surealname:{"":{paddingTop:"8rpx",paddingRight:"8rpx",paddingBottom:"8rpx",paddingLeft:"8rpx",fontSize:"24rpx",borderRadius:"12rpx",backgroundColor:"rgba(22,119,255,0.12)",color:"#1677FF"}},realname:{"":{paddingTop:"8rpx",paddingRight:"8rpx",paddingBottom:"8rpx",paddingLeft:"8rpx",fontSize:"24rpx",borderRadius:"12rpx",backgroundColor:"rgba(255,255,255,0.12)",color:"rgba(255,255,255,0.85)"}},phone:{"":{fontSize:"28rpx",color:"rgba(255,255,255,0.65)"}},"menu-list":{"":{marginTop:"44rpx",backgroundColor:"rgba(255,255,255,0.0362)",borderWidth:"1rpx",borderStyle:"solid",borderColor:"rgba(255,255,255,0.0972)",borderRadius:"12rpx"}},"menu-item":{"":{height:"116rpx",flexDirection:"row",alignItems:"center",justifyContent:"space-between",paddingLeft:"32rpx",paddingRight:"32rpx",borderBottomWidth:"1rpx",borderBottomStyle:"solid",borderBottomColor:"rgba(167,169,172,0.15)","borderBottomWidth:last-child":0}},"menu-item-left":{"":{flexDirection:"row",alignItems:"center"}},"menu-icon":{"":{width:"40rpx",height:"40rpx",marginRight:"32rpx"}},"menu-text":{"":{fontSize:"34rpx",color:"rgba(255,255,255,0.85)"}},"arrow-right":{"":{width:"60rpx",height:"60rpx"}}},te={components:{customHeader:_},data(){return{screenWidth:0,screenHeight:0,userInfo:{},menuItems:[{icon:"/static/image/user/security.png",text:"\u5B89\u5168\u4E2D\u5FC3",path:"/pages/user/components/security"}]}},created(){let e=uni.getSystemInfoSync();this.screenWidth=e.windowWidth,this.screenHeight=e.windowHeight},onLoad(){return w(this,null,function*(){yield this.loadUserData()})},onShow(){this.userInfo=u.getData("userinfo")},methods:{loadUserData(){return w(this,null,function*(){try{let e=yield T.get("/personal/get_info");e.status===0?(u.setData("userinfo",e.data),this.userInfo=u.getData("userinfo")):uni.showToast({title:"\u7528\u6237\u4FE1\u606F\u83B7\u53D6\u5931\u8D25",icon:"none",duration:2e3})}catch(e){C("error","at pages/user/user.nvue:130","Error fetching user info:",e),uni.showToast({title:"\u7F51\u7EDC\u4E0D\u7A33\u5B9A\uFF0C\u8BF7\u7A0D\u540E\u518D\u8BD5",icon:"none",duration:2e3})}})},formatPhone(e){return e?e.replace(/(\d{3})\d{4}(\d{4})/,"$1****$2"):""},navigateToUserDetail(){uni.navigateTo({url:"/pages/user/components/detail"})},handleMenuClick(e){uni.navigateTo({url:e.path})},setting(){uni.navigateTo({url:"/pages/user/components/setting"})}}};function re(e,t,i,s,o,a){return(0,r.openBlock)(),(0,r.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,r.createElementVNode)("view",{class:"container"},[(0,r.createElementVNode)("u-image",{class:"background-image",src:B,mode:"aspectFill",style:(0,r.normalizeStyle)({width:o.screenWidth+"px",height:o.screenHeight+"px"})},null,4),(0,r.createElementVNode)("view",{class:"content-wrapper"},[(0,r.createElementVNode)("view",{class:"header",background:"transparent",style:{color:"#fff"}},[(0,r.createElementVNode)("u-text",{style:{"font-size":"34rpx",color:"rgba(255, 255, 255, 0.85)"}})]),(0,r.createElementVNode)("scroll-view",{class:"content",scrollY:"true"},[(0,r.createElementVNode)("view",{class:"user-info-card",onClick:t[0]||(t[0]=(...l)=>a.navigateToUserDetail&&a.navigateToUserDetail(...l))},[(0,r.createElementVNode)("view",{class:"user-info"},[(0,r.createElementVNode)("view",{class:"avatar-content"},[(0,r.createElementVNode)("u-image",{class:"avatar",src:o.userInfo.profilePic||"/static/logo.png",mode:"aspectFill"},null,8,["src"])]),(0,r.createElementVNode)("view",{class:"info-content"},[(0,r.createElementVNode)("view",{class:"content-name"},[(0,r.createElementVNode)("u-text",{class:"username"},(0,r.toDisplayString)(o.userInfo.name||"\u672A\u767B\u5F55"),1),o.userInfo.authentication==1?((0,r.openBlock)(),(0,r.createElementBlock)("u-text",{key:0,class:"surealname"},"\u5DF2\u5B9E\u540D")):((0,r.openBlock)(),(0,r.createElementBlock)("u-text",{key:1,class:"realname"},"\u672A\u5B9E\u540D"))]),o.userInfo.phoneNumber==""?((0,r.openBlock)(),(0,r.createElementBlock)("u-text",{key:0,class:"phone",style:{color:"rgba(255,255,255,0.65)"}},"\u672A\u7ED1\u5B9A\u624B\u673A\u53F7")):((0,r.openBlock)(),(0,r.createElementBlock)("u-text",{key:1,class:"phone"},(0,r.toDisplayString)(a.formatPhone(o.userInfo.phoneNumber)),1))])]),(0,r.createElementVNode)("u-image",{class:"arrow-right",src:b,mode:"aspectFit"})]),(0,r.createElementVNode)("view",{class:"menu-list"},[((0,r.openBlock)(!0),(0,r.createElementBlock)(r.Fragment,null,(0,r.renderList)(o.menuItems,(l,p)=>((0,r.openBlock)(),(0,r.createElementBlock)("view",{key:p,class:"menu-item",onClick:c=>a.handleMenuClick(l)},[(0,r.createElementVNode)("view",{class:"menu-item-left"},[(0,r.createElementVNode)("u-image",{class:"menu-icon",src:l.icon,mode:"aspectFit"},null,8,["src"]),(0,r.createElementVNode)("u-text",{class:"menu-text"},(0,r.toDisplayString)(l.text),1)]),(0,r.createElementVNode)("u-image",{class:"arrow-right",src:b,mode:"aspectFit"})],8,["onClick"]))),128))]),(0,r.createElementVNode)("view",{class:"menu-list"},[(0,r.createElementVNode)("view",{class:"menu-item",onClick:t[1]||(t[1]=(...l)=>a.setting&&a.setting(...l))},[(0,r.createElementVNode)("view",{class:"menu-item-left"},[(0,r.createElementVNode)("u-image",{class:"menu-icon",src:$,mode:"aspectFit"}),(0,r.createElementVNode)("u-text",{class:"menu-text"},"\u8BBE\u7F6E")]),(0,r.createElementVNode)("u-image",{class:"arrow-right",src:b,mode:"aspectFit"})])])])])])])}var m=d(te,[["render",re],["styles",[ee]]]);var I=plus.webview.currentWebview();if(I){let e=parseInt(I.id),t="pages/user/user",i={};try{i=JSON.parse(I.__query__)}catch(o){}m.mpType="page";let s=Vue.createPageApp(m,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:t,__pageQuery:i});s.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...m.styles||[]])),s.mount("#root")}})();
