"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var zt=Object.create;var Ge=Object.defineProperty;var Dt=Object.getOwnPropertyDescriptor;var Qt=Object.getOwnPropertyNames,ut=Object.getOwnPropertySymbols,Ht=Object.getPrototypeOf,ft=Object.prototype.hasOwnProperty,Wt=Object.prototype.propertyIsEnumerable;var ct=(e,f,t)=>f in e?Ge(e,f,{enumerable:!0,configurable:!0,writable:!0,value:t}):e[f]=t,ne=(e,f)=>{for(var t in f||(f={}))ft.call(f,t)&&ct(e,t,f[t]);if(ut)for(var t of ut(f))Wt.call(f,t)&&ct(e,t,f[t]);return e};var Vt=(e,f)=>()=>(f||e((f={exports:{}}).exports,f),f.exports);var qt=(e,f,t,a)=>{if(f&&typeof f=="object"||typeof f=="function")for(let m of Qt(f))!ft.call(e,m)&&m!==t&&Ge(e,m,{get:()=>f[m],enumerable:!(a=Dt(f,m))||a.enumerable});return e};var ze=(e,f,t)=>(t=e!=null?zt(Ht(e)):{},qt(f||!e||!e.__esModule?Ge(t,"default",{value:e,enumerable:!0}):t,e));var ke=(e,f,t)=>new Promise((a,m)=>{var r=h=>{try{p(t.next(h))}catch(c){m(c)}},i=h=>{try{p(t.throw(h))}catch(c){m(c)}},p=h=>h.done?a(h.value):Promise.resolve(h.value).then(r,i);p((t=t.apply(e,f)).next())});var Ne=Vt((rs,pt)=>{pt.exports=Vue});var ns=ze(Ne());function De(e){return weex.requireModule(e)}function fe(e,f,...t){uni.__log__?uni.__log__(e,f,...t):console[e].apply(console,[...t,f])}function _e(e,f){return typeof e=="string"?f:e}var we=ze(Ne());var Se=(e,f)=>{let t=e.__vccOpts||e;for(let[a,m]of f)t[a]=m;return t};var Kt={"uicon-level":"\uE693","uicon-column-line":"\uE68E","uicon-checkbox-mark":"\uE807","uicon-folder":"\uE7F5","uicon-movie":"\uE7F6","uicon-star-fill":"\uE669","uicon-star":"\uE65F","uicon-phone-fill":"\uE64F","uicon-phone":"\uE622","uicon-apple-fill":"\uE881","uicon-chrome-circle-fill":"\uE885","uicon-backspace":"\uE67B","uicon-attach":"\uE632","uicon-cut":"\uE948","uicon-empty-car":"\uE602","uicon-empty-coupon":"\uE682","uicon-empty-address":"\uE646","uicon-empty-favor":"\uE67C","uicon-empty-permission":"\uE686","uicon-empty-news":"\uE687","uicon-empty-search":"\uE664","uicon-github-circle-fill":"\uE887","uicon-rmb":"\uE608","uicon-person-delete-fill":"\uE66A","uicon-reload":"\uE788","uicon-order":"\uE68F","uicon-server-man":"\uE6BC","uicon-search":"\uE62A","uicon-fingerprint":"\uE955","uicon-more-dot-fill":"\uE630","uicon-scan":"\uE662","uicon-share-square":"\uE60B","uicon-map":"\uE61D","uicon-map-fill":"\uE64E","uicon-tags":"\uE629","uicon-tags-fill":"\uE651","uicon-bookmark-fill":"\uE63B","uicon-bookmark":"\uE60A","uicon-eye":"\uE613","uicon-eye-fill":"\uE641","uicon-mic":"\uE64A","uicon-mic-off":"\uE649","uicon-calendar":"\uE66E","uicon-calendar-fill":"\uE634","uicon-trash":"\uE623","uicon-trash-fill":"\uE658","uicon-play-left":"\uE66D","uicon-play-right":"\uE610","uicon-minus":"\uE618","uicon-plus":"\uE62D","uicon-info":"\uE653","uicon-info-circle":"\uE7D2","uicon-info-circle-fill":"\uE64B","uicon-question":"\uE715","uicon-error":"\uE6D3","uicon-close":"\uE685","uicon-checkmark":"\uE6A8","uicon-android-circle-fill":"\uE67E","uicon-android-fill":"\uE67D","uicon-ie":"\uE87B","uicon-IE-circle-fill":"\uE889","uicon-google":"\uE87A","uicon-google-circle-fill":"\uE88A","uicon-setting-fill":"\uE872","uicon-setting":"\uE61F","uicon-minus-square-fill":"\uE855","uicon-plus-square-fill":"\uE856","uicon-heart":"\uE7DF","uicon-heart-fill":"\uE851","uicon-camera":"\uE7D7","uicon-camera-fill":"\uE870","uicon-more-circle":"\uE63E","uicon-more-circle-fill":"\uE645","uicon-chat":"\uE620","uicon-chat-fill":"\uE61E","uicon-bag-fill":"\uE617","uicon-bag":"\uE619","uicon-error-circle-fill":"\uE62C","uicon-error-circle":"\uE624","uicon-close-circle":"\uE63F","uicon-close-circle-fill":"\uE637","uicon-checkmark-circle":"\uE63D","uicon-checkmark-circle-fill":"\uE635","uicon-question-circle-fill":"\uE666","uicon-question-circle":"\uE625","uicon-share":"\uE631","uicon-share-fill":"\uE65E","uicon-shopping-cart":"\uE621","uicon-shopping-cart-fill":"\uE65D","uicon-bell":"\uE609","uicon-bell-fill":"\uE640","uicon-list":"\uE650","uicon-list-dot":"\uE616","uicon-zhihu":"\uE6BA","uicon-zhihu-circle-fill":"\uE709","uicon-zhifubao":"\uE6B9","uicon-zhifubao-circle-fill":"\uE6B8","uicon-weixin-circle-fill":"\uE6B1","uicon-weixin-fill":"\uE6B2","uicon-twitter-circle-fill":"\uE6AB","uicon-twitter":"\uE6AA","uicon-taobao-circle-fill":"\uE6A7","uicon-taobao":"\uE6A6","uicon-weibo-circle-fill":"\uE6A5","uicon-weibo":"\uE6A4","uicon-qq-fill":"\uE6A1","uicon-qq-circle-fill":"\uE6A0","uicon-moments-circel-fill":"\uE69A","uicon-moments":"\uE69B","uicon-qzone":"\uE695","uicon-qzone-circle-fill":"\uE696","uicon-baidu-circle-fill":"\uE680","uicon-baidu":"\uE681","uicon-facebook-circle-fill":"\uE68A","uicon-facebook":"\uE689","uicon-car":"\uE60C","uicon-car-fill":"\uE636","uicon-warning-fill":"\uE64D","uicon-warning":"\uE694","uicon-clock-fill":"\uE638","uicon-clock":"\uE60F","uicon-edit-pen":"\uE612","uicon-edit-pen-fill":"\uE66B","uicon-email":"\uE611","uicon-email-fill":"\uE642","uicon-minus-circle":"\uE61B","uicon-minus-circle-fill":"\uE652","uicon-plus-circle":"\uE62E","uicon-plus-circle-fill":"\uE661","uicon-file-text":"\uE663","uicon-file-text-fill":"\uE665","uicon-pushpin":"\uE7E3","uicon-pushpin-fill":"\uE86E","uicon-grid":"\uE673","uicon-grid-fill":"\uE678","uicon-play-circle":"\uE647","uicon-play-circle-fill":"\uE655","uicon-pause-circle-fill":"\uE654","uicon-pause":"\uE8FA","uicon-pause-circle":"\uE643","uicon-eye-off":"\uE648","uicon-eye-off-outline":"\uE62B","uicon-gift-fill":"\uE65C","uicon-gift":"\uE65B","uicon-rmb-circle-fill":"\uE657","uicon-rmb-circle":"\uE677","uicon-kefu-ermai":"\uE656","uicon-server-fill":"\uE751","uicon-coupon-fill":"\uE8C4","uicon-coupon":"\uE8AE","uicon-integral":"\uE704","uicon-integral-fill":"\uE703","uicon-home-fill":"\uE964","uicon-home":"\uE965","uicon-hourglass-half-fill":"\uE966","uicon-hourglass":"\uE967","uicon-account":"\uE628","uicon-plus-people-fill":"\uE626","uicon-minus-people-fill":"\uE615","uicon-account-fill":"\uE614","uicon-thumb-down-fill":"\uE726","uicon-thumb-down":"\uE727","uicon-thumb-up":"\uE733","uicon-thumb-up-fill":"\uE72F","uicon-lock-fill":"\uE979","uicon-lock-open":"\uE973","uicon-lock-opened-fill":"\uE974","uicon-lock":"\uE97A","uicon-red-packet-fill":"\uE690","uicon-photo-fill":"\uE98B","uicon-photo":"\uE98D","uicon-volume-off-fill":"\uE659","uicon-volume-off":"\uE644","uicon-volume-fill":"\uE670","uicon-volume":"\uE633","uicon-red-packet":"\uE691","uicon-download":"\uE63C","uicon-arrow-up-fill":"\uE6B0","uicon-arrow-down-fill":"\uE600","uicon-play-left-fill":"\uE675","uicon-play-right-fill":"\uE676","uicon-rewind-left-fill":"\uE679","uicon-rewind-right-fill":"\uE67A","uicon-arrow-downward":"\uE604","uicon-arrow-leftward":"\uE601","uicon-arrow-rightward":"\uE603","uicon-arrow-upward":"\uE607","uicon-arrow-down":"\uE60D","uicon-arrow-right":"\uE605","uicon-arrow-left":"\uE60E","uicon-arrow-up":"\uE606","uicon-skip-back-left":"\uE674","uicon-skip-forward-right":"\uE672","uicon-rewind-right":"\uE66F","uicon-rewind-left":"\uE671","uicon-arrow-right-double":"\uE68D","uicon-arrow-left-double":"\uE68C","uicon-wifi-off":"\uE668","uicon-wifi":"\uE667","uicon-empty-data":"\uE62F","uicon-empty-history":"\uE684","uicon-empty-list":"\uE68B","uicon-empty-page":"\uE627","uicon-empty-order":"\uE639","uicon-man":"\uE697","uicon-woman":"\uE69C","uicon-man-add":"\uE61C","uicon-man-add-fill":"\uE64C","uicon-man-delete":"\uE61A","uicon-man-delete-fill":"\uE66A","uicon-zh":"\uE70A","uicon-en":"\uE692"},Ce=e=>e,ht="3",Re={v:ht,version:ht,type:["primary","success","info","error","warning"],color:{"u-primary":"#2979ff","u-warning":"#ff9900","u-success":"#19be6b","u-error":"#fa3534","u-info":"#909399","u-main-color":"#303133","u-content-color":"#606266","u-tips-color":"#909399","u-light-color":"#c0c4cc","up-primary":"#2979ff","up-warning":"#ff9900","up-success":"#19be6b","up-error":"#fa3534","up-info":"#909399","up-main-color":"#303133","up-content-color":"#606266","up-tips-color":"#909399","up-light-color":"#c0c4cc"},unit:"px",interceptor:{navbarLeftClick:null}},Yt={toast:10090,noNetwork:10080,popup:10075,mask:10070,navbar:980,topTips:975,sticky:970,indexListSticky:965},_t={primary:"#3c9cff",info:"#909399",default:"#909399",warning:"#f9ae3d",error:"#f56c6c",success:"#5ac725",mainColor:"#303133",contentColor:"#606266",tipsColor:"#909399",lightColor:"#c0c4cc",borderColor:"#e4e7ed"},{toString:St}=Object.prototype;function xt(e){return St.call(e)==="[object Array]"}function Gt(e){return e!==null&&typeof e=="object"}function Xt(e){return St.call(e)==="[object Date]"}function Jt(e){return typeof URLSearchParams!="undefined"&&e instanceof URLSearchParams}function Xe(e,f){if(!(e===null||typeof e=="undefined"))if(typeof e!="object"&&(e=[e]),xt(e))for(let t=0,a=e.length;t<a;t++)f.call(null,e[t],t,e);else for(let t in e)Object.prototype.hasOwnProperty.call(e,t)&&f.call(null,e[t],t,e)}function Zt(e){return Object.prototype.toString.call(e)==="[object Object]"}function Je(){let e={};function f(t,a){typeof e[a]=="object"&&typeof t=="object"?e[a]=Je(e[a],t):typeof t=="object"?e[a]=Je({},t):e[a]=t}for(let t=0,a=arguments.length;t<a;t++)Xe(arguments[t],f);return e}function je(e){return typeof e=="undefined"}function dt(e){return encodeURIComponent(e).replace(/%40/gi,"@").replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function $t(e,f){if(!f)return e;let t;if(Jt(f))t=f.toString();else{let a=[];Xe(f,(m,r)=>{m===null||typeof m=="undefined"||(xt(m)?r=`${r}[]`:m=[m],Xe(m,i=>{Xt(i)?i=i.toISOString():Gt(i)&&(i=JSON.stringify(i)),a.push(`${dt(r)}=${dt(i)}`)}))}),t=a.join("&")}if(t){let a=e.indexOf("#");a!==-1&&(e=e.slice(0,a)),e+=(e.indexOf("?")===-1?"?":"&")+t}return e}function er(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function tr(e,f){return f?`${e.replace(/\/+$/,"")}/${f.replace(/^\/+/,"")}`:e}function rr(e,f){return e&&!er(f)?tr(e,f):f}function nr(e,f,t){let{validateStatus:a}=t.config,m=t.statusCode;m&&(!a||a(m))?e(t):f(t)}var mt=(e,f)=>{let t={};return e.forEach(a=>{je(f[a])||(t[a]=f[a])}),t},ir=e=>new Promise((f,t)=>{let a=$t(rr(e.baseURL,e.url),e.params),m={url:a,header:e.header,complete:i=>{e.fullPath=a,i.config=e;try{typeof i.data=="string"&&(i.data=JSON.parse(i.data))}catch(p){}nr(f,t,i)}},r;if(e.method==="UPLOAD"){delete m.header["content-type"],delete m.header["Content-Type"];let i={filePath:e.filePath,name:e.name},p=["files","timeout","formData"];r=uni.uploadFile(ne(ne(ne({},m),i),mt(p,e)))}else if(e.method==="DOWNLOAD")je(e.timeout)||(m.timeout=e.timeout),r=uni.downloadFile(m);else{let i=["data","method","timeout","dataType","responseType","sslVerify","firstIpv4"];r=uni.request(ne(ne({},m),mt(i,e)))}e.getTask&&e.getTask(r,e)}),or=e=>ir(e);function Fe(){this.handlers=[]}Fe.prototype.use=function(f,t){return this.handlers.push({fulfilled:f,rejected:t}),this.handlers.length-1};Fe.prototype.eject=function(f){this.handlers[f]&&(this.handlers[f]=null)};Fe.prototype.forEach=function(f){this.handlers.forEach(t=>{t!==null&&f(t)})};var gt=(e,f,t)=>{let a={};return e.forEach(m=>{je(t[m])?je(f[m])||(a[m]=f[m]):a[m]=t[m]}),a},sr=(e,f={})=>{let t=f.method||e.method||"GET",a={baseURL:e.baseURL||"",method:t,url:f.url||"",params:f.params||{},custom:ne(ne({},e.custom||{}),f.custom||{}),header:Je(e.header||{},f.header||{})},m=["getTask","validateStatus"];if(a=ne(ne({},a),gt(m,e,f)),t==="DOWNLOAD")je(f.timeout)?je(e.timeout)||(a.timeout=e.timeout):a.timeout=f.timeout;else if(t==="UPLOAD")delete a.header["content-type"],delete a.header["Content-Type"],["files","filePath","name","timeout","formData"].forEach(i=>{je(f[i])||(a[i]=f[i])}),je(a.timeout)&&!je(e.timeout)&&(a.timeout=e.timeout);else{let r=["data","timeout","dataType","responseType","sslVerify","firstIpv4"];a=ne(ne({},a),gt(r,e,f))}return a},ar={baseURL:"",header:{},method:"GET",dataType:"json",responseType:"text",custom:{},timeout:6e4,sslVerify:!0,firstIpv4:!1,validateStatus:function(f){return f>=200&&f<300}},lr=function(){function e(o,n){return n!=null&&o instanceof n}var f;try{f=Map}catch(o){f=function(){}}var t;try{t=Set}catch(o){t=function(){}}var a;try{a=Promise}catch(o){a=function(){}}function m(o,n,d,g,E){typeof n=="object"&&(d=n.depth,g=n.prototype,E=n.includeNonEnumerable,n=n.circular);var u=[],_=[],C=typeof Buffer!="undefined";typeof n=="undefined"&&(n=!0),typeof d=="undefined"&&(d=1/0);function y(P,M){if(P===null)return null;if(M===0)return P;var k,O;if(typeof P!="object")return P;if(e(P,f))k=new f;else if(e(P,t))k=new t;else if(e(P,a))k=new a(function(q,G){P.then(function(D){q(y(D,M-1))},function(D){G(y(D,M-1))})});else if(m.__isArray(P))k=[];else if(m.__isRegExp(P))k=new RegExp(P.source,c(P)),P.lastIndex&&(k.lastIndex=P.lastIndex);else if(m.__isDate(P))k=new Date(P.getTime());else{if(C&&Buffer.isBuffer(P))return Buffer.from?k=Buffer.from(P):(k=new Buffer(P.length),P.copy(k)),k;e(P,Error)?k=Object.create(P):typeof g=="undefined"?(O=Object.getPrototypeOf(P),k=Object.create(O)):(k=Object.create(g),O=g)}if(n){var U=u.indexOf(P);if(U!=-1)return _[U];u.push(P),_.push(k)}e(P,f)&&P.forEach(function(q,G){var D=y(G,M-1),te=y(q,M-1);k.set(D,te)}),e(P,t)&&P.forEach(function(q){var G=y(q,M-1);k.add(G)});for(var Y in P){var Z=Object.getOwnPropertyDescriptor(P,Y);Z&&(k[Y]=y(P[Y],M-1));try{var ee=Object.getOwnPropertyDescriptor(P,Y);if(ee.set==="undefined")continue;k[Y]=y(P[Y],M-1)}catch(q){if(q instanceof TypeError)continue;if(q instanceof ReferenceError)continue}}if(Object.getOwnPropertySymbols)for(var A=Object.getOwnPropertySymbols(P),Y=0;Y<A.length;Y++){var j=A[Y],b=Object.getOwnPropertyDescriptor(P,j);b&&!b.enumerable&&!E||(k[j]=y(P[j],M-1),Object.defineProperty(k,j,b))}if(E)for(var N=Object.getOwnPropertyNames(P),Y=0;Y<N.length;Y++){var z=N[Y],b=Object.getOwnPropertyDescriptor(P,z);b&&b.enumerable||(k[z]=y(P[z],M-1),Object.defineProperty(k,z,b))}return k}return y(o,d)}m.clonePrototype=function(n){if(n===null)return null;var d=function(){};return d.prototype=n,new d};function r(o){return Object.prototype.toString.call(o)}m.__objToStr=r;function i(o){return typeof o=="object"&&r(o)==="[object Date]"}m.__isDate=i;function p(o){return typeof o=="object"&&r(o)==="[object Array]"}m.__isArray=p;function h(o){return typeof o=="object"&&r(o)==="[object RegExp]"}m.__isRegExp=h;function c(o){var n="";return o.global&&(n+="g"),o.ignoreCase&&(n+="i"),o.multiline&&(n+="m"),n}return m.__getRegExpFlags=c,m}(),Ze=class{constructor(f={}){Zt(f)||(f={},fe("warn","at uni_modules/uview-plus/libs/luch-request/core/Request.js:40","\u8BBE\u7F6E\u5168\u5C40\u53C2\u6570\u5FC5\u987B\u63A5\u6536\u4E00\u4E2AObject")),this.config=lr(ne(ne({},ar),f)),this.interceptors={request:new Fe,response:new Fe}}setConfig(f){this.config=f(this.config)}middleware(f){f=sr(this.config,f);let t=[or,void 0],a=Promise.resolve(f);for(this.interceptors.request.forEach(m=>{t.unshift(m.fulfilled,m.rejected)}),this.interceptors.response.forEach(m=>{t.push(m.fulfilled,m.rejected)});t.length;)a=a.then(t.shift(),t.shift());return a}request(f={}){return this.middleware(f)}get(f,t={}){return this.middleware(ne({url:f,method:"GET"},t))}post(f,t,a={}){return this.middleware(ne({url:f,data:t,method:"POST"},a))}put(f,t,a={}){return this.middleware(ne({url:f,data:t,method:"PUT"},a))}delete(f,t,a={}){return this.middleware(ne({url:f,data:t,method:"DELETE"},a))}options(f,t,a={}){return this.middleware(ne({url:f,data:t,method:"OPTIONS"},a))}upload(f,t={}){return t.url=f,t.method="UPLOAD",this.middleware(t)}download(f,t={}){return t.url=f,t.method="DOWNLOAD",this.middleware(t)}},ur=new Ze;function cr(e){return/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(e)}function fr(e){return/^1[23456789]\d{9}$/.test(e)}function pr(e){return/^((https|http|ftp|rtsp|mms):\/\/)(([0-9a-zA-Z_!~*'().&=+$%-]+: )?[0-9a-zA-Z_!~*'().&=+$%-]+@)?(([0-9]{1,3}.){3}[0-9]{1,3}|([0-9a-zA-Z_!~*'()-]+.)*([0-9a-zA-Z][0-9a-zA-Z-]{0,61})?[0-9a-zA-Z].[a-zA-Z]{2,6})(:[0-9]{1,4})?((\/?)|(\/[0-9a-zA-Z_!~*'().;?:@&=+$,%#-]+)+\/?)$/.test(e)}function hr(e){if(!e)return!1;if(typeof e=="number")return e.toString().length!==10&&e.toString().length!==13?!1:!isNaN(new Date(e).getTime());if(typeof e=="string"){let f=Number(e);if(!isNaN(f)&&(f.toString().length===10||f.toString().length===13))return!isNaN(new Date(f).getTime());if(e.length<10||e.length>19||!/^\d{4}[-\/]\d{2}[-\/]\d{2}( \d{1,2}:\d{2}(:\d{2})?)?$/.test(e))return!1;let a=new Date(e);return!isNaN(a.getTime())}return!1}function dr(e){return/^\d{4}[\/\-](0?[1-9]|1[012])[\/\-](0?[1-9]|[12][0-9]|3[01])$/.test(e)}function $e(e){return/^[\+-]?(\d+\.?\d*|\.\d+|\d\.\d+e\+\d+)$/.test(e)}function mr(e){return typeof e=="string"}function gr(e){return/^\d+$/.test(e)}function yr(e){return/^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/.test(e)}function br(e){let f=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}(([0-9]{5}[DF]$)|([DF][A-HJ-NP-Z0-9][0-9]{4}$))/,t=/^[京津沪渝冀豫云辽黑湘皖鲁新苏浙赣鄂桂甘晋蒙陕吉闽贵粤青藏川宁琼使领A-Z]{1}[A-Z]{1}[A-HJ-NP-Z0-9]{4}[A-HJ-NP-Z0-9挂学警港澳]{1}$/;return e.length===7?t.test(e):e.length===8?f.test(e):!1}function vr(e){return/^[1-9]\d*(,\d{3})*(\.\d{1,2})?$|^0\.\d{1,2}$/.test(e)}function wr(e){return/^[\u4e00-\u9fa5]+$/gi.test(e)}function _r(e){return/^[a-zA-Z]*$/.test(e)}function Sr(e){return/^[0-9a-zA-Z]*$/g.test(e)}function xr(e,f){return e.indexOf(f)>=0}function Ar(e,f){return e>=f[0]&&e<=f[1]}function Cr(e,f){return e.length>=f[0]&&e.length<=f[1]}function Er(e){return/^\d{3,4}-\d{7,8}(-\d{3,4})?$/.test(e)}function et(e){switch(typeof e){case"undefined":return!0;case"string":if(e.replace(/(^[ \t\n\r]*)|([ \t\n\r]*$)/g,"").length==0)return!0;break;case"boolean":if(!e)return!0;break;case"number":if(e===0||isNaN(e))return!0;break;case"object":if(e===null||e.length===0)return!0;for(let f in e)return!1;return!0}return!1}function Ir(e){if(typeof e=="string")try{let f=JSON.parse(e);return!!(typeof f=="object"&&f)}catch(f){return!1}return!1}function At(e){return typeof Array.isArray=="function"?Array.isArray(e):Object.prototype.toString.call(e)==="[object Array]"}function Ct(e){return Object.prototype.toString.call(e)==="[object Object]"}function Br(e,f=6){return new RegExp(`^\\d{${f}}$`).test(e)}function tt(e){return typeof e=="function"}function Tr(e){return Ct(e)&&tt(e.then)&&tt(e.catch)}function kr(e){let f=e.split("?")[0];return/\.(jpeg|jpg|gif|png|svg|webp|jfif|bmp|dpg)/i.test(f)}function Pr(e){return/\.(mp4|mpg|mpeg|dat|asf|avi|rm|rmvb|mov|wmv|flv|mkv|m3u8)/i.test(e)}function jr(e){return e&&Object.prototype.toString.call(e)==="[object RegExp]"}var Or={email:cr,mobile:fr,url:pr,date:hr,dateISO:dr,number:$e,digits:gr,idCard:yr,carNo:br,amount:vr,chinese:wr,letter:_r,enOrNum:Sr,contains:xr,range:Ar,rangeLength:Cr,empty:et,isEmpty:et,jsonString:Ir,landline:Er,object:Ct,array:At,code:Br,func:tt,promise:Tr,video:Pr,image:kr,regExp:jr,string:mr};function nt(e=30){return new Promise(f=>{setTimeout(()=>{f()},e)})}function Et(){return uni.getDeviceInfo().platform.toLowerCase()}function He(){let e={};return e=uni.getWindowInfo(),e}function Ue(e=void 0){let f=this.$parent;for(;f;)if(e=e.replace(/up-([a-zA-Z0-9-_]+)/g,"u-$1"),f.$options&&f.$options.name!==e)f=f.$parent;else return f;return!1}function Pe(e,f="object"){if(et(e)||typeof e=="object"&&f==="object"||f==="string"&&typeof e=="string")return e;if(f==="object"){e=Qe(e);let a=e.split(";"),m={};for(let r=0;r<a.length;r++)if(a[r]){let i=a[r].split(":");m[Qe(i[0])]=Qe(i[1])}return m}let t="";return typeof e=="object"&&e.forEach((a,m)=>{let r=m.replace(/([A-Z])/g,"-$1").toLowerCase();t+=`${r}:${a};`}),Qe(t)}function ve(e="auto",f=""){return f||(f=Re.unit||"px"),f=="rpx"&&$e(String(e))&&(e=e*2),e=String(e),$e(e)?`${e}${f}`:e}function It(e){if([null,void 0,NaN,!1].includes(e)||typeof e!="object"&&typeof e!="function")return e;let f=At(e)?[]:{};for(let t in e)e.hasOwnProperty(t)&&(f[t]=typeof e[t]=="object"?It(e[t]):e[t]);return f}function xe(e={},f={}){let t=It(e);if(typeof t!="object"||typeof f!="object")return!1;for(let a in f)f.hasOwnProperty(a)&&(a in t?f[a]==null||typeof t[a]!="object"||typeof f[a]!="object"?t[a]=f[a]:t[a].concat&&f[a].concat?t[a]=t[a].concat(f[a]):t[a]=xe(t[a],f[a]):t[a]=f[a]);return t}function Le(e,f={}){if(typeof e!="object"||typeof f!="object")return!1;for(let t in f)f.hasOwnProperty(t)&&(t in e?f[t]==null||typeof e[t]!="object"||typeof f[t]!="object"?e[t]=f[t]:e[t].concat&&f[t].concat?e[t]=e[t].concat(f[t]):e[t]=Le(e[t],f[t]):e[t]=f[t]);return e}String.prototype.padStart||(String.prototype.padStart=function(e,f=" "){if(Object.prototype.toString.call(f)!=="[object String]")throw new TypeError("fillString must be String");let t=this;if(t.length>=e)return String(t);let a=e-t.length,m=Math.ceil(a/f.length);for(;m>>=1;)f+=f,m===1&&(f+=f);return f.slice(0,a)+t});function Qe(e,f="both"){return e=String(e),f=="both"?e.replace(/^\s+|\s+$/g,""):f=="left"?e.replace(/^\s*/,""):f=="right"?e.replace(/(\s*$)/g,""):f=="all"?e.replace(/\s+/g,""):e}function yt(e={},f=!0,t="brackets"){let a=f?"?":"",m=[];["indices","brackets","repeat","comma"].indexOf(t)==-1&&(t="brackets");for(let r in e){let i=e[r];if(!(["",void 0,null].indexOf(i)>=0))if(i.constructor===Array)switch(t){case"indices":for(let h=0;h<i.length;h++)m.push(`${r}[${h}]=${i[h]}`);break;case"brackets":i.forEach(h=>{m.push(`${r}[]=${h}`)});break;case"repeat":i.forEach(h=>{m.push(`${r}=${h}`)});break;case"comma":let p="";i.forEach(h=>{p+=(p?",":"")+h}),m.push(`${r}=${p}`);break;default:i.forEach(h=>{m.push(`${r}[]=${h}`)})}else m.push(`${r}=${i}`)}return m.length?a+m.join("&"):""}function We(e,f){let t=Ue.call(e,"u-form-item"),a=Ue.call(e,"u-form");t&&a&&a.validateField(t.prop,()=>{},f)}function Rr(){let e=getCurrentPages();return`/${e[e.length-1].route||""}`}var Mr={actionSheet:{show:!1,title:"",description:"",actions:[],index:"",cancelText:"",closeOnClickAction:!0,safeAreaInsetBottom:!0,openType:"",closeOnClickOverlay:!0,round:0,wrapMaxHeight:"600px"}},Nr={album:{urls:[],keyName:"",singleSize:180,multipleSize:70,space:6,singleMode:"scaleToFill",multipleMode:"aspectFill",maxCount:9,previewFullImage:!0,rowCount:3,showMore:!0,autoWrap:!1,unit:"px",stop:!0}},Lr={alert:{title:"",type:"warning",description:"",closable:!1,showIcon:!1,effect:"light",center:!1,fontSize:14}},Fr={avatar:{src:"",shape:"circle",size:40,mode:"scaleToFill",text:"",bgColor:"#c0c4cc",color:"#ffffff",fontSize:18,icon:"",mpAvatar:!1,randomBgColor:!1,defaultUrl:"",colorIndex:"",name:""}},Ur={avatarGroup:{urls:[],maxCount:5,shape:"circle",mode:"scaleToFill",showMore:!0,size:40,keyName:"",gap:.5,extraValue:0}},zr={backtop:{mode:"circle",icon:"arrow-upward",text:"",duration:100,scrollTop:0,top:400,bottom:100,right:20,zIndex:9,iconStyle:{color:"#909399",fontSize:"19px"}}},Dr={badge:{isDot:!1,value:"",show:!0,max:999,type:"error",showZero:!1,bgColor:null,color:null,shape:"circle",numberType:"overflow",offset:[],inverted:!1,absolute:!1}},Qr={button:{hairline:!1,type:"info",size:"normal",shape:"square",plain:!1,disabled:!1,loading:!1,loadingText:"",loadingMode:"spinner",loadingSize:15,openType:"",formType:"",appParameter:"",hoverStopPropagation:!0,lang:"en",sessionFrom:"",sendMessageTitle:"",sendMessagePath:"",sendMessageImg:"",showMessageCard:!1,dataName:"",throttleTime:0,hoverStartTime:0,hoverStayTime:200,text:"",icon:"",iconColor:"",color:"",stop:!0}},Hr={calendar:{title:"\u65E5\u671F\u9009\u62E9",showTitle:!0,showSubtitle:!0,mode:"single",startText:"\u5F00\u59CB",endText:"\u7ED3\u675F",customList:[],color:"#3c9cff",minDate:0,maxDate:0,defaultDate:null,maxCount:Number.MAX_SAFE_INTEGER,rowHeight:56,formatter:null,showLunar:!1,showMark:!0,confirmText:"\u786E\u5B9A",confirmDisabledText:"\u786E\u5B9A",show:!1,closeOnClickOverlay:!1,readonly:!1,showConfirm:!0,maxRange:Number.MAX_SAFE_INTEGER,rangePrompt:"",showRangePrompt:!0,allowSameDay:!1,round:0,monthNum:3,weekText:["\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D","\u65E5"],forbidDays:[],forbidDaysToast:"\u8BE5\u65E5\u671F\u5DF2\u7981\u7528"}},Wr={carKeyboard:{random:!1}},Vr={cell:{customClass:"",title:"",label:"",value:"",icon:"",disabled:!1,border:!0,center:!1,url:"",linkType:"navigateTo",clickable:!1,isLink:!1,required:!1,arrowDirection:"",iconStyle:{},rightIconStyle:{},rightIcon:"arrow-right",titleStyle:{},size:"",stop:!0,name:""}},qr={cellGroup:{title:"",border:!0,customStyle:{}}},Kr={checkbox:{name:"",shape:"",size:"",checkbox:!1,disabled:"",activeColor:"",inactiveColor:"",iconSize:"",iconColor:"",label:"",labelSize:"",labelColor:"",labelDisabled:""}},Yr={checkboxGroup:{name:"",value:[],shape:"square",disabled:!1,activeColor:"#2979ff",inactiveColor:"#c8c9cc",size:18,placement:"row",labelSize:14,labelColor:"#303133",labelDisabled:!1,iconColor:"#ffffff",iconSize:12,iconPlacement:"left",borderBottom:!1}},Gr={circleProgress:{percentage:30}},Xr={code:{seconds:60,startText:"\u83B7\u53D6\u9A8C\u8BC1\u7801",changeText:"X\u79D2\u91CD\u65B0\u83B7\u53D6",endText:"\u91CD\u65B0\u83B7\u53D6",keepRunning:!1,uniqueKey:""}},Jr={codeInput:{adjustPosition:!0,maxlength:6,dot:!1,mode:"box",hairline:!1,space:10,value:"",focus:!1,bold:!1,color:"#606266",fontSize:18,size:35,disabledKeyboard:!1,borderColor:"#c9cacc",disabledDot:!0}},Zr={col:{span:12,offset:0,justify:"start",align:"stretch",textAlign:"left"}},$r={collapse:{value:null,accordion:!1,border:!0}},en={collapseItem:{title:"",value:"",label:"",disabled:!1,isLink:!0,clickable:!0,border:!0,align:"left",name:"",icon:"",duration:300,showRight:!0,titleStyle:{},iconStyle:{},rightIconStyle:{},cellCustomStyle:{},cellCustomClass:""}},tn={columnNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80,step:!1,duration:1500,disableTouch:!0,justifyContent:"flex-start"}},rn={countDown:{time:0,format:"HH:mm:ss",autoStart:!0,millisecond:!1}},nn={countTo:{startVal:0,endVal:0,duration:2e3,autoplay:!0,decimals:0,useEasing:!0,decimal:".",color:"#606266",fontSize:22,bold:!1,separator:""}},on={datetimePicker:{show:!1,popupMode:"bottom",showToolbar:!0,value:"",title:"",mode:"datetime",maxDate:new Date(new Date().getFullYear()+10,0,1).getTime(),minDate:new Date(new Date().getFullYear()-10,0,1).getTime(),minHour:0,maxHour:23,minMinute:0,maxMinute:59,filter:null,formatter:null,loading:!1,itemHeight:44,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u8BA4",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,closeOnClickOverlay:!1,defaultIndex:[]}},sn={divider:{dashed:!1,hairline:!0,dot:!1,textPosition:"center",text:"",textSize:14,textColor:"#909399",lineColor:"#dcdfe6"}},an={empty:{icon:"",text:"",textColor:"#c0c4cc",textSize:14,iconColor:"#c0c4cc",iconSize:90,mode:"data",width:160,height:160,show:!0,marginTop:0}},ln={form:{model:{},rules:{},errorType:"message",borderBottom:!0,labelPosition:"left",labelWidth:45,labelAlign:"left",labelStyle:{}}},un={formItem:{label:"",prop:"",rules:[],borderBottom:"",labelPosition:"",labelWidth:"",rightIcon:"",leftIcon:"",required:!1,leftIconStyle:""}},cn={gap:{bgColor:"transparent",height:20,marginTop:0,marginBottom:0,customStyle:{}}},fn={grid:{col:3,border:!1,align:"left"}},pn={gridItem:{name:null,bgColor:"transparent"}},{color:bt}=Re,hn={icon:{name:"",color:bt["u-content-color"],size:"16px",bold:!1,index:"",hoverClass:"",customPrefix:"uicon",label:"",labelPos:"right",labelSize:"15px",labelColor:bt["u-content-color"],space:"3px",imgMode:"",width:"",height:"",top:0,stop:!1}},dn={image:{src:"",mode:"aspectFill",width:"300",height:"225",shape:"square",radius:0,lazyLoad:!0,showMenuByLongpress:!0,loadingIcon:"photo",errorIcon:"error-circle",showLoading:!0,showError:!0,fade:!0,webp:!1,duration:500,bgColor:"#f3f4f6"}},mn={indexAnchor:{text:"",color:"#606266",size:14,bgColor:"#dedede",height:32}},gn={indexList:{inactiveColor:"#606266",activeColor:"#5677fc",indexList:[],sticky:!0,customNavHeight:0,safeBottomFix:!1}},yn={input:{value:"",type:"text",fixed:!1,disabled:!1,disabledColor:"#f5f7fa",clearable:!1,password:!1,maxlength:140,placeholder:null,placeholderClass:"input-placeholder",placeholderStyle:"color: #c0c4cc",showWordLimit:!1,confirmType:"done",confirmHold:!1,holdKeyboard:!1,focus:!1,autoBlur:!1,disableDefaultPadding:!1,cursor:-1,cursorSpacing:30,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,inputAlign:"left",fontSize:"15px",color:"#303133",prefixIcon:"",prefixIconStyle:"",suffixIcon:"",suffixIconStyle:"",border:"surround",readonly:!1,shape:"square",formatter:null}},bn={keyboard:{mode:"number",dotDisabled:!1,tooltip:!0,showTips:!0,tips:"",showCancel:!0,showConfirm:!0,random:!1,safeAreaInsetBottom:!0,closeOnClickOverlay:!0,show:!1,overlay:!0,zIndex:10075,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u5B9A",autoChange:!1}},vn={line:{color:"#d6d7d9",length:"100%",direction:"row",hairline:!0,margin:0,dashed:!1}},wn={lineProgress:{activeColor:"#19be6b",inactiveColor:"#ececec",percentage:0,showText:!0,height:12}},{color:_n}=Re,Sn={link:{color:_n["u-primary"],fontSize:15,underLine:!1,href:"",mpTips:"\u94FE\u63A5\u5DF2\u590D\u5236\uFF0C\u8BF7\u5728\u6D4F\u89C8\u5668\u6253\u5F00",lineColor:"",text:""}},xn={list:{showScrollbar:!1,lowerThreshold:50,upperThreshold:0,scrollTop:0,offsetAccuracy:10,enableFlex:!1,pagingEnabled:!1,scrollable:!0,scrollIntoView:"",scrollWithAnimation:!1,enableBackToTop:!1,height:0,width:0,preLoadScreen:1}},An={listItem:{anchor:""}},{color:vt}=Re,Cn={loadingIcon:{show:!0,color:vt["u-tips-color"],textColor:vt["u-tips-color"],vertical:!1,mode:"spinner",size:24,textSize:15,text:"",timingFunction:"ease-in-out",duration:1200,inactiveColor:""}},En={loadingPage:{loadingText:"\u6B63\u5728\u52A0\u8F7D",image:"",loadingMode:"circle",loading:!1,bgColor:"#ffffff",color:"#C8C8C8",fontSize:19,iconSize:28,loadingColor:"#C8C8C8",zIndex:10}},In={loadmore:{status:"loadmore",bgColor:"transparent",icon:!0,fontSize:14,iconSize:17,color:"#606266",loadingIcon:"spinner",loadmoreText:"\u52A0\u8F7D\u66F4\u591A",loadingText:"\u6B63\u5728\u52A0\u8F7D...",nomoreText:"\u6CA1\u6709\u66F4\u591A\u4E86",isDot:!1,iconColor:"#b7b7b7",marginTop:10,marginBottom:10,height:"auto",line:!1,lineColor:"#E6E8EB",dashed:!1}},Bn={modal:{show:!1,title:"",content:"",confirmText:"\u786E\u8BA4",cancelText:"\u53D6\u6D88",showConfirmButton:!0,showCancelButton:!1,confirmColor:"#2979ff",cancelColor:"#606266",buttonReverse:!1,zoom:!0,asyncClose:!1,closeOnClickOverlay:!1,negativeTop:0,width:"650rpx",confirmButtonShape:"",contentTextAlign:"left",asyncCloseTip:"\u64CD\u4F5C\u4E2D...",asyncCancelClose:!1}},Tn={navbar:{safeAreaInsetTop:!0,placeholder:!1,fixed:!0,border:!1,leftIcon:"arrow-left",leftText:"",rightText:"",rightIcon:"",title:"",titleColor:"",bgColor:"#ffffff",titleWidth:"400rpx",height:"44px",leftIconSize:20,leftIconColor:_t.mainColor,autoBack:!1,titleStyle:""}},kn={noNetwork:{tips:"\u54CE\u5440\uFF0C\u7F51\u7EDC\u4FE1\u53F7\u4E22\u5931",zIndex:"",image:"data:image/png;base64,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"}},Pn={noticeBar:{text:[],direction:"row",step:!1,icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",speed:80,fontSize:14,duration:2e3,disableTouch:!0,url:"",linkType:"navigateTo",justifyContent:"flex-start"}},jn={notify:{top:0,type:"primary",color:"#ffffff",bgColor:"",message:"",duration:3e3,fontSize:15,safeAreaInsetTop:!1}},On={numberBox:{name:"",value:0,min:1,max:Number.MAX_SAFE_INTEGER,step:1,integer:!1,disabled:!1,disabledInput:!1,asyncChange:!1,inputWidth:35,showMinus:!0,showPlus:!0,decimalLength:null,longPress:!0,color:"#323233",buttonWidth:30,buttonSize:30,buttonRadius:"0px",bgColor:"#EBECEE",inputBgColor:"#EBECEE",cursorSpacing:100,disableMinus:!1,disablePlus:!1,iconStyle:"",miniMode:!1}},Rn={numberKeyboard:{mode:"number",dotDisabled:!1,random:!1}},Mn={overlay:{show:!1,zIndex:10070,duration:300,opacity:.5}},Nn={parse:{copyLink:!0,errorImg:"",lazyLoad:!1,loadingImg:"",pauseVideo:!0,previewImg:!0,setTitle:!0,showImgMenu:!0}},Ln={picker:{show:!1,popupMode:"bottom",showToolbar:!0,title:"",columns:[],loading:!1,itemHeight:44,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u5B9A",cancelColor:"#909193",confirmColor:"#3c9cff",visibleItemCount:5,keyName:"text",closeOnClickOverlay:!1,defaultIndex:[],immediateChange:!0,zIndex:10076}},Fn={popup:{show:!1,overlay:!0,mode:"bottom",duration:300,closeable:!1,overlayStyle:{},closeOnClickOverlay:!0,zIndex:10075,safeAreaInsetBottom:!0,safeAreaInsetTop:!1,closeIconPos:"top-right",round:0,zoom:!0,bgColor:"",overlayOpacity:.5}},Un={radio:{name:"",shape:"",disabled:"",labelDisabled:"",activeColor:"",inactiveColor:"",iconSize:"",labelSize:"",label:"",labelColor:"",size:"",iconColor:"",placement:""}},zn={radioGroup:{value:"",disabled:!1,shape:"circle",activeColor:"#2979ff",inactiveColor:"#c8c9cc",name:"",size:18,placement:"row",label:"",labelColor:"#303133",labelSize:14,labelDisabled:!1,iconColor:"#ffffff",iconSize:12,borderBottom:!1,iconPlacement:"left",gap:"10px"}},Dn={rate:{value:1,count:5,disabled:!1,size:18,inactiveColor:"#b2b2b2",activeColor:"#FA3534",gutter:4,minCount:1,allowHalf:!1,activeIcon:"star-fill",inactiveIcon:"star",touchable:!0}},Qn={readMore:{showHeight:400,toggle:!1,closeText:"\u5C55\u5F00\u9605\u8BFB\u5168\u6587",openText:"\u6536\u8D77",color:"#2979ff",fontSize:14,textIndent:"2em",name:""}},Hn={row:{gutter:0,justify:"start",align:"center"}},Wn={rowNotice:{text:"",icon:"volume",mode:"",color:"#f9ae3d",bgColor:"#fdf6ec",fontSize:14,speed:80}},Vn={scrollList:{indicatorWidth:50,indicatorBarWidth:20,indicator:!0,indicatorColor:"#f2f2f2",indicatorActiveColor:"#3c9cff",indicatorStyle:""}},qn={search:{shape:"round",bgColor:"#f2f2f2",placeholder:"\u8BF7\u8F93\u5165\u5173\u952E\u5B57",clearabled:!0,focus:!1,showAction:!0,actionStyle:{},actionText:"\u641C\u7D22",inputAlign:"left",inputStyle:{},disabled:!1,borderColor:"transparent",searchIconColor:"#909399",searchIconSize:22,color:"#606266",placeholderColor:"#909399",searchIcon:"search",margin:"0",animation:!1,value:"",maxlength:"-1",height:32,label:null}},Kn={section:{title:"",subTitle:"\u66F4\u591A",right:!0,fontSize:15,bold:!0,color:"#303133",subColor:"#909399",showLine:!0,lineColor:"",arrow:!0}},Yn={skeleton:{loading:!0,animate:!0,rows:0,rowsWidth:"100%",rowsHeight:18,title:!0,titleWidth:"50%",titleHeight:18,avatar:!1,avatarSize:32,avatarShape:"circle"}},Gn={slider:{value:0,blockSize:18,min:0,max:100,step:1,activeColor:"#2979ff",inactiveColor:"#c0c4cc",blockColor:"#ffffff",showValue:!1,disabled:!1,blockStyle:{},useNative:!1,height:"2px"}},Xn={statusBar:{bgColor:"transparent"}},Jn={steps:{direction:"row",current:0,activeColor:"#3c9cff",inactiveColor:"#969799",activeIcon:"",inactiveIcon:"",dot:!1}},Zn={stepsItem:{title:"",desc:"",iconSize:17,error:!1}},$n={sticky:{offsetTop:0,customNavHeight:0,disabled:!1,bgColor:"transparent",zIndex:"",index:""}},ei={subsection:{list:[],current:0,activeColor:"#3c9cff",inactiveColor:"#303133",mode:"button",fontSize:12,bold:!0,bgColor:"#eeeeef",keyName:"name"}},ti={swipeAction:{autoClose:!0}},ri={swipeActionItem:{show:!1,closeOnClick:!0,name:"",disabled:!1,threshold:20,autoClose:!0,options:[],duration:300}},ni={swiper:{list:[],indicator:!1,indicatorActiveColor:"#FFFFFF",indicatorInactiveColor:"rgba(255, 255, 255, 0.35)",indicatorStyle:"",indicatorMode:"line",autoplay:!0,current:0,currentItemId:"",interval:3e3,duration:300,circular:!1,previousMargin:0,nextMargin:0,acceleration:!1,displayMultipleItems:1,easingFunction:"default",keyName:"url",imgMode:"aspectFill",height:130,bgColor:"#f3f4f6",radius:4,loading:!1,showTitle:!1}},ii={swiperIndicator:{length:0,current:0,indicatorActiveColor:"",indicatorInactiveColor:"",indicatorMode:"line"}},oi={switch:{loading:!1,disabled:!1,size:25,activeColor:"#2979ff",inactiveColor:"#ffffff",value:!1,activeValue:!0,inactiveValue:!1,asyncChange:!1,space:0}},si={tabbar:{value:null,safeAreaInsetBottom:!0,border:!0,zIndex:1,activeColor:"#1989fa",inactiveColor:"#7d7e80",fixed:!0,placeholder:!0}},ai={tabbarItem:{name:null,icon:"",badge:null,dot:!1,text:"",badgeStyle:"top: 6px;right:2px;"}},li={tabs:{duration:300,list:[],lineColor:"#3c9cff",activeStyle:{color:"#303133"},inactiveStyle:{color:"#606266"},lineWidth:20,lineHeight:3,lineBgSize:"cover",itemStyle:{height:"44px"},scrollable:!0,current:0,keyName:"name",iconStyle:{}}},ui={tag:{type:"primary",disabled:!1,size:"medium",shape:"square",text:"",bgColor:"",color:"",borderColor:"",closeColor:"#C6C7CB",name:"",plainFill:!1,plain:!1,closable:!1,show:!0,icon:"",iconColor:""}},ci={text:{type:"",show:!0,text:"",prefixIcon:"",suffixIcon:"",mode:"",href:"",format:"",call:!1,openType:"",bold:!1,block:!1,lines:"",color:"#303133",size:15,iconStyle:{fontSize:"15px"},decoration:"none",margin:0,lineHeight:"",align:"left",wordWrap:"normal",flex1:!0}},fi={textarea:{value:"",placeholder:"",placeholderClass:"textarea-placeholder",placeholderStyle:"color: #c0c4cc",height:70,confirmType:"done",disabled:!1,count:!1,focus:!1,autoHeight:!1,fixed:!1,cursorSpacing:0,cursor:"",showConfirmBar:!0,selectionStart:-1,selectionEnd:-1,adjustPosition:!0,disableDefaultPadding:!1,holdKeyboard:!1,maxlength:140,border:"surround",formatter:null}},pi={toast:{zIndex:10090,loading:!1,message:"",icon:"",type:"",loadingMode:"",show:"",overlay:!1,position:"center",params:{},duration:2e3,isTab:!1,url:"",callback:null,back:!1}},hi={toolbar:{show:!0,cancelText:"\u53D6\u6D88",confirmText:"\u786E\u8BA4",cancelColor:"#909193",confirmColor:"#3c9cff",title:""}},di={tooltip:{text:"",copyText:"",size:14,color:"#606266",bgColor:"transparent",direction:"top",zIndex:10071,showCopy:!0,buttons:[],overlay:!0,showToast:!0}},mi={transition:{show:!1,mode:"fade",duration:"300",timingFunction:"ease-out"}},gi={upload:{accept:"image",extension:[],capture:["album","camera"],compressed:!0,camera:"back",maxDuration:60,uploadIcon:"camera-fill",uploadIconColor:"#D3D4D6",useBeforeRead:!1,previewFullImage:!0,maxCount:52,disabled:!1,imageMode:"aspectFill",name:"",sizeType:["original","compressed"],multiple:!1,deletable:!0,maxSize:Number.MAX_VALUE,fileList:[],uploadText:"",width:80,height:80,previewImage:!0}},X=ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne(ne({},Mr),Nr),Lr),Fr),Ur),zr),Dr),Qr),Hr),Wr),Vr),qr),Kr),Yr),Gr),Xr),Jr),Zr),$r),en),tn),rn),nn),on),sn),an),ln),un),cn),fn),pn),hn),dn),mn),gn),yn),bn),vn),wn),Sn),xn),An),Cn),En),In),Bn),Tn),kn),Pn),jn),On),Rn),Mn),Nn),Ln),Fn),Un),zn),Dn),Qn),Hn),Wn),Vn),qn),Kn),Yn),Gn),Xn),Jn),Zn),$n),ei),ti),ri),ni),ii),oi),si),ai),li),ui),ci),fi),pi),hi),di),mi),gi);function yi(e){Le(Re,e.config||{}),Le(X,e.props||{}),Le(_t,e.color||{}),Le(Yt,e.zIndex||{})}if(uni&&uni.upuiParams){fe("log","at uni_modules/uview-plus/libs/config/props.js:204","setting uview-plus");let e=uni.upuiParams();e.httpIns&&e.httpIns(ur),e.options&&yi(e.options)}var bi=Ce({props:{name:{type:String,default:()=>X.icon.name},color:{type:String,default:()=>X.icon.color},size:{type:[String,Number],default:()=>X.icon.size},bold:{type:Boolean,default:()=>X.icon.bold},index:{type:[String,Number],default:()=>X.icon.index},hoverClass:{type:String,default:()=>X.icon.hoverClass},customPrefix:{type:String,default:()=>X.icon.customPrefix},label:{type:[String,Number],default:()=>X.icon.label},labelPos:{type:String,default:()=>X.icon.labelPos},labelSize:{type:[String,Number],default:()=>X.icon.labelSize},labelColor:{type:String,default:()=>X.icon.labelColor},space:{type:[String,Number],default:()=>X.icon.space},imgMode:{type:String,default:()=>X.icon.imgMode},width:{type:[String,Number],default:()=>X.icon.width},height:{type:[String,Number],default:()=>X.icon.height},top:{type:[String,Number],default:()=>X.icon.top},stop:{type:Boolean,default:()=>X.icon.stop}}}),Be=Ce({}),rt=class{constructor(){this.config={type:"navigateTo",url:"",delta:1,params:{},animationType:"pop-in",animationDuration:300,intercept:!1},this.route=this.route.bind(this)}addRootPath(f){return f[0]==="/"?f:`/${f}`}mixinParam(f,t){f=f&&this.addRootPath(f);let a="";return/.*\/.*\?.*=.*/.test(f)?(a=yt(t,!1),f+=`&${a}`):(a=yt(t),f+=a)}route(){return ke(this,arguments,function*(f={},t={}){let a={};typeof f=="string"?(a.url=this.mixinParam(f,t),a.type="navigateTo"):(a=xe(this.config,f),a.url=this.mixinParam(f.url,f.params)),a.url!==Rr()&&(t.intercept&&(this.config.intercept=t.intercept),a.params=t,a=xe(this.config,a),typeof uni.$u.routeIntercept=="function"?(yield new Promise((r,i)=>{uni.$u.routeIntercept(a,r)}))&&this.openPage(a):this.openPage(a))})}openPage(f){let{url:t,type:a,delta:m,animationType:r,animationDuration:i}=f;(f.type=="navigateTo"||f.type=="to")&&uni.navigateTo({url:t,animationType:r,animationDuration:i}),(f.type=="redirectTo"||f.type=="redirect")&&uni.redirectTo({url:t}),(f.type=="switchTab"||f.type=="tab")&&uni.switchTab({url:t}),(f.type=="reLaunch"||f.type=="launch")&&uni.reLaunch({url:t}),(f.type=="navigateBack"||f.type=="back")&&uni.navigateBack({delta:m})}},wt=new rt().route,vi=De("dom"),Te=Ce({props:{customStyle:{type:[Object,String],default:()=>({})},customClass:{type:String,default:""},url:{type:String,default:""},linkType:{type:String,default:"navigateTo"}},data(){return{}},onLoad(){this.$u.getRect=this.$uGetRect},created(){this.$u.getRect=this.$uGetRect},computed:{$u(){return uni.$u},bem(){return function(e,f,t){let a=`u-${e}--`,m={};return f&&f.map(r=>{m[a+this[r]]=!0}),t&&t.map(r=>{this[r]?m[a+r]=this[r]:delete m[a+r]}),Object.keys(m)}}},methods:{openPage(e="url"){let f=this[e];f&&wt({type:this.linkType,url:f})},navTo(e="",f="navigateTo"){wt({type:this.linkType,url:e})},$uGetRect(e,f){return new Promise(t=>{nt(30).then(()=>{let a=e.substring(1),m=this.$refs[a];m||t({with:0,height:0,left:0,right:0,top:0,bottom:0}),vi.getComponentRect(m,r=>{t(r.size)})})})},getParentData(e=""){this.parent||(this.parent={}),this.parent=Ue.call(this,e),this.parent.children&&this.parent.children.indexOf(this)===-1&&this.parent.children.push(this),this.parent&&this.parentData&&Object.keys(this.parentData).map(f=>{this.parentData[f]=this.parent[f]})},preventEvent(e){e&&typeof e.stopPropagation=="function"&&e.stopPropagation()},noop(e){this.preventEvent(e)}},onReachBottom(){uni.$emit("uOnReachBottom")},beforeUnmount(){if(this.parent&&Or.array(this.parent.children)){let e=this.parent.children;e.map((f,t)=>{f===this&&e.splice(t,1)})}}}),wi={"u-icon":{"":{alignItems:"center"}},"u-icon--left":{"":{flexDirection:"row-reverse",alignItems:"center"}},"u-icon--right":{"":{flexDirection:"row",alignItems:"center"}},"u-icon--top":{"":{flexDirection:"column-reverse",justifyContent:"center"}},"u-icon--bottom":{"":{flexDirection:"column",justifyContent:"center"}},"u-icon__icon":{"":{fontFamily:"uicon-iconfont",position:"relative",flexDirection:"row",alignItems:"center"}},"u-icon__icon--primary":{"":{color:"#3c9cff"}},"u-icon__icon--success":{"":{color:"#5ac725"}},"u-icon__icon--error":{"":{color:"#f56c6c"}},"u-icon__icon--warning":{"":{color:"#f9ae3d"}},"u-icon__icon--info":{"":{color:"#909399"}}},_i="https://at.alicdn.com/t/font_2225171_8kdcwk4po24.ttf",Bt=weex.requireModule("dom");Bt.addRule("fontFace",{fontFamily:"uicon-iconfont",src:`url('${_i}')`});var Si={name:"u-icon",beforeCreate(){this.customFontFamily&&Bt.addRule("fontFace",{fontFamily:`${this.customPrefix}-${this.customFontFamily}`,src:`url('${this.customFontUrl}')`})},data(){return{}},emits:["click"],mixins:[Be,Te,bi],computed:{uClasses(){let e=[];return e.push(this.customPrefix+"-"+this.name),this.customPrefix=="uicon"?e.push("u-iconfont"):e.push(this.customPrefix),this.color&&Re.type.includes(this.color)&&e.push("u-icon__icon--"+this.color),e},iconStyle(){let e={};return e={fontSize:ve(this.size),lineHeight:ve(this.size),fontWeight:this.bold?"bold":"normal",top:ve(this.top)},this.color&&!Re.type.includes(this.color)&&(e.color=this.color),e},isImg(){return this.name.indexOf("/")!==-1},imgStyle(){let e={};return e.width=this.width?ve(this.width):ve(this.size),e.height=this.height?ve(this.height):ve(this.size),e},icon(){return this.customPrefix!=="uicon"?this.customIcons[this.customPrefix+"-"+this.name]||this.name:Kt["uicon-"+this.name]||this.name}},methods:{addStyle:Pe,addUnit:ve,clickHandler(e){this.$emit("click",this.index,e),this.stop&&this.preventEvent(e)}}};function xi(e,f,t,a,m,r){return(0,we.openBlock)(),(0,we.createElementBlock)("view",{class:(0,we.normalizeClass)(["u-icon",["u-icon--"+e.labelPos]]),onClick:f[0]||(f[0]=(...i)=>r.clickHandler&&r.clickHandler(...i)),renderWhole:!0},[r.isImg?((0,we.openBlock)(),(0,we.createElementBlock)("u-image",{key:0,class:"u-icon__img",src:e.name,mode:e.imgMode,style:(0,we.normalizeStyle)([r.imgStyle,r.addStyle(e.customStyle)])},null,12,["src","mode"])):((0,we.openBlock)(),(0,we.createElementBlock)("u-text",{key:1,class:(0,we.normalizeClass)(["u-icon__icon",r.uClasses]),style:(0,we.normalizeStyle)([r.iconStyle,r.addStyle(e.customStyle)]),hoverClass:e.hoverClass},(0,we.toDisplayString)(r.icon),15,["hoverClass"])),e.label!==""?((0,we.openBlock)(),(0,we.createElementBlock)("u-text",{key:2,class:"u-icon__label",style:(0,we.normalizeStyle)({color:e.labelColor,fontSize:r.addUnit(e.labelSize),marginLeft:e.labelPos=="right"?r.addUnit(e.space):0,marginTop:e.labelPos=="bottom"?r.addUnit(e.space):0,marginRight:e.labelPos=="left"?r.addUnit(e.space):0,marginBottom:e.labelPos=="top"?r.addUnit(e.space):0})},(0,we.toDisplayString)(e.label),5)):(0,we.createCommentVNode)("",!0)],2)}var Ve=Se(Si,[["render",xi],["styles",[wi]]]);var x=ze(Ne());var Ai="https://diytflservtest.eykj.cn",Ci="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJjb3JwX3Byb2R1Y3QiOiJjYW55aW4iLCJ0eXBlcyI6ImlzdiIsImNvcnBpZCI6ImRpbmdiOTYxNGRmOTQzNDJmNTcwYTEzMjBkY2IyNWU5MTM1MSIsImNvcnBfbmFtZSI6Ilx1NGUwMFx1NGUwMFx1NzlkMVx1NjI4MFx1NTE4NVx1OTBlOFx1NWYwMFx1NTNkMVx1NWU3M1x1NTNmMCIsInVzZXJpZCI6IjMzMzYzNTMzMjEyNDIxMDY2NCIsIm5hbWUiOiJcdTVmMjBcdTYzMmZcdTUzMTcxIiwic3RhZmZfbmFtZSI6Ilx1NWYyMFx1NjMyZlx1NTMxNzEiLCJzdGFmZmlkIjoiMzMzNjM1MzMyMTI0MjEwNjY0IiwiZGVwdF9pZF9saXN0IjoiWzI5OTkzOTAxNywgNDg1Mzg5NTMwLCA0ODYzNzAwMTgsIDY2NDk0NjI5OV0iLCJwYXJlbnRfaWRfbGlzdCI6WzEsMjk5OTM5MDE3XSwiZGluaW5naGFsbF9pZCI6OTMsImRpbmluZ2hhbGxfdGl0bGUiOiJcdTk5MTBcdTUzODUtXHU2YmI1IiwiYWRtaW4iOjF9.mLy2KnEIjCrp7Y-UHA8SrtBjfbdR7W2EvQtBhlE44bk",Ei="MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBKPfRCrkJ367IiaaxUjR0xIyO68pMwPEVWL/OqI78dtFPPW+zQQ1wb7YGk+pMJV2oa64cC5xZCLzPNHV6LaZe4hIxlxrcGed3aB1cLLNz0ujj1OplHD8PA2Hwlcz1bOo6U7VsQA2tYaXS9xGlBGgqNoGD3KHtDNYWiApA8FyJYwIDAQAB",Tt={COMMONAPI:Ai,publicKey:Ei,token:Ci},qe="_mallStore";function Ii(e,f,t){uni.setStorageSync(e,f);var a=parseInt(t);if(a>0){var m=Date.parse(new Date);m=m/1e3+a,uni.setStorageSync(e+qe,m+"")}else uni.removeStorageSync(e+qe)}function Bi(e,f){var t=parseInt(uni.getStorageSync(e+qe));if(t&&parseInt(t)<Date.parse(new Date)/1e3)return f||!1;var a=uni.getStorageSync(e);return a||((f==null||f=="")&&(f=!1),f)}function Ti(e){uni.removeStorageSync(e),uni.removeStorageSync(e+qe)}function ki(){uni.clearStorageSync()}var Pi={put:Ii,get:Bi,remove:Ti,clear:ki};function ji(e,f){try{uni.setStorageSync(e,f)}catch(t){}}function Oi(e){try{let f=uni.getStorageSync(e);if(f)return f}catch(f){}}var it={setData:ji,getData:Oi};function Ri(e){var f=null;if(e){var t=e.split(".");if(t.length>1?f=Tt[t[0]][t[1]]||null:f=Tt[e]||null,f==null){let a=Pi.get("web_config");a&&(t.length>1?f=a[t[0]][t[1]]||null:f=a[e]||null)}}return f}function Mi(e,f,t){return t=it.getData("Authorization"),e=this.config("COMMONAPI")+e,new Promise((a,m)=>{uni.request({url:e,data:f,method:"POST",header:{Authorization:t,serviceType:3},success:function(r){a.call(self,r.data)},fail:function(r){m.call(self,r)},complete:function(){}})})}function Ni(e,f,t){return t=it.getData("Authorization"),e="https://fileapitest.eykj.cn"+e,new Promise((a,m)=>{uni.request({url:e,data:f,method:"POST",header:{Authorization:t},success:function(r){a.call(self,r.data)},fail:function(r){m.call(self,r)},complete:function(){}})})}function Li(e,f,t){return t=it.getData("Authorization"),e=this.config("COMMONAPI")+e,new Promise((a,m)=>{uni.request({url:e,data:f,method:"GET",header:{Authorization:t,serviceType:3},success:function(r){a.call(self,r.data)},fail:function(r){m.call(self,r)}})})}var Me={config:Ri,get:Li,post:Mi,postimg:Ni};var de=ze(Ne());var ot="/static/image/left.png",Fi={header_cont:{"":{position:"fixed",top:0,left:0,width:100,zIndex:100}},"custom-header":{"":{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",paddingTop:"76rpx",height:"80rpx",backgroundColor:"rgba(255,255,255,0.0362)",position:"relative"}},left:{".custom-header ":{position:"absolute",height:"48rpx",width:"48rpx",left:"12rpx"}},iconfont:{"":{zIndex:100,height:"48rpx",width:"48rpx"}},title:{".custom-header ":{fontSize:"34rpx",fontWeight:"600",flex:1,color:"#ffffff",textAlign:"center"}},right:{".custom-header ":{position:"absolute",color:"#ffffff",right:15}},"icon-arrowleft":{"":{fontSize:20,color:"#ffffff",lineHeight:50}}},Ui={props:{title:{type:String,default:""},showBack:{type:Boolean,default:!1},showBack2:{type:Boolean,default:!1},showBack3:{type:Boolean,default:!1},rightContent:{type:String,default:""},rightType:{type:String,default:""},rightClick:{type:Function,default:null}},methods:{handleBack(){uni.navigateBack({delta:1})},handleBack2(){uni.switchTab({url:"/pages/apply/apply"})},handleBack3(){uni.switchTab({url:"/pages/user/user"})},handleRightClick(){this.rightClick&&typeof this.rightClick=="function"&&this.rightClick()}}};function zi(e,f,t,a,m,r){return(0,de.openBlock)(),(0,de.createElementBlock)("view",{class:"header_cont",renderWhole:!0},[(0,de.createElementVNode)("view",{class:"custom-header"},[t.showBack?((0,de.openBlock)(),(0,de.createElementBlock)("view",{key:0,class:"left"},[(0,de.createElementVNode)("u-image",{onClick:f[0]||(f[0]=(...i)=>r.handleBack&&r.handleBack(...i)),class:"iconfont",src:ot,mode:""})])):(0,de.createCommentVNode)("",!0),t.showBack2?((0,de.openBlock)(),(0,de.createElementBlock)("view",{key:1,class:"left"},[(0,de.createElementVNode)("u-image",{onClick:f[1]||(f[1]=(...i)=>r.handleBack2&&r.handleBack2(...i)),class:"iconfont",src:ot,mode:""})])):(0,de.createCommentVNode)("",!0),t.showBack3?((0,de.openBlock)(),(0,de.createElementBlock)("view",{key:2,class:"left"},[(0,de.createElementVNode)("u-image",{onClick:f[2]||(f[2]=(...i)=>r.handleBack3&&r.handleBack3(...i)),class:"iconfont",src:ot,mode:""})])):(0,de.createCommentVNode)("",!0),(0,de.createElementVNode)("view",{class:"title"},[(0,de.createElementVNode)("u-text",null,(0,de.toDisplayString)(t.title),1)]),t.rightContent||e.$slots.right?((0,de.openBlock)(),(0,de.createElementBlock)("view",{key:3,class:"right"},[t.rightType=="text"?((0,de.openBlock)(),(0,de.createElementBlock)("u-text",{key:0,onClick:f[3]||(f[3]=(...i)=>r.handleRightClick&&r.handleRightClick(...i))},(0,de.toDisplayString)(t.rightContent),1)):t.rightType=="slot"?((0,de.openBlock)(),(0,de.createElementBlock)("view",{key:1},[(0,de.renderSlot)(e.$slots,"right")])):(0,de.createCommentVNode)("",!0)])):(0,de.createCommentVNode)("",!0)])])}var kt=Se(Ui,[["render",zi],["styles",[Fi]]]);var Pt="/static/image/BG.png";var Di=Ce({props:{modelValue:{type:[String,Number],default:()=>X.input.value},type:{type:String,default:()=>X.input.type},fixed:{type:Boolean,default:()=>X.input.fixed},disabled:{type:Boolean,default:()=>X.input.disabled},disabledColor:{type:String,default:()=>X.input.disabledColor},clearable:{type:Boolean,default:()=>X.input.clearable},password:{type:Boolean,default:()=>X.input.password},maxlength:{type:[String,Number],default:()=>X.input.maxlength},placeholder:{type:String,default:()=>X.input.placeholder},placeholderClass:{type:String,default:()=>X.input.placeholderClass},placeholderStyle:{type:[String,Object],default:()=>X.input.placeholderStyle},showWordLimit:{type:Boolean,default:()=>X.input.showWordLimit},confirmType:{type:String,default:()=>X.input.confirmType},confirmHold:{type:Boolean,default:()=>X.input.confirmHold},holdKeyboard:{type:Boolean,default:()=>X.input.holdKeyboard},focus:{type:Boolean,default:()=>X.input.focus},autoBlur:{type:Boolean,default:()=>X.input.autoBlur},disableDefaultPadding:{type:Boolean,default:()=>X.input.disableDefaultPadding},cursor:{type:[String,Number],default:()=>X.input.cursor},cursorSpacing:{type:[String,Number],default:()=>X.input.cursorSpacing},selectionStart:{type:[String,Number],default:()=>X.input.selectionStart},selectionEnd:{type:[String,Number],default:()=>X.input.selectionEnd},adjustPosition:{type:Boolean,default:()=>X.input.adjustPosition},inputAlign:{type:String,default:()=>X.input.inputAlign},fontSize:{type:[String,Number],default:()=>X.input.fontSize},color:{type:String,default:()=>X.input.color},prefixIcon:{type:String,default:()=>X.input.prefixIcon},prefixIconStyle:{type:[String,Object],default:()=>X.input.prefixIconStyle},suffixIcon:{type:String,default:()=>X.input.suffixIcon},suffixIconStyle:{type:[String,Object],default:()=>X.input.suffixIconStyle},border:{type:String,default:()=>X.input.border},readonly:{type:Boolean,default:()=>X.input.readonly},shape:{type:String,default:()=>X.input.shape},formatter:{type:[Function,null],default:()=>X.input.formatter},ignoreCompositionEvent:{type:Boolean,default:!0}}}),Qi={"u-input":{"":{flexDirection:"row",alignItems:"center",justifyContent:"space-between",flex:1}},"u-input--radius":{"":{borderRadius:4}},"u-input--square":{"":{borderRadius:4}},"u-input--no-radius":{"":{borderRadius:0}},"u-input--circle":{"":{borderRadius:100}},"u-input__content":{"":{flex:1,flexDirection:"row",alignItems:"center",justifyContent:"space-between"}},"u-input__content__field-wrapper":{"":{position:"relative",flexDirection:"row",marginTop:0,marginRight:0,marginBottom:0,marginLeft:0,flex:1}},"u-input__content__field-wrapper__field":{"":{lineHeight:26,textAlign:"left",color:"#303133",height:24,fontSize:15,flex:1}},"u-input__content__clear":{"":{width:20,height:20,borderRadius:100,backgroundColor:"#c6c7cb",flexDirection:"row",alignItems:"center",justifyContent:"center",transform:"scale(0.82)",marginLeft:4}},"u-input__content__subfix-icon":{"":{marginLeft:4}},"u-input__content__prefix-icon":{"":{marginRight:4}}},Hi={name:"u-input",mixins:[Be,Te,Di],data(){return{clearInput:!1,innerValue:"",focused:!1,firstChange:!0,changeFromInner:!1,innerFormatter:e=>e}},created(){this.formatter&&(this.innerFormatter=this.formatter)},watch:{modelValue:{immediate:!0,handler(e,f){if(this.changeFromInner||this.innerValue===e){this.changeFromInner=!1;return}this.innerValue=e,this.firstChange===!1&&this.changeFromInner===!1?this.valueChange(this.innerValue,!0):this.firstChange||We(this,"change"),this.firstChange=!1,this.changeFromInner=!1}}},computed:{isShowClear(){let{clearable:e,readonly:f,focused:t,innerValue:a}=this;return!!e&&!f&&!!t&&a!==""},inputClass(){let e=[],{border:f,disabled:t,shape:a}=this;return f==="surround"&&(e=e.concat(["u-border","u-input--radius"])),e.push(`u-input--${a}`),f==="bottom"&&(e=e.concat(["u-border-bottom","u-input--no-radius"])),e.join(" ")},wrapperStyle(){let e={};return this.disabled&&(e.backgroundColor=this.disabledColor),this.border==="none"?e.padding="0":(e.paddingTop="6px",e.paddingBottom="6px",e.paddingLeft="9px",e.paddingRight="9px"),xe(e,Pe(this.customStyle))},inputStyle(){return{color:this.color,fontSize:ve(this.fontSize),textAlign:this.inputAlign}}},emits:["update:modelValue","focus","blur","change","confirm","clear","keyboardheightchange","nicknamereview"],methods:{setFormatter(e){this.innerFormatter=e},onInput(e){let{value:f=""}=e.detail||{};this.innerValue=f,this.$nextTick(()=>{let t=this.innerFormatter(f);this.innerValue=t,this.valueChange(t)})},onBlur(e){this.$emit("blur",e.detail.value),nt(150).then(()=>{this.focused=!1}),We(this,"blur")},onFocus(e){this.focused=!0,this.$emit("focus")},doFocus(){this.$refs["input-native"].focus()},doBlur(){this.$refs["input-native"].blur()},onConfirm(e){this.$emit("confirm",this.innerValue)},onkeyboardheightchange(e){this.$emit("keyboardheightchange",e)},onnicknamereview(e){this.$emit("nicknamereview",e)},valueChange(e,f=!1){this.clearInput&&(this.innerValue="",this.clearInput=!1),this.$nextTick(()=>{(!f||this.clearInput)&&(this.changeFromInner=!0,this.$emit("change",e),this.$emit("update:modelValue",e)),We(this,"change")})},onClear(){this.clearInput=!0,this.innerValue="",this.$nextTick(()=>{this.valueChange(""),this.$emit("clear")})},clickHandler(){if((this.disabled||this.readonly)&&uni.hideKeyboard(),Et()==="android"){let e=Ue.call(this,"u-form-item");e&&e.clickHandler()}}}};function Wi(e,f,t,a,m,r){let i=_e((0,x.resolveDynamicComponent)("u-icon"),Ve);return(0,x.openBlock)(),(0,x.createElementBlock)("view",{class:(0,x.normalizeClass)(["u-input",r.inputClass]),style:(0,x.normalizeStyle)([r.wrapperStyle]),renderWhole:!0},[(0,x.createElementVNode)("view",{class:"u-input__content"},[e.prefixIcon||e.$slots.prefix?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:"u-input__content__prefix-icon"},[(0,x.renderSlot)(e.$slots,"prefix",{},()=>[(0,x.createVNode)(i,{name:e.prefixIcon,size:"18",customStyle:e.prefixIconStyle},null,8,["name","customStyle"])])])):(0,x.createCommentVNode)("",!0),(0,x.createElementVNode)("view",{class:"u-input__content__field-wrapper",onClick:f[6]||(f[6]=(...p)=>r.clickHandler&&r.clickHandler(...p))},[(0,x.createElementVNode)("u-input",{ref:"input-native",class:"u-input__content__field-wrapper__field",style:(0,x.normalizeStyle)([r.inputStyle]),type:e.type,focus:e.focus,cursor:e.cursor,value:m.innerValue,autoBlur:e.autoBlur,disabled:e.disabled||e.readonly,maxlength:e.maxlength,placeholder:e.placeholder,placeholderStyle:e.placeholderStyle,placeholderClass:e.placeholderClass,confirmType:e.confirmType,confirmHold:e.confirmHold,holdKeyboard:e.holdKeyboard,cursorSpacing:e.cursorSpacing,adjustPosition:e.adjustPosition,selectionEnd:e.selectionEnd,selectionStart:e.selectionStart,password:e.password||e.type==="password"||!1,ignoreCompositionEvent:e.ignoreCompositionEvent,onInput:f[0]||(f[0]=(...p)=>r.onInput&&r.onInput(...p)),onBlur:f[1]||(f[1]=(...p)=>r.onBlur&&r.onBlur(...p)),onFocus:f[2]||(f[2]=(...p)=>r.onFocus&&r.onFocus(...p)),onConfirm:f[3]||(f[3]=(...p)=>r.onConfirm&&r.onConfirm(...p)),onKeyboardheightchange:f[4]||(f[4]=(...p)=>r.onkeyboardheightchange&&r.onkeyboardheightchange(...p)),onNicknamereview:f[5]||(f[5]=(...p)=>r.onnicknamereview&&r.onnicknamereview(...p))},null,44,["type","focus","cursor","value","autoBlur","disabled","maxlength","placeholder","placeholderStyle","placeholderClass","confirmType","confirmHold","holdKeyboard","cursorSpacing","adjustPosition","selectionEnd","selectionStart","password","ignoreCompositionEvent"])]),r.isShowClear?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:1,class:"u-input__content__clear",onClick:f[7]||(f[7]=(...p)=>r.onClear&&r.onClear(...p))},[(0,x.createVNode)(i,{name:"close",size:"11",color:"#ffffff",customStyle:"line-height: 12px"})])):(0,x.createCommentVNode)("",!0),e.suffixIcon||e.$slots.suffix?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:2,class:"u-input__content__subfix-icon"},[(0,x.renderSlot)(e.$slots,"suffix",{},()=>[(0,x.createVNode)(i,{name:e.suffixIcon,size:"18",customStyle:e.suffixIconStyle},null,8,["name","customStyle"])])])):(0,x.createCommentVNode)("",!0)])],6)}var Vi=Se(Hi,[["render",Wi],["styles",[Qi]]]),qi=Ce({props:{color:{type:String,default:()=>X.line.color},length:{type:[String,Number],default:()=>X.line.length},direction:{type:String,default:()=>X.line.direction},hairline:{type:Boolean,default:()=>X.line.hairline},margin:{type:[String,Number],default:()=>X.line.margin},dashed:{type:Boolean,default:()=>X.line.dashed}}}),Ki={},Yi={name:"u-line",mixins:[Be,Te,qi],computed:{lineStyle(){let e={};return e.margin=this.margin,this.direction==="row"?(e.borderBottomWidth="1px",e.borderBottomStyle=this.dashed?"dashed":"solid",e.width=ve(this.length),this.hairline&&(e.transform="scaleY(0.5)")):(e.borderLeftWidth="1px",e.borderLeftStyle=this.dashed?"dashed":"solid",e.height=ve(this.length),this.hairline&&(e.transform="scaleX(0.5)")),e.borderColor=this.color,xe(e,Pe(this.customStyle))}}};function Gi(e,f,t,a,m,r){return(0,x.openBlock)(),(0,x.createElementBlock)("view",{class:"u-line",style:(0,x.normalizeStyle)([r.lineStyle]),renderWhole:!0},null,4)}var Nt=Se(Yi,[["render",Gi],["styles",[Ki]]]),Xi=Ce({props:{show:{type:Boolean,default:()=>X.loadingIcon.show},color:{type:String,default:()=>X.loadingIcon.color},textColor:{type:String,default:()=>X.loadingIcon.textColor},vertical:{type:Boolean,default:()=>X.loadingIcon.vertical},mode:{type:String,default:()=>X.loadingIcon.mode},size:{type:[String,Number],default:()=>X.loadingIcon.size},textSize:{type:[String,Number],default:()=>X.loadingIcon.textSize},text:{type:[String,Number],default:()=>X.loadingIcon.text},timingFunction:{type:String,default:()=>X.loadingIcon.timingFunction},duration:{type:[String,Number],default:()=>X.loadingIcon.duration},inactiveColor:{type:String,default:()=>X.loadingIcon.inactiveColor}}});function Ji(e="rgb(0, 0, 0)",f="rgb(255, 255, 255)",t=10){let a=jt(e,!1),m=a[0],r=a[1],i=a[2],p=jt(f,!1),h=p[0],c=p[1],o=p[2],n=(h-m)/t,d=(c-r)/t,g=(o-i)/t,E=[];for(let u=0;u<t;u++){let _=st(`rgb(${Math.round(n*u+m)},${Math.round(d*u+r)},${Math.round(g*u+i)})`);u===0&&(_=st(e)),u===t-1&&(_=st(f)),E.push(_)}return E}function jt(e,f=!0){let t=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;if(e=String(e).toLowerCase(),e&&t.test(e)){if(e.length===4){let m="#";for(let r=1;r<4;r+=1)m+=e.slice(r,r+1).concat(e.slice(r,r+1));e=m}let a=[];for(let m=1;m<7;m+=2)a.push(parseInt(`0x${e.slice(m,m+2)}`));return f?`rgb(${a[0]},${a[1]},${a[2]})`:a}return/^(rgb|RGB)/.test(e)?e.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(",").map(m=>Number(m)):e}function st(e){let f=e,t=/^#([0-9a-fA-f]{3}|[0-9a-fA-f]{6})$/;if(/^(rgb|RGB)/.test(f)){let a=f.replace(/(?:\(|\)|rgb|RGB)*/g,"").split(","),m="#";for(let r=0;r<a.length;r++){let i=Number(a[r]).toString(16);i=String(i).length==1?`0${i}`:i,i==="0"&&(i+=i),m+=i}return m.length!==7&&(m=f),m}if(t.test(f)){let a=f.replace(/#/,"").split("");if(a.length===6)return f;if(a.length===3){let m="#";for(let r=0;r<a.length;r+=1)m+=a[r]+a[r];return m}}else return f}var Zi={"u-loading-icon":{"":{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"center",color:"#c8c9cc"}},"u-loading-icon__text":{"":{marginLeft:4,color:"#606266",fontSize:14,lineHeight:20}},"u-loading-icon__spinner":{"":{width:30,height:30,position:"relative"}},"u-loading-icon__spinner--semicircle":{"":{borderWidth:2,borderColor:"rgba(0,0,0,0)",borderTopRightRadius:100,borderTopLeftRadius:100,borderBottomLeftRadius:100,borderBottomRightRadius:100,borderStyle:"solid"}},"u-loading-icon__spinner--circle":{"":{borderTopRightRadius:100,borderTopLeftRadius:100,borderBottomLeftRadius:100,borderBottomRightRadius:100,borderWidth:2,borderTopColor:"#e5e5e5",borderRightColor:"#e5e5e5",borderBottomColor:"#e5e5e5",borderLeftColor:"#e5e5e5",borderStyle:"solid"}},"u-loading-icon--vertical":{"":{flexDirection:"column"}}},$i=weex.requireModule("animation"),eo={name:"u-loading-icon",mixins:[Be,Te,Xi],data(){return{array12:Array.from({length:12}),aniAngel:360,webviewHide:!1,loading:!1}},computed:{otherBorderColor(){let e=Ji(this.color,"#ffffff",100)[80];return this.mode==="circle"?this.inactiveColor?this.inactiveColor:e:"transparent"}},watch:{show(e){e&&!this.loading&&setTimeout(()=>{this.startAnimate()},30)}},mounted(){this.init()},methods:{addUnit:ve,addStyle:Pe,init(){setTimeout(()=>{this.show&&this.nvueAnimate(),this.show&&this.addEventListenerToWebview()},20)},addEventListenerToWebview(){let e=getCurrentPages(),t=e[e.length-1].$getAppWebview();t.addEventListener("hide",()=>{this.webviewHide=!0}),t.addEventListener("show",()=>{this.webviewHide=!1})},nvueAnimate(){this.mode!=="spinner"&&this.startAnimate()},startAnimate(){this.loading=!0;let e=this.$refs.ani;e&&$i.transition(e,{styles:{transform:`rotate(${this.aniAngel}deg)`,transformOrigin:"center center"},duration:this.duration,timingFunction:this.timingFunction},()=>{this.aniAngel+=360,this.show&&!this.webviewHide?this.startAnimate():this.loading=!1})}}};function to(e,f,t,a,m,r){return e.show?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:(0,x.normalizeClass)(["u-loading-icon",[e.vertical&&"u-loading-icon--vertical"]]),style:(0,x.normalizeStyle)([r.addStyle(e.customStyle)]),renderWhole:!0},[m.webviewHide?(0,x.createCommentVNode)("",!0):((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:(0,x.normalizeClass)(["u-loading-icon__spinner",[`u-loading-icon__spinner--${e.mode}`]]),ref:"ani",style:(0,x.normalizeStyle)({color:e.color,width:r.addUnit(e.size),height:r.addUnit(e.size),borderTopColor:e.color,borderBottomColor:r.otherBorderColor,borderLeftColor:r.otherBorderColor,borderRightColor:r.otherBorderColor,"animation-duration":`${e.duration}ms`,"animation-timing-function":e.mode==="semicircle"||e.mode==="circle"?e.timingFunction:""})},[e.mode==="spinner"?((0,x.openBlock)(),(0,x.createElementBlock)(x.Fragment,{key:0},[m.webviewHide?(0,x.createCommentVNode)("",!0):((0,x.openBlock)(),(0,x.createElementBlock)("loading-indicator",{key:0,class:"u-loading-indicator",animating:!0,style:(0,x.normalizeStyle)({color:e.color,width:r.addUnit(e.size),height:r.addUnit(e.size)})},null,4))],64)):(0,x.createCommentVNode)("",!0)],6)),e.text?((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:1,class:"u-loading-icon__text",style:(0,x.normalizeStyle)({fontSize:r.addUnit(e.textSize),color:e.textColor})},(0,x.toDisplayString)(e.text),5)):(0,x.createCommentVNode)("",!0)],6)):(0,x.createCommentVNode)("",!0)}var Lt=Se(eo,[["render",to],["styles",[Zi]]]),ro=Ce({props:{show:{type:Boolean,default:()=>X.transition.show},mode:{type:String,default:()=>X.transition.mode},duration:{type:[String,Number],default:()=>X.transition.duration},timingFunction:{type:String,default:()=>X.transition.timingFunction}}}),no={fade:{enter:{opacity:0},"enter-to":{opacity:1},leave:{opacity:1},"leave-to":{opacity:0}},"fade-up":{enter:{opacity:0,transform:"translateY(100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateY(100%)"}},"fade-down":{enter:{opacity:0,transform:"translateY(-100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateY(-100%)"}},"fade-left":{enter:{opacity:0,transform:"translateX(-100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateX(-100%)"}},"fade-right":{enter:{opacity:0,transform:"translateX(100%)"},"enter-to":{opacity:1,transform:"translateY(0)"},leave:{opacity:1,transform:"translateY(0)"},"leave-to":{opacity:0,transform:"translateX(100%)"}},"slide-up":{enter:{transform:"translateY(100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateY(100%)"}},"slide-down":{enter:{transform:"translateY(-100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateY(-100%)"}},"slide-left":{enter:{transform:"translateX(-100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateX(-100%)"}},"slide-right":{enter:{transform:"translateX(100%)"},"enter-to":{transform:"translateY(0)"},leave:{transform:"translateY(0)"},"leave-to":{transform:"translateX(100%)"}},zoom:{enter:{transform:"scale(0.95)"},"enter-to":{transform:"scale(1)"},leave:{transform:"scale(1)"},"leave-to":{transform:"scale(0.95)"}},"fade-zoom":{enter:{opacity:0,transform:"scale(0.95)"},"enter-to":{opacity:1,transform:"scale(1)"},leave:{opacity:1,transform:"scale(1)"},"leave-to":{opacity:0,transform:"scale(0.95)"}}},Ot=()=>new Promise(e=>setTimeout(e,1e3/50)),Rt=De("animation"),Mt=e=>no[e],io={methods:{clickHandler(){this.$emit("click")},nvueEnter(){return ke(this,null,function*(){let e=Mt(this.mode);this.status="enter",this.$emit("beforeEnter"),this.inited=!0,this.display=!0,this.viewStyle={opacity:0},yield(0,x.nextTick)(),this.viewStyle=e.enter,Promise.resolve().then(Ot).then(()=>{this.$emit("enter"),Rt.transition(this.$refs["u-transition"].ref,{styles:e["enter-to"],duration:this.duration,timingFunction:this.timingFunction,needLayout:!1,delay:0},()=>{this.$emit("afterEnter")})}).catch(()=>{})})},nvueLeave(){if(!this.display)return;let e=Mt(this.mode);this.status="leave",this.$emit("beforeLeave"),this.viewStyle=e.leave,Promise.resolve().then(Ot).then(()=>{this.transitionEnded=!1,this.$emit("leave"),Rt.transition(this.$refs["u-transition"].ref,{styles:e["leave-to"],duration:this.duration,timingFunction:this.timingFunction,needLayout:!1,delay:0},()=>{this.onTransitionEnd()})}).catch(()=>{})},onTransitionEnd(){this.transitionEnded||(this.transitionEnded=!0,this.$emit(this.status==="leave"?"afterLeave":"afterEnter"),!this.show&&this.display&&(this.display=!1,this.inited=!1))}}},oo={},so={name:"u-transition",data(){return{inited:!1,viewStyle:{},status:"",transitionEnded:!1,display:!1,classes:""}},emits:["click","beforeEnter","enter","afterEnter","beforeLeave","leave","afterLeave"],computed:{mergeStyle(){let{viewStyle:e,customStyle:f}=this;return ne(ne({},Pe(f)),e)}},mixins:[Be,Te,io,ro],watch:{show:{handler(e){e?this.nvueEnter():this.nvueLeave()},immediate:!0}}};function ao(e,f,t,a,m,r){return m.inited?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:(0,x.normalizeClass)(["u-transition",m.classes]),ref:"u-transition",onClick:f[0]||(f[0]=(...i)=>e.clickHandler&&e.clickHandler(...i)),style:(0,x.normalizeStyle)([r.mergeStyle]),onTouchmove:f[1]||(f[1]=(...i)=>e.noop&&e.noop(...i)),renderWhole:!0},[(0,x.renderSlot)(e.$slots,"default")],38)):(0,x.createCommentVNode)("",!0)}var Ft=Se(so,[["render",ao],["styles",[oo]]]),lo=Ce({props:{show:{type:Boolean,default:()=>X.overlay.show},zIndex:{type:[String,Number],default:()=>X.overlay.zIndex},duration:{type:[String,Number],default:()=>X.overlay.duration},opacity:{type:[String,Number],default:()=>X.overlay.opacity}}}),uo={"u-overlay":{"":{position:"fixed",top:0,left:0,width:100,height:100,backgroundColor:"rgba(0,0,0,0.7)"}}},co={name:"u-overlay",mixins:[Be,Te,lo],computed:{overlayStyle(){let e={position:"fixed",top:0,left:0,right:0,zIndex:this.zIndex,bottom:0,"background-color":`rgba(0, 0, 0, ${this.opacity})`};return xe(e,Pe(this.customStyle))}},emits:["click"],methods:{clickHandler(){this.$emit("click")}}};function fo(e,f,t,a,m,r){let i=_e((0,x.resolveDynamicComponent)("u-transition"),Ft);return(0,x.openBlock)(),(0,x.createBlock)(i,{show:e.show,"custom-class":"u-overlay",duration:e.duration,"custom-style":r.overlayStyle,onClick:r.clickHandler,onTouchmove:(0,x.withModifiers)(e.noop,["stop","prevent"])},{default:(0,x.withCtx)(()=>[(0,x.renderSlot)(e.$slots,"default")]),_:3},8,["show","duration","custom-style","onClick","onTouchmove"])}var po=Se(co,[["render",fo],["styles",[uo]]]),ho=Ce({props:{bgColor:{type:String,default:()=>X.statusBar.bgColor}}}),mo={},go={name:"u-status-bar",mixins:[Be,Te,ho],data(){return{isH5:!1}},created(){},computed:{style(){let e={},f=He().statusBarHeight;return f==0?this.isH5=!0:e.height=ve(f,"px"),e.backgroundColor=this.bgColor,xe(e,Pe(this.customStyle))}}};function yo(e,f,t,a,m,r){return(0,x.openBlock)(),(0,x.createElementBlock)("view",{style:(0,x.normalizeStyle)([r.style]),class:(0,x.normalizeClass)(["u-status-bar",[m.isH5&&"u-safe-area-inset-top"]]),renderWhole:!0},[(0,x.renderSlot)(e.$slots,"default")],6)}var bo=Se(go,[["render",yo],["styles",[mo]]]),vo=Ce({props:{}}),wo={},_o={name:"u-safe-bottom",mixins:[Be,Te,vo],data(){return{safeAreaBottomHeight:0,isNvue:!1}},computed:{style(){let e={};return e.height=ve(He().safeAreaInsets.bottom,"px"),xe(e,Pe(this.customStyle))}},mounted(){this.isNvue=!0}};function So(e,f,t,a,m,r){return(0,x.openBlock)(),(0,x.createElementBlock)("view",{class:(0,x.normalizeClass)(["u-safe-bottom",[!m.isNvue&&"u-safe-area-inset-bottom"]]),style:(0,x.normalizeStyle)([r.style]),renderWhole:!0},null,6)}var xo=Se(_o,[["render",So],["styles",[wo]]]),Ao=Ce({props:{show:{type:Boolean,default:()=>X.popup.show},overlay:{type:Boolean,default:()=>X.popup.overlay},mode:{type:String,default:()=>X.popup.mode},duration:{type:[String,Number],default:()=>X.popup.duration},closeable:{type:Boolean,default:()=>X.popup.closeable},overlayStyle:{type:[Object,String],default:()=>X.popup.overlayStyle},closeOnClickOverlay:{type:Boolean,default:()=>X.popup.closeOnClickOverlay},zIndex:{type:[String,Number],default:()=>X.popup.zIndex},safeAreaInsetBottom:{type:Boolean,default:()=>X.popup.safeAreaInsetBottom},safeAreaInsetTop:{type:Boolean,default:()=>X.popup.safeAreaInsetTop},closeIconPos:{type:String,default:()=>X.popup.closeIconPos},round:{type:[Boolean,String,Number],default:()=>X.popup.round},zoom:{type:Boolean,default:()=>X.popup.zoom},bgColor:{type:String,default:()=>X.popup.bgColor},overlayOpacity:{type:[Number,String],default:()=>X.popup.overlayOpacity}}}),Co={"u-popup":{"":{flex:1}},"u-popup__content":{"":{backgroundColor:"#ffffff",position:"relative"}},"u-popup__content--round-top":{"":{borderTopLeftRadius:0,borderTopRightRadius:0,borderBottomLeftRadius:10,borderBottomRightRadius:10}},"u-popup__content--round-left":{"":{borderTopLeftRadius:0,borderTopRightRadius:10,borderBottomLeftRadius:0,borderBottomRightRadius:10}},"u-popup__content--round-right":{"":{borderTopLeftRadius:10,borderTopRightRadius:0,borderBottomLeftRadius:10,borderBottomRightRadius:0}},"u-popup__content--round-bottom":{"":{borderTopLeftRadius:10,borderTopRightRadius:10,borderBottomLeftRadius:0,borderBottomRightRadius:0}},"u-popup__content--round-center":{"":{borderTopLeftRadius:10,borderTopRightRadius:10,borderBottomLeftRadius:10,borderBottomRightRadius:10}},"u-popup__content__close":{"":{position:"absolute"}},"u-popup__content__close--hover":{"":{opacity:.4}},"u-popup__content__close--top-left":{"":{top:15,left:15}},"u-popup__content__close--top-right":{"":{top:15,right:15}},"u-popup__content__close--bottom-left":{"":{bottom:15,left:15}},"u-popup__content__close--bottom-right":{"":{right:15,bottom:15}}},Eo={name:"u-popup",mixins:[Be,Te,Ao],data(){return{overlayDuration:this.duration+50}},watch:{show(e,f){}},computed:{transitionStyle(){let e={zIndex:this.zIndex,position:"fixed",display:"flex"};if(e[this.mode]=0,this.mode==="left")return xe(e,{bottom:0,top:0});if(this.mode==="right")return xe(e,{bottom:0,top:0});if(this.mode==="top")return xe(e,{left:0,right:0});if(this.mode==="bottom")return xe(e,{left:0,right:0});if(this.mode==="center")return xe(e,{alignItems:"center","justify-content":"center",top:0,left:0,right:0,bottom:0})},contentStyle(){let e={};if(He(),this.mode!=="center"&&(e.flex=1),this.bgColor&&(e.backgroundColor=this.bgColor),this.round){let f=ve(this.round);this.mode==="top"?(e.borderBottomLeftRadius=f,e.borderBottomRightRadius=f):this.mode==="bottom"?(e.borderTopLeftRadius=f,e.borderTopRightRadius=f):this.mode==="center"&&(e.borderRadius=f)}return xe(e,Pe(this.customStyle))},position(){if(this.mode==="center")return this.zoom?"fade-zoom":"fade";if(this.mode==="left")return"slide-left";if(this.mode==="right")return"slide-right";if(this.mode==="bottom")return"slide-up";if(this.mode==="top")return"slide-down"}},emits:["open","close","click","update:show"],methods:{overlayClick(){this.closeOnClickOverlay&&(this.$emit("update:show",!1),this.$emit("close"))},close(e){this.$emit("update:show",!1),this.$emit("close")},afterEnter(){this.$emit("open")},clickHandler(){this.mode==="center"&&this.overlayClick(),this.$emit("click")}}};function Io(e,f,t,a,m,r){let i=_e((0,x.resolveDynamicComponent)("u-overlay"),po),p=_e((0,x.resolveDynamicComponent)("u-status-bar"),bo),h=_e((0,x.resolveDynamicComponent)("u-icon"),Ve),c=_e((0,x.resolveDynamicComponent)("u-safe-bottom"),xo),o=_e((0,x.resolveDynamicComponent)("u-transition"),Ft);return(0,x.openBlock)(),(0,x.createElementBlock)("view",{class:(0,x.normalizeClass)(["u-popup",[e.customClass]]),renderWhole:!0},[e.overlay?((0,x.openBlock)(),(0,x.createBlock)(i,{key:0,show:e.show,onClick:r.overlayClick,zIndex:e.zIndex,duration:m.overlayDuration,customStyle:e.overlayStyle,opacity:e.overlayOpacity},null,8,["show","onClick","zIndex","duration","customStyle","opacity"])):(0,x.createCommentVNode)("",!0),(0,x.createVNode)(o,{show:e.show,customStyle:r.transitionStyle,mode:r.position,duration:e.duration,onAfterEnter:r.afterEnter,onClick:r.clickHandler},{default:(0,x.withCtx)(()=>[(0,x.createElementVNode)("view",{class:"u-popup__content",style:(0,x.normalizeStyle)([r.contentStyle]),onClick:f[1]||(f[1]=(0,x.withModifiers)((...n)=>e.noop&&e.noop(...n),["stop"])),onTouchmove:f[2]||(f[2]=(0,x.withModifiers)((...n)=>e.noop&&e.noop(...n),["stop","prevent"]))},[e.safeAreaInsetTop?((0,x.openBlock)(),(0,x.createBlock)(p,{key:0})):(0,x.createCommentVNode)("",!0),(0,x.renderSlot)(e.$slots,"default"),e.closeable?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:1,onClick:f[0]||(f[0]=(0,x.withModifiers)((...n)=>r.close&&r.close(...n),["stop"])),class:(0,x.normalizeClass)(["u-popup__content__close",["u-popup__content__close--"+e.closeIconPos]]),hoverClass:"u-popup__content__close--hover",hoverStayTime:"150"},[(0,x.createVNode)(h,{name:"close",color:"#909399",size:"18",bold:""})],2)):(0,x.createCommentVNode)("",!0),e.safeAreaInsetBottom?((0,x.openBlock)(),(0,x.createBlock)(c,{key:2})):(0,x.createCommentVNode)("",!0)],36)]),_:3},8,["show","customStyle","mode","duration","onAfterEnter","onClick"])],2)}var Bo=Se(Eo,[["render",Io],["styles",[Co]]]),To=Ce({props:{show:{type:Boolean,default:()=>X.modal.show},title:{type:[String],default:()=>X.modal.title},content:{type:String,default:()=>X.modal.content},confirmText:{type:String,default:()=>X.modal.confirmText},cancelText:{type:String,default:()=>X.modal.cancelText},showConfirmButton:{type:Boolean,default:()=>X.modal.showConfirmButton},showCancelButton:{type:Boolean,default:()=>X.modal.showCancelButton},confirmColor:{type:String,default:()=>X.modal.confirmColor},cancelColor:{type:String,default:()=>X.modal.cancelColor},buttonReverse:{type:Boolean,default:()=>X.modal.buttonReverse},zoom:{type:Boolean,default:()=>X.modal.zoom},asyncClose:{type:Boolean,default:()=>X.modal.asyncClose},closeOnClickOverlay:{type:Boolean,default:()=>X.modal.closeOnClickOverlay},negativeTop:{type:[String,Number],default:()=>X.modal.negativeTop},width:{type:[String,Number],default:()=>X.modal.width},confirmButtonShape:{type:String,default:()=>X.modal.confirmButtonShape},contentTextAlign:{type:String,default:()=>X.modal.contentTextAlign},asyncCloseTip:{type:String,default:()=>X.modal.asyncCloseTip},asyncCancelClose:{type:Boolean,default:()=>X.modal.asyncCancelClose}}}),ko={"u-modal":{"":{width:"650rpx",borderRadius:6,overflow:"hidden"}},"u-modal__title":{"":{display:"flex",flexDirection:"column",justifyContent:"center",alignItems:"center",fontSize:16,fontWeight:"bold",color:"#606266",textAlign:"center",paddingTop:25}},"u-modal__content":{"":{paddingTop:12,paddingRight:25,paddingBottom:25,paddingLeft:25,flexDirection:"row",justifyContent:"center"}},"u-modal__content__text":{"":{fontSize:15,color:"#606266",flex:1}},"u-modal__button-group":{"":{flexDirection:"row"}},"u-modal__button-group--confirm-button":{"":{flexDirection:"column",paddingTop:0,paddingRight:25,paddingBottom:15,paddingLeft:25}},"u-modal__button-group__wrapper":{"":{flex:1,flexDirection:"row",justifyContent:"center",alignItems:"center",height:48}},"u-modal__button-group__wrapper--confirm":{"":{borderBottomRightRadius:6}},"u-modal__button-group__wrapper--only-cancel":{"":{borderBottomRightRadius:6}},"u-modal__button-group__wrapper--cancel":{"":{borderBottomLeftRadius:6}},"u-modal__button-group__wrapper--only-confirm":{"":{borderBottomLeftRadius:6}},"u-modal__button-group__wrapper--hover":{"":{backgroundColor:"#f3f4f6"}},"u-modal__button-group__wrapper__text":{"":{color:"#606266",fontSize:16,textAlign:"center"}}},Po={name:"u-modal",mixins:[Be,Te,To],data(){return{loading:!1}},watch:{show(e){e&&this.loading&&(this.loading=!1)}},emits:["confirm","cancel","close","update:show","cancelOnAsync"],methods:{addUnit:ve,confirmHandler(){this.asyncClose?this.loading=!0:this.$emit("update:show",!1),this.$emit("confirm")},cancelHandler(){this.asyncClose&&this.loading?(this.asyncCloseTip&&uni.showToast({title:this.asyncCloseTip,icon:"none"}),this.$emit("cancelOnAsync")):this.asyncCancelClose||this.$emit("update:show",!1),this.$emit("cancel")},clickHandler(){this.closeOnClickOverlay&&(this.$emit("update:show",!1),this.$emit("close"))}}};function jo(e,f,t,a,m,r){let i=_e((0,x.resolveDynamicComponent)("u-line"),Nt),p=_e((0,x.resolveDynamicComponent)("u-loading-icon"),Lt),h=_e((0,x.resolveDynamicComponent)("u-popup"),Bo);return(0,x.openBlock)(),(0,x.createBlock)(h,{mode:"center",zoom:e.zoom,show:e.show,class:(0,x.normalizeClass)([e.customClass]),customStyle:{borderRadius:"6px",overflow:"hidden",marginTop:`-${r.addUnit(e.negativeTop)}`},closeOnClickOverlay:e.closeOnClickOverlay,safeAreaInsetBottom:!1,duration:400,onClick:r.clickHandler},{default:(0,x.withCtx)(()=>[(0,x.createElementVNode)("view",{class:"u-modal",style:(0,x.normalizeStyle)({width:r.addUnit(e.width)})},[e.title?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:"u-modal__title"},[(0,x.createElementVNode)("u-text",null,(0,x.toDisplayString)(e.title),1)])):(0,x.createCommentVNode)("",!0),(0,x.createElementVNode)("view",{class:"u-modal__content",style:(0,x.normalizeStyle)({paddingTop:`${e.title?12:25}px`})},[(0,x.renderSlot)(e.$slots,"default",{},()=>[(0,x.createElementVNode)("u-text",{class:"u-modal__content__text",style:(0,x.normalizeStyle)({textAlign:e.contentTextAlign})},(0,x.toDisplayString)(e.content),5)])],4),e.$slots.confirmButton?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:1,class:"u-modal__button-group--confirm-button"},[(0,x.renderSlot)(e.$slots,"confirmButton")])):((0,x.openBlock)(),(0,x.createElementBlock)(x.Fragment,{key:2},[(0,x.createVNode)(i),(0,x.createElementVNode)("view",{class:"u-modal__button-group",style:(0,x.normalizeStyle)({flexDirection:e.buttonReverse?"row-reverse":"row"})},[e.showCancelButton?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:(0,x.normalizeClass)(["u-modal__button-group__wrapper u-modal__button-group__wrapper--cancel",[e.showCancelButton&&!e.showConfirmButton&&"u-modal__button-group__wrapper--only-cancel"]]),hoverStayTime:150,hoverClass:"u-modal__button-group__wrapper--hover",onClick:f[0]||(f[0]=(...c)=>r.cancelHandler&&r.cancelHandler(...c))},[(0,x.createElementVNode)("u-text",{class:"u-modal__button-group__wrapper__text",style:(0,x.normalizeStyle)({color:e.cancelColor})},(0,x.toDisplayString)(e.cancelText),5)],2)):(0,x.createCommentVNode)("",!0),e.showConfirmButton&&e.showCancelButton?((0,x.openBlock)(),(0,x.createBlock)(i,{key:1,direction:"column"})):(0,x.createCommentVNode)("",!0),e.showConfirmButton?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:2,class:(0,x.normalizeClass)(["u-modal__button-group__wrapper u-modal__button-group__wrapper--confirm",[!e.showCancelButton&&e.showConfirmButton&&"u-modal__button-group__wrapper--only-confirm"]]),hoverStayTime:150,hoverClass:"u-modal__button-group__wrapper--hover",onClick:f[1]||(f[1]=(...c)=>r.confirmHandler&&r.confirmHandler(...c))},[m.loading?((0,x.openBlock)(),(0,x.createBlock)(p,{key:0})):((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:1,class:"u-modal__button-group__wrapper__text",style:(0,x.normalizeStyle)({color:e.confirmColor})},(0,x.toDisplayString)(e.confirmText),5))],2)):(0,x.createCommentVNode)("",!0)],4)],64))],4)]),_:3},8,["zoom","show","class","customStyle","closeOnClickOverlay","onClick"])}var Oo=Se(Po,[["render",jo],["styles",[ko]]]),Ro=Ce({props:{icon:{type:String,default:()=>X.empty.icon},text:{type:String,default:()=>X.empty.text},textColor:{type:String,default:()=>X.empty.textColor},textSize:{type:[String,Number],default:()=>X.empty.textSize},iconColor:{type:String,default:()=>X.empty.iconColor},iconSize:{type:[String,Number],default:()=>X.empty.iconSize},mode:{type:String,default:()=>X.empty.mode},width:{type:[String,Number],default:()=>X.empty.width},height:{type:[String,Number],default:()=>X.empty.height},show:{type:Boolean,default:()=>X.empty.show},marginTop:{type:[String,Number],default:()=>X.empty.marginTop}}}),Mo={"u-empty":{"":{flexDirection:"column",justifyContent:"center",alignItems:"center"}},"u-empty__text":{"":{flexDirection:"row",justifyContent:"center",alignItems:"center",marginTop:"20rpx"}},"u-slot-wrap":{"":{flexDirection:"row",justifyContent:"center",alignItems:"center",marginTop:"20rpx"}}},No={name:"u-empty",mixins:[Be,Te,Ro],data(){return{icons:{car:"\u8D2D\u7269\u8F66\u4E3A\u7A7A",page:"\u9875\u9762\u4E0D\u5B58\u5728",search:"\u6CA1\u6709\u641C\u7D22\u7ED3\u679C",address:"\u6CA1\u6709\u6536\u8D27\u5730\u5740",wifi:"\u6CA1\u6709WiFi",order:"\u8BA2\u5355\u4E3A\u7A7A",coupon:"\u6CA1\u6709\u4F18\u60E0\u5238",favor:"\u6682\u65E0\u6536\u85CF",permission:"\u65E0\u6743\u9650",history:"\u65E0\u5386\u53F2\u8BB0\u5F55",news:"\u65E0\u65B0\u95FB\u5217\u8868",message:"\u6D88\u606F\u5217\u8868\u4E3A\u7A7A",list:"\u5217\u8868\u4E3A\u7A7A",data:"\u6570\u636E\u4E3A\u7A7A",comment:"\u6682\u65E0\u8BC4\u8BBA"}}},computed:{emptyStyle(){let e={};return e.marginTop=ve(this.marginTop),xe(Pe(this.customStyle),e)},textStyle(){let e={};return e.color=this.textColor,e.fontSize=ve(this.textSize),e},isSrc(){return this.icon.indexOf("/")>=0}},methods:{addUnit:ve}};function Lo(e,f,t,a,m,r){let i=_e((0,x.resolveDynamicComponent)("u-icon"),Ve);return e.show?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:"u-empty",style:(0,x.normalizeStyle)([r.emptyStyle]),renderWhole:!0},[r.isSrc?((0,x.openBlock)(),(0,x.createElementBlock)("u-image",{key:1,style:(0,x.normalizeStyle)({width:r.addUnit(e.width),height:r.addUnit(e.height)}),src:e.icon,mode:"widthFix"},null,12,["src"])):((0,x.openBlock)(),(0,x.createBlock)(i,{key:0,name:e.mode==="message"?"chat":`empty-${e.mode}`,size:e.iconSize,color:e.iconColor,"margin-top":"14"},null,8,["name","size","color"])),(0,x.createElementVNode)("u-text",{class:"u-empty__text",style:(0,x.normalizeStyle)([r.textStyle])},(0,x.toDisplayString)(e.text?e.text:m.icons[e.mode]),5),e.$slots.default||e.$slots.$default?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:2,class:"u-empty__wrap"},[(0,x.renderSlot)(e.$slots,"default")])):(0,x.createCommentVNode)("",!0)],4)):(0,x.createCommentVNode)("",!0)}var Fo=Se(No,[["render",Lo],["styles",[Mo]]]),Uo=Ce({props:{status:{type:String,default:()=>X.loadmore.status},bgColor:{type:String,default:()=>X.loadmore.bgColor},icon:{type:Boolean,default:()=>X.loadmore.icon},fontSize:{type:[String,Number],default:()=>X.loadmore.fontSize},iconSize:{type:[String,Number],default:()=>X.loadmore.iconSize},color:{type:String,default:()=>X.loadmore.color},loadingIcon:{type:String,default:()=>X.loadmore.loadingIcon},loadmoreText:{type:String,default:()=>X.loadmore.loadmoreText},loadingText:{type:String,default:()=>X.loadmore.loadingText},nomoreText:{type:String,default:()=>X.loadmore.nomoreText},isDot:{type:Boolean,default:()=>X.loadmore.isDot},iconColor:{type:String,default:()=>X.loadmore.iconColor},marginTop:{type:[String,Number],default:()=>X.loadmore.marginTop},marginBottom:{type:[String,Number],default:()=>X.loadmore.marginBottom},height:{type:[String,Number],default:()=>X.loadmore.height},line:{type:Boolean,default:()=>X.loadmore.line},lineColor:{type:String,default:()=>X.loadmore.lineColor},dashed:{type:Boolean,default:()=>X.loadmore.dashed}}}),zo={"u-loadmore":{"":{flexDirection:"row",alignItems:"center",justifyContent:"center",flex:1}},"u-loadmore__content":{"":{marginTop:0,marginRight:15,marginBottom:0,marginLeft:15,flexDirection:"row",alignItems:"center",justifyContent:"center"}},"u-loadmore__content__icon-wrap":{"":{marginRight:8}},"u-loadmore__content__text":{"":{fontSize:14,color:"#606266"}},"u-loadmore__content__dot-text":{"":{fontSize:15,color:"#909193"}}},Do={name:"u-loadmore",mixins:[Be,Te,Uo],data(){return{dotText:"\u25CF"}},computed:{loadTextStyle(){return{color:this.color,fontSize:ve(this.fontSize),lineHeight:ve(this.fontSize),backgroundColor:this.bgColor}},showText(){let e="";return this.status=="loadmore"?e=this.loadmoreText:this.status=="loading"?e=this.loadingText:this.status=="nomore"&&this.isDot?e=this.dotText:e=this.nomoreText,e}},emits:["loadmore"],methods:{addStyle:Pe,addUnit:ve,loadMore(){this.status=="loadmore"&&this.$emit("loadmore")}}};function Qo(e,f,t,a,m,r){let i=_e((0,x.resolveDynamicComponent)("u-line"),Nt),p=_e((0,x.resolveDynamicComponent)("u-loading-icon"),Lt);return(0,x.openBlock)(),(0,x.createElementBlock)("view",{class:"u-loadmore",style:(0,x.normalizeStyle)([r.addStyle(e.customStyle),{backgroundColor:e.bgColor,marginBottom:r.addUnit(e.marginBottom),marginTop:r.addUnit(e.marginTop),height:r.addUnit(e.height)}]),renderWhole:!0},[e.line?((0,x.openBlock)(),(0,x.createBlock)(i,{key:0,length:"140rpx",color:e.lineColor,hairline:!1,dashed:e.dashed},null,8,["color","dashed"])):(0,x.createCommentVNode)("",!0),(0,x.createElementVNode)("view",{class:(0,x.normalizeClass)([e.status=="loadmore"||e.status=="nomore"?"u-more":"","u-loadmore__content"])},[e.status==="loading"&&e.icon?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:"u-loadmore__content__icon-wrap"},[(0,x.createVNode)(p,{color:e.iconColor,size:e.iconSize,mode:e.loadingIcon},null,8,["color","size","mode"])])):(0,x.createCommentVNode)("",!0),(0,x.createElementVNode)("u-text",{class:(0,x.normalizeClass)(["u-line-1",[e.status=="nomore"&&e.isDot==!0?"u-loadmore__content__dot-text":"u-loadmore__content__text"]]),style:(0,x.normalizeStyle)([r.loadTextStyle]),onClick:f[0]||(f[0]=(...h)=>r.loadMore&&r.loadMore(...h))},(0,x.toDisplayString)(r.showText),7)],2),e.line?((0,x.openBlock)(),(0,x.createBlock)(i,{key:1,length:"140rpx",color:e.lineColor,hairline:!1,dashed:e.dashed},null,8,["color","dashed"])):(0,x.createCommentVNode)("",!0)],4)}var Ho=Se(Do,[["render",Qo],["styles",[zo]]]),Ee=typeof globalThis!="undefined"?globalThis:typeof window!="undefined"?window:typeof global!="undefined"?global:typeof self!="undefined"?self:{};function Wo(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function Ke(e){throw new Error('Could not dynamically require "'+e+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var Ut={exports:{}};(function(e,f){(function(t){e.exports=t()})(function(){return function(){function t(a,m,r){function i(c,o){if(!m[c]){if(!a[c]){var n=typeof Ke=="function"&&Ke;if(!o&&n)return n(c,!0);if(p)return p(c,!0);var d=new Error("Cannot find module '"+c+"'");throw d.code="MODULE_NOT_FOUND",d}var g=m[c]={exports:{}};a[c][0].call(g.exports,function(E){var u=a[c][1][E];return i(u||E)},g,g.exports,t,a,m,r)}return m[c].exports}for(var p=typeof Ke=="function"&&Ke,h=0;h<r.length;h++)i(r[h]);return i}return t}()({1:[function(t,a,m){(function(r,i){var p=t("events").EventEmitter,h=t("./store"),c=t("mqtt-packet"),o=t("readable-stream").Writable,n=t("inherits"),d=t("reinterval"),g=t("./validations"),E=t("xtend"),u=t("debug")("mqttjs:client"),_=i.setImmediate||function(A){r.nextTick(A)},C={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:30*1e3,clean:!0,resubscribe:!0},y=["ECONNREFUSED","EADDRINUSE","ECONNRESET","ENOTFOUND"],P={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};function M(){return"mqttjs_"+Math.random().toString(16).substr(2,8)}function k(A,j,b){u("sendPacket :: packet: %O",j),u("sendPacket :: emitting `packetsend`"),A.emit("packetsend",j),u("sendPacket :: writing to stream");var N=c.writeToStream(j,A.stream,A.options);u("sendPacket :: writeToStream result %s",N),!N&&b?(u("sendPacket :: handle events on `drain` once through callback."),A.stream.once("drain",b)):b&&(u("sendPacket :: invoking cb"),b())}function O(A){A&&(u("flush: queue exists? %b",!!A),Object.keys(A).forEach(function(j){typeof A[j].cb=="function"&&(A[j].cb(new Error("Connection closed")),delete A[j])}))}function U(A){A&&(u("flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(A).forEach(function(j){A[j].volatile&&typeof A[j].cb=="function"&&(A[j].cb(new Error("Connection closed")),delete A[j])}))}function Y(A,j,b,N){u("storeAndSend :: store packet with cmd %s to outgoingStore",j.cmd),A.outgoingStore.put(j,function(q){if(q)return b&&b(q);N(),k(A,j,b)})}function Z(A){u("nop ::",A)}function ee(A,j){var b,N=this;if(!(this instanceof ee))return new ee(A,j);this.options=j||{};for(b in C)typeof this.options[b]=="undefined"?this.options[b]=C[b]:this.options[b]=j[b];u("MqttClient :: options.protocol",j.protocol),u("MqttClient :: options.protocolVersion",j.protocolVersion),u("MqttClient :: options.username",j.username),u("MqttClient :: options.keepalive",j.keepalive),u("MqttClient :: options.reconnectPeriod",j.reconnectPeriod),u("MqttClient :: options.rejectUnauthorized",j.rejectUnauthorized),this.options.clientId=typeof j.clientId=="string"?j.clientId:M(),u("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=j.protocolVersion===5&&j.customHandleAcks?j.customHandleAcks:function(){arguments[3](0)},this.streamBuilder=A,this.outgoingStore=j.outgoingStore||new h,this.incomingStore=j.incomingStore||new h,this.queueQoSZero=j.queueQoSZero===void 0?!0:j.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.pingTimer=null,this.connected=!1,this.disconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this.nextId=Math.max(1,Math.floor(Math.random()*65535)),this.outgoing={},this._firstConnection=!0,this.on("connect",function(){var z=this.queue;function q(){var G=z.shift();u("deliver :: entry %o",G);var D=null;G&&(D=G.packet,u("deliver :: call _sendPacket for %o",D),N._sendPacket(D,function(te){G.cb&&G.cb(te),q()}))}u("connect :: sending queued packets"),q()}),this.on("close",function(){u("close :: connected set to `false`"),this.connected=!1,u("close :: clearing connackTimer"),clearTimeout(this.connackTimer),u("close :: clearing ping timer"),N.pingTimer!==null&&(N.pingTimer.clear(),N.pingTimer=null),u("close :: calling _setupReconnect"),this._setupReconnect()}),p.call(this),u("MqttClient :: setting up stream"),this._setupStream()}n(ee,p),ee.prototype._setupStream=function(){var A,j=this,b=new o,N=c.parser(this.options),z=null,q=[];u("_setupStream :: calling method to clear reconnect"),this._clearReconnect(),u("_setupStream :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),N.on("packet",function(ce){u("parser :: on packet push to packets array."),q.push(ce)});function G(){if(q.length)r.nextTick(D);else{var ce=z;z=null,ce()}}function D(){u("work :: getting next packet in queue");var ce=q.shift();if(ce)u("work :: packet pulled from queue"),j._handlePacket(ce,G);else{u("work :: no packets in queue");var pe=z;z=null,u("work :: done flag is %s",!!pe),pe&&pe()}}b._write=function(ce,pe,Q){z=Q,u("writable stream :: parsing buffer"),N.parse(ce),D()};function te(ce){u("streamErrorHandler :: error",ce.message),y.includes(ce.code)?(u("streamErrorHandler :: emitting error"),j.emit("error",ce)):Z(ce)}if(u("_setupStream :: pipe stream to writable stream"),this.stream.pipe(b),this.stream.on("error",te),this.stream.on("close",function(){u("(%s)stream :: on close",j.options.clientId),U(j.outgoing),u("stream: emit close to MqttClient"),j.emit("close")}),u("_setupStream: sending packet `connect`"),A=Object.create(this.options),A.cmd="connect",k(this,A),N.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return j.end(()=>this.emit("error",new Error("Packet has no Authentication Method"))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&typeof this.options.authPacket=="object"){var le=E({cmd:"auth",reasonCode:0},this.options.authPacket);k(this,le)}}this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout(function(){u("!!connectTimeout hit!! Calling _cleanUp with force `true`"),j._cleanUp(!0)},this.options.connectTimeout)},ee.prototype._handlePacket=function(A,j){var b=this.options;if(b.protocolVersion===5&&b.properties&&b.properties.maximumPacketSize&&b.properties.maximumPacketSize<A.length)return this.emit("error",new Error("exceeding packets size "+A.cmd)),this.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),this;switch(u("_handlePacket :: emitting packetreceive"),this.emit("packetreceive",A),A.cmd){case"publish":this._handlePublish(A,j);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":this._handleAck(A),j();break;case"pubrel":this._handlePubrel(A,j);break;case"connack":this._handleConnack(A),j();break;case"pingresp":this._handlePingresp(A),j();break;case"disconnect":this._handleDisconnect(A),j();break}},ee.prototype._checkDisconnecting=function(A){return this.disconnecting&&(A?A(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting},ee.prototype.publish=function(A,j,b,N){u("publish :: message `%s` to topic `%s`",j,A);var z,q=this.options;typeof b=="function"&&(N=b,b=null);var G={qos:0,retain:!1,dup:!1};if(b=E(G,b),this._checkDisconnecting(N))return this;switch(z={cmd:"publish",topic:A,payload:j,qos:b.qos,retain:b.retain,messageId:this._nextId(),dup:b.dup},q.protocolVersion===5&&(z.properties=b.properties,(!q.properties&&z.properties&&z.properties.topicAlias||b.properties&&q.properties&&(b.properties.topicAlias&&q.properties.topicAliasMaximum&&b.properties.topicAlias>q.properties.topicAliasMaximum||!q.properties.topicAliasMaximum&&b.properties.topicAlias))&&delete z.properties.topicAlias),u("publish :: qos",b.qos),b.qos){case 1:case 2:this.outgoing[z.messageId]={volatile:!1,cb:N||Z},this._storeProcessing?(u("_storeProcessing enabled"),this._packetIdsDuringStoreProcessing[z.messageId]=!1,this._storePacket(z,void 0,b.cbStorePut)):(u("MqttClient:publish: packet cmd: %s",z.cmd),this._sendPacket(z,void 0,b.cbStorePut));break;default:this._storeProcessing?(u("_storeProcessing enabled"),this._storePacket(z,N,b.cbStorePut)):(u("MqttClient:publish: packet cmd: %s",z.cmd),this._sendPacket(z,N,b.cbStorePut));break}return this},ee.prototype.subscribe=function(){for(var A,j=new Array(arguments.length),b=0;b<arguments.length;b++)j[b]=arguments[b];var N=[],z=j.shift(),q=z.resubscribe,G=j.pop()||Z,D=j.pop(),te,le=this,ce=this.options.protocolVersion;if(delete z.resubscribe,typeof z=="string"&&(z=[z]),typeof G!="function"&&(D=G,G=Z),te=g.validateTopics(z),te!==null)return _(G,new Error("Invalid topic "+te)),this;if(this._checkDisconnecting(G))return u("subscribe: discconecting true"),this;var pe={qos:0};if(ce===5&&(pe.nl=!1,pe.rap=!1,pe.rh=0),D=E(pe,D),Array.isArray(z)?z.forEach(function(L){if(u("subscribe: array topic %s",L),!le._resubscribeTopics.hasOwnProperty(L)||le._resubscribeTopics[L].qos<D.qos||q){var I={topic:L,qos:D.qos};ce===5&&(I.nl=D.nl,I.rap=D.rap,I.rh=D.rh,I.properties=D.properties),u("subscribe: pushing topic `%s` and qos `%s` to subs list",I.topic,I.qos),N.push(I)}}):Object.keys(z).forEach(function(L){if(u("subscribe: object topic %s",L),!le._resubscribeTopics.hasOwnProperty(L)||le._resubscribeTopics[L].qos<z[L].qos||q){var I={topic:L,qos:z[L].qos};ce===5&&(I.nl=z[L].nl,I.rap=z[L].rap,I.rh=z[L].rh,I.properties=D.properties),u("subscribe: pushing `%s` to subs list",I),N.push(I)}}),A={cmd:"subscribe",subscriptions:N,qos:1,retain:!1,dup:!1,messageId:this._nextId()},D.properties&&(A.properties=D.properties),!N.length){G(null,[]);return}if(this.options.resubscribe){u("subscribe :: resubscribe true");var Q=[];N.forEach(function(L){if(le.options.reconnectPeriod>0){var I={qos:L.qos};ce===5&&(I.nl=L.nl||!1,I.rap=L.rap||!1,I.rh=L.rh||0,I.properties=L.properties),le._resubscribeTopics[L.topic]=I,Q.push(L.topic)}}),le.messageIdToTopic[A.messageId]=Q}return this.outgoing[A.messageId]={volatile:!0,cb:function(L,I){if(!L)for(var R=I.granted,K=0;K<R.length;K+=1)N[K].qos=R[K];G(L,N)}},u("subscribe :: call _sendPacket"),this._sendPacket(A),this},ee.prototype.unsubscribe=function(){for(var A={cmd:"unsubscribe",qos:1,messageId:this._nextId()},j=this,b=new Array(arguments.length),N=0;N<arguments.length;N++)b[N]=arguments[N];var z=b.shift(),q=b.pop()||Z,G=b.pop();return typeof z=="string"&&(z=[z]),typeof q!="function"&&(G=q,q=Z),this._checkDisconnecting(q)?this:(typeof z=="string"?A.unsubscriptions=[z]:Array.isArray(z)&&(A.unsubscriptions=z),this.options.resubscribe&&A.unsubscriptions.forEach(function(D){delete j._resubscribeTopics[D]}),typeof G=="object"&&G.properties&&(A.properties=G.properties),this.outgoing[A.messageId]={volatile:!0,cb:q},u("unsubscribe: call _sendPacket"),this._sendPacket(A),this)},ee.prototype.end=function(A,j,b){var N=this;u("end :: (%s)",this.options.clientId),(A==null||typeof A!="boolean")&&(b=j||Z,j=A,A=!1,typeof j!="object"&&(b=j,j=null,typeof b!="function"&&(b=Z))),typeof j!="object"&&(b=j,j=null),u("end :: cb? %s",!!b),b=b||Z;function z(){u("end :: closeStores: closing incoming and outgoing stores"),N.disconnected=!0,N.incomingStore.close(function(){N.outgoingStore.close(function(){u("end :: closeStores: emitting end"),N.emit("end"),b&&(u("end :: closeStores: invoking callback with args"),b())})}),N._deferredReconnect&&N._deferredReconnect()}function q(){u("end :: (%s) :: finish :: calling _cleanUp with force %s",N.options.clientId,A),N._cleanUp(A,()=>{u("end :: finish :: calling process.nextTick on closeStores"),r.nextTick(z.bind(N))},j)}return this.disconnecting?(b(),this):(this._clearReconnect(),this.disconnecting=!0,!A&&Object.keys(this.outgoing).length>0?(u("end :: (%s) :: calling finish in 10ms once outgoing is empty",N.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,q,10))):(u("end :: (%s) :: immediately calling finish",N.options.clientId),q()),this)},ee.prototype.removeOutgoingMessage=function(A){var j=this.outgoing[A]?this.outgoing[A].cb:null;return delete this.outgoing[A],this.outgoingStore.del({messageId:A},function(){j(new Error("Message removed"))}),this},ee.prototype.reconnect=function(A){u("client reconnect");var j=this,b=function(){A?(j.options.incomingStore=A.incomingStore,j.options.outgoingStore=A.outgoingStore):(j.options.incomingStore=null,j.options.outgoingStore=null),j.incomingStore=j.options.incomingStore||new h,j.outgoingStore=j.options.outgoingStore||new h,j.disconnecting=!1,j.disconnected=!1,j._deferredReconnect=null,j._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=b:b(),this},ee.prototype._reconnect=function(){u("_reconnect: emitting reconnect to client"),this.emit("reconnect"),u("_reconnect: calling _setupStream"),this._setupStream()},ee.prototype._setupReconnect=function(){var A=this;!A.disconnecting&&!A.reconnectTimer&&A.options.reconnectPeriod>0?(this.reconnecting||(u("_setupReconnect :: emit `offline` state"),this.emit("offline"),u("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),u("_setupReconnect :: setting reconnectTimer for %d ms",A.options.reconnectPeriod),A.reconnectTimer=setInterval(function(){u("reconnectTimer :: reconnect triggered!"),A._reconnect()},A.options.reconnectPeriod)):u("_setupReconnect :: doing nothing...")},ee.prototype._clearReconnect=function(){u("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)},ee.prototype._cleanUp=function(A,j){var b=arguments[2];if(j&&(u("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",j)),u("_cleanUp :: forced? %s",A),A)this.options.reconnectPeriod===0&&this.options.clean&&O(this.outgoing),u("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{var N=E({cmd:"disconnect"},b);u("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(N,_.bind(null,this.stream.end.bind(this.stream)))}this.disconnecting||(u("_cleanUp :: client not disconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),this.pingTimer!==null&&(u("_cleanUp :: clearing pingTimer"),this.pingTimer.clear(),this.pingTimer=null),j&&!this.connected&&(u("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",j),j())},ee.prototype._sendPacket=function(A,j,b){if(u("_sendPacket :: (%s) ::  start",this.options.clientId),b=b||Z,!this.connected){u("_sendPacket :: client not connected. Storing packet offline."),this._storePacket(A,j,b);return}switch(this._shiftPingInterval(),A.cmd){case"publish":break;case"pubrel":Y(this,A,j,b);return;default:k(this,A,j);return}switch(A.qos){case 2:case 1:Y(this,A,j,b);break;case 0:default:k(this,A,j);break}u("_sendPacket :: (%s) ::  end",this.options.clientId)},ee.prototype._storePacket=function(A,j,b){u("_storePacket :: packet: %o",A),u("_storePacket :: cb? %s",!!j),b=b||Z,(A.qos||0)===0&&this.queueQoSZero||A.cmd!=="publish"?this.queue.push({packet:A,cb:j}):A.qos>0?(j=this.outgoing[A.messageId]?this.outgoing[A.messageId].cb:null,this.outgoingStore.put(A,function(N){if(N)return j&&j(N);b()})):j&&j(new Error("No connection to broker"))},ee.prototype._setupPingTimer=function(){u("_setupPingTimer :: keepalive %d (seconds)",this.options.keepalive);var A=this;!this.pingTimer&&this.options.keepalive&&(this.pingResp=!0,this.pingTimer=d(function(){A._checkPing()},this.options.keepalive*1e3))},ee.prototype._shiftPingInterval=function(){this.pingTimer&&this.options.keepalive&&this.options.reschedulePings&&this.pingTimer.reschedule(this.options.keepalive*1e3)},ee.prototype._checkPing=function(){u("_checkPing :: checking ping..."),this.pingResp?(u("_checkPing :: ping response received. Clearing flag and sending `pingreq`"),this.pingResp=!1,this._sendPacket({cmd:"pingreq"})):(u("_checkPing :: calling _cleanUp with force true"),this._cleanUp(!0))},ee.prototype._handlePingresp=function(){this.pingResp=!0},ee.prototype._handleConnack=function(A){u("_handleConnack");var j=this.options,b=j.protocolVersion,N=b===5?A.reasonCode:A.returnCode;if(clearTimeout(this.connackTimer),A.properties&&(A.properties.topicAliasMaximum&&(j.properties||(j.properties={}),j.properties.topicAliasMaximum=A.properties.topicAliasMaximum),A.properties.serverKeepAlive&&j.keepalive&&(j.keepalive=A.properties.serverKeepAlive,this._shiftPingInterval()),A.properties.maximumPacketSize&&(j.properties||(j.properties={}),j.properties.maximumPacketSize=A.properties.maximumPacketSize)),N===0)this.reconnecting=!1,this._onConnect(A);else if(N>0){var z=new Error("Connection refused: "+P[N]);z.code=N,this.emit("error",z)}},ee.prototype._handlePublish=function(A,j){u("_handlePublish: packet %o",A),j=typeof j!="undefined"?j:Z;var b=A.topic.toString(),N=A.payload,z=A.qos,q=A.messageId,G=this,D=this.options,te=[0,16,128,131,135,144,145,151,153];switch(u("_handlePublish: qos %d",z),z){case 2:{D.customHandleAcks(b,N,A,function(le,ce){if(le instanceof Error||(ce=le,le=null),le)return G.emit("error",le);if(te.indexOf(ce)===-1)return G.emit("error",new Error("Wrong reason code for pubrec"));ce?G._sendPacket({cmd:"pubrec",messageId:q,reasonCode:ce},j):G.incomingStore.put(A,function(){G._sendPacket({cmd:"pubrec",messageId:q},j)})});break}case 1:{D.customHandleAcks(b,N,A,function(le,ce){if(le instanceof Error||(ce=le,le=null),le)return G.emit("error",le);if(te.indexOf(ce)===-1)return G.emit("error",new Error("Wrong reason code for puback"));ce||G.emit("message",b,N,A),G.handleMessage(A,function(pe){if(pe)return j&&j(pe);G._sendPacket({cmd:"puback",messageId:q,reasonCode:ce},j)})});break}case 0:this.emit("message",b,N,A),this.handleMessage(A,j);break;default:u("_handlePublish: unknown QoS. Doing nothing.");break}},ee.prototype.handleMessage=function(A,j){j()},ee.prototype._handleAck=function(A){var j=A.messageId,b=A.cmd,N=null,z=this.outgoing[j]?this.outgoing[j].cb:null,q=this,G;if(!z){u("_handleAck :: Server sent an ack in error. Ignoring.");return}switch(u("_handleAck :: packet type",b),b){case"pubcomp":case"puback":var D=A.reasonCode;D&&D>0&&D!==16&&(G=new Error("Publish error: "+P[D]),G.code=D,z(G,A)),delete this.outgoing[j],this.outgoingStore.del(A,z);break;case"pubrec":N={cmd:"pubrel",qos:2,messageId:j};var te=A.reasonCode;te&&te>0&&te!==16?(G=new Error("Publish error: "+P[te]),G.code=te,z(G,A)):this._sendPacket(N);break;case"suback":delete this.outgoing[j];for(var le=0;le<A.granted.length;le++)if(A.granted[le]&128){var ce=this.messageIdToTopic[j];ce&&ce.forEach(function(pe){delete q._resubscribeTopics[pe]})}z(null,A);break;case"unsuback":delete this.outgoing[j],z(null);break;default:q.emit("error",new Error("unrecognized packet type"))}this.disconnecting&&Object.keys(this.outgoing).length===0&&this.emit("outgoingEmpty")},ee.prototype._handlePubrel=function(A,j){u("handling pubrel packet"),j=typeof j!="undefined"?j:Z;var b=A.messageId,N=this,z={cmd:"pubcomp",messageId:b};N.incomingStore.get(A,function(q,G){q?N._sendPacket(z,j):(N.emit("message",G.topic,G.payload,G),N.handleMessage(G,function(D){if(D)return j(D);N.incomingStore.del(G,Z),N._sendPacket(z,j)}))})},ee.prototype._handleDisconnect=function(A){this.emit("disconnect",A)},ee.prototype._nextId=function(){var A=this.nextId++;return this.nextId===65536&&(this.nextId=1),A},ee.prototype.getLastMessageId=function(){return this.nextId===1?65535:this.nextId-1},ee.prototype._resubscribe=function(A){u("_resubscribe");var j=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||this.options.protocolVersion===5&&!A.sessionPresent)&&j.length>0)if(this.options.resubscribe)if(this.options.protocolVersion===5){u("_resubscribe: protocolVersion 5");for(var b=0;b<j.length;b++){var N={};N[j[b]]=this._resubscribeTopics[j[b]],N.resubscribe=!0,this.subscribe(N,{properties:N[j[b]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1},ee.prototype._onConnect=function(A){if(this.disconnected){this.emit("connect",A);return}var j=this;this._setupPingTimer(),this._resubscribe(A),this.connected=!0;function b(){var N=j.outgoingStore.createStream();function z(){j._storeProcessing=!1,j._packetIdsDuringStoreProcessing={}}j.once("close",q),N.on("error",function(D){z(),j.removeListener("close",q),j.emit("error",D)});function q(){N.destroy(),N=null,z()}function G(){if(N){j._storeProcessing=!0;var D=N.read(1),te;if(!D){N.once("readable",G);return}if(j._packetIdsDuringStoreProcessing[D.messageId]){G();return}!j.disconnecting&&!j.reconnectTimer?(te=j.outgoing[D.messageId]?j.outgoing[D.messageId].cb:null,j.outgoing[D.messageId]={volatile:!1,cb:function(le,ce){te&&te(le,ce),G()}},j._packetIdsDuringStoreProcessing[D.messageId]=!0,j._sendPacket(D)):N.destroy&&N.destroy()}}N.on("end",function(){var D=!0;for(var te in j._packetIdsDuringStoreProcessing)if(!j._packetIdsDuringStoreProcessing[te]){D=!1;break}D?(z(),j.removeListener("close",q),j.emit("connect",A)):b()}),G()}b()},a.exports=ee}).call(this,t("_process"),typeof Ee!="undefined"?Ee:typeof self!="undefined"?self:typeof window!="undefined"?window:{})},{"./store":7,"./validations":8,_process:100,debug:17,events:83,inherits:88,"mqtt-packet":92,"readable-stream":116,reinterval:117,xtend:140}],2:[function(t,a,m){(function(r){var i=t("readable-stream").Transform,p=t("duplexify"),h=t("base64-js"),c,o,n,d=!1;function g(){var y=new i;return y._write=function(P,M,k){c.sendSocketMessage({data:P.buffer,success:function(){k()},fail:function(){k(new Error)}})},y._flush=function(M){c.closeSocket({success:function(){M()}})},y}function E(y){y.hostname||(y.hostname="localhost"),y.path||(y.path="/"),y.wsOptions||(y.wsOptions={})}function u(y,P){var M=y.protocol==="alis"?"wss":"ws",k=M+"://"+y.hostname+y.path;return y.port&&y.port!==80&&y.port!==443&&(k=M+"://"+y.hostname+":"+y.port+y.path),typeof y.transformWsUrl=="function"&&(k=y.transformWsUrl(k,y,P)),k}function _(){d||(d=!0,c.onSocketOpen(function(){n.setReadable(o),n.setWritable(o),n.emit("connect")}),c.onSocketMessage(function(y){if(typeof y.data=="string"){var P=h.toByteArray(y.data),M=r.from(P);o.push(M)}else{var k=new FileReader;k.addEventListener("load",function(){var O=k.result;O instanceof ArrayBuffer?O=r.from(O):O=r.from(O,"utf8"),o.push(O)}),k.readAsArrayBuffer(y.data)}}),c.onSocketClose(function(){n.end(),n.destroy()}),c.onSocketError(function(y){n.destroy(y)}))}function C(y,P){if(P.hostname=P.hostname||P.host,!P.hostname)throw new Error("Could not determine host. Specify host manually.");var M=P.protocolId==="MQIsdp"&&P.protocolVersion===3?"mqttv3.1":"mqtt";E(P);var k=u(P,y);return c=P.my,c.connectSocket({url:k,protocols:M}),o=g(),n=p.obj(),_(),n}a.exports=C}).call(this,t("buffer").Buffer)},{"base64-js":10,buffer:12,duplexify:19,"readable-stream":116}],3:[function(t,a,m){var r=t("net"),i=t("debug")("mqttjs:tcp");function p(h,c){var o,n;return c.port=c.port||1883,c.hostname=c.hostname||c.host||"localhost",o=c.port,n=c.hostname,i("port %d and host %s",o,n),r.createConnection(o,n)}a.exports=p},{debug:17,net:11}],4:[function(t,a,m){var r=t("tls"),i=t("debug")("mqttjs:tls");function p(h,c){var o;c.port=c.port||8883,c.host=c.hostname||c.host||"localhost",c.servername=c.host,c.rejectUnauthorized=c.rejectUnauthorized!==!1,delete c.path,i("port %d host %s rejectUnauthorized %b",c.port,c.host,c.rejectUnauthorized),o=r.connect(c),o.on("secureConnect",function(){c.rejectUnauthorized&&!o.authorized?o.emit("error",new Error("TLS not authorized")):o.removeListener("error",n)});function n(d){c.rejectUnauthorized&&h.emit("error",d),o.end()}return o.on("error",n),o}a.exports=p},{debug:17,tls:11}],5:[function(t,a,m){(function(r){var i=t("debug")("mqttjs:ws"),p=t("websocket-stream"),h=t("url"),c=["rejectUnauthorized","ca","cert","key","pfx","passphrase"],o=r.title==="browser";function n(_,C){var y=_.protocol+"://"+_.hostname+":"+_.port+_.path;return typeof _.transformWsUrl=="function"&&(y=_.transformWsUrl(y,_,C)),y}function d(_){_.hostname||(_.hostname="localhost"),_.port||(_.protocol==="wss"?_.port=443:_.port=80),_.path||(_.path="/"),_.wsOptions||(_.wsOptions={}),!o&&_.protocol==="wss"&&c.forEach(function(C){_.hasOwnProperty(C)&&!_.wsOptions.hasOwnProperty(C)&&(_.wsOptions[C]=_[C])})}function g(_,C){i("createWebSocket");var y=C.protocolId==="MQIsdp"&&C.protocolVersion===3?"mqttv3.1":"mqtt";d(C);var P=n(C,_);return i("url %s protocol %s",P,y),p(P,[y],C.wsOptions)}function E(_,C){return g(_,C)}function u(_,C){if(i("browserStreamBuilder"),C.hostname||(C.hostname=C.host),!C.hostname){if(typeof document=="undefined")throw new Error("Could not determine host. Specify host manually.");var y=h.parse(document.URL);C.hostname=y.hostname,C.port||(C.port=y.port)}return g(_,C)}o?a.exports=u:a.exports=E}).call(this,t("_process"))},{_process:100,debug:17,url:132,"websocket-stream":137}],6:[function(t,a,m){(function(r,i){var p=t("readable-stream").Transform,h=t("duplexify"),c,o,n;function d(){var C=new p;return C._write=function(y,P,M){c.send({data:y.buffer,success:function(){M()},fail:function(k){M(new Error(k))}})},C._flush=function(P){c.close({success:function(){P()}})},C}function g(C){C.hostname||(C.hostname="localhost"),C.path||(C.path="/"),C.wsOptions||(C.wsOptions={})}function E(C,y){var P=C.protocol==="wxs"?"wss":"ws",M=P+"://"+C.hostname+C.path;return C.port&&C.port!==80&&C.port!==443&&(M=P+"://"+C.hostname+":"+C.port+C.path),typeof C.transformWsUrl=="function"&&(M=C.transformWsUrl(M,C,y)),M}function u(){c.onOpen(function(){n.setReadable(o),n.setWritable(o),n.emit("connect")}),c.onMessage(function(C){var y=C.data;y instanceof ArrayBuffer?y=i.from(y):y=i.from(y,"utf8"),o.push(y)}),c.onClose(function(){n.end(),n.destroy()}),c.onError(function(C){n.destroy(new Error(C.errMsg))})}function _(C,y){if(y.hostname=y.hostname||y.host,!y.hostname)throw new Error("Could not determine host. Specify host manually.");var P=y.protocolId==="MQIsdp"&&y.protocolVersion===3?"mqttv3.1":"mqtt";g(y);var M=E(y,C);c=uni.connectSocket({url:M,protocols:[P],complete:()=>{}}),o=d(),n=h.obj(),n._destroy=function(O,U){c.close({success:function(){U&&U(O)}})};var k=n.destroy;return n.destroy=function(){n.destroy=k;var O=this;r.nextTick(function(){c.close({fail:function(){O._destroy(new Error)}})})}.bind(n),u(),n}a.exports=_}).call(this,t("_process"),t("buffer").Buffer)},{_process:100,buffer:12,duplexify:19,"readable-stream":116}],7:[function(t,a,m){(function(r){var i=t("xtend"),p=t("readable-stream").Readable,h={objectMode:!0},c={clean:!0},o=t("es6-map");function n(d){if(!(this instanceof n))return new n(d);this.options=d||{},this.options=i(c,d),this._inflights=new o}n.prototype.put=function(d,g){return this._inflights.set(d.messageId,d),g&&g(),this},n.prototype.createStream=function(){var d=new p(h),g=!1,E=[],u=0;return this._inflights.forEach(function(_,C){E.push(_)}),d._read=function(){!g&&u<E.length?this.push(E[u++]):this.push(null)},d.destroy=function(){if(!g){var _=this;g=!0,r.nextTick(function(){_.emit("close")})}},d},n.prototype.del=function(d,g){return d=this._inflights.get(d.messageId),d?(this._inflights.delete(d.messageId),g(null,d)):g&&g(new Error("missing packet")),this},n.prototype.get=function(d,g){return d=this._inflights.get(d.messageId),d?g(null,d):g&&g(new Error("missing packet")),this},n.prototype.close=function(d){this.options.clean&&(this._inflights=null),d&&d()},a.exports=n}).call(this,t("_process"))},{_process:100,"es6-map":68,"readable-stream":116,xtend:140}],8:[function(t,a,m){function r(p){for(var h=p.split("/"),c=0;c<h.length;c++)if(h[c]!=="+"){if(h[c]==="#")return c===h.length-1;if(h[c].indexOf("+")!==-1||h[c].indexOf("#")!==-1)return!1}return!0}function i(p){if(p.length===0)return"empty_topic_list";for(var h=0;h<p.length;h++)if(!r(p[h]))return p[h];return null}a.exports={validateTopics:i}},{}],9:[function(t,a,m){(function(r){var i=t("../client"),p=t("../store"),h=t("url"),c=t("xtend"),o=t("debug")("mqttjs"),n={};r.title!=="browser"?(n.mqtt=t("./tcp"),n.tcp=t("./tcp"),n.ssl=t("./tls"),n.tls=t("./tls"),n.mqtts=t("./tls")):(n.wx=t("./wx"),n.wxs=t("./wx"),n.ali=t("./ali"),n.alis=t("./ali")),n.ws=t("./ws"),n.wss=t("./ws");function d(E){var u;E.auth&&(u=E.auth.match(/^(.+):(.+)$/),u?(E.username=u[1],E.password=u[2]):E.username=E.auth)}function g(E,u){if(o("connecting to an MQTT broker..."),typeof E=="object"&&!u&&(u=E,E=null),u=u||{},E){var _=h.parse(E,!0);if(_.port!=null&&(_.port=Number(_.port)),u=c(_,u),u.protocol===null)throw new Error("Missing protocol");u.protocol=u.protocol.replace(/:$/,"")}if(d(u),u.query&&typeof u.query.clientId=="string"&&(u.clientId=u.query.clientId),u.cert&&u.key)if(u.protocol){if(["mqtts","wss","wxs","alis"].indexOf(u.protocol)===-1)switch(u.protocol){case"mqtt":u.protocol="mqtts";break;case"ws":u.protocol="wss";break;case"wx":u.protocol="wxs";break;case"ali":u.protocol="alis";break;default:throw new Error('Unknown protocol for secure connection: "'+u.protocol+'"!')}}else throw new Error("Missing secure protocol key");if(!n[u.protocol]){var C=["mqtts","wss"].indexOf(u.protocol)!==-1;u.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter(function(M,k){return C&&k%2===0?!1:typeof n[M]=="function"})[0]}if(u.clean===!1&&!u.clientId)throw new Error("Missing clientId for unclean clients");u.protocol&&(u.defaultProtocol=u.protocol);function y(M){return u.servers&&((!M._reconnectCount||M._reconnectCount===u.servers.length)&&(M._reconnectCount=0),u.host=u.servers[M._reconnectCount].host,u.port=u.servers[M._reconnectCount].port,u.protocol=u.servers[M._reconnectCount].protocol?u.servers[M._reconnectCount].protocol:u.defaultProtocol,u.hostname=u.host,M._reconnectCount++),o("calling streambuilder for",u.protocol),n[u.protocol](M,u)}var P=new i(y,u);return P.on("error",function(){}),P}a.exports=g,a.exports.connect=g,a.exports.MqttClient=i,a.exports.Store=p}).call(this,t("_process"))},{"../client":1,"../store":7,"./ali":2,"./tcp":3,"./tls":4,"./ws":5,"./wx":6,_process:100,debug:17,url:132,xtend:140}],10:[function(t,a,m){m.byteLength=d,m.toByteArray=E,m.fromByteArray=C;for(var r=[],i=[],p=typeof Uint8Array!="undefined"?Uint8Array:Array,h="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=0,o=h.length;c<o;++c)r[c]=h[c],i[h.charCodeAt(c)]=c;i[45]=62,i[95]=63;function n(y){var P=y.length;if(P%4>0)throw new Error("Invalid string. Length must be a multiple of 4");var M=y.indexOf("=");M===-1&&(M=P);var k=M===P?0:4-M%4;return[M,k]}function d(y){var P=n(y),M=P[0],k=P[1];return(M+k)*3/4-k}function g(y,P,M){return(P+M)*3/4-M}function E(y){for(var P,M=n(y),k=M[0],O=M[1],U=new p(g(y,k,O)),Y=0,Z=O>0?k-4:k,ee=0;ee<Z;ee+=4)P=i[y.charCodeAt(ee)]<<18|i[y.charCodeAt(ee+1)]<<12|i[y.charCodeAt(ee+2)]<<6|i[y.charCodeAt(ee+3)],U[Y++]=P>>16&255,U[Y++]=P>>8&255,U[Y++]=P&255;return O===2&&(P=i[y.charCodeAt(ee)]<<2|i[y.charCodeAt(ee+1)]>>4,U[Y++]=P&255),O===1&&(P=i[y.charCodeAt(ee)]<<10|i[y.charCodeAt(ee+1)]<<4|i[y.charCodeAt(ee+2)]>>2,U[Y++]=P>>8&255,U[Y++]=P&255),U}function u(y){return r[y>>18&63]+r[y>>12&63]+r[y>>6&63]+r[y&63]}function _(y,P,M){for(var k,O=[],U=P;U<M;U+=3)k=(y[U]<<16&16711680)+(y[U+1]<<8&65280)+(y[U+2]&255),O.push(u(k));return O.join("")}function C(y){for(var P,M=y.length,k=M%3,O=[],U=16383,Y=0,Z=M-k;Y<Z;Y+=U)O.push(_(y,Y,Y+U>Z?Z:Y+U));return k===1?(P=y[M-1],O.push(r[P>>2]+r[P<<4&63]+"==")):k===2&&(P=(y[M-2]<<8)+y[M-1],O.push(r[P>>10]+r[P>>4&63]+r[P<<2&63]+"=")),O.join("")}},{}],11:[function(t,a,m){},{}],12:[function(t,a,m){(function(n){var i=t("base64-js"),p=t("ieee754");m.Buffer=n,m.SlowBuffer=k,m.INSPECT_MAX_BYTES=50;var h=2147483647;m.kMaxLength=h,n.TYPED_ARRAY_SUPPORT=c(),!n.TYPED_ARRAY_SUPPORT&&typeof console!="undefined"&&typeof console.error=="function"&&fe("error","at node_modules/mqtt/dist/mqtt.js:2580","This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support.");function c(){try{var v=new Uint8Array(1);return v.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},v.foo()===42}catch(s){return!1}}Object.defineProperty(n.prototype,"parent",{enumerable:!0,get:function(){if(n.isBuffer(this))return this.buffer}}),Object.defineProperty(n.prototype,"offset",{enumerable:!0,get:function(){if(n.isBuffer(this))return this.byteOffset}});function o(v){if(v>h)throw new RangeError('The value "'+v+'" is invalid for option "size"');var s=new Uint8Array(v);return s.__proto__=n.prototype,s}function n(v,s,l){if(typeof v=="number"){if(typeof s=="string")throw new TypeError('The "string" argument must be of type string. Received type number');return u(v)}return d(v,s,l)}typeof Symbol!="undefined"&&Symbol.species!=null&&n[Symbol.species]===n&&Object.defineProperty(n,Symbol.species,{value:null,configurable:!0,enumerable:!1,writable:!1}),n.poolSize=8192;function d(v,s,l){if(typeof v=="string")return _(v,s);if(ArrayBuffer.isView(v))return C(v);if(v==null)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof v);if(ae(v,ArrayBuffer)||v&&ae(v.buffer,ArrayBuffer))return y(v,s,l);if(typeof v=="number")throw new TypeError('The "value" argument must not be of type number. Received type number');var T=v.valueOf&&v.valueOf();if(T!=null&&T!==v)return n.from(T,s,l);var H=P(v);if(H)return H;if(typeof Symbol!="undefined"&&Symbol.toPrimitive!=null&&typeof v[Symbol.toPrimitive]=="function")return n.from(v[Symbol.toPrimitive]("string"),s,l);throw new TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof v)}n.from=function(v,s,l){return d(v,s,l)},n.prototype.__proto__=Uint8Array.prototype,n.__proto__=Uint8Array;function g(v){if(typeof v!="number")throw new TypeError('"size" argument must be of type number');if(v<0)throw new RangeError('The value "'+v+'" is invalid for option "size"')}function E(v,s,l){return g(v),v<=0?o(v):s!==void 0?typeof l=="string"?o(v).fill(s,l):o(v).fill(s):o(v)}n.alloc=function(v,s,l){return E(v,s,l)};function u(v){return g(v),o(v<0?0:M(v)|0)}n.allocUnsafe=function(v){return u(v)},n.allocUnsafeSlow=function(v){return u(v)};function _(v,s){if((typeof s!="string"||s==="")&&(s="utf8"),!n.isEncoding(s))throw new TypeError("Unknown encoding: "+s);var l=O(v,s)|0,T=o(l),H=T.write(v,s);return H!==l&&(T=T.slice(0,H)),T}function C(v){for(var s=v.length<0?0:M(v.length)|0,l=o(s),T=0;T<s;T+=1)l[T]=v[T]&255;return l}function y(v,s,l){if(s<0||v.byteLength<s)throw new RangeError('"offset" is outside of buffer bounds');if(v.byteLength<s+(l||0))throw new RangeError('"length" is outside of buffer bounds');var T;return s===void 0&&l===void 0?T=new Uint8Array(v):l===void 0?T=new Uint8Array(v,s):T=new Uint8Array(v,s,l),T.__proto__=n.prototype,T}function P(v){if(n.isBuffer(v)){var s=M(v.length)|0,l=o(s);return l.length===0||v.copy(l,0,0,s),l}if(v.length!==void 0)return typeof v.length!="number"||oe(v.length)?o(0):C(v);if(v.type==="Buffer"&&Array.isArray(v.data))return C(v.data)}function M(v){if(v>=h)throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+h.toString(16)+" bytes");return v|0}function k(v){return+v!=v&&(v=0),n.alloc(+v)}n.isBuffer=function(s){return s!=null&&s._isBuffer===!0&&s!==n.prototype},n.compare=function(s,l){if(ae(s,Uint8Array)&&(s=n.from(s,s.offset,s.byteLength)),ae(l,Uint8Array)&&(l=n.from(l,l.offset,l.byteLength)),!n.isBuffer(s)||!n.isBuffer(l))throw new TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(s===l)return 0;for(var T=s.length,H=l.length,re=0,se=Math.min(T,H);re<se;++re)if(s[re]!==l[re]){T=s[re],H=l[re];break}return T<H?-1:H<T?1:0},n.isEncoding=function(s){switch(String(s).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},n.concat=function(s,l){if(!Array.isArray(s))throw new TypeError('"list" argument must be an Array of Buffers');if(s.length===0)return n.alloc(0);var T;if(l===void 0)for(l=0,T=0;T<s.length;++T)l+=s[T].length;var H=n.allocUnsafe(l),re=0;for(T=0;T<s.length;++T){var se=s[T];if(ae(se,Uint8Array)&&(se=n.from(se)),!n.isBuffer(se))throw new TypeError('"list" argument must be an Array of Buffers');se.copy(H,re),re+=se.length}return H};function O(v,s){if(n.isBuffer(v))return v.length;if(ArrayBuffer.isView(v)||ae(v,ArrayBuffer))return v.byteLength;if(typeof v!="string")throw new TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof v);var l=v.length,T=arguments.length>2&&arguments[2]===!0;if(!T&&l===0)return 0;for(var H=!1;;)switch(s){case"ascii":case"latin1":case"binary":return l;case"utf8":case"utf-8":return ue(v).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return l*2;case"hex":return l>>>1;case"base64":return W(v).length;default:if(H)return T?-1:ue(v).length;s=(""+s).toLowerCase(),H=!0}}n.byteLength=O;function U(v,s,l){var T=!1;if((s===void 0||s<0)&&(s=0),s>this.length||((l===void 0||l>this.length)&&(l=this.length),l<=0)||(l>>>=0,s>>>=0,l<=s))return"";for(v||(v="utf8");;)switch(v){case"hex":return Q(this,s,l);case"utf8":case"utf-8":return D(this,s,l);case"ascii":return ce(this,s,l);case"latin1":case"binary":return pe(this,s,l);case"base64":return G(this,s,l);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return L(this,s,l);default:if(T)throw new TypeError("Unknown encoding: "+v);v=(v+"").toLowerCase(),T=!0}}n.prototype._isBuffer=!0;function Y(v,s,l){var T=v[s];v[s]=v[l],v[l]=T}n.prototype.swap16=function(){var s=this.length;if(s%2!==0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var l=0;l<s;l+=2)Y(this,l,l+1);return this},n.prototype.swap32=function(){var s=this.length;if(s%4!==0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var l=0;l<s;l+=4)Y(this,l,l+3),Y(this,l+1,l+2);return this},n.prototype.swap64=function(){var s=this.length;if(s%8!==0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var l=0;l<s;l+=8)Y(this,l,l+7),Y(this,l+1,l+6),Y(this,l+2,l+5),Y(this,l+3,l+4);return this},n.prototype.toString=function(){var s=this.length;return s===0?"":arguments.length===0?D(this,0,s):U.apply(this,arguments)},n.prototype.toLocaleString=n.prototype.toString,n.prototype.equals=function(s){if(!n.isBuffer(s))throw new TypeError("Argument must be a Buffer");return this===s?!0:n.compare(this,s)===0},n.prototype.inspect=function(){var s="",l=m.INSPECT_MAX_BYTES;return s=this.toString("hex",0,l).replace(/(.{2})/g,"$1 ").trim(),this.length>l&&(s+=" ... "),"<Buffer "+s+">"},n.prototype.compare=function(s,l,T,H,re){if(ae(s,Uint8Array)&&(s=n.from(s,s.offset,s.byteLength)),!n.isBuffer(s))throw new TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof s);if(l===void 0&&(l=0),T===void 0&&(T=s?s.length:0),H===void 0&&(H=0),re===void 0&&(re=this.length),l<0||T>s.length||H<0||re>this.length)throw new RangeError("out of range index");if(H>=re&&l>=T)return 0;if(H>=re)return-1;if(l>=T)return 1;if(l>>>=0,T>>>=0,H>>>=0,re>>>=0,this===s)return 0;for(var se=re-H,he=T-l,me=Math.min(se,he),ye=this.slice(H,re),be=s.slice(l,T),ge=0;ge<me;++ge)if(ye[ge]!==be[ge]){se=ye[ge],he=be[ge];break}return se<he?-1:he<se?1:0};function Z(v,s,l,T,H){if(v.length===0)return-1;if(typeof l=="string"?(T=l,l=0):l>2147483647?l=2147483647:l<-2147483648&&(l=-2147483648),l=+l,oe(l)&&(l=H?0:v.length-1),l<0&&(l=v.length+l),l>=v.length){if(H)return-1;l=v.length-1}else if(l<0)if(H)l=0;else return-1;if(typeof s=="string"&&(s=n.from(s,T)),n.isBuffer(s))return s.length===0?-1:ee(v,s,l,T,H);if(typeof s=="number")return s=s&255,typeof Uint8Array.prototype.indexOf=="function"?H?Uint8Array.prototype.indexOf.call(v,s,l):Uint8Array.prototype.lastIndexOf.call(v,s,l):ee(v,[s],l,T,H);throw new TypeError("val must be string, number or Buffer")}function ee(v,s,l,T,H){var re=1,se=v.length,he=s.length;if(T!==void 0&&(T=String(T).toLowerCase(),T==="ucs2"||T==="ucs-2"||T==="utf16le"||T==="utf-16le")){if(v.length<2||s.length<2)return-1;re=2,se/=2,he/=2,l/=2}function me(Oe,lt){return re===1?Oe[lt]:Oe.readUInt16BE(lt*re)}var ye;if(H){var be=-1;for(ye=l;ye<se;ye++)if(me(v,ye)===me(s,be===-1?0:ye-be)){if(be===-1&&(be=ye),ye-be+1===he)return be*re}else be!==-1&&(ye-=ye-be),be=-1}else for(l+he>se&&(l=se-he),ye=l;ye>=0;ye--){for(var ge=!0,Ie=0;Ie<he;Ie++)if(me(v,ye+Ie)!==me(s,Ie)){ge=!1;break}if(ge)return ye}return-1}n.prototype.includes=function(s,l,T){return this.indexOf(s,l,T)!==-1},n.prototype.indexOf=function(s,l,T){return Z(this,s,l,T,!0)},n.prototype.lastIndexOf=function(s,l,T){return Z(this,s,l,T,!1)};function A(v,s,l,T){l=Number(l)||0;var H=v.length-l;T?(T=Number(T),T>H&&(T=H)):T=H;var re=s.length;T>re/2&&(T=re/2);for(var se=0;se<T;++se){var he=parseInt(s.substr(se*2,2),16);if(oe(he))return se;v[l+se]=he}return se}function j(v,s,l,T){return J(ue(s,v.length-l),v,l,T)}function b(v,s,l,T){return J(S(s),v,l,T)}function N(v,s,l,T){return b(v,s,l,T)}function z(v,s,l,T){return J(W(s),v,l,T)}function q(v,s,l,T){return J(w(s,v.length-l),v,l,T)}n.prototype.write=function(s,l,T,H){if(l===void 0)H="utf8",T=this.length,l=0;else if(T===void 0&&typeof l=="string")H=l,T=this.length,l=0;else if(isFinite(l))l=l>>>0,isFinite(T)?(T=T>>>0,H===void 0&&(H="utf8")):(H=T,T=void 0);else throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var re=this.length-l;if((T===void 0||T>re)&&(T=re),s.length>0&&(T<0||l<0)||l>this.length)throw new RangeError("Attempt to write outside buffer bounds");H||(H="utf8");for(var se=!1;;)switch(H){case"hex":return A(this,s,l,T);case"utf8":case"utf-8":return j(this,s,l,T);case"ascii":return b(this,s,l,T);case"latin1":case"binary":return N(this,s,l,T);case"base64":return z(this,s,l,T);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return q(this,s,l,T);default:if(se)throw new TypeError("Unknown encoding: "+H);H=(""+H).toLowerCase(),se=!0}},n.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};function G(v,s,l){return s===0&&l===v.length?i.fromByteArray(v):i.fromByteArray(v.slice(s,l))}function D(v,s,l){l=Math.min(v.length,l);for(var T=[],H=s;H<l;){var re=v[H],se=null,he=re>239?4:re>223?3:re>191?2:1;if(H+he<=l){var me,ye,be,ge;switch(he){case 1:re<128&&(se=re);break;case 2:me=v[H+1],(me&192)===128&&(ge=(re&31)<<6|me&63,ge>127&&(se=ge));break;case 3:me=v[H+1],ye=v[H+2],(me&192)===128&&(ye&192)===128&&(ge=(re&15)<<12|(me&63)<<6|ye&63,ge>2047&&(ge<55296||ge>57343)&&(se=ge));break;case 4:me=v[H+1],ye=v[H+2],be=v[H+3],(me&192)===128&&(ye&192)===128&&(be&192)===128&&(ge=(re&15)<<18|(me&63)<<12|(ye&63)<<6|be&63,ge>65535&&ge<1114112&&(se=ge))}}se===null?(se=65533,he=1):se>65535&&(se-=65536,T.push(se>>>10&1023|55296),se=56320|se&1023),T.push(se),H+=he}return le(T)}var te=4096;function le(v){var s=v.length;if(s<=te)return String.fromCharCode.apply(String,v);for(var l="",T=0;T<s;)l+=String.fromCharCode.apply(String,v.slice(T,T+=te));return l}function ce(v,s,l){var T="";l=Math.min(v.length,l);for(var H=s;H<l;++H)T+=String.fromCharCode(v[H]&127);return T}function pe(v,s,l){var T="";l=Math.min(v.length,l);for(var H=s;H<l;++H)T+=String.fromCharCode(v[H]);return T}function Q(v,s,l){var T=v.length;(!s||s<0)&&(s=0),(!l||l<0||l>T)&&(l=T);for(var H="",re=s;re<l;++re)H+=ie(v[re]);return H}function L(v,s,l){for(var T=v.slice(s,l),H="",re=0;re<T.length;re+=2)H+=String.fromCharCode(T[re]+T[re+1]*256);return H}n.prototype.slice=function(s,l){var T=this.length;s=~~s,l=l===void 0?T:~~l,s<0?(s+=T,s<0&&(s=0)):s>T&&(s=T),l<0?(l+=T,l<0&&(l=0)):l>T&&(l=T),l<s&&(l=s);var H=this.subarray(s,l);return H.__proto__=n.prototype,H};function I(v,s,l){if(v%1!==0||v<0)throw new RangeError("offset is not uint");if(v+s>l)throw new RangeError("Trying to access beyond buffer length")}n.prototype.readUIntLE=function(s,l,T){s=s>>>0,l=l>>>0,T||I(s,l,this.length);for(var H=this[s],re=1,se=0;++se<l&&(re*=256);)H+=this[s+se]*re;return H},n.prototype.readUIntBE=function(s,l,T){s=s>>>0,l=l>>>0,T||I(s,l,this.length);for(var H=this[s+--l],re=1;l>0&&(re*=256);)H+=this[s+--l]*re;return H},n.prototype.readUInt8=function(s,l){return s=s>>>0,l||I(s,1,this.length),this[s]},n.prototype.readUInt16LE=function(s,l){return s=s>>>0,l||I(s,2,this.length),this[s]|this[s+1]<<8},n.prototype.readUInt16BE=function(s,l){return s=s>>>0,l||I(s,2,this.length),this[s]<<8|this[s+1]},n.prototype.readUInt32LE=function(s,l){return s=s>>>0,l||I(s,4,this.length),(this[s]|this[s+1]<<8|this[s+2]<<16)+this[s+3]*16777216},n.prototype.readUInt32BE=function(s,l){return s=s>>>0,l||I(s,4,this.length),this[s]*16777216+(this[s+1]<<16|this[s+2]<<8|this[s+3])},n.prototype.readIntLE=function(s,l,T){s=s>>>0,l=l>>>0,T||I(s,l,this.length);for(var H=this[s],re=1,se=0;++se<l&&(re*=256);)H+=this[s+se]*re;return re*=128,H>=re&&(H-=Math.pow(2,8*l)),H},n.prototype.readIntBE=function(s,l,T){s=s>>>0,l=l>>>0,T||I(s,l,this.length);for(var H=l,re=1,se=this[s+--H];H>0&&(re*=256);)se+=this[s+--H]*re;return re*=128,se>=re&&(se-=Math.pow(2,8*l)),se},n.prototype.readInt8=function(s,l){return s=s>>>0,l||I(s,1,this.length),this[s]&128?(255-this[s]+1)*-1:this[s]},n.prototype.readInt16LE=function(s,l){s=s>>>0,l||I(s,2,this.length);var T=this[s]|this[s+1]<<8;return T&32768?T|4294901760:T},n.prototype.readInt16BE=function(s,l){s=s>>>0,l||I(s,2,this.length);var T=this[s+1]|this[s]<<8;return T&32768?T|4294901760:T},n.prototype.readInt32LE=function(s,l){return s=s>>>0,l||I(s,4,this.length),this[s]|this[s+1]<<8|this[s+2]<<16|this[s+3]<<24},n.prototype.readInt32BE=function(s,l){return s=s>>>0,l||I(s,4,this.length),this[s]<<24|this[s+1]<<16|this[s+2]<<8|this[s+3]},n.prototype.readFloatLE=function(s,l){return s=s>>>0,l||I(s,4,this.length),p.read(this,s,!0,23,4)},n.prototype.readFloatBE=function(s,l){return s=s>>>0,l||I(s,4,this.length),p.read(this,s,!1,23,4)},n.prototype.readDoubleLE=function(s,l){return s=s>>>0,l||I(s,8,this.length),p.read(this,s,!0,52,8)},n.prototype.readDoubleBE=function(s,l){return s=s>>>0,l||I(s,8,this.length),p.read(this,s,!1,52,8)};function R(v,s,l,T,H,re){if(!n.isBuffer(v))throw new TypeError('"buffer" argument must be a Buffer instance');if(s>H||s<re)throw new RangeError('"value" argument is out of bounds');if(l+T>v.length)throw new RangeError("Index out of range")}n.prototype.writeUIntLE=function(s,l,T,H){if(s=+s,l=l>>>0,T=T>>>0,!H){var re=Math.pow(2,8*T)-1;R(this,s,l,T,re,0)}var se=1,he=0;for(this[l]=s&255;++he<T&&(se*=256);)this[l+he]=s/se&255;return l+T},n.prototype.writeUIntBE=function(s,l,T,H){if(s=+s,l=l>>>0,T=T>>>0,!H){var re=Math.pow(2,8*T)-1;R(this,s,l,T,re,0)}var se=T-1,he=1;for(this[l+se]=s&255;--se>=0&&(he*=256);)this[l+se]=s/he&255;return l+T},n.prototype.writeUInt8=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,1,255,0),this[l]=s&255,l+1},n.prototype.writeUInt16LE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,2,65535,0),this[l]=s&255,this[l+1]=s>>>8,l+2},n.prototype.writeUInt16BE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,2,65535,0),this[l]=s>>>8,this[l+1]=s&255,l+2},n.prototype.writeUInt32LE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,4,4294967295,0),this[l+3]=s>>>24,this[l+2]=s>>>16,this[l+1]=s>>>8,this[l]=s&255,l+4},n.prototype.writeUInt32BE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,4,4294967295,0),this[l]=s>>>24,this[l+1]=s>>>16,this[l+2]=s>>>8,this[l+3]=s&255,l+4},n.prototype.writeIntLE=function(s,l,T,H){if(s=+s,l=l>>>0,!H){var re=Math.pow(2,8*T-1);R(this,s,l,T,re-1,-re)}var se=0,he=1,me=0;for(this[l]=s&255;++se<T&&(he*=256);)s<0&&me===0&&this[l+se-1]!==0&&(me=1),this[l+se]=(s/he>>0)-me&255;return l+T},n.prototype.writeIntBE=function(s,l,T,H){if(s=+s,l=l>>>0,!H){var re=Math.pow(2,8*T-1);R(this,s,l,T,re-1,-re)}var se=T-1,he=1,me=0;for(this[l+se]=s&255;--se>=0&&(he*=256);)s<0&&me===0&&this[l+se+1]!==0&&(me=1),this[l+se]=(s/he>>0)-me&255;return l+T},n.prototype.writeInt8=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,1,127,-128),s<0&&(s=255+s+1),this[l]=s&255,l+1},n.prototype.writeInt16LE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,2,32767,-32768),this[l]=s&255,this[l+1]=s>>>8,l+2},n.prototype.writeInt16BE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,2,32767,-32768),this[l]=s>>>8,this[l+1]=s&255,l+2},n.prototype.writeInt32LE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,4,2147483647,-2147483648),this[l]=s&255,this[l+1]=s>>>8,this[l+2]=s>>>16,this[l+3]=s>>>24,l+4},n.prototype.writeInt32BE=function(s,l,T){return s=+s,l=l>>>0,T||R(this,s,l,4,2147483647,-2147483648),s<0&&(s=4294967295+s+1),this[l]=s>>>24,this[l+1]=s>>>16,this[l+2]=s>>>8,this[l+3]=s&255,l+4};function K(v,s,l,T,H,re){if(l+T>v.length)throw new RangeError("Index out of range");if(l<0)throw new RangeError("Index out of range")}function F(v,s,l,T,H){return s=+s,l=l>>>0,H||K(v,s,l,4),p.write(v,s,l,T,23,4),l+4}n.prototype.writeFloatLE=function(s,l,T){return F(this,s,l,!0,T)},n.prototype.writeFloatBE=function(s,l,T){return F(this,s,l,!1,T)};function B(v,s,l,T,H){return s=+s,l=l>>>0,H||K(v,s,l,8),p.write(v,s,l,T,52,8),l+8}n.prototype.writeDoubleLE=function(s,l,T){return B(this,s,l,!0,T)},n.prototype.writeDoubleBE=function(s,l,T){return B(this,s,l,!1,T)},n.prototype.copy=function(s,l,T,H){if(!n.isBuffer(s))throw new TypeError("argument should be a Buffer");if(T||(T=0),!H&&H!==0&&(H=this.length),l>=s.length&&(l=s.length),l||(l=0),H>0&&H<T&&(H=T),H===T||s.length===0||this.length===0)return 0;if(l<0)throw new RangeError("targetStart out of bounds");if(T<0||T>=this.length)throw new RangeError("Index out of range");if(H<0)throw new RangeError("sourceEnd out of bounds");H>this.length&&(H=this.length),s.length-l<H-T&&(H=s.length-l+T);var re=H-T;if(this===s&&typeof Uint8Array.prototype.copyWithin=="function")this.copyWithin(l,T,H);else if(this===s&&T<l&&l<H)for(var se=re-1;se>=0;--se)s[se+l]=this[se+T];else Uint8Array.prototype.set.call(s,this.subarray(T,H),l);return re},n.prototype.fill=function(s,l,T,H){if(typeof s=="string"){if(typeof l=="string"?(H=l,l=0,T=this.length):typeof T=="string"&&(H=T,T=this.length),H!==void 0&&typeof H!="string")throw new TypeError("encoding must be a string");if(typeof H=="string"&&!n.isEncoding(H))throw new TypeError("Unknown encoding: "+H);if(s.length===1){var re=s.charCodeAt(0);(H==="utf8"&&re<128||H==="latin1")&&(s=re)}}else typeof s=="number"&&(s=s&255);if(l<0||this.length<l||this.length<T)throw new RangeError("Out of range index");if(T<=l)return this;l=l>>>0,T=T===void 0?this.length:T>>>0,s||(s=0);var se;if(typeof s=="number")for(se=l;se<T;++se)this[se]=s;else{var he=n.isBuffer(s)?s:n.from(s,H),me=he.length;if(me===0)throw new TypeError('The value "'+s+'" is invalid for argument "value"');for(se=0;se<T-l;++se)this[se+l]=he[se%me]}return this};var V=/[^+/0-9A-Za-z-_]/g;function $(v){if(v=v.split("=")[0],v=v.trim().replace(V,""),v.length<2)return"";for(;v.length%4!==0;)v=v+"=";return v}function ie(v){return v<16?"0"+v.toString(16):v.toString(16)}function ue(v,s){s=s||1/0;for(var l,T=v.length,H=null,re=[],se=0;se<T;++se){if(l=v.charCodeAt(se),l>55295&&l<57344){if(!H){if(l>56319){(s-=3)>-1&&re.push(239,191,189);continue}else if(se+1===T){(s-=3)>-1&&re.push(239,191,189);continue}H=l;continue}if(l<56320){(s-=3)>-1&&re.push(239,191,189),H=l;continue}l=(H-55296<<10|l-56320)+65536}else H&&(s-=3)>-1&&re.push(239,191,189);if(H=null,l<128){if((s-=1)<0)break;re.push(l)}else if(l<2048){if((s-=2)<0)break;re.push(l>>6|192,l&63|128)}else if(l<65536){if((s-=3)<0)break;re.push(l>>12|224,l>>6&63|128,l&63|128)}else if(l<1114112){if((s-=4)<0)break;re.push(l>>18|240,l>>12&63|128,l>>6&63|128,l&63|128)}else throw new Error("Invalid code point")}return re}function S(v){for(var s=[],l=0;l<v.length;++l)s.push(v.charCodeAt(l)&255);return s}function w(v,s){for(var l,T,H,re=[],se=0;se<v.length&&!((s-=2)<0);++se)l=v.charCodeAt(se),T=l>>8,H=l%256,re.push(H),re.push(T);return re}function W(v){return i.toByteArray($(v))}function J(v,s,l,T){for(var H=0;H<T&&!(H+l>=s.length||H>=v.length);++H)s[H+l]=v[H];return H}function ae(v,s){return v instanceof s||v!=null&&v.constructor!=null&&v.constructor.name!=null&&v.constructor.name===s.name}function oe(v){return v!==v}}).call(this,t("buffer").Buffer)},{"base64-js":10,buffer:12,ieee754:87}],13:[function(t,a,m){(function(r){function i(k){return Array.isArray?Array.isArray(k):M(k)==="[object Array]"}m.isArray=i;function p(k){return typeof k=="boolean"}m.isBoolean=p;function h(k){return k===null}m.isNull=h;function c(k){return k==null}m.isNullOrUndefined=c;function o(k){return typeof k=="number"}m.isNumber=o;function n(k){return typeof k=="string"}m.isString=n;function d(k){return typeof k=="symbol"}m.isSymbol=d;function g(k){return k===void 0}m.isUndefined=g;function E(k){return M(k)==="[object RegExp]"}m.isRegExp=E;function u(k){return typeof k=="object"&&k!==null}m.isObject=u;function _(k){return M(k)==="[object Date]"}m.isDate=_;function C(k){return M(k)==="[object Error]"||k instanceof Error}m.isError=C;function y(k){return typeof k=="function"}m.isFunction=y;function P(k){return k===null||typeof k=="boolean"||typeof k=="number"||typeof k=="string"||typeof k=="symbol"||typeof k=="undefined"}m.isPrimitive=P,m.isBuffer=r.isBuffer;function M(k){return Object.prototype.toString.call(k)}}).call(this,{isBuffer:t("../../is-buffer/index.js")})},{"../../is-buffer/index.js":89}],14:[function(t,a,m){var r=t("type/value/is"),i=t("type/value/ensure"),p=t("type/plain-function/ensure"),h=t("es5-ext/object/copy"),c=t("es5-ext/object/normalize-options"),o=t("es5-ext/object/map"),n=Function.prototype.bind,d=Object.defineProperty,g=Object.prototype.hasOwnProperty,E;E=function(u,_,C){var y=i(_)&&p(_.value),P;return P=h(_),delete P.writable,delete P.value,P.get=function(){return!C.overwriteDefinition&&g.call(this,u)?y:(_.value=n.call(y,C.resolveContext?C.resolveContext(this):this),d(this,u,_),this[u])},P},a.exports=function(u){var _=c(arguments[1]);return r(_.resolveContext)&&p(_.resolveContext),o(u,function(C,y){return E(y,C,_)})}},{"es5-ext/object/copy":41,"es5-ext/object/map":49,"es5-ext/object/normalize-options":50,"type/plain-function/ensure":126,"type/value/ensure":130,"type/value/is":131}],15:[function(t,a,m){var r=t("type/value/is"),i=t("type/plain-function/is"),p=t("es5-ext/object/assign"),h=t("es5-ext/object/normalize-options"),c=t("es5-ext/string/#/contains"),o=a.exports=function(n,d){var g,E,u,_,C;return arguments.length<2||typeof n!="string"?(_=d,d=n,n=null):_=arguments[2],r(n)?(g=c.call(n,"c"),E=c.call(n,"e"),u=c.call(n,"w")):(g=u=!0,E=!1),C={value:d,configurable:g,enumerable:E,writable:u},_?p(h(_),C):C};o.gs=function(n,d,g){var E,u,_,C;return typeof n!="string"?(_=g,g=d,d=n,n=null):_=arguments[3],r(d)?i(d)?r(g)?i(g)||(_=g,g=void 0):g=void 0:(_=d,d=g=void 0):d=void 0,r(n)?(E=c.call(n,"c"),u=c.call(n,"e")):(E=!0,u=!1),C={get:d,set:g,configurable:E,enumerable:u},_?p(h(_),C):C}},{"es5-ext/object/assign":38,"es5-ext/object/normalize-options":50,"es5-ext/string/#/contains":57,"type/plain-function/is":127,"type/value/is":131}],16:[function(t,a,m){var r=1e3,i=r*60,p=i*60,h=p*24,c=h*7,o=h*365.25;a.exports=function(u,_){_=_||{};var C=typeof u;if(C==="string"&&u.length>0)return n(u);if(C==="number"&&isFinite(u))return _.long?g(u):d(u);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(u))};function n(u){if(u=String(u),!(u.length>100)){var _=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(u);if(_){var C=parseFloat(_[1]),y=(_[2]||"ms").toLowerCase();switch(y){case"years":case"year":case"yrs":case"yr":case"y":return C*o;case"weeks":case"week":case"w":return C*c;case"days":case"day":case"d":return C*h;case"hours":case"hour":case"hrs":case"hr":case"h":return C*p;case"minutes":case"minute":case"mins":case"min":case"m":return C*i;case"seconds":case"second":case"secs":case"sec":case"s":return C*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return C;default:return}}}}function d(u){var _=Math.abs(u);return _>=h?Math.round(u/h)+"d":_>=p?Math.round(u/p)+"h":_>=i?Math.round(u/i)+"m":_>=r?Math.round(u/r)+"s":u+"ms"}function g(u){var _=Math.abs(u);return _>=h?E(u,_,h,"day"):_>=p?E(u,_,p,"hour"):_>=i?E(u,_,i,"minute"):_>=r?E(u,_,r,"second"):u+" ms"}function E(u,_,C,y){var P=_>=C*1.5;return Math.round(u/C)+" "+y+(P?"s":"")}},{}],17:[function(t,a,m){(function(r){m.log=h,m.formatArgs=p,m.save=c,m.load=o,m.useColors=i,m.storage=n(),m.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function i(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function p(g){if(g[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+g[0]+(this.useColors?"%c ":" ")+"+"+a.exports.humanize(this.diff),!this.useColors)return;let E="color: "+this.color;g.splice(1,0,E,"color: inherit");let u=0,_=0;g[0].replace(/%[a-zA-Z%]/g,C=>{C!=="%%"&&(u++,C==="%c"&&(_=u))}),g.splice(_,0,E)}function h(...g){return typeof console=="object"&&console.log&&fe("log","at node_modules/mqtt/dist/mqtt.js:4879",...g)}function c(g){try{g?m.storage.setItem("debug",g):m.storage.removeItem("debug")}catch(E){}}function o(){let g;try{g=m.storage.getItem("debug")}catch(E){}return!g&&typeof r!="undefined"&&"env"in r&&(g=r.env.DEBUG),g}function n(){try{return localStorage}catch(g){}}a.exports=t("./common")(m);let{formatters:d}=a.exports;d.j=function(g){try{return JSON.stringify(g)}catch(E){return"[UnexpectedJSONParseError]: "+E.message}}}).call(this,t("_process"))},{"./common":18,_process:100}],18:[function(t,a,m){function r(i){h.debug=h,h.default=h,h.coerce=u,h.disable=d,h.enable=n,h.enabled=g,h.humanize=t("ms"),Object.keys(i).forEach(_=>{h[_]=i[_]}),h.instances=[],h.names=[],h.skips=[],h.formatters={};function p(_){let C=0;for(let y=0;y<_.length;y++)C=(C<<5)-C+_.charCodeAt(y),C|=0;return h.colors[Math.abs(C)%h.colors.length]}h.selectColor=p;function h(_){let C;function y(...P){if(!y.enabled)return;let M=y,k=Number(new Date),O=k-(C||k);M.diff=O,M.prev=C,M.curr=k,C=k,P[0]=h.coerce(P[0]),typeof P[0]!="string"&&P.unshift("%O");let U=0;P[0]=P[0].replace(/%([a-zA-Z%])/g,(Z,ee)=>{if(Z==="%%")return Z;U++;let A=h.formatters[ee];if(typeof A=="function"){let j=P[U];Z=A.call(M,j),P.splice(U,1),U--}return Z}),h.formatArgs.call(M,P),(M.log||h.log).apply(M,P)}return y.namespace=_,y.enabled=h.enabled(_),y.useColors=h.useColors(),y.color=p(_),y.destroy=c,y.extend=o,typeof h.init=="function"&&h.init(y),h.instances.push(y),y}function c(){let _=h.instances.indexOf(this);return _!==-1?(h.instances.splice(_,1),!0):!1}function o(_,C){let y=h(this.namespace+(typeof C=="undefined"?":":C)+_);return y.log=this.log,y}function n(_){h.save(_),h.names=[],h.skips=[];let C,y=(typeof _=="string"?_:"").split(/[\s,]+/),P=y.length;for(C=0;C<P;C++)y[C]&&(_=y[C].replace(/\*/g,".*?"),_[0]==="-"?h.skips.push(new RegExp("^"+_.substr(1)+"$")):h.names.push(new RegExp("^"+_+"$")));for(C=0;C<h.instances.length;C++){let M=h.instances[C];M.enabled=h.enabled(M.namespace)}}function d(){let _=[...h.names.map(E),...h.skips.map(E).map(C=>"-"+C)].join(",");return h.enable(""),_}function g(_){if(_[_.length-1]==="*")return!0;let C,y;for(C=0,y=h.skips.length;C<y;C++)if(h.skips[C].test(_))return!1;for(C=0,y=h.names.length;C<y;C++)if(h.names[C].test(_))return!0;return!1}function E(_){return _.toString().substring(2,_.toString().length-2).replace(/\.\*\?$/,"*")}function u(_){return _ instanceof Error?_.stack||_.message:_}return h.enable(h.load()),h}a.exports=r},{ms:16}],19:[function(t,a,m){(function(r,i){var p=t("readable-stream"),h=t("end-of-stream"),c=t("inherits"),o=t("stream-shift"),n=i.from&&i.from!==Uint8Array.from?i.from([0]):new i([0]),d=function(y,P){y._corked?y.once("uncork",P):P()},g=function(y,P){y._autoDestroy&&y.destroy(P)},E=function(y,P){return function(M){M?g(y,M.message==="premature close"?null:M):P&&!y._ended&&y.end()}},u=function(y,P){if(!y||y._writableState&&y._writableState.finished)return P();if(y._writableState)return y.end(P);y.end(),P()},_=function(y){return new p.Readable({objectMode:!0,highWaterMark:16}).wrap(y)},C=function(y,P,M){if(!(this instanceof C))return new C(y,P,M);p.Duplex.call(this,M),this._writable=null,this._readable=null,this._readable2=null,this._autoDestroy=!M||M.autoDestroy!==!1,this._forwardDestroy=!M||M.destroy!==!1,this._forwardEnd=!M||M.end!==!1,this._corked=1,this._ondrain=null,this._drained=!1,this._forwarding=!1,this._unwrite=null,this._unread=null,this._ended=!1,this.destroyed=!1,y&&this.setWritable(y),P&&this.setReadable(P)};c(C,p.Duplex),C.obj=function(y,P,M){return M||(M={}),M.objectMode=!0,M.highWaterMark=16,new C(y,P,M)},C.prototype.cork=function(){++this._corked===1&&this.emit("cork")},C.prototype.uncork=function(){this._corked&&--this._corked===0&&this.emit("uncork")},C.prototype.setWritable=function(y){if(this._unwrite&&this._unwrite(),this.destroyed){y&&y.destroy&&y.destroy();return}if(y===null||y===!1){this.end();return}var P=this,M=h(y,{writable:!0,readable:!1},E(this,this._forwardEnd)),k=function(){var U=P._ondrain;P._ondrain=null,U&&U()},O=function(){P._writable.removeListener("drain",k),M()};this._unwrite&&r.nextTick(k),this._writable=y,this._writable.on("drain",k),this._unwrite=O,this.uncork()},C.prototype.setReadable=function(y){if(this._unread&&this._unread(),this.destroyed){y&&y.destroy&&y.destroy();return}if(y===null||y===!1){this.push(null),this.resume();return}var P=this,M=h(y,{writable:!1,readable:!0},E(this)),k=function(){P._forward()},O=function(){P.push(null)},U=function(){P._readable2.removeListener("readable",k),P._readable2.removeListener("end",O),M()};this._drained=!0,this._readable=y,this._readable2=y._readableState?y:_(y),this._readable2.on("readable",k),this._readable2.on("end",O),this._unread=U,this._forward()},C.prototype._read=function(){this._drained=!0,this._forward()},C.prototype._forward=function(){if(!(this._forwarding||!this._readable2||!this._drained)){this._forwarding=!0;for(var y;this._drained&&(y=o(this._readable2))!==null;)this.destroyed||(this._drained=this.push(y));this._forwarding=!1}},C.prototype.destroy=function(y){if(!this.destroyed){this.destroyed=!0;var P=this;r.nextTick(function(){P._destroy(y)})}},C.prototype._destroy=function(y){if(y){var P=this._ondrain;this._ondrain=null,P?P(y):this.emit("error",y)}this._forwardDestroy&&(this._readable&&this._readable.destroy&&this._readable.destroy(),this._writable&&this._writable.destroy&&this._writable.destroy()),this.emit("close")},C.prototype._write=function(y,P,M){if(this.destroyed)return M();if(this._corked)return d(this,this._write.bind(this,y,P,M));if(y===n)return this._finish(M);if(!this._writable)return M();this._writable.write(y)===!1?this._ondrain=M:M()},C.prototype._finish=function(y){var P=this;this.emit("preend"),d(this,function(){u(P._forwardEnd&&P._writable,function(){P._writableState.prefinished===!1&&(P._writableState.prefinished=!0),P.emit("prefinish"),d(P,y)})})},C.prototype.end=function(y,P,M){return typeof y=="function"?this.end(null,null,y):typeof P=="function"?this.end(y,null,P):(this._ended=!0,y&&this.write(y),this._writableState.ending||this.write(n),p.Writable.prototype.end.call(this,M))},a.exports=C}).call(this,t("_process"),t("buffer").Buffer)},{_process:100,buffer:12,"end-of-stream":20,inherits:88,"readable-stream":116,"stream-shift":119}],20:[function(t,a,m){var r=t("once"),i=function(){},p=function(o){return o.setHeader&&typeof o.abort=="function"},h=function(o){return o.stdio&&Array.isArray(o.stdio)&&o.stdio.length===3},c=function(o,n,d){if(typeof n=="function")return c(o,null,n);n||(n={}),d=r(d||i);var g=o._writableState,E=o._readableState,u=n.readable||n.readable!==!1&&o.readable,_=n.writable||n.writable!==!1&&o.writable,C=function(){o.writable||y()},y=function(){_=!1,u||d.call(o)},P=function(){u=!1,_||d.call(o)},M=function(Y){d.call(o,Y?new Error("exited with error code: "+Y):null)},k=function(Y){d.call(o,Y)},O=function(){if(u&&!(E&&E.ended))return d.call(o,new Error("premature close"));if(_&&!(g&&g.ended))return d.call(o,new Error("premature close"))},U=function(){o.req.on("finish",y)};return p(o)?(o.on("complete",y),o.on("abort",O),o.req?U():o.on("request",U)):_&&!g&&(o.on("end",C),o.on("close",C)),h(o)&&o.on("exit",M),o.on("end",P),o.on("finish",y),n.error!==!1&&o.on("error",k),o.on("close",O),function(){o.removeListener("complete",y),o.removeListener("abort",O),o.removeListener("request",U),o.req&&o.req.removeListener("finish",y),o.removeListener("end",C),o.removeListener("close",C),o.removeListener("finish",y),o.removeListener("exit",M),o.removeListener("end",P),o.removeListener("error",k),o.removeListener("close",O)}};a.exports=c},{once:98}],21:[function(t,a,m){var r=t("../../object/valid-value");a.exports=function(){return r(this).length=0,this}},{"../../object/valid-value":56}],22:[function(t,a,m){var r=t("../../number/is-nan"),i=t("../../number/to-pos-integer"),p=t("../../object/valid-value"),h=Array.prototype.indexOf,c=Object.prototype.hasOwnProperty,o=Math.abs,n=Math.floor;a.exports=function(d){var g,E,u,_;if(!r(d))return h.apply(this,arguments);for(E=i(p(this).length),u=arguments[1],isNaN(u)?u=0:u>=0?u=n(u):u=i(this.length)-n(o(u)),g=u;g<E;++g)if(c.call(this,g)&&(_=this[g],r(_)))return g;return-1}},{"../../number/is-nan":32,"../../number/to-pos-integer":36,"../../object/valid-value":56}],23:[function(t,a,m){a.exports=t("./is-implemented")()?Array.from:t("./shim")},{"./is-implemented":24,"./shim":25}],24:[function(t,a,m){a.exports=function(){var r=Array.from,i,p;return typeof r!="function"?!1:(i=["raz","dwa"],p=r(i),!!(p&&p!==i&&p[1]==="dwa"))}},{}],25:[function(t,a,m){var r=t("es6-symbol").iterator,i=t("../../function/is-arguments"),p=t("../../function/is-function"),h=t("../../number/to-pos-integer"),c=t("../../object/valid-callable"),o=t("../../object/valid-value"),n=t("../../object/is-value"),d=t("../../string/is-string"),g=Array.isArray,E=Function.prototype.call,u={configurable:!0,enumerable:!0,writable:!0,value:null},_=Object.defineProperty;a.exports=function(C){var y=arguments[1],P=arguments[2],M,k,O,U,Y,Z,ee,A,j,b;if(C=Object(o(C)),n(y)&&c(y),!this||this===Array||!p(this)){if(!y){if(i(C))return Y=C.length,Y!==1?Array.apply(null,C):(U=new Array(1),U[0]=C[0],U);if(g(C)){for(U=new Array(Y=C.length),k=0;k<Y;++k)U[k]=C[k];return U}}U=[]}else M=this;if(!g(C)){if((j=C[r])!==void 0){for(ee=c(j).call(C),M&&(U=new M),A=ee.next(),k=0;!A.done;)b=y?E.call(y,P,A.value,k):A.value,M?(u.value=b,_(U,k,u)):U[k]=b,A=ee.next(),++k;Y=k}else if(d(C)){for(Y=C.length,M&&(U=new M),k=0,O=0;k<Y;++k)b=C[k],k+1<Y&&(Z=b.charCodeAt(0),Z>=55296&&Z<=56319&&(b+=C[++k])),b=y?E.call(y,P,b,O):b,M?(u.value=b,_(U,O,u)):U[O]=b,++O;Y=O}}if(Y===void 0)for(Y=h(C.length),M&&(U=new M(Y)),k=0;k<Y;++k)b=y?E.call(y,P,C[k],k):C[k],M?(u.value=b,_(U,k,u)):U[k]=b;return M&&(u.value=null,U.length=Y),U}},{"../../function/is-arguments":26,"../../function/is-function":27,"../../number/to-pos-integer":36,"../../object/is-value":45,"../../object/valid-callable":55,"../../object/valid-value":56,"../../string/is-string":60,"es6-symbol":74}],26:[function(t,a,m){var r=Object.prototype.toString,i=r.call(function(){return arguments}());a.exports=function(p){return r.call(p)===i}},{}],27:[function(t,a,m){var r=Object.prototype.toString,i=RegExp.prototype.test.bind(/^[object [A-Za-z0-9]*Function]$/);a.exports=function(p){return typeof p=="function"&&i(r.call(p))}},{}],28:[function(t,a,m){a.exports=function(){}},{}],29:[function(t,a,m){a.exports=t("./is-implemented")()?Math.sign:t("./shim")},{"./is-implemented":30,"./shim":31}],30:[function(t,a,m){a.exports=function(){var r=Math.sign;return typeof r!="function"?!1:r(10)===1&&r(-20)===-1}},{}],31:[function(t,a,m){a.exports=function(r){return r=Number(r),isNaN(r)||r===0?r:r>0?1:-1}},{}],32:[function(t,a,m){a.exports=t("./is-implemented")()?Number.isNaN:t("./shim")},{"./is-implemented":33,"./shim":34}],33:[function(t,a,m){a.exports=function(){var r=Number.isNaN;return typeof r!="function"?!1:!r({})&&r(NaN)&&!r(34)}},{}],34:[function(t,a,m){a.exports=function(r){return r!==r}},{}],35:[function(t,a,m){var r=t("../math/sign"),i=Math.abs,p=Math.floor;a.exports=function(h){return isNaN(h)?0:(h=Number(h),h===0||!isFinite(h)?h:r(h)*p(i(h)))}},{"../math/sign":29}],36:[function(t,a,m){var r=t("./to-integer"),i=Math.max;a.exports=function(p){return i(0,r(p))}},{"./to-integer":35}],37:[function(t,a,m){var r=t("./valid-callable"),i=t("./valid-value"),p=Function.prototype.bind,h=Function.prototype.call,c=Object.keys,o=Object.prototype.propertyIsEnumerable;a.exports=function(n,d){return function(g,E){var u,_=arguments[2],C=arguments[3];return g=Object(i(g)),r(E),u=c(g),C&&u.sort(typeof C=="function"?p.call(C,g):void 0),typeof n!="function"&&(n=u[n]),h.call(n,u,function(y,P){return o.call(g,y)?h.call(E,_,g[y],y,g,P):d})}}},{"./valid-callable":55,"./valid-value":56}],38:[function(t,a,m){a.exports=t("./is-implemented")()?Object.assign:t("./shim")},{"./is-implemented":39,"./shim":40}],39:[function(t,a,m){a.exports=function(){var r=Object.assign,i;return typeof r!="function"?!1:(i={foo:"raz"},r(i,{bar:"dwa"},{trzy:"trzy"}),i.foo+i.bar+i.trzy==="razdwatrzy")}},{}],40:[function(t,a,m){var r=t("../keys"),i=t("../valid-value"),p=Math.max;a.exports=function(h,c){var o,n,d=p(arguments.length,2),g;for(h=Object(i(h)),g=function(E){try{h[E]=c[E]}catch(u){o||(o=u)}},n=1;n<d;++n)c=arguments[n],r(c).forEach(g);if(o!==void 0)throw o;return h}},{"../keys":46,"../valid-value":56}],41:[function(t,a,m){var r=t("../array/from"),i=t("./assign"),p=t("./valid-value");a.exports=function(h){var c=Object(p(h)),o=arguments[1],n=Object(arguments[2]);if(c!==h&&!o)return c;var d={};return o?r(o,function(g){(n.ensure||g in h)&&(d[g]=h[g])}):i(d,h),d}},{"../array/from":23,"./assign":38,"./valid-value":56}],42:[function(t,a,m){var r=Object.create,i;t("./set-prototype-of/is-implemented")()||(i=t("./set-prototype-of/shim")),a.exports=function(){var p,h,c;return!i||i.level!==1?r:(p={},h={},c={configurable:!1,enumerable:!1,writable:!0,value:void 0},Object.getOwnPropertyNames(Object.prototype).forEach(function(o){if(o==="__proto__"){h[o]={configurable:!0,enumerable:!1,writable:!0,value:void 0};return}h[o]=c}),Object.defineProperties(p,h),Object.defineProperty(i,"nullPolyfill",{configurable:!1,enumerable:!1,writable:!1,value:p}),function(o,n){return r(o===null?p:o,n)})}()},{"./set-prototype-of/is-implemented":53,"./set-prototype-of/shim":54}],43:[function(t,a,m){a.exports=t("./_iterate")("forEach")},{"./_iterate":37}],44:[function(t,a,m){var r=t("./is-value"),i={function:!0,object:!0};a.exports=function(p){return r(p)&&i[typeof p]||!1}},{"./is-value":45}],45:[function(t,a,m){var r=t("../function/noop")();a.exports=function(i){return i!==r&&i!==null}},{"../function/noop":28}],46:[function(t,a,m){a.exports=t("./is-implemented")()?Object.keys:t("./shim")},{"./is-implemented":47,"./shim":48}],47:[function(t,a,m){a.exports=function(){try{return Object.keys("primitive"),!0}catch(r){return!1}}},{}],48:[function(t,a,m){var r=t("../is-value"),i=Object.keys;a.exports=function(p){return i(r(p)?Object(p):p)}},{"../is-value":45}],49:[function(t,a,m){var r=t("./valid-callable"),i=t("./for-each"),p=Function.prototype.call;a.exports=function(h,c){var o={},n=arguments[2];return r(c),i(h,function(d,g,E,u){o[g]=p.call(c,n,d,g,E,u)}),o}},{"./for-each":43,"./valid-callable":55}],50:[function(t,a,m){var r=t("./is-value"),i=Array.prototype.forEach,p=Object.create,h=function(c,o){var n;for(n in c)o[n]=c[n]};a.exports=function(c){var o=p(null);return i.call(arguments,function(n){r(n)&&h(Object(n),o)}),o}},{"./is-value":45}],51:[function(t,a,m){var r=Array.prototype.forEach,i=Object.create;a.exports=function(p){var h=i(null);return r.call(arguments,function(c){h[c]=!0}),h}},{}],52:[function(t,a,m){a.exports=t("./is-implemented")()?Object.setPrototypeOf:t("./shim")},{"./is-implemented":53,"./shim":54}],53:[function(t,a,m){var r=Object.create,i=Object.getPrototypeOf,p={};a.exports=function(){var h=Object.setPrototypeOf,c=arguments[0]||r;return typeof h!="function"?!1:i(h(c(null),p))===p}},{}],54:[function(t,a,m){var r=t("../is-object"),i=t("../valid-value"),p=Object.prototype.isPrototypeOf,h=Object.defineProperty,c={configurable:!0,enumerable:!1,writable:!0,value:void 0},o;o=function(n,d){if(i(n),d===null||r(d))return n;throw new TypeError("Prototype must be null or an object")},a.exports=function(n){var d,g;return n?(n.level===2?n.set?(g=n.set,d=function(E,u){return g.call(o(E,u),u),E}):d=function(E,u){return o(E,u).__proto__=u,E}:d=function E(u,_){var C;return o(u,_),C=p.call(E.nullPolyfill,u),C&&delete E.nullPolyfill.__proto__,_===null&&(_=E.nullPolyfill),u.__proto__=_,C&&h(E.nullPolyfill,"__proto__",c),u},Object.defineProperty(d,"level",{configurable:!1,enumerable:!1,writable:!1,value:n.level})):null}(function(){var n=Object.create(null),d={},g,E=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__");if(E){try{g=E.set,g.call(n,d)}catch(u){}if(Object.getPrototypeOf(n)===d)return{set:g,level:2}}return n.__proto__=d,Object.getPrototypeOf(n)===d?{level:2}:(n={},n.__proto__=d,Object.getPrototypeOf(n)===d?{level:1}:!1)}()),t("../create")},{"../create":42,"../is-object":44,"../valid-value":56}],55:[function(t,a,m){a.exports=function(r){if(typeof r!="function")throw new TypeError(r+" is not a function");return r}},{}],56:[function(t,a,m){var r=t("./is-value");a.exports=function(i){if(!r(i))throw new TypeError("Cannot use null or undefined");return i}},{"./is-value":45}],57:[function(t,a,m){a.exports=t("./is-implemented")()?String.prototype.contains:t("./shim")},{"./is-implemented":58,"./shim":59}],58:[function(t,a,m){var r="razdwatrzy";a.exports=function(){return typeof r.contains!="function"?!1:r.contains("dwa")===!0&&r.contains("foo")===!1}},{}],59:[function(t,a,m){var r=String.prototype.indexOf;a.exports=function(i){return r.call(this,i,arguments[1])>-1}},{}],60:[function(t,a,m){var r=Object.prototype.toString,i=r.call("");a.exports=function(p){return typeof p=="string"||p&&typeof p=="object"&&(p instanceof String||r.call(p)===i)||!1}},{}],61:[function(t,a,m){var r=t("es5-ext/object/set-prototype-of"),i=t("es5-ext/string/#/contains"),p=t("d"),h=t("es6-symbol"),c=t("./"),o=Object.defineProperty,n;n=a.exports=function(d,g){if(!(this instanceof n))throw new TypeError("Constructor requires 'new'");c.call(this,d),g?i.call(g,"key+value")?g="key+value":i.call(g,"key")?g="key":g="value":g="value",o(this,"__kind__",p("",g))},r&&r(n,c),delete n.prototype.constructor,n.prototype=Object.create(c.prototype,{_resolve:p(function(d){return this.__kind__==="value"?this.__list__[d]:this.__kind__==="key+value"?[d,this.__list__[d]]:d})}),o(n.prototype,h.toStringTag,p("c","Array Iterator"))},{"./":64,d:15,"es5-ext/object/set-prototype-of":52,"es5-ext/string/#/contains":57,"es6-symbol":74}],62:[function(t,a,m){var r=t("es5-ext/function/is-arguments"),i=t("es5-ext/object/valid-callable"),p=t("es5-ext/string/is-string"),h=t("./get"),c=Array.isArray,o=Function.prototype.call,n=Array.prototype.some;a.exports=function(d,g){var E,u=arguments[2],_,C,y,P,M,k,O;if(c(d)||r(d)?E="array":p(d)?E="string":d=h(d),i(g),C=function(){y=!0},E==="array"){n.call(d,function(U){return o.call(g,u,U,C),y});return}if(E==="string"){for(M=d.length,P=0;P<M&&(k=d[P],P+1<M&&(O=k.charCodeAt(0),O>=55296&&O<=56319&&(k+=d[++P])),o.call(g,u,k,C),!y);++P);return}for(_=d.next();!_.done;){if(o.call(g,u,_.value,C),y)return;_=d.next()}}},{"./get":63,"es5-ext/function/is-arguments":26,"es5-ext/object/valid-callable":55,"es5-ext/string/is-string":60}],63:[function(t,a,m){var r=t("es5-ext/function/is-arguments"),i=t("es5-ext/string/is-string"),p=t("./array"),h=t("./string"),c=t("./valid-iterable"),o=t("es6-symbol").iterator;a.exports=function(n){return typeof c(n)[o]=="function"?n[o]():r(n)?new p(n):i(n)?new h(n):new p(n)}},{"./array":61,"./string":66,"./valid-iterable":67,"es5-ext/function/is-arguments":26,"es5-ext/string/is-string":60,"es6-symbol":74}],64:[function(t,a,m){var r=t("es5-ext/array/#/clear"),i=t("es5-ext/object/assign"),p=t("es5-ext/object/valid-callable"),h=t("es5-ext/object/valid-value"),c=t("d"),o=t("d/auto-bind"),n=t("es6-symbol"),d=Object.defineProperty,g=Object.defineProperties,E;a.exports=E=function(u,_){if(!(this instanceof E))throw new TypeError("Constructor requires 'new'");g(this,{__list__:c("w",h(u)),__context__:c("w",_),__nextIndex__:c("w",0)}),_&&(p(_.on),_.on("_add",this._onAdd),_.on("_delete",this._onDelete),_.on("_clear",this._onClear))},delete E.prototype.constructor,g(E.prototype,i({_next:c(function(){var u;if(this.__list__){if(this.__redo__&&(u=this.__redo__.shift(),u!==void 0))return u;if(this.__nextIndex__<this.__list__.length)return this.__nextIndex__++;this._unBind()}}),next:c(function(){return this._createResult(this._next())}),_createResult:c(function(u){return u===void 0?{done:!0,value:void 0}:{done:!1,value:this._resolve(u)}}),_resolve:c(function(u){return this.__list__[u]}),_unBind:c(function(){this.__list__=null,delete this.__redo__,this.__context__&&(this.__context__.off("_add",this._onAdd),this.__context__.off("_delete",this._onDelete),this.__context__.off("_clear",this._onClear),this.__context__=null)}),toString:c(function(){return"[object "+(this[n.toStringTag]||"Object")+"]"})},o({_onAdd:c(function(u){if(!(u>=this.__nextIndex__)){if(++this.__nextIndex__,!this.__redo__){d(this,"__redo__",c("c",[u]));return}this.__redo__.forEach(function(_,C){_>=u&&(this.__redo__[C]=++_)},this),this.__redo__.push(u)}}),_onDelete:c(function(u){var _;u>=this.__nextIndex__||(--this.__nextIndex__,this.__redo__&&(_=this.__redo__.indexOf(u),_!==-1&&this.__redo__.splice(_,1),this.__redo__.forEach(function(C,y){C>u&&(this.__redo__[y]=--C)},this)))}),_onClear:c(function(){this.__redo__&&r.call(this.__redo__),this.__nextIndex__=0})}))),d(E.prototype,n.iterator,c(function(){return this}))},{d:15,"d/auto-bind":14,"es5-ext/array/#/clear":21,"es5-ext/object/assign":38,"es5-ext/object/valid-callable":55,"es5-ext/object/valid-value":56,"es6-symbol":74}],65:[function(t,a,m){var r=t("es5-ext/function/is-arguments"),i=t("es5-ext/object/is-value"),p=t("es5-ext/string/is-string"),h=t("es6-symbol").iterator,c=Array.isArray;a.exports=function(o){return i(o)?c(o)||p(o)||r(o)?!0:typeof o[h]=="function":!1}},{"es5-ext/function/is-arguments":26,"es5-ext/object/is-value":45,"es5-ext/string/is-string":60,"es6-symbol":74}],66:[function(t,a,m){var r=t("es5-ext/object/set-prototype-of"),i=t("d"),p=t("es6-symbol"),h=t("./"),c=Object.defineProperty,o;o=a.exports=function(n){if(!(this instanceof o))throw new TypeError("Constructor requires 'new'");n=String(n),h.call(this,n),c(this,"__length__",i("",n.length))},r&&r(o,h),delete o.prototype.constructor,o.prototype=Object.create(h.prototype,{_next:i(function(){if(this.__list__){if(this.__nextIndex__<this.__length__)return this.__nextIndex__++;this._unBind()}}),_resolve:i(function(n){var d=this.__list__[n],g;return this.__nextIndex__===this.__length__?d:(g=d.charCodeAt(0),g>=55296&&g<=56319?d+this.__list__[this.__nextIndex__++]:d)})}),c(o.prototype,p.toStringTag,i("c","String Iterator"))},{"./":64,d:15,"es5-ext/object/set-prototype-of":52,"es6-symbol":74}],67:[function(t,a,m){var r=t("./is-iterable");a.exports=function(i){if(!r(i))throw new TypeError(i+" is not iterable");return i}},{"./is-iterable":65}],68:[function(t,a,m){a.exports=t("./is-implemented")()?Map:t("./polyfill")},{"./is-implemented":69,"./polyfill":73}],69:[function(t,a,m){a.exports=function(){var r,i,p;if(typeof Map!="function")return!1;try{r=new Map([["raz","one"],["dwa","two"],["trzy","three"]])}catch(h){return!1}return!(String(r)!=="[object Map]"||r.size!==3||typeof r.clear!="function"||typeof r.delete!="function"||typeof r.entries!="function"||typeof r.forEach!="function"||typeof r.get!="function"||typeof r.has!="function"||typeof r.keys!="function"||typeof r.set!="function"||typeof r.values!="function"||(i=r.entries(),p=i.next(),p.done!==!1)||!p.value||p.value[0]!=="raz"||p.value[1]!=="one")}},{}],70:[function(t,a,m){a.exports=function(){return typeof Map=="undefined"?!1:Object.prototype.toString.call(new Map)==="[object Map]"}()},{}],71:[function(t,a,m){a.exports=t("es5-ext/object/primitive-set")("key","value","key+value")},{"es5-ext/object/primitive-set":51}],72:[function(t,a,m){var r=t("es5-ext/object/set-prototype-of"),i=t("d"),p=t("es6-iterator"),h=t("es6-symbol").toStringTag,c=t("./iterator-kinds"),o=Object.defineProperties,n=p.prototype._unBind,d;d=a.exports=function(g,E){if(!(this instanceof d))return new d(g,E);p.call(this,g.__mapKeysData__,g),(!E||!c[E])&&(E="key+value"),o(this,{__kind__:i("",E),__values__:i("w",g.__mapValuesData__)})},r&&r(d,p),d.prototype=Object.create(p.prototype,{constructor:i(d),_resolve:i(function(g){return this.__kind__==="value"?this.__values__[g]:this.__kind__==="key"?this.__list__[g]:[this.__list__[g],this.__values__[g]]}),_unBind:i(function(){this.__values__=null,n.call(this)}),toString:i(function(){return"[object Map Iterator]"})}),Object.defineProperty(d.prototype,h,i("c","Map Iterator"))},{"./iterator-kinds":71,d:15,"es5-ext/object/set-prototype-of":52,"es6-iterator":64,"es6-symbol":74}],73:[function(t,a,m){var r=t("es5-ext/array/#/clear"),i=t("es5-ext/array/#/e-index-of"),p=t("es5-ext/object/set-prototype-of"),h=t("es5-ext/object/valid-callable"),c=t("es5-ext/object/valid-value"),o=t("d"),n=t("event-emitter"),d=t("es6-symbol"),g=t("es6-iterator/valid-iterable"),E=t("es6-iterator/for-of"),u=t("./lib/iterator"),_=t("./is-native-implemented"),C=Function.prototype.call,y=Object.defineProperties,P=Object.getPrototypeOf,M;a.exports=M=function(){var k=arguments[0],O,U,Y;if(!(this instanceof M))throw new TypeError("Constructor requires 'new'");return _&&p&&Map!==M?Y=p(new Map,P(this)):Y=this,k!=null&&g(k),y(Y,{__mapKeysData__:o("c",O=[]),__mapValuesData__:o("c",U=[])}),k&&E(k,function(Z){var ee=c(Z)[0];Z=Z[1],i.call(O,ee)===-1&&(O.push(ee),U.push(Z))},Y),Y},_&&(p&&p(M,Map),M.prototype=Object.create(Map.prototype,{constructor:o(M)})),n(y(M.prototype,{clear:o(function(){this.__mapKeysData__.length&&(r.call(this.__mapKeysData__),r.call(this.__mapValuesData__),this.emit("_clear"))}),delete:o(function(k){var O=i.call(this.__mapKeysData__,k);return O===-1?!1:(this.__mapKeysData__.splice(O,1),this.__mapValuesData__.splice(O,1),this.emit("_delete",O,k),!0)}),entries:o(function(){return new u(this,"key+value")}),forEach:o(function(k){var O=arguments[1],U,Y;for(h(k),U=this.entries(),Y=U._next();Y!==void 0;)C.call(k,O,this.__mapValuesData__[Y],this.__mapKeysData__[Y],this),Y=U._next()}),get:o(function(k){var O=i.call(this.__mapKeysData__,k);if(O!==-1)return this.__mapValuesData__[O]}),has:o(function(k){return i.call(this.__mapKeysData__,k)!==-1}),keys:o(function(){return new u(this,"key")}),set:o(function(k,O){var U=i.call(this.__mapKeysData__,k),Y;return U===-1&&(U=this.__mapKeysData__.push(k)-1,Y=!0),this.__mapValuesData__[U]=O,Y&&this.emit("_add",U,k),this}),size:o.gs(function(){return this.__mapKeysData__.length}),values:o(function(){return new u(this,"value")}),toString:o(function(){return"[object Map]"})})),Object.defineProperty(M.prototype,d.iterator,o(function(){return this.entries()})),Object.defineProperty(M.prototype,d.toStringTag,o("c","Map"))},{"./is-native-implemented":70,"./lib/iterator":72,d:15,"es5-ext/array/#/clear":21,"es5-ext/array/#/e-index-of":22,"es5-ext/object/set-prototype-of":52,"es5-ext/object/valid-callable":55,"es5-ext/object/valid-value":56,"es6-iterator/for-of":62,"es6-iterator/valid-iterable":67,"es6-symbol":74,"event-emitter":82}],74:[function(t,a,m){a.exports=t("./is-implemented")()?t("ext/global-this").Symbol:t("./polyfill")},{"./is-implemented":75,"./polyfill":80,"ext/global-this":85}],75:[function(t,a,m){var r=t("ext/global-this"),i={object:!0,symbol:!0};a.exports=function(){var p=r.Symbol,h;if(typeof p!="function")return!1;h=p("test symbol");try{String(h)}catch(c){return!1}return!(!i[typeof p.iterator]||!i[typeof p.toPrimitive]||!i[typeof p.toStringTag])}},{"ext/global-this":85}],76:[function(t,a,m){a.exports=function(r){return r?typeof r=="symbol"?!0:!r.constructor||r.constructor.name!=="Symbol"?!1:r[r.constructor.toStringTag]==="Symbol":!1}},{}],77:[function(t,a,m){var r=t("d"),i=Object.create,p=Object.defineProperty,h=Object.prototype,c=i(null);a.exports=function(o){for(var n=0,d,g;c[o+(n||"")];)++n;return o+=n||"",c[o]=!0,d="@@"+o,p(h,d,r.gs(null,function(E){g||(g=!0,p(this,d,r(E)),g=!1)})),d}},{d:15}],78:[function(t,a,m){var r=t("d"),i=t("ext/global-this").Symbol;a.exports=function(p){return Object.defineProperties(p,{hasInstance:r("",i&&i.hasInstance||p("hasInstance")),isConcatSpreadable:r("",i&&i.isConcatSpreadable||p("isConcatSpreadable")),iterator:r("",i&&i.iterator||p("iterator")),match:r("",i&&i.match||p("match")),replace:r("",i&&i.replace||p("replace")),search:r("",i&&i.search||p("search")),species:r("",i&&i.species||p("species")),split:r("",i&&i.split||p("split")),toPrimitive:r("",i&&i.toPrimitive||p("toPrimitive")),toStringTag:r("",i&&i.toStringTag||p("toStringTag")),unscopables:r("",i&&i.unscopables||p("unscopables"))})}},{d:15,"ext/global-this":85}],79:[function(t,a,m){var r=t("d"),i=t("../../../validate-symbol"),p=Object.create(null);a.exports=function(h){return Object.defineProperties(h,{for:r(function(c){return p[c]?p[c]:p[c]=h(String(c))}),keyFor:r(function(c){var o;i(c);for(o in p)if(p[o]===c)return o})})}},{"../../../validate-symbol":81,d:15}],80:[function(t,a,m){var r=t("d"),i=t("./validate-symbol"),p=t("ext/global-this").Symbol,h=t("./lib/private/generate-name"),c=t("./lib/private/setup/standard-symbols"),o=t("./lib/private/setup/symbol-registry"),n=Object.create,d=Object.defineProperties,g=Object.defineProperty,E,u,_;if(typeof p=="function")try{String(p()),_=!0}catch(C){}else p=null;u=function(y){if(this instanceof u)throw new TypeError("Symbol is not a constructor");return E(y)},a.exports=E=function C(y){var P;if(this instanceof C)throw new TypeError("Symbol is not a constructor");return _?p(y):(P=n(u.prototype),y=y===void 0?"":String(y),d(P,{__description__:r("",y),__name__:r("",h(y))}))},c(E),o(E),d(u.prototype,{constructor:r(E),toString:r("",function(){return this.__name__})}),d(E.prototype,{toString:r(function(){return"Symbol ("+i(this).__description__+")"}),valueOf:r(function(){return i(this)})}),g(E.prototype,E.toPrimitive,r("",function(){var C=i(this);return typeof C=="symbol"?C:C.toString()})),g(E.prototype,E.toStringTag,r("c","Symbol")),g(u.prototype,E.toStringTag,r("c",E.prototype[E.toStringTag])),g(u.prototype,E.toPrimitive,r("c",E.prototype[E.toPrimitive]))},{"./lib/private/generate-name":77,"./lib/private/setup/standard-symbols":78,"./lib/private/setup/symbol-registry":79,"./validate-symbol":81,d:15,"ext/global-this":85}],81:[function(t,a,m){var r=t("./is-symbol");a.exports=function(i){if(!r(i))throw new TypeError(i+" is not a symbol");return i}},{"./is-symbol":76}],82:[function(t,a,m){var r=t("d"),i=t("es5-ext/object/valid-callable"),p=Function.prototype.apply,h=Function.prototype.call,c=Object.create,o=Object.defineProperty,n=Object.defineProperties,d=Object.prototype.hasOwnProperty,g={configurable:!0,enumerable:!1,writable:!0},E,u,_,C,y,P,M;E=function(k,O){var U;return i(O),d.call(this,"__ee__")?U=this.__ee__:(U=g.value=c(null),o(this,"__ee__",g),g.value=null),U[k]?typeof U[k]=="object"?U[k].push(O):U[k]=[U[k],O]:U[k]=O,this},u=function(k,O){var U,Y;return i(O),Y=this,E.call(this,k,U=function(){_.call(Y,k,U),p.call(O,this,arguments)}),U.__eeOnceListener__=O,this},_=function(k,O){var U,Y,Z,ee;if(i(O),!d.call(this,"__ee__"))return this;if(U=this.__ee__,!U[k])return this;if(Y=U[k],typeof Y=="object")for(ee=0;Z=Y[ee];++ee)(Z===O||Z.__eeOnceListener__===O)&&(Y.length===2?U[k]=Y[ee?0:1]:Y.splice(ee,1));else(Y===O||Y.__eeOnceListener__===O)&&delete U[k];return this},C=function(k){var O,U,Y,Z,ee;if(d.call(this,"__ee__")&&(Z=this.__ee__[k],!!Z))if(typeof Z=="object"){for(U=arguments.length,ee=new Array(U-1),O=1;O<U;++O)ee[O-1]=arguments[O];for(Z=Z.slice(),O=0;Y=Z[O];++O)p.call(Y,this,ee)}else switch(arguments.length){case 1:h.call(Z,this);break;case 2:h.call(Z,this,arguments[1]);break;case 3:h.call(Z,this,arguments[1],arguments[2]);break;default:for(U=arguments.length,ee=new Array(U-1),O=1;O<U;++O)ee[O-1]=arguments[O];p.call(Z,this,ee)}},y={on:E,once:u,off:_,emit:C},P={on:r(E),once:r(u),off:r(_),emit:r(C)},M=n({},P),a.exports=m=function(k){return k==null?c(M):n(Object(k),P)},m.methods=y},{d:15,"es5-ext/object/valid-callable":55}],83:[function(t,a,m){var r=Object.create||ee,i=Object.keys||A,p=Function.prototype.bind||j;function h(){(!this._events||!Object.prototype.hasOwnProperty.call(this,"_events"))&&(this._events=r(null),this._eventsCount=0),this._maxListeners=this._maxListeners||void 0}a.exports=h,h.EventEmitter=h,h.prototype._events=void 0,h.prototype._maxListeners=void 0;var c=10,o;try{var n={};Object.defineProperty&&Object.defineProperty(n,"x",{value:0}),o=n.x===0}catch(b){o=!1}o?Object.defineProperty(h,"defaultMaxListeners",{enumerable:!0,get:function(){return c},set:function(b){if(typeof b!="number"||b<0||b!==b)throw new TypeError('"defaultMaxListeners" must be a positive number');c=b}}):h.defaultMaxListeners=c,h.prototype.setMaxListeners=function(N){if(typeof N!="number"||N<0||isNaN(N))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=N,this};function d(b){return b._maxListeners===void 0?h.defaultMaxListeners:b._maxListeners}h.prototype.getMaxListeners=function(){return d(this)};function g(b,N,z){if(N)b.call(z);else for(var q=b.length,G=Y(b,q),D=0;D<q;++D)G[D].call(z)}function E(b,N,z,q){if(N)b.call(z,q);else for(var G=b.length,D=Y(b,G),te=0;te<G;++te)D[te].call(z,q)}function u(b,N,z,q,G){if(N)b.call(z,q,G);else for(var D=b.length,te=Y(b,D),le=0;le<D;++le)te[le].call(z,q,G)}function _(b,N,z,q,G,D){if(N)b.call(z,q,G,D);else for(var te=b.length,le=Y(b,te),ce=0;ce<te;++ce)le[ce].call(z,q,G,D)}function C(b,N,z,q){if(N)b.apply(z,q);else for(var G=b.length,D=Y(b,G),te=0;te<G;++te)D[te].apply(z,q)}h.prototype.emit=function(N){var z,q,G,D,te,le,ce=N==="error";if(le=this._events,le)ce=ce&&le.error==null;else if(!ce)return!1;if(ce){if(arguments.length>1&&(z=arguments[1]),z instanceof Error)throw z;var pe=new Error('Unhandled "error" event. ('+z+")");throw pe.context=z,pe}if(q=le[N],!q)return!1;var Q=typeof q=="function";switch(G=arguments.length,G){case 1:g(q,Q,this);break;case 2:E(q,Q,this,arguments[1]);break;case 3:u(q,Q,this,arguments[1],arguments[2]);break;case 4:_(q,Q,this,arguments[1],arguments[2],arguments[3]);break;default:for(D=new Array(G-1),te=1;te<G;te++)D[te-1]=arguments[te];C(q,Q,this,D)}return!0};function y(b,N,z,q){var G,D,te;if(typeof z!="function")throw new TypeError('"listener" argument must be a function');if(D=b._events,D?(D.newListener&&(b.emit("newListener",N,z.listener?z.listener:z),D=b._events),te=D[N]):(D=b._events=r(null),b._eventsCount=0),!te)te=D[N]=z,++b._eventsCount;else if(typeof te=="function"?te=D[N]=q?[z,te]:[te,z]:q?te.unshift(z):te.push(z),!te.warned&&(G=d(b),G&&G>0&&te.length>G)){te.warned=!0;var le=new Error("Possible EventEmitter memory leak detected. "+te.length+' "'+String(N)+'" listeners added. Use emitter.setMaxListeners() to increase limit.');le.name="MaxListenersExceededWarning",le.emitter=b,le.type=N,le.count=te.length,typeof console=="object"&&console.warn&&fe("warn","at node_modules/mqtt/dist/mqtt.js:7327","%s: %s",le.name,le.message)}return b}h.prototype.addListener=function(N,z){return y(this,N,z,!1)},h.prototype.on=h.prototype.addListener,h.prototype.prependListener=function(N,z){return y(this,N,z,!0)};function P(){if(!this.fired)switch(this.target.removeListener(this.type,this.wrapFn),this.fired=!0,arguments.length){case 0:return this.listener.call(this.target);case 1:return this.listener.call(this.target,arguments[0]);case 2:return this.listener.call(this.target,arguments[0],arguments[1]);case 3:return this.listener.call(this.target,arguments[0],arguments[1],arguments[2]);default:for(var b=new Array(arguments.length),N=0;N<b.length;++N)b[N]=arguments[N];this.listener.apply(this.target,b)}}function M(b,N,z){var q={fired:!1,wrapFn:void 0,target:b,type:N,listener:z},G=p.call(P,q);return G.listener=z,q.wrapFn=G,G}h.prototype.once=function(N,z){if(typeof z!="function")throw new TypeError('"listener" argument must be a function');return this.on(N,M(this,N,z)),this},h.prototype.prependOnceListener=function(N,z){if(typeof z!="function")throw new TypeError('"listener" argument must be a function');return this.prependListener(N,M(this,N,z)),this},h.prototype.removeListener=function(N,z){var q,G,D,te,le;if(typeof z!="function")throw new TypeError('"listener" argument must be a function');if(G=this._events,!G)return this;if(q=G[N],!q)return this;if(q===z||q.listener===z)--this._eventsCount===0?this._events=r(null):(delete G[N],G.removeListener&&this.emit("removeListener",N,q.listener||z));else if(typeof q!="function"){for(D=-1,te=q.length-1;te>=0;te--)if(q[te]===z||q[te].listener===z){le=q[te].listener,D=te;break}if(D<0)return this;D===0?q.shift():U(q,D),q.length===1&&(G[N]=q[0]),G.removeListener&&this.emit("removeListener",N,le||z)}return this},h.prototype.removeAllListeners=function(N){var z,q,G;if(q=this._events,!q)return this;if(!q.removeListener)return arguments.length===0?(this._events=r(null),this._eventsCount=0):q[N]&&(--this._eventsCount===0?this._events=r(null):delete q[N]),this;if(arguments.length===0){var D=i(q),te;for(G=0;G<D.length;++G)te=D[G],te!=="removeListener"&&this.removeAllListeners(te);return this.removeAllListeners("removeListener"),this._events=r(null),this._eventsCount=0,this}if(z=q[N],typeof z=="function")this.removeListener(N,z);else if(z)for(G=z.length-1;G>=0;G--)this.removeListener(N,z[G]);return this};function k(b,N,z){var q=b._events;if(!q)return[];var G=q[N];return G?typeof G=="function"?z?[G.listener||G]:[G]:z?Z(G):Y(G,G.length):[]}h.prototype.listeners=function(N){return k(this,N,!0)},h.prototype.rawListeners=function(N){return k(this,N,!1)},h.listenerCount=function(b,N){return typeof b.listenerCount=="function"?b.listenerCount(N):O.call(b,N)},h.prototype.listenerCount=O;function O(b){var N=this._events;if(N){var z=N[b];if(typeof z=="function")return 1;if(z)return z.length}return 0}h.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};function U(b,N){for(var z=N,q=z+1,G=b.length;q<G;z+=1,q+=1)b[z]=b[q];b.pop()}function Y(b,N){for(var z=new Array(N),q=0;q<N;++q)z[q]=b[q];return z}function Z(b){for(var N=new Array(b.length),z=0;z<N.length;++z)N[z]=b[z].listener||b[z];return N}function ee(b){var N=function(){};return N.prototype=b,new N}function A(b){for(var N in b)Object.prototype.hasOwnProperty.call(b,N);return N}function j(b){var N=this;return function(){return N.apply(b,arguments)}}},{}],84:[function(t,a,m){var r=function(){if(typeof self=="object"&&self)return self;if(typeof window=="object"&&window)return window;throw new Error("Unable to resolve global `this`")};a.exports=function(){if(this)return this;try{Object.defineProperty(Object.prototype,"__global__",{get:function(){return this},configurable:!0})}catch(i){return r()}try{return __global__||r()}finally{delete Object.prototype.__global__}}()},{}],85:[function(t,a,m){a.exports=t("./is-implemented")()?globalThis:t("./implementation")},{"./implementation":84,"./is-implemented":86}],86:[function(t,a,m){a.exports=function(){return typeof globalThis!="object"||!globalThis?!1:globalThis.Array===Array}},{}],87:[function(t,a,m){m.read=function(r,i,p,h,c){var o,n,d=c*8-h-1,g=(1<<d)-1,E=g>>1,u=-7,_=p?c-1:0,C=p?-1:1,y=r[i+_];for(_+=C,o=y&(1<<-u)-1,y>>=-u,u+=d;u>0;o=o*256+r[i+_],_+=C,u-=8);for(n=o&(1<<-u)-1,o>>=-u,u+=h;u>0;n=n*256+r[i+_],_+=C,u-=8);if(o===0)o=1-E;else{if(o===g)return n?NaN:(y?-1:1)*(1/0);n=n+Math.pow(2,h),o=o-E}return(y?-1:1)*n*Math.pow(2,o-h)},m.write=function(r,i,p,h,c,o){var n,d,g,E=o*8-c-1,u=(1<<E)-1,_=u>>1,C=c===23?Math.pow(2,-24)-Math.pow(2,-77):0,y=h?0:o-1,P=h?1:-1,M=i<0||i===0&&1/i<0?1:0;for(i=Math.abs(i),isNaN(i)||i===1/0?(d=isNaN(i)?1:0,n=u):(n=Math.floor(Math.log(i)/Math.LN2),i*(g=Math.pow(2,-n))<1&&(n--,g*=2),n+_>=1?i+=C/g:i+=C*Math.pow(2,1-_),i*g>=2&&(n++,g/=2),n+_>=u?(d=0,n=u):n+_>=1?(d=(i*g-1)*Math.pow(2,c),n=n+_):(d=i*Math.pow(2,_-1)*Math.pow(2,c),n=0));c>=8;r[p+y]=d&255,y+=P,d/=256,c-=8);for(n=n<<c|d,E+=c;E>0;r[p+y]=n&255,y+=P,n/=256,E-=8);r[p+y-P]|=M*128}},{}],88:[function(t,a,m){typeof Object.create=="function"?a.exports=function(i,p){i.super_=p,i.prototype=Object.create(p.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}})}:a.exports=function(i,p){i.super_=p;var h=function(){};h.prototype=p.prototype,i.prototype=new h,i.prototype.constructor=i}},{}],89:[function(t,a,m){a.exports=function(p){return p!=null&&(r(p)||i(p)||!!p._isBuffer)};function r(p){return!!p.constructor&&typeof p.constructor.isBuffer=="function"&&p.constructor.isBuffer(p)}function i(p){return typeof p.readFloatLE=="function"&&typeof p.slice=="function"&&r(p.slice(0,0))}},{}],90:[function(t,a,m){var r=t("safe-buffer").Buffer,i=a.exports;i.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"},i.codes={};for(var p in i.types){var h=i.types[p];i.codes[h]=p}i.CMD_SHIFT=4,i.CMD_MASK=240,i.DUP_MASK=8,i.QOS_MASK=3,i.QOS_SHIFT=1,i.RETAIN_MASK=1,i.LENGTH_MASK=127,i.LENGTH_FIN_MASK=128,i.SESSIONPRESENT_MASK=1,i.SESSIONPRESENT_HEADER=r.from([i.SESSIONPRESENT_MASK]),i.CONNACK_HEADER=r.from([i.codes.connack<<i.CMD_SHIFT]),i.USERNAME_MASK=128,i.PASSWORD_MASK=64,i.WILL_RETAIN_MASK=32,i.WILL_QOS_MASK=24,i.WILL_QOS_SHIFT=3,i.WILL_FLAG_MASK=4,i.CLEAN_SESSION_MASK=2,i.CONNECT_HEADER=r.from([i.codes.connect<<i.CMD_SHIFT]),i.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11},i.propertiesCodes={};for(var c in i.properties){var o=i.properties[c];i.propertiesCodes[o]=c}i.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int32",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"};function n(d){return[0,1,2].map(function(g){return[0,1].map(function(E){return[0,1].map(function(u){var _=new r(1);return _.writeUInt8(i.codes[d]<<i.CMD_SHIFT|(E?i.DUP_MASK:0)|g<<i.QOS_SHIFT|u,0,!0),_})})})}i.PUBLISH_HEADER=n("publish"),i.SUBSCRIBE_HEADER=n("subscribe"),i.SUBSCRIBE_OPTIONS_QOS_MASK=3,i.SUBSCRIBE_OPTIONS_NL_MASK=1,i.SUBSCRIBE_OPTIONS_NL_SHIFT=2,i.SUBSCRIBE_OPTIONS_RAP_MASK=1,i.SUBSCRIBE_OPTIONS_RAP_SHIFT=3,i.SUBSCRIBE_OPTIONS_RH_MASK=3,i.SUBSCRIBE_OPTIONS_RH_SHIFT=4,i.SUBSCRIBE_OPTIONS_RH=[0,16,32],i.SUBSCRIBE_OPTIONS_NL=4,i.SUBSCRIBE_OPTIONS_RAP=8,i.SUBSCRIBE_OPTIONS_QOS=[0,1,2],i.UNSUBSCRIBE_HEADER=n("unsubscribe"),i.ACKS={unsuback:n("unsuback"),puback:n("puback"),pubcomp:n("pubcomp"),pubrel:n("pubrel"),pubrec:n("pubrec")},i.SUBACK_HEADER=r.from([i.codes.suback<<i.CMD_SHIFT]),i.VERSION3=r.from([3]),i.VERSION4=r.from([4]),i.VERSION5=r.from([5]),i.QOS=[0,1,2].map(function(d){return r.from([d])}),i.EMPTY={pingreq:r.from([i.codes.pingreq<<4,0]),pingresp:r.from([i.codes.pingresp<<4,0]),disconnect:r.from([i.codes.disconnect<<4,0])}},{"safe-buffer":118}],91:[function(t,a,m){var r=t("safe-buffer").Buffer,i=t("./writeToStream"),p=t("events").EventEmitter,h=t("inherits");function c(n,d){var g=new o;return i(n,g,d),g.concat()}function o(){this._array=new Array(20),this._i=0}h(o,p),o.prototype.write=function(n){return this._array[this._i++]=n,!0},o.prototype.concat=function(){var n=0,d=new Array(this._array.length),g=this._array,E=0,u,_;for(u=0;u<g.length&&g[u]!==void 0;u++)typeof g[u]!="string"?d[u]=g[u].length:d[u]=r.byteLength(g[u]),n+=d[u];for(_=r.allocUnsafe(n),u=0;u<g.length&&g[u]!==void 0;u++)typeof g[u]!="string"?(g[u].copy(_,E),E+=d[u]):(_.write(g[u],E),E+=d[u]);return _},a.exports=c},{"./writeToStream":97,events:83,inherits:88,"safe-buffer":118}],92:[function(t,a,m){m.parser=t("./parser"),m.generate=t("./generate"),m.writeToStream=t("./writeToStream")},{"./generate":91,"./parser":96,"./writeToStream":97}],93:[function(t,a,m){var r=t("readable-stream/duplex"),i=t("util"),p=t("safe-buffer").Buffer;function h(c){if(!(this instanceof h))return new h(c);if(this._bufs=[],this.length=0,typeof c=="function"){this._callback=c;var o=function(d){this._callback&&(this._callback(d),this._callback=null)}.bind(this);this.on("pipe",function(d){d.on("error",o)}),this.on("unpipe",function(d){d.removeListener("error",o)})}else this.append(c);r.call(this)}i.inherits(h,r),h.prototype._offset=function(o){var n=0,d=0,g;if(o===0)return[0,0];for(;d<this._bufs.length;d++){if(g=n+this._bufs[d].length,o<g||d==this._bufs.length-1)return[d,o-n];n=g}},h.prototype.append=function(o){var n=0;if(p.isBuffer(o))this._appendBuffer(o);else if(Array.isArray(o))for(;n<o.length;n++)this.append(o[n]);else if(o instanceof h)for(;n<o._bufs.length;n++)this.append(o._bufs[n]);else o!=null&&(typeof o=="number"&&(o=o.toString()),this._appendBuffer(p.from(o)));return this},h.prototype._appendBuffer=function(o){this._bufs.push(o),this.length+=o.length},h.prototype._write=function(o,n,d){this._appendBuffer(o),typeof d=="function"&&d()},h.prototype._read=function(o){if(!this.length)return this.push(null);o=Math.min(o,this.length),this.push(this.slice(0,o)),this.consume(o)},h.prototype.end=function(o){r.prototype.end.call(this,o),this._callback&&(this._callback(null,this.slice()),this._callback=null)},h.prototype.get=function(o){return this.slice(o,o+1)[0]},h.prototype.slice=function(o,n){return typeof o=="number"&&o<0&&(o+=this.length),typeof n=="number"&&n<0&&(n+=this.length),this.copy(null,0,o,n)},h.prototype.copy=function(o,n,d,g){if((typeof d!="number"||d<0)&&(d=0),(typeof g!="number"||g>this.length)&&(g=this.length),d>=this.length||g<=0)return o||p.alloc(0);var E=!!o,u=this._offset(d),_=g-d,C=_,y=E&&n||0,P=u[1],M,k;if(d===0&&g==this.length){if(!E)return this._bufs.length===1?this._bufs[0]:p.concat(this._bufs,this.length);for(k=0;k<this._bufs.length;k++)this._bufs[k].copy(o,y),y+=this._bufs[k].length;return o}if(C<=this._bufs[u[0]].length-P)return E?this._bufs[u[0]].copy(o,n,P,P+C):this._bufs[u[0]].slice(P,P+C);for(E||(o=p.allocUnsafe(_)),k=u[0];k<this._bufs.length;k++){if(M=this._bufs[k].length-P,C>M)this._bufs[k].copy(o,y,P);else{this._bufs[k].copy(o,y,P,P+C);break}y+=M,C-=M,P&&(P=0)}return o},h.prototype.shallowSlice=function(o,n){o=o||0,n=n||this.length,o<0&&(o+=this.length),n<0&&(n+=this.length);var d=this._offset(o),g=this._offset(n),E=this._bufs.slice(d[0],g[0]+1);return g[1]==0?E.pop():E[E.length-1]=E[E.length-1].slice(0,g[1]),d[1]!=0&&(E[0]=E[0].slice(d[1])),new h(E)},h.prototype.toString=function(o,n,d){return this.slice(n,d).toString(o)},h.prototype.consume=function(o){for(;this._bufs.length;)if(o>=this._bufs[0].length)o-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift();else{this._bufs[0]=this._bufs[0].slice(o),this.length-=o;break}return this},h.prototype.duplicate=function(){for(var o=0,n=new h;o<this._bufs.length;o++)n.append(this._bufs[o]);return n},h.prototype.destroy=function(){this._bufs.length=0,this.length=0,this.push(null)},function(){var c={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1};for(var o in c)(function(n){h.prototype[n]=function(d){return this.slice(d,d+c[n])[n](0)}})(o)}(),a.exports=h},{"readable-stream/duplex":105,"safe-buffer":118,util:136}],94:[function(t,a,m){var r=t("safe-buffer").Buffer,i=65536,p={};function h(g){var E=r.allocUnsafe(2);return E.writeUInt8(g>>8,0),E.writeUInt8(g&255,1),E}function c(){for(var g=0;g<i;g++)p[g]=h(g)}function o(g){return g>=0&&g<128?1:g>=128&&g<16384?2:g>=16384&&g<2097152?3:g>=2097152&&g<268435456?4:0}function n(g){var E=0,u=0,_=o(g),C=r.allocUnsafe(_);do E=g%128|0,g=g/128|0,g>0&&(E=E|128),C.writeUInt8(E,u++);while(g>0);return{data:C,length:_}}function d(g){var E=r.allocUnsafe(4);return E.writeUInt32BE(g,0),E}a.exports={cache:p,generateCache:c,generateNumber:h,genBufVariableByteInt:n,generate4ByteBuffer:d}},{"safe-buffer":118}],95:[function(t,a,m){function r(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}a.exports=r},{}],96:[function(t,a,m){var r=t("bl"),i=t("inherits"),p=t("events").EventEmitter,h=t("./packet"),c=t("./constants");function o(n){if(!(this instanceof o))return new o(n);this.settings=n||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState()}i(o,p),o.prototype._resetState=function(){this.packet=new h,this.error=null,this._list=r(),this._stateCounter=0},o.prototype.parse=function(n){for(this.error&&this._resetState(),this._list.append(n);(this.packet.length!==-1||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error;)this._stateCounter++,this._stateCounter>=this._states.length&&(this._stateCounter=0);return this._list.length},o.prototype._parseHeader=function(){var n=this._list.readUInt8(0);return this.packet.cmd=c.types[n>>c.CMD_SHIFT],this.packet.retain=(n&c.RETAIN_MASK)!==0,this.packet.qos=n>>c.QOS_SHIFT&c.QOS_MASK,this.packet.dup=(n&c.DUP_MASK)!==0,this._list.consume(1),!0},o.prototype._parseLength=function(){var n=this._parseVarByteNum(!0);return n&&(this.packet.length=n.value,this._list.consume(n.bytes)),!!n},o.prototype._parsePayload=function(){var n=!1;if(this.packet.length===0||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}n=!0}return n},o.prototype._parseConnect=function(){var n,d,g,E,u,_,C={},y=this.packet;if(n=this._parseString(),n===null)return this._emitError(new Error("Cannot parse protocolId"));if(n!=="MQTT"&&n!=="MQIsdp")return this._emitError(new Error("Invalid protocolId"));if(y.protocolId=n,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(y.protocolVersion=this._list.readUInt8(this._pos),y.protocolVersion!==3&&y.protocolVersion!==4&&y.protocolVersion!==5)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(C.username=this._list.readUInt8(this._pos)&c.USERNAME_MASK,C.password=this._list.readUInt8(this._pos)&c.PASSWORD_MASK,C.will=this._list.readUInt8(this._pos)&c.WILL_FLAG_MASK,C.will&&(y.will={},y.will.retain=(this._list.readUInt8(this._pos)&c.WILL_RETAIN_MASK)!==0,y.will.qos=(this._list.readUInt8(this._pos)&c.WILL_QOS_MASK)>>c.WILL_QOS_SHIFT),y.clean=(this._list.readUInt8(this._pos)&c.CLEAN_SESSION_MASK)!==0,this._pos++,y.keepalive=this._parseNum(),y.keepalive===-1)return this._emitError(new Error("Packet too short"));if(y.protocolVersion===5){var P=this._parseProperties();Object.getOwnPropertyNames(P).length&&(y.properties=P)}if(d=this._parseString(),d===null)return this._emitError(new Error("Packet too short"));if(y.clientId=d,C.will){if(y.protocolVersion===5){var M=this._parseProperties();Object.getOwnPropertyNames(M).length&&(y.will.properties=M)}if(g=this._parseString(),g===null)return this._emitError(new Error("Cannot parse will topic"));if(y.will.topic=g,E=this._parseBuffer(),E===null)return this._emitError(new Error("Cannot parse will payload"));y.will.payload=E}if(C.username){if(_=this._parseString(),_===null)return this._emitError(new Error("Cannot parse username"));y.username=_}if(C.password){if(u=this._parseBuffer(),u===null)return this._emitError(new Error("Cannot parse password"));y.password=u}return this.settings=y,y},o.prototype._parseConnack=function(){var n=this.packet;if(this._list.length<2)return null;if(n.sessionPresent=!!(this._list.readUInt8(this._pos++)&c.SESSIONPRESENT_MASK),this.settings.protocolVersion===5?n.reasonCode=this._list.readUInt8(this._pos++):n.returnCode=this._list.readUInt8(this._pos++),n.returnCode===-1||n.reasonCode===-1)return this._emitError(new Error("Cannot parse return code"));if(this.settings.protocolVersion===5){var d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(n.properties=d)}},o.prototype._parsePublish=function(){var n=this.packet;if(n.topic=this._parseString(),n.topic===null)return this._emitError(new Error("Cannot parse topic"));if(!(n.qos>0&&!this._parseMessageId())){if(this.settings.protocolVersion===5){var d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(n.properties=d)}n.payload=this._list.slice(this._pos,n.length)}},o.prototype._parseSubscribe=function(){var n=this.packet,d,g,E,u,_,C,y;if(n.qos!==1)return this._emitError(new Error("Wrong subscribe header"));if(n.subscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){var P=this._parseProperties();Object.getOwnPropertyNames(P).length&&(n.properties=P)}for(;this._pos<n.length;){if(d=this._parseString(),d===null)return this._emitError(new Error("Cannot parse topic"));g=this._parseByte(),E=g&c.SUBSCRIBE_OPTIONS_QOS_MASK,C=(g>>c.SUBSCRIBE_OPTIONS_NL_SHIFT&c.SUBSCRIBE_OPTIONS_NL_MASK)!==0,_=(g>>c.SUBSCRIBE_OPTIONS_RAP_SHIFT&c.SUBSCRIBE_OPTIONS_RAP_MASK)!==0,u=g>>c.SUBSCRIBE_OPTIONS_RH_SHIFT&c.SUBSCRIBE_OPTIONS_RH_MASK,y={topic:d,qos:E},this.settings.protocolVersion===5&&(y.nl=C,y.rap=_,y.rh=u),n.subscriptions.push(y)}}},o.prototype._parseSuback=function(){var n=this.packet;if(this.packet.granted=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){var d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(n.properties=d)}for(;this._pos<this.packet.length;)this.packet.granted.push(this._list.readUInt8(this._pos++))}},o.prototype._parseUnsubscribe=function(){var n=this.packet;if(n.unsubscriptions=[],!!this._parseMessageId()){if(this.settings.protocolVersion===5){var d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(n.properties=d)}for(;this._pos<n.length;){var g;if(g=this._parseString(),g===null)return this._emitError(new Error("Cannot parse topic"));n.unsubscriptions.push(g)}}},o.prototype._parseUnsuback=function(){var n=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if(this.settings.protocolVersion===5){var d=this._parseProperties();for(Object.getOwnPropertyNames(d).length&&(n.properties=d),n.granted=[];this._pos<this.packet.length;)this.packet.granted.push(this._list.readUInt8(this._pos++))}},o.prototype._parseConfirmation=function(){var n=this.packet;if(this._parseMessageId(),this.settings.protocolVersion===5&&n.length>2){n.reasonCode=this._parseByte();var d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(n.properties=d)}return!0},o.prototype._parseDisconnect=function(){var n=this.packet;if(this.settings.protocolVersion===5){n.reasonCode=this._parseByte();var d=this._parseProperties();Object.getOwnPropertyNames(d).length&&(n.properties=d)}return!0},o.prototype._parseAuth=function(){var n=this.packet;if(this.settings.protocolVersion!==5)return this._emitError(new Error("Not supported auth packet for this version MQTT"));n.reasonCode=this._parseByte();var d=this._parseProperties();return Object.getOwnPropertyNames(d).length&&(n.properties=d),!0},o.prototype._parseMessageId=function(){var n=this.packet;return n.messageId=this._parseNum(),n.messageId===null?(this._emitError(new Error("Cannot parse messageId")),!1):!0},o.prototype._parseString=function(n){var d=this._parseNum(),g,E=d+this._pos;return d===-1||E>this._list.length||E>this.packet.length?null:(g=this._list.toString("utf8",this._pos,E),this._pos+=d,g)},o.prototype._parseStringPair=function(){return{name:this._parseString(),value:this._parseString()}},o.prototype._parseBuffer=function(){var n=this._parseNum(),d,g=n+this._pos;return n===-1||g>this._list.length||g>this.packet.length?null:(d=this._list.slice(this._pos,g),this._pos+=n,d)},o.prototype._parseNum=function(){if(this._list.length-this._pos<2)return-1;var n=this._list.readUInt16BE(this._pos);return this._pos+=2,n},o.prototype._parse4ByteNum=function(){if(this._list.length-this._pos<4)return-1;var n=this._list.readUInt32BE(this._pos);return this._pos+=4,n},o.prototype._parseVarByteNum=function(n){for(var d=0,g=1,E=0,u=!0,_,C=this._pos?this._pos:0;d<5&&(_=this._list.readUInt8(C+d++),E+=g*(_&c.LENGTH_MASK),g*=128,!!(_&c.LENGTH_FIN_MASK));)if(this._list.length<=d){u=!1;break}return C&&(this._pos+=d),u=u?n?{bytes:d,value:E}:E:!1,u},o.prototype._parseByte=function(){var n=this._list.readUInt8(this._pos);return this._pos++,n},o.prototype._parseByType=function(n){switch(n){case"byte":return this._parseByte()!==0;case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}},o.prototype._parseProperties=function(){for(var n=this._parseVarByteNum(),d=this._pos,g=d+n,E={};this._pos<g;){var u=this._parseByte(),_=c.propertiesCodes[u];if(!_)return this._emitError(new Error("Unknown property")),!1;if(_==="userProperties"){E[_]||(E[_]={});var C=this._parseByType(c.propertiesTypes[_]);E[_][C.name]=C.value;continue}E[_]=this._parseByType(c.propertiesTypes[_])}return E},o.prototype._newPacket=function(){return this.packet&&(this._list.consume(this.packet.length),this.emit("packet",this.packet)),this.packet=new h,this._pos=0,!0},o.prototype._emitError=function(n){this.error=n,this.emit("error",n)},a.exports=o},{"./constants":90,"./packet":95,bl:93,events:83,inherits:88}],97:[function(t,a,m){var r=t("./constants"),i=t("safe-buffer").Buffer,p=i.allocUnsafe(0),h=i.from([0]),c=t("./numbers"),o=t("process-nextick-args").nextTick,n=c.cache,d=c.generateNumber,g=c.generateCache,E=c.genBufVariableByteInt,u=c.generate4ByteBuffer,_=te,C=!0;function y(F,B,V){switch(B.cork&&(B.cork(),o(P,B)),C&&(C=!1,g()),F.cmd){case"connect":return M(F,B);case"connack":return k(F,B,V);case"publish":return O(F,B,V);case"puback":case"pubrec":case"pubrel":case"pubcomp":return U(F,B,V);case"subscribe":return Y(F,B,V);case"suback":return Z(F,B,V);case"unsubscribe":return ee(F,B,V);case"unsuback":return A(F,B,V);case"pingreq":case"pingresp":return j(F,B);case"disconnect":return b(F,B,V);case"auth":return N(F,B,V);default:return B.emit("error",new Error("Unknown command")),!1}}Object.defineProperty(y,"cacheNumbers",{get:function(){return _===te},set:function(F){F?((!n||Object.keys(n).length===0)&&(C=!0),_=te):(C=!1,_=le)}});function P(F){F.uncork()}function M(F,B,V){var $=F||{},ie=$.protocolId||"MQTT",ue=$.protocolVersion||4,S=$.will,w=$.clean,W=$.keepalive||0,J=$.clientId||"",ae=$.username,oe=$.password,v=$.properties;w===void 0&&(w=!0);var s=0;if(!ie||typeof ie!="string"&&!i.isBuffer(ie))return B.emit("error",new Error("Invalid protocolId")),!1;if(s+=ie.length+2,ue!==3&&ue!==4&&ue!==5)return B.emit("error",new Error("Invalid protocol version")),!1;if(s+=1,(typeof J=="string"||i.isBuffer(J))&&(J||ue===4)&&(J||w))s+=J.length+2;else{if(ue<4)return B.emit("error",new Error("clientId must be supplied before 3.1.1")),!1;if(w*1===0)return B.emit("error",new Error("clientId must be given if cleanSession set to 0")),!1}if(typeof W!="number"||W<0||W>65535||W%1!==0)return B.emit("error",new Error("Invalid keepalive")),!1;if(s+=2,s+=1,ue===5){var l=Q(B,v);s+=l.length}if(S){if(typeof S!="object")return B.emit("error",new Error("Invalid will")),!1;if(!S.topic||typeof S.topic!="string")return B.emit("error",new Error("Invalid will topic")),!1;if(s+=i.byteLength(S.topic)+2,S.payload){if(S.payload.length>=0)typeof S.payload=="string"?s+=i.byteLength(S.payload)+2:s+=S.payload.length+2;else return B.emit("error",new Error("Invalid will payload")),!1;var T={};ue===5&&(T=Q(B,S.properties),s+=T.length)}}var H=!1;if(ae!=null)if(K(ae))H=!0,s+=i.byteLength(ae)+2;else return B.emit("error",new Error("Invalid username")),!1;if(oe!=null){if(!H)return B.emit("error",new Error("Username is required to use password")),!1;if(K(oe))s+=R(oe)+2;else return B.emit("error",new Error("Invalid password")),!1}B.write(r.CONNECT_HEADER),q(B,s),pe(B,ie),B.write(ue===4?r.VERSION4:ue===5?r.VERSION5:r.VERSION3);var re=0;return re|=ae!=null?r.USERNAME_MASK:0,re|=oe!=null?r.PASSWORD_MASK:0,re|=S&&S.retain?r.WILL_RETAIN_MASK:0,re|=S&&S.qos?S.qos<<r.WILL_QOS_SHIFT:0,re|=S?r.WILL_FLAG_MASK:0,re|=w?r.CLEAN_SESSION_MASK:0,B.write(i.from([re])),_(B,W),ue===5&&l.write(),pe(B,J),S&&(ue===5&&T.write(),G(B,S.topic),pe(B,S.payload)),ae!=null&&pe(B,ae),oe!=null&&pe(B,oe),!0}function k(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=$===5?ie.reasonCode:ie.returnCode,S=ie.properties,w=2;if(typeof ue!="number")return B.emit("error",new Error("Invalid return code")),!1;var W=null;return $===5&&(W=Q(B,S),w+=W.length),B.write(r.CONNACK_HEADER),q(B,w),B.write(ie.sessionPresent?r.SESSIONPRESENT_HEADER:h),B.write(i.from([ue])),W!=null&&W.write(),!0}function O(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.qos||0,S=ie.retain?r.RETAIN_MASK:0,w=ie.topic,W=ie.payload||p,J=ie.messageId,ae=ie.properties,oe=0;if(typeof w=="string")oe+=i.byteLength(w)+2;else if(i.isBuffer(w))oe+=w.length+2;else return B.emit("error",new Error("Invalid topic")),!1;if(i.isBuffer(W)?oe+=W.length:oe+=i.byteLength(W),ue&&typeof J!="number")return B.emit("error",new Error("Invalid messageId")),!1;ue&&(oe+=2);var v=null;return $===5&&(v=Q(B,ae),oe+=v.length),B.write(r.PUBLISH_HEADER[ue][ie.dup?1:0][S?1:0]),q(B,oe),_(B,R(w)),B.write(w),ue>0&&_(B,J),v!=null&&v.write(),B.write(W)}function U(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.cmd||"puback",S=ie.messageId,w=ie.dup&&ue==="pubrel"?r.DUP_MASK:0,W=0,J=ie.reasonCode,ae=ie.properties,oe=$===5?3:2;if(ue==="pubrel"&&(W=1),typeof S!="number")return B.emit("error",new Error("Invalid messageId")),!1;var v=null;if($===5){if(v=L(B,ae,V,oe),!v)return!1;oe+=v.length}return B.write(r.ACKS[ue][W][w][0]),q(B,oe),_(B,S),$===5&&B.write(i.from([J])),v!==null&&v.write(),!0}function Y(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.dup?r.DUP_MASK:0,S=ie.messageId,w=ie.subscriptions,W=ie.properties,J=0;if(typeof S!="number")return B.emit("error",new Error("Invalid messageId")),!1;J+=2;var ae=null;if($===5&&(ae=Q(B,W),J+=ae.length),typeof w=="object"&&w.length)for(var oe=0;oe<w.length;oe+=1){var v=w[oe].topic,s=w[oe].qos;if(typeof v!="string")return B.emit("error",new Error("Invalid subscriptions - invalid topic")),!1;if(typeof s!="number")return B.emit("error",new Error("Invalid subscriptions - invalid qos")),!1;if($===5){var l=w[oe].nl||!1;if(typeof l!="boolean")return B.emit("error",new Error("Invalid subscriptions - invalid No Local")),!1;var T=w[oe].rap||!1;if(typeof T!="boolean")return B.emit("error",new Error("Invalid subscriptions - invalid Retain as Published")),!1;var H=w[oe].rh||0;if(typeof H!="number"||H>2)return B.emit("error",new Error("Invalid subscriptions - invalid Retain Handling")),!1}J+=i.byteLength(v)+2+1}else return B.emit("error",new Error("Invalid subscriptions")),!1;B.write(r.SUBSCRIBE_HEADER[1][ue?1:0][0]),q(B,J),_(B,S),ae!==null&&ae.write();for(var re=!0,se=0;se<w.length;se++){var he=w[se],me=he.topic,ye=he.qos,be=+he.nl,ge=+he.rap,Ie=he.rh,Oe;G(B,me),Oe=r.SUBSCRIBE_OPTIONS_QOS[ye],$===5&&(Oe|=be?r.SUBSCRIBE_OPTIONS_NL:0,Oe|=ge?r.SUBSCRIBE_OPTIONS_RAP:0,Oe|=Ie?r.SUBSCRIBE_OPTIONS_RH[Ie]:0),re=B.write(i.from([Oe]))}return re}function Z(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.messageId,S=ie.granted,w=ie.properties,W=0;if(typeof ue!="number")return B.emit("error",new Error("Invalid messageId")),!1;if(W+=2,typeof S=="object"&&S.length)for(var J=0;J<S.length;J+=1){if(typeof S[J]!="number")return B.emit("error",new Error("Invalid qos vector")),!1;W+=1}else return B.emit("error",new Error("Invalid qos vector")),!1;var ae=null;if($===5){if(ae=L(B,w,V,W),!ae)return!1;W+=ae.length}return B.write(r.SUBACK_HEADER),q(B,W),_(B,ue),ae!==null&&ae.write(),B.write(i.from(S))}function ee(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.messageId,S=ie.dup?r.DUP_MASK:0,w=ie.unsubscriptions,W=ie.properties,J=0;if(typeof ue!="number")return B.emit("error",new Error("Invalid messageId")),!1;if(J+=2,typeof w=="object"&&w.length)for(var ae=0;ae<w.length;ae+=1){if(typeof w[ae]!="string")return B.emit("error",new Error("Invalid unsubscriptions")),!1;J+=i.byteLength(w[ae])+2}else return B.emit("error",new Error("Invalid unsubscriptions")),!1;var oe=null;$===5&&(oe=Q(B,W),J+=oe.length),B.write(r.UNSUBSCRIBE_HEADER[1][S?1:0][0]),q(B,J),_(B,ue),oe!==null&&oe.write();for(var v=!0,s=0;s<w.length;s++)v=G(B,w[s]);return v}function A(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.messageId,S=ie.dup?r.DUP_MASK:0,w=ie.granted,W=ie.properties,J=ie.cmd,ae=0,oe=2;if(typeof ue!="number")return B.emit("error",new Error("Invalid messageId")),!1;if($===5)if(typeof w=="object"&&w.length)for(var v=0;v<w.length;v+=1){if(typeof w[v]!="number")return B.emit("error",new Error("Invalid qos vector")),!1;oe+=1}else return B.emit("error",new Error("Invalid qos vector")),!1;var s=null;if($===5){if(s=L(B,W,V,oe),!s)return!1;oe+=s.length}return B.write(r.ACKS[J][ae][S][0]),q(B,oe),_(B,ue),s!==null&&s.write(),$===5&&B.write(i.from(w)),!0}function j(F,B,V){return B.write(r.EMPTY[F.cmd])}function b(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.reasonCode,S=ie.properties,w=$===5?1:0,W=null;if($===5){if(W=L(B,S,V,w),!W)return!1;w+=W.length}return B.write(i.from([r.codes.disconnect<<4])),q(B,w),$===5&&B.write(i.from([ue])),W!==null&&W.write(),!0}function N(F,B,V){var $=V?V.protocolVersion:4,ie=F||{},ue=ie.reasonCode,S=ie.properties,w=$===5?1:0;$!==5&&B.emit("error",new Error("Invalid mqtt version for auth packet"));var W=L(B,S,V,w);return W?(w+=W.length,B.write(i.from([r.codes.auth<<4])),q(B,w),B.write(i.from([ue])),W!==null&&W.write(),!0):!1}var z={};function q(F,B){var V=z[B];V||(V=E(B).data,B<16384&&(z[B]=V)),F.write(V)}function G(F,B){var V=i.byteLength(B);_(F,V),F.write(B,"utf8")}function D(F,B,V){G(F,B),G(F,V)}function te(F,B){return F.write(n[B])}function le(F,B){return F.write(d(B))}function ce(F,B){return F.write(u(B))}function pe(F,B){typeof B=="string"?G(F,B):B?(_(F,B.length),F.write(B)):_(F,0)}function Q(F,B){if(typeof B!="object"||B.length!=null)return{length:1,write:function(){I(F,{},0)}};var V=0;function $(w){var W=r.propertiesTypes[w],J=B[w],ae=0;switch(W){case"byte":{if(typeof J!="boolean")return F.emit("error",new Error("Invalid "+w)),!1;ae+=2;break}case"int8":{if(typeof J!="number")return F.emit("error",new Error("Invalid "+w)),!1;ae+=2;break}case"binary":{if(J&&J===null)return F.emit("error",new Error("Invalid "+w)),!1;ae+=1+i.byteLength(J)+2;break}case"int16":{if(typeof J!="number")return F.emit("error",new Error("Invalid "+w)),!1;ae+=3;break}case"int32":{if(typeof J!="number")return F.emit("error",new Error("Invalid "+w)),!1;ae+=5;break}case"var":{if(typeof J!="number")return F.emit("error",new Error("Invalid "+w)),!1;ae+=1+E(J).length;break}case"string":{if(typeof J!="string")return F.emit("error",new Error("Invalid "+w)),!1;ae+=3+i.byteLength(J.toString());break}case"pair":{if(typeof J!="object")return F.emit("error",new Error("Invalid "+w)),!1;ae+=Object.getOwnPropertyNames(J).reduce(function(oe,v){return oe+=3+i.byteLength(v.toString())+2+i.byteLength(J[v].toString()),oe},0);break}default:return F.emit("error",new Error("Invalid property "+w)),!1}return ae}if(B)for(var ie in B){var ue=$(ie);if(!ue)return!1;V+=ue}var S=E(V).length;return{length:S+V,write:function(){I(F,B,V)}}}function L(F,B,V,$){var ie=["reasonString","userProperties"],ue=V&&V.properties&&V.properties.maximumPacketSize?V.properties.maximumPacketSize:0,S=Q(F,B);if(ue)for(;$+S.length>ue;){var w=ie.shift();if(w&&B[w])delete B[w],S=Q(F,B);else return!1}return S}function I(F,B,V){q(F,V);for(var $ in B)if(B.hasOwnProperty($)&&B[$]!==null){var ie=B[$],ue=r.propertiesTypes[$];switch(ue){case"byte":{F.write(i.from([r.properties[$]])),F.write(i.from([+ie]));break}case"int8":{F.write(i.from([r.properties[$]])),F.write(i.from([ie]));break}case"binary":{F.write(i.from([r.properties[$]])),pe(F,ie);break}case"int16":{F.write(i.from([r.properties[$]])),_(F,ie);break}case"int32":{F.write(i.from([r.properties[$]])),ce(F,ie);break}case"var":{F.write(i.from([r.properties[$]])),q(F,ie);break}case"string":{F.write(i.from([r.properties[$]])),G(F,ie);break}case"pair":{Object.getOwnPropertyNames(ie).forEach(function(S){F.write(i.from([r.properties[$]])),D(F,S.toString(),ie[S].toString())});break}default:return F.emit("error",new Error("Invalid property "+$)),!1}}}function R(F){return F?F instanceof i?F.length:i.byteLength(F):0}function K(F){return typeof F=="string"||F instanceof i}a.exports=y},{"./constants":90,"./numbers":94,"process-nextick-args":99,"safe-buffer":118}],98:[function(t,a,m){var r=t("wrappy");a.exports=r(i),a.exports.strict=r(p),i.proto=i(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return p(this)},configurable:!0})});function i(h){var c=function(){return c.called?c.value:(c.called=!0,c.value=h.apply(this,arguments))};return c.called=!1,c}function p(h){var c=function(){if(c.called)throw new Error(c.onceError);return c.called=!0,c.value=h.apply(this,arguments)},o=h.name||"Function wrapped with `once`";return c.onceError=o+" shouldn't be called more than once",c.called=!1,c}},{wrappy:139}],99:[function(t,a,m){(function(r){typeof r=="undefined"||!r.version||r.version.indexOf("v0.")===0||r.version.indexOf("v1.")===0&&r.version.indexOf("v1.8.")!==0?a.exports={nextTick:i}:a.exports=r;function i(p,h,c,o){if(typeof p!="function")throw new TypeError('"callback" argument must be a function');var n=arguments.length,d,g;switch(n){case 0:case 1:return r.nextTick(p);case 2:return r.nextTick(function(){p.call(null,h)});case 3:return r.nextTick(function(){p.call(null,h,c)});case 4:return r.nextTick(function(){p.call(null,h,c,o)});default:for(d=new Array(n-1),g=0;g<d.length;)d[g++]=arguments[g];return r.nextTick(function(){p.apply(null,d)})}}}).call(this,t("_process"))},{_process:100}],100:[function(t,a,m){var r=a.exports={},i,p;function h(){throw new Error("setTimeout has not been defined")}function c(){throw new Error("clearTimeout has not been defined")}(function(){try{typeof setTimeout=="function"?i=setTimeout:i=h}catch(M){i=h}try{typeof clearTimeout=="function"?p=clearTimeout:p=c}catch(M){p=c}})();function o(M){if(i===setTimeout)return setTimeout(M,0);if((i===h||!i)&&setTimeout)return i=setTimeout,setTimeout(M,0);try{return i(M,0)}catch(k){try{return i.call(null,M,0)}catch(O){return i.call(this,M,0)}}}function n(M){if(p===clearTimeout)return clearTimeout(M);if((p===c||!p)&&clearTimeout)return p=clearTimeout,clearTimeout(M);try{return p(M)}catch(k){try{return p.call(null,M)}catch(O){return p.call(this,M)}}}var d=[],g=!1,E,u=-1;function _(){!g||!E||(g=!1,E.length?d=E.concat(d):u=-1,d.length&&C())}function C(){if(!g){var M=o(_);g=!0;for(var k=d.length;k;){for(E=d,d=[];++u<k;)E&&E[u].run();u=-1,k=d.length}E=null,g=!1,n(M)}}r.nextTick=function(M){var k=new Array(arguments.length-1);if(arguments.length>1)for(var O=1;O<arguments.length;O++)k[O-1]=arguments[O];d.push(new y(M,k)),d.length===1&&!g&&o(C)};function y(M,k){this.fun=M,this.array=k}y.prototype.run=function(){this.fun.apply(null,this.array)},r.title="browser",r.browser=!0,r.env={},r.argv=[],r.version="",r.versions={};function P(){}r.on=P,r.addListener=P,r.once=P,r.off=P,r.removeListener=P,r.removeAllListeners=P,r.emit=P,r.prependListener=P,r.prependOnceListener=P,r.listeners=function(M){return[]},r.binding=function(M){throw new Error("process.binding is not supported")},r.cwd=function(){return"/"},r.chdir=function(M){throw new Error("process.chdir is not supported")},r.umask=function(){return 0}},{}],101:[function(t,a,m){(function(r){(function(i){var p=typeof m=="object"&&m&&!m.nodeType&&m,h=typeof a=="object"&&a&&!a.nodeType&&a,c=typeof r=="object"&&r;(c.global===c||c.window===c||c.self===c)&&(i=c);var o,n=2147483647,d=36,g=1,E=26,u=38,_=700,C=72,y=128,P="-",M=/^xn--/,k=/[^\x20-\x7E]/,O=/[\x2E\u3002\uFF0E\uFF61]/g,U={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},Y=d-g,Z=Math.floor,ee=String.fromCharCode,A;function j(L){throw new RangeError(U[L])}function b(L,I){for(var R=L.length,K=[];R--;)K[R]=I(L[R]);return K}function N(L,I){var R=L.split("@"),K="";R.length>1&&(K=R[0]+"@",L=R[1]),L=L.replace(O,".");var F=L.split("."),B=b(F,I).join(".");return K+B}function z(L){for(var I=[],R=0,K=L.length,F,B;R<K;)F=L.charCodeAt(R++),F>=55296&&F<=56319&&R<K?(B=L.charCodeAt(R++),(B&64512)==56320?I.push(((F&1023)<<10)+(B&1023)+65536):(I.push(F),R--)):I.push(F);return I}function q(L){return b(L,function(I){var R="";return I>65535&&(I-=65536,R+=ee(I>>>10&1023|55296),I=56320|I&1023),R+=ee(I),R}).join("")}function G(L){return L-48<10?L-22:L-65<26?L-65:L-97<26?L-97:d}function D(L,I){return L+22+75*(L<26)-((I!=0)<<5)}function te(L,I,R){var K=0;for(L=R?Z(L/_):L>>1,L+=Z(L/I);L>Y*E>>1;K+=d)L=Z(L/Y);return Z(K+(Y+1)*L/(L+u))}function le(L){var I=[],R=L.length,K,F=0,B=y,V=C,$,ie,ue,S,w,W,J,ae,oe;for($=L.lastIndexOf(P),$<0&&($=0),ie=0;ie<$;++ie)L.charCodeAt(ie)>=128&&j("not-basic"),I.push(L.charCodeAt(ie));for(ue=$>0?$+1:0;ue<R;){for(S=F,w=1,W=d;ue>=R&&j("invalid-input"),J=G(L.charCodeAt(ue++)),(J>=d||J>Z((n-F)/w))&&j("overflow"),F+=J*w,ae=W<=V?g:W>=V+E?E:W-V,!(J<ae);W+=d)oe=d-ae,w>Z(n/oe)&&j("overflow"),w*=oe;K=I.length+1,V=te(F-S,K,S==0),Z(F/K)>n-B&&j("overflow"),B+=Z(F/K),F%=K,I.splice(F++,0,B)}return q(I)}function ce(L){var I,R,K,F,B,V,$,ie,ue,S,w,W=[],J,ae,oe,v;for(L=z(L),J=L.length,I=y,R=0,B=C,V=0;V<J;++V)w=L[V],w<128&&W.push(ee(w));for(K=F=W.length,F&&W.push(P);K<J;){for($=n,V=0;V<J;++V)w=L[V],w>=I&&w<$&&($=w);for(ae=K+1,$-I>Z((n-R)/ae)&&j("overflow"),R+=($-I)*ae,I=$,V=0;V<J;++V)if(w=L[V],w<I&&++R>n&&j("overflow"),w==I){for(ie=R,ue=d;S=ue<=B?g:ue>=B+E?E:ue-B,!(ie<S);ue+=d)v=ie-S,oe=d-S,W.push(ee(D(S+v%oe,0))),ie=Z(v/oe);W.push(ee(D(ie,0))),B=te(R,ae,K==F),R=0,++K}++R,++I}return W.join("")}function pe(L){return N(L,function(I){return M.test(I)?le(I.slice(4).toLowerCase()):I})}function Q(L){return N(L,function(I){return k.test(I)?"xn--"+ce(I):I})}if(o={version:"1.4.1",ucs2:{decode:z,encode:q},decode:le,encode:ce,toASCII:Q,toUnicode:pe},p&&h)if(a.exports==p)h.exports=o;else for(A in o)o.hasOwnProperty(A)&&(p[A]=o[A]);else i.punycode=o})(this)}).call(this,typeof Ee!="undefined"?Ee:typeof self!="undefined"?self:typeof window!="undefined"?window:{})},{}],102:[function(t,a,m){function r(p,h){return Object.prototype.hasOwnProperty.call(p,h)}a.exports=function(p,h,c,o){h=h||"&",c=c||"=";var n={};if(typeof p!="string"||p.length===0)return n;var d=/\+/g;p=p.split(h);var g=1e3;o&&typeof o.maxKeys=="number"&&(g=o.maxKeys);var E=p.length;g>0&&E>g&&(E=g);for(var u=0;u<E;++u){var _=p[u].replace(d,"%20"),C=_.indexOf(c),y,P,M,k;C>=0?(y=_.substr(0,C),P=_.substr(C+1)):(y=_,P=""),M=decodeURIComponent(y),k=decodeURIComponent(P),r(n,M)?i(n[M])?n[M].push(k):n[M]=[n[M],k]:n[M]=k}return n};var i=Array.isArray||function(p){return Object.prototype.toString.call(p)==="[object Array]"}},{}],103:[function(t,a,m){var r=function(c){switch(typeof c){case"string":return c;case"boolean":return c?"true":"false";case"number":return isFinite(c)?c:"";default:return""}};a.exports=function(c,o,n,d){return o=o||"&",n=n||"=",c===null&&(c=void 0),typeof c=="object"?p(h(c),function(g){var E=encodeURIComponent(r(g))+n;return i(c[g])?p(c[g],function(u){return E+encodeURIComponent(r(u))}).join(o):E+encodeURIComponent(r(c[g]))}).join(o):d?encodeURIComponent(r(d))+n+encodeURIComponent(r(c)):""};var i=Array.isArray||function(c){return Object.prototype.toString.call(c)==="[object Array]"};function p(c,o){if(c.map)return c.map(o);for(var n=[],d=0;d<c.length;d++)n.push(o(c[d],d));return n}var h=Object.keys||function(c){var o=[];for(var n in c)Object.prototype.hasOwnProperty.call(c,n)&&o.push(n);return o}},{}],104:[function(t,a,m){m.decode=m.parse=t("./decode"),m.encode=m.stringify=t("./encode")},{"./decode":102,"./encode":103}],105:[function(t,a,m){a.exports=t("./lib/_stream_duplex.js")},{"./lib/_stream_duplex.js":106}],106:[function(t,a,m){var r=t("process-nextick-args"),i=Object.keys||function(_){var C=[];for(var y in _)C.push(y);return C};a.exports=g;var p=t("core-util-is");p.inherits=t("inherits");var h=t("./_stream_readable"),c=t("./_stream_writable");p.inherits(g,h);for(var o=i(c.prototype),n=0;n<o.length;n++){var d=o[n];g.prototype[d]||(g.prototype[d]=c.prototype[d])}function g(_){if(!(this instanceof g))return new g(_);h.call(this,_),c.call(this,_),_&&_.readable===!1&&(this.readable=!1),_&&_.writable===!1&&(this.writable=!1),this.allowHalfOpen=!0,_&&_.allowHalfOpen===!1&&(this.allowHalfOpen=!1),this.once("end",E)}Object.defineProperty(g.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function E(){this.allowHalfOpen||this._writableState.ended||r.nextTick(u,this)}function u(_){_.end()}Object.defineProperty(g.prototype,"destroyed",{get:function(){return this._readableState===void 0||this._writableState===void 0?!1:this._readableState.destroyed&&this._writableState.destroyed},set:function(_){this._readableState===void 0||this._writableState===void 0||(this._readableState.destroyed=_,this._writableState.destroyed=_)}}),g.prototype._destroy=function(_,C){this.push(null),this.end(),r.nextTick(C,_)}},{"./_stream_readable":108,"./_stream_writable":110,"core-util-is":13,inherits:88,"process-nextick-args":99}],107:[function(t,a,m){a.exports=p;var r=t("./_stream_transform"),i=t("core-util-is");i.inherits=t("inherits"),i.inherits(p,r);function p(h){if(!(this instanceof p))return new p(h);r.call(this,h)}p.prototype._transform=function(h,c,o){o(null,h)}},{"./_stream_transform":109,"core-util-is":13,inherits:88}],108:[function(t,a,m){(function(r,i){var p=t("process-nextick-args");a.exports=Z;var h=t("isarray"),c;Z.ReadableState=Y,t("events").EventEmitter;var o=function(S,w){return S.listeners(w).length},n=t("./internal/streams/stream"),d=t("safe-buffer").Buffer,g=i.Uint8Array||function(){};function E(S){return d.from(S)}function u(S){return d.isBuffer(S)||S instanceof g}var _=t("core-util-is");_.inherits=t("inherits");var C=t("util"),y=void 0;C&&C.debuglog?y=C.debuglog("stream"):y=function(){};var P=t("./internal/streams/BufferList"),M=t("./internal/streams/destroy"),k;_.inherits(Z,n);var O=["error","close","destroy","pause","resume"];function U(S,w,W){if(typeof S.prependListener=="function")return S.prependListener(w,W);!S._events||!S._events[w]?S.on(w,W):h(S._events[w])?S._events[w].unshift(W):S._events[w]=[W,S._events[w]]}function Y(S,w){c=c||t("./_stream_duplex"),S=S||{};var W=w instanceof c;this.objectMode=!!S.objectMode,W&&(this.objectMode=this.objectMode||!!S.readableObjectMode);var J=S.highWaterMark,ae=S.readableHighWaterMark,oe=this.objectMode?16:16*1024;J||J===0?this.highWaterMark=J:W&&(ae||ae===0)?this.highWaterMark=ae:this.highWaterMark=oe,this.highWaterMark=Math.floor(this.highWaterMark),this.buffer=new P,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.destroyed=!1,this.defaultEncoding=S.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,S.encoding&&(k||(k=t("string_decoder/").StringDecoder),this.decoder=new k(S.encoding),this.encoding=S.encoding)}function Z(S){if(c=c||t("./_stream_duplex"),!(this instanceof Z))return new Z(S);this._readableState=new Y(S,this),this.readable=!0,S&&(typeof S.read=="function"&&(this._read=S.read),typeof S.destroy=="function"&&(this._destroy=S.destroy)),n.call(this)}Object.defineProperty(Z.prototype,"destroyed",{get:function(){return this._readableState===void 0?!1:this._readableState.destroyed},set:function(S){this._readableState&&(this._readableState.destroyed=S)}}),Z.prototype.destroy=M.destroy,Z.prototype._undestroy=M.undestroy,Z.prototype._destroy=function(S,w){this.push(null),w(S)},Z.prototype.push=function(S,w){var W=this._readableState,J;return W.objectMode?J=!0:typeof S=="string"&&(w=w||W.defaultEncoding,w!==W.encoding&&(S=d.from(S,w),w=""),J=!0),ee(this,S,w,!1,J)},Z.prototype.unshift=function(S){return ee(this,S,null,!0,!1)};function ee(S,w,W,J,ae){var oe=S._readableState;if(w===null)oe.reading=!1,G(S,oe);else{var v;ae||(v=j(oe,w)),v?S.emit("error",v):oe.objectMode||w&&w.length>0?(typeof w!="string"&&!oe.objectMode&&Object.getPrototypeOf(w)!==d.prototype&&(w=E(w)),J?oe.endEmitted?S.emit("error",new Error("stream.unshift() after end event")):A(S,oe,w,!0):oe.ended?S.emit("error",new Error("stream.push() after EOF")):(oe.reading=!1,oe.decoder&&!W?(w=oe.decoder.write(w),oe.objectMode||w.length!==0?A(S,oe,w,!1):le(S,oe)):A(S,oe,w,!1))):J||(oe.reading=!1)}return b(oe)}function A(S,w,W,J){w.flowing&&w.length===0&&!w.sync?(S.emit("data",W),S.read(0)):(w.length+=w.objectMode?1:W.length,J?w.buffer.unshift(W):w.buffer.push(W),w.needReadable&&D(S)),le(S,w)}function j(S,w){var W;return!u(w)&&typeof w!="string"&&w!==void 0&&!S.objectMode&&(W=new TypeError("Invalid non-string/buffer chunk")),W}function b(S){return!S.ended&&(S.needReadable||S.length<S.highWaterMark||S.length===0)}Z.prototype.isPaused=function(){return this._readableState.flowing===!1},Z.prototype.setEncoding=function(S){return k||(k=t("string_decoder/").StringDecoder),this._readableState.decoder=new k(S),this._readableState.encoding=S,this};var N=8388608;function z(S){return S>=N?S=N:(S--,S|=S>>>1,S|=S>>>2,S|=S>>>4,S|=S>>>8,S|=S>>>16,S++),S}function q(S,w){return S<=0||w.length===0&&w.ended?0:w.objectMode?1:S!==S?w.flowing&&w.length?w.buffer.head.data.length:w.length:(S>w.highWaterMark&&(w.highWaterMark=z(S)),S<=w.length?S:w.ended?w.length:(w.needReadable=!0,0))}Z.prototype.read=function(S){y("read",S),S=parseInt(S,10);var w=this._readableState,W=S;if(S!==0&&(w.emittedReadable=!1),S===0&&w.needReadable&&(w.length>=w.highWaterMark||w.ended))return y("read: emitReadable",w.length,w.ended),w.length===0&&w.ended?$(this):D(this),null;if(S=q(S,w),S===0&&w.ended)return w.length===0&&$(this),null;var J=w.needReadable;y("need readable",J),(w.length===0||w.length-S<w.highWaterMark)&&(J=!0,y("length less than watermark",J)),w.ended||w.reading?(J=!1,y("reading or ended",J)):J&&(y("do read"),w.reading=!0,w.sync=!0,w.length===0&&(w.needReadable=!0),this._read(w.highWaterMark),w.sync=!1,w.reading||(S=q(W,w)));var ae;return S>0?ae=K(S,w):ae=null,ae===null?(w.needReadable=!0,S=0):w.length-=S,w.length===0&&(w.ended||(w.needReadable=!0),W!==S&&w.ended&&$(this)),ae!==null&&this.emit("data",ae),ae};function G(S,w){if(!w.ended){if(w.decoder){var W=w.decoder.end();W&&W.length&&(w.buffer.push(W),w.length+=w.objectMode?1:W.length)}w.ended=!0,D(S)}}function D(S){var w=S._readableState;w.needReadable=!1,w.emittedReadable||(y("emitReadable",w.flowing),w.emittedReadable=!0,w.sync?p.nextTick(te,S):te(S))}function te(S){y("emit readable"),S.emit("readable"),R(S)}function le(S,w){w.readingMore||(w.readingMore=!0,p.nextTick(ce,S,w))}function ce(S,w){for(var W=w.length;!w.reading&&!w.flowing&&!w.ended&&w.length<w.highWaterMark&&(y("maybeReadMore read 0"),S.read(0),W!==w.length);)W=w.length;w.readingMore=!1}Z.prototype._read=function(S){this.emit("error",new Error("_read() is not implemented"))},Z.prototype.pipe=function(S,w){var W=this,J=this._readableState;switch(J.pipesCount){case 0:J.pipes=S;break;case 1:J.pipes=[J.pipes,S];break;default:J.pipes.push(S);break}J.pipesCount+=1,y("pipe count=%d opts=%j",J.pipesCount,w);var ae=(!w||w.end!==!1)&&S!==r.stdout&&S!==r.stderr,oe=ae?s:be;J.endEmitted?p.nextTick(oe):W.once("end",oe),S.on("unpipe",v);function v(ge,Ie){y("onunpipe"),ge===W&&Ie&&Ie.hasUnpiped===!1&&(Ie.hasUnpiped=!0,H())}function s(){y("onend"),S.end()}var l=pe(W);S.on("drain",l);var T=!1;function H(){y("cleanup"),S.removeListener("close",me),S.removeListener("finish",ye),S.removeListener("drain",l),S.removeListener("error",he),S.removeListener("unpipe",v),W.removeListener("end",s),W.removeListener("end",be),W.removeListener("data",se),T=!0,J.awaitDrain&&(!S._writableState||S._writableState.needDrain)&&l()}var re=!1;W.on("data",se);function se(ge){y("ondata"),re=!1;var Ie=S.write(ge);Ie===!1&&!re&&((J.pipesCount===1&&J.pipes===S||J.pipesCount>1&&ue(J.pipes,S)!==-1)&&!T&&(y("false write response, pause",W._readableState.awaitDrain),W._readableState.awaitDrain++,re=!0),W.pause())}function he(ge){y("onerror",ge),be(),S.removeListener("error",he),o(S,"error")===0&&S.emit("error",ge)}U(S,"error",he);function me(){S.removeListener("finish",ye),be()}S.once("close",me);function ye(){y("onfinish"),S.removeListener("close",me),be()}S.once("finish",ye);function be(){y("unpipe"),W.unpipe(S)}return S.emit("pipe",W),J.flowing||(y("pipe resume"),W.resume()),S};function pe(S){return function(){var w=S._readableState;y("pipeOnDrain",w.awaitDrain),w.awaitDrain&&w.awaitDrain--,w.awaitDrain===0&&o(S,"data")&&(w.flowing=!0,R(S))}}Z.prototype.unpipe=function(S){var w=this._readableState,W={hasUnpiped:!1};if(w.pipesCount===0)return this;if(w.pipesCount===1)return S&&S!==w.pipes?this:(S||(S=w.pipes),w.pipes=null,w.pipesCount=0,w.flowing=!1,S&&S.emit("unpipe",this,W),this);if(!S){var J=w.pipes,ae=w.pipesCount;w.pipes=null,w.pipesCount=0,w.flowing=!1;for(var oe=0;oe<ae;oe++)J[oe].emit("unpipe",this,W);return this}var v=ue(w.pipes,S);return v===-1?this:(w.pipes.splice(v,1),w.pipesCount-=1,w.pipesCount===1&&(w.pipes=w.pipes[0]),S.emit("unpipe",this,W),this)},Z.prototype.on=function(S,w){var W=n.prototype.on.call(this,S,w);if(S==="data")this._readableState.flowing!==!1&&this.resume();else if(S==="readable"){var J=this._readableState;!J.endEmitted&&!J.readableListening&&(J.readableListening=J.needReadable=!0,J.emittedReadable=!1,J.reading?J.length&&D(this):p.nextTick(Q,this))}return W},Z.prototype.addListener=Z.prototype.on;function Q(S){y("readable nexttick read 0"),S.read(0)}Z.prototype.resume=function(){var S=this._readableState;return S.flowing||(y("resume"),S.flowing=!0,L(this,S)),this};function L(S,w){w.resumeScheduled||(w.resumeScheduled=!0,p.nextTick(I,S,w))}function I(S,w){w.reading||(y("resume read 0"),S.read(0)),w.resumeScheduled=!1,w.awaitDrain=0,S.emit("resume"),R(S),w.flowing&&!w.reading&&S.read(0)}Z.prototype.pause=function(){return y("call pause flowing=%j",this._readableState.flowing),this._readableState.flowing!==!1&&(y("pause"),this._readableState.flowing=!1,this.emit("pause")),this};function R(S){var w=S._readableState;for(y("flow",w.flowing);w.flowing&&S.read()!==null;);}Z.prototype.wrap=function(S){var w=this,W=this._readableState,J=!1;S.on("end",function(){if(y("wrapped end"),W.decoder&&!W.ended){var v=W.decoder.end();v&&v.length&&w.push(v)}w.push(null)}),S.on("data",function(v){if(y("wrapped data"),W.decoder&&(v=W.decoder.write(v)),!(W.objectMode&&v==null)&&!(!W.objectMode&&(!v||!v.length))){var s=w.push(v);s||(J=!0,S.pause())}});for(var ae in S)this[ae]===void 0&&typeof S[ae]=="function"&&(this[ae]=function(v){return function(){return S[v].apply(S,arguments)}}(ae));for(var oe=0;oe<O.length;oe++)S.on(O[oe],this.emit.bind(this,O[oe]));return this._read=function(v){y("wrapped _read",v),J&&(J=!1,S.resume())},this},Object.defineProperty(Z.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Z._fromList=K;function K(S,w){if(w.length===0)return null;var W;return w.objectMode?W=w.buffer.shift():!S||S>=w.length?(w.decoder?W=w.buffer.join(""):w.buffer.length===1?W=w.buffer.head.data:W=w.buffer.concat(w.length),w.buffer.clear()):W=F(S,w.buffer,w.decoder),W}function F(S,w,W){var J;return S<w.head.data.length?(J=w.head.data.slice(0,S),w.head.data=w.head.data.slice(S)):S===w.head.data.length?J=w.shift():J=W?B(S,w):V(S,w),J}function B(S,w){var W=w.head,J=1,ae=W.data;for(S-=ae.length;W=W.next;){var oe=W.data,v=S>oe.length?oe.length:S;if(v===oe.length?ae+=oe:ae+=oe.slice(0,S),S-=v,S===0){v===oe.length?(++J,W.next?w.head=W.next:w.head=w.tail=null):(w.head=W,W.data=oe.slice(v));break}++J}return w.length-=J,ae}function V(S,w){var W=d.allocUnsafe(S),J=w.head,ae=1;for(J.data.copy(W),S-=J.data.length;J=J.next;){var oe=J.data,v=S>oe.length?oe.length:S;if(oe.copy(W,W.length-S,0,v),S-=v,S===0){v===oe.length?(++ae,J.next?w.head=J.next:w.head=w.tail=null):(w.head=J,J.data=oe.slice(v));break}++ae}return w.length-=ae,W}function $(S){var w=S._readableState;if(w.length>0)throw new Error('"endReadable()" called on non-empty stream');w.endEmitted||(w.ended=!0,p.nextTick(ie,w,S))}function ie(S,w){!S.endEmitted&&S.length===0&&(S.endEmitted=!0,w.readable=!1,w.emit("end"))}function ue(S,w){for(var W=0,J=S.length;W<J;W++)if(S[W]===w)return W;return-1}}).call(this,t("_process"),typeof Ee!="undefined"?Ee:typeof self!="undefined"?self:typeof window!="undefined"?window:{})},{"./_stream_duplex":106,"./internal/streams/BufferList":111,"./internal/streams/destroy":112,"./internal/streams/stream":113,_process:100,"core-util-is":13,events:83,inherits:88,isarray:114,"process-nextick-args":99,"safe-buffer":118,"string_decoder/":115,util:11}],109:[function(t,a,m){a.exports=h;var r=t("./_stream_duplex"),i=t("core-util-is");i.inherits=t("inherits"),i.inherits(h,r);function p(n,d){var g=this._transformState;g.transforming=!1;var E=g.writecb;if(!E)return this.emit("error",new Error("write callback called multiple times"));g.writechunk=null,g.writecb=null,d!=null&&this.push(d),E(n);var u=this._readableState;u.reading=!1,(u.needReadable||u.length<u.highWaterMark)&&this._read(u.highWaterMark)}function h(n){if(!(this instanceof h))return new h(n);r.call(this,n),this._transformState={afterTransform:p.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,n&&(typeof n.transform=="function"&&(this._transform=n.transform),typeof n.flush=="function"&&(this._flush=n.flush)),this.on("prefinish",c)}function c(){var n=this;typeof this._flush=="function"?this._flush(function(d,g){o(n,d,g)}):o(this,null,null)}h.prototype.push=function(n,d){return this._transformState.needTransform=!1,r.prototype.push.call(this,n,d)},h.prototype._transform=function(n,d,g){throw new Error("_transform() is not implemented")},h.prototype._write=function(n,d,g){var E=this._transformState;if(E.writecb=g,E.writechunk=n,E.writeencoding=d,!E.transforming){var u=this._readableState;(E.needTransform||u.needReadable||u.length<u.highWaterMark)&&this._read(u.highWaterMark)}},h.prototype._read=function(n){var d=this._transformState;d.writechunk!==null&&d.writecb&&!d.transforming?(d.transforming=!0,this._transform(d.writechunk,d.writeencoding,d.afterTransform)):d.needTransform=!0},h.prototype._destroy=function(n,d){var g=this;r.prototype._destroy.call(this,n,function(E){d(E),g.emit("close")})};function o(n,d,g){if(d)return n.emit("error",d);if(g!=null&&n.push(g),n._writableState.length)throw new Error("Calling transform done when ws.length != 0");if(n._transformState.transforming)throw new Error("Calling transform done when still transforming");return n.push(null)}},{"./_stream_duplex":106,"core-util-is":13,inherits:88}],110:[function(t,a,m){(function(r,i,p){var h=t("process-nextick-args");a.exports=U;function c(I){var R=this;this.next=null,this.entry=null,this.finish=function(){L(R,I)}}var o=!r.browser&&["v0.10","v0.9."].indexOf(r.version.slice(0,5))>-1?p:h.nextTick,n;U.WritableState=k;var d=t("core-util-is");d.inherits=t("inherits");var g={deprecate:t("util-deprecate")},E=t("./internal/streams/stream"),u=t("safe-buffer").Buffer,_=i.Uint8Array||function(){};function C(I){return u.from(I)}function y(I){return u.isBuffer(I)||I instanceof _}var P=t("./internal/streams/destroy");d.inherits(U,E);function M(){}function k(I,R){n=n||t("./_stream_duplex"),I=I||{};var K=R instanceof n;this.objectMode=!!I.objectMode,K&&(this.objectMode=this.objectMode||!!I.writableObjectMode);var F=I.highWaterMark,B=I.writableHighWaterMark,V=this.objectMode?16:16*1024;F||F===0?this.highWaterMark=F:K&&(B||B===0)?this.highWaterMark=B:this.highWaterMark=V,this.highWaterMark=Math.floor(this.highWaterMark),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var $=I.decodeStrings===!1;this.decodeStrings=!$,this.defaultEncoding=I.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(ie){z(R,ie)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new c(this)}k.prototype.getBuffer=function(){for(var R=this.bufferedRequest,K=[];R;)K.push(R),R=R.next;return K},function(){try{Object.defineProperty(k.prototype,"buffer",{get:g.deprecate(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(I){}}();var O;typeof Symbol=="function"&&Symbol.hasInstance&&typeof Function.prototype[Symbol.hasInstance]=="function"?(O=Function.prototype[Symbol.hasInstance],Object.defineProperty(U,Symbol.hasInstance,{value:function(I){return O.call(this,I)?!0:this!==U?!1:I&&I._writableState instanceof k}})):O=function(I){return I instanceof this};function U(I){if(n=n||t("./_stream_duplex"),!O.call(U,this)&&!(this instanceof n))return new U(I);this._writableState=new k(I,this),this.writable=!0,I&&(typeof I.write=="function"&&(this._write=I.write),typeof I.writev=="function"&&(this._writev=I.writev),typeof I.destroy=="function"&&(this._destroy=I.destroy),typeof I.final=="function"&&(this._final=I.final)),E.call(this)}U.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))};function Y(I,R){var K=new Error("write after end");I.emit("error",K),h.nextTick(R,K)}function Z(I,R,K,F){var B=!0,V=!1;return K===null?V=new TypeError("May not write null values to stream"):typeof K!="string"&&K!==void 0&&!R.objectMode&&(V=new TypeError("Invalid non-string/buffer chunk")),V&&(I.emit("error",V),h.nextTick(F,V),B=!1),B}U.prototype.write=function(I,R,K){var F=this._writableState,B=!1,V=!F.objectMode&&y(I);return V&&!u.isBuffer(I)&&(I=C(I)),typeof R=="function"&&(K=R,R=null),V?R="buffer":R||(R=F.defaultEncoding),typeof K!="function"&&(K=M),F.ended?Y(this,K):(V||Z(this,F,I,K))&&(F.pendingcb++,B=A(this,F,V,I,R,K)),B},U.prototype.cork=function(){var I=this._writableState;I.corked++},U.prototype.uncork=function(){var I=this._writableState;I.corked&&(I.corked--,!I.writing&&!I.corked&&!I.finished&&!I.bufferProcessing&&I.bufferedRequest&&D(this,I))},U.prototype.setDefaultEncoding=function(R){if(typeof R=="string"&&(R=R.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((R+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+R);return this._writableState.defaultEncoding=R,this};function ee(I,R,K){return!I.objectMode&&I.decodeStrings!==!1&&typeof R=="string"&&(R=u.from(R,K)),R}Object.defineProperty(U.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}});function A(I,R,K,F,B,V){if(!K){var $=ee(R,F,B);F!==$&&(K=!0,B="buffer",F=$)}var ie=R.objectMode?1:F.length;R.length+=ie;var ue=R.length<R.highWaterMark;if(ue||(R.needDrain=!0),R.writing||R.corked){var S=R.lastBufferedRequest;R.lastBufferedRequest={chunk:F,encoding:B,isBuf:K,callback:V,next:null},S?S.next=R.lastBufferedRequest:R.bufferedRequest=R.lastBufferedRequest,R.bufferedRequestCount+=1}else j(I,R,!1,ie,F,B,V);return ue}function j(I,R,K,F,B,V,$){R.writelen=F,R.writecb=$,R.writing=!0,R.sync=!0,K?I._writev(B,R.onwrite):I._write(B,V,R.onwrite),R.sync=!1}function b(I,R,K,F,B){--R.pendingcb,K?(h.nextTick(B,F),h.nextTick(pe,I,R),I._writableState.errorEmitted=!0,I.emit("error",F)):(B(F),I._writableState.errorEmitted=!0,I.emit("error",F),pe(I,R))}function N(I){I.writing=!1,I.writecb=null,I.length-=I.writelen,I.writelen=0}function z(I,R){var K=I._writableState,F=K.sync,B=K.writecb;if(N(K),R)b(I,K,F,R,B);else{var V=te(K);!V&&!K.corked&&!K.bufferProcessing&&K.bufferedRequest&&D(I,K),F?o(q,I,K,V,B):q(I,K,V,B)}}function q(I,R,K,F){K||G(I,R),R.pendingcb--,F(),pe(I,R)}function G(I,R){R.length===0&&R.needDrain&&(R.needDrain=!1,I.emit("drain"))}function D(I,R){R.bufferProcessing=!0;var K=R.bufferedRequest;if(I._writev&&K&&K.next){var F=R.bufferedRequestCount,B=new Array(F),V=R.corkedRequestsFree;V.entry=K;for(var $=0,ie=!0;K;)B[$]=K,K.isBuf||(ie=!1),K=K.next,$+=1;B.allBuffers=ie,j(I,R,!0,R.length,B,"",V.finish),R.pendingcb++,R.lastBufferedRequest=null,V.next?(R.corkedRequestsFree=V.next,V.next=null):R.corkedRequestsFree=new c(R),R.bufferedRequestCount=0}else{for(;K;){var ue=K.chunk,S=K.encoding,w=K.callback,W=R.objectMode?1:ue.length;if(j(I,R,!1,W,ue,S,w),K=K.next,R.bufferedRequestCount--,R.writing)break}K===null&&(R.lastBufferedRequest=null)}R.bufferedRequest=K,R.bufferProcessing=!1}U.prototype._write=function(I,R,K){K(new Error("_write() is not implemented"))},U.prototype._writev=null,U.prototype.end=function(I,R,K){var F=this._writableState;typeof I=="function"?(K=I,I=null,R=null):typeof R=="function"&&(K=R,R=null),I!=null&&this.write(I,R),F.corked&&(F.corked=1,this.uncork()),!F.ending&&!F.finished&&Q(this,F,K)};function te(I){return I.ending&&I.length===0&&I.bufferedRequest===null&&!I.finished&&!I.writing}function le(I,R){I._final(function(K){R.pendingcb--,K&&I.emit("error",K),R.prefinished=!0,I.emit("prefinish"),pe(I,R)})}function ce(I,R){!R.prefinished&&!R.finalCalled&&(typeof I._final=="function"?(R.pendingcb++,R.finalCalled=!0,h.nextTick(le,I,R)):(R.prefinished=!0,I.emit("prefinish")))}function pe(I,R){var K=te(R);return K&&(ce(I,R),R.pendingcb===0&&(R.finished=!0,I.emit("finish"))),K}function Q(I,R,K){R.ending=!0,pe(I,R),K&&(R.finished?h.nextTick(K):I.once("finish",K)),R.ended=!0,I.writable=!1}function L(I,R,K){var F=I.entry;for(I.entry=null;F;){var B=F.callback;R.pendingcb--,B(K),F=F.next}R.corkedRequestsFree?R.corkedRequestsFree.next=I:R.corkedRequestsFree=I}Object.defineProperty(U.prototype,"destroyed",{get:function(){return this._writableState===void 0?!1:this._writableState.destroyed},set:function(I){this._writableState&&(this._writableState.destroyed=I)}}),U.prototype.destroy=P.destroy,U.prototype._undestroy=P.undestroy,U.prototype._destroy=function(I,R){this.end(),R(I)}}).call(this,t("_process"),typeof Ee!="undefined"?Ee:typeof self!="undefined"?self:typeof window!="undefined"?window:{},t("timers").setImmediate)},{"./_stream_duplex":106,"./internal/streams/destroy":112,"./internal/streams/stream":113,_process:100,"core-util-is":13,inherits:88,"process-nextick-args":99,"safe-buffer":118,timers:120,"util-deprecate":134}],111:[function(t,a,m){function r(c,o){if(!(c instanceof o))throw new TypeError("Cannot call a class as a function")}var i=t("safe-buffer").Buffer,p=t("util");function h(c,o,n){c.copy(o,n)}a.exports=function(){function c(){r(this,c),this.head=null,this.tail=null,this.length=0}return c.prototype.push=function(n){var d={data:n,next:null};this.length>0?this.tail.next=d:this.head=d,this.tail=d,++this.length},c.prototype.unshift=function(n){var d={data:n,next:this.head};this.length===0&&(this.tail=d),this.head=d,++this.length},c.prototype.shift=function(){if(this.length!==0){var n=this.head.data;return this.length===1?this.head=this.tail=null:this.head=this.head.next,--this.length,n}},c.prototype.clear=function(){this.head=this.tail=null,this.length=0},c.prototype.join=function(n){if(this.length===0)return"";for(var d=this.head,g=""+d.data;d=d.next;)g+=n+d.data;return g},c.prototype.concat=function(n){if(this.length===0)return i.alloc(0);if(this.length===1)return this.head.data;for(var d=i.allocUnsafe(n>>>0),g=this.head,E=0;g;)h(g.data,d,E),E+=g.data.length,g=g.next;return d},c}(),p&&p.inspect&&p.inspect.custom&&(a.exports.prototype[p.inspect.custom]=function(){var c=p.inspect({length:this.length});return this.constructor.name+" "+c})},{"safe-buffer":118,util:11}],112:[function(t,a,m){var r=t("process-nextick-args");function i(c,o){var n=this,d=this._readableState&&this._readableState.destroyed,g=this._writableState&&this._writableState.destroyed;return d||g?(o?o(c):c&&(!this._writableState||!this._writableState.errorEmitted)&&r.nextTick(h,this,c),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(c||null,function(E){!o&&E?(r.nextTick(h,n,E),n._writableState&&(n._writableState.errorEmitted=!0)):o&&o(E)}),this)}function p(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function h(c,o){c.emit("error",o)}a.exports={destroy:i,undestroy:p}},{"process-nextick-args":99}],113:[function(t,a,m){a.exports=t("events").EventEmitter},{events:83}],114:[function(t,a,m){var r={}.toString;a.exports=Array.isArray||function(i){return r.call(i)=="[object Array]"}},{}],115:[function(t,a,m){var r=t("safe-buffer").Buffer,i=r.isEncoding||function(O){switch(O=""+O,O&&O.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function p(O){if(!O)return"utf8";for(var U;;)switch(O){case"utf8":case"utf-8":return"utf8";case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return"utf16le";case"latin1":case"binary":return"latin1";case"base64":case"ascii":case"hex":return O;default:if(U)return;O=(""+O).toLowerCase(),U=!0}}function h(O){var U=p(O);if(typeof U!="string"&&(r.isEncoding===i||!i(O)))throw new Error("Unknown encoding: "+O);return U||O}m.StringDecoder=c;function c(O){this.encoding=h(O);var U;switch(this.encoding){case"utf16le":this.text=_,this.end=C,U=4;break;case"utf8":this.fillLast=g,U=4;break;case"base64":this.text=y,this.end=P,U=3;break;default:this.write=M,this.end=k;return}this.lastNeed=0,this.lastTotal=0,this.lastChar=r.allocUnsafe(U)}c.prototype.write=function(O){if(O.length===0)return"";var U,Y;if(this.lastNeed){if(U=this.fillLast(O),U===void 0)return"";Y=this.lastNeed,this.lastNeed=0}else Y=0;return Y<O.length?U?U+this.text(O,Y):this.text(O,Y):U||""},c.prototype.end=u,c.prototype.text=E,c.prototype.fillLast=function(O){if(this.lastNeed<=O.length)return O.copy(this.lastChar,this.lastTotal-this.lastNeed,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);O.copy(this.lastChar,this.lastTotal-this.lastNeed,0,O.length),this.lastNeed-=O.length};function o(O){return O<=127?0:O>>5===6?2:O>>4===14?3:O>>3===30?4:O>>6===2?-1:-2}function n(O,U,Y){var Z=U.length-1;if(Z<Y)return 0;var ee=o(U[Z]);return ee>=0?(ee>0&&(O.lastNeed=ee-1),ee):--Z<Y||ee===-2?0:(ee=o(U[Z]),ee>=0?(ee>0&&(O.lastNeed=ee-2),ee):--Z<Y||ee===-2?0:(ee=o(U[Z]),ee>=0?(ee>0&&(ee===2?ee=0:O.lastNeed=ee-3),ee):0))}function d(O,U,Y){if((U[0]&192)!==128)return O.lastNeed=0,"\uFFFD";if(O.lastNeed>1&&U.length>1){if((U[1]&192)!==128)return O.lastNeed=1,"\uFFFD";if(O.lastNeed>2&&U.length>2&&(U[2]&192)!==128)return O.lastNeed=2,"\uFFFD"}}function g(O){var U=this.lastTotal-this.lastNeed,Y=d(this,O);if(Y!==void 0)return Y;if(this.lastNeed<=O.length)return O.copy(this.lastChar,U,0,this.lastNeed),this.lastChar.toString(this.encoding,0,this.lastTotal);O.copy(this.lastChar,U,0,O.length),this.lastNeed-=O.length}function E(O,U){var Y=n(this,O,U);if(!this.lastNeed)return O.toString("utf8",U);this.lastTotal=Y;var Z=O.length-(Y-this.lastNeed);return O.copy(this.lastChar,0,Z),O.toString("utf8",U,Z)}function u(O){var U=O&&O.length?this.write(O):"";return this.lastNeed?U+"\uFFFD":U}function _(O,U){if((O.length-U)%2===0){var Y=O.toString("utf16le",U);if(Y){var Z=Y.charCodeAt(Y.length-1);if(Z>=55296&&Z<=56319)return this.lastNeed=2,this.lastTotal=4,this.lastChar[0]=O[O.length-2],this.lastChar[1]=O[O.length-1],Y.slice(0,-1)}return Y}return this.lastNeed=1,this.lastTotal=2,this.lastChar[0]=O[O.length-1],O.toString("utf16le",U,O.length-1)}function C(O){var U=O&&O.length?this.write(O):"";if(this.lastNeed){var Y=this.lastTotal-this.lastNeed;return U+this.lastChar.toString("utf16le",0,Y)}return U}function y(O,U){var Y=(O.length-U)%3;return Y===0?O.toString("base64",U):(this.lastNeed=3-Y,this.lastTotal=3,Y===1?this.lastChar[0]=O[O.length-1]:(this.lastChar[0]=O[O.length-2],this.lastChar[1]=O[O.length-1]),O.toString("base64",U,O.length-Y))}function P(O){var U=O&&O.length?this.write(O):"";return this.lastNeed?U+this.lastChar.toString("base64",0,3-this.lastNeed):U}function M(O){return O.toString(this.encoding)}function k(O){return O&&O.length?this.write(O):""}},{"safe-buffer":118}],116:[function(t,a,m){m=a.exports=t("./lib/_stream_readable.js"),m.Stream=m,m.Readable=m,m.Writable=t("./lib/_stream_writable.js"),m.Duplex=t("./lib/_stream_duplex.js"),m.Transform=t("./lib/_stream_transform.js"),m.PassThrough=t("./lib/_stream_passthrough.js")},{"./lib/_stream_duplex.js":106,"./lib/_stream_passthrough.js":107,"./lib/_stream_readable.js":108,"./lib/_stream_transform.js":109,"./lib/_stream_writable.js":110}],117:[function(t,a,m){function r(p,h,c){var o=this;this._callback=p,this._args=c,this._interval=setInterval(p,h,this._args),this.reschedule=function(n){n||(n=o._interval),o._interval&&clearInterval(o._interval),o._interval=setInterval(o._callback,n,o._args)},this.clear=function(){o._interval&&(clearInterval(o._interval),o._interval=void 0)},this.destroy=function(){o._interval&&clearInterval(o._interval),o._callback=void 0,o._interval=void 0,o._args=void 0}}function i(){if(typeof arguments[0]!="function")throw new Error("callback needed");if(typeof arguments[1]!="number")throw new Error("interval needed");var p;if(arguments.length>0){p=new Array(arguments.length-2);for(var h=0;h<p.length;h++)p[h]=arguments[h+2]}return new r(arguments[0],arguments[1],p)}a.exports=i},{}],118:[function(t,a,m){var r=t("buffer"),i=r.Buffer;function p(c,o){for(var n in c)o[n]=c[n]}i.from&&i.alloc&&i.allocUnsafe&&i.allocUnsafeSlow?a.exports=r:(p(r,m),m.Buffer=h);function h(c,o,n){return i(c,o,n)}p(i,h),h.from=function(c,o,n){if(typeof c=="number")throw new TypeError("Argument must not be a number");return i(c,o,n)},h.alloc=function(c,o,n){if(typeof c!="number")throw new TypeError("Argument must be a number");var d=i(c);return o!==void 0?typeof n=="string"?d.fill(o,n):d.fill(o):d.fill(0),d},h.allocUnsafe=function(c){if(typeof c!="number")throw new TypeError("Argument must be a number");return i(c)},h.allocUnsafeSlow=function(c){if(typeof c!="number")throw new TypeError("Argument must be a number");return r.SlowBuffer(c)}},{buffer:12}],119:[function(t,a,m){a.exports=r;function r(p){var h=p._readableState;return h?h.objectMode||typeof p._duplexState=="number"?p.read():p.read(i(h)):null}function i(p){return p.buffer.length?p.buffer.head?p.buffer.head.data.length:p.buffer[0].length:p.length}},{}],120:[function(t,a,m){(function(r,i){var p=t("process/browser.js").nextTick,h=Function.prototype.apply,c=Array.prototype.slice,o={},n=0;m.setTimeout=function(){return new d(h.call(setTimeout,window,arguments),clearTimeout)},m.setInterval=function(){return new d(h.call(setInterval,window,arguments),clearInterval)},m.clearTimeout=m.clearInterval=function(g){g.close()};function d(g,E){this._id=g,this._clearFn=E}d.prototype.unref=d.prototype.ref=function(){},d.prototype.close=function(){this._clearFn.call(window,this._id)},m.enroll=function(g,E){clearTimeout(g._idleTimeoutId),g._idleTimeout=E},m.unenroll=function(g){clearTimeout(g._idleTimeoutId),g._idleTimeout=-1},m._unrefActive=m.active=function(g){clearTimeout(g._idleTimeoutId);var E=g._idleTimeout;E>=0&&(g._idleTimeoutId=setTimeout(function(){g._onTimeout&&g._onTimeout()},E))},m.setImmediate=typeof r=="function"?r:function(g){var E=n++,u=arguments.length<2?!1:c.call(arguments,1);return o[E]=!0,p(function(){o[E]&&(u?g.apply(null,u):g.call(null),m.clearImmediate(E))}),E},m.clearImmediate=typeof i=="function"?i:function(g){delete o[g]}}).call(this,t("timers").setImmediate,t("timers").clearImmediate)},{"process/browser.js":100,timers:120}],121:[function(t,a,m){var r=t("../prototype/is");a.exports=function(i){if(typeof i!="function"||!hasOwnProperty.call(i,"length"))return!1;try{if(typeof i.length!="number"||typeof i.call!="function"||typeof i.apply!="function")return!1}catch(p){return!1}return!r(i)}},{"../prototype/is":128}],122:[function(t,a,m){var r=t("../value/is"),i=t("../object/is"),p=t("../string/coerce"),h=t("./to-short-string"),c=function(o,n){return o.replace("%v",h(n))};a.exports=function(o,n,d){if(!i(d))throw new TypeError(c(n,o));if(!r(o)){if("default"in d)return d.default;if(d.isOptional)return null}var g=p(d.errorMessage);throw r(g)||(g=n),new TypeError(c(g,o))}},{"../object/is":125,"../string/coerce":129,"../value/is":131,"./to-short-string":124}],123:[function(t,a,m){a.exports=function(r){try{return r.toString()}catch(i){try{return String(r)}catch(p){return null}}}},{}],124:[function(t,a,m){var r=t("./safe-to-string"),i=/[\n\r\u2028\u2029]/g;a.exports=function(p){var h=r(p);return h===null?"<Non-coercible to string value>":(h.length>100&&(h=h.slice(0,99)+"\u2026"),h=h.replace(i,function(c){switch(c){case`
`:return"\\n";case"\r":return"\\r";case"\u2028":return"\\u2028";case"\u2029":return"\\u2029";default:throw new Error("Unexpected character")}}),h)}},{"./safe-to-string":123}],125:[function(t,a,m){var r=t("../value/is"),i={object:!0,function:!0,undefined:!0};a.exports=function(p){return r(p)?hasOwnProperty.call(i,typeof p):!1}},{"../value/is":131}],126:[function(t,a,m){var r=t("../lib/resolve-exception"),i=t("./is");a.exports=function(p){return i(p)?p:r(p,"%v is not a plain function",arguments[1])}},{"../lib/resolve-exception":122,"./is":127}],127:[function(t,a,m){var r=t("../function/is"),i=/^\s*class[\s{/}]/,p=Function.prototype.toString;a.exports=function(h){return!(!r(h)||i.test(p.call(h)))}},{"../function/is":121}],128:[function(t,a,m){var r=t("../object/is");a.exports=function(i){if(!r(i))return!1;try{return i.constructor?i.constructor.prototype===i:!1}catch(p){return!1}}},{"../object/is":125}],129:[function(t,a,m){var r=t("../value/is"),i=t("../object/is"),p=Object.prototype.toString;a.exports=function(h){if(!r(h))return null;if(i(h)){var c=h.toString;if(typeof c!="function"||c===p)return null}try{return""+h}catch(o){return null}}},{"../object/is":125,"../value/is":131}],130:[function(t,a,m){var r=t("../lib/resolve-exception"),i=t("./is");a.exports=function(p){return i(p)?p:r(p,"Cannot use %v",arguments[1])}},{"../lib/resolve-exception":122,"./is":131}],131:[function(t,a,m){var r=void 0;a.exports=function(i){return i!==r&&i!==null}},{}],132:[function(t,a,m){var r=t("punycode"),i=t("./util");m.parse=U,m.resolve=Z,m.resolveObject=ee,m.format=Y,m.Url=p;function p(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var h=/^([a-z0-9.+-]+:)/i,c=/:[0-9]*$/,o=/^(\/\/?(?!\/)[^\?\s]*)(\?[^\s]*)?$/,n=["<",">",'"',"`"," ","\r",`
`,"	"],d=["{","}","|","\\","^","`"].concat(n),g=["'"].concat(d),E=["%","/","?",";","#"].concat(g),u=["/","?","#"],_=255,C=/^[+a-z0-9A-Z_-]{0,63}$/,y=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,P={javascript:!0,"javascript:":!0},M={javascript:!0,"javascript:":!0},k={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},O=t("querystring");function U(A,j,b){if(A&&i.isObject(A)&&A instanceof p)return A;var N=new p;return N.parse(A,j,b),N}p.prototype.parse=function(A,j,b){if(!i.isString(A))throw new TypeError("Parameter 'url' must be a string, not "+typeof A);var N=A.indexOf("?"),z=N!==-1&&N<A.indexOf("#")?"?":"#",q=A.split(z),G=/\\/g;q[0]=q[0].replace(G,"/"),A=q.join(z);var D=A;if(D=D.trim(),!b&&A.split("#").length===1){var te=o.exec(D);if(te)return this.path=D,this.href=D,this.pathname=te[1],te[2]?(this.search=te[2],j?this.query=O.parse(this.search.substr(1)):this.query=this.search.substr(1)):j&&(this.search="",this.query={}),this}var le=h.exec(D);if(le){le=le[0];var ce=le.toLowerCase();this.protocol=ce,D=D.substr(le.length)}if(b||le||D.match(/^\/\/[^@\/]+@[^@\/]+/)){var pe=D.substr(0,2)==="//";pe&&!(le&&M[le])&&(D=D.substr(2),this.slashes=!0)}if(!M[le]&&(pe||le&&!k[le])){for(var Q=-1,L=0;L<u.length;L++){var I=D.indexOf(u[L]);I!==-1&&(Q===-1||I<Q)&&(Q=I)}var R,K;Q===-1?K=D.lastIndexOf("@"):K=D.lastIndexOf("@",Q),K!==-1&&(R=D.slice(0,K),D=D.slice(K+1),this.auth=decodeURIComponent(R)),Q=-1;for(var L=0;L<E.length;L++){var I=D.indexOf(E[L]);I!==-1&&(Q===-1||I<Q)&&(Q=I)}Q===-1&&(Q=D.length),this.host=D.slice(0,Q),D=D.slice(Q),this.parseHost(),this.hostname=this.hostname||"";var F=this.hostname[0]==="["&&this.hostname[this.hostname.length-1]==="]";if(!F)for(var B=this.hostname.split(/\./),L=0,V=B.length;L<V;L++){var $=B[L];if($&&!$.match(C)){for(var ie="",ue=0,S=$.length;ue<S;ue++)$.charCodeAt(ue)>127?ie+="x":ie+=$[ue];if(!ie.match(C)){var w=B.slice(0,L),W=B.slice(L+1),J=$.match(y);J&&(w.push(J[1]),W.unshift(J[2])),W.length&&(D="/"+W.join(".")+D),this.hostname=w.join(".");break}}}this.hostname.length>_?this.hostname="":this.hostname=this.hostname.toLowerCase(),F||(this.hostname=r.toASCII(this.hostname));var ae=this.port?":"+this.port:"",oe=this.hostname||"";this.host=oe+ae,this.href+=this.host,F&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),D[0]!=="/"&&(D="/"+D))}if(!P[ce])for(var L=0,V=g.length;L<V;L++){var v=g[L];if(D.indexOf(v)!==-1){var s=encodeURIComponent(v);s===v&&(s=escape(v)),D=D.split(v).join(s)}}var l=D.indexOf("#");l!==-1&&(this.hash=D.substr(l),D=D.slice(0,l));var T=D.indexOf("?");if(T!==-1?(this.search=D.substr(T),this.query=D.substr(T+1),j&&(this.query=O.parse(this.query)),D=D.slice(0,T)):j&&(this.search="",this.query={}),D&&(this.pathname=D),k[ce]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){var ae=this.pathname||"",H=this.search||"";this.path=ae+H}return this.href=this.format(),this};function Y(A){return i.isString(A)&&(A=U(A)),A instanceof p?A.format():p.prototype.format.call(A)}p.prototype.format=function(){var A=this.auth||"";A&&(A=encodeURIComponent(A),A=A.replace(/%3A/i,":"),A+="@");var j=this.protocol||"",b=this.pathname||"",N=this.hash||"",z=!1,q="";this.host?z=A+this.host:this.hostname&&(z=A+(this.hostname.indexOf(":")===-1?this.hostname:"["+this.hostname+"]"),this.port&&(z+=":"+this.port)),this.query&&i.isObject(this.query)&&Object.keys(this.query).length&&(q=O.stringify(this.query));var G=this.search||q&&"?"+q||"";return j&&j.substr(-1)!==":"&&(j+=":"),this.slashes||(!j||k[j])&&z!==!1?(z="//"+(z||""),b&&b.charAt(0)!=="/"&&(b="/"+b)):z||(z=""),N&&N.charAt(0)!=="#"&&(N="#"+N),G&&G.charAt(0)!=="?"&&(G="?"+G),b=b.replace(/[?#]/g,function(D){return encodeURIComponent(D)}),G=G.replace("#","%23"),j+z+b+G+N};function Z(A,j){return U(A,!1,!0).resolve(j)}p.prototype.resolve=function(A){return this.resolveObject(U(A,!1,!0)).format()};function ee(A,j){return A?U(A,!1,!0).resolveObject(j):j}p.prototype.resolveObject=function(A){if(i.isString(A)){var j=new p;j.parse(A,!1,!0),A=j}for(var b=new p,N=Object.keys(this),z=0;z<N.length;z++){var q=N[z];b[q]=this[q]}if(b.hash=A.hash,A.href==="")return b.href=b.format(),b;if(A.slashes&&!A.protocol){for(var G=Object.keys(A),D=0;D<G.length;D++){var te=G[D];te!=="protocol"&&(b[te]=A[te])}return k[b.protocol]&&b.hostname&&!b.pathname&&(b.path=b.pathname="/"),b.href=b.format(),b}if(A.protocol&&A.protocol!==b.protocol){if(!k[A.protocol]){for(var le=Object.keys(A),ce=0;ce<le.length;ce++){var pe=le[ce];b[pe]=A[pe]}return b.href=b.format(),b}if(b.protocol=A.protocol,!A.host&&!M[A.protocol]){for(var V=(A.pathname||"").split("/");V.length&&!(A.host=V.shift()););A.host||(A.host=""),A.hostname||(A.hostname=""),V[0]!==""&&V.unshift(""),V.length<2&&V.unshift(""),b.pathname=V.join("/")}else b.pathname=A.pathname;if(b.search=A.search,b.query=A.query,b.host=A.host||"",b.auth=A.auth,b.hostname=A.hostname||A.host,b.port=A.port,b.pathname||b.search){var Q=b.pathname||"",L=b.search||"";b.path=Q+L}return b.slashes=b.slashes||A.slashes,b.href=b.format(),b}var I=b.pathname&&b.pathname.charAt(0)==="/",R=A.host||A.pathname&&A.pathname.charAt(0)==="/",K=R||I||b.host&&A.pathname,F=K,B=b.pathname&&b.pathname.split("/")||[],V=A.pathname&&A.pathname.split("/")||[],$=b.protocol&&!k[b.protocol];if($&&(b.hostname="",b.port=null,b.host&&(B[0]===""?B[0]=b.host:B.unshift(b.host)),b.host="",A.protocol&&(A.hostname=null,A.port=null,A.host&&(V[0]===""?V[0]=A.host:V.unshift(A.host)),A.host=null),K=K&&(V[0]===""||B[0]==="")),R)b.host=A.host||A.host===""?A.host:b.host,b.hostname=A.hostname||A.hostname===""?A.hostname:b.hostname,b.search=A.search,b.query=A.query,B=V;else if(V.length)B||(B=[]),B.pop(),B=B.concat(V),b.search=A.search,b.query=A.query;else if(!i.isNullOrUndefined(A.search)){if($){b.hostname=b.host=B.shift();var ie=b.host&&b.host.indexOf("@")>0?b.host.split("@"):!1;ie&&(b.auth=ie.shift(),b.host=b.hostname=ie.shift())}return b.search=A.search,b.query=A.query,(!i.isNull(b.pathname)||!i.isNull(b.search))&&(b.path=(b.pathname?b.pathname:"")+(b.search?b.search:"")),b.href=b.format(),b}if(!B.length)return b.pathname=null,b.search?b.path="/"+b.search:b.path=null,b.href=b.format(),b;for(var ue=B.slice(-1)[0],S=(b.host||A.host||B.length>1)&&(ue==="."||ue==="..")||ue==="",w=0,W=B.length;W>=0;W--)ue=B[W],ue==="."?B.splice(W,1):ue===".."?(B.splice(W,1),w++):w&&(B.splice(W,1),w--);if(!K&&!F)for(;w--;w)B.unshift("..");K&&B[0]!==""&&(!B[0]||B[0].charAt(0)!=="/")&&B.unshift(""),S&&B.join("/").substr(-1)!=="/"&&B.push("");var J=B[0]===""||B[0]&&B[0].charAt(0)==="/";if($){b.hostname=b.host=J?"":B.length?B.shift():"";var ie=b.host&&b.host.indexOf("@")>0?b.host.split("@"):!1;ie&&(b.auth=ie.shift(),b.host=b.hostname=ie.shift())}return K=K||b.host&&B.length,K&&!J&&B.unshift(""),B.length?b.pathname=B.join("/"):(b.pathname=null,b.path=null),(!i.isNull(b.pathname)||!i.isNull(b.search))&&(b.path=(b.pathname?b.pathname:"")+(b.search?b.search:"")),b.auth=A.auth||b.auth,b.slashes=b.slashes||A.slashes,b.href=b.format(),b},p.prototype.parseHost=function(){var A=this.host,j=c.exec(A);j&&(j=j[0],j!==":"&&(this.port=j.substr(1)),A=A.substr(0,A.length-j.length)),A&&(this.hostname=A)}},{"./util":133,punycode:101,querystring:104}],133:[function(t,a,m){a.exports={isString:function(r){return typeof r=="string"},isObject:function(r){return typeof r=="object"&&r!==null},isNull:function(r){return r===null},isNullOrUndefined:function(r){return r==null}}},{}],134:[function(t,a,m){(function(r){a.exports=i;function i(h,c){if(p("noDeprecation"))return h;var o=!1;function n(){if(!o){if(p("throwDeprecation"))throw new Error(c);p("traceDeprecation")?console.trace(c):fe("warn","at node_modules/mqtt/dist/mqtt.js:14844",c),o=!0}return h.apply(this,arguments)}return n}function p(h){try{if(!r.localStorage)return!1}catch(o){return!1}var c=r.localStorage[h];return c==null?!1:String(c).toLowerCase()==="true"}}).call(this,typeof Ee!="undefined"?Ee:typeof self!="undefined"?self:typeof window!="undefined"?window:{})},{}],135:[function(t,a,m){a.exports=function(i){return i&&typeof i=="object"&&typeof i.copy=="function"&&typeof i.fill=="function"&&typeof i.readUInt8=="function"}},{}],136:[function(t,a,m){(function(r,i){var p=/%[sdj%]/g;m.format=function(Q){if(!Z(Q)){for(var L=[],I=0;I<arguments.length;I++)L.push(o(arguments[I]));return L.join(" ")}for(var I=1,R=arguments,K=R.length,F=String(Q).replace(p,function(V){if(V==="%%")return"%";if(I>=K)return V;switch(V){case"%s":return String(R[I++]);case"%d":return Number(R[I++]);case"%j":try{return JSON.stringify(R[I++])}catch($){return"[Circular]"}default:return V}}),B=R[I];I<K;B=R[++I])O(B)||!b(B)?F+=" "+B:F+=" "+o(B);return F},m.deprecate=function(Q,L){if(A(i.process))return function(){return m.deprecate(Q,L).apply(this,arguments)};if(r.noDeprecation===!0)return Q;var I=!1;function R(){if(!I){if(r.throwDeprecation)throw new Error(L);r.traceDeprecation?console.trace(L):fe("error","at node_modules/mqtt/dist/mqtt.js:14968",L),I=!0}return Q.apply(this,arguments)}return R};var h={},c;m.debuglog=function(Q){if(A(c)&&(c=r.env.NODE_DEBUG||""),Q=Q.toUpperCase(),!h[Q])if(new RegExp("\\b"+Q+"\\b","i").test(c)){var L=r.pid;h[Q]=function(){var I=m.format.apply(m,arguments);fe("error","at node_modules/mqtt/dist/mqtt.js:14990","%s %d: %s",Q,L,I)}}else h[Q]=function(){};return h[Q]};function o(Q,L){var I={seen:[],stylize:d};return arguments.length>=3&&(I.depth=arguments[2]),arguments.length>=4&&(I.colors=arguments[3]),k(L)?I.showHidden=L:L&&m._extend(I,L),A(I.showHidden)&&(I.showHidden=!1),A(I.depth)&&(I.depth=2),A(I.colors)&&(I.colors=!1),A(I.customInspect)&&(I.customInspect=!0),I.colors&&(I.stylize=n),E(I,Q,I.depth)}m.inspect=o,o.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},o.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"};function n(Q,L){var I=o.styles[L];return I?"\x1B["+o.colors[I][0]+"m"+Q+"\x1B["+o.colors[I][1]+"m":Q}function d(Q,L){return Q}function g(Q){var L={};return Q.forEach(function(I,R){L[I]=!0}),L}function E(Q,L,I){if(Q.customInspect&&L&&q(L.inspect)&&L.inspect!==m.inspect&&!(L.constructor&&L.constructor.prototype===L)){var R=L.inspect(I,Q);return Z(R)||(R=E(Q,R,I)),R}var K=u(Q,L);if(K)return K;var F=Object.keys(L),B=g(F);if(Q.showHidden&&(F=Object.getOwnPropertyNames(L)),z(L)&&(F.indexOf("message")>=0||F.indexOf("description")>=0))return _(L);if(F.length===0){if(q(L)){var V=L.name?": "+L.name:"";return Q.stylize("[Function"+V+"]","special")}if(j(L))return Q.stylize(RegExp.prototype.toString.call(L),"regexp");if(N(L))return Q.stylize(Date.prototype.toString.call(L),"date");if(z(L))return _(L)}var $="",ie=!1,ue=["{","}"];if(M(L)&&(ie=!0,ue=["[","]"]),q(L)){var S=L.name?": "+L.name:"";$=" [Function"+S+"]"}if(j(L)&&($=" "+RegExp.prototype.toString.call(L)),N(L)&&($=" "+Date.prototype.toUTCString.call(L)),z(L)&&($=" "+_(L)),F.length===0&&(!ie||L.length==0))return ue[0]+$+ue[1];if(I<0)return j(L)?Q.stylize(RegExp.prototype.toString.call(L),"regexp"):Q.stylize("[Object]","special");Q.seen.push(L);var w;return ie?w=C(Q,L,I,B,F):w=F.map(function(W){return y(Q,L,I,B,W,ie)}),Q.seen.pop(),P(w,$,ue)}function u(Q,L){if(A(L))return Q.stylize("undefined","undefined");if(Z(L)){var I="'"+JSON.stringify(L).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return Q.stylize(I,"string")}if(Y(L))return Q.stylize(""+L,"number");if(k(L))return Q.stylize(""+L,"boolean");if(O(L))return Q.stylize("null","null")}function _(Q){return"["+Error.prototype.toString.call(Q)+"]"}function C(Q,L,I,R,K){for(var F=[],B=0,V=L.length;B<V;++B)pe(L,String(B))?F.push(y(Q,L,I,R,String(B),!0)):F.push("");return K.forEach(function($){$.match(/^\d+$/)||F.push(y(Q,L,I,R,$,!0))}),F}function y(Q,L,I,R,K,F){var B,V,$;if($=Object.getOwnPropertyDescriptor(L,K)||{value:L[K]},$.get?$.set?V=Q.stylize("[Getter/Setter]","special"):V=Q.stylize("[Getter]","special"):$.set&&(V=Q.stylize("[Setter]","special")),pe(R,K)||(B="["+K+"]"),V||(Q.seen.indexOf($.value)<0?(O(I)?V=E(Q,$.value,null):V=E(Q,$.value,I-1),V.indexOf(`
`)>-1&&(F?V=V.split(`
`).map(function(ie){return"  "+ie}).join(`
`).substr(2):V=`
`+V.split(`
`).map(function(ie){return"   "+ie}).join(`
`))):V=Q.stylize("[Circular]","special")),A(B)){if(F&&K.match(/^\d+$/))return V;B=JSON.stringify(""+K),B.match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(B=B.substr(1,B.length-2),B=Q.stylize(B,"name")):(B=B.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),B=Q.stylize(B,"string"))}return B+": "+V}function P(Q,L,I){var R=Q.reduce(function(K,F){return F.indexOf(`
`)>=0,K+F.replace(/\u001b\[\d\d?m/g,"").length+1},0);return R>60?I[0]+(L===""?"":L+`
 `)+" "+Q.join(`,
  `)+" "+I[1]:I[0]+L+" "+Q.join(", ")+" "+I[1]}function M(Q){return Array.isArray(Q)}m.isArray=M;function k(Q){return typeof Q=="boolean"}m.isBoolean=k;function O(Q){return Q===null}m.isNull=O;function U(Q){return Q==null}m.isNullOrUndefined=U;function Y(Q){return typeof Q=="number"}m.isNumber=Y;function Z(Q){return typeof Q=="string"}m.isString=Z;function ee(Q){return typeof Q=="symbol"}m.isSymbol=ee;function A(Q){return Q===void 0}m.isUndefined=A;function j(Q){return b(Q)&&D(Q)==="[object RegExp]"}m.isRegExp=j;function b(Q){return typeof Q=="object"&&Q!==null}m.isObject=b;function N(Q){return b(Q)&&D(Q)==="[object Date]"}m.isDate=N;function z(Q){return b(Q)&&(D(Q)==="[object Error]"||Q instanceof Error)}m.isError=z;function q(Q){return typeof Q=="function"}m.isFunction=q;function G(Q){return Q===null||typeof Q=="boolean"||typeof Q=="number"||typeof Q=="string"||typeof Q=="symbol"||typeof Q=="undefined"}m.isPrimitive=G,m.isBuffer=t("./support/isBuffer");function D(Q){return Object.prototype.toString.call(Q)}function te(Q){return Q<10?"0"+Q.toString(10):Q.toString(10)}var le=["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"];function ce(){var Q=new Date,L=[te(Q.getHours()),te(Q.getMinutes()),te(Q.getSeconds())].join(":");return[Q.getDate(),le[Q.getMonth()],L].join(" ")}m.log=function(){fe("log","at node_modules/mqtt/dist/mqtt.js:15436","%s - %s",ce(),m.format.apply(m,arguments))},m.inherits=t("inherits"),m._extend=function(Q,L){if(!L||!b(L))return Q;for(var I=Object.keys(L),R=I.length;R--;)Q[I[R]]=L[I[R]];return Q};function pe(Q,L){return Object.prototype.hasOwnProperty.call(Q,L)}}).call(this,t("_process"),typeof Ee!="undefined"?Ee:typeof self!="undefined"?self:typeof window!="undefined"?window:{})},{"./support/isBuffer":135,_process:100,inherits:88}],137:[function(t,a,m){(function(r,i){var p=t("readable-stream").Transform,h=t("duplexify"),c=t("ws"),o=t("safe-buffer").Buffer;a.exports=d;function n(g,E,u){var _=new p({objectMode:g.objectMode});return _._write=E,_._flush=u,_}function d(g,E,u){var _,C,y=r.title==="browser",P=!!i.WebSocket,M=y?ee:Z;E&&!Array.isArray(E)&&typeof E=="object"&&(u=E,E=null,(typeof u.protocol=="string"||Array.isArray(u.protocol))&&(E=u.protocol)),u||(u={}),u.objectMode===void 0&&(u.objectMode=!(u.binary===!0||u.binary===void 0));var k=n(u,M,A);u.objectMode||(k._writev=G);var O=u.browserBufferSize||1024*512,U=u.browserBufferTimeout||1e3;typeof g=="object"?C=g:(P&&y?C=new c(g,E):C=new c(g,E,u),C.binaryType="arraybuffer"),C.readyState===C.OPEN?_=k:(_=h.obj(),C.onopen=j),_.socket=C,C.onclose=b,C.onerror=N,C.onmessage=z,k.on("close",q);var Y=!u.objectMode;function Z(D,te,le){if(C.readyState!==C.OPEN){le();return}Y&&typeof D=="string"&&(D=o.from(D,"utf8")),C.send(D,le)}function ee(D,te,le){if(C.bufferedAmount>O){setTimeout(ee,U,D,te,le);return}Y&&typeof D=="string"&&(D=o.from(D,"utf8"));try{C.send(D)}catch(ce){return le(ce)}le()}function A(D){C.close(),D()}function j(){_.setReadable(k),_.setWritable(k),_.emit("connect")}function b(){_.end(),_.destroy()}function N(D){_.destroy(D)}function z(D){var te=D.data;te instanceof ArrayBuffer?te=o.from(te):te=o.from(te,"utf8"),k.push(te)}function q(){C.close()}function G(D,te){for(var le=new Array(D.length),ce=0;ce<D.length;ce++)typeof D[ce].chunk=="string"?le[ce]=o.from(D[ce],"utf8"):le[ce]=D[ce].chunk;this._write(o.concat(le),"binary",te)}return _}}).call(this,t("_process"),typeof Ee!="undefined"?Ee:typeof self!="undefined"?self:typeof window!="undefined"?window:{})},{_process:100,duplexify:19,"readable-stream":116,"safe-buffer":118,ws:138}],138:[function(t,a,m){var r=null;typeof WebSocket!="undefined"?r=WebSocket:typeof MozWebSocket!="undefined"?r=MozWebSocket:typeof window!="undefined"&&(r=window.WebSocket||window.MozWebSocket),a.exports=r},{}],139:[function(t,a,m){a.exports=r;function r(i,p){if(i&&p)return r(i)(p);if(typeof i!="function")throw new TypeError("need wrapper function");return Object.keys(i).forEach(function(c){h[c]=i[c]}),h;function h(){for(var c=new Array(arguments.length),o=0;o<c.length;o++)c[o]=arguments[o];var n=i.apply(this,c),d=c[c.length-1];return typeof n=="function"&&n!==d&&Object.keys(d).forEach(function(g){n[g]=d[g]}),n}}},{}],140:[function(t,a,m){a.exports=i;var r=Object.prototype.hasOwnProperty;function i(){for(var p={},h=0;h<arguments.length;h++){var c=arguments[h];for(var o in c)r.call(c,o)&&(p[o]=c[o])}return p}},{}]},{},[9])(9)})})(Ut);var Vo=Ut.exports,qo=Wo(Vo),Ae=null;function Ko(e="/sys/todo/update"){Ae&&(Ae.end(),Ae=null);let f="mqttwsdev.eykj.cn",t=443,a="/mqtt",m="tfl01",r="987123",i=!0,p=`uniapp_${Math.random().toString(16).slice(3)}`,h={clientId:p,clean:!0,keepalive:60,connectTimeout:4e3,reconnectPeriod:1e3,port:t,hostname:f,username:m,password:r};fe("log","at utils/mqtt-helper.js:44","\u8FDE\u63A5MQTT\u670D\u52A1\u5668:",f),fe("log","at utils/mqtt-helper.js:45","\u7AEF\u53E3:",t),fe("log","at utils/mqtt-helper.js:46","\u8DEF\u5F84:",a),fe("log","at utils/mqtt-helper.js:47","\u4F7F\u7528SSL:",i),fe("log","at utils/mqtt-helper.js:48","\u5BA2\u6237\u7AEFID:",p),fe("log","at utils/mqtt-helper.js:60","APP\u73AF\u5883\u8FDE\u63A5");let c=`wxs://${f}:${t}${a}`;return fe("log","at utils/mqtt-helper.js:62","APP\u8FDE\u63A5URL:",c),Ae=qo.connect(c,h),Ae.on("connect",()=>{fe("log","at utils/mqtt-helper.js:68","MQTT\u8FDE\u63A5\u6210\u529F!"),e&&Ae.subscribe(e,o=>{o?fe("error","at utils/mqtt-helper.js:74","\u8BA2\u9605\u5931\u8D25:",o):fe("log","at utils/mqtt-helper.js:72",`\u5DF2\u8BA2\u9605\u4E3B\u9898: ${e}`)})}),Ae.on("message",(o,n)=>{fe("log","at utils/mqtt-helper.js:82",`\u6536\u5230\u6D88\u606F\uFF0C\u4E3B\u9898: ${o}`);try{let d=JSON.parse(n.toString());fe("log","at utils/mqtt-helper.js:85","\u89E3\u6790\u540E\u7684\u6570\u636E:",d)}catch(d){fe("log","at utils/mqtt-helper.js:87","\u6D88\u606F\u5185\u5BB9:",n.toString())}}),Ae.on("error",o=>{fe("error","at utils/mqtt-helper.js:93","MQTT\u9519\u8BEF:",o)}),Ae.on("reconnect",()=>{fe("log","at utils/mqtt-helper.js:98","MQTT\u6B63\u5728\u91CD\u8FDE...")}),Ae.on("close",()=>{fe("log","at utils/mqtt-helper.js:103","MQTT\u8FDE\u63A5\u5DF2\u5173\u95ED")}),Ae}function Yo(){Ae&&(Ae.end(),Ae=null,fe("log","at utils/mqtt-helper.js:116","MQTT\u8FDE\u63A5\u5DF2\u65AD\u5F00"))}function Go(e){return Ae?(Ae.on("message",e),!0):!1}var Xo="/static/image/ding.png",Jo="/static/image/commission.png",Zo={container:{"":{flex:1,position:"relative"}},"background-image":{"":{position:"absolute",top:0,left:0}},"content-wrapper":{"":{paddingLeft:"26rpx",paddingRight:"26rpx",position:"absolute",top:0,left:0,right:0,bottom:0,flex:1}},header:{"":{position:"fixed",top:0,left:0,right:0,bottom:0,paddingTop:"50rpx",fontSize:"34rpx",fontWeight:"600",textAlign:"center",display:"flex",justifyContent:"center",alignItems:"center"}},"status-bar":{"":{height:44}},sear_inp:{"":{"!borderWidth":0,"!borderStyle":"solid","!borderColor":"#ffffff",backgroundColor:"rgba(255,255,255,0.08)",color:"rgba(255,255,255,0.8)"}},"u-border":{"":{borderWidth:0,borderColor:"#000000"}},filter_icon:{"":{height:35,width:28,display:"flex",justifyContent:"center",alignItems:"center",borderRadius:"10rpx",backgroundColor:"rgba(255,255,255,0.08)"}},"nav-bar":{"":{height:44,justifyContent:"center",alignItems:"center"}},"nav-title":{"":{fontSize:"34rpx",color:"rgba(255,255,255,0.85)"}},"filter-wrapper":{"":{paddingTop:0,paddingRight:"10rpx",paddingBottom:0,paddingLeft:"10rpx",zIndex:9999}},"u-dropdown":{"":{flex:1}},"u-select__label":{"":{flexDirection:"row",height:100}},"u-dropdown__content":{"":{"!borderRadius":"20rpx",backgroundColor:"rgba(22,23,27,0.95)"}},"u-dropdown__content__popup":{".u-dropdown__content ":{backgroundColor:"#000000"},"":{marginTop:"5rpx","!borderRadius":"10rpx"}},"uni-modal":{"":{backgroundColor:"#16171b",color:"rgba(255,255,255,0.85)"}},"u-dropdown-item__scroll":{"":{backgroundColor:"#28292e",color:"rgba(255,255,255,0.85)"}},"u-dropdown__menu__item__text":{"":{"!color":"rgba(255,255,255,0.85)",fontSize:"28rpx"}},"u-line":{"":{"!borderWidth":0,"!borderColor":"#000000"},".u-modal ":{"!borderColor":"rgba(255,255,255,0.25)"}},"u-cell":{"":{"!borderBottomWidth":"1rpx","!borderBottomStyle":"solid","!borderBottomColor":"rgba(214,215,217,0.2)","!borderBottomWidth:last-child":"0rpx","!borderBottomStyle:last-child":"solid","!borderBottomColor:last-child":"rgba(214,215,217,0.5)"}},"u-dropdown__menu":{"":{height:"100rpx",backgroundColor:"rgba(240,240,240,0.08)"}},"u-dropdown__menu__item":{"":{height:"100rpx",justifyContent:"center",alignItems:"center"}},"u-select__list__item":{"":{paddingTop:"24rpx",paddingRight:"32rpx",paddingBottom:"24rpx",paddingLeft:"32rpx",backgroundColor:"rgba(0,0,0,0.75)",borderBottomWidth:"1rpx",borderBottomStyle:"solid",borderBottomColor:"rgba(255,255,255,0.08)","borderBottomWidth:last-child":0}},"u-modal":{"":{"!borderWidth":"1rpx","!borderStyle":"solid","!borderColor":"rgba(255,255,255,0.1)"}},"u-modal__title":{".u-modal ":{paddingTop:"32rpx",paddingRight:"32rpx",paddingBottom:"32rpx",paddingLeft:"32rpx",fontSize:"32rpx",fontWeight:"500","!color":"rgba(255,255,255,0.75)"}},"u-textarea":{".u-modal .u-modal__content .modal-content ":{borderRadius:"8rpx","!borderWidth":"1rpx","!borderStyle":"solid","!borderColor":"rgba(255,255,255,0.1)","!minHeight":"200rpx"}},"u-textarea__field":{".u-modal .u-modal__content .modal-content ":{fontSize:"28rpx",lineHeight:1.5}},"u-select__list__item__text":{"":{fontSize:"28rpx",color:"rgba(255,255,255,0.85)"},".u-select__list__item--active ":{color:"#177DDC"}},"u-select__list__item--active":{"":{backgroundColor:"rgba(255,255,255,0.08)"}},content:{"":{paddingTop:"150rpx",zIndex:500,flex:1}},"todo-item":{"":{marginTop:"32rpx",marginRight:"10rpx",marginBottom:0,marginLeft:"10rpx",borderRadius:"12rpx",backgroundColor:"rgba(255,255,255,0.0362)",borderWidth:"1rpx",borderStyle:"solid",borderColor:"rgba(255,255,255,0.0972)"}},"item-wrapper":{"":{paddingTop:"32rpx",paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx"}},"item-header":{"":{flexDirection:"row",alignItems:"center",marginBottom:"24rpx"}},"header-left":{"":{marginRight:"16rpx"}},"item-icon":{"":{width:"48rpx",height:"48rpx",borderRadius:"8rpx",backgroundColor:"#F8CF8D",paddingTop:"8rpx",paddingRight:"8rpx",paddingBottom:"8rpx",paddingLeft:"8rpx"}},"header-middle":{"":{flex:1}},"item-title":{"":{fontSize:"32rpx",color:"rgba(255,255,255,0.85)",lines:2,textOverflow:"ellipsis"}},"item-time":{"":{fontSize:"24rpx",color:"rgba(255,255,255,0.45)"}},"content-row":{"":{marginBottom:"16rpx"}},label:{"":{fontSize:"28rpx",color:"rgba(255,255,255,0.65)"}},value:{"":{fontSize:"28rpx",color:"rgba(255,255,255,0.65)"}},"item-footer":{"":{flexDirection:"row",justifyContent:"center",alignItems:"center",marginTop:"24rpx",borderTopWidth:"1rpx",borderTopStyle:"solid",borderTopColor:"rgba(255,255,255,0.08)"}},"btn-reject":{"":{flex:1,textAlign:"center",paddingTop:"24rpx",paddingRight:0,paddingBottom:"24rpx",paddingLeft:0,fontSize:"28rpx",justifyContent:"center",alignItems:"center",color:"rgba(255,255,255,0.75)"}},divider:{"":{color:"rgba(255,255,255,0.08)",marginTop:0,marginRight:"32rpx",marginBottom:0,marginLeft:"32rpx"}},"btn-approve":{"":{flex:1,justifyContent:"center",paddingTop:"24rpx",paddingRight:0,paddingBottom:"24rpx",paddingLeft:0,alignItems:"center",textAlign:"center",fontSize:"28rpx",color:"#177DDC"}},"btn-processed":{"":{fontSize:"28rpx",color:"rgba(255,255,255,0.45)"}},"empty-state":{"":{paddingTop:"120rpx",paddingRight:0,paddingBottom:"120rpx",paddingLeft:0,alignItems:"center",justifyContent:"center"}},"load-more":{"":{paddingTop:"32rpx",paddingRight:0,paddingBottom:"32rpx",paddingLeft:0,alignItems:"center"}},"modal-content":{"":{width:100}},"filter-container":{"":{display:"flex",flexDirection:"row",alignItems:"center",marginTop:"20rpx"}},"up-dropdown":{".filter-container ":{flex:1,marginRight:"20rpx"}},"up-dropdown-item":{"":{backgroundColor:"rgba(255,255,255,0.08)",borderRadius:"12rpx",height:"56rpx",lineHeight:"56rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",color:"rgba(255,255,255,0.8)"}},"up-dropdown-item__title":{"":{color:"rgba(255,255,255,0.8)",fontSize:"28rpx"}},"up-dropdown-item__options":{"":{backgroundColor:"rgba(22,23,27,0.95)",borderRadius:"12rpx",marginTop:"10rpx"}},"up-dropdown-item__option":{"":{height:"80rpx",lineHeight:"80rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",color:"rgba(255,255,255,0.8)",fontSize:"28rpx",borderBottomWidth:"1rpx",borderBottomStyle:"solid",borderBottomColor:"rgba(255,255,255,0.08)"}},"up-dropdown-item__option--active":{"":{color:"#177DDC",backgroundColor:"rgba(255,255,255,0.08)"}},"select-item":{"":{flex:1,paddingRight:"10rpx",borderRadius:"12rpx",marginRight:"20rpx",color:"rgba(255,255,255,0.8)",backgroundColor:"rgba(255,255,255,0.08)",display:"flex",alignItems:"center","marginRight:last-child":"10rpx"}},"uni-data-select":{".select-item ":{flex:1}},"uni-select":{".select-item .uni-data-select ":{"!borderWidth":0,"!borderColor":"#000000","!backgroundColor":"rgba(0,0,0,0)"}},"uni-select__input-box":{".select-item .uni-data-select .uni-select ":{height:"70rpx","!borderWidth":0,"!borderColor":"#000000",paddingTop:0,paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx","!backgroundColor":"rgba(0,0,0,0)"},".custom-select ":{"!backgroundColor":"rgba(0,0,0,0)","!borderWidth":0,"!borderColor":"#000000","!color":"rgba(255,255,255,0.85)"}},"uni-select__input-text":{".select-item .uni-data-select .uni-select .uni-select__input-box ":{"!color":"rgba(255,255,255,0.85)",fontSize:"28rpx"}},"uni-select__input-placeholder":{".select-item .uni-data-select .uni-select .uni-select__input-box ":{"!color":"rgba(255,255,255,0.45)",fontSize:"28rpx"},".custom-select ":{"!color":"rgba(255,255,255,0.45)"}},"uni-select__selector":{".select-item .uni-data-select ":{"!backgroundColor":"rgba(0,0,0,0.85)","!borderWidth":0,"!borderColor":"#000000",paddingTop:0,paddingRight:0,paddingBottom:0,paddingLeft:0,marginTop:2,"!borderRadius":0},".custom-select ":{"!backgroundColor":"rgba(0,0,0,0.85)","!borderWidth":0,"!borderColor":"#000000"}},"uni-select__selector-item":{".select-item .uni-data-select .uni-select__selector ":{paddingTop:"20rpx",paddingRight:"32rpx",paddingBottom:"20rpx",paddingLeft:"32rpx","!color":"rgba(255,255,255,0.85)",fontSize:"28rpx","!borderWidth":0,"!borderColor":"#000000","!backgroundColor":"rgba(0,0,0,0)","!backgroundColor:hover":"rgba(255,255,255,0.08)"},".select-item .uni-data-select .uni-select__selector .active":{"!color":"#177DDC"},".custom-select ":{"!color":"rgba(255,255,255,0.85)",fontSize:"28rpx",paddingTop:"20rpx",paddingRight:"32rpx",paddingBottom:"20rpx",paddingLeft:"32rpx"},".custom-select .active":{"!color":"#177DDC"}},"zxz-uni-data-select":{"":{backgroundColor:"rgba(255,255,255,0.08)",borderRadius:"12rpx",height:"56rpx",lineHeight:"56rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",color:"rgba(255,255,255,0.8)"}},"zxz-uni-data-select__input":{"":{color:"rgba(255,255,255,0.8)",fontSize:"28rpx"}},"zxz-uni-data-select__list":{"":{backgroundColor:"rgba(22,23,27,0.95)",borderRadius:"12rpx",marginTop:"10rpx"}},"zxz-uni-data-select__item":{"":{height:"80rpx",lineHeight:"80rpx",paddingTop:0,paddingRight:"20rpx",paddingBottom:0,paddingLeft:"20rpx",color:"rgba(255,255,255,0.8)",fontSize:"28rpx",borderBottomWidth:"1rpx",borderBottomStyle:"solid",borderBottomColor:"rgba(255,255,255,0.08)"}},"zxz-uni-data-select__item--active":{"":{color:"#177DDC",backgroundColor:"rgba(255,255,255,0.08)"}},"custom-select":{"":{backgroundColor:"rgba(255,255,255,0.08)",borderRadius:"12rpx",color:"rgba(255,255,255,0.85)",fontSize:"28rpx",height:"70rpx",lineHeight:"70rpx",paddingTop:0,paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx"}},picker:{"":{height:"70rpx",lineHeight:"70rpx",paddingTop:0,paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx",color:"rgba(255,255,255,0.85)",fontSize:"28rpx",backgroundColor:"rgba(255,255,255,0.08)",borderRadius:"12rpx"}},"custom-picker":{"":{height:"70rpx",lineHeight:"70rpx",paddingTop:0,paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx",color:"rgba(255,255,255,0.85)",fontSize:"28rpx",backgroundColor:"rgba(255,255,255,0.08)",borderRadius:"12rpx",display:"flex",justifyContent:"space-between",alignItems:"center"}},"arrow-icon":{"":{width:"24rpx",height:"24rpx"}},"picker-options-container":{"":{position:"absolute",top:"80rpx",left:0,zIndex:999,color:"rgba(255,255,255,0.85)",backgroundColor:"#ffffff"}},"picker-options":{"":{width:100,maxHeight:"300rpx",backgroundColor:"rgba(22,23,27,0.95)",borderRadius:"12rpx",boxShadow:"0 4rpx 12rpx rgba(0, 0, 0, 0.15)"}},"custom-picker-option":{"":{height:"80rpx",lineHeight:"80rpx",paddingTop:0,paddingRight:"32rpx",paddingBottom:0,paddingLeft:"32rpx",color:"rgba(255,255,255,0.85)",fontSize:"28rpx",borderBottomWidth:"1rpx",borderBottomStyle:"solid",borderBottomColor:"rgba(255,255,255,0.08)","borderBottomWidth:last-child":0,"borderBottomColor:last-child":"#000000","backgroundColor:hover":"rgba(255,255,255,0.08)"}},mask:{"":{position:"absolute",top:0,left:0,width:100,height:100,backgroundColor:"rgba(0,0,0,0.5)",zIndex:998}}},$o={inheritAttrs:!1,components:{customHeader:kt},data(){return{value1:0,value2:1,title1:"\u5168\u90E8",title2:"\u5F85\u5904\u7406",options1:[{name:"\u5168\u90E8",id:0},{name:"\u5BA1\u6279",id:1},{name:"\u901A\u77E5",id:2}],options2:[{label:"\u5F85\u5904\u7406",value:1},{label:"\u5DF2\u5904\u7406",value:2}],screenWidth:0,screenHeight:0,todoList:[],searchText:"",currentPage:1,pageSize:10,total:2,isRefreshing:!1,loadMoreStatus:"nomore",isPulling:!1,showRefusePopup:!1,refuseReason:"",currentItem:null,originalList:[],dropdownStyle:{backgroundColor:"#000",color:"#fff",boxShadow:"none"},userInfo:null,mqttClient:null,activeTopic:"/sys/todo/update"}},onShow(){this.currentPage=1,this.getTodoList(),this.getUserInfo(),this.connectMQTT()},onHide(){this.disconnectMQTT()},onUnload(){this.disconnectMQTT()},created(){let e=uni.getSystemInfoSync();this.screenWidth=e.windowWidth,this.screenHeight=e.windowHeight},methods:{connectMQTT(){fe("log","at pages/commission/commission.nvue:251","\u5F00\u59CB\u8FDE\u63A5MQTT..."),this.mqttClient=Ko(this.activeTopic),this.mqttClient&&this._setupMqttEventHandlers()},_setupMqttEventHandlers(){this.mqttClient&&(this.mqttClient.on("connect",()=>{fe("log","at pages/commission/commission.nvue:267","MQTT\u8FDE\u63A5\u6210\u529F\uFF0C\u5DF2\u8BA2\u9605\u4E3B\u9898:",this.activeTopic)}),Go((e,f)=>{fe("log","at pages/commission/commission.nvue:272","\u6536\u5230MQTT\u6D88\u606F:",e);try{let t=JSON.parse(f.toString());if(fe("log","at pages/commission/commission.nvue:276","\u89E3\u6790\u540E\u7684\u6D88\u606F\u6570\u636E:",t),Array.isArray(t)&&this.userInfo&&this.userInfo.account){let a=this.userInfo.account;fe("log","at pages/commission/commission.nvue:281","\u5F53\u524D\u7528\u6237\u8D26\u53F7:",a),t.includes(a)&&(fe("log","at pages/commission/commission.nvue:285","\u5F85\u529E\u9875\u9762\uFF1A\u5F53\u524D\u7528\u6237\u5728\u63A8\u9001\u5217\u8868\u4E2D\uFF0C\u5237\u65B0\u5F85\u529E\u5217\u8868"),this.currentPage=1,this.getTodoList())}}catch(t){fe("error","at pages/commission/commission.nvue:292","MQTT\u6D88\u606F\u89E3\u6790\u9519\u8BEF:",t)}}),this.mqttClient.on("error",e=>{fe("error","at pages/commission/commission.nvue:298","MQTT\u8FDE\u63A5\u9519\u8BEF:",e.message||"\u672A\u77E5\u9519\u8BEF")}),this.mqttClient.on("close",()=>{fe("log","at pages/commission/commission.nvue:303","MQTT\u8FDE\u63A5\u5DF2\u65AD\u5F00")}))},disconnectMQTT(){fe("log","at pages/commission/commission.nvue:309","\u65AD\u5F00MQTT\u8FDE\u63A5"),Yo(),this.mqttClient=null},getUserInfo(){try{let e=uni.getStorageSync("user");e?(typeof e=="object"?this.userInfo=e:this.userInfo=JSON.parse(e),fe("log","at pages/commission/commission.nvue:326","\u83B7\u53D6\u5230\u7528\u6237\u4FE1\u606F:",this.userInfo)):fe("log","at pages/commission/commission.nvue:328","\u672A\u627E\u5230\u7528\u6237\u4FE1\u606F")}catch(e){fe("error","at pages/commission/commission.nvue:331","\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25:",e);try{let f=uni.getStorageSync("user");f&&typeof f=="object"&&(this.userInfo=f,fe("log","at pages/commission/commission.nvue:337","\u76F4\u63A5\u4F7F\u7528\u5BF9\u8C61\u7C7B\u578B\u7684\u7528\u6237\u4FE1\u606F"))}catch(f){fe("error","at pages/commission/commission.nvue:340","\u65E0\u6CD5\u83B7\u53D6\u7528\u6237\u4FE1\u606F:",f)}}},getTodoList(){return ke(this,null,function*(){try{let e={page:this.currentPage,perPage:this.pageSize,status:this.value2,type:this.value1,keyWord:this.searchText},f=yield Me.post("/todo/get_ls",e);if(fe("log","at pages/commission/commission.nvue:356","\u83B7\u53D6\u5F85\u529E\u5217\u8868\u54CD\u5E94:",f.data.total),f.status===0){this.currentPage===1?this.todoList=f.data.items:this.todoList=[...this.todoList,...f.data.items],this.total=f.data.total;let t=f.data.items.filter(a=>a.status===0).length;fe("log","at pages/commission/commission.nvue:368","\u5F85\u529E\u9875\u9762\u672A\u5904\u7406\u6570\u91CF:",t),t>0?uni.setTabBarBadge({index:2,text:t.toString(),complete:a=>{fe("log","at pages/commission/commission.nvue:375","\u8BBE\u7F6E\u89D2\u6807\u7ED3\u679C:",a)}}):uni.removeTabBarBadge({index:2,complete:a=>{fe("log","at pages/commission/commission.nvue:382","\u79FB\u9664\u89D2\u6807\u7ED3\u679C:",a)}}),this.loadMoreStatus=this.todoList.length>=this.total?"nomore":"loadmore"}else uni.showToast({title:f.message||"\u83B7\u53D6\u6570\u636E\u5931\u8D25",icon:"none"})}catch(e){fe("error","at pages/commission/commission.nvue:395","\u83B7\u53D6\u5F85\u529E\u5217\u8868\u5931\u8D25:",e),uni.showToast({title:"\u7F51\u7EDC\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5",icon:"none"})}finally{this.isRefreshing=!1}})},formatDynamicFields(e){try{let t=JSON.parse(e).find(a=>a.title==="\u64CD\u4F5C\u5458");return t?`${t.title}\uFF1A${t.value}`:"\u65E0\u64CD\u4F5C\u5458\u4FE1\u606F"}catch(f){return fe("error","at pages/commission/commission.nvue:412","\u89E3\u6790 dynamicFields \u5931\u8D25:",f),"\u52A8\u6001\u4FE1\u606F\u89E3\u6790\u5931\u8D25"}},formatDynamicFieldss(e){try{return JSON.parse(e).map(t=>`${t.title}\uFF1A${t.value}`).join("\uFF0C")}catch(f){return fe("error","at pages/commission/commission.nvue:422","\u89E3\u6790 dynamicFields \u5931\u8D25:",f),"\u52A8\u6001\u4FE1\u606F\u89E3\u6790\u5931\u8D25"}},handleSearchInput(e){fe("log","at pages/commission/commission.nvue:428","\u9009\u62E9\u7684\u503C:",e),this.searchTimer&&clearTimeout(this.searchTimer),this.searchTimer=setTimeout(()=>{this.searchText=e,this.currentPage=1,this.getTodoList()},300)},handleClear(){this.searchText="",this.currentPage=1,this.getTodoList()},handleTourClick(e){uni.navigateTo({url:`/pages/commission/components/tour?id=${e.id}&&submitter=${e.submitter}`})},handleItemClick(e){uni.navigateTo({url:`/pages/commission/components/detail?id=${e.id}`})},showRejectDialog(e){this.currentItem=e,this.showRefusePopup=!0},handleApprove(e){return ke(this,null,function*(){uni.showModal({title:"\u786E\u8BA4",content:"\u786E\u5B9A\u540C\u610F\u8BE5\u7533\u8BF7\u5417\uFF1F",success:f=>ke(this,null,function*(){if(f.confirm)try{let t={id:e.id,action:1},a=yield Me.post("/todo/post_modify",t);a.status===0?(uni.showToast({title:"\u5904\u7406\u6210\u529F",icon:"none"}),this.getTodoList()):uni.showToast({title:a.msg||"\u5904\u7406\u5931\u8D25",icon:"none"})}catch(t){fe("error","at pages/commission/commission.nvue:488","\u5904\u7406\u540C\u610F\u64CD\u4F5C\u5931\u8D25:",t),uni.showToast({title:"\u7F51\u7EDC\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5",icon:"none"})}})})})},handleEndCheck(e){return ke(this,null,function*(){uni.showModal({title:"\u786E\u8BA4",content:"\u8BF7\u524D\u5F80pc\u7AEF\u67E5\u770B\u7EC8\u5B54\u62A5\u544A\u5355",success:f=>ke(this,null,function*(){if(f.confirm)try{let t={id:e.id,action:1},a=yield Me.post("/todo/post_modify",t);a.status===0?(uni.showToast({title:"\u5DF2\u67E5\u6536",icon:"none"}),this.getTodoList()):uni.showToast({title:a.message||"\u5904\u7406\u5931\u8D25",icon:"none"})}catch(t){fe("error","at pages/commission/commission.nvue:524","\u5904\u7406\u540C\u610F\u64CD\u4F5C\u5931\u8D25:",t),uni.showToast({title:"\u7F51\u7EDC\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5",icon:"none"})}})})})},handleCheck(e){return ke(this,null,function*(){uni.showModal({title:"\u786E\u8BA4",content:"\u786E\u5B9A\u67E5\u6536\u8BE5\u901A\u77E5\u5417\uFF1F",success:f=>ke(this,null,function*(){if(f.confirm)try{let t={id:e.id,action:1},a=yield Me.post("/todo/post_modify",t);a.status===0?(uni.showToast({title:"\u5DF2\u67E5\u6536",icon:"none"}),this.getTodoList()):uni.showToast({title:a.message||"\u5904\u7406\u5931\u8D25",icon:"none"})}catch(t){fe("error","at pages/commission/commission.nvue:560","\u5904\u7406\u540C\u610F\u64CD\u4F5C\u5931\u8D25:",t),uni.showToast({title:"\u7F51\u7EDC\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5",icon:"none"})}})})})},dialogClose(){this.showRefusePopup=!1,this.refuseReason="",this.currentItem=null},dialogConfirm(e){return ke(this,null,function*(){try{if(!this.refuseReason.trim()){uni.showToast({title:"\u8BF7\u8F93\u5165\u62D2\u7EDD\u539F\u56E0",icon:"none"});return}let f={id:e.id,refuse:this.refuseReason,action:2},t=yield Me.post("/todo/post_modify",f);t.status===0?(uni.showToast({title:"\u5DF2\u5904\u7406",icon:"none"}),this.getTodoList()):uni.showToast({title:t.message||"\u5904\u7406\u5931\u8D25",icon:"none"})}catch(f){fe("error","at pages/commission/commission.nvue:605","\u5904\u7406\u62D2\u7EDD\u64CD\u4F5C\u5931\u8D25:",f),uni.showToast({title:"\u7F51\u7EDC\u9519\u8BEF\uFF0C\u8BF7\u91CD\u8BD5",icon:"none"})}e&&(e.status=2),this.dialogClose()})},formatTime(e){if(!e)return"";let f=new Date(e).getTime();if(isNaN(f))return"";let a=Date.now()-f;return a<60*1e3?"\u521A\u521A":a<60*60*1e3?Math.floor(a/(60*1e3))+"\u5206\u949F\u524D":a<24*60*60*1e3?Math.floor(a/(60*60*1e3))+"\u5C0F\u65F6\u524D":Math.floor(a/(24*60*60*1e3))+"\u5929\u524D"},loadMore(){this.loadMoreStatus!=="nomore"&&(this.currentPage++,this.getTodoList())},onRefresh(){this.isRefreshing=!0,this.currentPage=1,this.getTodoList(),setTimeout(()=>{this.isRefreshing=!1},1e3)},onPulling(e){this.isPulling=!0},filterData(){this.todoList=[...this.originalList];let e=this.todoList.filter(f=>{let t=this.value1===""||f.type===this.value1,a=this.value2===""||f.status===this.value2,m=!this.searchText||f.title.includes(this.searchText)||f.submitter.includes(this.searchText)||f.content.includes(this.searchText);return t&&a&&m});this.todoList=e,this.total=e.length,this.loadMoreStatus="nomore"}}};function es(e,f,t,a,m,r){let i=_e((0,x.resolveDynamicComponent)("up-input"),Vi),p=_e((0,x.resolveDynamicComponent)("u-modal"),Oo),h=_e((0,x.resolveDynamicComponent)("u-empty"),Fo),c=_e((0,x.resolveDynamicComponent)("u-loadmore"),Ho);return(0,x.openBlock)(),(0,x.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,x.createElementVNode)("view",{class:"container"},[(0,x.createElementVNode)("u-image",{class:"background-image",src:Pt,mode:"aspectFill",style:(0,x.normalizeStyle)({width:m.screenWidth+"px",height:m.screenHeight+"px"})},null,4),(0,x.createElementVNode)("view",{class:"content-wrapper"},[(0,x.createElementVNode)("view",{class:"header",background:"transparent",style:{color:"#fff"}},[(0,x.createElementVNode)("u-text",{style:{"font-size":"34rpx",color:"rgba(255, 255, 255, 0.85)"}})]),(0,x.createElementVNode)("scroll-view",{class:"content",scrollY:"true",onScrolltolower:f[2]||(f[2]=(...o)=>r.loadMore&&r.loadMore(...o)),onRefresherrefresh:f[3]||(f[3]=(...o)=>r.onRefresh&&r.onRefresh(...o)),onRefresherpulling:f[4]||(f[4]=(...o)=>r.onPulling&&r.onPulling(...o)),refresherEnabled:!0,refresherTriggered:m.isRefreshing,refresherBackground:"rgba(0,0,0,0)",refresherThreshold:30,refresherDefaultStyle:"black"},[(0,x.createElementVNode)("view",{class:"filter-wrapper"},[(0,x.createVNode)(i,{class:"sear_inp",placeholder:"\u641C\u7D22\u4EE3\u529E\u540D\u79F0",prefixIcon:"search",modelValue:m.searchText,"onUpdate:modelValue":f[0]||(f[0]=o=>m.searchText=o),clearable:!0,onChange:r.handleSearchInput,onClear:r.handleClear,color:"rgba(255,255,255,0.8)",prefixIconStyle:"font-size: 22px;color: #909399"},null,8,["modelValue","onChange","onClear"])]),m.todoList.length!=0?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0},[((0,x.openBlock)(!0),(0,x.createElementBlock)(x.Fragment,null,(0,x.renderList)(m.todoList,(o,n)=>((0,x.openBlock)(),(0,x.createElementBlock)("view",{class:"todo-item",key:n},[o.type==2?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:"item-wrapper"},[(0,x.createElementVNode)("view",{class:"item-header"},[(0,x.createElementVNode)("view",{class:"header-left"},[(0,x.createElementVNode)("u-image",{class:"item-icon",style:{background:"#CAE4FF"},src:Xo,mode:"aspectFit"})]),(0,x.createElementVNode)("view",{class:"header-middle"},[(0,x.createElementVNode)("u-text",{class:"item-title"},(0,x.toDisplayString)(o.title),1)]),(0,x.createElementVNode)("view",{class:"header-right"},[(0,x.createElementVNode)("u-text",{class:"item-time"},(0,x.toDisplayString)(r.formatTime(o.createdAt)),1)])]),(0,x.createElementVNode)("view",{class:"item-content"},[(0,x.createElementVNode)("view",{class:"content-row"},[(0,x.createElementVNode)("u-text",{class:"value"},(0,x.toDisplayString)(r.formatDynamicFieldss(o.dynamicFields)),1)]),(0,x.createElementVNode)("view",{class:"content-row"},[(0,x.createElementVNode)("u-text",{class:"label"},"\u901A\u77E5\u65F6\u95F4\uFF1A"+(0,x.toDisplayString)(o.createdAt),1),(0,x.createElementVNode)("u-text",{class:"value"})])]),o.title=="\u4E91\u5E73\u53F0\u4EA4\u63A5\u73ED\u63D0\u9192"?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:0,class:"item-footer"},[o.status===0?((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:0,onClick:d=>r.handleTourClick(o),class:"btn-approve"},"\u53BB\u5904\u7406",8,["onClick"])):((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:1,class:"btn-processed"},"\u5DF2\u5904\u7406"))])):o.title=="\u4E91\u5E73\u53F0\u7EC8\u5B54\u63D0\u9192"?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:1,class:"item-footer"},[o.status===0?((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:0,onClick:d=>r.handleEndCheck(o),class:"btn-approve"},"\u786E\u8BA4\u67E5\u6536",8,["onClick"])):((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:1,class:"btn-processed"},"\u5DF2\u67E5\u6536"))])):((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:2,class:"item-footer"},[o.status===0?((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:0,class:"btn-approve",onClick:d=>r.handleCheck(o)},"\u786E\u8BA4\u67E5\u6536",8,["onClick"])):((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:1,class:"btn-processed"},"\u5DF2\u67E5\u6536"))]))])):((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:1,class:"item-wrapper"},[(0,x.createElementVNode)("view",{onClick:d=>r.handleItemClick(o)},[(0,x.createElementVNode)("view",{class:"item-header"},[(0,x.createElementVNode)("view",{class:"header-left"},[(0,x.createElementVNode)("u-image",{class:"item-icon",src:Jo,mode:"aspectFit"})]),(0,x.createElementVNode)("view",{class:"header-middle"},[(0,x.createElementVNode)("u-text",{class:"item-title"},(0,x.toDisplayString)(o.title),1)]),(0,x.createElementVNode)("view",{class:"header-right"},[(0,x.createElementVNode)("u-text",{class:"item-time"},(0,x.toDisplayString)(r.formatTime(o.createdAt)),1)])]),(0,x.createElementVNode)("view",{class:"item-content"},[(0,x.createElementVNode)("view",{class:"content-row"},[(0,x.createElementVNode)("u-text",{class:"value"},(0,x.toDisplayString)(r.formatDynamicFields(o.dynamicFields)),1)]),(0,x.createElementVNode)("view",{class:"content-row"},[(0,x.createElementVNode)("u-text",{class:"label"},"\u4EFB\u52A1\u4E0B\u53D1\u65F6\u95F4\uFF1A"+(0,x.toDisplayString)(o.createdAt),1),(0,x.createElementVNode)("u-text",{class:"value"})])])],8,["onClick"]),(0,x.createElementVNode)("view",{class:"item-footer"},[o.status===1?((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:0,class:"btn-processed"},"\u5DF2\u540C\u610F")):(0,x.createCommentVNode)("",!0),o.status===2?((0,x.openBlock)(),(0,x.createElementBlock)("u-text",{key:1,class:"btn-processed"},"\u5DF2\u62D2\u7EDD")):(0,x.createCommentVNode)("",!0),o.status===0?((0,x.openBlock)(),(0,x.createElementBlock)(x.Fragment,{key:2},[(0,x.createElementVNode)("u-text",{class:"btn-reject",onClick:(0,x.withModifiers)(d=>r.showRejectDialog(o),["stop"])},"\u62D2\u7EDD",8,["onClick"]),(0,x.createElementVNode)("u-text",{class:"divider"},"|"),(0,x.createElementVNode)("u-text",{class:"btn-approve",onClick:(0,x.withModifiers)(d=>r.handleApprove(o),["stop"])},"\u540C\u610F",8,["onClick"])],64)):(0,x.createCommentVNode)("",!0)])])),(0,x.createVNode)(p,{show:m.showRefusePopup,title:"\u62D2\u7EDD\u539F\u56E0",content:"",showCancelButton:!0,closeOnClickOverlay:!0,onClose:r.dialogClose,onCancel:r.dialogClose,onConfirm:d=>r.dialogConfirm(o)},{default:(0,x.withCtx)(()=>[(0,x.createElementVNode)("view",{class:"modal-content"},[(0,x.createElementVNode)("u-textarea",{modelValue:m.refuseReason,onInput:f[1]||(f[1]=d=>m.refuseReason=d.detail.value),placeholder:"\u8F93\u5165\u62D2\u7EDD\u539F\u56E0",maxlength:200,autoHeight:!0,customStyle:{borderRadius:"8rpx"}},null,40,["modelValue"])])]),_:2},1032,["show","onClose","onCancel","onConfirm"])]))),128))])):((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:1,class:"empty-state"},[(0,x.createVNode)(h,{mode:"data",text:"\u6682\u65E0\u5F85\u529E\u4E8B\u9879"})])),m.todoList.length>0?((0,x.openBlock)(),(0,x.createElementBlock)("view",{key:2,class:"load-more"},[(0,x.createVNode)(c,{bgColor:"transparent",status:m.loadMoreStatus},null,8,["status"])])):(0,x.createCommentVNode)("",!0)],40,["refresherTriggered"])])])])}var Ye=Se($o,[["render",es],["styles",[Zo]]]);var at=plus.webview.currentWebview();if(at){let e=parseInt(at.id),f="pages/commission/commission",t={};try{t=JSON.parse(at.__query__)}catch(m){}Ye.mpType="page";let a=Vue.createPageApp(Ye,{$store:getApp({allowDefault:!0}).$store,__pageId:e,__pagePath:f,__pageQuery:t});a.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...Ye.styles||[]])),a.mount("#root")}})();
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */
