<template>
  <view class="monitor-container">
    <view class="video-wrapper">
       <yingbing-video 
	   autoplay
	   :src="videoUrl"
	   :flvConfig="flvConfig"
	   :isLive="true"
	   ref="videoPlayer"
	   >
          </yingbing-video>
    </view>

    <!-- 添加终止视频流按钮 -->
    <!-- <view class="control-panel">
      <button class="stop-btn" @click="stopVideoStream" v-if="isPlaying">终止视频流</button>
      <button class="start-btn" @click="startVideoStream" v-else>开始播放</button>
    </view> -->

    <view class="video-info">
      <text class="video-title">监控视频</text>
      <text class="video-desc">实时监控画面</text>
    </view>
    
    <!-- 添加语音呼叫按钮 -->
    <view class="call-action">
      <button class="call-btn" @click="startVoiceCall" :disabled="isCallInProgress">
        <image class="call-icon" src="/static/phone.png" mode="aspectFit"></image>
        <text style="color:rgba(255,255,255,0.85)">{{ isCallInProgress ? '通话中...' : '语音呼叫' }}</text>
      </button>
    </view>
    
    <!-- 引入通话组件 -->
    <CallComponent 
      ref="callComponent"
      :visible="showCallComponent"
      :targetDeviceId="targetDeviceId"
      @login-success="handleLoginSuccess"
      @login-failed="handleLoginFailed"
      @rtc-initialized="handleRtcInitialized"
      @call-started="handleCallStarted"
      @call-ended="handleCallEnded"
      @call-timeout="handleCallTimeout"
      @call-failed="handleCallFailed"
      @show-call-ui="showCallComponent = true"
      @hide-call-ui="handleHideCallUI"
      @incoming-call="handleIncomingCall"
      @incoming-call-ui-shown="handleIncomingCallUIShown"
      @call-ui-should-show="enforceCallUIVisible"
    />
  </view>
</template>

<script>
	import Request from '@/components/utils/request.js'
	import CallComponent from '@/components/CallComponent.vue'

export default {
	components: {
		CallComponent
	},
	props: {
	    gbsId: {
	      type: String,
	      default: ''
	    },
      deviceCode:{
        type: String,
	      default: ''
      },
        // 新增属性用于检测组件是否应该显示视频
        isVisible: {
          type: Boolean,
          default: true
        },
        // 设备呼叫ID
        deviceCallId: {
          type: String,
          default: ''  
        }
	},
  data() {
    return {
      videoUrl: '',
      posterUrl: '', // 可以设置一个封面图片
      isLoading: true,
      isPlaying: false,
      videoContext: null,
      flvConfig: {
        hasAudio: false, // 禁用音频
        hasVideo: true,
        isLive: true,
        cors: true,
        enableStashBuffer: false,
        stashInitialSize: 128 * 1024,
        enableWorker: false,
		lazyLoad: false,
	    fixAudioTimestampGap: false
      },
      // 通话相关状态
      nimLoginStatus: false,
      rtcInitialized: false,
      isCallInProgress: false,
      showCallComponent: false,
      // 定义默认呼叫目标账号
      targetDeviceId: '', // 设置默认呼叫账号
      // 记录自动呼叫状态
      shouldAutoCallOnLogin: false
    }
  },
  mounted(){
    this.handelVideo()
    
    // Initialize call component early
    setTimeout(() => {
      if (this.$refs.callComponent) {
        // Force initialization even if component is not visible
        this.$refs.callComponent.initializeCall()
      }
    }, 1000)
  },
  // 监听isVisible属性变化
  watch: {
    isVisible(newVal) {
      if (!newVal && this.isPlaying) {
        console.log('视频组件不可见，强制终止视频流')
        this.forceStopVideoStream()
      }
      
      // 如果页面不可见，同时也需要结束通话
      if (!newVal && this.isCallInProgress) {
        this.endCall()
      }
    },
    isCallInProgress(newVal) {
      // 如果处于通话中，确保通话组件始终可见
      if (newVal === true && !this.showCallComponent) {
        console.log('检测到通话进行中但组件不可见，强制显示组件')
        this.showCallComponent = true
      }
    }
  },
  methods: {
	 async handelVideo(){
      
	  try {
	  	const res = await Request.get('/gbs/get_channel', {param:this.gbsId})
	  
	  	if (res.status == 0) {
	  		const data = JSON.parse(res.data)
	  		this.videoUrl = data.address.http_flv
			console.log('视频地址',this.videoUrl)
            this.isPlaying = true // 设置为播放状态
	  	} else {
	  		// 失败
	  		uni.showToast({
	  			title: res.msg,
	  			icon: 'none',
	  			duration: 2000
	  		});
	  	}
	  
	  } catch (error) {
	  	console.error('Error updating password:', error);
	  	uni.showToast({
	  		title: '网络错误，请稍后重试',
	  		icon: 'none',
	  		duration: 2000
	  	});
	  }
    },
    
    // 终止视频流方法
    stopVideoStream() {
      console.log('stopVideoStream 被调用')
      if (this.$refs.videoPlayer) {
        // 调用播放器的停止方法
        this.$refs.videoPlayer.stop()
        
        // 清空视频URL以确保网络层也停止请求
        this.videoUrl = ''
        this.isPlaying = false
        
        uni.showToast({
          title: '视频流已终止',
          icon: 'none',
          duration: 1500
        })
      }
    },
    
    // 强制终止视频流（添加更多保障措施）
    forceStopVideoStream() {
      console.log('强制终止视频流方法执行')
      
      // 确保视频播放器存在
      if (this.$refs.videoPlayer) {
        try {
          // 尝试调用播放器的所有可能的停止方法
          if (typeof this.$refs.videoPlayer.stop === 'function') {
            this.$refs.videoPlayer.stop()
          }
          
          if (typeof this.$refs.videoPlayer.destroy === 'function') {
            this.$refs.videoPlayer.destroy()
          }
          
          if (typeof this.$refs.videoPlayer.dispose === 'function') {
            this.$refs.videoPlayer.dispose()
          }
          
          // 记录状态变化
          console.log('视频播放器方法调用完成')
        } catch (e) {
          console.error('停止视频播放器失败:', e)
        }
      }
      
      // 无论播放器方法调用是否成功，都强制清空URL
      this.videoUrl = ''
      this.isPlaying = false
      
      // 强制触发GC回收（不同平台实现可能有差异）
      setTimeout(() => {
        console.log('延迟处理完成，视频流应已终止')
      }, 300)
    },
    
    // 重新开始视频流
    async startVideoStream() {
      this.isLoading = true
      await this.handelVideo()
      this.isLoading = false
    },
    
    // 语音呼叫处理方法
    async startVoiceCall() {
      try {
        // 显示加载提示
        uni.showLoading({
          title: '准备通话...'
        })
        
        // 确保组件可见
        this.showCallComponent = true
        
        // 确保登录成功
        if (!this.nimLoginStatus) {
          console.log('尚未登录，标记为登录后自动呼叫')
          this.shouldAutoCallOnLogin = true
          
          // 如果组件存在，强制初始化
          if (this.$refs.callComponent) {
            await this.$refs.callComponent.initializeCall()
          }
          
          // 等待登录完成 - 设置超时
          let loginTimeout = setTimeout(() => {
            uni.hideLoading()
            uni.showToast({
              title: '登录超时，请重试',
              icon: 'none'
            })
            this.shouldAutoCallOnLogin = false
          }, 10000)
          
          // 在登录成功回调中会清除此标记并执行呼叫
          return
        }
        
        // 如果已登录，直接发起呼叫
        if (this.nimLoginStatus && this.$refs.callComponent) {
          // 先设置目标设备ID
          this.targetDeviceId = this.deviceCode
          console.log('已登录，直接呼叫目标:', this.targetDeviceId)
          
          // 重要：直接设置通话组件的targetAccount
          this.$refs.callComponent.targetAccount = this.deviceCode
          
          // 确保组件已更新再调用makeCall
          setTimeout(async () => {
            const callResult = await this.$refs.callComponent.makeCall()
            
            if (!callResult) {
              throw new Error('呼叫失败')
            }
            
            this.isCallInProgress = true
            uni.hideLoading()
          }, 100)
        } else {
          throw new Error('未正确初始化通话组件')
        }
        
      } catch (error) {
        console.error('发起语音呼叫失败:', error)
        uni.hideLoading()
        uni.showToast({
          title: '呼叫失败: ' + (error.message || '未知错误'),
          icon: 'none'
        })
        this.showCallComponent = false
      }
    },
    
    // 结束通话
    endCall() {
      if (this.$refs.callComponent) {
        this.$refs.callComponent.hangUp()
      }
    },
    
    // 处理登录成功事件
    handleLoginSuccess(data) {
      console.log('登录成功:', data)
      this.nimLoginStatus = true
      
      // 检查是否需要自动呼叫
      if (this.shouldAutoCallOnLogin) {
        this.shouldAutoCallOnLogin = false
        console.log('登录成功后自动呼叫')
        
        // 延迟一点时间再呼叫，确保组件完全准备好
        setTimeout(async () => {
          try {
            if (this.$refs.callComponent) {
              // 确保targetAccount设置正确
              this.targetDeviceId = this.deviceCode
              this.$refs.callComponent.targetAccount = this.deviceCode
              console.log('自动呼叫目标账号已设置:', this.deviceCode)
              
              const callResult = await this.$refs.callComponent.makeCall()
              if (!callResult) {
                throw new Error('自动呼叫失败')
              }
              this.isCallInProgress = true
            }
          } catch (error) {
            console.error('自动呼叫失败:', error)
            uni.showToast({
              title: '呼叫失败，请重试',
              icon: 'none'
            })
          } finally {
            uni.hideLoading()
          }
        }, 1000)
      }
    },
    
    handleLoginFailed(data) {
      console.error('登录失败:', data)
      this.nimLoginStatus = false
      uni.showToast({
        title: '登录失败: ' + (data.message || '未知错误'),
        icon: 'none'
      })
    },
    
    handleRtcInitialized() {
      console.log('RTC初始化成功')
      this.rtcInitialized = true
    },
    
    handleCallStarted(data) {
      console.log('通话已开始:', data)
      this.isCallInProgress = true
      
      // 确保通话组件保持可见状态
      this.showCallComponent = true
      
      // 延迟确认状态
      setTimeout(() => {
        this.enforceCallUIVisible()
      }, 200)
      
      // 再次延迟确认，以处理任何状态同步问题
      setTimeout(() => {
        this.enforceCallUIVisible()
      }, 1000)
    },
    
    handleCallEnded(data) {
      console.log('通话已结束:', data)
      
      // 首先更新状态
      this.isCallInProgress = false
      
      // 延迟关闭组件，确保UI动画完成
      // 增加条件检查，只有在确定通话已结束时才隐藏组件
      setTimeout(() => {
        // 再次检查通话是否确实已经结束
        if (!this.isCallInProgress && this.$refs.callComponent && !this.$refs.callComponent.isInCall) {
          this.showCallComponent = false
        } else {
          console.log('检测到通话可能仍在进行中，保持组件可见')
        }
      }, 1000) // 延长时间以确保状态稳定
    },
    
    handleCallTimeout() {
      console.log('通话超时')
      this.isCallInProgress = false
      uni.showToast({
        title: '通话超时，对方未应答',
        icon: 'none'
      })
    },
    
    handleCallFailed(data) {
      console.error('通话失败:', data)
      this.isCallInProgress = false
      uni.showToast({
        title: '通话失败，请重试',
        icon: 'none'
      })
    },
    
    // Add new methods to handle incoming calls
    handleIncomingCall(callInfo) {
      console.log('收到来电:', callInfo)
      // 强制显示组件
      this.showCallComponent = true
      
      // 更新状态
      this.isCallInProgress = true
      
      // 振动提醒(如果支持)
      if (uni.vibrateShort) {
        uni.vibrateShort({
          success: function() {
            console.log('振动提醒成功')
          }
        })
      }
      
      // 播放提示音
      const innerAudioContext = uni.createInnerAudioContext()
      innerAudioContext.autoplay = true
      innerAudioContext.src = '/static/notice.mp3' // 提示音文件
      
      // 显示toast提示
      // uni.showToast({
      //   title: '收到来电请求',
      //   icon: 'none',
      //   duration: 3000
      // })
    },
    
    handleIncomingCallUIShown() {
      console.log('来电UI已显示')
      // 确保组件可见
      this.showCallComponent = true
      
      // 再次确认让父组件知道来电UI正在显示
      setTimeout(() => {
        if (this.$refs.callComponent) {
          // 确保来电模态框显示
          this.$refs.callComponent.showIncomingModal = true
          // 强制更新组件
          this.$refs.callComponent.$forceUpdate()
        }
      }, 200)
    },
    
    // 检查呼叫组件状态
    checkCallStatus() {
      if (!this.$refs.callComponent) return
      
      // 获取呼叫组件的登录状态
      this.nimLoginStatus = this.$refs.callComponent.nimConnected
      
      // 获取呼叫状态
      this.isCallInProgress = this.$refs.callComponent.isInCall
      
      console.log('同步呼叫状态:', {
        nimLoginStatus: this.nimLoginStatus,
        isCallInProgress: this.isCallInProgress
      })
    },
    
    // 监听组件挂载完成
    onComponentMounted() {
      // 定期检查组件状态
      this.statusCheckInterval = setInterval(() => {
        this.checkCallStatus()
      }, 2000)
    },
    
    // 添加新方法强制显示通话UI
    enforceCallUIVisible() {
      console.log('收到强制显示通话UI信号')
      
      // 确保组件可见
      this.showCallComponent = true
      
      // 延迟检查确保通话状态一致
      setTimeout(() => {
        if (this.$refs.callComponent) {
          const inCallStatus = this.$refs.callComponent.isInCall
          console.log('检查通话组件状态:', inCallStatus)
          
          if (inCallStatus) {
            // 如果通话中但UI不可见，强制更新
            this.isCallInProgress = true
            this.showCallComponent = true
            
            // 强制刷新组件
            this.$refs.callComponent.$forceUpdate()
            
            // 输出日志确认
            console.log('已强制更新通话UI状态')
          }
        }
      }, 100)
    },
    
    // 修改隐藏UI处理方法
    handleHideCallUI() {
      // 只有当通话确实结束时才隐藏UI
      if (!this.isCallInProgress && this.$refs.callComponent && !this.$refs.callComponent.isInCall) {
        console.log('通话已结束，隐藏UI')
        this.showCallComponent = false
      } else {
        console.log('收到隐藏UI信号，但通话仍在进行中，忽略')
      }
    }
  },
  beforeDestroy() {
    if (this.statusCheckInterval) {
      clearInterval(this.statusCheckInterval)
    }
  }
}
</script>

<style scoped>
	page{
		background: #16171b;
	}
.monitor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* background-color: #f5f5f5; */
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding: 0 20rpx;
  /* background-color: #ffffff; */
  position: relative;
  padding-top: var(--status-bar-height, 20px);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.video-wrapper {
  margin: 20rpx;
  /* background-color: #000; */
  border-radius: 12rpx;
  overflow: hidden;
  height: 420rpx;
  position: relative;
}

.video-player {
  width: 100%;
  height: 100%;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.play-button {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  /* background-color: rgba(255, 255, 255, 0.2); */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  border: 4rpx solid #fff;
}

.play-icon {
  color: #fff;
  font-size: 50rpx;
  line-height: 1;
  margin-left: 10rpx; /* 调整播放图标位置 */
}

.play-text {
  color: #ffffff;
  font-size: 28rpx;
}

.loading-overlay {
  position: absolute;
  top: 110rpx; /* 调整位置以覆盖视频区域 */
  left: 20rpx;
  right: 20rpx;
  height: 420rpx;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  z-index: 10;
}

.spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.loading-text {
  color: #ffffff;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.video-info {
  padding: 20rpx;
  background-color: rgba(255,255,255,0.04);
  margin: 0 20rpx;
  border-radius: 12rpx;
}

.video-title {
  font-size: 32rpx;
  font-weight: bold;
  color: rgba(255,255,255,0.65);
  margin-bottom: 10rpx;
  display: block;
}

.video-desc {
  font-size: 28rpx;
   color: rgba(255,255,255,0.45);
  display: block;
}

/* 添加控制面板样式 */
.control-panel {
  display: flex;
  justify-content: center;
  margin: 10rpx 20rpx 20rpx;
}

.stop-btn, .start-btn {
  width: 300rpx;
  height: 80rpx;
  line-height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  margin: 0 20rpx;
  color: #fff;
}

.stop-btn {
  background-color: #e64340;
  border: none;
}

.start-btn {
  background-color: #07c160;
  border: none;
}

.stop-btn:active, .start-btn:active {
  opacity: 0.8;
}

.call-action {
  margin: 30rpx 20rpx;
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12rpx;
}

.call-btn {
  display: flex;
  border: 1px solid rgba(255, 255, 255, 0.2);
  align-items: center;
  justify-content: center;
  background-color:rgb(0, 0, 0,0);
  color: #fff;
  border-radius: 12rpx;
  height: 90rpx;
  font-size: 30rpx;
  border: none;
}

.call-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
  width:40rpx;
  height:40rpx;
}

.call-btn:active {
  opacity: 0.8;
}
</style>