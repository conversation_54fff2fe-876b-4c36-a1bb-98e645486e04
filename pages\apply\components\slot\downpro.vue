<template>
	<view class="content">
		<view class="search-bar">
			<!-- <input @input="handleSearchInput" type="text" class="sear_inp" placeholder="钻具名称/钻具ID" v-model="searchText" /> -->
			<up-input class="sear_inp" placeholder="任务名称" prefixIcon="search" v-model="searchText"
				color="rgba(255,255,255,0.8)" prefixIconStyle="font-size: 22px;color: #909399"></up-input>
			<view class="filter_icon" @click="handelSearch1">
				<image src="@/static/image/apply/search.png" mode=""></image>
			</view>
		</view>

		<view style="height:550px;magin:10rpx;background: rgba(0, 0, 0, 0.1);border-radius: 10rpx;">
			<!-- <view> -->
			<zb-table @sort-change="sortChange" :pullUpLoading="pullUpLoading" :isShowLoadMore="true" :highlight="true"
				:show-header="true" :columns="column" :fit="false" :permissionBtn="permissionBtn" :stripe="true"
				row-key="id" @rowClick="rowClick" :border="false" @edit="buttonEdit" 
				:data="data"></zb-table>

		</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request.js'
	let that = null
	export default {
		
		data() {
			const baseData = {
				lanewayId: "TASK001",
				name: "3号区域初步打孔",
				type: "DALIHUA001",
				length: "3号区域",
				width: "3号煤层",
				height: "初步打孔",
				tiltAngle: "张三、李四",
				startTime: '2022-03-01',
				endTime: '2024-06-30',
				status: 0,
				responsiblePerson: '2024-01-11',
				remark: "2024-01-22",
				noodlesId: '作业前进行安全培训',
			};
			const generateData = () => {
				const repeatedData = [];
				for (let i = 0; i < 10; i++) {
					repeatedData.push({
						id: i,
						...baseData
					})
				}
				return repeatedData;
			}
			return {
				dateStart: '',
				dateEnd: '',
				id:null,
				searchText: '',
				column: [
					// { type:'selection', fixed:true,width:60 },
					{
						name: 'lanewayId',
						label: '任务编号',
						fixed: true,
						align: 'center',
						emptyString: '--'
					},
					{
						name: 'name',
						label: '任务名称',
						// sorter: 'custom',
					},
					{
						name: 'type',
						label: '关联打控计划编号',
						fixed: true
					},
					{
						name: 'length',
						label: '作业区域',
					},
					{
						name: 'width',
						label: '作业煤层',
						// sorter: true
					},
					{
						name: 'height',
						label: '任务类型'
					},
					{
						name: 'tiltAngle',
						label: '作业人员',
						// sorter: true
					},
					{
						name: 'startTime',
						label: '开始日期',
					},
					{
						name: 'endTime',
						label: '结束日期',
					},
					{
						name: 'responsiblePerson',
						label: '实际开始日期',
					},
					{
						name: 'remark',
						label: '实际结束日期',
					},
					{
						name: 'status',
						label: '任务状态',
						filters: {
							0: '进行中',
							1: '已完成'
						},
					
					},
					{
						name: 'noodlesId',
						label: '任务描述',
					},
					{
						name: 'operation',
						type: 'operation',
						label: '操作',
						align: 'center',
						renders: [{
								name: '详情',
								class: 'edit',
								type: "primary",
								align: 'center',
								func: 'edit' // func 代表子元素点击的事件 父元素接收的事件 父元素 @edit
							},

						]
					},
				],
				data: generateData(),
				// data: [],//表格数据
				data1: [],
				flag1: true,
				flag2: true,
				num: 0,
				num1: 0,
				// pullUpLoading: false, // 是否正在加载
				isShowLoadMore: true, // 是否显示“加载更多”  
				currentPage: 1, // 当前页码
				perPage: 10, // 每页条数
			}
		},
		onLoad() {
			// this.handelDrill()
		},
		created() {
			that = this
		},
		mounted() {
			// this.handelDrill();
		},
		onLoad(options) {
			console.log(options);
			this.dateStart = options.dateStart;
			this.dateEnd = options.dateEnd;
		},
		methods: {
			handleSearchInput(event) {
				console.log(event);
				this.searchText = event.detail.value;
				console.log('handleSearchInput', this.searchText);
				this.currentPage = 1; // 重置页码
				this.data = []; // 清空数据
				this.isShowLoadMore = true; // 重置加载更多
				this.handelDrill(); // 重新加载数据
			},
			//初始加载调取接口获取数据
			async handelDrill() {

				try {
					let drill_data = {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord: this.searchText,
						dateStart: this.dateStart,
						dateEnd: this.dateEnd
					};
					const res = await Request.post('/laneway/get_ls', drill_data)

					if (res.status == 0) {
						console.log('返回数据', res);
						this.data = res.data.items;
						if (res.data.items.length < this.perPage) {
							this.isShowLoadMore = false;
						}
						// 更新成功
						// uni.showToast({
						// 	title: '实名认证成功',
						// 	icon: 'none',
						// 	duration: 2000
						// });

					} else {
						// 失败
						uni.showToast({
							title: res.msg,
							icon: 'none',
							duration: 2000
						});
					}

				} catch (error) {
					console.error('Error updating password:', error);
					uni.showToast({
						title: '网络错误，请稍后重试',
						icon: 'none',
						duration: 2000
					});
				}

			},

			change(e) {
				console.log("e:", e);
			},
			sortChange(item, sorterMode, index) {

				console.log('触发排序', item, sorterMode, index)
			},
			//筛选
			handelSearch() {
				uni.navigateTo({
					// url: '/pages/apply/components/computer/search's
				})
			},
			cellStyle({
				row,
				column,
				rowIndex,
				columnIndex
			}) {
				// console.log('row, column, rowIndex, columnIndex')
				if ((columnIndex % 2) != 0) {
					return {
						background: 'red'
					}
				}
			},
			async pullUpLoading(done) {
				if (!this.flag1) {
					return
				}
				this.currentPage++; // 页码加1
				try {
					const res = await Request.post('/laneway/get_ls', {
						page: this.currentPage,
						perPage: this.perPage,
						keyWord: this.searchText,
						dateStart: this.dateStart,
						dateEnd: this.dateEnd
					});

					if (res.status == 0) {
						console.log('加载获取数据', res.data);
						// this.data.push(res.data.items);
						console.log('data11111', this.data);
						if (res.data.items && res.data.items.length > 0) {
							this.data = this.data.concat(res.data.items);
							console.log('data11111', this.data);
							done(); // 通知 zb-table 加载完成

						} else {
							done('ok'); // 通知zb-table 没有更多数据
							this.flag1 = false
							uni.showToast({
								title: '暂无更多数据',
								icon: 'none',
								duration: 1000
							})
						}

					} else {

						//    uni.showToast({
						// 	title: '加载数据失败' ,
						// 	icon: 'none',
						// 	duration: 1000
						// })
						done(); // 结束加载
					}
				} catch (error) {
					console.error("加载更多数据失败:", error);
					// uni.showToast({
					// 	title: '加载数据失败' ,
					// 	icon: 'none',
					// 	duration: 1000
					// })
					done(); //  结束加载
				}
				// setTimeout(() => {
				// 	this.data.push({
				// 		serialNum: 'ZJD0021',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	},
				// 	{
				// 		serialNum: 'ZJD0022',
				// 		title: '三牙钻头',
				// 		model: '钻头',
				// 		specs: "GD325-5",
				// 		productionTime: '2023-12-23',
				// 		purchaseTime: '2023-12-23',
				// 		lifespan: "20",
				// 		status: "0",
				// 		lastUsedTime: '2025-01-01',
				// 		nextRepaireTime: '2025-01-04'
				// 	})

				// 	this.num++
				// 	if (this.num === 3) {
				// 		done('ok')
				// 		this.flag1 = false
				// 	} else {
				// 		done()
				// 	}
				// }, 2000)
			},
			permissionBtn(row, renders, rowIndex) {
				if (row.id == 2) {
					let arr = renders.filter(item => item.func === 'edit')
					return arr
				}
				return renders
			},

			buttonEdit(ite, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '点击编辑'
				// })
				// console.log(ite, index),
				uni.navigateTo({
					// url: `/pages/apply/components/computer/detail?id=${ite.id}`
				})
			},

			rowClick(row, index) {
				// uni.showToast({
				// 	icon: 'none',
				// 	duration: 3000,
				// 	title: '单击某行'
				// })
				// console.log('单击某行', row, index)
				uni.navigateTo({
					// url: `/pages/apply/components/computer/detail?id=${row.id}`
				})
			}
		},

	}
</script>

<style scoped lang="scss">
	page {
		background: #16171b;
	}

	.filter {
		margin: 28rpx 0;
		height: 70rpx;
		display: flex;
		// background: #fff;
	}

	.select zxz-uni-data-select {
		border: none;
	}

	uni-icons {
		width: 20rpx;
		height: 20rpx;
		color: #fff;
	}

	.sear_inp {
		border: none;
		padding: 12rpx;
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.select {
		flex: 1;
		border: none;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
	}

	.filter_icon {
		height: 42rpx;
		width: 28rpx;
		padding: 14rpx;
		border-radius: 10rpx;
		background: rgba(255, 255, 255, 0.08);
	}

	.filter_icon image {
		width: 28rpx;
		height: 28rpx;

	}

	::v-deep {
		.uni-card {
			margin: 8px !important;
			padding: 0 !important;

			.uni-card__content {
				padding: 0 !important;
			}
		}
	}

	text {
		color: #fff;
	}

	.search-bar {
		padding-top: 32rpx;
		margin-bottom: 32rpx;
		display: flex;
		color: rgba(255, 255, 255, 0.8);
	}

	.sear_inp {
		flex: 1;
		margin-right: 10rpx;
		background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}

	.content {
		// padding: 0 34rpx;
		//height: 600rpx;
		//.title{
		//  font-weight: bold;
		//  padding: 20rpx 0;
		//}
	}
</style>