<template>
	<view class="bg-image"></view>
	<view
		class="header"
		background="transparent"
		style="color: #fff;"
	> 
		<text style="font-size: 32rpx;color: rgba(255, 255, 255, 0.85);"></text>
			</view>
	<view class="container">
		
		<view class="content-wrapper">
			<!-- <view 
				class="header"
				background="transparent"
				style="color: #fff;"
			> 
				<text style="font-size: 32rpx;color: rgba(255, 255, 255, 0.85);">云平台</text>
				@refresherrefresh="onRefresh"
				:refresher-enabled="canRefresh"
				:refresher-triggered="isRefreshing"
			</view> -->
			<scroll-view
				class="content" 
				scroll-y="true"
				@scrolltolower="loadMore"
				 
				@refresherpulling="onPulling"
				
				refresher-background="rgba(0,0,0,0)"
				:refresher-threshold="30"
				refresher-default-style="black"
				@scroll="onScroll"
			>
				<!-- 最上方数据显示 -->			
				<view class="data_header">
					<view class="data-items" style="margin-right: 32rpx;">
						<view class="data-title">总进尺</view>
						<view class="data-value">{{ dataBoard.footageSum }}米</view>
					</view>
					<view class="data-items">
						<view class="data-title">运行钻机数</view>
						<view class="data-value">{{ dataBoard.runSum }}台</view>
					</view>
				</view>
				<!-- 圆环饼图 -->
				<!-- <view class="echarts-container" v-if="!isLoading && pieData.length > 0"> -->
				<view class="echarts-container" v-if=" pieData.length > 0" >
					<view class="e_title">
						孔任务完成情况统计
					</view>
					<pie-chart style="width: 100%;" :chartData="pieData" />
				</view>
				<!-- <view v-else class="loading">
					数据加载中...
				</view> -->
				<!-- 中间数据 -->
				<view class="data_header">
					<view class="data-items" style="margin-right: 32rpx;">
						<view class="data-title">总任务数</view>
						<view class="data-value">{{ dataBoard.taskSum }}</view>
					</view>
					<view class="data-items">
						<view class="data-title">设备开机率</view>
						<view class="data-value" style="color: #ff6d37">{{  dataBoard.operatingRate }}%</view>
					</view>
				</view>
				<view class="data_header" >
					<view class="data-items" style="margin-right: 32rpx;">
						<view class="data-title">事件异常</view>
						<view class="data-value" style="color: #ff9100;">{{ dataBoard.abnormalNum }}</view>
					</view>
					<view class="data-items">
						<view class="data-title">完成任务</view>
						<view class="data-value" style="color: #00b142">{{ dataBoard.taskSumYes }}</view>
					</view>
				</view>
				<!-- 柱状图 -->
				<!-- <view class="echarts-container" v-if="!isLoading && cateData.length > 0"> -->
					<view class="echarts-container" v-if="cateData.length > 0">
					<view class="e_title">
						能源消耗统计
					</view>
					<category-chart :cateData="cateData" />
				</view>
				<!-- <view v-else class="loading">
					数据加载中...
				</view> -->
				<!-- 折线图 -->
				
				 <!-- <view class="echarts-container" v-if="!isLoading && lineData.length > 0"> -->
				 
					<view class="echarts-container" v-if="lineData.length > 0">
					<view class="e_title">
						设备运行时长
					</view>
					<line-chart :lineData="lineData" />
				</view>
				<!-- <view v-else class="loading">
					数据加载中...
				</view> -->
				<!-- 近期异常数据 -->
				<view class="echarts-container" style="margin-bottom: 88rpx;">
					<view class="e_title">
						近期异常数据
					</view>
					<view class="" style="align-items: center;display: flex;text-align: center;color: rgba(255, 255, 255, 0.65);" v-if="dataBoard.abnormalDateList==0">
						暂无异常数据
					</view>
					<view v-else class="error_cont" v-for="(item, index) in limitedAbnormalData" :key="index">
						<view class="error_img">						
							<image v-if="item.abnormalType == 0" src="@/static/red.png" mode=""></image>
							<image v-else src="@/static/yellow.png" mode=""></image>
						</view>
						<view class="error_right">
							<view class="error_title">{{ item.deviceName }}{{ item.name }}</view>
							<view class="error_value">{{ item.createdAt }}</view>
						</view>
					</view>
				</view>
			</scroll-view>
			<!-- <view class="refresh-indicator" v-if="__DEV__">
				可刷新: {{ isTopOfPage ? '是' : '否' }}
				滚动位置: {{ scrollTop }}
			</view> -->
		</view>
</view>

</template>

<script>
	import * as echarts from 'echarts';
	// import uCharts from '@qiun/ucharts';
	import PieChart from './components/pie.vue';
	import CategoryChart from './components/category.vue';
	import LineChart from './components/line.vue';
	import Request from '@/components/utils/request.js'
	// import NpieChart from './components/npie.nvue';
	export default {
		components: {
			PieChart,
			CategoryChart,
			LineChart
			// NpieChart
		},
		
		data() {		
			return {
				screenWidth: 0,
				screenHeight: 0,			
				isRefreshing: false,  // 是否正在刷新
				isPulling: false,  // 添加下拉状态
				chart: null,
				dataBoard: {
					 // footageSum: 0,
					 //    footageEd: 0,
					 //    runSum: 0,
					 //    taskSum: 0,
					 //    taskSumYes: 0,

					 //    abnormalSum: 1,
					 //    abnormalEd: 1,
					 //    unitSum: 2,
					 //    maintenanceSum: 3,
					 //    breakdownSum: 5,
					 //    expendSum: "{\"周一\":{\"用电量\":100.5,\"用水量\":200.3},\"周五\":{\"用电量\":140.1,\"用水量\":240.7},\"周日\":{\"用电量\":160.2,\"用水量\":260.9},\"周四\":{\"用电量\":130.3,\"用水量\":230.6},\"周三\":{\"用电量\":120.0,\"用水量\":220.5},\"周二\":{\"用电量\":110.2,\"用水量\":210.4},\"周六\":{\"用电量\":150.4,\"用水量\":250.8}}",
					 //    environmentalAnomalySum: 0,
					 //    abnormalDateList: [
					 //      {
					 //        drillingCode: "rig_gateway_test001",
					 //        abnormalType: 0,
					 //        type: 0,
					 //        createdAt: "2025-03-07 15:31:58"
					 //      }
					 //    ]
				},
				dataList: [], // 动态数据

				pieData: [
					
				],
				cateData: [],
				lineData: [], // 修改为数组格式
				isLoading: true,
				isTopOfPage: true,  // 标记是否在页面顶部
				scrollTop: 0,       // 记录滚动位置
				currentPage: 1,
				flag1: true,
				lastScrollTime: 0
			};
		},
		onShow() {
			this.getDataBoard();
			this.scrollTop = 0;
			this.isTopOfPage = true;
		},
		created() {
			// 获取屏幕尺寸
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.windowWidth
			this.screenHeight = systemInfo.windowHeight
		},
		methods: {
			async getDataBoard() {
				try {
					this.isLoading = true;
					const res = await Request.get('/dataBoard/get_ls');
					console.log('接口返回数据:', res);
					if (res.status === 0) {
						this.dataBoard = res.data;
						// 更新饼图数据
						this.pieData = [
							{ 
								// name: '已完成', 
							value: this.dataBoard.taskSumYes || 0 },
							{ 
								// name: '未完成', 
							value: this.dataBoard.taskNo || 0 }
							
						];
						
						// 更新柱状图数据
						if(this.dataBoard.expendSum) {
							try {
								const expendData = JSON.parse(this.dataBoard.expendSum);
								this.cateData = Object.keys(expendData).map(day => [
									day,
									expendData[day]['用电量'] || 0,
									expendData[day]['用水量'] || 0
								]);
							} catch(e) {
								console.error('解析expendSum失败:', e);
								this.cateData = [];
							}
						}
						
						// 更新折线图数据
						if(this.dataBoard.runTime) {
							try {
								const runTimeData = JSON.parse(this.dataBoard.runTime);
								this.lineData = Object.keys(runTimeData).map(date => ({
									date: date,
									value: runTimeData[date]
								}));
								console.log('折线图:', runTimeData);
							} catch(e) {
								console.error('解析runTime失败:', e);
								this.lineData = [];
							}
						}
					}
				} catch (error) {
					console.error('获取数据失败:', error);
					// 确保数据为空数组而不是null
					this.pieData = [];
					this.cateData = [];
					this.lineData = [];
				} finally {
					this.isLoading = false;
				}
			},
			// 改进下拉刷新方法
			async onRefresh() {
				
				
				this.isPulling = false;
				this.isRefreshing = true;
				
				try {
					await this.getDataBoard();
					uni.showToast({
						title: '刷新成功',
						icon: 'none',
						duration: 1000
					});
				} catch (error) {
					console.error('刷新失败:', error);
					uni.showToast({
						title: '刷新失败',
						icon: 'none',
						duration: 1000
					});
				} finally {
					this.isRefreshing = false;
				}
			},
			
			// 添加下拉事件处理
			onPulling(e) {
				// 只在顶部时才设置pulling状态
				if (this.isTopOfPage) {
					this.isPulling = true;
					console.log('下拉中...');
				} else {
					console.log('非顶部位置，忽略下拉');
				}
			},
			onScroll(e) {
				this.lastScrollTime = Date.now();
				this.scrollTop = e.detail.scrollTop;
				this.isTopOfPage = this.scrollTop === 0;
			}
		},
		computed: {
			// 增加一个计算属性，确保值确实更新了
			canRefresh() {
				return this.scrollTop === 0;
			},
			// 限制异常数据只显示前10条
			limitedAbnormalData() {
				if (!this.dataBoard.abnormalDateList || !Array.isArray(this.dataBoard.abnormalDateList)) {
					return [];
				}
				return this.dataBoard.abnormalDateList.slice(0, 10);
			}
		}
	};
</script>
<style>
	page{
		padding:0;
		padding:0 !important;
		height: 100%;
		/* 移除背景图属性，我们将使用单独的层 */
	}
</style>
<style lang="scss" scoped>
.bg-image {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: url("@/static/image/BG.png") no-repeat center center;
	background-size: cover;
	z-index: -1; /* 确保背景在最底层 */
}
.container {
		flex: 1;
		position: relative;
		height: 100vh; /* 确保容器高度占满整个视口 */
		z-index: 1; /* 确保内容在背景之上 */
	}
// .bg-layer {
//   position: fixed;
//   top: 0;
//   left: 0;

//   background-image: url("static/image/BG.png");
//   background-position: center top; /* 从顶部开始 */
//   background-repeat: no-repeat;
//   background-attachment: fixed; /* 关键属性 */
//   background-size: cover;
//   z-index: -1;
// }
.content-wrapper {
	   // width: 100%;
	   // height: 100%;
	  
	   padding: 0 32rpx;
		position: absolute;
		top: 108rpx;
		left: 0;
		right: 0;
		bottom: 0;
		flex: 1;
	}
	
	
	.header {
		padding-top: 50rpx;
		// height: 88rpx;
		font-size: 16rpx;
		width: 100%;
		z-index: 200;
		position: fixed;
		
		top: 0;
		left: 0;
		// font-weight: 600;
	    text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		
		// background-color: rgba(255,255,255,0.03);
	}
.content {
	// padding: 0 32rpx;
	// width: 100%;
	// padding: 0 32rpx;
	
}
.data_header{
	display: flex;
	// flex-flow: row;
	margin-top: 32rpx;
	// justify-content: space-between;
   
}


.data-items {
	flex:1;
	padding: 32rpx;
	// color: #fff;
	border-radius: 12rpx;
	background: rgba(255, 255, 255, 0.04);
	border: 0.5px solid rgba(255, 255, 255, 0.0972);
}

.data-title{
	color: rgba(255, 255, 255, 0.45);
	font-size: 30rpx;
	font-weight: normal;
	line-height: 44rpx;
	margin-bottom: 10rpx;
}
.data-value{
	color: rgba(255, 255, 255, 0.85);
	font-size: 40rpx;
	font-weight: 500;
	line-height: 44rpx;
}
.empty-state {
	padding: 120rpx 0;
	align-items: center;
	justify-content: center;
}
.echarts-container {
	margin-top: 32rpx ;
	padding: 32rpx;
	
	background: rgba(255, 255, 255, 0.04);
	border-radius: 12rpx;
	border: 0.5px solid rgba(255, 255, 255, 0.0972);
}
.e_title{
	font-size: 32rpx;
	font-weight: normal;
	line-height: 44rpx;
	letter-spacing: 0px;
	margin-bottom: 32rpx;
	color: #9E9E9E;
}
.chart-data {
	display: flex;
	justify-content: space-around;
	margin-top: 32rpx;
}
.data-item {
	display: flex;
	align-items: center;
}
.color-block {
	width: 16rpx;
	height: 16rpx;
	border-radius: 4rpx;
	margin-right: 8rpx;
}
.data-label {
	color: rgba(255, 255, 255, 0.85);
	font-size: 24rpx;
}
.error_cont{
	display: flex;
	margin-bottom:24rpx ;
}
.error_img{
	display: flex;
	align-items: center;
	justify-content: center;
	image{
		width: 28rpx;
		height: 28rpx;
	}
}
.error_right{
	margin-left: 16rpx;
}
.error_title{
	font-size: 28rpx;
	font-weight: normal;
	line-height: 40rpx;
	color: rgba(255,255,255,0.85);
}
.error_value{
	color: #9CA3AF;
	font-size: 24rpx;
	font-weight: normal;
	line-height: 32rpx;
}
.loading {
	padding: 120rpx 0;
	align-items: center;
	justify-content: center;
}
</style>