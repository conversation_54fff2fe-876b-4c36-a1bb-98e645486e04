import { Chatroom } from './types';
/**
 * 调用方式:
 * ```js
 * chatroom.chatroom.getInfo(options)
 * ```
 */
export interface ChatroomServiceInterface {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 获取本聊天室的信息
     *
     * @example
     * ```js
     * const chatroomInfo = await chatroom.chatroom.getInfo()
     * ```
     * @locale
     *
     * @locale en
     * Get the information of this chatroom
     * @locale
     */
    getInfo(): Promise<Chatroom>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 更新聊天室信息
     *
     * #### 影响范围
     * - 当更新聊天室时, 所有聊天室成员会收到类型为 'updateChatroom' 的聊天室通知消息。
     *
     * @example
     * ```js
     * chatroom.chatroom.updateInfo({
     *  chatroom: {
     *     "announcement": "announcement"
     *  },
     *  needNotify: true
     * })
     *
     * // 接收方
     * chatroom.on('chatroomMsg', (msg) => {
     *   console.log(msg.attach.type === 'updateChatroom')
     * })
     * ```
     * @locale
     *
     * @locale en
     * Update chatroom information
     *
     * When a chatroom is updated, all chatroom members will receive a chatroom notification message of the ' updateChatroom ' type.
     * @locale
     */
    updateInfo(options: UpdateInfoOptions): Promise<void>;
}
export interface UpdateInfoOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 待更新的聊天室信息
     * @locale
     *
     * @locale en
     * Chatroom information to be updated
     * @locale
     */
    chatroom: {
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 聊天室名
         * @locale
         *
         * @locale en
         * chatroom name
         * @locale
         */
        name?: string;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 聊天室公告
         * @locale
         *
         * @locale en
         * Chatroom announcement
         * @locale
         */
        announcement?: string;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 直播地址
         * @locale
         *
         * @locale en
         * Live address
         * @locale
         */
        broadcastUrl?: string;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 聊天室扩展字段
         * @locale
         *
         * @locale en
         * Chatroom extension field
         * @locale
         */
        ext?: string;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 队列管理权限：0:所有人都有权限变更队列，1:只有主播管理员才能操作变更
         * @locale
         *
         * @locale en
         * Queue management permission: 0: All members have permission to change the queue, 1: Only the host can change the queue.
         * @locale
         */
        queuelevel: number;
    };
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否需要下发对应的通知消息
     * @locale
     *
     * @locale en
     * Whether the corresponding notification message needs to be sent
     * @locale
     */
    needNotify: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 扩展字段
     * @locale
     *
     * @locale en
     * extension field
     * @locale
     */
    ext: string;
}
