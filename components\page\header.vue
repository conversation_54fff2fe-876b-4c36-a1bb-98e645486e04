<template>
<view class="header_cont">
  <view class="custom-header">
    <view class="left"  v-if="showBack">
      <!-- <text class="iconfont icon-arrowleft"></text> -->
	  <image @click="handleBack" class="iconfont" src="../../static/image/left.png" mode=""></image>
    </view>
	<view class="left"  v-if="showBack2">
	  <!-- <text class="iconfont icon-arrowleft"></text> -->
	  <image @click="handleBack2" class="iconfont" src="../../static/image/left.png" mode=""></image>
	</view>
	<view class="left"  v-if="showBack3">
	  <!-- <text class="iconfont icon-arrowleft"></text> -->
	  <image @click="handleBack3" class="iconfont" src="../../static/image/left.png" mode=""></image>
	</view>
    <view class="title">
      {{ title }}
    </view>
    <view class="right" v-if="rightContent || $slots.right">
      <text v-if="rightType == 'text'" @click="handleRightClick">{{ rightContent }}</text>
      <view v-else-if="rightType == 'slot'">
        <slot name="right"></slot>
      </view>
    </view>
  </view>
  </view>
</template>

<script>
export default {
  props: {
    title: {
      type: String,
      default: '',
    },
    showBack: { // 是否展示返回箭头
      type: Boolean,
      default: false,
    },
	showBack2: { // 是否展示返回箭头
	  type: Boolean,
	  default: false,
	},
	showBack3: { // 是否展示返回箭头
	  type: Boolean,
	  default: false,
	},
    rightContent: {
      type: String,
      default: ''
    },
    rightType: {
      type: String,
      default: ''
    },
     rightClick:{
        type: Function,
      default: null
     }
  },
  methods: {
    handleBack() {
      uni.navigateBack({ delta: 1 });
    },
	handleBack2() {
	  uni.switchTab ({ url:"/pages/apply/apply" });
	},
	handleBack3() {
	  uni.switchTab ({ url:"/pages/user/user" });
	},
      handleRightClick(){
       if(this.rightClick && typeof this.rightClick == 'function'){
          this.rightClick()
       }
      }
  },
};
</script>

<style scoped>
.header_cont{
	  position: fixed; /* 关键属性：设置为固定定位 */
	  top: 0;  /* 关键属性：顶部对齐 */
	  left: 0;
	  width: 100%;     
	  /* height: 88rpx; */ 
	  z-index: 100; /* 设置 z-index，确保在其他元素之上 */
}
.custom-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-top: 76rpx;
  height: 80rpx;
  background: rgba(255, 255, 255, 0.0362);
  /* padding: 0 15px; */
  /* padding-top: 76rpx; */
  position: relative;
  /* color: #fff; */
  /* 确保绝对定位子元素相对于它定位 */
}

.custom-header .left {
  position: absolute;
  /* background-color: #fff; */
  height: 48rpx;
  width: 48rpx;
  left: 12rpx;
  /* color: #fff; */
}
.iconfont{
	z-index: 100;
	height: 48rpx;
	width: 48rpx;
}

.custom-header .title {
  font-size: 34rpx;
  font-weight: 600;
  flex: 1;
  color: #fff;
  text-align: center;
  /* letter-spacing: 0px; */
}

.custom-header .right {
  position: absolute;
  color: #fff;
  right: 15px;
  /* cursor:pointer; */
}

.icon-arrowleft {
  font-size: 20px;
  color: #fff;
  line-height: 50px;
  /* cursor: pointer; */
}
</style>
