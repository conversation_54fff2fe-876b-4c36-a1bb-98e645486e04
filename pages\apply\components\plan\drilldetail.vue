<template>
		<custom-header style="height: 88rpx;" title="打孔管理" showBack />
  <view class="drill-detail">

    
    <!-- 步骤条容器，可横向滚动 -->
    <scroll-view scroll-x class="steps-scroll-container" :scroll-into-view="`step-${currentStep}`" scroll-with-animation>
      <view class="steps-container">
        <up-steps :current="currentStep" class="custom-steps">
          <up-steps-item title="下发任务" :id="`step-0`"></up-steps-item>
          <up-steps-item title="开孔申请" :id="`step-1`"></up-steps-item>
          <up-steps-item title="施工过程" :id="`step-2`"></up-steps-item>
          <up-steps-item title="退钻" :id="`step-3`"></up-steps-item>
          <up-steps-item title="封孔" :id="`step-4`"></up-steps-item>
          <up-steps-item title="终孔" :id="`step-5`"></up-steps-item>
          <up-steps-item v-if="taskStatus == 6" title="连抽" :id="`step-6`"></up-steps-item>
          <up-steps-item v-if="taskStatus === 7" title="废孔" :id="`step-6`"></up-steps-item>
        </up-steps>
      </view>
    </scroll-view>
    
    <!-- 审批信息 -->
    <view class="approval-info">
      <view class="info-item">
        <view class="label">审批名称:</view>
        <view class="value">{{ approvalData?.title || '暂无' }}</view>
      </view>
      <view class="info-item">
        <view class="label">提交人员:</view>
        <view class="value">{{ approvalData?.submitter || '暂无' }}</view>
      </view>
	  <view class="info-item">
	    <view class="label">审批人:</view>
	    <view class="value">{{ approvalData?.approver || '暂无' }}</view>
	  </view>
      <view class="info-item">
        <view class="label">审批状态:</view>
        <view class="value" v-html="approvalData ? getStatusText(approvalData.status) : '暂无'"></view>
      </view>
      <view class="info-item">
        <view class="label">创建时间:</view>
        <view class="value">{{ approvalData?.createdAt || '暂无' }}</view>
      </view>
      
    </view>

    <!-- 底部按钮 -->
    <view class="button-group">
      <up-button class="button_prev" @click="handlePrev"  v-if="currentStep > 0" :disabled="currentStep == 0">上一步</up-button>
      <up-button class="button_next" @click="handleNext"  v-if="currentStep < 6" :disabled="currentStep >= taskStatus" type="primary">下一步</up-button>
    </view>
  </view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
export default {
  name: 'DrillDetail',
  components: {
  	customHeader,
  	
  	// siteCont
  },
  data() {
	 
    return {
      taskId: '',
      currentStep: 0,
      taskStatus: 0,
      approvalData: null
    }
  },
  
  onLoad(options) {
    if (options.id) {
      this.taskId = options.id
      this.getTaskStatus()
    }
  },
  
  methods: {
    async getTaskStatus() {
      try {
		  let task_data={
			  id: this.taskId
		  }
        const res = await Request.get('/drill/get_info',task_data)
        if (res.data) {
          this.taskStatus = res.data.status
          if(this.taskStatus==7){
            this.currentStep=6
          } else {
            this.currentStep = res.data.status
          }
          this.getApprovalInfo()
        }
      } catch (error) {
        uni.showToast({ title: '获取任务状态失败', icon: 'none' })
      }
    },
    
    async getApprovalInfo() {
      try {
        let approve_data = {
          taskId: this.taskId,
          status: this.currentStep
        }
        const res = await Request.post('/drill/get_approve', approve_data)
        this.approvalData = res.data || null
      } catch (error) {
        uni.showToast({ title: '获取审批信息失败', icon: 'none' })
      }
    },
    
    handlePrev() {
      if (this.currentStep > 0) {
        this.currentStep--
        this.getApprovalInfo()
      }
    },
    
    handleNext() {
      if (this.currentStep < this.taskStatus) {
        this.currentStep++
        this.getApprovalInfo()
      }
    },
    
    getStatusText(status) {
      const statusMap = {
        0: '<span style="color: #D89614; font-weight: 500;">待审批</span>',
        1: '<span style="color: #2ea417; font-weight: 500;">已同意</span>',
        2: '<span style="color: #e52848; font-weight: 500;">已拒绝</span>'
      }
      return statusMap[status] || '暂无';
    },
    
    getStatusClass(status) {
      const statusMap = {
        0: 'status-pending',
        1: 'status-approved',
        2: 'status-rejected'
      }
      return approvalData ? statusMap[status] || '' : '';
    }
  }
}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style scoped>
.drill-detail {
  padding: 20px;
  padding-top: 176rpx;
  height: 100vh;
  display: flex;
  flex-direction: column;
}
:deep(.u-text__value--main){
	color: #3c9cff !important;
}
:deep(.u-text__value--content) {
	color: rgba(255,255,255,0.85) !important;
	}

.page-title {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
}

.steps-scroll-container {
  width: 100%;
  overflow: hidden;
  padding: 10px 0;
}

.steps-container {
  width: 100%;
}

/* 自定义步骤条样式，控制每个步骤的宽度 */
.custom-steps :deep(.u-steps-item) {
  min-width: 33.33%; /* 每个步骤占三分之一宽度，确保一行显示3个 */
}

.approval-info {
  margin-top: 20px;
  //flex: 1;
  overflow-y: auto;
  /* background: #1e1f23; */
  background: rgba(255, 255, 255, 0.0362);
  border-radius: 8rpx;
  padding:16rpx 32rpx;
  padding-top: 32rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
}

.label {
  /* width: 100px; */
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.65);
}

.value {
  /* flex: 1; */
 font-size: 28rpx;
   color: rgba(255, 255, 255, 0.85);
}

.button-group {
  width:calc(100% - 40px);
  position:fixed;
  bottom:100rpx;
  margin-top: 20px;
  display: flex;
  
  justify-content: space-between;
  padding: 10px 0;
}

.button-group up-button {
  margin: 0 5rpx;
}
.button_prev{
	background: rgba(255, 255, 255, 0.08);
	box-sizing: border-box;
	color: rgba(255,255,255,0.85);
	border: 0rpx solid rgba(0,0,0,0.15);
	margin-right: 10rpx;
}
.button_next{
	margin-left: 10rpx;
	/* border-radius: 8px; */
	background: linear-gradient(90deg, #3161FE -2%, #5A31FE 100%);
	border: 0rpx solid rgba(255,255,255,0.15);
}
.status-pending {
  color: #D89614 !important; /* 待审批 */
  font-weight: 500;
}

.status-approved {
  color: #2ea417 !important; /* 已同意 */
  font-weight: 500;
}

.status-rejected {
  color: #e52848 !important; /* 已拒绝 */
  font-weight: 500;
}
</style>