"use weex:vue";

if (typeof Promise !== 'undefined' && !Promise.prototype.finally) {
  Promise.prototype.finally = function(callback) {
    const promise = this.constructor
    return this.then(
      value => promise.resolve(callback()).then(() => value),
      reason => promise.resolve(callback()).then(() => {
        throw reason
      })
    )
  }
};

if (typeof uni !== 'undefined' && uni && uni.requireGlobal) {
  const global = uni.requireGlobal()
  ArrayBuffer = global.ArrayBuffer
  Int8Array = global.Int8Array
  Uint8Array = global.Uint8Array
  Uint8ClampedArray = global.Uint8ClampedArray
  Int16Array = global.Int16Array
  Uint16Array = global.Uint16Array
  Int32Array = global.Int32Array
  Uint32Array = global.Uint32Array
  Float32Array = global.Float32Array
  Float64Array = global.Float64Array
  BigInt64Array = global.BigInt64Array
  BigUint64Array = global.BigUint64Array
};


(()=>{var v=Object.create;var f=Object.defineProperty;var _=Object.getOwnPropertyDescriptor;var C=Object.getOwnPropertyNames;var b=Object.getPrototypeOf,B=Object.prototype.hasOwnProperty;var I=(a,i)=>()=>(i||a((i={exports:{}}).exports,i),i.exports);var S=(a,i,l,r)=>{if(i&&typeof i=="object"||typeof i=="function")for(let s of C(i))!B.call(a,s)&&s!==l&&f(a,s,{get:()=>i[s],enumerable:!(r=_(i,s))||r.enumerable});return a};var x=(a,i,l)=>(l=a!=null?v(b(a)):{},S(i||!a||!a.__esModule?f(l,"default",{value:a,enumerable:!0}):l,a));var d=I((D,y)=>{y.exports=Vue});var t=x(d());var p=(a,i)=>{let l=a.__vccOpts||a;for(let[r,s]of i)l[r]=s;return l};var m="/static/image/left.png",T={header_cont:{"":{position:"fixed",top:0,left:0,width:100,zIndex:100}},"custom-header":{"":{display:"flex",flexDirection:"row",alignItems:"center",justifyContent:"space-between",paddingTop:"76rpx",height:"80rpx",backgroundColor:"rgba(255,255,255,0.0362)",position:"relative"}},left:{".custom-header ":{position:"absolute",height:"48rpx",width:"48rpx",left:"12rpx"}},iconfont:{"":{zIndex:100,height:"48rpx",width:"48rpx"}},title:{".custom-header ":{fontSize:"34rpx",fontWeight:"600",flex:1,color:"#ffffff",textAlign:"center"}},right:{".custom-header ":{position:"absolute",color:"#ffffff",right:15}},"icon-arrowleft":{"":{fontSize:20,color:"#ffffff",lineHeight:50}}},z={props:{title:{type:String,default:""},showBack:{type:Boolean,default:!1},showBack2:{type:Boolean,default:!1},showBack3:{type:Boolean,default:!1},rightContent:{type:String,default:""},rightType:{type:String,default:""},rightClick:{type:Function,default:null}},methods:{handleBack(){uni.navigateBack({delta:1})},handleBack2(){uni.switchTab({url:"/pages/apply/apply"})},handleBack3(){uni.switchTab({url:"/pages/user/user"})},handleRightClick(){this.rightClick&&typeof this.rightClick=="function"&&this.rightClick()}}};function W(a,i,l,r,s,o){return(0,t.openBlock)(),(0,t.createElementBlock)("view",{class:"header_cont",renderWhole:!0},[(0,t.createElementVNode)("view",{class:"custom-header"},[l.showBack?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:0,class:"left"},[(0,t.createElementVNode)("u-image",{onClick:i[0]||(i[0]=(...n)=>o.handleBack&&o.handleBack(...n)),class:"iconfont",src:m,mode:""})])):(0,t.createCommentVNode)("",!0),l.showBack2?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1,class:"left"},[(0,t.createElementVNode)("u-image",{onClick:i[1]||(i[1]=(...n)=>o.handleBack2&&o.handleBack2(...n)),class:"iconfont",src:m,mode:""})])):(0,t.createCommentVNode)("",!0),l.showBack3?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:2,class:"left"},[(0,t.createElementVNode)("u-image",{onClick:i[2]||(i[2]=(...n)=>o.handleBack3&&o.handleBack3(...n)),class:"iconfont",src:m,mode:""})])):(0,t.createCommentVNode)("",!0),(0,t.createElementVNode)("view",{class:"title"},[(0,t.createElementVNode)("u-text",null,(0,t.toDisplayString)(l.title),1)]),l.rightContent||a.$slots.right?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:3,class:"right"},[l.rightType=="text"?((0,t.openBlock)(),(0,t.createElementBlock)("u-text",{key:0,onClick:i[3]||(i[3]=(...n)=>o.handleRightClick&&o.handleRightClick(...n))},(0,t.toDisplayString)(l.rightContent),1)):l.rightType=="slot"?((0,t.openBlock)(),(0,t.createElementBlock)("view",{key:1},[(0,t.renderSlot)(a.$slots,"right")])):(0,t.createCommentVNode)("",!0)])):(0,t.createCommentVNode)("",!0)])])}var w=p(z,[["render",W],["styles",[T]]]);var e=x(d());var k="/static/image/BG.png";var F={container:{"":{flex:1,position:"relative"}},"background-image":{"":{position:"absolute",top:0,left:0}},"content-wrapper":{"":{position:"absolute",top:0,left:0,right:0,bottom:0,flex:1}},header:{"":{paddingTop:"50rpx",fontSize:"34rpx",fontWeight:"600",textAlign:"center",display:"flex",justifyContent:"center",alignItems:"center"}},content:{"":{paddingTop:"52rpx",paddingLeft:"26rpx",paddingRight:"26rpx"}},"menu-list":{"":{marginTop:"44rpx",backgroundColor:"rgba(255,255,255,0.0362)",paddingTop:"32rpx",paddingRight:"32rpx",paddingBottom:"32rpx",paddingLeft:"32rpx",borderRadius:"12rpx"}},"menu-header":{"":{fontSize:"28rpx",color:"rgba(255,255,255,0.7)",marginBottom:"20rpx"}},"grid-list":{"":{flexDirection:"row",flexWrap:"nowrap",justifyContent:"flex-start"}},"grid-item":{"":{width:"160rpx",alignItems:"center",paddingTop:"26rpx"}},"icon-wrapper":{"":{width:"84rpx",height:"84rpx",backgroundColor:"rgba(255,255,255,0.1)",borderRadius:"32rpx",alignItems:"center",justifyContent:"center",marginBottom:"14rpx"}},icon:{"":{width:"36rpx",height:"36rpx"}},"item-text":{"":{fontSize:"21rpx",color:"rgba(255,255,255,0.85)",textAlign:"center"}}},H={components:{customHeader:w},data(){return{screenWidth:0,screenHeight:0,menuItems:[{icon:"/static/image/apply/plan.png",text:"\u4E0B\u53D1\u4EFB\u52A1",path:"/pages/apply/components/plan"},{icon:"/static/image/apply/slot.png",text:"\u6253\u5B54\u7BA1\u7406",path:"/pages/apply/components/project"},{icon:"/static/image/apply/slots.png",text:"\u65BD\u5DE5\u8BB0\u5F55",path:"/pages/apply/components/slot"},{icon:"/static/image/apply/commition.png",text:"\u4FE1\u606F\u4E0A\u62A5",path:"/pages/apply/components/message"}],proItems:[{icon:"/static/image/apply/equip.png",text:"\u8BBE\u5907\u7BA1\u7406",path:"/pages/apply/components/equip"},{icon:"/static/image/apply/drill.png",text:"\u94BB\u5177\u7BA1\u7406",path:"/pages/apply/components/drill"}],applyItems:[{icon:"/static/image/apply/noodles.png",text:"\u91C7\u9762\u7BA1\u7406",path:"/pages/apply/components/noodles"},{icon:"/static/image/apply/laneway.png",text:"\u5DF7\u9053\u7BA1\u7406",path:"/pages/apply/components/laneway"},{icon:"/static/image/apply/drillsite.png",text:"\u94BB\u573A\u7BA1\u7406",path:"/pages/apply/components/drillsite"}],Items:[{icon:"/static/image/apply/conputer.png",text:"\u8FD0\u7EF4\u4E2D\u5FC3",path:"/pages/apply/components/computer"},{icon:"/static/image/apply/energency.png",text:"\u5E94\u6025\u5904\u7406",path:"/pages/apply/components/emergency"},{icon:"/static/image/apply/log.png",text:"\u62A5\u8868",path:"/pages/apply/components/report"}]}},created(){let a=uni.getSystemInfoSync();this.screenWidth=a.windowWidth,this.screenHeight=a.windowHeight},methods:{handleMenuClick(a){uni.navigateTo({url:a.path})},handleproClick(a){uni.navigateTo({url:a.path})},handleapplyClick(a){uni.navigateTo({url:a.path})},handleClick(a){uni.navigateTo({url:a.path})}}};function R(a,i,l,r,s,o){return(0,e.openBlock)(),(0,e.createElementBlock)("scroll-view",{scrollY:!0,showScrollbar:!0,enableBackToTop:!0,bubble:"true",style:{flexDirection:"column"}},[(0,e.createElementVNode)("view",{class:"container"},[(0,e.createElementVNode)("u-image",{class:"background-image",src:k,mode:"aspectFill",style:(0,e.normalizeStyle)({width:s.screenWidth+"px",height:s.screenHeight+"px"})},null,4),(0,e.createElementVNode)("view",{class:"content-wrapper"},[(0,e.createElementVNode)("view",{class:"header",background:"transparent",style:{color:"#fff"}},[(0,e.createElementVNode)("u-text",{style:{"font-size":"34rpx",color:"rgba(255, 255, 255, 0.85)"}})]),(0,e.createElementVNode)("scroll-view",{class:"content"},[(0,e.createElementVNode)("view",{class:"menu-list"},[(0,e.createElementVNode)("u-text",{class:"menu-header"},"\u65BD\u5DE5\u7BA1\u7406"),(0,e.createElementVNode)("view",{class:"grid-list"},[((0,e.openBlock)(!0),(0,e.createElementBlock)(e.Fragment,null,(0,e.renderList)(s.menuItems,(n,c)=>((0,e.openBlock)(),(0,e.createElementBlock)("view",{key:c,class:"grid-item",onClick:u=>o.handleMenuClick(n)},[(0,e.createElementVNode)("view",{class:"icon-wrapper"},[(0,e.createElementVNode)("u-image",{class:"icon",src:n.icon,mode:"aspectFit"},null,8,["src"])]),(0,e.createElementVNode)("u-text",{class:"item-text"},(0,e.toDisplayString)(n.text),1)],8,["onClick"]))),128))])]),(0,e.createElementVNode)("view",{class:"menu-list"},[(0,e.createElementVNode)("u-text",{class:"menu-header"},"\u8D44\u4EA7\u7BA1\u7406"),(0,e.createElementVNode)("view",{class:"grid-list"},[((0,e.openBlock)(!0),(0,e.createElementBlock)(e.Fragment,null,(0,e.renderList)(s.proItems,(n,c)=>((0,e.openBlock)(),(0,e.createElementBlock)("view",{key:c,class:"grid-item",onClick:u=>o.handleproClick(n)},[(0,e.createElementVNode)("view",{class:"icon-wrapper"},[(0,e.createElementVNode)("u-image",{class:"icon",src:n.icon,mode:"aspectFit"},null,8,["src"])]),(0,e.createElementVNode)("u-text",{class:"item-text"},(0,e.toDisplayString)(n.text),1)],8,["onClick"]))),128))])]),(0,e.createElementVNode)("view",{class:"menu-list"},[(0,e.createElementVNode)("u-text",{class:"menu-header"},"\u77FF\u533A\u4FE1\u606F"),(0,e.createElementVNode)("view",{class:"grid-list"},[((0,e.openBlock)(!0),(0,e.createElementBlock)(e.Fragment,null,(0,e.renderList)(s.applyItems,(n,c)=>((0,e.openBlock)(),(0,e.createElementBlock)("view",{key:c,class:"grid-item",onClick:u=>o.handleapplyClick(n)},[(0,e.createElementVNode)("view",{class:"icon-wrapper"},[(0,e.createElementVNode)("u-image",{class:"icon",src:n.icon,mode:"aspectFit"},null,8,["src"])]),(0,e.createElementVNode)("u-text",{class:"item-text"},(0,e.toDisplayString)(n.text),1)],8,["onClick"]))),128))])]),(0,e.createElementVNode)("view",{class:"menu-list"},[(0,e.createElementVNode)("u-text",{class:"menu-header"},"\u5176\u4ED6"),(0,e.createElementVNode)("view",{class:"grid-list"},[((0,e.openBlock)(!0),(0,e.createElementBlock)(e.Fragment,null,(0,e.renderList)(s.Items,(n,c)=>((0,e.openBlock)(),(0,e.createElementBlock)("view",{key:c,class:"grid-item",onClick:u=>o.handleClick(n)},[(0,e.createElementVNode)("view",{class:"icon-wrapper"},[(0,e.createElementVNode)("u-image",{class:"icon",src:n.icon,mode:"aspectFit"},null,8,["src"])]),(0,e.createElementVNode)("u-text",{class:"item-text"},(0,e.toDisplayString)(n.text),1)],8,["onClick"]))),128))])])])])])])}var g=p(H,[["render",R],["styles",[F]]]);var h=plus.webview.currentWebview();if(h){let a=parseInt(h.id),i="pages/apply/apply",l={};try{l=JSON.parse(h.__query__)}catch(s){}g.mpType="page";let r=Vue.createPageApp(g,{$store:getApp({allowDefault:!0}).$store,__pageId:a,__pagePath:i,__pageQuery:l});r.provide("__globalStyles",Vue.useCssStyles([...__uniConfig.styles,...g.styles||[]])),r.mount("#root")}})();
