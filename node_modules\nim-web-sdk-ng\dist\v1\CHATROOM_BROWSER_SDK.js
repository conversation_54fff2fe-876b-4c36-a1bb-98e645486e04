/**
 * 
 *   Version: 10.8.30
 * 
 *   Git Hash: 53d9f639411d85eb37f1039804197c286cc2e303
 * 
 *   Created At: 4/24/2025, 7:32:47 AM
 * 
 *   Target: CHATROOM_BROWSER_SDK.js
 *   
 */

!function(t,a){"object"==typeof exports&&"undefined"!=typeof module?module.exports=a():"function"==typeof define&&define.amd?define(a):(t="undefined"!=typeof globalThis?globalThis:t||self).Chatroom=a()}(this,(function(){"use strict";var t="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function createCommonjsModule(t){var a={exports:{}};return t(a,a.exports),a.exports}var a,u,_=getDefaultExportFromCjs(createCommonjsModule((function(t){t.exports=function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t},t.exports.__esModule=!0,t.exports.default=t.exports}))),check=function(t){return t&&t.Math==Math&&t},h=check("object"==typeof globalThis&&globalThis)||check("object"==typeof window&&window)||check("object"==typeof self&&self)||check("object"==typeof t&&t)||function(){return this}()||Function("return this")(),fails=function(t){try{return!!t()}catch(t){return!0}},m=!fails((function(){var t=function(){}.bind();return"function"!=typeof t||t.hasOwnProperty("prototype")})),E=Function.prototype,g=E.apply,T=E.call,I="object"==typeof Reflect&&Reflect.apply||(m?T.bind(g):function(){return T.apply(g,arguments)}),S=Function.prototype,M=S.bind,N=S.call,A=m&&M.bind(N,N),O=m?function(t){return t&&A(t)}:function(t){return t&&function(){return N.apply(t,arguments)}},isCallable=function(t){return"function"==typeof t},R=!fails((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]})),C=Function.prototype.call,b=m?C.bind(C):function(){return C.apply(C,arguments)},P={}.propertyIsEnumerable,x=Object.getOwnPropertyDescriptor,k={f:x&&!P.call({1:2},1)?function propertyIsEnumerable(t){var a=x(this,t);return!!a&&a.enumerable}:P},createPropertyDescriptor=function(t,a){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:a}},w=O({}.toString),D=O("".slice),classofRaw=function(t){return D(w(t),8,-1)},L=Object,V=O("".split),U=fails((function(){return!L("z").propertyIsEnumerable(0)}))?function(t){return"String"==classofRaw(t)?V(t,""):L(t)}:L,isNullOrUndefined=function(t){return null==t},G=TypeError,requireObjectCoercible=function(t){if(isNullOrUndefined(t))throw G("Can't call method on "+t);return t},toIndexedObject=function(t){return U(requireObjectCoercible(t))},B="object"==typeof document&&document.all,Y=void 0===B&&void 0!==B?function(t){return"object"==typeof t?null!==t:isCallable(t)||t===B}:function(t){return"object"==typeof t?null!==t:isCallable(t)},j={},aFunction=function(t){return isCallable(t)?t:void 0},getBuiltIn=function(t,a){return arguments.length<2?aFunction(j[t])||aFunction(h[t]):j[t]&&j[t][a]||h[t]&&h[t][a]},H=O({}.isPrototypeOf),K=getBuiltIn("navigator","userAgent")||"",W=h.process,q=h.Deno,$=W&&W.versions||q&&q.version,z=$&&$.v8;z&&(u=(a=z.split("."))[0]>0&&a[0]<4?1:+(a[0]+a[1])),!u&&K&&(!(a=K.match(/Edge\/(\d+)/))||a[1]>=74)&&(a=K.match(/Chrome\/(\d+)/))&&(u=+a[1]);var J,Q=u,X=!!Object.getOwnPropertySymbols&&!fails((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&Q&&Q<41})),Z=X&&!Symbol.sham&&"symbol"==typeof Symbol.iterator,ee=Object,te=Z?function(t){return"symbol"==typeof t}:function(t){var a=getBuiltIn("Symbol");return isCallable(a)&&H(a.prototype,ee(t))},re=String,tryToString=function(t){try{return re(t)}catch(t){return"Object"}},oe=TypeError,aCallable=function(t){if(isCallable(t))return t;throw oe(tryToString(t)+" is not a function")},getMethod=function(t,a){var u=t[a];return isNullOrUndefined(u)?void 0:aCallable(u)},ne=TypeError,ie=Object.defineProperty,ae="__core-js_shared__",se=h[ae]||function(t,a){try{ie(h,t,{value:a,configurable:!0,writable:!0})}catch(u){h[t]=a}return a}(ae,{}),ce=createCommonjsModule((function(t){(t.exports=function(t,a){return se[t]||(se[t]=void 0!==a?a:{})})("versions",[]).push({version:"3.25.0",mode:"pure",copyright:"© 2014-2022 Denis Pushkarev (zloirock.ru)",license:"https://github.com/zloirock/core-js/blob/v3.25.0/LICENSE",source:"https://github.com/zloirock/core-js"})})),le=Object,toObject=function(t){return le(requireObjectCoercible(t))},ue=O({}.hasOwnProperty),de=Object.hasOwn||function hasOwn(t,a){return ue(toObject(t),a)},pe=0,fe=Math.random(),_e=O(1..toString),uid=function(t){return"Symbol("+(void 0===t?"":t)+")_"+_e(++pe+fe,36)},he=ce("wks"),me=h.Symbol,Ee=me&&me.for,ge=Z?me:me&&me.withoutSetter||uid,wellKnownSymbol=function(t){if(!de(he,t)||!X&&"string"!=typeof he[t]){var a="Symbol."+t;X&&de(me,t)?he[t]=me[t]:he[t]=Z&&Ee?Ee(a):ge(a)}return he[t]},ve=TypeError,Te=wellKnownSymbol("toPrimitive"),toPrimitive=function(t,a){if(!Y(t)||te(t))return t;var u,_=getMethod(t,Te);if(_){if(void 0===a&&(a="default"),u=b(_,t,a),!Y(u)||te(u))return u;throw ve("Can't convert object to primitive value")}return void 0===a&&(a="number"),function(t,a){var u,_;if("string"===a&&isCallable(u=t.toString)&&!Y(_=b(u,t)))return _;if(isCallable(u=t.valueOf)&&!Y(_=b(u,t)))return _;if("string"!==a&&isCallable(u=t.toString)&&!Y(_=b(u,t)))return _;throw ne("Can't convert object to primitive value")}(t,a)},toPropertyKey=function(t){var a=toPrimitive(t,"string");return te(a)?a:a+""},ye=h.document,Ie=Y(ye)&&Y(ye.createElement),documentCreateElement=function(t){return Ie?ye.createElement(t):{}},Se=!R&&!fails((function(){return 7!=Object.defineProperty(documentCreateElement("div"),"a",{get:function(){return 7}}).a})),Me=Object.getOwnPropertyDescriptor,Ne={f:R?Me:function getOwnPropertyDescriptor(t,a){if(t=toIndexedObject(t),a=toPropertyKey(a),Se)try{return Me(t,a)}catch(t){}if(de(t,a))return createPropertyDescriptor(!b(k.f,t,a),t[a])}},Ae=/#|\.prototype\./,isForced=function(t,a){var u=Re[Oe(t)];return u==be||u!=Ce&&(isCallable(a)?fails(a):!!a)},Oe=isForced.normalize=function(t){return String(t).replace(Ae,".").toLowerCase()},Re=isForced.data={},Ce=isForced.NATIVE="N",be=isForced.POLYFILL="P",Pe=isForced,xe=O(O.bind),functionBindContext=function(t,a){return aCallable(t),void 0===a?t:m?xe(t,a):function(){return t.apply(a,arguments)}},ke=R&&fails((function(){return 42!=Object.defineProperty((function(){}),"prototype",{value:42,writable:!1}).prototype})),we=String,De=TypeError,anObject=function(t){if(Y(t))return t;throw De(we(t)+" is not an object")},Le=TypeError,Ve=Object.defineProperty,Ue=Object.getOwnPropertyDescriptor,Fe="enumerable",Ge="configurable",Be="writable",Ye={f:R?ke?function defineProperty(t,a,u){if(anObject(t),a=toPropertyKey(a),anObject(u),"function"==typeof t&&"prototype"===a&&"value"in u&&Be in u&&!u.writable){var _=Ue(t,a);_&&_.writable&&(t[a]=u.value,u={configurable:Ge in u?u.configurable:_.configurable,enumerable:Fe in u?u.enumerable:_.enumerable,writable:!1})}return Ve(t,a,u)}:Ve:function defineProperty(t,a,u){if(anObject(t),a=toPropertyKey(a),anObject(u),Se)try{return Ve(t,a,u)}catch(t){}if("get"in u||"set"in u)throw Le("Accessors not supported");return"value"in u&&(t[a]=u.value),t}},je=R?function(t,a,u){return Ye.f(t,a,createPropertyDescriptor(1,u))}:function(t,a,u){return t[a]=u,t},He=Ne.f,wrapConstructor=function(t){var Wrapper=function(a,u,_){if(this instanceof Wrapper){switch(arguments.length){case 0:return new t;case 1:return new t(a);case 2:return new t(a,u)}return new t(a,u,_)}return I(t,this,arguments)};return Wrapper.prototype=t.prototype,Wrapper},_export=function(t,a){var u,_,m,E,g,T,I,S,M=t.target,N=t.global,A=t.stat,R=t.proto,C=N?h:A?h[M]:(h[M]||{}).prototype,b=N?j:j[M]||je(j,M,{})[M],P=b.prototype;for(m in a)u=!Pe(N?m:M+(A?".":"#")+m,t.forced)&&C&&de(C,m),g=b[m],u&&(T=t.dontCallGetSet?(S=He(C,m))&&S.value:C[m]),E=u&&T?T:a[m],u&&typeof g==typeof E||(I=t.bind&&u?functionBindContext(E,h):t.wrap&&u?wrapConstructor(E):R&&isCallable(E)?O(E):E,(t.sham||E&&E.sham||g&&g.sham)&&je(I,"sham",!0),je(b,m,I),R&&(de(j,_=M+"Prototype")||je(j,_,{}),je(j[_],m,E),t.real&&P&&!P[m]&&je(P,m,E)))},Ke=Math.ceil,We=Math.floor,qe=Math.trunc||function trunc(t){var a=+t;return(a>0?We:Ke)(a)},toIntegerOrInfinity=function(t){var a=+t;return a!=a||0===a?0:qe(a)},$e=Math.max,ze=Math.min,toAbsoluteIndex=function(t,a){var u=toIntegerOrInfinity(t);return u<0?$e(u+a,0):ze(u,a)},Je=Math.min,lengthOfArrayLike=function(t){return(a=t.length)>0?Je(toIntegerOrInfinity(a),****************):0;var a},createMethod$4=function(t){return function(a,u,_){var h,m=toIndexedObject(a),E=lengthOfArrayLike(m),g=toAbsoluteIndex(_,E);if(t&&u!=u){for(;E>g;)if((h=m[g++])!=h)return!0}else for(;E>g;g++)if((t||g in m)&&m[g]===u)return t||g||0;return!t&&-1}},Qe={includes:createMethod$4(!0),indexOf:createMethod$4(!1)},Xe={},Ze=Qe.indexOf,et=O([].push),objectKeysInternal=function(t,a){var u,_=toIndexedObject(t),h=0,m=[];for(u in _)!de(Xe,u)&&de(_,u)&&et(m,u);for(;a.length>h;)de(_,u=a[h++])&&(~Ze(m,u)||et(m,u));return m},tt=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"],rt=Object.keys||function keys(t){return objectKeysInternal(t,tt)},ot=R&&!ke?Object.defineProperties:function defineProperties(t,a){anObject(t);for(var u,_=toIndexedObject(a),h=rt(a),m=h.length,E=0;m>E;)Ye.f(t,u=h[E++],_[u]);return t},nt={f:ot},it=getBuiltIn("document","documentElement"),at=ce("keys"),sharedKey=function(t){return at[t]||(at[t]=uid(t))},st=sharedKey("IE_PROTO"),EmptyConstructor=function(){},scriptTag=function(t){return"<script>"+t+"</"+"script>"},NullProtoObjectViaActiveX=function(t){t.write(scriptTag("")),t.close();var a=t.parentWindow.Object;return t=null,a},NullProtoObject=function(){try{J=new ActiveXObject("htmlfile")}catch(t){}var t,a;NullProtoObject="undefined"!=typeof document?document.domain&&J?NullProtoObjectViaActiveX(J):((a=documentCreateElement("iframe")).style.display="none",it.appendChild(a),a.src=String("javascript:"),(t=a.contentWindow.document).open(),t.write(scriptTag("document.F=Object")),t.close(),t.F):NullProtoObjectViaActiveX(J);for(var u=tt.length;u--;)delete NullProtoObject.prototype[tt[u]];return NullProtoObject()};Xe[st]=!0;var ct=Object.create||function create(t,a){var u;return null!==t?(EmptyConstructor.prototype=anObject(t),u=new EmptyConstructor,EmptyConstructor.prototype=null,u[st]=t):u=NullProtoObject(),void 0===a?u:nt.f(u,a)};_export({target:"Object",stat:!0,sham:!R},{create:ct});var lt=j.Object,ut=function create(t,a){return lt.create(t,a)},dt=String,pt=TypeError,ft=Object.setPrototypeOf||("__proto__"in{}?function(){var t,a=!1,u={};try{(t=O(Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set))(u,[]),a=u instanceof Array}catch(t){}return function setPrototypeOf(u,_){return anObject(u),function(t){if("object"==typeof t||isCallable(t))return t;throw pt("Can't set "+dt(t)+" as a prototype")}(_),a?t(u,_):u.__proto__=_,u}}():void 0);_export({target:"Object",stat:!0},{setPrototypeOf:ft});var _t=j.Object.setPrototypeOf,ht=O([].slice),mt=Function,Et=O([].concat),gt=O([].join),vt={},construct$8=function(t,a,u){if(!de(vt,a)){for(var _=[],h=0;h<a;h++)_[h]="a["+h+"]";vt[a]=mt("C,a","return new C("+gt(_,",")+")")}return vt[a](t,u)},Tt=m?mt.bind:function bind(t){var a=aCallable(this),u=a.prototype,_=ht(arguments,1),h=function bound(){var u=Et(_,ht(arguments));return this instanceof h?construct$8(a,u.length,u):a.apply(t,u)};return Y(u)&&(h.prototype=u),h};_export({target:"Function",proto:!0,forced:Function.bind!==Tt},{bind:Tt});var entryVirtual=function(t){return j[t+"Prototype"]},yt=entryVirtual("Function").bind,It=Function.prototype,bind$1=function(t){var a=t.bind;return t===It||H(It,t)&&a===It.bind?yt:a},St=createCommonjsModule((function(t){function _setPrototypeOf(a,u){var _;return t.exports=_setPrototypeOf=_t?bind$1(_=_t).call(_):function _setPrototypeOf(t,a){return t.__proto__=a,t},t.exports.__esModule=!0,t.exports.default=t.exports,_setPrototypeOf(a,u)}t.exports=_setPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports})),Mt=getDefaultExportFromCjs(createCommonjsModule((function(t){t.exports=function _inheritsLoose(t,a){t.prototype=ut(a.prototype),t.prototype.constructor=t,St(t,a)},t.exports.__esModule=!0,t.exports.default=t.exports})));_export({target:"Number",stat:!0,nonConfigurable:!0,nonWritable:!0},{MAX_SAFE_INTEGER:****************});var Nt={f:Object.getOwnPropertySymbols},At=Object.assign,Ot=Object.defineProperty,Rt=O([].concat),Ct=!At||fails((function(){if(R&&1!==At({b:1},At(Ot({},"a",{enumerable:!0,get:function(){Ot(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},a={},u=Symbol(),_="abcdefghijklmnopqrst";return t[u]=7,_.split("").forEach((function(t){a[t]=t})),7!=At({},t)[u]||rt(At({},a)).join("")!=_}))?function assign(t,a){for(var u=toObject(t),_=arguments.length,h=1,m=Nt.f,E=k.f;_>h;)for(var g,T=U(arguments[h++]),I=m?Rt(rt(T),m(T)):rt(T),S=I.length,M=0;S>M;)g=I[M++],R&&!b(E,T,g)||(u[g]=T[g]);return u}:At;_export({target:"Object",stat:!0,arity:2,forced:Object.assign!==Ct},{assign:Ct});var bt=j.Object.assign,Pt=!fails((function(){function F(){}return F.prototype.constructor=null,Object.getPrototypeOf(new F)!==F.prototype})),xt=sharedKey("IE_PROTO"),kt=Object,wt=kt.prototype,Dt=Pt?kt.getPrototypeOf:function(t){var a=toObject(t);if(de(a,xt))return a[xt];var u=a.constructor;return isCallable(u)&&a instanceof u?u.prototype:a instanceof kt?wt:null},Lt=tt.concat("length","prototype"),Vt={f:Object.getOwnPropertyNames||function getOwnPropertyNames(t){return objectKeysInternal(t,Lt)}},Ut=O([].concat),Ft=getBuiltIn("Reflect","ownKeys")||function ownKeys(t){var a=Vt.f(anObject(t)),u=Nt.f;return u?Ut(a,u(t)):a},Gt=Error,Bt=O("".replace),Yt=String(Gt("zxcasd").stack),jt=/\n\s*at [^:]*:[^\n]*/,Ht=jt.test(Yt),errorStackClear=function(t,a){if(Ht&&"string"==typeof t&&!Gt.prepareStackTrace)for(;a--;)t=Bt(t,jt,"");return t},installErrorCause=function(t,a){Y(a)&&"cause"in a&&je(t,"cause",a.cause)},Kt={},Wt=wellKnownSymbol("iterator"),qt=Array.prototype,isArrayIteratorMethod=function(t){return void 0!==t&&(Kt.Array===t||qt[Wt]===t)},$t={};$t[wellKnownSymbol("toStringTag")]="z";var zt="[object z]"===String($t),Jt=wellKnownSymbol("toStringTag"),Qt=Object,Xt="Arguments"==classofRaw(function(){return arguments}()),Zt=zt?classofRaw:function(t){var a,u,_;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(u=function(t,a){try{return t[a]}catch(t){}}(a=Qt(t),Jt))?u:Xt?classofRaw(a):"Object"==(_=classofRaw(a))&&isCallable(a.callee)?"Arguments":_},er=wellKnownSymbol("iterator"),getIteratorMethod$5=function(t){if(!isNullOrUndefined(t))return getMethod(t,er)||getMethod(t,"@@iterator")||Kt[Zt(t)]},tr=TypeError,getIterator=function(t,a){var u=arguments.length<2?getIteratorMethod$5(t):a;if(aCallable(u))return anObject(b(u,t));throw tr(tryToString(t)+" is not iterable")},iteratorClose=function(t,a,u){var _,h;anObject(t);try{if(!(_=getMethod(t,"return"))){if("throw"===a)throw u;return u}_=b(_,t)}catch(t){h=!0,_=t}if("throw"===a)throw u;if(h)throw _;return anObject(_),u},rr=TypeError,Result=function(t,a){this.stopped=t,this.result=a},or=Result.prototype,iterate=function(t,a,u){var _,h,m,E,g,T,I,S=u&&u.that,M=!(!u||!u.AS_ENTRIES),N=!(!u||!u.IS_RECORD),A=!(!u||!u.IS_ITERATOR),O=!(!u||!u.INTERRUPTED),R=functionBindContext(a,S),stop=function(t){return _&&iteratorClose(_,"normal",t),new Result(!0,t)},callFn=function(t){return M?(anObject(t),O?R(t[0],t[1],stop):R(t[0],t[1])):O?R(t,stop):R(t)};if(N)_=t.iterator;else if(A)_=t;else{if(!(h=getIteratorMethod$5(t)))throw rr(tryToString(t)+" is not iterable");if(isArrayIteratorMethod(h)){for(m=0,E=lengthOfArrayLike(t);E>m;m++)if((g=callFn(t[m]))&&H(or,g))return g;return new Result(!1)}_=getIterator(t,h)}for(T=N?t.next:_.next;!(I=b(T,_)).done;){try{g=callFn(I.value)}catch(t){iteratorClose(_,"throw",t)}if("object"==typeof g&&g&&H(or,g))return g}return new Result(!1)},nr=String,toString=function(t){if("Symbol"===Zt(t))throw TypeError("Cannot convert a Symbol value to a string");return nr(t)},normalizeStringArgument=function(t,a){return void 0===t?arguments.length<2?"":a:toString(t)},ir=!fails((function(){var t=Error("a");return!("stack"in t)||(Object.defineProperty(t,"stack",createPropertyDescriptor(1,7)),7!==t.stack)})),ar=wellKnownSymbol("toStringTag"),sr=Error,cr=[].push,lr=function AggregateError(t,a){var u,_=arguments.length>2?arguments[2]:void 0,h=H(ur,this);ft?u=ft(sr(),h?Dt(this):ur):(u=h?this:ct(ur),je(u,ar,"Error")),void 0!==a&&je(u,"message",normalizeStringArgument(a)),ir&&je(u,"stack",errorStackClear(u.stack,1)),installErrorCause(u,_);var m=[];return iterate(t,cr,{that:m}),je(u,"errors",m),u};ft?ft(lr,sr):function(t,a,u){for(var _=Ft(a),h=Ye.f,m=Ne.f,E=0;E<_.length;E++){var g=_[E];de(t,g)||u&&de(u,g)||h(t,g,m(a,g))}}(lr,sr,{name:!0});var ur=lr.prototype=ct(sr.prototype,{constructor:createPropertyDescriptor(1,lr),message:createPropertyDescriptor(1,""),name:createPropertyDescriptor(1,"AggregateError")});_export({global:!0,constructor:!0,arity:2},{AggregateError:lr});var dr,pr,fr,_r=h.WeakMap,hr=isCallable(_r)&&/native code/.test(String(_r)),mr="Object already initialized",Er=h.TypeError,gr=h.WeakMap;if(hr||se.state){var vr=se.state||(se.state=new gr),Tr=O(vr.get),yr=O(vr.has),Ir=O(vr.set);dr=function(t,a){if(yr(vr,t))throw Er(mr);return a.facade=t,Ir(vr,t,a),a},pr=function(t){return Tr(vr,t)||{}},fr=function(t){return yr(vr,t)}}else{var Sr=sharedKey("state");Xe[Sr]=!0,dr=function(t,a){if(de(t,Sr))throw Er(mr);return a.facade=t,je(t,Sr,a),a},pr=function(t){return de(t,Sr)?t[Sr]:{}},fr=function(t){return de(t,Sr)}}var Mr,Nr,Ar,Or={set:dr,get:pr,has:fr,enforce:function(t){return fr(t)?pr(t):dr(t,{})},getterFor:function(t){return function(a){var u;if(!Y(a)||(u=pr(a)).type!==t)throw Er("Incompatible receiver, "+t+" required");return u}}},Rr=Function.prototype,Cr=R&&Object.getOwnPropertyDescriptor,br=de(Rr,"name"),Pr={EXISTS:br,PROPER:br&&"something"===function something(){}.name,CONFIGURABLE:br&&(!R||R&&Cr(Rr,"name").configurable)},defineBuiltIn=function(t,a,u,_){return _&&_.enumerable?t[a]=u:je(t,a,u),t},xr=wellKnownSymbol("iterator"),kr=!1;[].keys&&("next"in(Ar=[].keys())?(Nr=Dt(Dt(Ar)))!==Object.prototype&&(Mr=Nr):kr=!0);var wr=!Y(Mr)||fails((function(){var t={};return Mr[xr].call(t)!==t}));Mr=wr?{}:ct(Mr),isCallable(Mr[xr])||defineBuiltIn(Mr,xr,(function(){return this}));var Dr={IteratorPrototype:Mr,BUGGY_SAFARI_ITERATORS:kr},Lr=zt?{}.toString:function toString(){return"[object "+Zt(this)+"]"},Vr=Ye.f,Ur=wellKnownSymbol("toStringTag"),setToStringTag=function(t,a,u,_){if(t){var h=u?t:t.prototype;de(h,Ur)||Vr(h,Ur,{configurable:!0,value:a}),_&&!zt&&je(h,"toString",Lr)}},Fr=Dr.IteratorPrototype,returnThis$1=function(){return this},Gr=Pr.PROPER,Br=Dr.BUGGY_SAFARI_ITERATORS,Yr=wellKnownSymbol("iterator"),jr="keys",Hr="values",Kr="entries",returnThis=function(){return this},iteratorDefine=function(t,a,u,_,h,m,E){!function(t,a,u,_){var h=a+" Iterator";t.prototype=ct(Fr,{next:createPropertyDescriptor(+!_,u)}),setToStringTag(t,h,!1,!0),Kt[h]=returnThis$1}(u,a,_);var g,T,I,getIterationMethod=function(t){if(t===h&&O)return O;if(!Br&&t in N)return N[t];switch(t){case jr:return function keys(){return new u(this,t)};case Hr:return function values(){return new u(this,t)};case Kr:return function entries(){return new u(this,t)}}return function(){return new u(this)}},S=a+" Iterator",M=!1,N=t.prototype,A=N[Yr]||N["@@iterator"]||h&&N[h],O=!Br&&A||getIterationMethod(h),R="Array"==a&&N.entries||A;if(R&&(g=Dt(R.call(new t)))!==Object.prototype&&g.next&&(setToStringTag(g,S,!0,!0),Kt[S]=returnThis),Gr&&h==Hr&&A&&A.name!==Hr&&(M=!0,O=function values(){return b(A,this)}),h)if(T={values:getIterationMethod(Hr),keys:m?O:getIterationMethod(jr),entries:getIterationMethod(Kr)},E)for(I in T)(Br||M||!(I in N))&&defineBuiltIn(N,I,T[I]);else _export({target:a,proto:!0,forced:Br||M},T);return E&&N[Yr]!==O&&defineBuiltIn(N,Yr,O,{name:h}),Kt[a]=O,T};Ye.f;var Wr="Array Iterator",qr=Or.set,$r=Or.getterFor(Wr);iteratorDefine(Array,"Array",(function(t,a){qr(this,{type:Wr,target:toIndexedObject(t),index:0,kind:a})}),(function(){var t=$r(this),a=t.target,u=t.kind,_=t.index++;return!a||_>=a.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==u?{value:_,done:!1}:"values"==u?{value:a[_],done:!1}:{value:[_,a[_]],done:!1}}),"values"),Kt.Arguments=Kt.Array;var zr="process"==classofRaw(h.process),Jr=wellKnownSymbol("species"),setSpecies=function(t){var a=getBuiltIn(t),u=Ye.f;R&&a&&!a[Jr]&&u(a,Jr,{configurable:!0,get:function(){return this}})},Qr=TypeError,anInstance=function(t,a){if(H(a,t))return t;throw Qr("Incorrect invocation")},Xr=O(Function.toString);isCallable(se.inspectSource)||(se.inspectSource=function(t){return Xr(t)});var Zr=se.inspectSource,noop=function(){},eo=[],to=getBuiltIn("Reflect","construct"),ro=/^\s*(?:class|function)\b/,oo=O(ro.exec),no=!ro.exec(noop),io=function isConstructor(t){if(!isCallable(t))return!1;try{return to(noop,eo,t),!0}catch(t){return!1}},ao=function isConstructor(t){if(!isCallable(t))return!1;switch(Zt(t)){case"AsyncFunction":case"GeneratorFunction":case"AsyncGeneratorFunction":return!1}try{return no||!!oo(ro,Zr(t))}catch(t){return!0}};ao.sham=!0;var so,co,lo,uo,po=!to||fails((function(){var t;return io(io.call)||!io(Object)||!io((function(){t=!0}))||t}))?ao:io,fo=TypeError,aConstructor=function(t){if(po(t))return t;throw fo(tryToString(t)+" is not a constructor")},_o=wellKnownSymbol("species"),speciesConstructor=function(t,a){var u,_=anObject(t).constructor;return void 0===_||isNullOrUndefined(u=anObject(_)[_o])?a:aConstructor(u)},ho=TypeError,validateArgumentsLength=function(t,a){if(t<a)throw ho("Not enough arguments");return t},mo=/(?:ipad|iphone|ipod).*applewebkit/i.test(K),Eo=h.setImmediate,go=h.clearImmediate,vo=h.process,To=h.Dispatch,yo=h.Function,Io=h.MessageChannel,So=h.String,Mo=0,No={},Ao="onreadystatechange";try{so=h.location}catch(t){}var run=function(t){if(de(No,t)){var a=No[t];delete No[t],a()}},runner=function(t){return function(){run(t)}},listener=function(t){run(t.data)},post=function(t){h.postMessage(So(t),so.protocol+"//"+so.host)};Eo&&go||(Eo=function setImmediate(t){validateArgumentsLength(arguments.length,1);var a=isCallable(t)?t:yo(t),u=ht(arguments,1);return No[++Mo]=function(){I(a,void 0,u)},co(Mo),Mo},go=function clearImmediate(t){delete No[t]},zr?co=function(t){vo.nextTick(runner(t))}:To&&To.now?co=function(t){To.now(runner(t))}:Io&&!mo?(uo=(lo=new Io).port2,lo.port1.onmessage=listener,co=functionBindContext(uo.postMessage,uo)):h.addEventListener&&isCallable(h.postMessage)&&!h.importScripts&&so&&"file:"!==so.protocol&&!fails(post)?(co=post,h.addEventListener("message",listener,!1)):co=Ao in documentCreateElement("script")?function(t){it.appendChild(documentCreateElement("script")).onreadystatechange=function(){it.removeChild(this),run(t)}}:function(t){setTimeout(runner(t),0)});var Oo,Ro,Co,bo,Po,xo,ko,wo,Do={set:Eo,clear:go},Lo=/ipad|iphone|ipod/i.test(K)&&void 0!==h.Pebble,Vo=/web0s(?!.*chrome)/i.test(K),Uo=Ne.f,Fo=Do.set,Go=h.MutationObserver||h.WebKitMutationObserver,Bo=h.document,Yo=h.process,jo=h.Promise,Ho=Uo(h,"queueMicrotask"),Ko=Ho&&Ho.value;Ko||(Oo=function(){var t,a;for(zr&&(t=Yo.domain)&&t.exit();Ro;){a=Ro.fn,Ro=Ro.next;try{a()}catch(t){throw Ro?bo():Co=void 0,t}}Co=void 0,t&&t.enter()},mo||zr||Vo||!Go||!Bo?!Lo&&jo&&jo.resolve?((ko=jo.resolve(void 0)).constructor=jo,wo=functionBindContext(ko.then,ko),bo=function(){wo(Oo)}):zr?bo=function(){Yo.nextTick(Oo)}:(Fo=functionBindContext(Fo,h),bo=function(){Fo(Oo)}):(Po=!0,xo=Bo.createTextNode(""),new Go(Oo).observe(xo,{characterData:!0}),bo=function(){xo.data=Po=!Po}));var Wo=Ko||function(t){var a={fn:t,next:void 0};Co&&(Co.next=a),Ro||(Ro=a,bo()),Co=a},perform=function(t){try{return{error:!1,value:t()}}catch(t){return{error:!0,value:t}}},Queue=function(){this.head=null,this.tail=null};Queue.prototype={add:function(t){var a={item:t,next:null};this.head?this.tail.next=a:this.head=a,this.tail=a},get:function(){var t=this.head;if(t)return this.head=t.next,this.tail===t&&(this.tail=null),t.item}};var qo,$o,zo=Queue,Jo=h.Promise,Qo="object"==typeof Deno&&Deno&&"object"==typeof Deno.version,Xo=!Qo&&!zr&&"object"==typeof window&&"object"==typeof document,Zo=Jo&&Jo.prototype,en=wellKnownSymbol("species"),tn=!1,rn=isCallable(h.PromiseRejectionEvent),nn=Pe("Promise",(function(){var t=Zr(Jo),a=t!==String(Jo);if(!a&&66===Q)return!0;if(!Zo.catch||!Zo.finally)return!0;if(!Q||Q<51||!/native code/.test(t)){var u=new Jo((function(t){t(1)})),FakePromise=function(t){t((function(){}),(function(){}))};if((u.constructor={})[en]=FakePromise,!(tn=u.then((function(){}))instanceof FakePromise))return!0}return!a&&(Xo||Qo)&&!rn})),an={CONSTRUCTOR:nn,REJECTION_EVENT:rn,SUBCLASSING:tn},sn=TypeError,PromiseCapability=function(t){var a,u;this.promise=new t((function(t,_){if(void 0!==a||void 0!==u)throw sn("Bad Promise constructor");a=t,u=_})),this.resolve=aCallable(a),this.reject=aCallable(u)},cn={f:function(t){return new PromiseCapability(t)}},ln=Do.set,un="Promise",dn=an.CONSTRUCTOR,pn=an.REJECTION_EVENT,fn=Or.getterFor(un),_n=Or.set,hn=Jo&&Jo.prototype,mn=Jo,En=hn,gn=h.TypeError,vn=h.document,Tn=h.process,yn=cn.f,In=yn,Sn=!!(vn&&vn.createEvent&&h.dispatchEvent),Mn="unhandledrejection",isThenable=function(t){var a;return!(!Y(t)||!isCallable(a=t.then))&&a},callReaction=function(t,a){var u,_,h,m=a.value,E=1==a.state,g=E?t.ok:t.fail,T=t.resolve,I=t.reject,S=t.domain;try{g?(E||(2===a.rejection&&onHandleUnhandled(a),a.rejection=1),!0===g?u=m:(S&&S.enter(),u=g(m),S&&(S.exit(),h=!0)),u===t.promise?I(gn("Promise-chain cycle")):(_=isThenable(u))?b(_,u,T,I):T(u)):I(m)}catch(t){S&&!h&&S.exit(),I(t)}},notify=function(t,a){t.notified||(t.notified=!0,Wo((function(){for(var u,_=t.reactions;u=_.get();)callReaction(u,t);t.notified=!1,a&&!t.rejection&&onUnhandled(t)})))},dispatchEvent=function(t,a,u){var _,m;Sn?((_=vn.createEvent("Event")).promise=a,_.reason=u,_.initEvent(t,!1,!0),h.dispatchEvent(_)):_={promise:a,reason:u},!pn&&(m=h["on"+t])?m(_):t===Mn&&function(t,a){var u=h.console;u&&u.error&&(1==arguments.length?u.error(t):u.error(t,a))}("Unhandled promise rejection",u)},onUnhandled=function(t){b(ln,h,(function(){var a,u=t.facade,_=t.value;if(isUnhandled(t)&&(a=perform((function(){zr?Tn.emit("unhandledRejection",_,u):dispatchEvent(Mn,u,_)})),t.rejection=zr||isUnhandled(t)?2:1,a.error))throw a.value}))},isUnhandled=function(t){return 1!==t.rejection&&!t.parent},onHandleUnhandled=function(t){b(ln,h,(function(){var a=t.facade;zr?Tn.emit("rejectionHandled",a):dispatchEvent("rejectionhandled",a,t.value)}))},bind=function(t,a,u){return function(_){t(a,_,u)}},internalReject=function(t,a,u){t.done||(t.done=!0,u&&(t=u),t.value=a,t.state=2,notify(t,!0))},internalResolve=function(t,a,u){if(!t.done){t.done=!0,u&&(t=u);try{if(t.facade===a)throw gn("Promise can't be resolved itself");var _=isThenable(a);_?Wo((function(){var u={done:!1};try{b(_,a,bind(internalResolve,u,t),bind(internalReject,u,t))}catch(a){internalReject(u,a,t)}})):(t.value=a,t.state=1,notify(t,!1))}catch(a){internalReject({done:!1},a,t)}}};dn&&(En=(mn=function Promise(t){anInstance(this,En),aCallable(t),b(qo,this);var a=fn(this);try{t(bind(internalResolve,a),bind(internalReject,a))}catch(t){internalReject(a,t)}}).prototype,(qo=function Promise(t){_n(this,{type:un,done:!1,notified:!1,parent:!1,reactions:new zo,rejection:!1,state:0,value:void 0})}).prototype=defineBuiltIn(En,"then",(function then(t,a){var u=fn(this),_=yn(speciesConstructor(this,mn));return u.parent=!0,_.ok=!isCallable(t)||t,_.fail=isCallable(a)&&a,_.domain=zr?Tn.domain:void 0,0==u.state?u.reactions.add(_):Wo((function(){callReaction(_,u)})),_.promise})),$o=function(){var t=new qo,a=fn(t);this.promise=t,this.resolve=bind(internalResolve,a),this.reject=bind(internalReject,a)},cn.f=yn=function(t){return t===mn||undefined===t?new $o(t):In(t)}),_export({global:!0,constructor:!0,wrap:!0,forced:dn},{Promise:mn}),setToStringTag(mn,un,!1,!0),setSpecies(un);var Nn=wellKnownSymbol("iterator"),An=!1;try{var On=0,Rn={next:function(){return{done:!!On++}},return:function(){An=!0}};Rn[Nn]=function(){return this},Array.from(Rn,(function(){throw 2}))}catch(t){}var checkCorrectnessOfIteration=function(t,a){if(!a&&!An)return!1;var u=!1;try{var _={};_[Nn]=function(){return{next:function(){return{done:u=!0}}}},t(_)}catch(t){}return u},Cn=an.CONSTRUCTOR||!checkCorrectnessOfIteration((function(t){Jo.all(t).then(void 0,(function(){}))}));_export({target:"Promise",stat:!0,forced:Cn},{all:function all(t){var a=this,u=cn.f(a),_=u.resolve,h=u.reject,m=perform((function(){var u=aCallable(a.resolve),m=[],E=0,g=1;iterate(t,(function(t){var T=E++,I=!1;g++,b(u,a,t).then((function(t){I||(I=!0,m[T]=t,--g||_(m))}),h)})),--g||_(m)}));return m.error&&h(m.value),u.promise}});var bn=an.CONSTRUCTOR;Jo&&Jo.prototype,_export({target:"Promise",proto:!0,forced:bn,real:!0},{catch:function(t){return this.then(void 0,t)}}),_export({target:"Promise",stat:!0,forced:Cn},{race:function race(t){var a=this,u=cn.f(a),_=u.reject,h=perform((function(){var h=aCallable(a.resolve);iterate(t,(function(t){b(h,a,t).then(u.resolve,_)}))}));return h.error&&_(h.value),u.promise}}),_export({target:"Promise",stat:!0,forced:an.CONSTRUCTOR},{reject:function reject(t){var a=cn.f(this);return b(a.reject,void 0,t),a.promise}});var promiseResolve=function(t,a){if(anObject(t),Y(a)&&a.constructor===t)return a;var u=cn.f(t);return(0,u.resolve)(a),u.promise},Pn=an.CONSTRUCTOR,xn=getBuiltIn("Promise"),kn=!Pn;_export({target:"Promise",stat:!0,forced:!0},{resolve:function resolve(t){return promiseResolve(kn&&this===xn?Jo:this,t)}}),_export({target:"Promise",stat:!0},{allSettled:function allSettled(t){var a=this,u=cn.f(a),_=u.resolve,h=u.reject,m=perform((function(){var u=aCallable(a.resolve),h=[],m=0,E=1;iterate(t,(function(t){var g=m++,T=!1;E++,b(u,a,t).then((function(t){T||(T=!0,h[g]={status:"fulfilled",value:t},--E||_(h))}),(function(t){T||(T=!0,h[g]={status:"rejected",reason:t},--E||_(h))}))})),--E||_(h)}));return m.error&&h(m.value),u.promise}});var wn="No one promise resolved";_export({target:"Promise",stat:!0},{any:function any(t){var a=this,u=getBuiltIn("AggregateError"),_=cn.f(a),h=_.resolve,m=_.reject,E=perform((function(){var _=aCallable(a.resolve),E=[],g=0,T=1,I=!1;iterate(t,(function(t){var S=g++,M=!1;T++,b(_,a,t).then((function(t){M||I||(I=!0,h(t))}),(function(t){M||I||(M=!0,E[S]=t,--T||m(new u(E,wn)))}))})),--T||m(new u(E,wn))}));return E.error&&m(E.value),_.promise}});var Dn=Jo&&Jo.prototype,Ln=!!Jo&&fails((function(){Dn.finally.call({then:function(){}},(function(){}))}));_export({target:"Promise",proto:!0,real:!0,forced:Ln},{finally:function(t){var a=speciesConstructor(this,getBuiltIn("Promise")),u=isCallable(t);return this.then(u?function(u){return promiseResolve(a,t()).then((function(){return u}))}:t,u?function(u){return promiseResolve(a,t()).then((function(){throw u}))}:t)}});var Vn=O("".charAt),Un=O("".charCodeAt),Fn=O("".slice),createMethod$3=function(t){return function(a,u){var _,h,m=toString(requireObjectCoercible(a)),E=toIntegerOrInfinity(u),g=m.length;return E<0||E>=g?t?"":void 0:(_=Un(m,E))<55296||_>56319||E+1===g||(h=Un(m,E+1))<56320||h>57343?t?Vn(m,E):_:t?Fn(m,E,E+2):h-56320+(_-55296<<10)+65536}},Gn={codeAt:createMethod$3(!1),charAt:createMethod$3(!0)}.charAt,Bn="String Iterator",Yn=Or.set,jn=Or.getterFor(Bn);iteratorDefine(String,"String",(function(t){Yn(this,{type:Bn,string:toString(t),index:0})}),(function next(){var t,a=jn(this),u=a.string,_=a.index;return _>=u.length?{value:void 0,done:!0}:(t=Gn(u,_),a.index+=t.length,{value:t,done:!1})}));var Hn=j.Promise,Kn=wellKnownSymbol("toStringTag");for(var Wn in{CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}){var qn=h[Wn],$n=qn&&qn.prototype;$n&&Zt($n)!==Kn&&je($n,Kn,Wn),Kt[Wn]=Kt.Array}var zn=Hn;_export({target:"Promise",stat:!0,forced:!0},{try:function(t){var a=cn.f(this),u=perform(t);return(u.error?a.reject:a.resolve)(u.value),a.promise}});var Jn=zn,Qn=Date,Xn=O(Qn.prototype.getTime);_export({target:"Date",stat:!0},{now:function now(){return Xn(new Qn)}});var Zn=j.Date.now,ei=Array.isArray||function isArray(t){return"Array"==classofRaw(t)},ti=TypeError,doesNotExceedSafeInteger=function(t){if(t>****************)throw ti("Maximum allowed index exceeded");return t},createProperty=function(t,a,u){var _=toPropertyKey(a);_ in t?Ye.f(t,_,createPropertyDescriptor(0,u)):t[_]=u},ri=wellKnownSymbol("species"),oi=Array,arraySpeciesCreate=function(t,a){return new(function(t){var a;return ei(t)&&(a=t.constructor,(po(a)&&(a===oi||ei(a.prototype))||Y(a)&&null===(a=a[ri]))&&(a=void 0)),void 0===a?oi:a}(t))(0===a?0:a)},ni=wellKnownSymbol("species"),arrayMethodHasSpeciesSupport=function(t){return Q>=51||!fails((function(){var a=[];return(a.constructor={})[ni]=function(){return{foo:1}},1!==a[t](Boolean).foo}))},ii=wellKnownSymbol("isConcatSpreadable"),ai=Q>=51||!fails((function(){var t=[];return t[ii]=!1,t.concat()[0]!==t})),si=arrayMethodHasSpeciesSupport("concat"),isConcatSpreadable=function(t){if(!Y(t))return!1;var a=t[ii];return void 0!==a?!!a:ei(t)};_export({target:"Array",proto:!0,arity:1,forced:!ai||!si},{concat:function concat(t){var a,u,_,h,m,E=toObject(this),g=arraySpeciesCreate(E,0),T=0;for(a=-1,_=arguments.length;a<_;a++)if(isConcatSpreadable(m=-1===a?E:arguments[a]))for(h=lengthOfArrayLike(m),doesNotExceedSafeInteger(T+h),u=0;u<h;u++,T++)u in m&&createProperty(g,T,m[u]);else doesNotExceedSafeInteger(T+1),createProperty(g,T++,m);return g.length=T,g}});var ci=entryVirtual("Array").concat,li=Array.prototype,concat=function(t){var a=t.concat;return t===li||H(li,t)&&a===li.concat?ci:a},ui=/MSIE .\./.test(K),di=h.Function,wrap$1=function(t){return ui?function(a,u){var _=validateArgumentsLength(arguments.length,1)>2,h=isCallable(a)?a:di(a),m=_?ht(arguments,2):void 0;return t(_?function(){I(h,this,m)}:h,u)}:t},pi={setTimeout:wrap$1(h.setTimeout),setInterval:wrap$1(h.setInterval)},fi=pi.setInterval;_export({global:!0,bind:!0,forced:h.setInterval!==fi},{setInterval:fi});var _i=pi.setTimeout;_export({global:!0,bind:!0,forced:h.setTimeout!==_i},{setTimeout:_i});var hi=j.setTimeout,mi=createCommonjsModule((function(t){var a=Object.prototype.hasOwnProperty,u="~";function Events(){}function EE(t,a,u){this.fn=t,this.context=a,this.once=u||!1}function addListener(t,a,_,h,m){if("function"!=typeof _)throw new TypeError("The listener must be a function");var E=new EE(_,h||t,m),g=u?u+a:a;return t._events[g]?t._events[g].fn?t._events[g]=[t._events[g],E]:t._events[g].push(E):(t._events[g]=E,t._eventsCount++),t}function clearEvent(t,a){0==--t._eventsCount?t._events=new Events:delete t._events[a]}function EventEmitter(){this._events=new Events,this._eventsCount=0}Object.create&&(Events.prototype=Object.create(null),(new Events).__proto__||(u=!1)),EventEmitter.prototype.eventNames=function eventNames(){var t,_,h=[];if(0===this._eventsCount)return h;for(_ in t=this._events)a.call(t,_)&&h.push(u?_.slice(1):_);return Object.getOwnPropertySymbols?h.concat(Object.getOwnPropertySymbols(t)):h},EventEmitter.prototype.listeners=function listeners(t){var a=u?u+t:t,_=this._events[a];if(!_)return[];if(_.fn)return[_.fn];for(var h=0,m=_.length,E=new Array(m);h<m;h++)E[h]=_[h].fn;return E},EventEmitter.prototype.listenerCount=function listenerCount(t){var a=u?u+t:t,_=this._events[a];return _?_.fn?1:_.length:0},EventEmitter.prototype.emit=function emit(t,a,_,h,m,E){var g=u?u+t:t;if(!this._events[g])return!1;var T,I,S=this._events[g],M=arguments.length;if(S.fn){switch(S.once&&this.removeListener(t,S.fn,void 0,!0),M){case 1:return S.fn.call(S.context),!0;case 2:return S.fn.call(S.context,a),!0;case 3:return S.fn.call(S.context,a,_),!0;case 4:return S.fn.call(S.context,a,_,h),!0;case 5:return S.fn.call(S.context,a,_,h,m),!0;case 6:return S.fn.call(S.context,a,_,h,m,E),!0}for(I=1,T=new Array(M-1);I<M;I++)T[I-1]=arguments[I];S.fn.apply(S.context,T)}else{var N,A=S.length;for(I=0;I<A;I++)switch(S[I].once&&this.removeListener(t,S[I].fn,void 0,!0),M){case 1:S[I].fn.call(S[I].context);break;case 2:S[I].fn.call(S[I].context,a);break;case 3:S[I].fn.call(S[I].context,a,_);break;case 4:S[I].fn.call(S[I].context,a,_,h);break;default:if(!T)for(N=1,T=new Array(M-1);N<M;N++)T[N-1]=arguments[N];S[I].fn.apply(S[I].context,T)}}return!0},EventEmitter.prototype.on=function on(t,a,u){return addListener(this,t,a,u,!1)},EventEmitter.prototype.once=function once(t,a,u){return addListener(this,t,a,u,!0)},EventEmitter.prototype.removeListener=function removeListener(t,a,_,h){var m=u?u+t:t;if(!this._events[m])return this;if(!a)return clearEvent(this,m),this;var E=this._events[m];if(E.fn)E.fn!==a||h&&!E.once||_&&E.context!==_||clearEvent(this,m);else{for(var g=0,T=[],I=E.length;g<I;g++)(E[g].fn!==a||h&&!E[g].once||_&&E[g].context!==_)&&T.push(E[g]);T.length?this._events[m]=1===T.length?T[0]:T:clearEvent(this,m)}return this},EventEmitter.prototype.removeAllListeners=function removeAllListeners(t){var a;return t?(a=u?u+t:t,this._events[a]&&clearEvent(this,a)):(this._events=new Events,this._eventsCount=0),this},EventEmitter.prototype.off=EventEmitter.prototype.removeListener,EventEmitter.prototype.addListener=EventEmitter.prototype.on,EventEmitter.prefixed=u,EventEmitter.EventEmitter=EventEmitter,t.exports=EventEmitter}));_export({global:!0},{globalThis:h});var Ei=Array,gi=Math.max,arraySliceSimple=function(t,a,u){for(var _=lengthOfArrayLike(t),h=toAbsoluteIndex(a,_),m=toAbsoluteIndex(void 0===u?_:u,_),E=Ei(gi(m-h,0)),g=0;h<m;h++,g++)createProperty(E,g,t[h]);return E.length=g,E},vi=Vt.f,Ti="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],yi={f:function getOwnPropertyNames(t){return Ti&&"Window"==classofRaw(t)?function(t){try{return vi(t)}catch(t){return arraySliceSimple(Ti)}}(t):vi(toIndexedObject(t))}},Ii={f:wellKnownSymbol},Si=Ye.f,wellKnownSymbolDefine=function(t){var a=j.Symbol||(j.Symbol={});de(a,t)||Si(a,t,{value:Ii.f(t)})},symbolDefineToPrimitive=function(){var t=getBuiltIn("Symbol"),a=t&&t.prototype,u=a&&a.valueOf,_=wellKnownSymbol("toPrimitive");a&&!a[_]&&defineBuiltIn(a,_,(function(t){return b(u,this)}),{arity:1})},Mi=O([].push),createMethod$2=function(t){var a=1==t,u=2==t,_=3==t,h=4==t,m=6==t,E=7==t,g=5==t||m;return function(T,I,S,M){for(var N,A,O=toObject(T),R=U(O),C=functionBindContext(I,S),b=lengthOfArrayLike(R),P=0,x=M||arraySpeciesCreate,k=a?x(T,b):u||E?x(T,0):void 0;b>P;P++)if((g||P in R)&&(A=C(N=R[P],P,O),t))if(a)k[P]=A;else if(A)switch(t){case 3:return!0;case 5:return N;case 6:return P;case 2:Mi(k,N)}else switch(t){case 4:return!1;case 7:Mi(k,N)}return m?-1:_||h?h:k}},Ni={forEach:createMethod$2(0),map:createMethod$2(1),filter:createMethod$2(2),some:createMethod$2(3),every:createMethod$2(4),find:createMethod$2(5),findIndex:createMethod$2(6),filterReject:createMethod$2(7)},Ai=Ni.forEach,Oi=sharedKey("hidden"),Ri="Symbol",Ci=Or.set,bi=Or.getterFor(Ri),Pi=Object.prototype,xi=h.Symbol,ki=xi&&xi.prototype,wi=h.TypeError,Di=h.QObject,Li=Ne.f,Vi=Ye.f,Ui=yi.f,Fi=k.f,Gi=O([].push),Bi=ce("symbols"),Yi=ce("op-symbols"),ji=ce("wks"),Hi=!Di||!Di.prototype||!Di.prototype.findChild,Ki=R&&fails((function(){return 7!=ct(Vi({},"a",{get:function(){return Vi(this,"a",{value:7}).a}})).a}))?function(t,a,u){var _=Li(Pi,a);_&&delete Pi[a],Vi(t,a,u),_&&t!==Pi&&Vi(Pi,a,_)}:Vi,wrap=function(t,a){var u=Bi[t]=ct(ki);return Ci(u,{type:Ri,tag:t,description:a}),R||(u.description=a),u},Wi=function defineProperty(t,a,u){t===Pi&&Wi(Yi,a,u),anObject(t);var _=toPropertyKey(a);return anObject(u),de(Bi,_)?(u.enumerable?(de(t,Oi)&&t[Oi][_]&&(t[Oi][_]=!1),u=ct(u,{enumerable:createPropertyDescriptor(0,!1)})):(de(t,Oi)||Vi(t,Oi,createPropertyDescriptor(1,{})),t[Oi][_]=!0),Ki(t,_,u)):Vi(t,_,u)},qi=function defineProperties(t,a){anObject(t);var u=toIndexedObject(a),_=rt(u).concat($getOwnPropertySymbols(u));return Ai(_,(function(a){R&&!b($i,u,a)||Wi(t,a,u[a])})),t},$i=function propertyIsEnumerable(t){var a=toPropertyKey(t),u=b(Fi,this,a);return!(this===Pi&&de(Bi,a)&&!de(Yi,a))&&(!(u||!de(this,a)||!de(Bi,a)||de(this,Oi)&&this[Oi][a])||u)},zi=function getOwnPropertyDescriptor(t,a){var u=toIndexedObject(t),_=toPropertyKey(a);if(u!==Pi||!de(Bi,_)||de(Yi,_)){var h=Li(u,_);return!h||!de(Bi,_)||de(u,Oi)&&u[Oi][_]||(h.enumerable=!0),h}},Ji=function getOwnPropertyNames(t){var a=Ui(toIndexedObject(t)),u=[];return Ai(a,(function(t){de(Bi,t)||de(Xe,t)||Gi(u,t)})),u},$getOwnPropertySymbols=function(t){var a=t===Pi,u=Ui(a?Yi:toIndexedObject(t)),_=[];return Ai(u,(function(t){!de(Bi,t)||a&&!de(Pi,t)||Gi(_,Bi[t])})),_};X||(xi=function Symbol(){if(H(ki,this))throw wi("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?toString(arguments[0]):void 0,a=uid(t),setter=function(t){this===Pi&&b(setter,Yi,t),de(this,Oi)&&de(this[Oi],a)&&(this[Oi][a]=!1),Ki(this,a,createPropertyDescriptor(1,t))};return R&&Hi&&Ki(Pi,a,{configurable:!0,set:setter}),wrap(a,t)},ki=xi.prototype,defineBuiltIn(ki,"toString",(function toString(){return bi(this).tag})),defineBuiltIn(xi,"withoutSetter",(function(t){return wrap(uid(t),t)})),k.f=$i,Ye.f=Wi,nt.f=qi,Ne.f=zi,Vt.f=yi.f=Ji,Nt.f=$getOwnPropertySymbols,Ii.f=function(t){return wrap(wellKnownSymbol(t),t)},R&&Vi(ki,"description",{configurable:!0,get:function description(){return bi(this).description}})),_export({global:!0,constructor:!0,wrap:!0,forced:!X,sham:!X},{Symbol:xi}),Ai(rt(ji),(function(t){wellKnownSymbolDefine(t)})),_export({target:Ri,stat:!0,forced:!X},{useSetter:function(){Hi=!0},useSimple:function(){Hi=!1}}),_export({target:"Object",stat:!0,forced:!X,sham:!R},{create:function create(t,a){return void 0===a?ct(t):qi(ct(t),a)},defineProperty:Wi,defineProperties:qi,getOwnPropertyDescriptor:zi}),_export({target:"Object",stat:!0,forced:!X},{getOwnPropertyNames:Ji}),symbolDefineToPrimitive(),setToStringTag(xi,Ri),Xe[Oi]=!0;var Qi=X&&!!Symbol.for&&!!Symbol.keyFor,Xi=ce("string-to-symbol-registry"),Zi=ce("symbol-to-string-registry");_export({target:"Symbol",stat:!0,forced:!Qi},{for:function(t){var a=toString(t);if(de(Xi,a))return Xi[a];var u=getBuiltIn("Symbol")(a);return Xi[a]=u,Zi[u]=a,u}});var ea=ce("symbol-to-string-registry");_export({target:"Symbol",stat:!0,forced:!Qi},{keyFor:function keyFor(t){if(!te(t))throw TypeError(tryToString(t)+" is not a symbol");if(de(ea,t))return ea[t]}});var ta=getBuiltIn("JSON","stringify"),ra=O(/./.exec),oa=O("".charAt),na=O("".charCodeAt),ia=O("".replace),aa=O(1..toString),sa=/[\uD800-\uDFFF]/g,ca=/^[\uD800-\uDBFF]$/,la=/^[\uDC00-\uDFFF]$/,ua=!X||fails((function(){var t=getBuiltIn("Symbol")();return"[null]"!=ta([t])||"{}"!=ta({a:t})||"{}"!=ta(Object(t))})),da=fails((function(){return'"\\udf06\\ud834"'!==ta("\udf06\ud834")||'"\\udead"'!==ta("\udead")})),stringifyWithSymbolsFix=function(t,a){var u=ht(arguments),_=a;if((Y(a)||void 0!==t)&&!te(t))return ei(a)||(a=function(t,a){if(isCallable(_)&&(a=b(_,this,t,a)),!te(a))return a}),u[1]=a,I(ta,null,u)},fixIllFormed=function(t,a,u){var _=oa(u,a-1),h=oa(u,a+1);return ra(ca,t)&&!ra(la,h)||ra(la,t)&&!ra(ca,_)?"\\u"+aa(na(t,0),16):t};ta&&_export({target:"JSON",stat:!0,arity:3,forced:ua||da},{stringify:function stringify(t,a,u){var _=ht(arguments),h=I(ua?stringifyWithSymbolsFix:ta,null,_);return da&&"string"==typeof h?ia(h,sa,fixIllFormed):h}});var pa=!X||fails((function(){Nt.f(1)}));_export({target:"Object",stat:!0,forced:pa},{getOwnPropertySymbols:function getOwnPropertySymbols(t){var a=Nt.f;return a?a(toObject(t)):[]}}),wellKnownSymbolDefine("asyncIterator"),wellKnownSymbolDefine("hasInstance"),wellKnownSymbolDefine("isConcatSpreadable"),wellKnownSymbolDefine("iterator"),wellKnownSymbolDefine("match"),wellKnownSymbolDefine("matchAll"),wellKnownSymbolDefine("replace"),wellKnownSymbolDefine("search"),wellKnownSymbolDefine("species"),wellKnownSymbolDefine("split"),wellKnownSymbolDefine("toPrimitive"),symbolDefineToPrimitive(),wellKnownSymbolDefine("toStringTag"),setToStringTag(getBuiltIn("Symbol"),"Symbol"),wellKnownSymbolDefine("unscopables"),setToStringTag(h.JSON,"JSON",!0);var fa=j.Symbol;wellKnownSymbolDefine("asyncDispose"),wellKnownSymbolDefine("dispose"),wellKnownSymbolDefine("matcher"),wellKnownSymbolDefine("metadataKey"),wellKnownSymbolDefine("observable"),wellKnownSymbolDefine("metadata"),wellKnownSymbolDefine("patternMatch"),wellKnownSymbolDefine("replaceAll");var _a=fa,ha=Ye.f;_export({target:"Object",stat:!0,forced:Object.defineProperty!==ha,sham:!R},{defineProperty:ha});var ma=createCommonjsModule((function(t){var a=j.Object,u=t.exports=function defineProperty(t,u,_){return a.defineProperty(t,u,_)};a.defineProperty.sham&&(u.sham=!0)})),Ea=ma,ga=fails((function(){Dt(1)}));_export({target:"Object",stat:!0,forced:ga,sham:!Pt},{getPrototypeOf:function getPrototypeOf(t){return Dt(toObject(t))}});var va=j.Object.getPrototypeOf,arrayMethodIsStrict=function(t,a){var u=[][t];return!!u&&fails((function(){u.call(null,a||function(){return 1},1)}))},Ta=Ni.forEach,ya=arrayMethodIsStrict("forEach")?[].forEach:function forEach(t){return Ta(this,t,arguments.length>1?arguments[1]:void 0)};_export({target:"Array",proto:!0,forced:[].forEach!=ya},{forEach:ya});var Ia=entryVirtual("Array").forEach,Sa=Array.prototype,Ma={DOMTokenList:!0,NodeList:!0},forEach$1=function(t){var a=t.forEach;return t===Sa||H(Sa,t)&&a===Sa.forEach||de(Ma,Zt(t))?Ia:a},Na=O([].reverse),Aa=[1,2];_export({target:"Array",proto:!0,forced:String(Aa)===String(Aa.reverse())},{reverse:function reverse(){return ei(this)&&(this.length=this.length),Na(this)}});var Oa=entryVirtual("Array").reverse,Ra=Array.prototype,reverse=function(t){var a=t.reverse;return t===Ra||H(Ra,t)&&a===Ra.reverse?Oa:a},Ca=arrayMethodHasSpeciesSupport("slice"),ba=wellKnownSymbol("species"),Pa=Array,xa=Math.max;_export({target:"Array",proto:!0,forced:!Ca},{slice:function slice(t,a){var u,_,h,m=toIndexedObject(this),E=lengthOfArrayLike(m),g=toAbsoluteIndex(t,E),T=toAbsoluteIndex(void 0===a?E:a,E);if(ei(m)&&(u=m.constructor,(po(u)&&(u===Pa||ei(u.prototype))||Y(u)&&null===(u=u[ba]))&&(u=void 0),u===Pa||void 0===u))return ht(m,g,T);for(_=new(void 0===u?Pa:u)(xa(T-g,0)),h=0;g<T;g++,h++)g in m&&createProperty(_,h,m[g]);return _.length=h,_}});var ka=entryVirtual("Array").slice,wa=Array.prototype,slice=function(t){var a=t.slice;return t===wa||H(wa,t)&&a===wa.slice?ka:a},Da=Ii.f("iterator"),La=Qe.includes,Va=fails((function(){return!Array(1).includes()}));_export({target:"Array",proto:!0,forced:Va},{includes:function includes(t){return La(this,t,arguments.length>1?arguments[1]:void 0)}});var Ua=entryVirtual("Array").includes,Fa=wellKnownSymbol("match"),Ga=TypeError,notARegexp=function(t){if(function(t){var a;return Y(t)&&(void 0!==(a=t[Fa])?!!a:"RegExp"==classofRaw(t))}(t))throw Ga("The method doesn't accept regular expressions");return t},Ba=wellKnownSymbol("match"),Ya=O("".indexOf);_export({target:"String",proto:!0,forced:!function(t){var a=/./;try{"/./"[t](a)}catch(u){try{return a[Ba]=!1,"/./"[t](a)}catch(t){}}return!1}("includes")},{includes:function includes(t){return!!~Ya(toString(requireObjectCoercible(this)),toString(notARegexp(t)),arguments.length>1?arguments[1]:void 0)}});var ja=entryVirtual("String").includes,Ha=Array.prototype,Ka=String.prototype,includes=function(t){var a=t.includes;return t===Ha||H(Ha,t)&&a===Ha.includes?Ua:"string"==typeof t||t===Ka||H(Ka,t)&&a===Ka.includes?ja:a},Wa=fails((function(){rt(1)}));_export({target:"Object",stat:!0,forced:Wa},{keys:function keys(t){return rt(toObject(t))}});var qa=j.Object.keys,$a=Ni.map,za=arrayMethodHasSpeciesSupport("map");_export({target:"Array",proto:!0,forced:!za},{map:function map(t){return $a(this,t,arguments.length>1?arguments[1]:void 0)}});var Ja=entryVirtual("Array").map,Qa=Array.prototype,map$6=function(t){var a=t.map;return t===Qa||H(Qa,t)&&a===Qa.map?Ja:a},Xa=Ni.filter,Za=arrayMethodHasSpeciesSupport("filter");_export({target:"Array",proto:!0,forced:!Za},{filter:function filter(t){return Xa(this,t,arguments.length>1?arguments[1]:void 0)}});var es=entryVirtual("Array").filter,ts=Array.prototype,filter=function(t){var a=t.filter;return t===ts||H(ts,t)&&a===ts.filter?es:a},rs=createCommonjsModule((function(t,a){t.exports=function(){function _regeneratorRuntime(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
_regeneratorRuntime=function _regeneratorRuntime(){return t};var t={},a=Object.prototype,u=a.hasOwnProperty,_="function"==typeof _a?_a:{},h=_.iterator||"@@iterator",m=_.asyncIterator||"@@asyncIterator",E=_.toStringTag||"@@toStringTag";function define(t,a,u){return Ea(t,a,{value:u,enumerable:!0,configurable:!0,writable:!0}),t[a]}try{define({},"")}catch(t){define=function define(t,a,u){return t[a]=u}}function wrap(t,a,u,_){var h=a&&a.prototype instanceof Generator?a:Generator,m=ut(h.prototype),E=new Context(_||[]);return m._invoke=function(t,a,u){var _="suspendedStart";return function(h,m){if("executing"===_)throw new Error("Generator is already running");if("completed"===_){if("throw"===h)throw m;return doneResult()}for(u.method=h,u.arg=m;;){var E=u.delegate;if(E){var T=maybeInvokeDelegate(E,u);if(T){if(T===g)continue;return T}}if("next"===u.method)u.sent=u._sent=u.arg;else if("throw"===u.method){if("suspendedStart"===_)throw _="completed",u.arg;u.dispatchException(u.arg)}else"return"===u.method&&u.abrupt("return",u.arg);_="executing";var I=tryCatch(t,a,u);if("normal"===I.type){if(_=u.done?"completed":"suspendedYield",I.arg===g)continue;return{value:I.arg,done:u.done}}"throw"===I.type&&(_="completed",u.method="throw",u.arg=I.arg)}}}(t,u,E),m}function tryCatch(t,a,u){try{return{type:"normal",arg:t.call(a,u)}}catch(t){return{type:"throw",arg:t}}}t.wrap=wrap;var g={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var T={};define(T,h,(function(){return this}));var I=va&&va(va(values([])));I&&I!==a&&u.call(I,h)&&(T=I);var S=GeneratorFunctionPrototype.prototype=Generator.prototype=ut(T);function defineIteratorMethods(t){var a;forEach$1(a=["next","throw","return"]).call(a,(function(a){define(t,a,(function(t){return this._invoke(a,t)}))}))}function AsyncIterator(t,a){function invoke(_,h,m,E){var g=tryCatch(t[_],t,h);if("throw"!==g.type){var T=g.arg,I=T.value;return I&&"object"==typeof I&&u.call(I,"__await")?a.resolve(I.__await).then((function(t){invoke("next",t,m,E)}),(function(t){invoke("throw",t,m,E)})):a.resolve(I).then((function(t){T.value=t,m(T)}),(function(t){return invoke("throw",t,m,E)}))}E(g.arg)}var _;this._invoke=function(t,u){function callInvokeWithMethodAndArg(){return new a((function(a,_){invoke(t,u,a,_)}))}return _=_?_.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}}function maybeInvokeDelegate(t,a){var u=t.iterator[a.method];if(void 0===u){if(a.delegate=null,"throw"===a.method){if(t.iterator.return&&(a.method="return",a.arg=void 0,maybeInvokeDelegate(t,a),"throw"===a.method))return g;a.method="throw",a.arg=new TypeError("The iterator does not provide a 'throw' method")}return g}var _=tryCatch(u,t.iterator,a.arg);if("throw"===_.type)return a.method="throw",a.arg=_.arg,a.delegate=null,g;var h=_.arg;return h?h.done?(a[t.resultName]=h.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=void 0),a.delegate=null,g):h:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,g)}function pushTryEntry(t){var a={tryLoc:t[0]};1 in t&&(a.catchLoc=t[1]),2 in t&&(a.finallyLoc=t[2],a.afterLoc=t[3]),this.tryEntries.push(a)}function resetTryEntry(t){var a=t.completion||{};a.type="normal",delete a.arg,t.completion=a}function Context(t){this.tryEntries=[{tryLoc:"root"}],forEach$1(t).call(t,pushTryEntry,this),this.reset(!0)}function values(t){if(t){var a=t[h];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var _=-1,m=function next(){for(;++_<t.length;)if(u.call(t,_))return next.value=t[_],next.done=!1,next;return next.value=void 0,next.done=!0,next};return m.next=m}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,define(S,"constructor",GeneratorFunctionPrototype),define(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,E,"GeneratorFunction"),t.isGeneratorFunction=function(t){var a="function"==typeof t&&t.constructor;return!!a&&(a===GeneratorFunction||"GeneratorFunction"===(a.displayName||a.name))},t.mark=function(t){return _t?_t(t,GeneratorFunctionPrototype):(t.__proto__=GeneratorFunctionPrototype,define(t,E,"GeneratorFunction")),t.prototype=ut(S),t},t.awrap=function(t){return{__await:t}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,m,(function(){return this})),t.AsyncIterator=AsyncIterator,t.async=function(a,u,_,h,m){void 0===m&&(m=Jn);var E=new AsyncIterator(wrap(a,u,_,h),m);return t.isGeneratorFunction(u)?E:E.next().then((function(t){return t.done?t.value:E.next()}))},defineIteratorMethods(S),define(S,E,"Generator"),define(S,h,(function(){return this})),define(S,"toString",(function(){return"[object Generator]"})),t.keys=function(t){var a=[];for(var u in t)a.push(u);return reverse(a).call(a),function next(){for(;a.length;){var u=a.pop();if(u in t)return next.value=u,next.done=!1,next}return next.done=!0,next}},t.values=values,Context.prototype={constructor:Context,reset:function reset(t){var a;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,forEach$1(a=this.tryEntries).call(a,resetTryEntry),!t)for(var _ in this)"t"===_.charAt(0)&&u.call(this,_)&&!isNaN(+slice(_).call(_,1))&&(this[_]=void 0)},stop:function stop(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function dispatchException(t){if(this.done)throw t;var a=this;function handle(u,_){return m.type="throw",m.arg=t,a.next=u,_&&(a.method="next",a.arg=void 0),!!_}for(var _=this.tryEntries.length-1;_>=0;--_){var h=this.tryEntries[_],m=h.completion;if("root"===h.tryLoc)return handle("end");if(h.tryLoc<=this.prev){var E=u.call(h,"catchLoc"),g=u.call(h,"finallyLoc");if(E&&g){if(this.prev<h.catchLoc)return handle(h.catchLoc,!0);if(this.prev<h.finallyLoc)return handle(h.finallyLoc)}else if(E){if(this.prev<h.catchLoc)return handle(h.catchLoc,!0)}else{if(!g)throw new Error("try statement without catch or finally");if(this.prev<h.finallyLoc)return handle(h.finallyLoc)}}}},abrupt:function abrupt(t,a){for(var _=this.tryEntries.length-1;_>=0;--_){var h=this.tryEntries[_];if(h.tryLoc<=this.prev&&u.call(h,"finallyLoc")&&this.prev<h.finallyLoc){var m=h;break}}m&&("break"===t||"continue"===t)&&m.tryLoc<=a&&a<=m.finallyLoc&&(m=null);var E=m?m.completion:{};return E.type=t,E.arg=a,m?(this.method="next",this.next=m.finallyLoc,g):this.complete(E)},complete:function complete(t,a){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&a&&(this.next=a),g},finish:function finish(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.finallyLoc===t)return this.complete(u.completion,u.afterLoc),resetTryEntry(u),g}},catch:function _catch(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.tryLoc===t){var _=u.completion;if("throw"===_.type){var h=_.arg;resetTryEntry(u)}return h}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(t,a,u){return this.delegate={iterator:values(t),resultName:a,nextLoc:u},"next"===this.method&&(this.arg=void 0),g}},t}function _typeof(t){return _typeof="function"==typeof _a&&"symbol"==typeof Da?function(t){return typeof t}:function(t){return t&&"function"==typeof _a&&t.constructor===_a&&t!==_a.prototype?"symbol":typeof t},_typeof(t)}function _classCallCheck(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,a){for(var u=0;u<a.length;u++){var _=a[u];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Ea(t,_.key,_)}}function _createClass(t,a,u){return a&&_defineProperties(t.prototype,a),u&&_defineProperties(t,u),Ea(t,"prototype",{writable:!1}),t}function __awaiter(t,a,u,_){function adopt(t){return t instanceof u?t:new u((function(a){a(t)}))}return new(u||(u=Jn))((function(u,h){function fulfilled(t){try{step(_.next(t))}catch(t){h(t)}}function rejected(t){try{step(_.throw(t))}catch(t){h(t)}}function step(t){t.done?u(t.value):adopt(t.value).then(fulfilled,rejected)}step((_=_.apply(t,a||[])).next())}))}var t={isDataReportEnable:!0,maxSize:100,msgListMaxSize:1e3,cacheMaxSize:1e3,maxDelay:3e5,maxInterval:3e4,minInterval:1e4,timeout:5e3,autoStart:!0,loginFailIgnoreInterval:72e5},a=12,u=8e3,_=function emptyFn(){},h=function(){function Reporter(a){_classCallCheck(this,Reporter),this.isUploadEnable=!0,this.serverAllowUpload=!1,this.initConfigLoaded=!1,this.loading=!1,this.isDestroyed=!1,this.reportConfig=t,this.configPath="dispatcher/req",this.dataReportPath="statics/report/common/form",this.traceMsgCache={},this.reqRetryCount=0,this.highPriorityMsgList=[],this.msgList=[],this.lowPriorityMsgList=[],this.cacheMsgList=[],this.lastReportTime=Zn(),this.timer=null,this.endedAsyncMsgByModule={},this.lastFailLogin={},this.setConfig(a),this.reportConfig.isDataReportEnable&&this.reportConfig.autoStart&&this.initUploadConfig()}return _createClass(Reporter,[{key:"setConfig",value:function setConfig(t){var a=bt({},this.reportConfig.common,t.common);this.reportConfig=bt({},this.reportConfig,t),this.reportConfig.common=a,this.reportConfig.common.sdk_type||(this.reportConfig.common.sdk_type="im")}},{key:"reportImmediately",value:function reportImmediately(t,a){var u=this;this.reportConfig.isDataReportEnable&&this.reportConfig.request(t,bt({dataType:"json",method:"POST",timeout:this.reportConfig.timeout},a)).catch((function(t){var a,_;null===(_=null===(a=u.reportConfig)||void 0===a?void 0:a.logger)||void 0===_||_.warn("Reporter immediately upload failed",t)}))}},{key:"report",value:function report(a,u){var _=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};if(_.priority||(_.priority=this.getEventPriority(a,u)),this.reportConfig.isDataReportEnable&&a){if("login"===a&&!1===u.succeed&&u.process_id){var h=this.lastFailLogin[u.process_id]||0;if(u.start_time-h<t.loginFailIgnoreInterval)return;this.lastFailLogin[u.process_id]=u.start_time}var m=Zn();"HIGH"===_.priority?this.highPriorityMsgList.push({module:a,msg:u,createTime:m}):"NORMAL"===_.priority?this.msgList.push({module:a,msg:u,createTime:m}):"LOW"===_.priority&&this.lowPriorityMsgList.push({module:a,msg:u,createTime:m}),this.highPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.highPriorityMsgList.shift(),this.msgList.length>this.reportConfig.msgListMaxSize&&this.msgList.shift(),this.lowPriorityMsgList.length>this.reportConfig.msgListMaxSize&&this.lowPriorityMsgList.shift(),this.doReport()}}},{key:"reportTraceStart",value:function reportTraceStart(t,a){if(this.reportConfig.isDataReportEnable&&t&&!this.traceMsgCache[t]){var u=bt(bt({start_time:Zn()},a),{extension:[]});this.traceMsgCache[t]=u}}},{key:"reportTraceUpdate",value:function reportTraceUpdate(t){}},{key:"reportTraceUpdateV2",value:function reportTraceUpdateV2(t,a,u){var _,h=this;if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[t]){var m=this.traceMsgCache[t].extension,E=m.length,g=(new Date).getTime();0===E?a.duration=g-this.traceMsgCache[t].start_time:m[E-1].end_time?a.duration=g-m[E-1].end_time:a.duration=g-this.traceMsgCache[t].start_time,m.push(bt({end_time:g},a));var T=m.length-1;(null==u?void 0:u.asyncParams)&&((_=this.traceMsgCache[t]).asyncPromiseArray||(_.asyncPromiseArray=[]),this.traceMsgCache[t].asyncPromiseArray.push(u.asyncParams.then((function(a){h.traceMsgCache[t]&&h.traceMsgCache[t].extension[T]&&bt(h.traceMsgCache[t].extension[T],a)}))))}}},{key:"reportTraceEnd",value:function reportTraceEnd(t){var a,u,_=this,h=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(this.reportConfig.isDataReportEnable&&this.traceMsgCache[t])if("nos"!==t||!1===h){"boolean"==typeof h?this.traceMsgCache[t].succeed=!!h:this.traceMsgCache[t].state=h,this.traceMsgCache[t].duration=Zn()-this.traceMsgCache[t].start_time,forEach$1(a=this.traceMsgCache[t].extension).call(a,(function(t){delete t.end_time}));var m=this.traceMsgCache[t];if(this.traceMsgCache[t]=null,m.asyncPromiseArray){(u=this.endedAsyncMsgByModule)[t]||(u[t]=[]),this.endedAsyncMsgByModule[t].push(m);var E=function asyncCallback(){var a;_.endedAsyncMsgByModule[t]&&includes(a=_.endedAsyncMsgByModule[t]).call(a,m)&&(delete m.asyncPromiseArray,_.report(t,m,{priority:_.getEventPriority(t,m)}))};Jn.all(m.asyncPromiseArray).then(E).catch(E)}else this.report(t,m,{priority:this.getEventPriority(t,m)})}else this.traceMsgCache[t]=null}},{key:"getEventPriority",value:function getEventPriority(t,a){if("exceptions"===t){if(0===a.action)return"HIGH";if(2===a.action)return"HIGH";if(1===a.action&&0!==a.exception_service)return"HIGH"}else{if("msgReceive"===t)return"LOW";if("nim_api_trace"===t)return"LOW"}return"NORMAL"}},{key:"reportTraceCancel",value:function reportTraceCancel(t){this.reportConfig.isDataReportEnable&&(this.endedAsyncMsgByModule[t]=[],this.traceMsgCache[t]=null)}},{key:"pause",value:function pause(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!1)}},{key:"restore",value:function restore(){this.reportConfig.isDataReportEnable&&(this.isUploadEnable=!0,this.initConfigLoaded||this.initUploadConfig())}},{key:"destroy",value:function destroy(){var t,a=this;this.reportConfig.isDataReportEnable&&(forEach$1(t=qa(this.traceMsgCache)).call(t,(function(t){a.reportTraceEnd(t,1)})),null!==this.timer&&clearTimeout(this.timer),this.setConfig=_,this.report=_,this.reportTraceStart=_,this.reportTraceUpdate=_,this.reportTraceEnd=_,this.pause=_,this.restore=_,this.destroy=_,this.reqRetryCount=0,this.cacheMsgList=[],this.traceMsgCache={},this.lowPriorityMsgList=[],this.msgList=[],this.highPriorityMsgList=[],this.reportConfig={},this.isDestroyed=!0)}},{key:"initUploadConfig",value:function initUploadConfig(){var t,_;return __awaiter(this,void 0,void 0,_regeneratorRuntime().mark((function _callee(){var h,m,E,g,T,I=this;return _regeneratorRuntime().wrap((function _callee$(S){for(var M;;)switch(S.prev=S.next){case 0:if(!this.loading){S.next=2;break}return S.abrupt("return");case 2:this.loading=!0,h=this.reportConfig.common||{},m=map$6(M=this.reportConfig.compassDataEndpoint.split(",")).call(M,(function(t){var a;return concat(a="".concat(t,"/")).call(a,I.configPath)})),E=_regeneratorRuntime().mark((function _loop(E){return _regeneratorRuntime().wrap((function _loop$(g){for(;;)switch(g.prev=g.next){case 0:if(!I.initConfigLoaded&&!I.isDestroyed){g.next=2;break}return g.abrupt("return","break");case 2:return g.prev=2,g.next=5,I.reportConfig.request(m[E],{method:"GET",dataType:"json",params:{deviceId:h.dev_id,sdkVer:h.sdk_ver,platform:h.platform,appkey:h.app_key},timeout:I.reportConfig.timeout}).then((function(t){var a,u;if(!I.isDestroyed){if(200===t.status&&t.data&&200===t.data.code){I.initConfigLoaded=!0;var _=t.data.data||{};I.reportConfig.maxSize=_.maxSize>1e3?1e3:_.maxSize,I.reportConfig.maxInterval=_.maxInterval>1e4?1e4:_.maxInterval,I.reportConfig.maxInterval=_.maxInterval<10?10:_.maxInterval,I.reportConfig.minInterval=_.minInterval<2?2:_.minInterval,I.reportConfig.maxDelay=_.maxDelay||300,I.reportConfig.maxInterval=1e3*I.reportConfig.maxInterval,I.reportConfig.minInterval=1e3*I.reportConfig.minInterval,I.reportConfig.maxDelay=1e3*I.reportConfig.maxDelay,_.endpoint?I.dataReportEndpoint=_.endpoint:I.dataReportEndpoint=m[E],I.serverAllowUpload=!0,I.loading=!1,I.reportHeartBeat()}else 200===t.status&&(I.initConfigLoaded=!0);null===(u=null===(a=I.reportConfig)||void 0===a?void 0:a.logger)||void 0===u||u.log("Get reporter upload config success")}})).catch((function(t){var _,h;I.isDestroyed||(I.loading=!1,null===(h=null===(_=I.reportConfig)||void 0===_?void 0:_.logger)||void 0===h||h.error("Get reporter upload config failed",t),I.reqRetryCount<a&&(I.reqRetryCount++,hi((function(){I.isDestroyed||I.initUploadConfig()}),u)))}));case 5:g.next=14;break;case 7:if(g.prev=7,g.t0=g.catch(2),!I.isDestroyed){g.next=11;break}return g.abrupt("return",{v:void 0});case 11:I.loading=!1,null===(_=null===(t=I.reportConfig)||void 0===t?void 0:t.logger)||void 0===_||_.error("Exec reporter request failed",g.t0),I.reqRetryCount<a&&(I.reqRetryCount++,hi((function(){I.isDestroyed||I.initUploadConfig()}),u));case 14:case"end":return g.stop()}}),_loop,null,[[2,7]])})),g=0;case 7:if(!(g<m.length)){S.next=17;break}return S.delegateYield(E(g),"t0",9);case 9:if("break"!==(T=S.t0)){S.next=12;break}return S.abrupt("break",17);case 12:if("object"!==_typeof(T)){S.next=14;break}return S.abrupt("return",T.v);case 14:g++,S.next=7;break;case 17:case"end":return S.stop()}}),_callee,this)})))}},{key:"reportHeartBeat",value:function reportHeartBeat(){var t=this;this.isDestroyed||(this.timer=hi((function(){t.reportHeartBeat()}),this.reportConfig.minInterval),this.doReport())}},{key:"doReport",value:function doReport(){if(!this.isDestroyed){var t=this.highPriorityMsgList.length+this.msgList.length+this.lowPriorityMsgList.length+this.cacheMsgList.length>2*this.reportConfig.maxSize?this.reportConfig.minInterval:this.reportConfig.maxInterval;Zn()-this.lastReportTime>=t&&this.upload()}}},{key:"getUploadMsg",value:function getUploadMsg(){var t,a,u,_,h,m,E=this,g={},T=Zn();this.highPriorityMsgList=filter(t=this.highPriorityMsgList).call(t,(function(t){return T-t.createTime<E.reportConfig.maxDelay})),this.msgList=filter(a=this.msgList).call(a,(function(t){return T-t.createTime<E.reportConfig.maxDelay})),this.lowPriorityMsgList=filter(u=this.lowPriorityMsgList).call(u,(function(t){return T-t.createTime<E.reportConfig.maxDelay})),this.cacheMsgList=filter(_=this.cacheMsgList).call(_,(function(t){return T-t.createTime<E.reportConfig.maxDelay}));var I=slice(h=this.highPriorityMsgList).call(h,0,this.reportConfig.maxSize);if(this.highPriorityMsgList=slice(m=this.highPriorityMsgList).call(m,I.length),I.length<this.reportConfig.maxSize){var S,M,N=this.reportConfig.maxSize-I.length;I=concat(I).call(I,slice(S=this.msgList).call(S,0,N)),this.msgList=slice(M=this.msgList).call(M,N)}if(I.length<this.reportConfig.maxSize){var A,O,R=this.reportConfig.maxSize-I.length;I=concat(I).call(I,slice(A=this.lowPriorityMsgList).call(A,0,R)),this.lowPriorityMsgList=slice(O=this.lowPriorityMsgList).call(O,R)}if(I.length<this.reportConfig.maxSize){var C,b,P=this.reportConfig.maxSize-I.length;I=concat(I).call(I,slice(C=this.cacheMsgList).call(C,0,P)),this.cacheMsgList=slice(b=this.cacheMsgList).call(b,P)}return forEach$1(I).call(I,(function(t){g[t.module]?g[t.module].push(t.msg):g[t.module]=[t.msg]})),{uploadMsgArr:I,uploadMsg:g}}},{key:"upload",value:function upload(){var t,a,u=this;if(this.isUploadEnable&&this.serverAllowUpload&&!(this.lastReportTime&&Zn()-this.lastReportTime<this.reportConfig.minInterval)){var _=this.getUploadMsg(),h=_.uploadMsgArr,m=_.uploadMsg;if(h.length){this.lastReportTime=Zn();try{var E,g=concat(E="".concat(this.dataReportEndpoint,"/")).call(E,this.dataReportPath);this.reportConfig.request(g,{dataType:"json",method:"POST",data:{common:this.reportConfig.common,event:m},headers:{sdktype:"im"},timeout:this.reportConfig.timeout}).catch((function(t){var a,_,m,E;u.cacheMsgList=slice(a=concat(_=u.cacheMsgList).call(_,h)).call(a,0,u.reportConfig.cacheMaxSize),null===(E=null===(m=u.reportConfig)||void 0===m?void 0:m.logger)||void 0===E||E.warn("Reporter upload failed",t)}))}catch(u){null===(a=null===(t=this.reportConfig)||void 0===t?void 0:t.logger)||void 0===a||a.warn("Exec reporter request failed",u)}clearTimeout(this.timer),this.reportHeartBeat()}}}}]),Reporter}();return h}()})),os=Qe.indexOf,ns=O([].indexOf),is=!!ns&&1/ns([1],1,-0)<0,as=arrayMethodIsStrict("indexOf");_export({target:"Array",proto:!0,forced:is||!as},{indexOf:function indexOf(t){var a=arguments.length>1?arguments[1]:void 0;return is?ns(this,t,a)||0:os(this,t,a)}});var ss=entryVirtual("Array").indexOf,cs=Array.prototype,indexOf=function(t){var a=t.indexOf;return t===cs||H(cs,t)&&a===cs.indexOf?ss:a},ls=Math.min,us=[].lastIndexOf,ds=!!us&&1/[1].lastIndexOf(1,-0)<0,ps=arrayMethodIsStrict("lastIndexOf"),fs=ds||!ps?function lastIndexOf(t){if(ds)return I(us,this,arguments)||0;var a=toIndexedObject(this),u=lengthOfArrayLike(a),_=u-1;for(arguments.length>1&&(_=ls(_,toIntegerOrInfinity(arguments[1]))),_<0&&(_=u+_);_>=0;_--)if(_ in a&&a[_]===t)return _||0;return-1}:us;_export({target:"Array",proto:!0,forced:fs!==[].lastIndexOf},{lastIndexOf:fs});var _s,hs=entryVirtual("Array").lastIndexOf,ms=Array.prototype,lastIndexOf=function(t){var a=t.lastIndexOf;return t===ms||H(ms,t)&&a===ms.lastIndexOf?hs:a},Es="\t\n\v\f\r                　\u2028\u2029\ufeff",gs=O("".replace),vs="["+Es+"]",Ts=RegExp("^"+vs+vs+"*"),ys=RegExp(vs+vs+"*$"),createMethod$1=function(t){return function(a){var u=toString(requireObjectCoercible(a));return 1&t&&(u=gs(u,Ts,"")),2&t&&(u=gs(u,ys,"")),u}},Is={start:createMethod$1(1),end:createMethod$1(2),trim:createMethod$1(3)},Ss=Pr.PROPER,Ms=Is.trim;_export({target:"String",proto:!0,forced:(_s="trim",fails((function(){return!!Es[_s]()||"​᠎"!=="​᠎"[_s]()||Ss&&Es[_s].name!==_s})))},{trim:function trim(){return Ms(this)}}),entryVirtual("String").trim;var Ns=Ni.findIndex,As="findIndex",Os=!0;As in[]&&Array(1).findIndex((function(){Os=!1})),_export({target:"Array",proto:!0,forced:Os},{findIndex:function findIndex(t){return Ns(this,t,arguments.length>1?arguments[1]:void 0)}});var Rs=entryVirtual("Array").findIndex,Cs=Array.prototype,findIndex=function(t){var a=t.findIndex;return t===Cs||H(Cs,t)&&a===Cs.findIndex?Rs:a},bs=fails((function(){if("function"==typeof ArrayBuffer){var t=new ArrayBuffer(8);Object.isExtensible(t)&&Object.defineProperty(t,"a",{value:8})}})),Ps=Object.isExtensible,xs=fails((function(){Ps(1)}))||bs?function isExtensible(t){return!!Y(t)&&((!bs||"ArrayBuffer"!=classofRaw(t))&&(!Ps||Ps(t)))}:Ps,ks=!fails((function(){return Object.isExtensible(Object.preventExtensions({}))})),ws=createCommonjsModule((function(t){var a=Ye.f,u=!1,_=uid("meta"),h=0,setMetadata=function(t){a(t,_,{value:{objectID:"O"+h++,weakData:{}}})},m=t.exports={enable:function(){m.enable=function(){},u=!0;var t=Vt.f,a=O([].splice),h={};h[_]=1,t(h).length&&(Vt.f=function(u){for(var h=t(u),m=0,E=h.length;m<E;m++)if(h[m]===_){a(h,m,1);break}return h},_export({target:"Object",stat:!0,forced:!0},{getOwnPropertyNames:yi.f}))},fastKey:function(t,a){if(!Y(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!de(t,_)){if(!xs(t))return"F";if(!a)return"E";setMetadata(t)}return t[_].objectID},getWeakData:function(t,a){if(!de(t,_)){if(!xs(t))return!0;if(!a)return!1;setMetadata(t)}return t[_].weakData},onFreeze:function(t){return ks&&u&&xs(t)&&!de(t,_)&&setMetadata(t),t}};Xe[_]=!0})),Ds=Ye.f,Ls=Ni.forEach,Vs=Or.set,Us=Or.getterFor,defineBuiltIns=function(t,a,u){for(var _ in a)u&&u.unsafe&&t[_]?t[_]=a[_]:defineBuiltIn(t,_,a[_],u);return t},Fs=Ye.f,Gs=ws.fastKey,Bs=Or.set,Ys=Or.getterFor,js={getConstructor:function(t,a,u,_){var h=t((function(t,h){anInstance(t,m),Bs(t,{type:a,index:ct(null),first:void 0,last:void 0,size:0}),R||(t.size=0),isNullOrUndefined(h)||iterate(h,t[_],{that:t,AS_ENTRIES:u})})),m=h.prototype,E=Ys(a),define=function(t,a,u){var _,h,m=E(t),g=getEntry(t,a);return g?g.value=u:(m.last=g={index:h=Gs(a,!0),key:a,value:u,previous:_=m.last,next:void 0,removed:!1},m.first||(m.first=g),_&&(_.next=g),R?m.size++:t.size++,"F"!==h&&(m.index[h]=g)),t},getEntry=function(t,a){var u,_=E(t),h=Gs(a);if("F"!==h)return _.index[h];for(u=_.first;u;u=u.next)if(u.key==a)return u};return defineBuiltIns(m,{clear:function clear(){for(var t=E(this),a=t.index,u=t.first;u;)u.removed=!0,u.previous&&(u.previous=u.previous.next=void 0),delete a[u.index],u=u.next;t.first=t.last=void 0,R?t.size=0:this.size=0},delete:function(t){var a=this,u=E(a),_=getEntry(a,t);if(_){var h=_.next,m=_.previous;delete u.index[_.index],_.removed=!0,m&&(m.next=h),h&&(h.previous=m),u.first==_&&(u.first=h),u.last==_&&(u.last=m),R?u.size--:a.size--}return!!_},forEach:function forEach(t){for(var a,u=E(this),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);a=a?a.next:u.first;)for(_(a.value,a.key,this);a&&a.removed;)a=a.previous},has:function has(t){return!!getEntry(this,t)}}),defineBuiltIns(m,u?{get:function get(t){var a=getEntry(this,t);return a&&a.value},set:function set(t,a){return define(this,0===t?0:t,a)}}:{add:function add(t){return define(this,t=0===t?0:t,t)}}),R&&Fs(m,"size",{get:function(){return E(this).size}}),h},setStrong:function(t,a,u){var _=a+" Iterator",h=Ys(a),m=Ys(_);iteratorDefine(t,a,(function(t,a){Bs(this,{type:_,target:t,state:h(t),kind:a,last:void 0})}),(function(){for(var t=m(this),a=t.kind,u=t.last;u&&u.removed;)u=u.previous;return t.target&&(t.last=u=u?u.next:t.state.first)?"keys"==a?{value:u.key,done:!1}:"values"==a?{value:u.value,done:!1}:{value:[u.key,u.value],done:!1}:(t.target=void 0,{value:void 0,done:!0})}),u?"entries":"values",!u,!0),setSpecies(a)}};!function(t,a,u){var _,m=-1!==t.indexOf("Map"),E=-1!==t.indexOf("Weak"),g=m?"set":"add",T=h[t],I=T&&T.prototype,S={};if(R&&isCallable(T)&&(E||I.forEach&&!fails((function(){(new T).entries().next()})))){var M=(_=a((function(a,u){Vs(anInstance(a,M),{type:t,collection:new T}),null!=u&&iterate(u,a[g],{that:a,AS_ENTRIES:m})}))).prototype,N=Us(t);Ls(["add","clear","delete","forEach","get","has","set","keys","values","entries"],(function(t){var a="add"==t||"set"==t;!(t in I)||E&&"clear"==t||je(M,t,(function(u,_){var h=N(this).collection;if(!a&&E&&!Y(u))return"get"==t&&void 0;var m=h[t](0===u?0:u,_);return a?this:m}))})),E||Ds(M,"size",{configurable:!0,get:function(){return N(this).collection.size}})}else _=u.getConstructor(a,t,m,g),ws.enable();setToStringTag(_,t,!1,!0),S[t]=_,_export({global:!0,forced:!0},S),E||u.setStrong(_,t,m)}("Map",(function(t){return function Map(){return t(this,arguments.length?arguments[0]:void 0)}}),js);var Hs=j.Map,Ks=[].push;_export({target:"Map",stat:!0,forced:!0},{from:function from(t){var a,u,_,h,m=arguments.length,E=m>1?arguments[1]:void 0;return aConstructor(this),(a=void 0!==E)&&aCallable(E),isNullOrUndefined(t)?new this:(u=[],a?(_=0,h=functionBindContext(E,m>2?arguments[2]:void 0),iterate(t,(function(t){b(Ks,u,h(t,_++))}))):iterate(t,Ks,{that:u}),new this(u))}});_export({target:"Map",stat:!0,forced:!0},{of:function of(){return new this(ht(arguments))}});_export({target:"Map",proto:!0,real:!0,forced:!0},{deleteAll:function deleteAll(){for(var t,a=anObject(this),u=aCallable(a.delete),_=!0,h=0,m=arguments.length;h<m;h++)t=b(u,a,arguments[h]),_=_&&t;return!!_}});_export({target:"Map",proto:!0,real:!0,forced:!0},{emplace:function emplace(t,a){var u,_,h=anObject(this),m=aCallable(h.get),E=aCallable(h.has),g=aCallable(h.set);return b(E,h,t)?(u=b(m,h,t),"update"in a&&(u=a.update(u,t,h),b(g,h,t,u)),u):(_=a.insert(t,h),b(g,h,t,_),_)}});var Ws=getIterator;_export({target:"Map",proto:!0,real:!0,forced:!0},{every:function every(t){var a=anObject(this),u=Ws(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return!iterate(u,(function(t,u,h){if(!_(u,t,a))return h()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{filter:function filter(t){var a=anObject(this),u=Ws(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0),h=new(speciesConstructor(a,getBuiltIn("Map"))),m=aCallable(h.set);return iterate(u,(function(t,u){_(u,t,a)&&b(m,h,t,u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),h}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{find:function find(t){var a=anObject(this),u=Ws(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,h){if(_(u,t,a))return h(u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{findKey:function findKey(t){var a=anObject(this),u=Ws(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,h){if(_(u,t,a))return h(t)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}});var qs=O([].push);_export({target:"Map",stat:!0,forced:!0},{groupBy:function groupBy(t,a){aCallable(a);var u=getIterator(t),_=new this,h=aCallable(_.has),m=aCallable(_.get),E=aCallable(_.set);return iterate(u,(function(t){var u=a(t);b(h,_,u)?qs(b(m,_,u),t):b(E,_,u,[t])}),{IS_ITERATOR:!0}),_}});_export({target:"Map",proto:!0,real:!0,forced:!0},{includes:function includes(t){return iterate(Ws(anObject(this)),(function(a,u,_){if((h=u)===(m=t)||h!=h&&m!=m)return _();var h,m}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}}),_export({target:"Map",stat:!0,forced:!0},{keyBy:function keyBy(t,a){var u=new this;aCallable(a);var _=aCallable(u.set);return iterate(t,(function(t){b(_,u,a(t),t)})),u}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{keyOf:function keyOf(t){return iterate(Ws(anObject(this)),(function(a,u,_){if(u===t)return _(a)}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).result}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{mapKeys:function mapKeys(t){var a=anObject(this),u=Ws(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0),h=new(speciesConstructor(a,getBuiltIn("Map"))),m=aCallable(h.set);return iterate(u,(function(t,u){b(m,h,_(u,t,a),u)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),h}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{mapValues:function mapValues(t){var a=anObject(this),u=Ws(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0),h=new(speciesConstructor(a,getBuiltIn("Map"))),m=aCallable(h.set);return iterate(u,(function(t,u){b(m,h,t,_(u,t,a))}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),h}}),_export({target:"Map",proto:!0,real:!0,arity:1,forced:!0},{merge:function merge(t){for(var a=anObject(this),u=aCallable(a.set),_=arguments.length,h=0;h<_;)iterate(arguments[h++],u,{that:a,AS_ENTRIES:!0});return a}});var $s=TypeError;_export({target:"Map",proto:!0,real:!0,forced:!0},{reduce:function reduce(t){var a=anObject(this),u=Ws(a),_=arguments.length<2,h=_?void 0:arguments[1];if(aCallable(t),iterate(u,(function(u,m){_?(_=!1,h=m):h=t(h,m,u,a)}),{AS_ENTRIES:!0,IS_ITERATOR:!0}),_)throw $s("Reduce of empty map with no initial value");return h}}),_export({target:"Map",proto:!0,real:!0,forced:!0},{some:function some(t){var a=anObject(this),u=Ws(a),_=functionBindContext(t,arguments.length>1?arguments[1]:void 0);return iterate(u,(function(t,u,h){if(_(u,t,a))return h()}),{AS_ENTRIES:!0,IS_ITERATOR:!0,INTERRUPTED:!0}).stopped}});var zs=TypeError;_export({target:"Map",proto:!0,real:!0,forced:!0},{update:function update(t,a){var u=anObject(this),_=aCallable(u.get),h=aCallable(u.has),m=aCallable(u.set),E=arguments.length;aCallable(a);var g=b(h,u,t);if(!g&&E<3)throw zs("Updating absent value");var T=g?b(_,u,t):aCallable(E>2?arguments[2]:void 0)(t,u);return b(m,u,t,a(T,t,u)),u}});var Js=TypeError,Qs=function upsert(t,a){var u,_=anObject(this),h=aCallable(_.get),m=aCallable(_.has),E=aCallable(_.set),g=arguments.length>2?arguments[2]:void 0;if(!isCallable(a)&&!isCallable(g))throw Js("At least one callback required");return b(m,_,t)?(u=b(h,_,t),isCallable(a)&&(u=a(u),b(E,_,t,u))):isCallable(g)&&(u=g(),b(E,_,t,u)),u};_export({target:"Map",proto:!0,real:!0,forced:!0},{upsert:Qs}),_export({target:"Map",proto:!0,real:!0,name:"upsert",forced:!0},{updateOrInsert:Qs});var Xs=Hs,Zs=createCommonjsModule((function(t){function _getPrototypeOf(a){var u;return t.exports=_getPrototypeOf=_t?bind$1(u=va).call(u):function _getPrototypeOf(t){return t.__proto__||va(t)},t.exports.__esModule=!0,t.exports.default=t.exports,_getPrototypeOf(a)}t.exports=_getPrototypeOf,t.exports.__esModule=!0,t.exports.default=t.exports})),ec=createCommonjsModule((function(t){t.exports=function _isNativeFunction(t){var a;return-1!==indexOf(a=Function.toString.call(t)).call(a,"[native code]")},t.exports.__esModule=!0,t.exports.default=t.exports})),tc=getBuiltIn("Reflect","construct"),rc=Object.prototype,oc=[].push,nc=fails((function(){function F(){}return!(tc((function(){}),[],F)instanceof F)})),ic=!fails((function(){tc((function(){}))})),ac=nc||ic;_export({target:"Reflect",stat:!0,forced:ac,sham:ac},{construct:function construct(t,a){aConstructor(t),anObject(a);var u=arguments.length<3?t:aConstructor(arguments[2]);if(ic&&!nc)return tc(t,a,u);if(t==u){switch(a.length){case 0:return new t;case 1:return new t(a[0]);case 2:return new t(a[0],a[1]);case 3:return new t(a[0],a[1],a[2]);case 4:return new t(a[0],a[1],a[2],a[3])}var _=[null];return I(oc,_,a),new(I(Tt,t,_))}var h=u.prototype,m=ct(Y(h)?h:rc),E=I(t,m,a);return Y(E)?E:m}});var sc=j.Reflect.construct,cc=createCommonjsModule((function(t){t.exports=function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!sc)return!1;if(sc.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(sc(Boolean,[],(function(){}))),!0}catch(t){return!1}},t.exports.__esModule=!0,t.exports.default=t.exports})),lc=createCommonjsModule((function(t){function _construct(a,u,_){var h;cc()?(t.exports=_construct=bind$1(h=sc).call(h),t.exports.__esModule=!0,t.exports.default=t.exports):(t.exports=_construct=function _construct(t,a,u){var _=[null];_.push.apply(_,a);var h=new(bind$1(Function).apply(t,_));return u&&St(h,u.prototype),h},t.exports.__esModule=!0,t.exports.default=t.exports);return _construct.apply(null,arguments)}t.exports=_construct,t.exports.__esModule=!0,t.exports.default=t.exports})),uc=createCommonjsModule((function(t){function _wrapNativeSuper(a){var u="function"==typeof Xs?new Xs:void 0;return t.exports=_wrapNativeSuper=function _wrapNativeSuper(t){if(null===t||!ec(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==u){if(u.has(t))return u.get(t);u.set(t,Wrapper)}function Wrapper(){return lc(t,arguments,Zs(this).constructor)}return Wrapper.prototype=ut(t.prototype,{constructor:{value:Wrapper,enumerable:!1,writable:!0,configurable:!0}}),St(Wrapper,t)},t.exports.__esModule=!0,t.exports.default=t.exports,_wrapNativeSuper(a)}t.exports=_wrapNativeSuper,t.exports.__esModule=!0,t.exports.default=t.exports})),dc=getDefaultExportFromCjs(uc);j.JSON||(j.JSON={stringify:JSON.stringify});var pc=function stringify(t,a,u){return I(j.JSON.stringify,null,arguments)},fc=pc,_c=TypeError,createMethod=function(t){return function(a,u,_,h){aCallable(u);var m=toObject(a),E=U(m),g=lengthOfArrayLike(m),T=t?g-1:0,I=t?-1:1;if(_<2)for(;;){if(T in E){h=E[T],T+=I;break}if(T+=I,t?T<0:g<=T)throw _c("Reduce of empty array with no initial value")}for(;t?T>=0:g>T;T+=I)T in E&&(h=u(h,E[T],T,m));return h}},hc={left:createMethod(!1),right:createMethod(!0)}.left,mc=arrayMethodIsStrict("reduce");_export({target:"Array",proto:!0,forced:!mc||!zr&&Q>79&&Q<83},{reduce:function reduce(t){var a=arguments.length;return hc(this,t,a,a>1?arguments[1]:void 0)}});var Ec,gc,vc,Tc,yc,Ic,Sc,Mc,Nc,Ac,Oc,Rc,Cc,bc,Pc,xc,kc,wc,Dc,Lc,Vc,Uc,Fc,Gc,Bc,Yc,jc,Hc,Kc,Wc,qc,$c,zc,Jc,Qc,Xc,Zc,el,tl,rl,ol,nl,il,al,sl,cl,ll,ul,dl,pl,fl,_l,hl,ml=entryVirtual("Array").reduce,El=Array.prototype,reduce=function(t){var a=t.reduce;return t===El||H(El,t)&&a===El.reduce?ml:a};!function(t){t[t.V2NIM_DATA_SYNC_TYPE_LEVEL_FULL=0]="V2NIM_DATA_SYNC_TYPE_LEVEL_FULL",t[t.V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC=1]="V2NIM_DATA_SYNC_TYPE_LEVEL_BASIC"}(Ec||(Ec={})),function(t){t[t.V2NIM_DATA_SYNC_TYPE_MAIN=1]="V2NIM_DATA_SYNC_TYPE_MAIN",t[t.V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER=2]="V2NIM_DATA_SYNC_TYPE_TEAM_MEMBER",t[t.V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER=3]="V2NIM_DATA_SYNC_TYPE_SUPER_TEAM_MEMBER"}(gc||(gc={})),function(t){t[t.V2NIM_DATA_SYNC_STATE_WAITING=1]="V2NIM_DATA_SYNC_STATE_WAITING",t[t.V2NIM_DATA_SYNC_STATE_SYNCING=2]="V2NIM_DATA_SYNC_STATE_SYNCING",t[t.V2NIM_DATA_SYNC_STATE_COMPLETED=3]="V2NIM_DATA_SYNC_STATE_COMPLETED"}(vc||(vc={})),function(t){t[t.V2NIM_CONVERSATION_TYPE_UNKNOWN=0]="V2NIM_CONVERSATION_TYPE_UNKNOWN",t[t.V2NIM_CONVERSATION_TYPE_P2P=1]="V2NIM_CONVERSATION_TYPE_P2P",t[t.V2NIM_CONVERSATION_TYPE_TEAM=2]="V2NIM_CONVERSATION_TYPE_TEAM",t[t.V2NIM_CONVERSATION_TYPE_SUPER_TEAM=3]="V2NIM_CONVERSATION_TYPE_SUPER_TEAM"}(Tc||(Tc={})),function(t){t[t.V2NIM_MESSAGE_STATUS_DEFAULT=0]="V2NIM_MESSAGE_STATUS_DEFAULT",t[t.V2NIM_MESSAGE_STATUS_REVOKE=1]="V2NIM_MESSAGE_STATUS_REVOKE",t[t.V2NIM_MESSAGE_STATUS_BACKFILL=2]="V2NIM_MESSAGE_STATUS_BACKFILL"}(yc||(yc={})),function(t){t[t.V2NIM_FRIEND_MODE_TYPE_ADD=1]="V2NIM_FRIEND_MODE_TYPE_ADD",t[t.V2NIM_FRIEND_MODE_TYPE_APPLY=2]="V2NIM_FRIEND_MODE_TYPE_APPLY"}(Ic||(Ic={})),function(t){t[t.V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED=1]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_RECEIVED",t[t.V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_TYPE_REJECTED"}(Sc||(Sc={})),function(t){t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT=0]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_INIT",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED=1]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_AGREED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED=2]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_REJECTED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED=3]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_EXPIRED",t[t.V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD=4]="V2NIM_FRIEND_ADD_APPLICATION_STATUS_DIRECT_ADD"}(Mc||(Mc={})),function(t){t[t.V2NIM_FRIEND_DELETION_TYPE_BY_SELF=1]="V2NIM_FRIEND_DELETION_TYPE_BY_SELF",t[t.V2NIM_FRIEND_DELETION_TYPE_BY_PEER=2]="V2NIM_FRIEND_DELETION_TYPE_BY_PEER"}(Nc||(Nc={})),function(t){t[t.V2NIM_FRIEND_VERIFY_TYPE_ADD=1]="V2NIM_FRIEND_VERIFY_TYPE_ADD",t[t.V2NIM_FRIEND_VERIFY_TYPE_APPLY=2]="V2NIM_FRIEND_VERIFY_TYPE_APPLY",t[t.V2NIM_FRIEND_VERIFY_TYPE_ACCEPT=3]="V2NIM_FRIEND_VERIFY_TYPE_ACCEPT",t[t.V2NIM_FRIEND_VERIFY_TYPE_REJECT=4]="V2NIM_FRIEND_VERIFY_TYPE_REJECT"}(Ac||(Ac={})),function(t){t[t.V2NIM_LOGIN_AUTH_TYPE_DEFAULT=0]="V2NIM_LOGIN_AUTH_TYPE_DEFAULT",t[t.V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN=1]="V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN",t[t.V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY=2]="V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY"}(Oc||(Oc={})),function(t){t[t.V2NIM_LOGIN_STATUS_LOGOUT=0]="V2NIM_LOGIN_STATUS_LOGOUT",t[t.V2NIM_LOGIN_STATUS_LOGINED=1]="V2NIM_LOGIN_STATUS_LOGINED",t[t.V2NIM_LOGIN_STATUS_LOGINING=2]="V2NIM_LOGIN_STATUS_LOGINING",t[t.V2NIM_LOGIN_STATUS_UNLOGIN=3]="V2NIM_LOGIN_STATUS_UNLOGIN"}(Rc||(Rc={})),function(t){t[t.V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN=0]="V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN",t[t.V2NIM_LOGIN_CLIENT_TYPE_ANDROID=1]="V2NIM_LOGIN_CLIENT_TYPE_ANDROID",t[t.V2NIM_LOGIN_CLIENT_TYPE_IOS=2]="V2NIM_LOGIN_CLIENT_TYPE_IOS",t[t.V2NIM_LOGIN_CLIENT_TYPE_PC=4]="V2NIM_LOGIN_CLIENT_TYPE_PC",t[t.V2NIM_LOGIN_CLIENT_TYPE_WP=8]="V2NIM_LOGIN_CLIENT_TYPE_WP",t[t.V2NIM_LOGIN_CLIENT_TYPE_WEB=16]="V2NIM_LOGIN_CLIENT_TYPE_WEB",t[t.V2NIM_LOGIN_CLIENT_TYPE_RESTFUL=32]="V2NIM_LOGIN_CLIENT_TYPE_RESTFUL",t[t.V2NIM_LOGIN_CLIENT_TYPE_MAC_OS=64]="V2NIM_LOGIN_CLIENT_TYPE_MAC_OS",t[t.V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS=65]="V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS"}(Cc||(Cc={})),function(t){t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE=1]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_EXCLUSIVE",t[t.V2NIM_KICKED_OFFLINE_REASON_SERVER=2]="V2NIM_KICKED_OFFLINE_REASON_SERVER",t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT=3]="V2NIM_KICKED_OFFLINE_REASON_CLIENT",t[t.V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY=4]="V2NIM_KICKED_OFFLINE_REASON_CLIENT_QUIETLY"}(bc||(bc={})),function(t){t[t.V2NIM_LOGIN_CLIENT_CHANGE_LIST=1]="V2NIM_LOGIN_CLIENT_CHANGE_LIST",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGIN=2]="V2NIM_LOGIN_CLIENT_CHANGE_LOGIN",t[t.V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT=3]="V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT"}(Pc||(Pc={})),function(t){t[t.V2NIM_CONNECT_STATUS_DISCONNECTED=0]="V2NIM_CONNECT_STATUS_DISCONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTED=1]="V2NIM_CONNECT_STATUS_CONNECTED",t[t.V2NIM_CONNECT_STATUS_CONNECTING=2]="V2NIM_CONNECT_STATUS_CONNECTING",t[t.V2NIM_CONNECT_STATUS_WAITING=3]="V2NIM_CONNECT_STATUS_WAITING"}(xc||(xc={})),function(t){t[t.NIM_MESSAGE_AI_STREAM_STATUS_STREAMING=-1]="NIM_MESSAGE_AI_STREAM_STATUS_STREAMING",t[t.NIM_MESSAGE_AI_STREAM_STATUS_NONE=0]="NIM_MESSAGE_AI_STREAM_STATUS_NONE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER=1]="NIM_MESSAGE_AI_STREAM_STATUS_PLACEHOLDER",t[t.NIM_MESSAGE_AI_STREAM_STATUS_CANCEL=2]="NIM_MESSAGE_AI_STREAM_STATUS_CANCEL",t[t.NIM_MESSAGE_AI_STREAM_STATUS_UPDATE=3]="NIM_MESSAGE_AI_STREAM_STATUS_UPDATE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE=4]="NIM_MESSAGE_AI_STREAM_STATUS_COMPLETE",t[t.NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION=5]="NIM_MESSAGE_AI_STREAM_STATUS_EXCEPTION"}(kc||(kc={})),function(t){t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT=0]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_DEFAULT",t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE=1]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_REVOKE",t[t.V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE=2]="V2NIM_MESSAGE_AI_STREAM_STOP_OP_UPDATE"}(wc||(wc={})),function(t){t[t.V2NIM_MESSAGE_AI_REGEN_OP_UPDATE=1]="V2NIM_MESSAGE_AI_REGEN_OP_UPDATE",t[t.V2NIM_MESSAGE_AI_REGEN_OP_NEW=2]="V2NIM_MESSAGE_AI_REGEN_OP_NEW"}(Dc||(Dc={})),function(t){t[t.V2NIM_MESSAGE_AI_STATUS_UNKNOW=0]="V2NIM_MESSAGE_AI_STATUS_UNKNOW",t[t.V2NIM_MESSAGE_AI_STATUS_AT=1]="V2NIM_MESSAGE_AI_STATUS_AT",t[t.V2NIM_MESSAGE_AI_STATUS_RESPONSE=2]="V2NIM_MESSAGE_AI_STATUS_RESPONSE"}(Lc||(Lc={})),function(t){t[t.V2NIM_MESSAGE_TYPE_INVALID=-1]="V2NIM_MESSAGE_TYPE_INVALID",t[t.V2NIM_MESSAGE_TYPE_TEXT=0]="V2NIM_MESSAGE_TYPE_TEXT",t[t.V2NIM_MESSAGE_TYPE_IMAGE=1]="V2NIM_MESSAGE_TYPE_IMAGE",t[t.V2NIM_MESSAGE_TYPE_AUDIO=2]="V2NIM_MESSAGE_TYPE_AUDIO",t[t.V2NIM_MESSAGE_TYPE_VIDEO=3]="V2NIM_MESSAGE_TYPE_VIDEO",t[t.V2NIM_MESSAGE_TYPE_LOCATION=4]="V2NIM_MESSAGE_TYPE_LOCATION",t[t.V2NIM_MESSAGE_TYPE_NOTIFICATION=5]="V2NIM_MESSAGE_TYPE_NOTIFICATION",t[t.V2NIM_MESSAGE_TYPE_FILE=6]="V2NIM_MESSAGE_TYPE_FILE",t[t.V2NIM_MESSAGE_TYPE_AVCHAT=7]="V2NIM_MESSAGE_TYPE_AVCHAT",t[t.V2NIM_MESSAGE_TYPE_TIPS=10]="V2NIM_MESSAGE_TYPE_TIPS",t[t.V2NIM_MESSAGE_TYPE_ROBOT=11]="V2NIM_MESSAGE_TYPE_ROBOT",t[t.V2NIM_MESSAGE_TYPE_CALL=12]="V2NIM_MESSAGE_TYPE_CALL",t[t.V2NIM_MESSAGE_TYPE_CUSTOM=100]="V2NIM_MESSAGE_TYPE_CUSTOM"}(Vc||(Vc={})),function(t){t[t.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR=0]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_OR",t[t.V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND=1]="V2NIM_SEARCH_KEYWORD_MATCH_TYPE_AND"}(Uc||(Uc={})),function(t){t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED=-1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_UNDEFINED",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE=0]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK=1]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_KICK",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE=2]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_LEAVE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO=3]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_UPDATE_TINFO",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS=4]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_DISMISS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS=5]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_APPLY_PASS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER=6]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_OWNER_TRANSFER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER=7]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_ADD_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER=8]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_REMOVE_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT=9]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_INVITE_ACCEPT",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER=10]="V2NIM_MESSAGE_NOTIFICATION_TYPE_TEAM_BANNED_TEAM_MEMBER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE=401]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK=402]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_KICK",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE=403]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_LEAVE",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO=404]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_UPDATE_TINFO",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS=405]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_DISMISS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS=410]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_APPLY_PASS",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER=406]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_OWNER_TRANSFER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER=407]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_ADD_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER=408]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_REMOVE_MANAGER",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT=411]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_INVITE_ACCEPT",t[t.V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER=409]="V2NIM_MESSAGE_NOTIFICATION_TYPE_SUPER_TEAM_BANNED_TEAM_MEMBER"}(Fc||(Fc={})),function(t){t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN=0]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS=1]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_SUCCESS",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED=2]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_FAILED",t[t.V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING=3]="V2NIM_MESSAGE_ATTACHMENT_UPLOAD_STATE_UPLOADING"}(Gc||(Gc={})),function(t){t[t.V2NIM_MESSAGE_SENDING_STATE_UNKNOWN=0]="V2NIM_MESSAGE_SENDING_STATE_UNKNOWN",t[t.V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED=1]="V2NIM_MESSAGE_SENDING_STATE_SUCCEEDED",t[t.V2NIM_MESSAGE_SENDING_STATE_FAILED=2]="V2NIM_MESSAGE_SENDING_STATE_FAILED",t[t.V2NIM_MESSAGE_SENDING_STATE_SENDING=3]="V2NIM_MESSAGE_SENDING_STATE_SENDING"}(Bc||(Bc={})),function(t){t[t.V2NIM_QUERY_DIRECTION_DESC=0]="V2NIM_QUERY_DIRECTION_DESC",t[t.V2NIM_QUERY_DIRECTION_ASC=1]="V2NIM_QUERY_DIRECTION_ASC"}(Yc||(Yc={})),function(t){t[t.V2NIM_CLEAR_HISTORY_MODE_ALL=0]="V2NIM_CLEAR_HISTORY_MODE_ALL",t[t.V2NIM_CLEAR_HISTORY_MODE_LOCAL=1]="V2NIM_CLEAR_HISTORY_MODE_LOCAL"}(jc||(jc={})),function(t){t[t.V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED=0]="V2NIM_MESSAGE_REVOKE_TYPE_UNDEFINED",t[t.V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY=1]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY=2]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY=3]="V2NIM_MESSAGE_REVOKE_TYPE_SUPERTEAM_BOTHWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY=4]="V2NIM_MESSAGE_REVOKE_TYPE_P2P_ONEWAY",t[t.V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY=5]="V2NIM_MESSAGE_REVOKE_TYPE_TEAM_ONEWAY"}(Hc||(Hc={})),function(t){t[t.V2NIM_MESSAGE_PIN_STATE_NOT_PINNED=0]="V2NIM_MESSAGE_PIN_STATE_NOT_PINNED",t[t.V2NIM_MESSAGE_PIN_STATE_PINNED=1]="V2NIM_MESSAGE_PIN_STATE_PINNED",t[t.V2NIM_MESSAGE_PIN_STATE_UPDATED=2]="V2NIM_MESSAGE_PIN_STATE_UPDATED"}(Kc||(Kc={})),function(t){t[t.V2NIM_QUICK_COMMENT_STATE_ADD=1]="V2NIM_QUICK_COMMENT_STATE_ADD",t[t.V2NIM_QUICK_COMMENT_STATE_REMOVE=2]="V2NIM_QUICK_COMMENT_STATE_REMOVE"}(Wc||(Wc={})),function(t){t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_NONE=0]="V2NIM_CLIENT_ANTISPAM_OPERATE_NONE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE=1]="V2NIM_CLIENT_ANTISPAM_OPERATE_REPLACE",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD=2]="V2NIM_CLIENT_ANTISPAM_OPERATE_CLIENT_SHIELD",t[t.V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD=3]="V2NIM_CLIENT_ANTISPAM_OPERATE_SERVER_SHIELD"}(qc||(qc={})),function(t){t[t.V2NIM_SORT_ORDER_DESC=0]="V2NIM_SORT_ORDER_DESC",t[t.V2NIM_SORT_ORDER_ASC=1]="V2NIM_SORT_ORDER_ASC"}($c||($c={})),function(t){t[t.P2P_DELETE_MSG=7]="P2P_DELETE_MSG",t[t.TEAM_DELETE_MSG=8]="TEAM_DELETE_MSG",t[t.SUPERTEAM_DELETE_MSG=12]="SUPERTEAM_DELETE_MSG",t[t.P2P_ONE_WAY_DELETE_MSG=13]="P2P_ONE_WAY_DELETE_MSG",t[t.TEAM_ONE_WAY_DELETE_MSG=14]="TEAM_ONE_WAY_DELETE_MSG",t[t.CUSTOM_P2P_MSG=100]="CUSTOM_P2P_MSG",t[t.CUSTOM_TEAM_MSG=101]="CUSTOM_TEAM_MSG",t[t.CUSTOM_SUPERTEAM_MSG=103]="CUSTOM_SUPERTEAM_MSG"}(zc||(zc={})),function(t){t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_TEAM_MESSAGE_MUTE_MODE_OFF",t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_ON=1]="V2NIM_TEAM_MESSAGE_MUTE_MODE_ON",t[t.V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON=2]="V2NIM_TEAM_MESSAGE_MUTE_MODE_NORMAL_ON"}(Jc||(Jc={})),function(t){t[t.V2NIM_P2P_MESSAGE_MUTE_MODE_OFF=0]="V2NIM_P2P_MESSAGE_MUTE_MODE_OFF",t[t.V2NIM_P2P_MESSAGE_MUTE_MODE_ON=1]="V2NIM_P2P_MESSAGE_MUTE_MODE_ON"}(Qc||(Qc={})),function(t){t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL=0]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_ALL",t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL=1]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_NORMAL",t[t.V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_QUERY_TYPE_MANAGER"}(Xc||(Xc={})),function(t){t[t.V2NIM_TEAM_TYPE_INVALID=0]="V2NIM_TEAM_TYPE_INVALID",t[t.V2NIM_TEAM_TYPE_ADVANCED=1]="V2NIM_TEAM_TYPE_ADVANCED",t[t.V2NIM_TEAM_TYPE_SUPER=2]="V2NIM_TEAM_TYPE_SUPER"}(Zc||(Zc={})),function(t){t[t.V2NIM_TEAM_JOIN_MODE_FREE=0]="V2NIM_TEAM_JOIN_MODE_FREE",t[t.V2NIM_TEAM_JOIN_MODE_APPLY=1]="V2NIM_TEAM_JOIN_MODE_APPLY",t[t.V2NIM_TEAM_JOIN_MODE_INVITE=2]="V2NIM_TEAM_JOIN_MODE_INVITE"}(el||(el={})),function(t){t[t.V2NIM_TEAM_AGREE_MODE_AUTH=0]="V2NIM_TEAM_AGREE_MODE_AUTH",t[t.V2NIM_TEAM_AGREE_MODE_NO_AUTH=1]="V2NIM_TEAM_AGREE_MODE_NO_AUTH"}(tl||(tl={})),function(t){t[t.V2NIM_TEAM_INVITE_MODE_MANAGER=0]="V2NIM_TEAM_INVITE_MODE_MANAGER",t[t.V2NIM_TEAM_INVITE_MODE_ALL=1]="V2NIM_TEAM_INVITE_MODE_ALL"}(rl||(rl={})),function(t){t[t.V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_INFO_MODE_MANAGER",t[t.V2NIM_TEAM_UPDATE_INFO_MODE_ALL=1]="V2NIM_TEAM_UPDATE_INFO_MODE_ALL"}(ol||(ol={})),function(t){t[t.V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN=0]="V2NIM_TEAM_CHAT_BANNED_MODE_UNBAN",t[t.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL=1]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_NORMAL",t[t.V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL=3]="V2NIM_TEAM_CHAT_BANNED_MODE_BANNED_ALL"}(nl||(nl={})),function(t){t[t.V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER=0]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_MANAGER",t[t.V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL=1]="V2NIM_TEAM_UPDATE_EXTENSION_MODE_ALL"}(il||(il={})),function(t){t[t.V2NIM_TEAM_MEMBER_ROLE_NORMAL=0]="V2NIM_TEAM_MEMBER_ROLE_NORMAL",t[t.V2NIM_TEAM_MEMBER_ROLE_OWNER=1]="V2NIM_TEAM_MEMBER_ROLE_OWNER",t[t.V2NIM_TEAM_MEMBER_ROLE_MANAGER=2]="V2NIM_TEAM_MEMBER_ROLE_MANAGER"}(al||(al={})),function(t){t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION=0]="V2NIM_TEAM_JOIN_ACTION_TYPE_APPLICATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION=1]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_APPLICATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION=2]="V2NIM_TEAM_JOIN_ACTION_TYPE_INVITATION",t[t.V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION=3]="V2NIM_TEAM_JOIN_ACTION_TYPE_REJECT_INVITATION"}(sl||(sl={})),function(t){t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_INIT=0]="V2NIM_TEAM_JOIN_ACTION_STATUS_INIT",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED=1]="V2NIM_TEAM_JOIN_ACTION_STATUS_AGREED",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED=2]="V2NIM_TEAM_JOIN_ACTION_STATUS_REJECTED",t[t.V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED=3]="V2NIM_TEAM_JOIN_ACTION_STATUS_EXPIRED"}(cl||(cl={})),function(t){t[t.teamApply=0]="teamApply",t[t.teamApplyReject=1]="teamApplyReject",t[t.teamInvite=2]="teamInvite",t[t.teamInviteReject=3]="teamInviteReject",t[t.tlistUpdate=4]="tlistUpdate",t[t.superTeamApply=15]="superTeamApply",t[t.superTeamApplyReject=16]="superTeamApplyReject",t[t.superTeamInvite=17]="superTeamInvite",t[t.superTeamInviteReject=18]="superTeamInviteReject"}(ll||(ll={})),function(t){t[t.V2NIM_AI_MODEL_TYPE_UNKNOW=0]="V2NIM_AI_MODEL_TYPE_UNKNOW",t[t.V2NIM_AI_MODEL_TYPE_QWEN=1]="V2NIM_AI_MODEL_TYPE_QWEN",t[t.V2NIM_AI_MODEL_TYPE_AZURE=2]="V2NIM_AI_MODEL_TYPE_AZURE",t[t.V2NIM_AI_MODEL_TYPE_PRIVATE=3]="V2NIM_AI_MODEL_TYPE_PRIVATE"}(ul||(ul={})),function(t){t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE=0]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_NONE",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL=2]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_CANCEL",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE=4]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_COMPLETE",t[t.V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION=5]="V2NIM_AI_MODEL_STREAM_CALL_STATUS_EXCEPTION"}(dl||(dl={})),function(t){t.V2NIM_AI_MODEL_ROLE_TYPE_SYSTEM="system",t.V2NIM_AI_MODEL_ROLE_TYPE_USER="user",t.V2NIM_AI_MODEL_ROLE_TYPE_ASSISTANT="assistant"}(pl||(pl={})),function(t){t[t.V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN=0]="V2NIM_SIGNALLING_EVENT_TYPE_UNKNOWN",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CLOSE=1]="V2NIM_SIGNALLING_EVENT_TYPE_CLOSE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_JOIN=2]="V2NIM_SIGNALLING_EVENT_TYPE_JOIN",t[t.V2NIM_SIGNALLING_EVENT_TYPE_INVITE=3]="V2NIM_SIGNALLING_EVENT_TYPE_INVITE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE=4]="V2NIM_SIGNALLING_EVENT_TYPE_CANCEL_INVITE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_REJECT=5]="V2NIM_SIGNALLING_EVENT_TYPE_REJECT",t[t.V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT=6]="V2NIM_SIGNALLING_EVENT_TYPE_ACCEPT",t[t.V2NIM_SIGNALLING_EVENT_TYPE_LEAVE=7]="V2NIM_SIGNALLING_EVENT_TYPE_LEAVE",t[t.V2NIM_SIGNALLING_EVENT_TYPE_CONTROL=8]="V2NIM_SIGNALLING_EVENT_TYPE_CONTROL"}(fl||(fl={})),function(t){t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO=1]="V2NIM_SIGNALLING_CHANNEL_TYPE_AUDIO",t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO=2]="V2NIM_SIGNALLING_CHANNEL_TYPE_VIDEO",t[t.V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM=3]="V2NIM_SIGNALLING_CHANNEL_TYPE_CUSTOM"}(_l||(_l={})),function(t){t[t.V2NIM_USER_STATUS_TYPE_UNKNOWN=0]="V2NIM_USER_STATUS_TYPE_UNKNOWN",t[t.V2NIM_USER_STATUS_TYPE_LOGIN=1]="V2NIM_USER_STATUS_TYPE_LOGIN",t[t.V2NIM_USER_STATUS_TYPE_LOGOUT=2]="V2NIM_USER_STATUS_TYPE_LOGOUT",t[t.V2NIM_USER_STATUS_TYPE_DISCONNECT=3]="V2NIM_USER_STATUS_TYPE_DISCONNECT"}(hl||(hl={}));var gl={V2NIM_ERROR_CODE_UNKNOWN:{code:0,message:"unknown error"},V2NIM_ERROR_CODE_SUCCESS:{code:200,message:"success"},V2NIM_ERROR_CODE_HANDSHAKE:{code:201,message:"handshake error"},V2NIM_ERROR_CODE_REQUEST_TEMPERARY_FORBIDDEN:{code:398,message:"request temprary forbidden"},V2NIM_ERROR_CODE_SERVER_UNIT_ERROR:{code:399,message:"server unit error"},V2NIM_ERROR_CODE_FORBIDDEN:{code:403,message:"forbidden"},V2NIM_ERROR_CODE_NOT_FOUND:{code:404,message:"not found"},V2NIM_ERROR_CODE_PARAMETER_ERROR:{code:414,message:"parameter error"},V2NIM_ERROR_CODE_RATE_LIMIT_REACHED:{code:416,message:"rate limit reached"},V2NIM_ERROR_CODE_MULTI_LOGIN_FORBIDDEN:{code:417,message:"multi login forbidden"},V2NIM_ERROR_CODE_SERVER_INTERNAL_ERROR:{code:500,message:"server internal error"},V2NIM_ERROR_CODE_SERVER_BUSY:{code:503,message:"server busy"},V2NIM_ERROR_CODE_APP_UNREACHABLE:{code:511,message:"app server unreachable"},V2NIM_ERROR_CODE_SERVICE_UNAVAILABLE:{code:514,message:"service unavailable"},V2NIM_ERROR_CODE_PROTOCOL_BLACKHOLE_FILTERED:{code:599,message:"protocol filtered by blackhole rule"},V2NIM_ERROR_CODE_NO_PERMISSION:{code:997,message:"appid has no permission to call the protocol"},V2NIM_ERROR_CODE_UNPACK_ERROR:{code:998,message:"unpack error"},V2NIM_ERROR_CODE_PACK_ERROR:{code:999,message:"pack error"},V2NIM_ERROR_CODE_IM_DISABLED:{code:101301,message:"IM disabled"},V2NIM_ERROR_CODE_SERVICE_ADDRESS_INVALID:{code:101302,message:"service address invalid"},V2NIM_ERROR_CODE_APPKEY_NOT_EXIST:{code:101303,message:"appkey not exist"},V2NIM_ERROR_CODE_BUNDLEID_CHECK_FAILED:{code:101304,message:"bundleid check failed"},V2NIM_ERROR_CODE_APPKEY_BLOCKED:{code:101403,message:"appkey blocked"},V2NIM_ERROR_CODE_INVALID_TOKEN:{code:102302,message:"invalid token"},V2NIM_ERROR_CODE_ROBOT_NOT_ALLOWED:{code:102303,message:"robot not allowed"},V2NIM_ERROR_CODE_ACCOUNT_NOT_EXIST:{code:102404,message:"account not exist"},V2NIM_ERROR_CODE_ACCOUNT_CHAT_BANNED:{code:102421,message:"account chat banned"},V2NIM_ERROR_CODE_ACCOUNT_BANNED:{code:102422,message:"account banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_BLOCK_LIST:{code:102426,message:"account in block list"},V2NIM_ERROR_CODE_USER_PROFILE_NOT_EXIST:{code:103404,message:"user profile not exist"},V2NIM_ERROR_CODE_USER_PROFILE_HIT_ANTISPAM:{code:103451,message:"user profile hit antispam"},V2NIM_ERROR_CODE_PEER_FRIEND_LIMIT:{code:104301,message:"peer friend limit"},V2NIM_ERROR_CODE_FRIEND_APPLICATION_NOT_EXIST:{code:104302,message:"friend application not exist"},V2NIM_ERROR_CODE_FRIEND_NOT_EXIST:{code:104404,message:"friend not exist"},V2NIM_ERROR_CODE_FRIEND_ALREADY_EXIST:{code:104405,message:"friend already exist"},V2NIM_ERROR_CODE_SELF_FRIEND_OPERATION_NOT_ALLOWED:{code:104429,message:"self friend operation not allowed"},V2NIM_ERROR_CODE_FRIEND_LIMIT:{code:104435,message:"friend limit"},V2NIM_ERROR_CODE_FRIEND_OPERATION_RATE_LIMIT:{code:104449,message:"friend operation rate limit"},V2NIM_ERROR_CODE_FRIEND_HIT_ANTISPAM:{code:104451,message:"friend hit antispam"},V2NIM_ERROR_CODE_SELF_MUTE_OPERATION_NOT_ALLOWED:{code:105429,message:"self mute operation not allowed"},V2NIM_ERROR_CODE_MUTE_LIST_LIMIT:{code:105435,message:"mute list limit"},V2NIM_ERROR_CODE_SELF_BLOCK_LIST_OPERATION_NOT_ALLOWED:{code:106429,message:"self block list operation not allowed"},V2NIM_ERROR_CODE_BLOCK_LIST_LIMIT:{code:106435,message:"block list limit"},V2NIM_ERROR_CODE_REVOKE_THIRD_PARTY_MESSAGE_NOT_ALLOWED:{code:107301,message:"revoke third party message not allowed"},V2NIM_ERROR_CODE_SHORT_TO_LONG_URL_FAILED:{code:107307,message:"short to long URL failed"},V2NIM_ERROR_CODE_URL_INVALID:{code:107308,message:"URL invalid"},V2NIM_ERROR_CODE_DURATION_OUT_OF_RANGE:{code:107309,message:"duration out of range"},V2NIM_ERROR_CODE_GET_FILE_META_INFO_FAILED:{code:107310,message:"get file meta info failed"},V2NIM_ERROR_CODE_AUDIO_FILE_SIZE_LIMIT:{code:107311,message:"audio file size limit"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_TIMEOUT:{code:107312,message:"voice to text timeout"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FAILED:{code:107313,message:"voice to text failed"},V2NIM_ERROR_CODE_REVOKE_EXCEED_TIME_LIMIT:{code:107314,message:"revoke message exceed time limit"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_NOT_ALLOWED:{code:107315,message:"revoke specific message not allowed"},V2NIM_ERROR_CODE_FORCE_PUSH_LIST_LIMIT:{code:107316,message:"force push list limit"},V2NIM_ERROR_CODE_TEAM_MESSAGE_RECEIPT_RATE_LIMIT:{code:107317,message:"team message receipt rate limit"},V2NIM_ERROR_CODE_SNAPSHOT_NOT_EXIST:{code:107318,message:"snapshot not exist"},V2NIM_ERROR_CODE_PIN_LIMIT:{code:107319,message:"pin limit"},V2NIM_ERROR_CODE_PIN_NOT_EXIST:{code:107320,message:"pin not exist"},V2NIM_ERROR_CODE_QUICK_COMMENT_LIMIT:{code:107321,message:"quick comment limit"},V2NIM_ERROR_CODE_PIN_ALREADY_EXIST:{code:107322,message:"pin already exist"},V2NIM_ERROR_CODE_VOICE_TO_TEXT_FUNCTION_DISABLED:{code:107333,message:"voice to text function disabled"},V2NIM_ERROR_CODE_CLOUD_SEARCH_FUNCTION_DISABLED:{code:107334,message:"cloud search function disabled"},V2NIM_ERROR_CODE_ONE_WAY_DELETE_FUNCTION_DISABLED:{code:107335,message:"one-way delete function disabled"},V2NIM_ERRPR_CODE_ONEWAY_DELETION_NOT_ALLOW_FOR_TARGET_MESSAGES:{code:107338,message:"one-way deletion is not allowed for target messages"},V2NIM_ERRPR_CODE_SENDER_CANNOT_INCLUDED_IN_TARGET_LIST:{code:107339,message:"The message sender cannot be included in the target list"},V2NIM_ERROR_CODE_ROBOT_CANNOT_SEND_TARGET_MESSAGE:{code:107340,message:"Robot can not send target message"},V2NIM_ERROR_CODE_PIN_TARGET_MESSAGE_NOT_ALLOWED:{code:107345,message:"Pin target message is not allowed"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_REPLY:{code:107346,message:"Target message not allowed reply"},V2NIM_ERROR_CODE_TARGET_MESSAGE_NOT_ALLOWED_QUICK_COMMENT:{code:107347,message:"Target message not allowed quick comment"},V2NIM_ERROR_CODE_REVOKE_MESSAGE_TO_SELF_NOT_ALLOWED:{code:107429,message:"revoke message to self not allowed"},V2NIM_ERROR_CODE_APP_CHAT_BANNED:{code:107410,message:"app chat banned"},V2NIM_ERROR_CODE_QUICK_COMMENT_FUNCTION_DISABLED:{code:107326,message:"quick comment function disabled"},V2NIM_ERROR_CODE_PIN_FUNCTION_DISABLED:{code:107327,message:"PIN function disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_FUNCTION_DISABLED:{code:107324,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_P2P_READ_RECEIPT_FUNCTION_DISABLED:{code:107325,message:"read receipt for p2p messages function disabled"},V2NIM_ERROR_CODE_RATE_LIMIT_FOR_MESSAGING_REACHED:{code:107323,message:"rate limit for messaging reached"},V2NIM_ERROR_CODE_MESSAGE_HIT_ANTISPAM:{code:107451,message:"message hit antispam"},V2NIM_ERROR_CODE_MESSAGE_NOT_EXIST:{code:107404,message:"message not exist"},V2NIM_ERROR_CODE_UNSENDING_MESSAGE_EXPIRED:{code:107406,message:"unsending message expired"},V2NIM_ERROR_CODE_TEAM_MARK_READ_FAILED:{code:107302,message:"sending message failed for marking message read failed for too many team members"},V2NIM_ERROR_CODE_SENDER_OR_MANAGER_PERMISSION_ONLY_REVOKE:{code:107303,message:"only sender or manager can revoke message"},V2NIM_ERROR_CODE_DELETE_SELF_MESSAGE_NOT_ALLOWED:{code:107328,message:"delete self message not allowed"},V2NIM_ERROR_CODE_NOT_CHATBOT_ACCOUNT:{code:107329,message:"is not chatbot account"},V2NIM_ERROR_CODE_MESSAGE_SENSE_REQUIRED:{code:107330,message:"sender or receiver must sense message"},V2NIM_ERROR_CODE_HIGH_PRIORITY_MESSAGE_RATE_LIMIT:{code:107304,message:"rate limit of high-priority messages exceeded"},ACK_MESSAGE_BE_HIGH_PRIORITY:{code:107305,message:"ack message should be high-priority"},V2NIM_ERROR_CODE_DUPLICATE_CLIENT_MESSAGE_ID:{code:107306,message:"duplicate client message ID"},V2NIM_ERROR_CODE_INVALID_TIME_RANGE:{code:107439,message:"invalid time range"},V2NIM_ERROR_CODE_NOT_ADVANCED_TEAM:{code:108302,message:"not advanced team"},V2NIM_ERROR_CODE_TEAM_MANAGER_LIMIT:{code:108303,message:"team manager limit"},V2NIM_ERROR_CODE_JOINED_TEAM_LIMIT:{code:108305,message:"joined team limit"},V2NIM_ERROR_CODE_TEAM_NORMAL_MEMBER_CHAT_BANNED:{code:108306,message:"team normal member chat banned"},V2NIM_ERROR_CODE_INVITED_ACCOUNT_NOT_FRIEND:{code:108307,message:"invited account not friend"},V2NIM_ERROR_CODE_REJECT_ALL_TEAM_APPLICATIONS:{code:108308,message:"reject all team applications"},V2NIM_ERROR_CODE_TARGETING_MESSAGE_FOR_TEAM_DISABLED:{code:108318,message:"Targeting messages for group chat is disabled"},V2NIM_ERROR_CODE_INCLUSIVE_AS_FALSE_NOT_ALLOWED_FOR_SUPER_TEAM:{code:108319,message:'Setting "inclusive" to false for super teams is not allowed'},V2NIM_ERROR_CODE_CANNOT_MAKE_SUPER_TEAM_MESSAGE_VISIBLE_TO_NEW_MEMBERS:{code:108320,message:"Cannot make super team targeted messages visible to new members"},V2NIM_ERROR_CODE_CANNOT_ALLOW_TARGETED_MESSAGES_INCLUSIVE_TO_NEW_MEMBERS:{code:108321,message:"Cannot allow targeted messages inclusive to new members"},V2NIM_ERROR_CODE_TEAM_NOT_EXIST:{code:108404,message:"team not exist"},V2NIM_ERROR_CODE_TEAM_ALREADY_CHAT_BANNED:{code:108420,message:"team already chat banned"},V2NIM_ERROR_CODE_ALL_TEAM_MEMBER_CHAT_BANNED:{code:108423,message:"all team member chat banned"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT:{code:108434,message:"extended super team limit"},V2NIM_ERROR_CODE_CREATED_TEAM_LIMIT:{code:108435,message:"created team limit"},V2NIM_ERROR_CODE_TEAM_INVITATION_LIMIT:{code:108437,message:"team invitation limit"},V2NIM_ERROR_CODE_TEAM_HIT_ANTISPAM:{code:108451,message:"team hit antispam"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_LIMIT_NOT_CONFIGURED:{code:108304,message:"extended super team limit not configured"},V2NIM_ERROR_CODE_SUPER_TEAM_SERVICE_DISABLED:{code:108311,message:"super team service disabled"},V2NIM_ERROR_CODE_TEAM_READ_RECEIPT_RECORD_NOT_FOUND:{code:108301,message:"read receipt record for the team message not found"},V2NIM_ERROR_CODE_NOT_MANAGER:{code:108430,message:"unable to assign owner manager"},V2NIM_ERROR_CODE_ONLINE_MEMBER_COUNT_DISABLED:{code:108406,message:"number of online users service disabled"},V2NIM_ERROR_CODE_TRANSFER_DISABLED:{code:108310,message:"unable to transfer the ownership to owner"},V2NIM_ERROR_CODE_CREATE_TEAM_DISABLED:{code:108309,message:"unable to create team with more than %s people"},V2NIM_ERROR_CODE_EXTENDED_SUPER_TEAM_CREATE_FAILED:{code:108313,message:"/ extended super team creation failed，use open api to create the team"},V2NIM_ERROR_CODE_TEAM_MESSAGE_READ_RECEIPT_DISABLED:{code:108312,message:"read receipt for team messages function disabled"},V2NIM_ERROR_CODE_RETRY:{code:108449,message:"an error occurred, try again"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_NOT_TEAM_MEMBER:{code:109301,message:"list of chat banned users contains non team members"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_OPERATOR:{code:109303,message:"list of chat banned users contains the operator"},V2NIM_ERROR_CODE_CHAT_BAN_LIST_CONTAIN_TEAM_OWNER:{code:109304,message:"list of chat banned users contains the team owner"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_MANAGER_NOT_ALLOWED:{code:109305,message:"operation on team manager not allowed"},V2NIM_ERROR_CODE_NO_TEAM_INVITE_PERMISSION:{code:109306,message:"no team invite permission"},V2NIM_ERROR_CODE_TEAM_OWNER_QUIT_NOT_ALLOWED:{code:109307,message:"team owner quit not allowed"},V2NIM_ERROR_CODE_TEAM_OWNER_IN_KICK_LIST:{code:109308,message:"list of kicked user contains the team owner"},V2NIM_ERROR_CODE_INVITE_ROBOT_ACCOUNT_NOT_ALLOWED:{code:109309,message:"invite robot account not allowed"},V2NIM_ERROR_CODE_KICK_OPERATOR_NOT_ALLOWED:{code:109310,message:"kick operator not allowed"},V2NIM_ERROR_CODE_TEAM_MEMBER_ALREADY_EXIST:{code:109311,message:"team member already exist"},V2NIM_ERROR_CODE_TEAM_INVITATION_OR_APPLICATION_NOT_EXIST:{code:109313,message:"team invitation or application not exist"},V2NIM_ERROR_CODE_OPERATION_ON_TEAM_OWNER_NOT_ALLOWED:{code:109314,message:"operation on team owner not allowed"},V2NIM_ERROR_CODE_FORCED_PUSH_LIST_INCLUDES_NON_TARGETED_ACCOUNTS:{code:109318,message:"The forced push list includes non-targeted accounts"},V2NIM_ERROR_CODE_TEAM_MEMBER_NOT_EXIST:{code:109404,message:"team member not exist"},V2NIM_ERROR_CODE_TEAM_MEMBER_CHAT_BANNED:{code:109424,message:"team member chat banned"},V2NIM_ERROR_CODE_TEAM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:109427,message:"team owner operation permission required"},V2NIM_ERROR_CODE_TEAM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:109432,message:"team owner or manager operation permission required"},V2NIM_ERROR_CODE_TEAM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:109449,message:"team member concurrent operation failed"},V2NIM_ERROR_CODE_TEAM_MEMBER_HIT_ANTISPAM:{code:109451,message:"team member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_AND_ACCOUNT_MISMATCH:{code:110302,message:"conversation and account mismatch"},V2NIM_ERROR_CODE_CONVERSATION_STICK_TOP_LIMIT:{code:110303,message:"conversation stick top limit"},V2NIM_ERROR_CODE_CONVERSATION_BELONGED_GROUP_LIMIT:{code:110304,message:"conversation belonged group limit"},V2NIM_ERROR_CODE_CONVERSATION_IS_NOT_STICK_TOP:{code:110305,message:"conversation is not stick top"},V2NIM_ERROR_CODE_STICK_TOP_DISABLED:{code:110306,message:"conversation stick top disabled"},V2NIM_ERROR_CODE_CONVERSATION_NOT_EXIST:{code:110404,message:"conversation not exist"},V2NIM_ERROR_CODE_CHATROOM_LINK_UNAVAILABLE:{code:113304,message:"chatroom link unavailable"},V2NIM_ERROR_CODE_IM_CONNECTION_ABNORMAL:{code:113305,message:"IM connection abnormal"},V2NIM_ERROR_CODE_CHATROOM_NOT_EXIST:{code:113404,message:"chatroom not exist"},V2NIM_ERROR_CODE_CHATROOM_CLOSED:{code:113406,message:"chatroom closed"},V2NIM_ERROR_CODE_CHATROOM_REPEATED_OPERATION:{code:113409,message:"chatroom repeated operation"},V2NIM_ERROR_CODE_CHATROOM_DISABLED:{code:113410,message:"chatroom disabled"},V2NIM_ERROR_CODE_ALL_CHATROOM_MEMBER_CHAT_BANNED:{code:113423,message:"all chatroom member chat banned"},V2NIM_ERROR_CODE_CHATROOM_HIT_ANTISPAM:{code:113451,message:"chatroom hit antispam"},V2NIM_ERROR_CODE_ANONYMOUS_MEMBER_FORBIDDEN:{code:114303,message:"anonymous member forbidden"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_NOT_EXIST:{code:114404,message:"chatroom member not exist"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_REPEATED_OPERATION:{code:114405,message:"chatroom member repeated operation"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CHAT_BANNED:{code:114421,message:"chatroom member chat banned"},V2NIM_ERROR_CODE_ACCOUNT_IN_CHATROOM_BLOCK_LIST:{code:114426,message:"account in chatroom block list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OPERATION_PERMISSION_REQUIRED:{code:114427,message:"chatroom owner operation permission required"},V2NIM_ERROR_CODE_SELF_IN_CHATROOM_MEMBER_OPERATION_LIST:{code:114429,message:"self in chatroom member operation list"},V2NIM_ERROR_CODE_CHATROOM_OWNER_OR_MANAGER_OPERATION_PERMISSION_REQUIRED:{code:114432,message:"chatroom owner or manager operation permission required"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_LIMIT:{code:114437,message:"chatroom member limit"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_CONCURRENT_OPERATION_FAILED:{code:114449,message:"chatroom member concurrent operation failed"},V2NIM_ERROR_CODE_CHATROOM_MEMBER_HIT_ANTISPAM:{code:114451,message:"chatroom member hit antispam"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_NOT_EXIST:{code:116404,message:"conversation group not exist"},V2NIM_ERROR_CODE_CONVERSATION_GROUP_LIMIT:{code:116435,message:"conversation group limit"},V2NIM_ERROR_CODE_CONVERSATIONS_IN_GROUP_LIMIT:{code:116437,message:"conversations in group limit"},V2NIM_ERROR_CODE_COLLECTION_LIMIT:{code:189301,message:"collection limit"},V2NIM_ERROR_CODE_COLLECTION_NOT_EXIST:{code:189302,message:"collection not exist"},V2NIM_ERROR_CODE_COLLECTION_CONCURRENT_OPERATION_FAILED:{code:189449,message:"collection concurrent operation failed"},V2NIM_ERROR_CODE_INTERNAL:{code:190001,message:"internal error"},V2NIM_ERROR_CODE_ILLEGAL_STATE:{code:190002,message:"illegal state"},V2NIM_ERROR_CODE_MISUSE:{code:191001,message:"misuse"},V2NIM_ERROR_CODE_CANCELLED:{code:191002,message:"operation cancelled"},V2NIM_ERROR_CODE_CALLBACK_FAILED:{code:191003,message:"callback failed"},V2NIM_ERROR_CODE_INVALID_PARAMETER:{code:191004,message:"invalid parameter"},V2NIM_ERROR_CODE_TIMEOUT:{code:191005,message:"timeout"},V2NIM_ERROR_CODE_RESOURCE_NOT_EXIST:{code:191006,message:"resource not exist"},V2NIM_ERROR_CODE_RESOURCE_ALREADY_EXIST:{code:191007,message:"resource already exist"},V2NIM_ERROR_CODE_CONNECT_FAILED:{code:192001,message:"connect failed"},V2NIM_ERROR_CODE_CONNECT_TIMEOUT:{code:192002,message:"connect timeout"},V2NIM_ERROR_CODE_DISCONNECT:{code:192003,message:"disconnect"},V2NIM_ERROR_CODE_PROTOCOL_TIMEOUT:{code:192004,message:"protocol timeout"},V2NIM_ERROR_CODE_PROTOCOL_SEND_FAILED:{code:192005,message:"protocol send failed"},V2NIM_ERROR_CODE_REQUEST_FAILED:{code:192006,message:"request failed"},V2NIM_ERROR_CODE_FILE_NOT_FOUND:{code:194001,message:"file not found"},V2NIM_ERROR_CODE_FILE_CREATE_FAILED:{code:194002,message:"file create failed"},V2NIM_ERROR_CODE_FILE_OPEN_FAILED:{code:194003,message:"file open failed"},V2NIM_ERROR_CODE_FILE_WRITE_FAILED:{code:194004,message:"file write failed"},V2NIM_ERROR_CODE_FILE_READ_FAILED:{code:194005,message:"file read failed"},V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:{code:194006,message:"file upload failed"},V2NIM_ERROR_CODE_FILE_DOWNLOAD_FAILED:{code:194007,message:"file download failed"},V2NIM_ERROR_CODE_CLIENT_ANTISPAM:{code:195001,message:"client anti-spam"},V2NIM_ERROR_CODE_SERVER_ANTISPAM:{code:195002,message:"server anti-spam"},V2NIM_ERROR_CODE_STREAM_OUTPUT_STOPPED:{code:189318,message:"Streaming text response stopped"},V2NIM_ERROR_CODE_STREAM_OUTPUT_GENERATED:{code:189319,message:"Streaming text response generated"},V2NIM_ERROR_CODE_STREAM_OUTPUT_ABORTED:{code:189320,message:"Streaming text response aborted due to exception"},V2NIM_ERROR_CODE_INTERRUPTION_REJECTED:{code:189321,message:"Non-streaming messages cannot be interrupted"}},vl=qa(gl),Tl=reduce(vl).call(vl,(function(t,a){var u=gl[a];return t[a]=u.code,t}),{}),yl=reduce(vl).call(vl,(function(t,a){var u=gl[a];return t[u.code]=u.message,t}),{}),Il=function(t){function V2NIMErrorImpl(a){var u;return(u=t.call(this,a.desc)||this).name="V2NIMError",u.code=a.code||0,u.desc=a.desc||yl[u.code]||Al[u.code]||"",u.message=u.desc,u.detail=a.detail||{},u}return Mt(V2NIMErrorImpl,t),V2NIMErrorImpl.prototype.toString=function toString(){var t,a=this.name+"\n code: "+this.code+'\n message: "'+this.message+'"\n detail: '+(this.detail?fc(this.detail):"");return(null===(t=null==this?void 0:this.detail)||void 0===t?void 0:t.rawError)&&(a+="\n rawError: "+this.detail.rawError.message),a},V2NIMErrorImpl}(dc(Error));var Sl=function(t){function ValidateError(a,u,_){var h;return void 0===u&&(u={}),(h=t.call(this,{code:Tl.V2NIM_ERROR_CODE_PARAMETER_ERROR,detail:{reason:a,rules:_,data:u}})||this).name="validateError",h.message=h.message+"\n"+fc(h.detail,null,2),h.data=u,h.rules=_,h}return Mt(ValidateError,t),ValidateError}(Il),Ml=function(t){function ValidateErrorV2(a){var u,_,h,m;return(u=t.call(this,{code:Tl.V2NIM_ERROR_CODE_INVALID_PARAMETER,detail:{reason:null===(_=a.detail)||void 0===_?void 0:_.reason,rules:null===(h=a.detail)||void 0===h?void 0:h.rules,data:null===(m=a.detail)||void 0===m?void 0:m.data}})||this).name="ValidateErrorV2",u}return Mt(ValidateErrorV2,t),ValidateErrorV2}(Il),Nl=function(t){function UploadError(a){var u;return(u=t.call(this,bt({code:400},a))||this).desc=u.desc||"upload file error",u.message=u.desc,u.name="uploadError",u}return Mt(UploadError,t),UploadError}(Il),Al={200:null,406:null,808:null,810:null,302:"The user name or password is incorrect.",405:"Parameter length too long",408:"Client request timed out",415:"Client network unavailable",422:"Account disabled",508:"Expiration date",509:"Invalid",7101:"Be pulled black",700:"Partial failure of batch operation",801:"The number of people in the team has reached the upper limit",802:"No permission",803:"The team does not exist or has not changed",804:"The user is not in the team",805:"Team type mismatch",806:"The number of teams created has reached the limit",807:"Team member not valid",809:"Already in the team",811:"The number of accounts in the forced push list exceeds the limit",812:"The team is muted",813:"Due to the limited number of team, some pull people successfully",814:"Disable team message read service",815:"Maximum number of team administrators",816:"Batch operation partial failure",9102:"Channel failure",9103:"This call has been answered / rejected at another end",10201:"Signaling: target NIM client is offline",10202:"Signaling: push is unreachable",10404:"Signaling: channel not exists",10405:"Signaling: channel already exists",10406:"Signaling: member of channel not exists",10407:"Signaling: member of channel already exists",10408:"Signaling: the invitation request does not exist or has expired",10409:"Signaling: the invitation request has been rejected",10410:"Signaling: the invitation request has been accepted",10414:"Signaling: request parameter error",10417:"Signaling: uid conflict",10419:"Signaling: the number of members of channel exceed the limit",10420:"Signaling: member is already in the channel on other client",10700:"Signaling: phased success",13002:"Abnormal chatroom status",13003:"In the blacklist",13004:"In the mute list",13006:"All members are muted, and only the administrator can speak"};var Ol=Is.trim,Rl=h.parseInt,Cl=h.Symbol,bl=Cl&&Cl.iterator,Pl=/^[+-]?0x/i,xl=O(Pl.exec),kl=8!==Rl(Es+"08")||22!==Rl(Es+"0x16")||bl&&!fails((function(){Rl(Object(bl))}))?function parseInt(t,a){var u=Ol(toString(t));return Rl(u,a>>>0||(xl(Pl,u)?16:10))}:Rl;_export({global:!0,forced:parseInt!=kl},{parseInt:kl});var wl=j.parseInt;_export({target:"Array",stat:!0},{isArray:ei});var Dl=j.Array.isArray;function get(t,a){if("object"!=typeof t||null===t)return t;for(var u=(a=a||"").split("."),_=0;_<u.length;_++){var h=u[_],m=t[h],E=indexOf(h).call(h,"["),g=indexOf(h).call(h,"]");if(-1!==E&&-1!==g&&E<g){var T=slice(h).call(h,0,E),I=wl(slice(h).call(h,E+1,g));m=t[T],m=Dl(m)?m[I]:void 0}if(null==m)return m;t=m}return t}var Ll,Vl=(Ll=function _s4(){return(65536*(1+Math.random())|0).toString(16).substring(1)},function(){return Ll()+Ll()+Ll()+Ll()+Ll()+Ll()+Ll()+Ll()});function getEnumKeys(t){var a;return filter(a=qa(t)).call(a,(function(t){return!(+t>=0)}))}function getEnumKeyByEnumValue(t,a){var u,_=filter(u=qa(t)).call(u,(function(u){return t[u]==a}));return _.length>0?_[0]:void 0}function assignOptions(t,a){return function assignWith(t,a,u,_){for(var h in t=t||{},u=u||{},_=_||function(){},a=a||{}){var m=_(t[h],a[h]);t[h]=void 0===m?a[h]:m}for(var E in u){var g=_(t[E],u[E]);t[E]=void 0===g?u[E]:g}return t}({},t,a,(function(t,a){return void 0===a?t:a}))}function emptyFuncWithPromise(){return Jn.resolve()}var callWithSafeIterationClosing=function(t,a,u,_){try{return _?a(anObject(u)[0],u[1]):a(u)}catch(a){iteratorClose(t,"throw",a)}},Ul=Array,Fl=!checkCorrectnessOfIteration((function(t){Array.from(t)}));_export({target:"Array",stat:!0,forced:Fl},{from:function from(t){var a=toObject(t),u=po(this),_=arguments.length,h=_>1?arguments[1]:void 0,m=void 0!==h;m&&(h=functionBindContext(h,_>2?arguments[2]:void 0));var E,g,T,I,S,M,N=getIteratorMethod$5(a),A=0;if(!N||this===Ul&&isArrayIteratorMethod(N))for(E=lengthOfArrayLike(a),g=u?new this(E):Ul(E);E>A;A++)M=m?h(a[A],A):a[A],createProperty(g,A,M);else for(S=(I=getIterator(a,N)).next,g=u?new this:[];!(T=b(S,I)).done;A++)M=m?callWithSafeIterationClosing(I,h,[T.value,A],!0):T.value,createProperty(g,A,M);return g.length=A,g}});var Gl=j.Array.from,Bl=getIteratorMethod$5;var Yl=createCommonjsModule((function(t){function _typeof(a){return t.exports=_typeof="function"==typeof _a&&"symbol"==typeof Da?function(t){return typeof t}:function(t){return t&&"function"==typeof _a&&t.constructor===_a&&t!==_a.prototype?"symbol":typeof t},t.exports.__esModule=!0,t.exports.default=t.exports,_typeof(a)}t.exports=_typeof,t.exports.__esModule=!0,t.exports.default=t.exports})),jl=createCommonjsModule((function(t){var a=Yl.default;function _regeneratorRuntime(){
/*! regenerator-runtime -- Copyright (c) 2014-present, Facebook, Inc. -- license (MIT): https://github.com/facebook/regenerator/blob/main/LICENSE */
t.exports=_regeneratorRuntime=function _regeneratorRuntime(){return u},t.exports.__esModule=!0,t.exports.default=t.exports;var u={},_=Object.prototype,h=_.hasOwnProperty,m="function"==typeof _a?_a:{},E=m.iterator||"@@iterator",g=m.asyncIterator||"@@asyncIterator",T=m.toStringTag||"@@toStringTag";function define(t,a,u){return Ea(t,a,{value:u,enumerable:!0,configurable:!0,writable:!0}),t[a]}try{define({},"")}catch(t){define=function define(t,a,u){return t[a]=u}}function wrap(t,a,u,_){var h=a&&a.prototype instanceof Generator?a:Generator,m=ut(h.prototype),E=new Context(_||[]);return m._invoke=function(t,a,u){var _="suspendedStart";return function(h,m){if("executing"===_)throw new Error("Generator is already running");if("completed"===_){if("throw"===h)throw m;return doneResult()}for(u.method=h,u.arg=m;;){var E=u.delegate;if(E){var g=maybeInvokeDelegate(E,u);if(g){if(g===I)continue;return g}}if("next"===u.method)u.sent=u._sent=u.arg;else if("throw"===u.method){if("suspendedStart"===_)throw _="completed",u.arg;u.dispatchException(u.arg)}else"return"===u.method&&u.abrupt("return",u.arg);_="executing";var T=tryCatch(t,a,u);if("normal"===T.type){if(_=u.done?"completed":"suspendedYield",T.arg===I)continue;return{value:T.arg,done:u.done}}"throw"===T.type&&(_="completed",u.method="throw",u.arg=T.arg)}}}(t,u,E),m}function tryCatch(t,a,u){try{return{type:"normal",arg:t.call(a,u)}}catch(t){return{type:"throw",arg:t}}}u.wrap=wrap;var I={};function Generator(){}function GeneratorFunction(){}function GeneratorFunctionPrototype(){}var S={};define(S,E,(function(){return this}));var M=va&&va(va(values([])));M&&M!==_&&h.call(M,E)&&(S=M);var N=GeneratorFunctionPrototype.prototype=Generator.prototype=ut(S);function defineIteratorMethods(t){var a;forEach$1(a=["next","throw","return"]).call(a,(function(a){define(t,a,(function(t){return this._invoke(a,t)}))}))}function AsyncIterator(t,u){function invoke(_,m,E,g){var T=tryCatch(t[_],t,m);if("throw"!==T.type){var I=T.arg,S=I.value;return S&&"object"==a(S)&&h.call(S,"__await")?u.resolve(S.__await).then((function(t){invoke("next",t,E,g)}),(function(t){invoke("throw",t,E,g)})):u.resolve(S).then((function(t){I.value=t,E(I)}),(function(t){return invoke("throw",t,E,g)}))}g(T.arg)}var _;this._invoke=function(t,a){function callInvokeWithMethodAndArg(){return new u((function(u,_){invoke(t,a,u,_)}))}return _=_?_.then(callInvokeWithMethodAndArg,callInvokeWithMethodAndArg):callInvokeWithMethodAndArg()}}function maybeInvokeDelegate(t,a){var u=t.iterator[a.method];if(void 0===u){if(a.delegate=null,"throw"===a.method){if(t.iterator.return&&(a.method="return",a.arg=void 0,maybeInvokeDelegate(t,a),"throw"===a.method))return I;a.method="throw",a.arg=new TypeError("The iterator does not provide a 'throw' method")}return I}var _=tryCatch(u,t.iterator,a.arg);if("throw"===_.type)return a.method="throw",a.arg=_.arg,a.delegate=null,I;var h=_.arg;return h?h.done?(a[t.resultName]=h.value,a.next=t.nextLoc,"return"!==a.method&&(a.method="next",a.arg=void 0),a.delegate=null,I):h:(a.method="throw",a.arg=new TypeError("iterator result is not an object"),a.delegate=null,I)}function pushTryEntry(t){var a={tryLoc:t[0]};1 in t&&(a.catchLoc=t[1]),2 in t&&(a.finallyLoc=t[2],a.afterLoc=t[3]),this.tryEntries.push(a)}function resetTryEntry(t){var a=t.completion||{};a.type="normal",delete a.arg,t.completion=a}function Context(t){this.tryEntries=[{tryLoc:"root"}],forEach$1(t).call(t,pushTryEntry,this),this.reset(!0)}function values(t){if(t){var a=t[E];if(a)return a.call(t);if("function"==typeof t.next)return t;if(!isNaN(t.length)){var u=-1,_=function next(){for(;++u<t.length;)if(h.call(t,u))return next.value=t[u],next.done=!1,next;return next.value=void 0,next.done=!0,next};return _.next=_}}return{next:doneResult}}function doneResult(){return{value:void 0,done:!0}}return GeneratorFunction.prototype=GeneratorFunctionPrototype,define(N,"constructor",GeneratorFunctionPrototype),define(GeneratorFunctionPrototype,"constructor",GeneratorFunction),GeneratorFunction.displayName=define(GeneratorFunctionPrototype,T,"GeneratorFunction"),u.isGeneratorFunction=function(t){var a="function"==typeof t&&t.constructor;return!!a&&(a===GeneratorFunction||"GeneratorFunction"===(a.displayName||a.name))},u.mark=function(t){return _t?_t(t,GeneratorFunctionPrototype):(t.__proto__=GeneratorFunctionPrototype,define(t,T,"GeneratorFunction")),t.prototype=ut(N),t},u.awrap=function(t){return{__await:t}},defineIteratorMethods(AsyncIterator.prototype),define(AsyncIterator.prototype,g,(function(){return this})),u.AsyncIterator=AsyncIterator,u.async=function(t,a,_,h,m){void 0===m&&(m=Jn);var E=new AsyncIterator(wrap(t,a,_,h),m);return u.isGeneratorFunction(a)?E:E.next().then((function(t){return t.done?t.value:E.next()}))},defineIteratorMethods(N),define(N,T,"Generator"),define(N,E,(function(){return this})),define(N,"toString",(function(){return"[object Generator]"})),u.keys=function(t){var a=[];for(var u in t)a.push(u);return reverse(a).call(a),function next(){for(;a.length;){var u=a.pop();if(u in t)return next.value=u,next.done=!1,next}return next.done=!0,next}},u.values=values,Context.prototype={constructor:Context,reset:function reset(t){var a;if(this.prev=0,this.next=0,this.sent=this._sent=void 0,this.done=!1,this.delegate=null,this.method="next",this.arg=void 0,forEach$1(a=this.tryEntries).call(a,resetTryEntry),!t)for(var u in this)"t"===u.charAt(0)&&h.call(this,u)&&!isNaN(+slice(u).call(u,1))&&(this[u]=void 0)},stop:function stop(){this.done=!0;var t=this.tryEntries[0].completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function dispatchException(t){if(this.done)throw t;var a=this;function handle(u,_){return m.type="throw",m.arg=t,a.next=u,_&&(a.method="next",a.arg=void 0),!!_}for(var u=this.tryEntries.length-1;u>=0;--u){var _=this.tryEntries[u],m=_.completion;if("root"===_.tryLoc)return handle("end");if(_.tryLoc<=this.prev){var E=h.call(_,"catchLoc"),g=h.call(_,"finallyLoc");if(E&&g){if(this.prev<_.catchLoc)return handle(_.catchLoc,!0);if(this.prev<_.finallyLoc)return handle(_.finallyLoc)}else if(E){if(this.prev<_.catchLoc)return handle(_.catchLoc,!0)}else{if(!g)throw new Error("try statement without catch or finally");if(this.prev<_.finallyLoc)return handle(_.finallyLoc)}}}},abrupt:function abrupt(t,a){for(var u=this.tryEntries.length-1;u>=0;--u){var _=this.tryEntries[u];if(_.tryLoc<=this.prev&&h.call(_,"finallyLoc")&&this.prev<_.finallyLoc){var m=_;break}}m&&("break"===t||"continue"===t)&&m.tryLoc<=a&&a<=m.finallyLoc&&(m=null);var E=m?m.completion:{};return E.type=t,E.arg=a,m?(this.method="next",this.next=m.finallyLoc,I):this.complete(E)},complete:function complete(t,a){if("throw"===t.type)throw t.arg;return"break"===t.type||"continue"===t.type?this.next=t.arg:"return"===t.type?(this.rval=this.arg=t.arg,this.method="return",this.next="end"):"normal"===t.type&&a&&(this.next=a),I},finish:function finish(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.finallyLoc===t)return this.complete(u.completion,u.afterLoc),resetTryEntry(u),I}},catch:function _catch(t){for(var a=this.tryEntries.length-1;a>=0;--a){var u=this.tryEntries[a];if(u.tryLoc===t){var _=u.completion;if("throw"===_.type){var h=_.arg;resetTryEntry(u)}return h}}throw new Error("illegal catch attempt")},delegateYield:function delegateYield(t,a,u){return this.delegate={iterator:values(t),resultName:a,nextLoc:u},"next"===this.method&&(this.arg=void 0),I}},u}t.exports=_regeneratorRuntime,t.exports.__esModule=!0,t.exports.default=t.exports})),Hl=jl(),Kl=Hl;try{regeneratorRuntime=Hl}catch(t){"object"==typeof globalThis?globalThis.regeneratorRuntime=Hl:Function("r","regeneratorRuntime = r")(Hl)}function __rest(t,a){var u={};for(var _ in t)Object.prototype.hasOwnProperty.call(t,_)&&a.indexOf(_)<0&&(u[_]=t[_]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var h=0;for(_=Object.getOwnPropertySymbols(t);h<_.length;h++)a.indexOf(_[h])<0&&Object.prototype.propertyIsEnumerable.call(t,_[h])&&(u[_[h]]=t[_[h]])}return u}function __awaiter(t,a,u,_){return new(u||(u=Promise))((function(h,m){function fulfilled(t){try{step(_.next(t))}catch(t){m(t)}}function rejected(t){try{step(_.throw(t))}catch(t){m(t)}}function step(t){t.done?h(t.value):function adopt(t){return t instanceof u?t:new u((function(a){a(t)}))}(t.value).then(fulfilled,rejected)}step((_=_.apply(t,a||[])).next())}))}function isPlainObject(t){return null!=t&&"object"==typeof t&&va(t)==Object.prototype}function merge$1(t,a){var u=isPlainObject(t)||Dl(t),_=isPlainObject(a)||Dl(a);if(u&&_){for(var h in a){var m=merge$1(t[h],a[h]);void 0!==m&&(t[h]=m)}return t}return a}var Wl={setLogger:function setLogger(t){throw new Error("setLogger not implemented.")},platform:"",WebSocket:function(){function AdapterSocket(t,a){throw this.CONNECTING=0,this.OPEN=1,this.CLOSING=2,this.CLOSED=3,this.binaryType="",new Error("Method not implemented.")}var t=AdapterSocket.prototype;return t.close=function close(t,a){throw new Error("Method not implemented.")},t.send=function send(t){throw new Error("Method not implemented.")},t.onclose=function onclose(t){throw new Error("Method not implemented.")},t.onerror=function onerror(t){throw new Error("Method not implemented.")},t.onmessage=function onmessage(t){throw new Error("Method not implemented.")},t.onopen=function onopen(t){throw new Error("Method not implemented.")},AdapterSocket}(),localStorage:{},request:function request(t,a){throw new Error("request not implemented.")},uploadFile:function uploadFile(t){throw new Error("uploadFile not implemented.")},getSystemInfo:function getSystemInfo(){throw new Error("getSystemInfo not implemented.")},getFileUploadInformation:function getFileUploadInformation(t){throw new Error("getFileUploadInformation not implemented.")},envPayload:{},net:{getNetworkStatus:function getNetworkStatus(){return Jn.resolve({net_type:0,net_connect:!0})},onNetworkStatusChange:function onNetworkStatusChange(t){},offNetworkStatusChange:function offNetworkStatusChange(){}},logStorage:function(){function AdapterLogStorageImpl(t){}var t=AdapterLogStorageImpl.prototype;return t.open=function open(){return Jn.resolve()},t.close=function close(){},t.addLogs=function addLogs(t){return Jn.resolve()},t.extractLogs=function extractLogs(){return Jn.resolve()},AdapterLogStorageImpl}()};function getPromiseWithAbort(t){var a={},u=new Jn((function(t,u){a.abort=u}));return a.promise=Jn.race([t,u]),a}Jn.reject;var ql=function(){function PromiseManager(){this.abortFns=[]}var t=PromiseManager.prototype;return t.add=function add(t){var a=getPromiseWithAbort(t);return this.abortFns.push(a.abort),a.promise},t.clear=function clear(t){var a;forEach$1(a=this.abortFns).call(a,(function(a){return a(t||new Il({code:Tl.V2NIM_ERROR_CODE_CANCELLED,detail:{reason:"Aborted"}}))})),this.abortFns=[]},t.destroy=function destroy(){this.clear()},PromiseManager}(),$l={tolerantRTT:3e3,bestRTT:100,maxChances:5,enable:!0},zl={timestamp:0,rtt:0,baseClock:0,baseTime:0},Jl=function(){function TimeOrigin(t,a,u){void 0===u&&(u="getServerTime"),this.serverOrigin=zl,this.config=$l,this.isSettingNTP=!1,this.currentChance=0,this.failedDelay=2e3,this.successDelay=3e5,this.timer=0,this.cmdName="getServerTime",this.core=t,this.logger=t.logger,this.promiseManager=new ql,this.cmdName=u,a&&this.setOptions(a)}var t=TimeOrigin.prototype;return t.setOptions=function setOptions(t){this.config=bt({},$l,this.config,t)},t.reset=function reset(){this.timer&&clearTimeout(this.timer),this.promiseManager.clear(),this.serverOrigin=zl,this.currentChance=0},t.setOriginTimetick=function setOriginTimetick(){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var t,a,u,_,h,m,E,g,T,I;return Kl.wrap((function _callee$(S){for(;;)switch(S.prev=S.next){case 0:if(this.config.enable){S.next=2;break}return S.abrupt("return");case 2:if(!this.isSettingNTP){S.next=4;break}return S.abrupt("return");case 4:if(!(this.currentChance>=this.config.maxChances)){S.next=6;break}return S.abrupt("return");case 6:if(t=get(this.core,"auth.status"),a=get(this.core,"status"),u=get(this.core,"V2NIMLoginService.lifeCycle.loginStatus"),"logined"===t||"logined"===a||1===u){S.next=11;break}return S.abrupt("return");case 11:return this.isSettingNTP=!0,this.currentChance++,this.timer&&clearTimeout(this.timer),this.timer=0,_="TimeOrigin::setOriginTimetick:",h=Zn(),this.core.logger.debug(_+" getServerTime start, times "+this.currentChance),S.prev=18,S.next=21,this.promiseManager.add(this.core.sendCmd(this.cmdName));case 21:E=S.sent,m=get(E,"content.time"),this.isSettingNTP=!1,S.next=33;break;case 26:return S.prev=26,S.t0=S.catch(18),g=S.t0,this.isSettingNTP=!1,this.logger.warn(_+" Calculate Delay time, getServerTime error",g),g.code!==Tl.V2NIM_ERROR_CODE_CANCELLED&&(clearTimeout(this.timer),this.timer=hi(bind$1(T=this.setOriginTimetick).call(T,this),this.failedDelay)),S.abrupt("return");case 33:if(m){S.next=37;break}return this.core.logger.warn(_+" Calculate Delay time incorrect format"),this.config.enable=!1,S.abrupt("return");case 37:I=Zn()-h,this.doSet(m,I);case 39:case"end":return S.stop()}}),_callee,this,[[18,26]])})))},t.doSet=function doSet(t,a){var u,_="TimeOrigin::setOriginTimetick:";if(a>this.config.tolerantRTT)this.logger.warn(_+" denied RTT:"+a),clearTimeout(this.timer),this.timer=hi(bind$1(u=this.setOriginTimetick).call(u,this),this.failedDelay);else if(a>this.config.bestRTT){var h;this.serverOrigin.rtt&&a>=this.serverOrigin.rtt?this.logger.warn(_+" ignore RTT:"+a):(this.setServerOrigin(a,t),this.logger.log(_+" accept reluctantly RTT:"+a)),clearTimeout(this.timer),this.timer=hi(bind$1(h=this.setOriginTimetick).call(h,this),this.failedDelay)}else{var m;this.setServerOrigin(a,t),this.logger.debug(_+" accept best RTT:"+a),this.currentChance=0,clearTimeout(this.timer),this.timer=hi(bind$1(m=this.setOriginTimetick).call(m,this),this.successDelay)}},t.getNTPTime=function getNTPTime(t){if(void 0===t&&(t=this.getTimeNode()),this.checkNodeReliable(t)){var a=Math.floor(t.time-this.serverOrigin.baseTime);return this.serverOrigin.timestamp+a}return Zn()},t.checkNodeReliable=function checkNodeReliable(t){if(void 0===t&&(t=this.getTimeNode()),this.serverOrigin.timestamp){if(0===this.serverOrigin.baseClock)return!0;var a=t.clock-this.serverOrigin.baseClock,u=t.time-this.serverOrigin.baseTime;return Math.abs(u-a)<500}return!1},t.checkPerformance=function checkPerformance(){return"BROWSER"===Wl.platform&&!("undefined"==typeof performance||!performance.now)},TimeOrigin.checkPerformance=function checkPerformance(){return"BROWSER"===Wl.platform&&!("undefined"==typeof performance||!performance.now)},t.getTimeNode=function getTimeNode(){return{clock:this.checkPerformance()?performance.now():0,time:Zn()}},TimeOrigin.getTimeNode=function getTimeNode(){return{clock:TimeOrigin.checkPerformance()?performance.now():0,time:Zn()}},t.setServerOrigin=function setServerOrigin(t,a){this.serverOrigin={timestamp:a+Math.floor(t/2),rtt:t,baseClock:this.checkPerformance()?performance.now():0,baseTime:Zn()}},TimeOrigin}();function _createForOfIteratorHelperLoose$2(t,a){var u,_=void 0!==_a&&Bl(t)||t["@@iterator"];if(_)return bind$1(u=(_=_.call(t)).next).call(u,_);if(Dl(t)||(_=function _unsupportedIterableToArray$2(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$2(t,a);var _=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Gl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?_arrayLikeToArray$2(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){_&&(t=_);var h=0;return function(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$2(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=Array(a);u<a;u++)_[u]=t[u];return _}var Ql={},Xl={};function parseEachCmd(t,a,u,_,h){var m,E={cmd:u,raw:t,error:null,service:null==a?void 0:a.service,content:{},__receiveTimeNode:Jl.getTimeNode()};if(!u||!a)return E.notFound=!0,E;(18===a.sid||a.sid>=26&&a.sid<100)&&(t.code=function toReadableCode(t){if("number"!=typeof t||t!=t)throw new Il({code:Tl.V2NIM_ERROR_CODE_INTERNAL,detail:{reason:"Read code failed",rawData:""+t}});if(t<0||t>=0&&t<1e3||t>=2e4&&t<=20099)return t;var a=(65535&t)>>9;a-=a<=38?1:2;return 1e5+1e3*a+(511&t)}(t.code));var g,T=function genCmdError(t,a){var u=yl[t],_=Al[t];return null===_?null:new Il({code:t,desc:u||_||t,detail:{cmd:a,timetag:Zn()}})}(t.code,u);if(E.error=T,E.error){if(E.error.detail.cmd=u,!(null===(m=null==a?void 0:a.ignoreErrCodes)||void 0===m?void 0:includes(m).call(m,t.code)))return E;h.warn("parseCmd:: ignore error ",E.error),E.error.detail.ignore=!0}a.response&&forEach$1(g=a.response).call(g,(function(t,a){var u=_[a],h=t.type,m=t.name,g=t.reflectMapper;if(void 0!==u)switch(h){case"Property":E.content[m]=g?deserialize(u,g):u;break;case"PropertyArray":E.content[m]=map$6(u).call(u,(function(t){return g?deserialize(t,g):t}));break;case"Int":case"Long":case"Byte":E.content[m]=+u;break;case"Bool":E.content[m]="true"===u||!0===u||1===u;break;default:E.content[m]=u}}));return E}function serialize(t,a,u){var _={};for(var h in t=function flattenObjByMapper(t,a){var u={};for(var _ in a){for(var h,m=a[_],E="number"==typeof m?_:m.access?m.access:_,g=t,T=_createForOfIteratorHelperLoose$2(E.split("."));!(h=T()).done;){var I=h.value;if(void 0===g[I]||null===g[I]){g=void 0;break}g=g[I]}void 0!==g&&(u[E]=g)}return u}(t,a),a){var m=a[h],E="number"==typeof m?h:m.access?m.access:h;if(!u||includes(u).call(u,h))if(E in t){if("number"==typeof m)_[m]=t[E];else if("object"==typeof m)if(m.converter){var g=m.converter(t[E],t);void 0!==g&&(_[m.id]=g)}else _[m.id]=t[E]}else"object"==typeof m&&m.def&&("function"==typeof m.def?_[m.id]=m.def(t):_[m.id]=m.def)}return _}function deserialize(t,a){var u={};for(var _ in t){var h=a[_];if("string"==typeof h)u[h]=t[_];else if("object"==typeof h&&"prop"in h){var m=h.access?h.access:h.prop;if(h.converter){var E=h.converter(t[_],t);void 0!==E&&(u[m]=E)}else h.type&&"number"===h.type?u[m]=+t[_]:h.type&&"boolean"===h.type?u[m]=!("0"===t[_]||!t[_]):u[m]=t[_]}}for(var g in a){var T=a[g];if(T&&void 0!==T.def){var I=T.access?T.access:T.prop;I in u||("function"==typeof T.def?u[I]=T.def(t):u[I]=T.def)}}return u=function unflattenObj(t){var a={},u=function _loop(u){var _=u.split(".");reduce(_).call(_,(function(a,h,m){return a[h]||(a[h]=isNaN(Number(_[m+1]))?_.length-1==m?t[u]:{}:[])}),a)};for(var _ in t)u(_);return a}(u),u}function registerParser(t){for(var a in bt(Ql,t.cmdConfig),t.cmdMap){var u=t.cmdMap[a],_=t.cmdConfig[u];if(_)if(Dl(Xl[a])){for(var h,m=!1,E=_createForOfIteratorHelperLoose$2(Xl[a]);!(h=E()).done;){var g=h.value;if(g.cmd===u&&g.config.service===_.service){m=!0;break}}m||Xl[a].push({config:_,cmd:u})}else Xl[a]=[{config:_,cmd:u}]}}var Zl=entryVirtual("Array").values,eu=Array.prototype,tu={DOMTokenList:!0,NodeList:!0},values=function(t){var a=t.values;return t===eu||H(eu,t)&&a===eu.values||de(tu,Zt(t))?Zl:a},ru=Ni.every,ou=arrayMethodIsStrict("every");_export({target:"Array",proto:!0,forced:!ou},{every:function every(t){return ru(this,t,arguments.length>1?arguments[1]:void 0)}});var nu=entryVirtual("Array").every,iu=Array.prototype,every=function(t){var a=t.every;return t===iu||H(iu,t)&&a===iu.every?nu:a};function replacer(t,a){return a instanceof RegExp?"__REGEXP "+a.toString():a}function validate(t,a,u,_){var h;void 0===a&&(a={}),void 0===_&&(_=!1);var m={};return forEach$1(h=qa(t)).call(h,(function(h){var E=t[h].type,g=u?"In "+u+", ":"";if(null==a){var T=g+"param is null or undefined";throw _?new Ml({detail:{reason:T,data:{key:h},rules:"required"}}):new Sl(T,{key:h},"required")}if(void 0===a[h]){if(!1===t[h].required)return void(m[h]=a[h]);var I=g+"param '"+h+"' is required";throw _?new Ml({detail:{reason:I,data:{key:h},rules:"required"}}):new Sl(I,{key:h},"required")}var S=au[E];if(S&&!S(a,h,t[h],_)){var M=g+"param '"+h+"' unexpected",N={key:h,value:a[h]};throw _?new Ml({detail:{reason:M,data:N,rules:fc(t[h],replacer)}}):new Sl(M,N,fc(t[h],replacer))}m[h]=a[h]})),m}var au={string:function string(t,a,u){var _=u.allowEmpty,h=u.max,m=u.min,E=u.regExp,g=t[a];return"string"==typeof g&&((!1!==_||""!==g)&&(!("number"==typeof h&&g.length>h)&&(!("number"==typeof m&&g.length<m)&&!(function isRegExp(t){return"[object RegExp]"===Object.prototype.toString.call(t)}(E)&&!E.test(g)))))},number:function number(t,a,u){var _=u.min,h=u.max,m=t[a];return"number"==typeof m&&(!("number"==typeof _&&m<_)&&!("number"==typeof h&&m>h))},boolean:function boolean(t,a){return"boolean"==typeof t[a]},file:function file(t,a){return!0},enum:function _enum(t,a,u){var _=values(u),h=t[a];return!_||indexOf(_).call(_,h)>-1},jsonstr:function jsonstr(t,a){try{var u=JSON.parse(t[a]);return"object"==typeof u&&null!==u}catch(t){return!1}},func:function func(t,a){return"function"==typeof t[a]},array:function array(t,a,u,_){void 0===_&&(_=!1);var h=u.itemType,m=u.itemRules,E=u.rules,g=u.min,T=u.max,I=values(u),S=t[a];if(!Dl(S))return!1;if("number"==typeof T&&S.length>T)return!1;if("number"==typeof g&&S.length<g)return!1;if(m)forEach$1(S).call(S,(function(t,u){var h,E;validate(((h={})[u]=m,h),((E={})[u]=t,E),a+"["+u+"]",_)}));else if(E)forEach$1(S).call(S,(function(t,u){return validate(E,t,a+"["+u+"]",_)}));else if("enum"===h){if(I&&function difference(t,a){return a=a||[],filter(t=t||[]).call(t,(function(t){return-1===indexOf(a).call(a,t)}))}(S,I).length)return!1}else if(h&&!every(S).call(S,(function(t){return typeof t===h})))return!1;return!0},object:function object(t,a,u,_){void 0===_&&(_=!1);var h=u.rules,m=u.allowEmpty,E=t[a];if("object"!=typeof E||null===E)return!1;if(h){var g=qa(h),T=qa(E),I=filter(T).call(T,(function(t){return indexOf(g).call(g,t)>-1}));if(!1===m&&0===I.length)return!1;validate(h,E,a,_)}return!0}},su=["error","warn","log","debug"],cu=function emptyFunc(){},lu=["off","error","warn","log","debug"],uu=function(){function Logger(t,a){void 0===a&&(a={}),this.storageArr=[],this.debugLevel="off",this.timer=0,this.strategies={debug:{name:"debg",func:console.log},log:{name:"info",func:console.log},warn:{name:"warn",func:console.warn},error:{name:"erro",func:console.error}},this.debug=cu,this.log=cu,this.warn=cu,this.error=cu,this.iid=Math.round(1e3*Math.random()),this.debugLevel=includes(lu).call(lu,t)?t:"off",a.debugLevel&&(this.debugLevel=includes(lu).call(lu,a.debugLevel)?a.debugLevel:this.debugLevel),this.logStorage=!1===a.storageEnable?null:new Wl.logStorage(null==a?void 0:a.storageName),this.setOptions(a),this.setLogFunc(this.debugLevel),this.setTimer(),this.open()}var t=Logger.prototype;return t.getDebugMode=function getDebugMode(){return"debug"===this.debugLevel},t.open=function open(t){var a=this;this.logStorage&&this.logStorage.open(t).then((function(){a.log("Logger::open success")})).catch((function(t){a.warn("Logger::open failed",t)}))},t.setOptions=function setOptions(t){if(t&&t.logFunc){var a=t.logFunc;for(var u in a){var _=u,h=a[_];h&&(this.strategies[_].func=h)}}},t.setLogFunc=function setLogFunc(t,a){var u=this;void 0===a&&(a="log");var _=findIndex(su).call(su,(function(a){return a===t})),h=findIndex(su).call(su,(function(t){return t===a}));forEach$1(su).call(su,(function(t,a){u[t]=function(){if(!(a>_&&a>h)){var u=slice(Array.prototype).call(arguments),m=this.strategies[t],E=this.formatArgs(u,m.name);a<=h&&this.logStorage&&this.prepareSaveLog(E,t),a<=_&&m.func(E)}}}))},t.extractLogs=function extractLogs(){var t;return this.logStorage?null===(t=this.logStorage)||void 0===t?void 0:t.extractLogs():Jn.resolve("")},t.prepareSaveLog=function prepareSaveLog(t,a){this.storageArr.push({text:t,level:a,time:Zn(),iid:this.iid}),this.timer||this.setTimer(),this.storageArr.length>=100&&(this.triggerTimer(),this.storageArr=[])},t.saveLogs=function saveLogs(){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var t;return Kl.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.logStorage){a.next=2;break}return a.abrupt("return");case 2:return t=this.storageArr,this.storageArr=[],a.prev=4,a.next=7,this.logStorage.addLogs(t);case 7:a.next=11;break;case 9:a.prev=9,a.t0=a.catch(4);case 11:case"end":return a.stop()}}),_callee,this,[[4,9]])})))},t.clearTimer=function clearTimer(){this.timer&&clearTimeout(this.timer),this.timer=0},t.setTimer=function setTimer(){var t;this.clearTimer(),this.timer=hi(bind$1(t=this.triggerTimer).call(t,this),5e3)},t.triggerTimer=function triggerTimer(){this.clearTimer(),this.saveLogs()},t.formatArgs=function formatArgs(t,a){var u=new Date;return"[NIM "+this.iid+" "+a+" "+(u.getMonth()+1+"-"+u.getDate()+" "+u.getHours()+":"+u.getMinutes()+":"+u.getSeconds()+":"+u.getMilliseconds())+"] "+map$6(t).call(t,(function(t){return t instanceof Il?t.toString():t instanceof Error?t&&t.message?t.message:t:"object"==typeof t?fc(t):t})).join(" ")},t.destroy=function destroy(){this.debug=cu,this.log=cu,this.warn=cu,this.error=cu,this.saveLogs(),this.clearTimer(),this.storageArr=[],this.logStorage&&this.logStorage.close()},Logger}(),du=function(){function CoreAdapters(t){this.lastSuccUploadHost="",this.core=t}var t=CoreAdapters.prototype;return t.getFileUploadInformation=function getFileUploadInformation(t){return Wl.getFileUploadInformation(t)},t.request=function request(t,a,u){var _=this,h=(new Date).getTime(),m=(null==u?void 0:u.exception_service)||0;return Wl.request(t,a).catch((function(u){var E,g,T,I,S=u;throw _.core.reporter.reportTraceStart("exceptions",{user_id:_.core.options.account||(null===(g=null===(E=_.core)||void 0===E?void 0:E.auth)||void 0===g?void 0:g.account),trace_id:null===(I=null===(T=_.core.clientSocket)||void 0===T?void 0:T.socket)||void 0===I?void 0:I.sessionId,start_time:h,action:1,exception_service:m}),_.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof S.code?S.code:0,description:S.message||""+S.code,operation_type:0,target:t,context:a?fc(a):""},{asyncParams:Wl.net.getNetworkStatus()}),_.core.reporter.reportTraceEnd("exceptions",1),u}))},t.uploadFile=function uploadFile(t){var a,u,_,h;return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var m,E,g,T,I,S,M,N,A,O,R,C,b,P;return Kl.wrap((function _callee$(x){for(;;)switch(x.prev=x.next){case 0:g="BROWSER"===Wl.platform,T=g?t.chunkUploadHostBackupList:t.commonUploadHostBackupList,I=g?t.chunkUploadHost:t.commonUploadHost,S=indexOf(T).call(T,I),M=-1===S?concat(m=[I]).call(m,T):concat(E=[I]).call(E,slice(T).call(T,0,S),slice(T).call(T,S+1)),N=Math.max(indexOf(M).call(M,this.lastSuccUploadHost),0),A=null,O=0;case 8:if(!(O<M.length)){x.next=32;break}return R=(new Date).getTime(),C=M[(O+N)%M.length],x.prev=11,x.next=14,Wl.uploadFile(bt(bt({},t),g?{chunkUploadHost:C}:{commonUploadHost:C}));case 14:return b=x.sent,this.lastSuccUploadHost=C,x.abrupt("return",b);case 19:if(x.prev=19,x.t0=x.catch(11),this.core.cloudStorage.nos.nosErrorCount--,A=x.t0,P=x.t0,this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(u=null===(a=this.core)||void 0===a?void 0:a.auth)||void 0===u?void 0:u.account),trace_id:null===(h=null===(_=this.core.clientSocket)||void 0===_?void 0:_.socket)||void 0===h?void 0:h.sessionId,start_time:R,action:1,exception_service:3}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof P.code?P.code:0,description:P.message||""+P.code,operation_type:1,target:C},{asyncParams:Wl.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),!x.t0||x.t0.code!==Tl.V2NIM_ERROR_CODE_CANCELLED&&10499!==x.t0.errCode){x.next=29;break}throw x.t0;case 29:O++,x.next=8;break;case 32:throw A;case 33:case"end":return x.stop()}}),_callee,this,[[11,19]])})))},CoreAdapters}(),pu="https://abt-online.netease.im/v1/api/abt/client/getExperimentInfo",fu="imElite_sdk_abtest_web",_u="https://statistic.live.126.net,https://statistic-overseas.yunxinfw.com",hu=function(){function ABTest(t,a){this.abtInfo={},this.core=t,this.config=assignOptions({isAbtestEnable:!0,abtestUrl:pu,abtestProjectKey:fu},a)}var t=ABTest.prototype;return t.setOptions=function setOptions(t){this.config=assignOptions(this.config,t)},t.abtRequest=function abtRequest(){var t,a;return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var u;return Kl.wrap((function _callee$(_){for(;;)switch(_.prev=_.next){case 0:if(this.config.isAbtestEnable){_.next=2;break}return _.abrupt("return");case 2:if(!this.abtInfo.experiments){_.next=4;break}return _.abrupt("return");case 4:if(this.config.abtestUrl){_.next=6;break}return _.abrupt("return");case 6:return _.prev=6,_.next=9,this.core.adapters.request(this.config.abtestUrl,{method:"POST",dataType:"json",headers:{sdktype:"ABTest"},data:{clientInfo:{projectKey:this.config.abtestProjectKey,appKey:this.core.options.appkey,osType:"Web",sdkVersion:"10.8.30",deviceId:this.core.config.deviceId},useLocalCache:!0}},{exception_service:7});case 9:u=_.sent,_.next=15;break;case 12:_.prev=12,_.t0=_.catch(6),this.core.logger.warn("ABTest request failed");case 15:this.abtInfo=(null===(a=null===(t=null==u?void 0:u.data)||void 0===t?void 0:t.data)||void 0===a?void 0:a.abtInfo)||{};case 16:case"end":return _.stop()}}),_callee,this,[[6,12]])})))},ABTest}(),mu=TypeError,Eu=Object.getOwnPropertyDescriptor,gu=R&&!function(){if(void 0!==this)return!0;try{Object.defineProperty([],"length",{writable:!1}).length=1}catch(t){return t instanceof TypeError}}()?function(t,a){if(ei(t)&&!Eu(t,"length").writable)throw mu("Cannot set read only .length");return t.length=a}:function(t,a){return t.length=a},vu=TypeError,deletePropertyOrThrow=function(t,a){if(!delete t[a])throw vu("Cannot delete property "+tryToString(a)+" of "+tryToString(t))},Tu=arrayMethodHasSpeciesSupport("splice"),yu=Math.max,Iu=Math.min;_export({target:"Array",proto:!0,forced:!Tu},{splice:function splice(t,a){var u,_,h,m,E,g,T=toObject(this),I=lengthOfArrayLike(T),S=toAbsoluteIndex(t,I),M=arguments.length;for(0===M?u=_=0:1===M?(u=0,_=I-S):(u=M-2,_=Iu(yu(toIntegerOrInfinity(a),0),I-S)),doesNotExceedSafeInteger(I+u-_),h=arraySpeciesCreate(T,_),m=0;m<_;m++)(E=S+m)in T&&createProperty(h,m,T[E]);if(h.length=_,u<_){for(m=S;m<I-_;m++)g=m+u,(E=m+_)in T?T[g]=T[E]:deletePropertyOrThrow(T,g);for(m=I;m>I-_+u;m--)deletePropertyOrThrow(T,m-1)}else if(u>_)for(m=I-_;m>S;m--)g=m+u-1,(E=m+_-1)in T?T[g]=T[E]:deletePropertyOrThrow(T,g);for(m=0;m<u;m++)T[m+S]=arguments[m+2];return gu(T,I-_+u),h}});var Su=entryVirtual("Array").splice,Mu=Array.prototype,splice=function(t){var a=t.splice;return t===Mu||H(Mu,t)&&a===Mu.splice?Su:a};function _createForOfIteratorHelperLoose$1(t,a){var u,_=void 0!==_a&&Bl(t)||t["@@iterator"];if(_)return bind$1(u=(_=_.call(t)).next).call(u,_);if(Dl(t)||(_=function _unsupportedIterableToArray$1(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray$1(t,a);var _=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Gl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?_arrayLikeToArray$1(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){_&&(t=_);var h=0;return function(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray$1(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=Array(a);u<a;u++)_[u]=t[u];return _}var Nu=function(){function TimerManager(){this.timerList=[],this.id=1,this.timer=null,this.timeout=0}var t=TimerManager.prototype;return t.addTimer=function addTimer(t,a,u){void 0===a&&(a=0),void 0===u&&(u=1);var _=(new Date).getTime(),h=this.id;return this.timerList.push({id:h,loop:u,count:0,timeout:_+a,interval:a,callback:t}),this.id++,this.checkTimer(_),h},t.checkTimer=function checkTimer(t){if(void 0===t&&(t=(new Date).getTime()),this.removeFinished(),0!==this.timerList.length||null==this.timer){for(var a,u,_=0,h=_createForOfIteratorHelperLoose$1(this.timerList);!(a=h()).done;){var m=a.value;(0===_||_>m.timeout)&&(_=m.timeout)}if(0!==this.timerList.length)if(null===this.timer||_<this.timeout||this.timeout<t)this.timer=hi(bind$1(u=this.nowTime).call(u,this),_-t),this.timeout=_}},t.nowTime=function nowTime(){for(var t,nowTime=(new Date).getTime(),a=_createForOfIteratorHelperLoose$1(this.timerList);!(t=a()).done;){var u=t.value;nowTime>=u.timeout&&(u.callback(),u.count++,u.timeout=nowTime+u.interval)}this.clerTime(),this.checkTimer(nowTime)},t.clerTime=function clerTime(){null!==this.timer&&(clearTimeout(this.timer),this.timer=null)},t.deleteTimer=function deleteTimer(t){for(var a=this.timerList.length-1;a>=0;a--){var u;if(this.timerList[a].id===t)splice(u=this.timerList).call(u,a,1)}},t.removeFinished=function removeFinished(){for(var t=this.timerList.length-1;t>=0;t--){var a,u=this.timerList[t];if(u.loop>=0&&u.count>=u.loop)splice(a=this.timerList).call(a,t,1)}},t.destroy=function destroy(){this.clerTime(),this.timerList=[],this.id=1,this.timer=null},TimerManager}(),Au=function(){function Service(t,a){this.name=t,this.core=a,this.name=t,this.logger=a.logger,this.core=a}return Service.prototype.process=function process(t){var a=this[t.cmd+"Handler"];if("function"==typeof a)return a.call(this,t);var u=get(t,"error.detail.ignore");return t.error&&!u?Jn.reject(t.error):Jn.resolve(t)},Service}(),Ou=Backoff;function Backoff(t){t=t||{},this.ms=t.min||100,this.max=t.max||1e4,this.factor=t.factor||2,this.jitter=t.jitter>0&&t.jitter<=1?t.jitter:0,this.attempts=0}Backoff.prototype.duration=function(){var t=this.ms*Math.pow(this.factor,this.attempts++);if(this.jitter){var a=Math.random(),u=Math.floor(a*this.jitter*t);t=0==(1&Math.floor(10*a))?t-u:t+u}return 0|Math.min(t,this.max)},Backoff.prototype.reset=function(){this.attempts=0},Backoff.prototype.setMin=function(t){this.ms=t},Backoff.prototype.setMax=function(t){this.max=t},Backoff.prototype.setJitter=function(t){this.jitter=t};var Ru,Cu=["disconnect","connect","heartbeat","message","json","event","ack","error","noop"],bu=["transport not supported","client not handshaken","unauthorized"],Pu=["reconnect"],xu=function(t){function BaseWebsocket(a,u,_){var h;return(h=t.call(this)||this).websocket=null,h.socketConnectTimer=0,h.url="",h.linkSSL=!0,h.core=a,h.url=u,h.linkSSL=_,h.status="disconnected",h.logger=a.logger,h.connect(),h}Mt(BaseWebsocket,t);var a=BaseWebsocket.prototype;return a.connect=function connect(){var t=this;"connecting"!==this.status&&"connected"!==this.status?(this.status="connecting",this.core.adapters.request((this.linkSSL?"https":"http")+"://"+this.url+"/socket.io/1/?t="+Zn(),{method:"GET",dataType:"text",timeout:this.core.options.xhrConnectTimeout||8e3},{exception_service:6}).then((function(a){if("connecting"===t.status){var u=a.data.split(":"),_=u[0];return u[1],t.sessionId=_,t.logger.log("imsocket::XHR success. status "+t.status+", "+("connecting"===t.status?"continue websocket connection":"stop websocket connection")),t._createWebsocket((t.linkSSL?"wss":"ws")+"://"+t.url+"/socket.io/1/websocket/"+_)}})).catch((function(a){if("connecting"===t.status){var u='imsocket::XHR fail. raw message: "'+(a=a||{}).message+'", code: "'+a.code+'"',_=a.code;_="v2"===get(t.core,"options.apiVersion")?a.code===Tl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT?Tl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:Tl.V2NIM_ERROR_CODE_CONNECT_FAILED:408===a.code?408:415;var h=new Il({code:_,detail:{reason:u,rawError:a}});t.logger.error(u),t.status="disconnected",t.emit("handshakeFailed",h)}}))):this.logger.warn("imsocket::socket is connecting or connected",this.status)},a.close=function close(){if(this.status="disconnected",this.websocket){this.logger.log("imsocket:: close websocket");try{this.websocket.send(this.encodePacket({type:"disconnect"}))}catch(t){this.logger.warn("imsocket::attempt to send encodePacket error",t)}try{this.websocket.close()}catch(t){this.logger.warn("imsocket::attempt to close websocket error",t)}this.clean(),this.emit("disconnect",{code:0,reason:"Active close websocket"})}},a.clean=function clean(){this.status="disconnected",clearTimeout(this.socketConnectTimer),this.websocket&&(this.socketUrl=void 0,this.websocket.onmessage=null,this.websocket.onopen=null,this.websocket.onerror=null,this.websocket.onclose=null,this.websocket=null)},a.onConnect=function onConnect(){this.status="connected",this.emit("connect"),clearTimeout(this.socketConnectTimer)},a._createWebsocket=function _createWebsocket(t){var a,u=this;this.socketConnectTimer=hi((function(){u.logger.error("imsocket::Websocket connect timeout. url: ",u.socketUrl),u.emit("handshakeFailed",new Il({code:"v2"===get(u.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_CONNECT_TIMEOUT:415,detail:{reason:"imsocket::Websocket connect timeout. url: "+u.socketUrl}}))}),this.core.options.socketConnectTimeout||8e3),this.socketUrl=t,this.websocket=new Wl.WebSocket(t),this.websocket.onmessage=bind$1(a=this.onMessage).call(a,this),this.websocket.onclose=function(t){t=t||{},u.logger.log("imsocket::Websocket onclose done "+t.wasClean+"/"+t.code+"/"+t.reason),u.clean(),u.emit("disconnect",{code:t.code||0,reason:t.reason})},this.websocket.onerror=function(t){u.logger.error("imsocket::Websocket onerror",t),"logined"===u.core.status&&u.core.clientSocket.ping()}},a.onMessage=function onMessage(t){var a,u=this.decodePacket(t.data);if(u)switch(u.type){case"connect":this.onConnect();break;case"disconnect":this.close(),this.emit("disconnect",{code:0,reason:"MessageEvent type disconnect"});break;case"message":case"json":this.emit("message",u.data);break;case"event":u.name&&this.emit(u.name,u.args);break;case"error":"unauthorized"===u.reason?this.emit("connect_failed",u.reason):this.emit("error",u.reason),this.logger.error("imsocket::Websocket connect failed, onmessage type error. url: ",this.socketUrl),clearTimeout(this.socketConnectTimer),this.emit("handshakeFailed",new Il({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_CONNECT_FAILED:408,detail:{reason:"imsocket::Websocket connect failed, onMessage socket error. url: "+this.socketUrl}}));break;case"heartbeat":null===(a=this.websocket)||void 0===a||a.send(this.encodePacket({type:"heartbeat"}));break;default:this.logger.warn("imsocket::Websocket no handler type",u.type)}},a.encodePacket=function encodePacket(t){var a,u,_=t.type,h=t.id,m=void 0===h?"":h,E=t.endpoint,g=void 0===E?"":E,T=t.ack,I=null;if(!_)return"";switch(_){case"error":a=t.reason?indexOf(bu).call(bu,t.reason):"",u=t.advice?indexOf(Pu).call(Pu,t.advice):"",""===a&&""===u||(I=a+(""!==u?"+"+u:""));break;case"message":""!==t.data&&(I=t.data);break;case"event":a={name:t.name},a=t.args&&t.args.length?{name:t.name,args:t.args}:{name:t.name},I=fc(a);break;case"json":I=fc(t.data);break;case"connect":t.qs&&(I=t.qs);break;case"ack":I=t.ackId+(t.args&&t.args.length?"+"+fc(t.args):"")}var S=[indexOf(Cu).call(Cu,_),m+("data"===T?"+":""),g];return null!=I&&S.push(I),S.join(":")},a.decodePacket=function decodePacket(t){if(t)if("�"!=t.charAt(0)){var a=t.match(/([^:]+):([0-9]+)?(\+)?:([^:]+)?:?([\s\S]*)?/);if(a){var u,_=a[1],h=a[2],m=a[3],E=a[4],g=a[5],T={type:Cu[+_],endpoint:E};switch(h&&(T.id=h,T.ack=!m||"data"),T.type){case"error":u=g.split("+"),T.reason=bu[+u[0]]||"";break;case"message":T.data=g||"";break;case"connect":T.qs=g||"";break;case"event":try{var I=JSON.parse(g);T.name=I.name,T.args=I.args}catch(t){this.logger.error("imsocket::parseData::type::event error",t)}T.args=T.args||[];break;case"json":try{T.data=JSON.parse(g)}catch(t){this.logger.error("imsocket::parseData::type::json error",t)}break;case"ack":if((u=g.match(/^([0-9]+)(\+)?(.*)/))&&(T.ackId=u[1],T.args=[],u[3]))try{T.args=u[3]?JSON.parse(u[3]):[]}catch(t){this.logger.error("imsocket::parseData::type::ack error",t)}}return T}}else this.logger.error("imsocket::unrecognize dataStr",slice(t).call(t,0,20))},a.send=function send(t){var a,u={data:t,type:"message",endpoint:""};null===(a=this.websocket)||void 0===a||a.send(this.encodePacket(u))},BaseWebsocket}(mi);function uniq(t){t=t||[];for(var a=[],u=0;u<t.length;u++)-1===indexOf(a).call(a,t[u])&&a.push(t[u]);return a}function _createForOfIteratorHelperLoose(t,a){var u,_=void 0!==_a&&Bl(t)||t["@@iterator"];if(_)return bind$1(u=(_=_.call(t)).next).call(u,_);if(Dl(t)||(_=function _unsupportedIterableToArray(t,a){if(t){var u;if("string"==typeof t)return _arrayLikeToArray(t,a);var _=slice(u={}.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Gl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?_arrayLikeToArray(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){_&&(t=_);var h=0;return function(){return h>=t.length?{done:!0}:{done:!1,value:t[h++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _arrayLikeToArray(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=Array(a);u<a;u++)_[u]=t[u];return _}!function(t){t[t.ACTIVE=1]="ACTIVE",t[t.KICKED=2]="KICKED",t[t.OFFLINE=3]="OFFLINE"}(Ru||(Ru={}));var ku,wu,Du=function(){function V1ClientSocket(t,a){this.linkUrls=[],this.isAutoReconnect=!1,this.packetTimeout=3e4,this.linkSSL=!0,this.packetSer=1,this.retryCount=0,this.reconnectTimer=0,this.backoff=new Ou({max:8e3,min:1600,jitter:.01}),this.sendingCmdMap=new Xs,this.pingTimer=0,this.hasNetworkListener=!1,this.core=t,a&&(this.auth=a),this.logger=t.logger,this.reporter=t.reporter,this.timerManager=t.timerManager}var t=V1ClientSocket.prototype;return t.setSessionId=function setSessionId(t){},t.setLinkSSL=function setLinkSSL(t){this.linkSSL=t},t.connect=function connect(t,a){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,Kl.mark((function _callee(){var u,_,h,m,E,g;return Kl.wrap((function _callee$(T){for(;;)switch(T.prev=T.next){case 0:if(validate({linkUrls:{type:"array",itemType:"string",required:!1}},t),/^(unconnected|waitReconnect)$/.test(this.core.status)){T.next=5;break}return u="Core socket status is "+this.core.status+", and would not connect",this.logger.warn(u),T.abrupt("return",Jn.reject(u));case 5:this.core.status="connecting",t.linkUrls&&t.linkUrls.length>0&&(this.linkUrls=concat(_=t.linkUrls).call(_,this.linkUrls),this.linkUrls=uniq(this.linkUrls)),0===this.linkUrls.length&&this.linkUrls.push("weblink.netease.im:443"),h=0;case 9:if(!(h<this.linkUrls.length)){T.next=31;break}return m=this.linkUrls[h],E=(new Date).getTime(),T.prev=12,T.next=15,this.doConnect(m);case 15:return this.core.status="connected",this.logger.log("clientsocketV1::connect success with url: "+m),T.abrupt("return",m);case 20:T.prev=20,T.t0=T.catch(12),g=T.t0,a&&a(g,m),this.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account,start_time:E,action:0,exception_service:6}),this.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof g.code?g.code:0,description:g.message||""+g.code,operation_type:0,target:m},{asyncParams:Wl.net.getNetworkStatus()}),this.reporter.reportTraceEnd("exceptions",1),this.logger.warn("clientsocketV1::connect failed with url: "+m,T.t0);case 28:h++,T.next=9;break;case 31:throw 0===this.retryCount?this.doDisconnect(Ru.ACTIVE,"SocketHandshakeFailed"):this.doDisconnect(Ru.OFFLINE,"ReconnectHadRetryAllLinks"),new Error("clientSocketV1::socket xhr or socket connect failed");case 33:case"end":return T.stop()}}),_callee,this,[[12,20]])})))},t.doConnect=function doConnect(t){var a=this,u=!1;return new Jn((function(_,h){var m;a.socket=new xu(a.core,t,a.linkSSL),a.socket.on("connect",(function(){a.logger.log("clientSocketV1::on connect",t),a.core.reporterHookLinkKeep&&(a.core.reporterHookLinkKeep.start(),a.core.reporterHookLinkKeep.update({code:0,description:"connection begin",operation_type:0,target:t})),u=!0,_()})),a.socket.on("message",bind$1(m=a.onMessage).call(m,a)),a.socket.on("disconnect",(function(_){return __awaiter(a,void 0,void 0,Kl.mark((function _callee2(){return Kl.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:if(this.logger.log("clientSocketV1::socket on disconnect",_),!this.core.reporterHookLinkKeep){a.next=5;break}return a.next=4,this.core.reporterHookLinkKeep.update({code:(null==_?void 0:_.code)||0,description:(null==_?void 0:_.reason)||"socket on disconnect",operation_type:1,target:t});case 4:this.core.reporterHookLinkKeep.end(!1);case 5:u=!0,this.doDisconnect(Ru.OFFLINE,"SocketOnDisconnect");case 7:case"end":return a.stop()}}),_callee2,this)})))})),a.socket.on("handshakeFailed",(function(t){u?a.ping():(a.logger.error('clientsocketV1::handshake failed: "'+(t&&t.message)+'"'),a.cleanSocket()),u=!0,h(t)}))}))},t.cleanSocket=function cleanSocket(){this.socket&&("function"==typeof this.socket.removeAllListeners&&this.socket.removeAllListeners(),"function"==typeof this.socket.close&&this.socket.close(),this.socket=void 0)},t.beforeConnect=function beforeConnect(){this.reconnectTimer&&clearTimeout(this.reconnectTimer)},t.resetConnectStatus=function resetConnectStatus(){clearTimeout(this.reconnectTimer),this.backoff.reset(),this.retryCount=0,this.initOnlineListener()},t.doDisconnect=function doDisconnect(t,a,u){var _,h,m,E,g;if(this.logger.log("doDisconnect: type "+t+", description "+a),"unconnected"!==this.core.status){var T={1:"close",2:"kicked",3:"broken"}[t]||"";this.markAllCmdInvaild(new Il({code:415,desc:"Packet timeout due to instance disconnect",detail:{reason:"Packet timeout due to instance disconnect",disconnect_reason:T}})),this.timerManager.destroy(),clearTimeout(this.pingTimer),this.cleanSocket();var I=!this.core.options.needReconnect||this.retryCount>=this.core.options.reconnectionAttempts;if(t===Ru.ACTIVE||I)this.logger.log("doDisconnect: emit disconnect, type "+t,I),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(_=this.auth)||void 0===_||_.emit("disconnect"),this.destroyOnlineListener();else if(t===Ru.KICKED){this.logger.log("doDisconnect: kicked"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer);var S="string"==typeof a?{reason:"unknow",message:a}:a;this.core.eventBus.emit("kicked",S),this.core.emit("kicked",S),null===(h=this.auth)||void 0===h||h.emit("kicked",S),this.destroyOnlineListener()}else t===Ru.OFFLINE&&this.core.V1NIMLoginService.isManualLoginAttempt?(this.logger.log("doDisconnect: offline in manual login phase. no reconnect"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.destroyOnlineListener()):t===Ru.OFFLINE&&(null===(E=null===(m=this.auth)||void 0===m?void 0:m.authenticator)||void 0===E?void 0:E.checkLoginTerminalCode(null==u?void 0:u.code))?(this.logger.log("doDisconnect: login terminal code "+(null==u?void 0:u.code)+", no reconnect"),this.core.status="unconnected",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.destroyOnlineListener(),this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(g=this.auth)||void 0===g||g.emit("disconnect")):t===Ru.OFFLINE?(this.logger.log("doDisconnect: start to reconnect"),this.attempToReconnect()):this.logger.log("doDisconnect: nothing to do")}else this.logger.warn("doDisconnect: already unconnected")},t.attempToReconnect=function attempToReconnect(){var t,a,u=this;if("waitReconnect"!==this.core.status){0===this.retryCount&&(this.core.eventBus.emit("disconnect"),this.core.emit("disconnect"),null===(t=this.auth)||void 0===t||t.emit("disconnect"));var _=this.backoff.duration();this.retryCount++,this.logger.log("willReconnect "+this.retryCount+" "+_),this.core.eventBus.emit("willReconnect",{retryCount:this.retryCount,duration:_}),this.core.emit("willReconnect",{retryCount:this.retryCount,duration:_}),null===(a=this.auth)||void 0===a||a.emit("willReconnect",{retryCount:this.retryCount,duration:_}),this.core.status="waitReconnect",this.reconnectTimer&&clearTimeout(this.reconnectTimer),this.reconnectTimer=hi((function(){return __awaiter(u,void 0,void 0,Kl.mark((function _callee3(){var t=this;return Kl.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:if("waitReconnect"===this.core.status){a.next=3;break}return this.logger.warn("doDisconnect: reconnectTimer status is "+this.core.status+", would not go on reconnecting"),a.abrupt("return");case 3:return a.next=5,Wl.net.getNetworkStatus();case 5:!1===a.sent.net_connect?(this.logger.log("doDisconnect: skip this reconnection attempt because network is offline"),this.core.status="connecting",this.retryCount>=this.core.options.reconnectionAttempts?this.doDisconnect(Ru.OFFLINE,"MaxReconnectionAttemptExceed"):this.attempToReconnect()):this.core.V1NIMLoginService.login({isAutoReconnect:!0}).catch((function(){t.logger.error("clientsocketV1::attempToReconnect failed "+t.retryCount)}));case 7:case"end":return a.stop()}}),_callee3,this)})))}),_)}else this.logger.warn("doDisconnect: already is waiting reconnect")},t.sendCmd=function sendCmd(t,a,u){var _=this;if("logined"!==this.core.status&&"login"!==t&&"chatroomLogin"!==t&&"qchatLogin"!==t)return this.logger.warn("instance status is "+this.core.status+", so can not sendCmd "+t),Jn.reject({cmd:t,error:{code:"No_connected",message:"Connection not established",timetag:(new Date).getTime()}});if(!this.socket||!this.socket.send)return Jn.reject("No_socket");var h="heartbeat"!==t,m=h?this.packetSer++:0,E=function createCmd(t,a,u,_){var h=Ql[t];if(!h)return u.error("createCmd:: can not find cmd config: ",t),null;var m,E={SER:a,SID:h.sid,CID:h.cid,Q:[]};return h.params&&_&&forEach$1(m=h.params).call(m,(function(t){var a=_[t.name];if(null!=a){var u=t.type,h=t.reflectMapper,m=t.select;switch(t.type){case"PropertyArray":u="ArrayMable",a=map$6(a).call(a,(function(t){return{t:"Property",v:h?serialize(t,h,m):t}}));break;case"Property":a=h?serialize(a,h,m):a;break;case"Bool":a=a?"true":"false"}E.Q.push({t:u,v:a})}})),{packet:E,hasPacketResponse:"boolean"!=typeof h.hasPacketResponse||h.hasPacketResponse,hasPacketTimer:"boolean"!=typeof h.hasPacketTimer||h.hasPacketTimer}}(t,m,this.logger,a);if(!E){var g="SendCmd "+m+" "+t+" error";return this.logger.error(g),Jn.reject(new Error(g))}var T=E.packet,I=E.hasPacketResponse,S=E.hasPacketTimer,M=fc(T);h&&(this.logger.getDebugMode()?this.logger.debug("clientsocketV1::sendCmd",t,"ser:"+m,M):this.logger.log("clientsocketV1::sendCmd",t,"ser:"+m));var N=(new Date).getTime();return new Jn((function(h,E){I&&_.sendingCmdMap.set(m,{cmd:t,params:a,callback:[h,E],timer:S?hi((function(){var a=new Il({code:408,desc:"Packet Timeout",detail:{reason:"Packet Timeout",cmd:t,ser:m,timetag:Zn()}});_.markCmdInvalid(m,a,t)}),u&&u.timeout?u.timeout:_.core.config.timeout):null});try{_.socket.send(M),I||h(T)}catch(a){var g=new Il({code:415,detail:{reason:a&&a.message||"Unable to send packet",cmd:t,ser:m,timetag:Zn(),rawError:a}});_.markCmdInvalid(m,g,t),E(a)}})).catch((function(t){var a,u=[408,415];if(!includes(u).call(u,t.code))return Jn.reject(t);_.reporter.reportTraceStart("exceptions",{user_id:_.core.options.account,trace_id:null===(a=_.socket)||void 0===a?void 0:a.sessionId,start_time:N,action:2,exception_service:6});var h=get(t,"data.disconnect_reason")||"",m=408===t.code?"Send failed due to timeout":"Send failed. Reason unknown";return m=415===t.code?fc({disconnect_reason:h}):m,_.reporter.reportTraceUpdateV2("exceptions",{code:t.code||415,description:m,operation_type:1,target:T.SID+"-"+T.CID,context:""+T.SER},{asyncParams:Wl.net.getNetworkStatus()}),_.reporter.reportTraceEnd("exceptions",1),Jn.reject(t)}))},t.onMessage=function onMessage(t){var a=function parseCmd(t,a){var u,_;try{_=JSON.parse(t)}catch(u){return void a.error('Parse command error:"'+t+'"')}var h=_.sid+"_"+_.cid,m=_.r;if(includes(u=["4_1","4_2","4_10","4_11"]).call(u,h)){var E=_.r[1].headerPacket;h=E.sid+"_"+E.cid,_.sid=E.sid,_.cid=E.cid,m=_.r[1].body}var g=Xl[h],T=[];if(g){for(var I,S=_createForOfIteratorHelperLoose$2(g);!(I=S()).done;){var M=I.value;T.push(parseEachCmd(_,M.config,M.cmd,m,a))}return T}a.error("parseCmd:: mapper not exist",h,_.code)}(t,this.logger);if(a)for(var u,_=_createForOfIteratorHelperLoose(a);!(u=_()).done;){var h=u.value,m=h.raw.ser;if(h.error&&this.logger.error("core:onMessage packet error",h.raw.sid+"_"+h.raw.cid+", ser:"+m+",",h.error),h.notFound)return void this.logger.warn("clientsocketV1::onMessage packet not found",h.raw.sid+"_"+h.raw.cid+", ser:"+m);"heartbeat"!==h.cmd&&(this.logger.getDebugMode()?this.logger.debug("imsocket::recvCmd ser:"+m,h.cmd,h.content):this.logger.log("imsocket::recvCmd ser:"+m,h.cmd)),this.packetHandler(h)}},t.packetHandler=function packetHandler(t){var a,u,_,h,m=this;if(t){var E=t.raw.ser,g=this.sendingCmdMap.get(E);if(g&&g.cmd===t.cmd){var T=g.callback,I=g.timer,S=g.params;if(clearTimeout(I),t.params=S,this.sendingCmdMap.delete(E),"heartbeat"===t.cmd)return void T[0]();var M=null===(u=null===(a=this.core[t.service])||void 0===a?void 0:a.process)||void 0===u?void 0:u.call(a,t);M&&"function"==typeof M.then?M.then((function(t){T[0](t)})).catch((function(t){T[1](t)})):(this.logger.log("imsocket:: handlerFn without promise",t.service,t.cmd),T[0]())}else{var N=null===(h=null===(_=this.core[t.service])||void 0===_?void 0:_.process)||void 0===h?void 0:h.call(_,t);N&&"function"==typeof N.then&&N.catch((function(t){m.logger.error("imsocket::no obj cache, no process handler",t)}))}}},t.markCmdInvalid=function markCmdInvalid(t,a,u){var _=this.sendingCmdMap.get(t);if(_){var h=_.callback,m=_.timer;m&&clearTimeout(m),this.sendingCmdMap.delete(t),this.logger.warn("packet "+t+", "+u+" is invalid:",a),h[1](a)}},t.markAllCmdInvaild=function markAllCmdInvaild(t){var a,u=this;this.logger.log("markAllCmdInvaild",t),forEach$1(a=this.sendingCmdMap).call(a,(function(a){var _=a.callback,h=a.timer,m=a.cmd;u.logger.log('markAllCmdInvaild:: cmd "'+m+'"'),h&&clearTimeout(h),_[1](t)})),this.sendingCmdMap.clear()},t.ping=function ping(){var t;return __awaiter(this,void 0,void 0,Kl.mark((function _callee4(){var a=this;return Kl.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return clearTimeout(this.pingTimer),u.prev=1,u.next=4,this.sendCmd("heartbeat");case 4:u.next=18;break;case 6:return u.prev=6,u.t0=u.catch(1),u.next=10,this.testHeartBeat5Timeout();case 10:if(!u.sent){u.next=18;break}if(!this.core.reporterHookLinkKeep){u.next=16;break}return u.next=15,this.core.reporterHookLinkKeep.update({code:0,description:"Heartbeat-discovered link failure",operation_type:1,target:null===(t=this.socket)||void 0===t?void 0:t.url});case 15:this.core.reporterHookLinkKeep.end(!0);case 16:return this.doDisconnect(Ru.OFFLINE,"PingError"),u.abrupt("return");case 18:this.pingTimer=hi((function(){a.ping()}),3e4);case 19:case"end":return u.stop()}}),_callee4,this,[[1,6]])})))},t.testHeartBeat5Timeout=function testHeartBeat5Timeout(){return __awaiter(this,void 0,void 0,Kl.mark((function _callee5(){var t;return Kl.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:clearTimeout(this.pingTimer),t=0;case 2:if(!(t<5)){a.next=15;break}return a.prev=3,a.next=6,this.sendCmd("heartbeat",{},{timeout:3e3});case 6:return a.abrupt("return",!1);case 9:a.prev=9,a.t0=a.catch(3),this.logger.log("clientsocketV1:: test heartbeat "+t+" Timeout");case 12:t++,a.next=2;break;case 15:return a.abrupt("return",!0);case 16:case"end":return a.stop()}}),_callee5,this,[[3,9]])})))},t.initOnlineListener=function initOnlineListener(){var t=this;this.hasNetworkListener||(this.logger.log("clientsocketV1::onlineListener:init"),this.hasNetworkListener=!0,Wl.net.onNetworkStatusChange((function(a){t.logger.log("clientsocketV1::onlineListener:network change",a),a.isConnected&&"logined"===t.core.status?t.ping():a.isConnected&&"waitReconnect"===t.core.status?(t.reconnectTimer&&clearTimeout(t.reconnectTimer),t.core.V1NIMLoginService.login({isAutoReconnect:!0}).catch((function(){t.logger.error("clientsocketV1::attempToReconnect failed "+t.retryCount)}))):a.isConnected||t.doDisconnect(Ru.OFFLINE,"OfflineListener")})))},t.destroyOnlineListener=function destroyOnlineListener(){this.logger.log("clientsocketV1::onlineListener:destroy"),Wl.net.offNetworkStatusChange(),this.hasNetworkListener=!1},t.disconnect=function disconnect(){switch(this.core.status){case"connected":case"logined":case"connecting":case"waitReconnect":return this.doDisconnect(Ru.ACTIVE,"UserActiveDisconnect"),Jn.resolve();default:return Jn.resolve()}},V1ClientSocket}();function invert(t){t=t||{};var a={};for(var u in t)a[t[u]]=u;return a}function formatChatroom(t){var a=bt({},t),u=["announcement","broadcastUrl","ext"],_=["createTime","updateTime","onlineMemberNum"],h=["mute"];return forEach$1(u).call(u,(function(t){void 0===a[t]&&(a[t]="")})),forEach$1(_).call(_,(function(t){void 0!==a[t]&&(a[t]=wl(a[t]))})),forEach$1(h).call(h,(function(t){void 0!==a[t]&&(a[t]=1===wl(a[t]))})),a}!function(t){t[t.Android=1]="Android",t[t.iOS=2]="iOS",t[t.PC=4]="PC",t[t.WindowsPhone=8]="WindowsPhone",t[t.Web=16]="Web",t[t.Server=32]="Server",t[t.Mac=64]="Mac",t[t.HarmonyOS=65]="HarmonyOS"}(ku||(ku={})),function(t){t[t.text=0]="text",t[t.image=1]="image",t[t.audio=2]="audio",t[t.video=3]="video",t[t.geo=4]="geo",t[t.notification=5]="notification",t[t.file=6]="file",t[t.tip=10]="tip",t[t.robot=11]="robot",t[t.g2=12]="g2",t[t.custom=100]="custom"}(wu||(wu={}));var Lu=invert({unset:"-2",restricted:"-1",common:"0",owner:"1",manager:"2",guest:"3",anonymous:"4"});function formatChatroomMember(t){var a,u=bt({},t),_=["chatroomId"],h=["level","enterTime","updateTime","tempMuteDuration"],m=["online","guest","blacked","muted","valid","tempMuted"],E={tempMuted:!1,tempMuteDuration:0};return forEach$1(a=qa(E)).call(a,(function(t){u[t]=u[t]||E[t]})),forEach$1(_).call(_,(function(t){void 0!==u[t]&&(u[t]=u[t].toString())})),forEach$1(h).call(h,(function(t){void 0!==u[t]&&(u[t]=wl(u[t]))})),forEach$1(m).call(m,(function(t){void 0!==u[t]&&(u[t]=1===wl(u[t]))})),void 0!==u.type&&(u.type=Lu[u.type]),u.avatar,u.online||delete u.enterTime,u.guest&&(u.type="guest",delete u.valid,delete u.guest),"common"!==u.type&&delete u.level,u}function formatChatroomMembers(t){return t&&t.length>0?map$6(t).call(t,(function(t){return formatChatroomMember(t)})):[]}function generatorMsgForCmd(t,a,u){t.onSendBefore;var _=__rest(t,["onSendBefore"]),h=["subType"],m=["body","antiSpamContent","antiSpamBusinessId","ext"],E=["resend","needAntiSpam","antiSpamUsingYidun","skipHistory","highPriority","clientAntiSpam"];return t.resend||(_.idClient=Vl()),_.type=wu[t.type],_.from=a,_.fromClientType=16,_.fromDeviceId=u,_.status=_.status||"sending",forEach$1(E).call(E,(function(a){void 0!==t[a]&&(_[a]=t[a]?1:0)})),forEach$1(h).call(h,(function(a){void 0!==t[a]&&(_[a]=wl(t[a]))})),forEach$1(m).call(m,(function(a){void 0!==t[a]&&(_[a]="string"==typeof t[a]?t[a]:fc(t[a]))})),_}var Vu={301:"memberEnter",302:"memberExit",303:"blackMember",304:"unblackMember",305:"gagMember",306:"ungagMember",307:"addManager",308:"removeManager",309:"addCommon",310:"removeCommon",311:"closeChatroom",312:"updateChatroom",313:"kickMember",314:"addTempMute",315:"removeTempMute",316:"updateMemberInfo",317:"updateQueue",318:"muteRoom",319:"unmuteRoom",320:"batchUpdateQueue",321:"addTempMuteTag",322:"removeTempMuteTag",323:"deleteChatroomMsg",325:"updateChatroomTags"};function formatChatroomMsg(t,a){t.onUploadDone,t.onUploadProgress,t.onUploadStart;var u=__rest(t,["onUploadDone","onUploadProgress","onUploadStart"]),_=["time","userUpdateTime"],h=["resend","needAntiSpam","antiSpamUsingYidun","skipHistory","highPriority","clientAntiSpam"];if(forEach$1(_).call(_,(function(t){void 0!==u[t]&&(u[t]=wl(u[t]))})),forEach$1(h).call(h,(function(t){void 0!==u[t]&&(u[t]=1===wl(u[t]))})),u.type=getEnumKeyByEnumValue(wu,u.type),u.fromClientType=getEnumKeyByEnumValue(ku,u.fromClientType),u.flow=u.from===a?"out":"in",u.status=u.status||"success","string"==typeof u.body)try{u.body=JSON.parse(u.body)}catch(t){}return"notification"===u.type&&(u.attach=u.body?function formatNotificationBody(t){var a,u={};if(u.type=Vu[t.id]||t.id,!t.data)return t;u=bt(u,t.data);var _={operator:"from",opeNick:"fromNick",target:"to",tarNick:"toNick"};forEach$1(a=qa(_)).call(a,(function(t){void 0!==u[t]&&(u[_[t]]=u[t],delete u[t])}));var h=["muteDuration"];if(forEach$1(h).call(h,(function(t){void 0!==u[t]&&(u[t]=wl(u[t]))})),!u.queueChange)return u;var m={};try{m=JSON.parse(u.queueChange)}catch(t){return u}switch(m._e){case"OFFER":m={type:"OFFER",elementKey:m.key,elementValue:m.content};break;case"POLL":m={type:"POLL",elementKey:m.key,elementValue:m.content};break;case"DROP":m={type:"DROP"};break;case"PARTCLEAR":case"BATCH_UPDATE":m={type:m._e,elementKv:m.kvObject}}return u.queueChange=m,u}(u.body):{},u.body=""),u}function formatChatroomMsgs(t,a){return t&&t.length>0?map$6(t).call(t,(function(t){return formatChatroomMsg(t,a)})):[]}var Uu={1:{code:"chatroomClosed",message:"Chatroom is closed"},2:{code:"managerKick",message:"Kicked out by owners or administrators"},3:{code:"samePlatformKick",message:"The same account is not allowed to multiple login at the same time"},4:{code:"silentlyKick",message:"Quietly kicked"},5:{code:"blacked",message:"Was blacklisted"}};var Fu=function(){function V1AuthAuthenticatorService(t){this.core=t}return V1AuthAuthenticatorService.prototype.verifyAuthentication=function verifyAuthentication(t){return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,Kl.mark((function _callee(){var a,u,_,h,m,E,g,T;return Kl.wrap((function _callee$(I){for(;;)switch(I.prev=I.next){case 0:return u=this.core.options,_=Wl.getSystemInfo(),h=bt(bt({},u),{appLogin:t?0:1,deviceId:this.core.config.deviceId,clientSession:this.core.config.clientSession,clientType:16,protocolVersion:1,sdkVersion:100830,sdkHumanVersion:"10.8.30",os:_.os,browser:_.browser,userAgent:this.core.options.loginSDKTypeParamCompat?"Native/10.8.30":slice(a=_.userAgent.replace("{{appkey}}",u.appkey)).call(a,0,299),libEnv:this.core.options.loginSDKTypeParamCompat?void 0:_.libEnv,hostEnv:this.core.options.loginSDKTypeParamCompat?0:_.hostEnvEnum}),m=bt(bt({},u),{appkey:u.appkey,account:u.account,deviceId:this.core.config.deviceId,clientSession:this.core.config.clientSession,appLogin:1}),u.isAnonymous&&(m.isAnonymous=1,m.account=m.account||"nimanon_"+Vl(),h.account=m.account,m.chatroomNick=m.account||"nimanon_"+Vl(),m.chatroomAvatar=m.chatroomAvatar||" "),u.tags&&u.tags.length>0&&(m.tags=fc(u.tags)),I.next=8,this.core.clientSocket.sendCmd("chatroomLogin",{type:1,chatroomLogin:m,chatroomIMLogin:h});case 8:return E=I.sent,this.core.status="logined",g=formatChatroom(E.content.chatroom),T=formatChatroomMember(E.content.chatroomMember),I.abrupt("return",{chatroom:g,member:T});case 13:case"end":return I.stop()}}),_callee,this)})))},V1AuthAuthenticatorService}(),Gu=function(t){function ChatroomAuthService(a){var u;return(u=t.call(this,"chatroomAuth",a)||this).account="",u.token="",u.deviceId="",u.isManualLoginAttempt=!1,u.authenticatorService=new Fu(a),u}Mt(ChatroomAuthService,t);var a=ChatroomAuthService.prototype;return a.login=function login(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,Kl.mark((function _callee(){var a,u;return Kl.wrap((function _callee$(_){for(;;)switch(_.prev=_.next){case 0:return t.isAutoReconnect||(this.isManualLoginAttempt=!0),a=Zn(),_.next=4,this._connect(t);case 4:return u=_.sent,this.core.abtest.abtRequest(),_.prev=6,_.next=9,this.doLogin(t.isAutoReconnect,u,a);case 9:this.isManualLoginAttempt=!1,_.next=16;break;case 12:throw _.prev=12,_.t0=_.catch(6),this.isManualLoginAttempt=!1,_.t0;case 16:case"end":return _.stop()}}),_callee,this,[[6,12]])})))},a._connect=function _connect(t){return void 0===t&&(t={}),__awaiter(this,void 0,void 0,Kl.mark((function _callee2(){var a,u,_;return Kl.wrap((function _callee2$(h){for(;;)switch(h.prev=h.next){case 0:if(/^(unconnected|waitReconnect)$/.test(this.core.status)){h.next=4;break}return a="Chatroom status is "+this.core.status+", and would not connect",this.logger.warn(a),h.abrupt("return",Jn.reject(a));case 4:return this.core.clientSocket.beforeConnect(),u=t.isAutoReconnect||!1,h.next=8,this.core.clientSocket.connect({linkUrls:this.core.options.chatroomAddresses,isAutoReconnect:u});case 8:return _=h.sent,h.abrupt("return",_);case 10:case"end":return h.stop()}}),_callee2,this)})))},a.doLogin=function doLogin(t,a,u){var _;return void 0===t&&(t=!1),__awaiter(this,void 0,void 0,Kl.mark((function _callee3(){var h,m,E,g,T,I,S;return Kl.wrap((function _callee3$(M){for(;;)switch(M.prev=M.next){case 0:return M.prev=0,M.next=3,this.authenticatorService.verifyAuthentication(t);case 3:return h=M.sent,M.next=6,Wl.net.getNetworkStatus();case 6:m=M.sent,E=m.net_connect,this.core.status="logined",this.core.reporter.report("chatroomLogin",{accid:this.core.account,roomId:this.core.options.chatroomId,serverIps:this.core.options.chatroomAddresses,currentServerIp:a,rt:Zn()-u,result:200,failReason:"",time:Zn(),net_connect:E}),M.next=26;break;case 12:return M.prev=12,M.t0=M.catch(0),this.logger.error("chatroom login error",M.t0),g=get(M.t0,"error.code")||get(M.t0,"code")||408,T=get(M.t0,"error.message")||get(M.t0,"message")||"login failed",M.next=19,Wl.net.getNetworkStatus();case 19:if(I=M.sent,S=I.net_connect,this.core.reporter.report("chatroomLogin",{accid:this.core.account,roomId:this.core.options.chatroomId,serverIps:this.core.options.chatroomAddresses,currentServerIp:a,rt:Zn()-u,result:g,failReason:T,time:Zn(),net_connect:S}),this.core.clientSocket.doDisconnect(Ru.OFFLINE,this.isManualLoginAttempt?"FailedToInitializeLogin":"ReconnectLoginFailed"),!this.isManualLoginAttempt){M.next=25;break}throw M.t0;case 25:return M.abrupt("return");case 26:return M.prev=26,M.next=29,this.core.cloudStorage.init(null===(_=h.member)||void 0===_?void 0:_.enterTime);case 29:M.next=34;break;case 31:M.prev=31,M.t1=M.catch(26),this.logger.error("NIM:login cloudStorage init failed ",M.t1);case 34:this.core.eventBus.emit("logined",h),this.core.emit("logined",h),this.core.clientSocket.resetConnectStatus(),this.core.clientSocket.ping(),this.core.timeOrigin.setOriginTimetick();case 39:case"end":return M.stop()}}),_callee3,this,[[0,12],[26,31]])})))},a.beKickedFromChatroomHandler=function beKickedFromChatroomHandler(t){var a=t.content,u=a.reason,_=a.ext,h=function formatKickedReason(t){var a=Uu[t];return{reason:a?a.code:"unknow",message:a?a.message:"Unknown reason"}}(u);this.logger.warn("beKickedFromChatroomHandler:: ",h.reason,h.message,_),this.core.clientSocket.doDisconnect(Ru.KICKED,h)},ChatroomAuthService}(Au),Bu=function(t){function ChatroomService(a){return t.call(this,"chatroom",a)||this}Mt(ChatroomService,t);var a=ChatroomService.prototype;return a.getInfo=function getInfo(){var t;return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var a;return Kl.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,this.core.sendCmd("getChatroom");case 2:return a=u.sent,u.abrupt("return",formatChatroom(null===(t=a.content)||void 0===t?void 0:t.chatroom));case 4:case"end":return u.stop()}}),_callee,this)})))},a.updateInfo=function updateInfo(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){return Kl.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return validate({chatroom:{type:"object",rules:{name:{type:"string",required:!1},announcement:{type:"string",required:!1},broadcastUrl:{type:"string",required:!1},ext:{type:"string",required:!1},queuelevel:{type:"number",min:0,max:1,required:!1}}},needNotify:{type:"boolean"},ext:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("updateChatroom",bt(bt({},t),{ext:t.ext||""}));case 3:case"end":return a.stop()}}),_callee2,this)})))},a.updateChatroomTagHandler=function updateChatroomTagHandler(t){var a,u=t.content;this.logger.log("updateChatroomTagHandler:: ",u);var _=null===(a=null==u?void 0:u.updateTags)||void 0===a?void 0:a.currentTags;try{_=JSON.parse(_)}catch(t){this.logger.error("updateChatroomTagHandler:: ",t)}this.core.emit("tagsUpdate",_)},ChatroomService}(Au),Yu=getDefaultExportFromCjs(createCommonjsModule((function(t){function _defineProperties(t,a){for(var u=0;u<a.length;u++){var _=a[u];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Ea(t,_.key,_)}}t.exports=function _createClass(t,a,u){return a&&_defineProperties(t.prototype,a),u&&_defineProperties(t,u),Ea(t,"prototype",{writable:!1}),t},t.exports.__esModule=!0,t.exports.default=t.exports}))),ju=Ni.some,Hu=arrayMethodIsStrict("some");_export({target:"Array",proto:!0,forced:!Hu},{some:function some(t){return ju(this,t,arguments.length>1?arguments[1]:void 0)}});var Ku,Wu,qu=entryVirtual("Array").some,$u=Array.prototype,some=function(t){var a=t.some;return t===$u||H($u,t)&&a===$u.some?qu:a},zu={file:{md5:"$(Etag)",size:"$(ObjectSize)"},image:{md5:"$(Etag)",size:"$(ObjectSize)",w:"$(ImageInfo.Width)",h:"$(ImageInfo.Height)",orientation:"$(ImageInfo.Orientation)"},audio:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Audio.Duration)"},video:{md5:"$(Etag)",size:"$(ObjectSize)",dur:"$(AVinfo.Video.Duration)",w:"$(AVinfo.Video.Width)",h:"$(AVinfo.Video.Height)"}},Ju={accessKeyId:"",secretAccessKey:"",sessionToken:"",region:"",maxRetries:0,bucket:"",objectName:"",token:"",shortUrl:""};function getUploadResponseFormat(t){return void 0===t&&(t="file"),fc(zu[t]||{}).replace(/"/gi,'\\"')}!function(t){t[t.nos=1]="nos",t[t.s3=2]="s3"}(Ku||(Ku={})),function(t){t[t.dontNeed=-1]="dontNeed",t[t.time=2]="time",t[t.urls=3]="urls"}(Wu||(Wu={}));var Qu={chunkUploadHost:"https://wannos-web.127.net",chunkUploadHostBackupList:["https://fileup.chatnos.com","https://oss.chatnos.com"],commonUploadHost:"https://fileup.chatnos.com",commonUploadHostBackupList:["https://oss.chatnos.com"],chunkMaxSize:4194304e4,commonMaxSize:104857600,uploadReplaceFormat:"https://{host}/{object}",cdn:{defaultCdnDomain:"nim-nosdn.netease.im",cdnDomain:"",bucket:"",objectNamePrefix:""},downloadUrl:"https://{bucket}-nosdn.netease.im/{object}",downloadHostList:["nos.netease.com"],nosCdnEnable:!0,isNeedToGetUploadPolicyFromServer:!0};function pickBy(t,a){t=t||{},a=a||function(){return!0};var u={};for(var _ in t)a(t[_])&&(u[_]=t[_]);return u}var Xu=function(){function NOS(t,a){this.nosCdnHostTimer=0,this.nosErrorCount=0,this.core=t,this.cloudStorage=a}var t=NOS.prototype;return t.reset=function reset(){this.nosErrorCount=0},t.getNosAccessToken=function getNosAccessToken(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var a,u,_;return Kl.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("getNosAccessToken",{tag:t});case 2:return a=h.sent,u=get(a,"content.nosAccessTokenTag.token"),_=t.url,h.abrupt("return",{token:u,url:-1!==indexOf(_).call(_,"?")?_+"&token="+u:_+"?token="+u});case 6:case"end":return h.stop()}}),_callee,this)})))},t.deleteNosAccessToken=function deleteNosAccessToken(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){return Kl.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("deleteNosAccessToken",{tag:t});case 2:case"end":return a.stop()}}),_callee2,this)})))},t.nosUpload=function nosUpload(t,a){var u,_,h,m,E,g,T,I;return __awaiter(this,void 0,void 0,Kl.mark((function _callee3(){var S,M,N,A,O,R,C,b,P,x,k,w,D,L,V,U,G,B,Y;return Kl.wrap((function _callee3$(j){for(;;)switch(j.prev=j.next){case 0:if(S=get(this.core,"config.cdn.bucket"),M={tag:t.nosScenes||S||"nim"},t.nosSurvivalTime&&(M.expireSec=t.nosSurvivalTime),N=this.core.adapters.getFileUploadInformation(t),!(!a&&!N)){j.next=18;break}return j.prev=6,j.next=9,this.core.sendCmd("getNosToken",{responseBody:getUploadResponseFormat(t.type),nosToken:M});case 9:A=j.sent,j.next=18;break;case 12:if(j.prev=12,j.t0=j.catch(6),this.core.logger.error("uploadFile:: getNosToken error",j.t0),!(j.t0 instanceof Il)){j.next=17;break}throw j.t0;case 17:throw new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getNosToken error",rawError:j.t0,curProvider:1}});case 18:return O=this.config.uploadReplaceFormat.replace("{host}",this.config.cdn.cdnDomain||this.config.cdn.defaultCdnDomain).replace("{object}",N?null===(u=N.uploadInfo)||void 0===u?void 0:u.objectName:a?null==a?void 0:a.objectName:A.content.nosToken.objectName),R="",a&&a.shortUrl&&(R=a.shortUrl),(null===(m=null===(h=null===(_=null==N?void 0:N.uploadInfo)||void 0===_?void 0:_.payload)||void 0===h?void 0:h.mixStoreToken)||void 0===m?void 0:m.shortUrl)&&(R=N.uploadInfo.payload.mixStoreToken.shortUrl),C=R||O,j.prev=23,P=N?{token:null===(E=null==N?void 0:N.uploadInfo)||void 0===E?void 0:E.token,bucket:null===(g=null==N?void 0:N.uploadInfo)||void 0===g?void 0:g.bucketName,objectName:null===(T=null==N?void 0:N.uploadInfo)||void 0===T?void 0:T.objectName}:a||A.content.nosToken,this.core.logger.log("uploadFile:: uploadFile params",{nosToken:P,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,platform:Wl.platform}),x="BROWSER"===Wl.platform?this.config.chunkUploadHost:this.config.commonUploadHost+"/"+(P&&P.bucket),this.core.reporterHookCloudStorage.update({remote_addr:x,operation_type:a?2:0}),j.next=30,this.core.adapters.uploadFile(bt(bt(bt({},t),{nosToken:P,chunkUploadHost:this.config.chunkUploadHost,chunkUploadHostBackupList:this.config.chunkUploadHostBackupList,commonUploadHost:this.config.commonUploadHost,commonUploadHostBackupList:this.config.commonUploadHostBackupList,maxSize:t.maxSize||this.config.chunkMaxSize}),a?{payload:{mixStoreToken:a}}:{}));case 30:b=j.sent,j.next=65;break;case 33:if(j.prev=33,j.t1=j.catch(23),this.core.logger.error("uploadFile::nos uploadFile error:",j.t1),k="v2"===get(this.core,"options.apiVersion"),j.t1.code!==Tl.V2NIM_ERROR_CODE_CANCELLED&&10499!==j.t1.errCode){j.next=39;break}throw new Nl({code:k?Tl.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:get(j.t1,"message")||"Request abort",rawError:j.t1,curProvider:1}});case 39:if(!k||j.t1.errCode!==Tl.V2NIM_ERROR_CODE_FILE_OPEN_FAILED){j.next=41;break}throw new Il({code:Tl.V2NIM_ERROR_CODE_FILE_OPEN_FAILED,detail:{reason:get(j.t1,"message")||"Read file failed",rawError:j.t1,curProvider:1}});case 41:return j.next=43,Wl.net.getNetworkStatus();case 43:if(w=j.sent,!1!==w.net_connect){j.next=47;break}throw new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:j.t1,curProvider:1}});case 47:if(!a){j.next=64;break}if(!(this.nosErrorCount<=0)){j.next=60;break}j.prev=49,this.cloudStorage.mixStorage._addCircuitTimer(),j.next=56;break;case 53:throw j.prev=53,j.t2=j.catch(49),new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:j.t2,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.cloudStorage.mixStorage.mixStorePolicy,file:t.file||t.filePath}});case 56:return this.nosErrorCount=get(this.cloudStorage,"mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry"),j.abrupt("return",this.cloudStorage._uploadFile(t));case 60:return this.nosErrorCount--,j.abrupt("return",this.nosUpload(t,a));case 62:j.next=65;break;case 64:throw new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"NOS attempts failed",rawError:j.t1,curProvider:1}});case 65:if(D=null==b?void 0:b.type,(L=D&&indexOf(D).call(D,"/")>-1?slice(D).call(D,0,indexOf(D).call(D,"/")):"")||(L=t.type||""),(V={image:"imageInfo",video:"vinfo",audio:"vinfo"})[L]){j.next=71;break}return j.abrupt("return",bt({url:C},b));case 71:return j.prev=71,j.next=74,this.core.adapters.request(O+"?"+V[L],{method:"GET",dataType:"json",timeout:5e3},{exception_service:3});case 74:U=j.sent,j.next=81;break;case 77:return j.prev=77,j.t3=j.catch(71),this.core.logger.error("uploadFile:: fetch file info error",j.t3),j.abrupt("return",bt({url:C},b));case 81:if(!U){j.next=88;break}return G=U.data,B="imageInfo"===V[L]?G:null===(I=null==G?void 0:G.GetVideoInfo)||void 0===I?void 0:I.VideoInfo,Y={url:C,name:b.name,size:b.size,ext:b.ext,w:null==B?void 0:B.Width,h:null==B?void 0:B.Height,orientation:null==B?void 0:B.Orientation,dur:null==B?void 0:B.Duration,audioCodec:null==B?void 0:B.AudioCodec,videoCodec:null==B?void 0:B.VideoCodec,container:null==B?void 0:B.Container},j.abrupt("return",pickBy(Y,(function(t){return void 0!==t})));case 88:return j.abrupt("return",bt({url:C},b));case 89:case"end":return j.stop()}}),_callee3,this,[[6,12],[23,33],[49,53],[71,77]])})))},t._getNosCdnHost=function _getNosCdnHost(){var t;return __awaiter(this,void 0,void 0,Kl.mark((function _callee4(){var a,u,_,h=this;return Kl.wrap((function _callee4$(m){for(;;)switch(m.prev=m.next){case 0:return m.prev=0,m.next=3,this.core.sendCmd("getNosCdnHost");case 3:a=m.sent,m.next=10;break;case 6:return m.prev=6,m.t0=m.catch(0),this.core.logger.error("getNosCdnHost::error",m.t0),m.abrupt("return");case 10:if(a){m.next=12;break}return m.abrupt("return");case 12:u=null===(t=null==a?void 0:a.content)||void 0===t?void 0:t.nosConfigTag,0!==(_=wl(null==u?void 0:u.expire))&&u.cdnDomain?-1===_?(this.config.cdn.bucket=u.bucket,this.config.cdn.cdnDomain=u.cdnDomain,this.config.cdn.objectNamePrefix=u.objectNamePrefix):(this.config.cdn.bucket=u.bucket,this.config.cdn.cdnDomain=u.cdnDomain,this.config.cdn.objectNamePrefix=u.objectNamePrefix,this.nosCdnHostTimer=this.core.timerManager.addTimer((function(){h._getNosCdnHost()}),1e3*_)):(this.config.cdn.bucket="",this.config.cdn.cdnDomain="",this.config.cdn.objectNamePrefix="");case 15:case"end":return m.stop()}}),_callee4,this,[[0,6]])})))},Yu(NOS,[{key:"config",get:function get(){return this.cloudStorage.config}}]),NOS}(),Zu={"6_2":"getNosToken","6_22":"getOriginUrl","6_24":"getNosAccessToken","6_25":"deleteNosAccessToken","6_26":"getNosCdnHost","6_27":"getGrayscaleConfig","6_28":"getMixStorePolicy","6_29":"getMixStoreToken","6_30":"getFileAuthToken"},ed={nosToken:{objectName:1,token:2,bucket:3,expireTime:4,expireSec:7,tag:8,shortUrl:9},mixStoreTokenReqTag:{provider:0,tokenCount:1,nosSurvivalTime:2,tag:3,returnBody:4,policyVersion:5},nosConfigTag:{bucket:1,cdnDomain:2,expire:3,objectNamePrefix:4},grayConfigTag:{config:0,ttl:1},mixStorePolicyTag:{providers:0,ttl:1,mixEnable:2,nosPolicy:3,s3Policy:4,policyVersion:5},mixStoreTokenResTag:{provider:0,accessKeyId:1,secretAccessKey:2,sessionToken:3,token:4,expireTime:5,bucket:6,objectName:7,fileExpireSec:8,tag:9,shortUrl:10,region:11},nosSafeUrlTag:{safeUrl:0,originUrl:1},mixStoreAuthTokenReqTag:{type:1,urls:2},mixStoreAuthTokenResTag:{type:1,tokens:2,token:3,ttl:4},nosAccessTokenTag:{token:0,url:1,userAgent:2,ext:3}},td={getNosToken:{sid:6,cid:2,service:"cloudStorage",params:[{type:"String",name:"responseBody"},{type:"Property",name:"nosToken",entity:"nosToken",reflectMapper:ed.nosToken}],response:[{type:"Property",name:"nosToken",reflectMapper:invert(ed.nosToken)}]},getOriginUrl:{sid:6,cid:22,service:"cloudStorage",params:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:ed.nosSafeUrlTag}],response:[{type:"Property",name:"nosSafeUrlTag",reflectMapper:invert(ed.nosSafeUrlTag)}]},getNosCdnHost:{sid:6,cid:26,service:"cloudStorage",response:[{type:"Property",name:"nosConfigTag",reflectMapper:invert(ed.nosConfigTag)}]},getGrayscaleConfig:{sid:6,cid:27,service:"cloudStorage",params:[{type:"Property",name:"config"}],response:[{type:"Property",name:"grayConfigTag",reflectMapper:invert(ed.grayConfigTag)}]},getMixStorePolicy:{sid:6,cid:28,service:"cloudStorage",params:[{type:"LongArray",name:"supportType"}],response:[{type:"Property",name:"mixStorePolicyTag",reflectMapper:invert(ed.mixStorePolicyTag)}]},getMixStoreToken:{sid:6,cid:29,service:"cloudStorage",params:[{type:"Property",name:"mixStoreTokenReqTag",reflectMapper:ed.mixStoreTokenReqTag}],response:[{type:"Property",name:"mixStoreTokenResTag",reflectMapper:invert(ed.mixStoreTokenResTag)}]},getFileAuthToken:{sid:6,cid:30,service:"cloudStorage",params:[{type:"Property",name:"mixStoreAuthTokenReqTag",reflectMapper:ed.mixStoreAuthTokenReqTag}],response:[{type:"Property",name:"mixStoreAuthTokenResTag",reflectMapper:invert(ed.mixStoreAuthTokenResTag)}]},getNosAccessToken:{sid:6,cid:24,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:ed.nosAccessTokenTag}],response:[{type:"Property",name:"tag",reflectMapper:invert(ed.nosAccessTokenTag)}]},deleteNosAccessToken:{sid:6,cid:25,service:"cloudStorage",params:[{type:"Property",name:"tag",reflectMapper:ed.nosAccessTokenTag}]}},rd=function(){function MixStorage(t,a){this.GRAYKEY="AllGrayscaleConfig",this.MIXSTOREKEY="AllMixStorePolicy",this.grayConfig={mixStoreEnable:!1,timeStamp:0,ttl:0},this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10,this.circuitTimer=0,this.core=t,this.cloudStorage=a,this.logger=t.logger}var t=MixStorage.prototype;return t.reset=function reset(){this.grayConfig=null,this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.curProvider=1,this.mixStoreErrorCount=10},t.getGrayscaleConfig=function getGrayscaleConfig(t,a){var u;return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var _,h;return Kl.wrap((function _callee$(m){for(;;)switch(m.prev=m.next){case 0:if(Wl.localStorage)try{Wl.localStorage.getItem&&Wl.localStorage.getItem(this.GRAYKEY)&&(this.grayConfig=JSON.parse(Wl.localStorage.getItem(this.GRAYKEY))[t])}catch(t){Wl.localStorage.getItem(this.GRAYKEY)&&this.core.logger.error("uploadFile:: JSON.parse grayscaleConfig error ",t)}if(this.grayConfig&&!(this.grayConfig.timeStamp+1e3*this.grayConfig.ttl<a)){m.next=17;break}return m.next=4,this.core.sendCmd("getGrayscaleConfig",{config:{}});case 4:if(!(_=m.sent).content||!_.content.grayConfigTag){m.next=16;break}this.logger.log("uploadFile::getAppGrayConfigRequest success ");try{this.grayConfig=JSON.parse(_.content.grayConfigTag.config),this.grayConfig.ttl=JSON.parse(_.content.grayConfigTag.ttl)}catch(t){this.logger.error("getGrayscaleConfig error",t)}if(this.grayConfig){m.next=10;break}return m.abrupt("return");case 10:h=Wl.localStorage.getItem(this.GRAYKEY)?JSON.parse(Wl.localStorage.getItem(this.GRAYKEY)):{},this.grayConfig.timeStamp=(new Date).getTime(),h[t]=this.grayConfig,Wl.localStorage.setItem(this.GRAYKEY,fc(h)),m.next=17;break;case 16:this.logger.log("uploadFile:: result grayConfig:",_.content);case 17:if(!(null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable)){m.next=20;break}return m.next=20,this._getMixStorePolicy(t);case 20:case"end":return m.stop()}}),_callee,this)})))},t._getMixStorePolicy=function _getMixStorePolicy(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){var a,u,_,h,m,E,g;return Kl.wrap((function _callee2$(T){for(;;)switch(T.prev=T.next){case 0:if(a=(new Date).getTime(),Wl.localStorage)try{this.mixStorePolicy=JSON.parse(Wl.localStorage.getItem(this.MIXSTOREKEY))[t],this.curProvider=wl(this.mixStorePolicy.providers[0]),this.mixStorePolicy.timeStamp&&this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl>a&&(_=this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl-a,this.core.timerManager.addTimer(bind$1(u=this._getMixStorePolicy).call(u,this,t),_))}catch(a){Wl.localStorage.getItem(this.MIXSTOREKEY)&&JSON.parse(Wl.localStorage.getItem(this.MIXSTOREKEY))[t]&&this.core.logger.error("uploadFile:: JSON.parse mixStorePolicy error ",a)}if(this.mixStorePolicy&&!(this.mixStorePolicy.timeStamp+1e3*this.mixStorePolicy.ttl<=a)){T.next=31;break}return T.prev=3,T.next=6,this.core.sendCmd("getMixStorePolicy",{supportType:this.cloudStorage.aws.s3?[1,2]:[1]});case 6:m=T.sent,E=m.content.mixStorePolicyTag,this.mixStorePolicy={providers:[],timeStamp:0,ttl:0,s3Policy:null,nosPolicy:null,policyVersion:void 0},this.mixStorePolicy.policyVersion=E.policyVersion,this.mixStorePolicy.ttl=Number(E.ttl),this.mixStorePolicy.providers=E.providers.split(","),this.circuitTimer&&this.core.timerManager.deleteTimer(this.circuitTimer),this.curProvider=wl(this.mixStorePolicy.providers[0]),this.mixStorePolicy.nosPolicy=E.nosPolicy?JSON.parse(E.nosPolicy):null,this.mixStorePolicy.s3Policy=E.s3Policy?JSON.parse(E.s3Policy):null,null===this.mixStorePolicy.s3Policy?this.mixStorePolicy.providers=["1"]:null===this.mixStorePolicy.nosPolicy?this.mixStorePolicy.providers=["2"]:this.mixStorePolicy.providers=this.mixStorePolicy.s3Policy.priority<this.mixStorePolicy.nosPolicy.priority?["2","1"]:["1","2"],this.core.timerManager.addTimer(bind$1(h=this._getMixStorePolicy).call(h,this,t),1e3*this.mixStorePolicy.ttl),g=Wl.localStorage.getItem(this.MIXSTOREKEY)?JSON.parse(Wl.localStorage.getItem(this.MIXSTOREKEY)):{},this.mixStorePolicy.timeStamp=(new Date).getTime(),g[t]=this.mixStorePolicy,Wl.localStorage.setItem(this.MIXSTOREKEY,fc(g)),T.next=31;break;case 24:if(T.prev=24,T.t0=T.catch(3),this.logger.error("getMixStorePolicy error",T.t0),0!==this.mixStoreErrorCount){T.next=29;break}throw new Error("getMixStorePolicy all count error");case 29:this._getMixStorePolicy(t),this.mixStoreErrorCount--;case 31:this.mixStorePolicy.nosPolicy&&(this.cloudStorage.nos.nosErrorCount=this.mixStorePolicy.nosPolicy.uploadConfig.retryPolicy.retry);case 32:case"end":return T.stop()}}),_callee2,this,[[3,24]])})))},t._addCircuitTimer=function _addCircuitTimer(){var t=this,a=this.mixStorePolicy.providers,u=a[(indexOf(a).call(a,String(this.curProvider))+1)%a.length];if(!u)throw new Error("uploadFile nextProvider error");if(u===a[0])throw new Error("uploadFile all policy fail");if(this.logger.log("uploadFile:: upload policy will change,now policy:"+this.curProvider+" nextProvider:"+u),this.curProvider=wl(u),this.mixStorePolicy.nosPolicy&&this.mixStorePolicy.s3Policy){var _=this.mixStorePolicy[1===this.curProvider?"nosPolicy":"s3Policy"].uploadConfig.retryPolicy.circuit;if(!_||0===_)throw new Error("uploadFile circuit error");this.circuitTimer=this.core.timerManager.addTimer((function(){t.logger.log("uploadFile:: upload policy will change,now policy:"+t.curProvider+" nextProvider:"+wl(t.mixStorePolicy.providers[0])),t.curProvider=wl(t.mixStorePolicy.providers[0]),t.core.timerManager.deleteTimer(t.circuitTimer)}),1e3*_)}throw new Error("uploadFile will not retry again")},t.getFileAuthToken=function getFileAuthToken(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee3(){var a;return Kl.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return u.next=2,this.core.sendCmd("getFileAuthToken",{mixStoreAuthTokenReqTag:t});case 2:return a=u.sent,u.abrupt("return",a.content.mixStoreAuthTokenResTag);case 4:case"end":return u.stop()}}),_callee3,this)})))},MixStorage}(),od=Is.trim,nd=O("".charAt),id=h.parseFloat,ad=h.Symbol,sd=ad&&ad.iterator,cd=1/id(Es+"-0")!=-1/0||sd&&!fails((function(){id(Object(sd))}))?function parseFloat(t){var a=od(toString(t)),u=id(a);return 0===u&&"-"==nd(a,0)?-0:u}:id;_export({global:!0,forced:parseFloat!=cd},{parseFloat:cd});var ld,ud=j.parseFloat,dd=-1,pd=function(){function AWS(t,a){this.s3=null,this.core=t,this.cloudStorage=a,this.logger=t.logger}var t=AWS.prototype;return t.s3Upload=function s3Upload(t,a){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){var u,_,h,m,E,g,T,I,S,M,N,A,O,R,C=this;return Kl.wrap((function _callee2$(b){for(;;)switch(b.prev=b.next){case 0:if(dd+=1,!t.file){b.next=5;break}u=t.file,b.next=20;break;case 5:if("string"!=typeof t.fileInput){b.next=15;break}if(this.logger.warn("fileInput will abandon,Please use file or filepath"),!((_=document.getElementById(t.fileInput))&&_.files&&_.files[0])){b.next=12;break}u=_.files[0],b.next=13;break;case 12:throw new Error("Can not get file from fileInput");case 13:b.next=20;break;case 15:if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0])){b.next=19;break}u=t.fileInput.files[0],b.next=20;break;case 19:throw new Error("Can not get file from fileInput "+t.fileInput);case 20:if(this.mixStorePolicy.s3Policy){b.next=22;break}throw new Error("dont get s3 policy");case 22:return h={accessKeyId:a.accessKeyId,secretAccessKey:a.secretAccessKey,sessionToken:a.sessionToken,region:a.region,maxRetries:this.mixStorePolicy.s3Policy.uploadConfig.retryPolicy.retry},m=this.s3,E=decodeURIComponent(a.bucket),g=decodeURIComponent(a.objectName),T=u,I="https://"+E+".s3.amazonaws.com/"+g,S={},(M=this.mixStorePolicy.s3Policy)&&M.uploadConfig&&Dl(M.uploadConfig.uploadUrl)&&M.uploadConfig.uploadUrl.length>0&&(N=M.uploadConfig.uploadUrl.length,dd%=N,S.endpoint=M.uploadConfig.uploadUrl[dd],S.s3ForcePathStyle=!0,I=S.endpoint+"/"+E+"/"+g),this.core.reporterHookCloudStorage.update({remote_addr:I,operation_type:1}),(A=new m(S)).config.update(h),O={Bucket:E,Key:g,Body:T,Metadata:{token:a.token},ContentType:T.type||"application/octet-stream"},this.core.logger.log("uploadFile:: s3 upload params:",O),(R=A.upload(O)).on("httpUploadProgress",(function(a){var u=ud((a.loaded/a.total).toFixed(2));t.onUploadProgress&&t.onUploadProgress({total:a.total,loaded:a.loaded,percentage:u,percentageText:Math.round(100*u)+"%"})})),b.abrupt("return",new Jn((function(u,_){var h=(new Date).getTime();R.send((function(m,I){return __awaiter(C,void 0,void 0,Kl.mark((function _callee(){var S,M,N,A,O,R,C,b;return Kl.wrap((function _callee$(P){for(;;)switch(P.prev=P.next){case 0:if(!m||"RequestAbortedError"!==m.code){P.next=5;break}this.logger.error("uploadFile:","api::s3:upload file abort.",m),_(new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_CANCELLED:400,detail:{reason:"S3RequestAbortedError",rawError:m,curProvider:2}})),P.next=41;break;case 5:if(!m){P.next=26;break}return this.logger.error("uploadFile:","api::s3:upload file failed.",m),this.core.reporter.reportTraceStart("exceptions",{user_id:this.core.options.account||(null===(M=null===(S=this.core)||void 0===S?void 0:S.auth)||void 0===M?void 0:M.account),trace_id:null===(N=this.core.clientSocket.socket)||void 0===N?void 0:N.sessionId,start_time:h,action:1,exception_service:4}),this.core.reporter.reportTraceUpdateV2("exceptions",{code:"number"==typeof m.status?m.status:"number"==typeof m.code?m.code:0,description:m.message||""+m.code,operation_type:1,target:fc({bucket:E,object:g})},{asyncParams:Wl.net.getNetworkStatus()}),this.core.reporter.reportTraceEnd("exceptions",1),P.next=12,Wl.net.getNetworkStatus();case 12:if(A=P.sent,!1!==A.net_connect){P.next=16;break}return P.abrupt("return",_(new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"No network",rawError:m,curProvider:this.cloudStorage.mixStorage.curProvider}})));case 16:P.prev=16,this.cloudStorage.mixStorage._addCircuitTimer(),P.next=23;break;case 20:return P.prev=20,P.t0=P.catch(16),P.abrupt("return",_(new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"All upload attempts failed",rawError:P.t0,curProvider:this.cloudStorage.mixStorage.curProvider,mixStorePolicy:this.mixStorePolicy,file:t.file||t.filePath}})));case 23:u(this.cloudStorage._uploadFile(t)),P.next=41;break;case 26:if(O=(O=(O=this.mixStorePolicy.s3Policy.cdnSchema).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn)).replace("{objectName}",I.Key),R={size:T.size,name:T.name,url:a.shortUrl?a.shortUrl:O,ext:T.name.split(".")[1]||"unknown"},C=t.type||"",(b={image:"imageInfo"})[C]){P.next=36;break}return P.abrupt("return",u(R));case 36:return P.t1=u,P.next=39,this.getS3FileInfo({url:O,infoSuffix:b[C],s3Result:R});case 39:return P.t2=P.sent,P.abrupt("return",(0,P.t1)(P.t2));case 41:case"end":return P.stop()}}),_callee,this,[[16,20]])})))})),t.onUploadStart&&t.onUploadStart(R)})));case 39:case"end":return b.stop()}}),_callee2,this)})))},t.getS3FileInfo=function getS3FileInfo(t){var a;return __awaiter(this,void 0,void 0,Kl.mark((function _callee3(){var u,_,h,m,E,g,T;return Kl.wrap((function _callee3$(I){for(;;)switch(I.prev=I.next){case 0:return u=t.url,_=t.infoSuffix,h=t.s3Result,I.prev=1,I.next=4,this.core.adapters.request(u+"?"+_,{method:"GET",dataType:"text",timeout:5e3},{exception_service:3});case 4:m=I.sent,I.next=11;break;case 7:return I.prev=7,I.t0=I.catch(1),this.core.logger.error("uploadFile:: fetch file info error",I.t0),I.abrupt("return",h);case 11:if(!m){I.next=18;break}return E=m.data,g="imageInfo"===_?E:null===(a=null==E?void 0:E.GetVideoInfo)||void 0===a?void 0:a.VideoInfo,T=bt(bt({},h),{w:null==g?void 0:g.Width,h:null==g?void 0:g.Height,orientation:null==g?void 0:g.Orientation,dur:null==g?void 0:g.Duration,audioCodec:null==g?void 0:g.AudioCodec,videoCodec:null==g?void 0:g.VideoCodec,container:null==g?void 0:g.Container}),I.abrupt("return",pickBy(T,(function(t){return void 0!==t})));case 18:return this.core.logger.error("uploadFile:: fetch s3 file info no result",u+"?"+_),I.abrupt("return",h);case 20:case"end":return I.stop()}}),_callee3,this,[[1,7]])})))},Yu(AWS,[{key:"mixStorePolicy",get:function get(){return this.cloudStorage.mixStorage.mixStorePolicy}}]),AWS}(),fd=function(){function CloudStorageService(t,a){void 0===a&&(a={}),this.config={},this.uploadTaskMap={},this.name="cloudStorage",this.logger=t.logger,this.core=t,this.nos=new Xu(t,this),this.mixStorage=new rd(t,this),this.aws=new pd(t,this),registerParser({cmdMap:Zu,cmdConfig:td}),this.setOptions(a),this.setListeners()}var t=CloudStorageService.prototype;return t.setOptions=function setOptions(t){void 0===t&&(t={});var a=t.storageKeyPrefix||"NIMClient";this.mixStorage.GRAYKEY=a+"-AllGrayscaleConfig",this.mixStorage.MIXSTOREKEY=a+"-AllMixStorePolicy";var u=t.s3,_=__rest(t,["s3"]),h=bt({},Qu,this.config);if(_&&Object.prototype.hasOwnProperty.call(_,"cdn")){var m=bt(bt({},h.cdn),_.cdn);this.config=bt({},h,_),this.config.cdn=m}else this.config=bt({},h,_);u&&(this.aws.s3=u)},t.setListeners=function setListeners(){var t,a,u,_;this.core.eventBus.on("kicked",bind$1(t=this._clearUnCompleteTask).call(t,this)),this.core.eventBus.on("disconnect",bind$1(a=this._clearUnCompleteTask).call(a,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleLogout",bind$1(u=this._clearUnCompleteTask).call(u,this)),this.core.eventBus.on("V2NIMLoginService/loginLifeCycleKicked",bind$1(_=this._clearUnCompleteTask).call(_,this))},t._clearUnCompleteTask=function _clearUnCompleteTask(){var t,a=this;forEach$1(t=qa(this.uploadTaskMap)).call(t,(function(t){var u=a.uploadTaskMap[t];u&&u.abort&&u.abort()})),this.uploadTaskMap={}},t.init=function init(t){return void 0===t&&(t=Zn()),__awaiter(this,void 0,void 0,Kl.mark((function _callee(){return Kl.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.mixStorage.reset(),this.nos.reset(),!this.config.isNeedToGetUploadPolicyFromServer){a.next=5;break}return a.next=5,this.mixStorage.getGrayscaleConfig(this.core.options.appkey,t);case 5:return a.next=7,this.nos._getNosCdnHost();case 7:case"end":return a.stop()}}),_callee,this)})))},t.processCallback=function processCallback(t,a){var u=this,_=t.onUploadProgress,h=t.onUploadDone,m=t.onUploadStart;return{onUploadStart:"function"==typeof m?function(t){u.uploadTaskMap[a]=t;try{m(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadStart execute error",t)}}:function(t){u.uploadTaskMap[a]=t},onUploadProgress:"function"==typeof _?function(t){u.core.reporterHookCloudStorage.update({transferred_size:t.loaded,full_size:t.total});try{_(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadProgress execute error",t)}}:function(t){u.core.reporterHookCloudStorage.update({transferred_size:t.loaded,full_size:t.total})},onUploadDone:"function"==typeof h?function(t){u.core.reporterHookCloudStorage.end(0);try{h(t)}catch(t){u.logger.error("CloudStorage::uploadFile:options.onUploadDone execute error",t)}}:function(){u.core.reporterHookCloudStorage.end(0)},taskKey:a}},t.uploadFile=function uploadFile(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){var a,u,_,h,m,E,g,T;return Kl.wrap((function _callee2$(I){for(;;)switch(I.prev=I.next){case 0:if(validate({maxSize:{type:"number",required:!1},type:{type:"enum",values:["file","image","audio","video"]}},t),t.fileInput||t.file||t.filePath){I.next=3;break}throw new Error("uploadFile needs target file object or a filePath");case 3:if(!t.type||"file"===t.type){I.next=7;break}if(!(a=get(t,"file.type"))||"string"!=typeof a||-1!==indexOf(a).call(a,t.type)){I.next=7;break}throw new Error('The meta type "'+a+'" does not match "'+t.type+'"');case 7:return this.core.reporterHookCloudStorage.start(),t.file?this.core.reporterHookCloudStorage.update({full_size:t.file.size}):"string"==typeof t.fileInput?(u=document.getElementById(t.fileInput))&&u.files&&u.files[0]&&this.core.reporterHookCloudStorage.update({full_size:u.files[0].size}):t.fileInput&&t.fileInput.files&&t.fileInput.files[0]&&this.core.reporterHookCloudStorage.update({full_size:t.fileInput.files[0].size}),_=Vl(),h=this.processCallback(t,_),m=h.onUploadStart,E=h.onUploadProgress,g=h.onUploadDone,t.onUploadStart=m,t.onUploadProgress=E,t.onUploadDone=g,T=null,I.prev=15,I.next=18,this._uploadFile(t);case 18:T=I.sent,t.md5&&(T.md5=t.md5),delete this.uploadTaskMap[_],I.next=28;break;case 23:throw I.prev=23,I.t0=I.catch(15),delete this.uploadTaskMap[_],this.core.reporterHookCloudStorage.end((I.t0&&I.t0.code)===Tl.V2NIM_ERROR_CODE_CANCELLED?3:1),I.t0;case 28:return T&&(T.size=void 0===T.size?void 0:Number(T.size),T.w=void 0===T.w?void 0:Number(T.w),T.h=void 0===T.h?void 0:Number(T.h),T.dur=void 0===T.dur?void 0:Number(T.dur)),T.url=decodeURIComponent(T.url),t.onUploadDone({size:T.size,name:T.name,url:T.url,ext:T.name.split(".")[1]||"unknown"}),I.abrupt("return",T);case 32:case"end":return I.stop()}}),_callee2,this,[[15,23]])})))},t._uploadFile=function _uploadFile(t){var a,u;return __awaiter(this,void 0,void 0,Kl.mark((function _callee3(){var _,h,m,E;return Kl.wrap((function _callee3$(g){for(;;)switch(g.prev=g.next){case 0:if(get(this.mixStorage,"grayConfig.mixStoreEnable")&&get(this.mixStorage,"mixStorePolicy.providers.length")){g.next=3;break}return this.logger.log("uploadFile:: uploadFile begin, use old nos"),g.abrupt("return",this.nos.nosUpload(t));case 3:if(this.logger.log("uploadFile::_uploadFile, grayConfig enable:"+get(this.mixStorage,"grayConfig.mixStoreEnable")+" curProvider:"+get(this.mixStorage,"curProvider")),_=this.core.adapters.getFileUploadInformation(t),h=!0,_?!1===_.complete&&2===this.mixStorage.curProvider&&(h=!1):h=!1,this.aws.s3||(this.mixStorage.curProvider=1),m=Ju,h){g.next=23;break}return g.prev=10,g.next=13,this.core.sendCmd("getMixStoreToken",{mixStoreTokenReqTag:{provider:this.mixStorage.curProvider,tokenCount:1,tag:"qchat",nosSurvivalTime:t.nosSurvivalTime,returnBody:getUploadResponseFormat(t.type),policyVersion:this.mixStorage.mixStorePolicy.policyVersion}});case 13:E=g.sent,m=E.content.mixStoreTokenResTag,g.next=23;break;case 17:if(g.prev=17,g.t0=g.catch(10),this.core.logger.error("uploadFile:: getMixStoreToken error",g.t0),!(g.t0 instanceof Il)){g.next=22;break}throw g.t0;case 22:throw new Nl({code:"v2"===get(this.core,"options.apiVersion")?Tl.V2NIM_ERROR_CODE_FILE_UPLOAD_FAILED:400,detail:{reason:"getMixStoreToken error",rawError:g.t0,curProvider:this.mixStorage.curProvider,mixStorePolicy:this.mixStorage.mixStorePolicy}});case 23:if(h){g.next=27;break}return g.abrupt("return",2===this.mixStorage.curProvider?this.aws.s3Upload(t,m):this.nos.nosUpload(t,m));case 27:return g.abrupt("return",this.nos.nosUpload(t,null===(u=null===(a=null==_?void 0:_.uploadInfo)||void 0===a?void 0:a.payload)||void 0===u?void 0:u.mixStoreToken));case 28:case"end":return g.stop()}}),_callee3,this,[[10,17]])})))},t.getThumbUrl=function getThumbUrl(t,a){var u,_,h,m,E;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),t;var g=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);g[0],g[1],g[2],g[3],g[4];var T=g[5];if(g[6],g[7],null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable){var I=this._getUrlType(t);if(2===I&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.imagethumb"))return(null===(h=null===(_=this.mixStorePolicy.s3Policy)||void 0===_?void 0:_.thumbPolicy)||void 0===h?void 0:h.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",T).replace("{x}",a.width.toString()).replace("{y}",a.height.toString());if(1===I&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.imagethumb"))return(null===(E=null===(m=this.mixStorePolicy.nosPolicy)||void 0===m?void 0:m.thumbPolicy)||void 0===E?void 0:E.imagethumb).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",T).replace("{x}",a.width.toString()).replace("{y}",a.height.toString())}return includes(t).call(t,"?")?t+"&imageView&thumbnail="+a.width+"x"+a.height:t+"?imageView&thumbnail="+a.width+"x"+a.height},t.getVideoCoverUrl=function getVideoCoverUrl(t,a){var u,_,h,m,E;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),t;var g=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);g[0],g[1],g[2],g[3],g[4];var T=g[5];if(g[6],g[7],null===(u=this.grayConfig)||void 0===u?void 0:u.mixStoreEnable){var I=this._getUrlType(t);if(2===I&&this.mixStorePolicy.s3Policy&&get(this.mixStorePolicy,"s3Policy.thumbPolicy.vframe"))return(null===(h=null===(_=this.mixStorePolicy.s3Policy)||void 0===_?void 0:_.thumbPolicy)||void 0===h?void 0:h.vframe).replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",T).replace("{x}",a.width.toString()).replace("{y}",a.height.toString()).replace("{offset}","0").replace("{type}","png");if(1===I&&this.mixStorePolicy.nosPolicy&&get(this.mixStorePolicy,"nosPolicy.thumbPolicy.vframe"))return(null===(E=null===(m=this.mixStorePolicy.nosPolicy)||void 0===m?void 0:m.thumbPolicy)||void 0===E?void 0:E.vframe).replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",T).replace("{x}",a.width.toString()).replace("{y}",a.height.toString()).replace("{offset}","0").replace("{type}","png")}return includes(t).call(t,"?")?t+"&vframe&offset=0&resize="+a.width+"x"+a.height+"&type=png":t+"?vframe&offset=0&resize="+a.width+"x"+a.height+"&type=png"},t.getPrivateUrl=function getPrivateUrl(t){var a;if(!new RegExp(/http(s)?:\/\/([\w-]+\.)+[\w-]+(\/[\w- ./?%&=]*)?/).test(t))return this.logger.error("illegal file url:"+t),"";var u=/^(?:([A-Za-z]+):)?(\/{0,3})([0-9.\-A-Za-z]+)(?::(\d+))?(?:\/([^?#]*))?(?:\?([^#]*))?(?:#(.*))?$/.exec(t);u[0];var _=u[1];u[2];var h=u[3];u[4];var m=u[5];if(u[6],u[7],null===(a=this.grayConfig)||void 0===a?void 0:a.mixStoreEnable){var E=this._getUrlType(t);return 2===E&&this.mixStorePolicy.s3Policy&&(t=this.mixStorePolicy.s3Policy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.s3Policy.dlcdn).replace("{objectName}",m)),1===E&&this.mixStorePolicy.nosPolicy&&(t=this.mixStorePolicy.nosPolicy.cdnSchema.replace("{cdnDomain}",this.mixStorePolicy.nosPolicy.dlcdn).replace("{objectName}",m)),t}var g=this.config,T=g.downloadUrl,I=g.downloadHostList,S=g.nosCdnEnable,M=this.config.cdn.cdnDomain,N=this.config.cdn.objectNamePrefix?decodeURIComponent(this.config.cdn.objectNamePrefix):"",A=decodeURIComponent(m),O=indexOf(A).call(A,N);if(M&&O>-1&&S)return""+_+M+"/"+slice(A).call(A,O);if(includes(I).call(I,h)&&includes(m).call(m,"/")){var R=indexOf(m).call(m,"/"),C=m.substring(0,R),b=m.substring(R+1);return T.replace("{bucket}",C).replace("{object}",b)}var P=filter(I).call(I,(function(t){return"string"==typeof h&&includes(h).call(h,t)}))[0],x=P?h.replace(P,"").replace(/\W/g,""):null;return x?T.replace("{bucket}",x).replace("{object}",m):t},t.getOriginUrl=function getOriginUrl(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee4(){var a;return Kl.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:if("string"==typeof t&&includes(t).call(t,"_im_url=1")){u.next=2;break}return u.abrupt("return",t);case 2:return u.next=4,this.core.sendCmd("getOriginUrl",{nosSafeUrlTag:{safeUrl:t}});case 4:return a=u.sent,u.abrupt("return",a.content.nosSafeUrlTag.originUrl);case 6:case"end":return u.stop()}}),_callee4,this)})))},t.getFileToken=function getFileToken(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee5(){var a,u,_,h,m,E=this;return Kl.wrap((function _callee5$(g){for(;;)switch(g.prev=g.next){case 0:if(validate({type:{type:"number",min:2,max:3},urls:{type:"array",required:!1,itemType:"string"}},t),a=this.mixStorePolicy.nosPolicy?this.mixStorePolicy.nosPolicy.authPolicy.policyType:null,u=this.mixStorePolicy.s3Policy?this.mixStorePolicy.s3Policy.authPolicy.policyType:null,a!==String(-1)||u!==String(-1)){g.next=8;break}throw this.logger.error("don't need token"),new Error("don't need token");case 8:if(2!==t.type){g.next=17;break}if(!(a&&indexOf(a).call(a,String(2))>=0||u&&indexOf(u).call(u,String(2))>0)){g.next=13;break}return g.abrupt("return",this.mixStorage.getFileAuthToken(t));case 13:throw this.logger.error("don't support time token "),new Error("don't support type time token ");case 15:g.next=33;break;case 17:if(t.urls&&t.urls.length){g.next=20;break}throw this.logger.error("urls is required when urls token"),new Error("urls is required when urls token");case 20:if(h=[],m=[],forEach$1(_=t.urls).call(_,(function(t){var a=E._getUrlType(t);1===a&&m.push(t),2===a&&h.push(t)})),(!u||0!==h.length&&indexOf(u).call(u,String(3))<0)&&(this.logger.warn("s3 url don't support url token"),h=[]),(!a||0!==m.length&&indexOf(a).call(a,String(3))<0)&&(this.logger.warn("nos url don't support url token"),m=[]),0!==h.length||0!==m.length){g.next=30;break}throw this.logger.error("not support urls"),new Error("not support urls");case 30:if(0!==h.length&&0!==m.length){g.next=33;break}return t.urls=fc(t.urls),g.abrupt("return",this.mixStorage.getFileAuthToken(t));case 33:case"end":return g.stop()}}),_callee5,this)})))},t._getUrlType=function _getUrlType(t){var a,u;return this.mixStorePolicy.nosPolicy&&some(a=this.mixStorePolicy.nosPolicy.dlcdns).call(a,(function(a){return indexOf(t).call(t,a)>=0}))?1:this.mixStorePolicy.s3Policy&&some(u=this.mixStorePolicy.s3Policy.dlcdns).call(u,(function(a){return indexOf(t).call(t,a)>=0}))?2:null},t.getNosAccessToken=function getNosAccessToken(t){return validate({url:{type:"string",allowEmpty:!1}},t),this.nos.getNosAccessToken(t)},t.deleteNosAccessToken=function deleteNosAccessToken(t){return validate({token:{type:"string",allowEmpty:!1}},t),this.nos.deleteNosAccessToken(t)},t.process=function process(t){var a=get(t,"error.detail.ignore");return t.error&&!a?Jn.reject(t.error):Jn.resolve(t)},Yu(CloudStorageService,[{key:"grayConfig",get:function get(){return this.mixStorage.grayConfig}},{key:"mixStorePolicy",get:function get(){return this.mixStorage.mixStorePolicy}}]),CloudStorageService}(),_d=function(t){function ChatroomMsgService(a){return t.call(this,"chatroomMsg",a)||this}Mt(ChatroomMsgService,t);var a=ChatroomMsgService.prototype;return a.sendTextMsg=function sendTextMsg(t){return validate({body:{type:"string",allowEmpty:!1}},t),this.sendMsg(bt(bt({},t),{type:"text"}))},a.sendGeoLocationMsg=function sendGeoLocationMsg(t){return validate({body:{type:"object",rules:{title:{type:"string",allowEmpty:!1},lat:{type:"number"},lng:{type:"number"}}}},t),this.sendMsg(bt(bt({},t),{type:"geo",body:fc(t.body)}))},a.sendTipMsg=function sendTipMsg(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){return Kl.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return validate({body:{type:"string",allowEmpty:!1}},t),a.abrupt("return",this.sendMsg(bt(bt({},t),{type:"tip"})));case 2:case"end":return a.stop()}}),_callee,this)})))},a.sendCustomMsg=function sendCustomMsg(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){return Kl.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return validate({body:{type:"string",allowEmpty:!1}},t),a.abrupt("return",this.sendMsg(bt(bt({},t),{type:"custom"})));case 2:case"end":return a.stop()}}),_callee2,this)})))},a.sendImageMsg=function sendImageMsg(t){return this.doSendFile(bt(bt({},t),{type:"image"}))},a.sendFileMsg=function sendFileMsg(t){return this.doSendFile(bt(bt({},t),{type:"file"}))},a.sendAudioMsg=function sendAudioMsg(t){return this.doSendFile(bt(bt({},t),{type:"audio"}))},a.sendVideoMsg=function sendVideoMsg(t){return this.doSendFile(bt(bt({},t),{type:"video"}))},a.doSendFile=function doSendFile(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee3(){var a;return Kl.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:if(validate({type:{type:"string",allowEmpty:!1},body:{type:"object",rules:{url:{type:"string",allowEmpty:!1}},required:!1},maxSize:{type:"number",min:1,required:!1}},t),a=t.body){u.next=15;break}if(this.core.cloudStorage&&this.core.cloudStorage.uploadFile){u.next=5;break}throw new Error('Service "cloudStorage" does not exist');case 5:return u.prev=5,u.next=8,this.core.cloudStorage.uploadFile(t);case 8:a=u.sent,u.next=15;break;case 11:throw u.prev=11,u.t0=u.catch(5),this.logger.error("sendFile:: upload File error or abort.",u.t0),u.t0;case 15:return u.abrupt("return",this.sendMsg(bt(bt({},t),{body:fc(a),type:t.type})));case 16:case"end":return u.stop()}}),_callee3,this,[[5,11]])})))},a.queryMessageHistory=function queryMessageHistory(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee4(){var a,u,_;return Kl.wrap((function _callee4$(h){for(;;)switch(h.prev=h.next){case 0:return validate({timetag:{type:"number",required:!1},limit:{type:"number",min:1,max:100,required:!1},reverse:{type:"boolean",required:!1},msgTypes:{type:"array",itemType:"string",required:!1}},t),h.next=3,this.core.sendCmd("chatroomQueryMessageHistory",bt(bt({timetag:0,limit:100,reverse:!1},t),{msgTypes:t.msgTypes?map$6(a=t.msgTypes).call(a,(function(t){return wu[t]})):[]}));case 3:return u=h.sent,_=u.content.chatroomMsgs,h.abrupt("return",formatChatroomMsgs(_,this.core.account));case 6:case"end":return h.stop()}}),_callee4,this)})))},a.getHistoryMsgsByTags=function getHistoryMsgsByTags(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee5(){var a,u,_,h,m;return Kl.wrap((function _callee5$(E){for(;;)switch(E.prev=E.next){case 0:return validate({tags:{type:"array",itemType:"string",required:!0},types:{type:"array",itemType:"string",required:!1},fromTime:{type:"number",min:0,required:!1},toTime:{type:"number",min:0,required:!1},limit:{type:"number",min:0,required:!1},reverse:{type:"number",required:!1}},t),_=bt(bt({fromTime:0,toTime:0,limit:100,reverse:0},t),{types:t.types?map$6(a=filter(u=t.types).call(u,(function(t){return void 0!==wu[t]}))).call(a,(function(t){return wu[t]})):[]}),E.next=4,this.core.sendCmd("getHistoryMsgsByTags",{chatRoomTagHistoryMsgRequestTag:bt(_,{tags:fc(_.tags),types:fc(_.types)})});case 4:return h=E.sent,m=h.content.chatroomMsgs,E.abrupt("return",formatChatroomMsgs(m,this.core.account));case 7:case"end":return E.stop()}}),_callee5,this)})))},a.onChatroomMsgHandler=function onChatroomMsgHandler(t){var a=t.content;Zn();var u=formatChatroomMsg(null==a?void 0:a.chatroomMsg,this.core.account);this.logger.getDebugMode()?this.logger.debug("onChatroomMsgHandler::recvMsg",u.type,u.idClient,u):this.logger.log("onChatroomMsgHandler::recvMsg",u.type,u.idClient),this.core.emit("chatroomMsg",u),"out"!==u.flow&&this.doMsgReceiveReport(u,t)},a.doMsgReceiveReport=function doMsgReceiveReport(t,a){if(t.from!==this.core.account){var u=get(t,"__clientExt.statistics.apiCallingTime")||0,_=get(t,"__clientExt.statistics.sendTime")||0,h=get(t,"__clientExt.statistics.attachUploadDuration")||0,m=this.core.timeOrigin.getNTPTime(),E=t.time,g=this.core.timeOrigin.checkNodeReliable(a.__receiveTimeNode)?this.core.timeOrigin.getNTPTime(a.__receiveTimeNode):m;this.core.reporter.report("msgReceive",{msgId:"",clientId:t.idClient,serverTime:E,receiveTime:g,fromAccid:t.from,toAccid:this.core.account,type:4,roomId:t.chatroomId,apiCallingTime:u,sendTime:_,attachUploadDuration:h,callbackTime:m,preHandleTime:m,result:200,failReason:"",rt:m-E})}},a.sendMsg=function sendMsg(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee6(){var a,u,_,h,m;return Kl.wrap((function _callee6$(E){for(;;)switch(E.prev=E.next){case 0:validate({type:{type:"enum",values:getEnumKeys(wu)},subType:{type:"number",min:1,required:!1}},t),a=generatorMsgForCmd(t,this.core.account,this.core.config.deviceId),u=formatChatroomMsg(bt(bt({},a),{status:"sending",time:(new Date).getTime()}),this.core.account);try{t.onSendBefore&&t.onSendBefore(u)}catch(t){this.core.logger.error("sendMsg: options.onSendBefore error",t)}return E.prev=4,E.next=7,this.core.sendCmd("sendChatroomMsg",{chatroomMsg:a});case 7:return _=E.sent,h=formatChatroomMsg(bt(bt(bt({},a),_.content.chatroomMsg),{status:"success"}),this.core.account),this.core.reporter.report("msgSend",{msgId:"",clientId:h.idClient,msgTime:h.time,fromAccid:this.core.account,toAccid:"",type:4,roomId:h.chatroomId,tid:"",result:200,failReason:"",rt:Zn()-u.time}),E.abrupt("return",h);case 13:throw E.prev=13,E.t0=E.catch(4),m=formatChatroomMsg(bt(bt({},a),{status:"fail"}),this.core.account),E.t0.msg=m,this.core.reporter.report("msgSend",{msgId:"",clientId:m.idClient,fromAccid:this.core.account,toAccid:"",type:4,roomId:m.chatroomId,tid:"",result:null===E.t0||void 0===E.t0?void 0:E.t0.code,failReason:(null===E.t0||void 0===E.t0?void 0:E.t0.message)||"",rt:Zn()-a.time}),E.t0;case 19:case"end":return E.stop()}}),_callee6,this,[[4,13]])})))},ChatroomMsgService}(Au);!function(t){t[t.regular=0]="regular",t[t.temp=1]="temp",t[t.regularOnline=2]="regularOnline",t[t.regularReverse=3]="regularReverse"}(ld||(ld={}));var hd=function(t){function ChatroomMemberService(a){return t.call(this,"chatroomMember",a)||this}Mt(ChatroomMemberService,t);var a=ChatroomMemberService.prototype;return a.updateMyRoomRole=function updateMyRoomRole(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){return Kl.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return validate({member:{type:"object",rules:{nick:{type:"string",required:!1},avatar:{type:"string",required:!1},ext:{type:"string",required:!1}}},needNotify:{type:"boolean"},ext:{type:"string",required:!1},needSave:{type:"boolean",required:!1}},t),a.next=3,this.core.sendCmd("chatroomUpdateMyRoomRole",bt(bt({},t),{chatroomMember:t.member,ext:t.ext||""}));case 3:case"end":return a.stop()}}),_callee,this)})))},a.setMemberManager=function setMemberManager(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){return Kl.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.markChatroomMember(bt(bt({},t),{type:1}));case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee2,this)})))},a.setMemberNormal=function setMemberNormal(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee3(){return Kl.wrap((function _callee3$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.markChatroomMember(bt(bt({},t),{type:2}));case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee3,this)})))},a.setMemberMute=function setMemberMute(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee4(){return Kl.wrap((function _callee4$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.markChatroomMember(bt(bt({},t),{type:-2}));case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee4,this)})))},a.setMemberBlackList=function setMemberBlackList(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee5(){return Kl.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.markChatroomMember(bt(bt({},t),{type:-1}));case 2:return a.abrupt("return",a.sent);case 3:case"end":return a.stop()}}),_callee5,this)})))},a.queryMembers=function queryMembers(t){var a;return __awaiter(this,void 0,void 0,Kl.mark((function _callee6(){var u;return Kl.wrap((function _callee6$(_){for(;;)switch(_.prev=_.next){case 0:return validate({type:{type:"enum",values:getEnumKeys(ld)},time:{type:"number",required:!1},limit:{type:"number",required:!1}},t),_.next=3,this.core.sendCmd("chatroomQueryMembers",bt(bt({time:0,limit:100},t),{type:ld[t.type]}));case 3:return u=_.sent,_.abrupt("return",formatChatroomMembers(null===(a=u.content)||void 0===a?void 0:a.members));case 5:case"end":return _.stop()}}),_callee6,this)})))},a.queryMembersByAccounts=function queryMembersByAccounts(t){var a;return __awaiter(this,void 0,void 0,Kl.mark((function _callee7(){var u;return Kl.wrap((function _callee7$(_){for(;;)switch(_.prev=_.next){case 0:return validate({accounts:{type:"array",min:1,max:20,itemType:"string"}},t),_.next=3,this.core.sendCmd("chatroomQueryMembersByAccounts",t);case 3:return u=_.sent,_.abrupt("return",formatChatroomMembers(null===(a=u.content)||void 0===a?void 0:a.members));case 5:case"end":return _.stop()}}),_callee7,this)})))},a.setMemberTempMute=function setMemberTempMute(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee8(){return Kl.wrap((function _callee8$(a){for(;;)switch(a.prev=a.next){case 0:return validate({account:{type:"string",allowEmpty:!1},duration:{type:"number",min:0},needNotify:{type:"boolean",required:!1},ext:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("chatroomSetMemberTempMute",bt(bt({},t),{ext:t.ext||""}));case 3:case"end":return a.stop()}}),_callee8,this)})))},a.kickMember=function kickMember(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee9(){return Kl.wrap((function _callee9$(a){for(;;)switch(a.prev=a.next){case 0:return validate({account:{type:"string",allowEmpty:!1},ext:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("chatroomKickMember",bt(bt({},t),{ext:t.ext||""}));case 3:case"end":return a.stop()}}),_callee9,this)})))},a.queryMembersCountByTag=function queryMembersCountByTag(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee10(){var a;return Kl.wrap((function _callee10$(u){for(;;)switch(u.prev=u.next){case 0:return validate({tag:{type:"string",allowEmpty:!1}},t),u.next=3,this.core.sendCmd("chatroomQueryMembersCountByTag",bt({},t));case 3:return a=u.sent,u.abrupt("return",a.content.count);case 5:case"end":return u.stop()}}),_callee10,this)})))},a.queryMembersByTag=function queryMembersByTag(t){var a;return __awaiter(this,void 0,void 0,Kl.mark((function _callee11(){var u;return Kl.wrap((function _callee11$(_){for(;;)switch(_.prev=_.next){case 0:return validate({tag:{type:"string",allowEmpty:!1},time:{type:"number",required:!1},limit:{type:"number",max:100,required:!1}},t),_.next=3,this.core.sendCmd("chatroomQueryMembersByTag",{chatroomTagMemberReq:bt({time:0,limit:100},t)});case 3:return u=_.sent,_.abrupt("return",formatChatroomMembers(null===(a=u.content)||void 0===a?void 0:a.members));case 5:case"end":return _.stop()}}),_callee11,this)})))},a.setMembersTempMuteByTag=function setMembersTempMuteByTag(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee12(){return Kl.wrap((function _callee12$(a){for(;;)switch(a.prev=a.next){case 0:return validate({tag:{type:"string",allowEmpty:!1},duration:{type:"number",min:0},needNotify:{type:"boolean"},notifyTargetTags:{type:"string",allowEmpty:!1,required:!1},ext:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("chatroomSetMembersTempMuteByTag",{chatroomTagMuteReq:bt(bt({},t),{needNotify:t.needNotify?1:0,ext:t.ext||""})});case 3:case"end":return a.stop()}}),_callee12,this)})))},a.markChatroomMember=function markChatroomMember(t){var a;return __awaiter(this,void 0,void 0,Kl.mark((function _callee13(){var u;return Kl.wrap((function _callee13$(_){for(;;)switch(_.prev=_.next){case 0:return validate({account:{type:"string"},isAdd:{type:"boolean"},ext:{type:"string",required:!1},level:{type:"number",required:!1}},t),_.next=3,this.core.sendCmd("markChatroomMember",bt(bt({level:0},t),{ext:t.ext||""}));case 3:return u=_.sent,_.abrupt("return",formatChatroomMember(null===(a=u.content)||void 0===a?void 0:a.chatroomMember));case 5:case"end":return _.stop()}}),_callee13,this)})))},ChatroomMemberService}(Au),md=function(t){function ChatroomQueueService(a){return t.call(this,"chatroomQueue",a)||this}Mt(ChatroomQueueService,t);var a=ChatroomQueueService.prototype;return a.init=function init(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){return Kl.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:return validate({maxItemCount:{type:"number",min:0,max:1e3}},t),a.next=3,this.core.sendCmd("chatroomQueueInit",t);case 3:case"end":return a.stop()}}),_callee,this)})))},a.update=function update(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){return Kl.wrap((function _callee2$(a){for(;;)switch(a.prev=a.next){case 0:return validate({elementKey:{type:"string",allowEmpty:!1},elementValue:{type:"string",max:4096,allowEmpty:!1},transient:{type:"boolean",required:!1},account:{type:"string",required:!1}},t),a.next=3,this.core.sendCmd("chatroomQueueOffer",bt(bt({},t),{transient:!!t.transient}));case 3:case"end":return a.stop()}}),_callee2,this)})))},a.poll=function poll(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee3(){var a;return Kl.wrap((function _callee3$(u){for(;;)switch(u.prev=u.next){case 0:return validate({key:{type:"string",required:!1}},t),t.key||(t.key=""),u.next=4,this.core.sendCmd("chatroomQueuePoll",t||{});case 4:return a=u.sent,u.abrupt("return",a.content);case 6:case"end":return u.stop()}}),_callee3,this)})))},a.batchUpdate=function batchUpdate(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee4(){var a;return Kl.wrap((function _callee4$(u){for(;;)switch(u.prev=u.next){case 0:return validate({itemList:{type:"object"},needNotify:{type:"boolean",required:!1},notifyExtension:{type:"string",required:!1}},t),u.next=3,this.core.sendCmd("chatroomQueueChange",bt(bt({},t),{needNotify:!!t.needNotify}));case 3:return a=u.sent,u.abrupt("return",a.content.invalidKeyList);case 5:case"end":return u.stop()}}),_callee4,this)})))},a.fetch=function fetch(){return __awaiter(this,void 0,void 0,Kl.mark((function _callee5(){var t;return Kl.wrap((function _callee5$(a){for(;;)switch(a.prev=a.next){case 0:return a.next=2,this.core.sendCmd("chatroomQueueList");case 2:return t=a.sent,a.abrupt("return",t.content.itemList);case 4:case"end":return a.stop()}}),_callee5,this)})))},a.pickHeader=function pickHeader(){return __awaiter(this,void 0,void 0,Kl.mark((function _callee6(){var t,a,u,_;return Kl.wrap((function _callee6$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,this.core.sendCmd("chatroomQueuePeak");case 2:return a=h.sent,u=a.content.elementKey,_=a.content.elementValue,h.abrupt("return",((t={})[u]=_,t));case 6:case"end":return h.stop()}}),_callee6,this)})))},a.clear=function clear(){return __awaiter(this,void 0,void 0,Kl.mark((function _callee7(){return Kl.wrap((function _callee7$(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,this.core.sendCmd("chatroomQueueDrop");case 2:case"end":return t.stop()}}),_callee7,this)})))},ChatroomQueueService}(Au),Ed={"1_2":"heartbeat","6_23":"getServerTime","13_2":"chatroomLogin","13_3":"beKickedFromChatroom","13_4":"chatroomExit","13_6":"sendChatroomMsg","13_7":"onChatroomMsg","13_8":"chatroomQueryMembers","13_9":"chatroomQueryMessageHistory","13_11":"markChatroomMember","13_12":"closeChatroom","13_13":"getChatroom","13_14":"updateChatroom","13_15":"chatroomUpdateMyRoomRole","13_16":"chatroomQueryMembersByAccounts","13_17":"chatroomKickMember","13_19":"chatroomSetMemberTempMute","13_20":"chatroomQueueOffer","13_21":"chatroomQueuePoll","13_22":"chatroomQueueList","13_23":"chatroomQueuePeak","13_24":"chatroomQueueDrop","13_25":"chatroomQueueInit","13_26":"chatroomQueueChange","13_30":"chatroomSetMembersTempMuteByTag","13_31":"chatroomQueryMembersByTag","13_32":"chatroomQueryMembersCountByTag","13_36":"getHistoryMsgsByTags","13_101":"updateChatroomTag"},gd={chatroomLogin:{appkey:1,account:2,deviceId:3,chatroomId:5,appLogin:8,chatroomNick:20,chatroomAvatar:21,chatroomExt:22,chatroomEnterExt:23,clientSession:26,isAnonymous:38,tags:39,notifyTargetTags:40,authType:41,loginExt:42},chatroomIMLogin:{clientType:3,os:4,sdkVersion:6,appLogin:8,protocolVersion:9,pushTokenName:10,pushToken:11,deviceId:13,appkey:18,account:19,browser:24,clientSession:26,deviceInfo:32,token:1e3,customTag:38,customClientType:39,sdkHumanVersion:40,hostEnv:41,userAgent:42,libEnv:44,isReactNative:112,authType:115,loginExt:116},chatroomMsg:{idClient:1,type:2,body:3,ext:4,resend:5,userUpdateTime:6,fromNick:7,fromAvatar:8,fromExt:9,needAntiSpam:10,antiSpamContent:11,skipHistory:12,messageBody:13,antiSpamBusinessId:14,clientAntiSpam:15,antiSpamUsingYidun:16,time:20,from:21,chatroomId:22,fromClientType:23,highPriority:25,callbackExt:27,subType:28,yidunAntiCheating:29,env:30,notifyTargetTags:31,yidunAntiSpamExt:32,yidunAntiSpamRes:33,__clientExt:{id:39,converter:function objectToJSONString(t){if(t&&"object"==typeof t)try{return fc(t)}catch(t){return}},retConverter:function stringToJSONObject(t){if(t&&"string"==typeof t)try{return JSON.parse(t)}catch(t){return}}}},chatroom:{id:1,name:3,announcement:4,broadcastUrl:5,ext:12,createTime:14,updateTime:15,queuelevel:16,creator:100,onlineMemberNum:101,mute:102},chatroomMember:{chatroomId:1,account:2,type:3,level:4,nick:5,avatar:6,ext:7,online:8,guest:9,enterTime:10,blacked:12,muted:13,valid:14,updateTime:15,tempMuted:16,tempMuteDuration:17},chatroomTagMemberReq:{tag:1,time:2,limit:3},chatroomTagMuteReq:{tag:1,duration:2,needNotify:3,ext:4,notifyTargetTags:5},chatroomCdnInfo:{enable:1,cdnUrls:2,timestamp:3,interval:4,decryptType:5,decryptKey:6,timeout:7},chatRoomTagHistoryMsgRequestTag:{tags:1,types:2,fromTime:3,toTime:4,limit:5,reverse:6},updateTags:{currentTags:1}},vd=function getDeserializeTag(){return function invertSerializeMap(t){var a,u={};return forEach$1(a=qa(t)).call(a,(function(a){u[a]=function invertSerializeItem(t){var a={};for(var u in t){var _=t[u];"number"==typeof _?a[_]=u:"object"==typeof _&&(a[_.id]={prop:u,type:_.retType,access:_.retAccess?_.retAccess:_.access?_.access:u,def:_.retDef,converter:_.retConverter})}return a}(t[a])})),u}(gd)},Td=function getCmdConfig(){var t=vd();return{heartbeat:{sid:1,cid:2,service:"chatroom"},getServerTime:{sid:6,cid:23,service:"chatroom",response:[{type:"Long",name:"time"}]},chatroomLogin:{sid:13,cid:2,service:"chatroom",params:[{type:"Byte",name:"type"},{type:"Property",name:"chatroomLogin",reflectMapper:gd.chatroomLogin},{type:"Property",name:"chatroomIMLogin",reflectMapper:gd.chatroomIMLogin}],response:[{type:"Property",name:"chatroom",reflectMapper:t.chatroom},{type:"Property",name:"chatroomMember",reflectMapper:t.chatroomMember},{type:"Property",name:"chatroomCdnInfo",reflectMapper:t.chatroomCdnInfo}]},chatroomExit:{sid:13,cid:4,service:"chatroom"},sendChatroomMsg:{sid:13,cid:6,service:"chatroomMsg",params:[{type:"Property",name:"chatroomMsg",reflectMapper:gd.chatroomMsg}],response:[{type:"Property",name:"chatroomMsg",reflectMapper:t.chatroomMsg}]},chatroomQueryMessageHistory:{sid:13,cid:9,service:"chatroomMsg",params:[{type:"Long",name:"timetag"},{type:"Int",name:"limit"},{type:"Bool",name:"reverse"},{type:"LongArray",name:"msgTypes"}],response:[{type:"PropertyArray",name:"chatroomMsgs",reflectMapper:t.chatroomMsg}]},getHistoryMsgsByTags:{sid:13,cid:36,service:"chatroomMsg",params:[{type:"Property",name:"chatRoomTagHistoryMsgRequestTag",reflectMapper:gd.chatRoomTagHistoryMsgRequestTag}],response:[{type:"PropertyArray",name:"chatroomMsgs",reflectMapper:t.chatroomMsg}]},getChatroom:{sid:13,cid:13,service:"chatroom",response:[{type:"Property",name:"chatroom",reflectMapper:t.chatroom}]},updateChatroom:{sid:13,cid:14,service:"chatroom",params:[{type:"Property",name:"chatroom",reflectMapper:gd.chatroom},{type:"Bool",name:"needNotify"},{type:"String",name:"ext"}]},chatroomUpdateMyRoomRole:{sid:13,cid:15,service:"chatroomMember",params:[{type:"Property",name:"chatroomMember",reflectMapper:gd.chatroomMember},{type:"Bool",name:"needNotify"},{type:"String",name:"ext"},{type:"Bool",name:"needSave"}]},markChatroomMember:{sid:13,cid:11,service:"chatroomMember",params:[{type:"String",name:"account"},{type:"Int",name:"type"},{type:"Bool",name:"isAdd"},{type:"Int",name:"level"},{type:"String",name:"ext"}],response:[{type:"Property",name:"chatroomMember",reflectMapper:t.chatroomMember}]},chatroomQueryMembers:{sid:13,cid:8,service:"chatroomMember",params:[{type:"Byte",name:"type"},{type:"Long",name:"time"},{type:"Int",name:"limit"}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.chatroomMember}]},chatroomQueryMembersByAccounts:{sid:13,cid:16,service:"chatroomMember",params:[{type:"StrArray",name:"accounts"}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.chatroomMember}]},chatroomKickMember:{sid:13,cid:17,service:"chatroomMember",params:[{type:"String",name:"account"},{type:"String",name:"ext"}]},chatroomSetMemberTempMute:{sid:13,cid:19,service:"chatroomMember",params:[{type:"String",name:"account"},{type:"Long",name:"duration"},{type:"Bool",name:"needNotify"},{type:"String",name:"ext"}]},closeChatroom:{sid:13,cid:12,service:"chatroom",params:[{type:"String",name:"ext"}]},chatroomQueryMembersCountByTag:{sid:13,cid:32,service:"chatroomMember",params:[{type:"String",name:"tag"}],response:[{type:"Long",name:"count"}]},chatroomQueryMembersByTag:{sid:13,cid:31,service:"chatroomMember",params:[{type:"Property",name:"chatroomTagMemberReq",reflectMapper:gd.chatroomTagMemberReq}],response:[{type:"PropertyArray",name:"members",reflectMapper:t.chatroomMember}]},chatroomSetMembersTempMuteByTag:{sid:13,cid:30,service:"chatroomMember",params:[{type:"Property",name:"chatroomTagMuteReq",reflectMapper:gd.chatroomTagMuteReq}]},chatroomQueueOffer:{sid:13,cid:20,service:"chatroomQueue",params:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"},{type:"Bool",name:"transient"},{type:"String",name:"account"}]},chatroomQueuePoll:{sid:13,cid:21,service:"chatroomQueue",params:[{type:"String",name:"key"}],response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},chatroomQueueChange:{sid:13,cid:26,service:"chatroomQueue",params:[{type:"StrStrMap",name:"itemList"},{type:"Bool",name:"needNotify"},{type:"String",name:"notifyExtension"}],response:[{type:"StrArray",name:"invalidKeyList"}]},chatroomQueueList:{sid:13,cid:22,service:"chatroomQueue",response:[{type:"KVArray",name:"itemList"}]},chatroomQueuePeak:{sid:13,cid:23,service:"chatroomQueue",response:[{type:"String",name:"elementKey"},{type:"String",name:"elementValue"}]},chatroomQueueDrop:{sid:13,cid:24,service:"chatroomQueue"},chatroomQueueInit:{sid:13,cid:25,service:"chatroomQueue",params:[{type:"Int",name:"maxItemCount"}]},onChatroomMsg:{sid:13,cid:7,service:"chatroomMsg",response:[{type:"Property",name:"chatroomMsg",reflectMapper:t.chatroomMsg}]},beKickedFromChatroom:{sid:13,cid:3,service:"auth",response:[{type:"Int",name:"reason"},{type:"String",name:"ext"}]},updateChatroomTag:{sid:13,cid:101,service:"chatroom",response:[{type:"Property",name:"updateTags",reflectMapper:t.updateTags}]}}},yd={user_id:"",trace_id:"",action:7,exception_service:6,duration:0,start_time:0,state:1,extension:[]},Id=function(){function ReporterHookLinkKeep(t,a){this.traceData=yd,this.core=t,this.traceData=bt({},yd,a),this.traceData.extension=[]}var t=ReporterHookLinkKeep.prototype;return t.reset=function reset(){this.traceData=bt({},yd),this.traceData.extension=[]},t.start=function start(){var t,a;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(a=null===(t=this.core.clientSocket)||void 0===t?void 0:t.socket)||void 0===a?void 0:a.sessionId)||"",this.traceData.start_time=(new Date).getTime()},t.update=function update(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var a,u,_;return Kl.wrap((function _callee$(h){for(;;)switch(h.prev=h.next){case 0:return h.next=2,Wl.net.getNetworkStatus();case 2:a=h.sent,u=a.net_type,_=a.net_connect,this.traceData.extension.push(bt({code:0,foreground:!0,foreg_backg_switch:!1,net_type:u,net_connect:_},t));case 6:case"end":return h.stop()}}),_callee,this)})))},t.end=function end(t){var a=this.traceData.extension[0],u=this.traceData.extension[1];if(a&&0===a.operation_type&&u&&1===u.operation_type){var _=a.net_type!==u.net_type||a.net_connect!==u.net_connect;if(t||!_)return this.traceData.duration=(new Date).getTime()-this.traceData.start_time,this.core.reporter.report("exceptions",this.traceData),void this.reset();this.reset()}else this.reset()},ReporterHookLinkKeep}(),Sd={user_id:"",trace_id:"",action:0,state:0,duration:0,start_time:0,offset:0,full_size:0,transferred_size:0,operation_type:0,remote_addr:""},Md="ReporterHook::setMonitorForResources:",Nd=function(){function ReporterHookCloudStorage(t,a){this.traceData=Sd,this.core=t,this.traceData=bt({},Sd,a)}var t=ReporterHookCloudStorage.prototype;return t.reset=function reset(){this.traceData=bt({},Sd)},t.start=function start(){var t,a;this.reset(),this.traceData.user_id=this.core.account,this.traceData.trace_id=(null===(a=null===(t=this.core.clientSocket)||void 0===t?void 0:t.socket)||void 0===a?void 0:a.sessionId)||"",this.traceData.start_time="timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Zn()},t.update=function update(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){return Kl.wrap((function _callee$(a){for(;;)switch(a.prev=a.next){case 0:if(this.traceData.user_id){a.next=2;break}return a.abrupt("return");case 2:this.core.logger.log(Md+" upload update",t),bt(this.traceData,t);case 4:case"end":return a.stop()}}),_callee,this)})))},t.end=function end(t){this.traceData.user_id&&(this.core.logger.log(Md+" upload end cause of "+t),this.traceData.state=t,this.traceData.duration=("timeOrigin"in this.core?this.core.timeOrigin.getNTPTime():Zn())-this.traceData.start_time,this.core.reporter.report("nim_sdk_resources",this.traceData),this.traceData=Sd)},ReporterHookCloudStorage}();function getIsDataReportEnable(t){var a,u,_=!0;return"boolean"==typeof(null===(a=null==t?void 0:t.reporterConfig)||void 0===a?void 0:a.enableCompass)?_=t.reporterConfig.enableCompass:"boolean"==typeof(null===(u=null==t?void 0:t.reporterConfig)||void 0===u?void 0:u.isDataReportEnable)&&(_=t.reporterConfig.isDataReportEnable),_}var Ad={debugLevel:"off",needReconnect:!0,reconnectionAttempts:****************,autoMarkRead:!0,isAbtestEnable:!0,abtestUrl:pu,abtestProjectKey:fu},Od=function(t){function Chatroom(a,u){var h;void 0===u&&(u={}),(h=t.call(this)||this).instanceName="Chatroom",h.status="unconnected",h.account="",h.eventBus=new mi,h.options={},h.chatroom={},h.chatroomMsg={},h.chatroomMember={},h.chatroomQueue={},h.cloudStorage={},h.logger=new uu(a.debugLevel,u.loggerConfig),h.setInitOptions(a),registerParser({cmdMap:Ed,cmdConfig:Td()}),h.otherOptions=u,h.timerManager=new Nu,h.adapters=new du(_(h)),h.timeOrigin=new Jl(_(h)),h.abtest=new hu(_(h),{isAbtestEnable:h.options.isAbtestEnable,abtestUrl:h.options.abtestUrl,abtestProjectKey:fu});var m="",E="";h.options.isFixedDeviceId?(m=Wl.localStorage.getItem("__CHATROOM_DEVC_ID__")||Vl(),E=Wl.localStorage.getItem("__CHATROOM_CLIENT_SESSION_ID__")||Vl(),Wl.localStorage.setItem("__CHATROOM_DEVC_ID__",m),Wl.localStorage.setItem("__CHATROOM_CLIENT_SESSION_ID__",E)):(m=Vl(),E=Vl()),h.config={timeout:8e3,deviceId:m,clientSession:E};var g=Wl.getSystemInfo(),T=function getCompassDataEndpoint(t,a){var u,_,h=null===(u=null==a?void 0:a.reporterConfig)||void 0===u?void 0:u.compassDataEndpoint,m=null===(_=null==a?void 0:a.reporterConfig)||void 0===_?void 0:_.reportConfigUrl;if(h)return h;if(m){var E=m.match(/^https:\/\/([^/]+)\/*/);return Dl(E)&&E.length>=1?"https://"+E[1]:(t.error("Invalid reportConfigUrl: "+m),_u)}return _u}(h.logger,h.otherOptions);return h.reporter=new rs(bt(bt({},T?{compassDataEndpoint:T}:{}),{isDataReportEnable:getIsDataReportEnable(h.otherOptions),common:{app_key:a.appkey,dev_id:h.config.deviceId,platform:"Web",sdk_ver:"10.8.30",env:"online",os_name:g.os,os_ver:g.osVer,lib_env:g.libEnv,host_env:g.hostEnv,host_env_ver:g.hostEnvVer,manufactor:g.manufactor,model:g.model,v2:!1},request:Wl.request,logger:h.logger,autoStart:!0})),h.reporterHookLinkKeep=new Id(_(h)),h.reporterHookCloudStorage=new Nd(_(h)),Wl.setLogger(h.logger),h.auth=new Gu(_(h)),h.V1NIMLoginService=h.auth,h.clientSocket=new Du(_(h)),h.chatroom=new Bu(_(h)),h.chatroomMsg=new _d(_(h)),h.chatroomMember=new hd(_(h)),h.chatroomQueue=new md(_(h)),h.cloudStorage=new fd(_(h),bt({storageKeyPrefix:h.instanceName},u.cloudStorageConfig)),Chatroom.instance=_(h),h.logger.log("Chatroom init, version ","10.8.30"," sdk version ",100830," appkey ",a.appkey),h}Mt(Chatroom,t),Chatroom.getInstance=function getInstance(t,a){if(!Chatroom.instance){if(t)return new Chatroom(t,a);throw new Error("Instance not exist, please input options")}if(t){if(Chatroom.instance.options.account===t.account&&Chatroom.instance.options.appkey===t.appkey)return Chatroom.instance.setOptions(t),Chatroom.instance;throw new Error("Unexpected login")}return Chatroom.instance};var a=Chatroom.prototype;return a.setOptions=function setOptions(t){if("object"==typeof t&&null!==t&&(Object.prototype.hasOwnProperty.call(t,"account")&&t.account!==this.options.account||Object.prototype.hasOwnProperty.call(t,"appkey")&&t.appkey!==this.options.appkey))throw new Error("chatroom::setOptions account and appkey is not allowed to reset");validate({token:{type:"string",required:!1},chatroomId:{type:"string",required:!1},chatroomAddresses:{type:"array",itemType:"string",min:1,required:!1},needReconnect:{type:"boolean",required:!1},reconnectionAttempts:{type:"number",required:!1},isAnonymous:{type:"boolean",required:!1},tags:{type:"array",itemType:"string",required:!1}},t),this.logger.log("chatroom::setOptions options is",t),this.options=bt(bt({},this.options),t)},a.setInitOptions=function setInitOptions(t){validate({account:{type:"string"},appkey:{type:"string"},token:{type:"string"},chatroomId:{type:"string"},chatroomAddresses:{type:"array",itemType:"string",min:1},needReconnect:{type:"boolean",required:!1},reconnectionAttempts:{type:"number",required:!1},isAnonymous:{type:"boolean",required:!1},tags:{type:"array",itemType:"string",required:!1}},t),this.logger&&this.logger.log("chatroom::setInitOptions options is",t),this.account=t.account,this.options=bt(bt({},Ad),t)},a.connect=function connect(t){return void 0===t&&(t={}),this.auth.login(t)},a.disconnect=function disconnect(){var t=this;switch(this.status){case"logined":return this.sendCmd("chatroomExit",void 0,{timeout:1e3}).then((function(){t.clientSocket.doDisconnect(Ru.ACTIVE,"UserActiveDisconnect")})).catch((function(a){t.logger.error("Instance::disconnect sendCmd:logout error",a),t.clientSocket.doDisconnect(Ru.ACTIVE,"UserActiveDisconnect")}));case"connected":case"connecting":case"waitReconnect":return this.clientSocket.doDisconnect(Ru.ACTIVE,"UserActiveDisconnect"),Jn.resolve();case"unconnected":case"destroyed":return Jn.resolve()}},a.destroy=function destroy(){var t=this;return Chatroom.instance=void 0,this.disconnect().then((function(){t.status="destroyed",t.removeAllListeners(),t.eventBus.removeAllListeners(),t.logger.destroy(),t.reporter.destroy(),t.timerManager.destroy(),t.connect=emptyFuncWithPromise,t.disconnect=emptyFuncWithPromise,t.destroy=emptyFuncWithPromise}))},a.sendCmd=function sendCmd(t,a,u){return this.clientSocket.sendCmd(t,a,u)},a.emit=function emit(a){var u=this;try{for(var _,h,m=Zn(),E=arguments.length,g=new Array(E>1?E-1:0),T=1;T<E;T++)g[T-1]=arguments[T];var I=(_=t.prototype.emit).call.apply(_,concat(h=[this,a]).call(h,g)),S=Zn()-m;return S>=10&&this.logger.warn("Core::emit event: "+a+" process takes: "+S+"ms"),I}catch(t){return this.logger.error("Core::emit event: "+a+". Error: "+t),hi((function(){throw u.logger.error("Core::emit throw error in setTimeout. event: "+a+". Error: "+t),t}),0),!1}},Chatroom}(mi);Od.sdkVersion=100830,Od.sdkVersionFormat="10.8.30";var Rd=createCommonjsModule((function(t,a){t.exports=function(){function object2String(t){if(t){var a,u="";return forEach$1(a=qa(t)).call(a,(function(a,_){u+=0===_?"?":"&",u+=a+"="+t[a]})),u}return""}var t=function(t){function V2NIMError(a,u,_,h){var m;return(m=t.call(this,_)||this).source=a,m.code=u,m.desc=_,m.detail=h||{},m}return Mt(V2NIMError,t),V2NIMError}(dc(Error));function request(a,u){void 0===u&&(u={dataType:"json",method:"GET",timeout:5e3});var _="text"===u.dataType?"text/plain; charset=UTF-8":"application/json; charset=UTF-8",h="GET"===u.method?object2String(u.params):"";return new Jn((function(m,E){if(window.XMLHttpRequest){var g,T=new XMLHttpRequest;if(T.onreadystatechange=function(){if(4===T.readyState)if(200===T.status){try{g=JSON.parse(T.response||"{}")}catch(t){g=T.response}m({status:T.status,data:g})}else hi((function(){E(new t(1,T.status,"readyState: "+T.readyState+"; statusText: "+T.statusText))}),0)},T.open(u.method,""+a+h),T.timeout=u.timeout||5e3,T.setRequestHeader("Content-Type",_),u.headers)for(var I in u.headers)T.setRequestHeader(I,u.headers[I]);T.ontimeout=function(a){E(new t(1,408,a&&a.message?a.message:"request timeout"))},T.send(fc(u.data))}else E(new t(2,10400,"request no suppout"))}))}return request}()})),Cd=Math.floor,mergeSort=function(t,a){var u=t.length,_=Cd(u/2);return u<8?insertionSort(t,a):merge(t,mergeSort(arraySliceSimple(t,0,_),a),mergeSort(arraySliceSimple(t,_),a),a)},insertionSort=function(t,a){for(var u,_,h=t.length,m=1;m<h;){for(_=m,u=t[m];_&&a(t[_-1],u)>0;)t[_]=t[--_];_!==m++&&(t[_]=u)}return t},merge=function(t,a,u,_){for(var h=a.length,m=u.length,E=0,g=0;E<h||g<m;)t[E+g]=E<h&&g<m?_(a[E],u[g])<=0?a[E++]:u[g++]:E<h?a[E++]:u[g++];return t},bd=mergeSort,Pd=K.match(/firefox\/(\d+)/i),xd=!!Pd&&+Pd[1],kd=/MSIE|Trident/.test(K),wd=K.match(/AppleWebKit\/(\d+)\./),Dd=!!wd&&+wd[1],Ld=[],Vd=O(Ld.sort),Ud=O(Ld.push),Fd=fails((function(){Ld.sort(void 0)})),Gd=fails((function(){Ld.sort(null)})),Bd=arrayMethodIsStrict("sort"),Yd=!fails((function(){if(Q)return Q<70;if(!(xd&&xd>3)){if(kd)return!0;if(Dd)return Dd<603;var t,a,u,_,h="";for(t=65;t<76;t++){switch(a=String.fromCharCode(t),t){case 66:case 69:case 70:case 72:u=3;break;case 68:case 71:u=4;break;default:u=2}for(_=0;_<47;_++)Ld.push({k:a+_,v:u})}for(Ld.sort((function(t,a){return a.v-t.v})),_=0;_<Ld.length;_++)a=Ld[_].k.charAt(0),h.charAt(h.length-1)!==a&&(h+=a);return"DGBEFHACIJK"!==h}}));_export({target:"Array",proto:!0,forced:Fd||!Gd||!Bd||!Yd},{sort:function sort(t){void 0!==t&&aCallable(t);var a=toObject(this);if(Yd)return void 0===t?Vd(a):Vd(a,t);var u,_,h=[],m=lengthOfArrayLike(a);for(_=0;_<m;_++)_ in a&&Ud(h,a[_]);for(bd(h,function(t){return function(a,u){return void 0===u?-1:void 0===a?1:void 0!==t?+t(a,u)||0:toString(a)>toString(u)?1:-1}}(t)),u=lengthOfArrayLike(h),_=0;_<u;)a[_]=h[_++];for(;_<m;)deletePropertyOrThrow(a,_++);return a}});var jd=entryVirtual("Array").sort,Hd=Array.prototype,sort=function(t){var a=t.sort;return t===Hd||H(Hd,t)&&a===Hd.sort?jd:a},Kd=createCommonjsModule((function(t,a){self,t.exports=function(){var t={d:function d(a,u){for(var _ in u)t.o(u,_)&&!t.o(a,_)&&Ea(a,_,{enumerable:!0,get:u[_]})},o:function o(t,a){return Object.prototype.hasOwnProperty.call(t,a)}},a={};t.d(a,{default:function _default(){return M}});var u=function e(t){for(var a in function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.directUploadAddr="https://wanproxy-web.127.net",this.retryCount=4,this.trunkSize=4194304,this.trunkUploadTimeout=5e4,this.getOffsetTimeout=1e4,this.version="1.0",this.enableCache=!0,this.logger=console,this.onError=function(t){},this.onProgress=function(t){},this.onUploadProgress=function(t){},this.onComplete=function(t){},t)this[a]=t[a]};function n(t,a){var u=void 0!==_a&&Bl(t)||t["@@iterator"];if(!u){if(Dl(t)||(u=function(t,a){if(t){var u;if("string"==typeof t)return r(t,a);var _=slice(u=Object.prototype.toString.call(t)).call(u,8,-1);return"Object"===_&&t.constructor&&(_=t.constructor.name),"Map"===_||"Set"===_?Gl(t):"Arguments"===_||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(_)?r(t,a):void 0}}(t))||a&&t&&"number"==typeof t.length){u&&(t=u);var _=0,h=function i(){};return{s:h,n:function n(){return _>=t.length?{done:!0}:{done:!1,value:t[_++]}},e:function e(t){throw t},f:h}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var m,E=!0,g=!1;return{s:function s(){u=u.call(t)},n:function n(){var t=u.next();return E=t.done,t},e:function e(t){g=!0,m=t},f:function f(){try{E||null==u.return||u.return()}finally{if(g)throw m}}}}function r(t,a){(null==a||a>t.length)&&(a=t.length);for(var u=0,_=new Array(a);u<a;u++)_[u]=t[u];return _}var _={privateObj:{},setItem:function setItem(t,a){_.privateObj[t]=a},getItem:function getItem(t){return _.privateObj[t]},removeItem:function removeItem(t){delete _.privateObj[t]},getKeys:function getKeys(){return qa(_.privateObj)}},h={getFileKey:function getFileKey(t){var a=t.size.toString(),u=t.lastModified.toString();return"_NosUploader_"+t.name+slice(a).call(a,a.length-5)+slice(u).call(u,u.length-5)},getFileInfo:function getFileInfo(t){var a=_.getItem(t);if(!a)return null;try{return JSON.parse(a)}catch(t){return null}},initFile:function initFile(t,a,u){h.clearExpiredInfo();var m=this.getFileKey(a),E={ctx:void 0!==t.ctx?t.ctx:"",bucket:t.bucketName,obj:t.objectName,token:t.token,modifyAt:Zn(),end:!1};return t.payload&&(E.payload=t.payload),u&&_.setItem(m,fc(E)),m},setUploadContext:function setUploadContext(t,a,u){var h=this.getFileInfo(t);h&&(h.ctx=a,u&&_.setItem(t,fc(h)))},setComplete:function setComplete(t,a){var u=this.getFileInfo(t);u&&(u.modifyAt=Zn(),u.end=!0,a&&_.setItem(t,fc(u)))},getUploadContext:function getUploadContext(t){var a=this.getFileInfo(t);return a?a.ctx:""},removeFileInfo:function removeFileInfo(t){0===indexOf(t).call(t,"_NosUploader_")&&_.removeItem(t)},clearExpiredInfo:function clearExpiredInfo(){var t,a="function"==typeof _.getKeys?_.getKeys():qa(_),u=Zn(),m=[],E=n(a);try{for(E.s();!(t=E.n()).done;){var g=t.value;if(0===indexOf(g).call(g,"_NosUploader_")){var T=h.getFileInfo(g);null===T||u-T.modifyAt>M.expireTime?_.removeItem(g):m.push({fileInfo:T,key:g})}}}catch(t){E.e(t)}finally{E.f()}if(m.length>M.maxFileCache){var I,S,N=n(slice(I=sort(m).call(m,(function(t,a){return a.fileInfo.modifyAt-t.fileInfo.modifyAt}))).call(I,M.maxFileCache));try{for(N.s();!(S=N.n()).done;){var A,O=S.value;0===indexOf(A=O.key).call(A,"_NosUploader_")&&_.removeItem(O.key)}}catch(t){N.e(t)}finally{N.f()}}}},m=h;function c(t){return(c="function"==typeof _a&&"symbol"==typeof Da?function(t){return typeof t}:function(t){return t&&"function"==typeof _a&&t.constructor===_a&&t!==_a.prototype?"symbol":typeof t})(t)}function s(t,a){return!a||"object"!==c(a)&&"function"!=typeof a?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):a}function f(t){var a="function"==typeof Xs?new Xs:void 0;return(f=function f(t){var u,_;if(null===t||(_=t,-1===indexOf(u=Function.toString.call(_)).call(u,"[native code]")))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==a){if(a.has(t))return a.get(t);a.set(t,n)}function n(){return l(t,arguments,y(this).constructor)}return n.prototype=ut(t.prototype,{constructor:{value:n,enumerable:!1,writable:!0,configurable:!0}}),d(n,t)})(t)}function l(t,a,u){return(l=p()?sc:function(t,a,u){var _=[null];_.push.apply(_,a);var h=new(bind$1(Function).apply(t,_));return u&&d(h,u.prototype),h}).apply(null,arguments)}function p(){if("undefined"==typeof Reflect||!sc)return!1;if(sc.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(sc(Boolean,[],(function(){}))),!0}catch(t){return!1}}function d(t,a){return(d=_t||function(t,a){return t.__proto__=a,t})(t,a)}function y(t){return(y=_t?va:function(t){return t.__proto__||va(t)})(t)}var E=function(t){!function(t,a){if("function"!=typeof a&&null!==a)throw new TypeError("Super expression must either be null or a function");t.prototype=ut(a&&a.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),a&&d(t,a)}(r,t);var a,u,_=(a=r,u=p(),function(){var t,_=y(a);if(u){var h=y(this).constructor;t=sc(_,arguments,h)}else t=_.apply(this,arguments);return s(this,t)});function r(t,a){var u;return function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,r),(u=_.call(this,"NosUploadError:"+t)).errCode=a,u.errMsg=t,u}return r}(f(Error)),g=function e(t,a,u){if("uploading"===t.uploadState){var _=t.config,h=t.param,g=m.getUploadContext(t.fileKey);if(!g)return u(0);var T=new XMLHttpRequest,I=_.directUploadAddr+"/".concat(h.bucketName)+"/".concat(encodeURIComponent(h.objectName))+"?uploadContext"+"&context=".concat(g)+"&version=".concat(_.version);T.onreadystatechange=function(){var h;if("abort"!==t.uploadState&&4===T.readyState){var g,S,M,N;try{N=JSON.parse(T.responseText)}catch(t){N={errMsg:"JsonParseError in getOffset. xhr.status = "+T.status+". xhr.responseText: "+T.responseText,errCode:500}}200===T.status?N.errCode?t.config.onError(new E(N.errMsg,N.errCode)):u(N.offset):T.status.toString().match(/^5/)?e(t,a-1,u):a>0?("function"==typeof(null===(h=_.logger)||void 0===h?void 0:h.error)&&_.logger.error(concat(g="getOffset(".concat(I,") error. retry after 3 seconds. ")).call(g,(new Date).toTimeString())),hi((function(){e(t,a-1,u)}),3500)):T.status?(m.removeFileInfo(t.fileKey),_.onError(new E(concat(S=concat(M="getOffset(".concat(I,") error: ")).call(M,T.status," ")).call(S,T.statusText)))):_.onError(new E("getOffset(".concat(I,") error. no Error Code")))}},T.open("get",I),T.setRequestHeader("x-nos-token",h.token),T.timeout=_.getOffsetTimeout,T.send()}},T=function e(t,a,u,_){if("uploading"===t.uploadState){var h=t.param,g=t.config,T=slice(File.prototype),I=void 0!==h.ctx?h.ctx:"",S=a+g.trunkSize>=t.file.size,M=S?t.file.size:a+g.trunkSize,N=new XMLHttpRequest,A=g.directUploadAddr+"/".concat(h.bucketName)+"/".concat(encodeURIComponent(h.objectName));if(N.upload.onprogress=function(u){if("abort"!==t.uploadState){var _=0;u.lengthComputable?(_=(a+u.loaded)/t.file.size,g.onProgress(_),g.onUploadProgress({loaded:u.loaded,total:t.file.size,percentage:_,percentageText:(100*_).toFixed(2)+"%"})):g.onError(new E("browser does not support query upload progress"))}},N.onreadystatechange=function(){var h,T;if("abort"!==t.uploadState&&4===N.readyState){var I,M,O,R;try{R=JSON.parse(N.responseText)}catch(t){"function"==typeof(null===(h=g.logger)||void 0===h?void 0:h.error)&&g.logger.error("JsonParseError in uploadTrunk. xhr.status = "+N.status+". xhr.responseText: "+N.responseText,t),R={errMsg:"JsonParseError in uploadTrunk. xhr.status = "+N.status+". xhr.responseText: "+N.responseText}}200===N.status?(t.setContext(R.context),S?(_(),t.setComplete()):e(t,R.offset,g.retryCount,_)):N.status.toString().match(/^5/)?u>0?e(t,a,u-1,_):(m.removeFileInfo(t.fileKey),g.onError(new E(R.errMsg,R.errCode))):u>0?("function"==typeof(null===(T=g.logger)||void 0===T?void 0:T.error)&&g.logger.error(concat(I="uploadTrunk(".concat(A,") error. retry after 3 seconds. ")).call(I,(new Date).toTimeString())),hi((function(){e(t,a,u-1,_)}),3500)):N.status?(m.removeFileInfo(t.fileKey),g.onError(new E(concat(M=concat(O="uploadTrunk(".concat(A,") error: ")).call(O,N.status," ")).call(M,N.statusText)))):g.onError(new E("uploadTrunk(".concat(A,") error. no Error Code. Please check your network")))}},N.open("post",A+"?offset=".concat(a)+"&complete=".concat(S)+"&context=".concat(I)+"&version=".concat(g.version)),N.setRequestHeader("x-nos-token",h.token),h.md5&&N.setRequestHeader("content-md5",h.md5),t.file.type&&N.setRequestHeader("content-type",t.file.type),N.timeout=g.trunkUploadTimeout,"undefined"!=typeof FileReader){var O=new FileReader;O.addEventListener("load",(function(t){var a;(null===(a=null==t?void 0:t.target)||void 0===a?void 0:a.result)instanceof ArrayBuffer&&t.target.result.byteLength>0?N.send(t.target.result):g.onError(new E("Read ArrayBuffer failed",194003))})),O.addEventListener("error",(function(t){var a=t.target.error;g.onError(new E("Read ArrayBuffer error. ".concat(a.toString()),194003))})),O.readAsArrayBuffer(T.call(t.file,a,M))}else N.send(T.call(t.file,a,M))}};function v(t,a){for(var u=0;u<a.length;u++){var _=a[u];_.enumerable=_.enumerable||!1,_.configurable=!0,"value"in _&&(_.writable=!0),Ea(t,_.key,_)}}var I=function(){function e(t,a,u){!function(t,a){if(!(t instanceof a))throw new TypeError("Cannot call a class as a function")}(this,e),this.uploadState="paused",this.config=u,this.file=t,this.param=a,this.fileKey=m.initFile(a,t,this.config.enableCache),this.resume()}var t;return(t=[{key:"resume",value:function value(){var t=this;if("uploading"!==this.uploadState){this.setUploadState("uploading");var a=this.config;g(this,a.retryCount,(function(u){T(t,u,a.retryCount,(function(){t.setUploadState("ended"),"function"==typeof a.onComplete&&a.onComplete(t.param)}))}))}}},{key:"pause",value:function value(){this.setUploadState("paused")}},{key:"abort",value:function value(){"ended"!==this.uploadState&&"abort"!==this.uploadState&&(this.setUploadState("abort"),this.config.onError(new E("Upload Aborted",10499)))}},{key:"setUploadState",value:function value(t){t!==this.uploadState&&(this.uploadState=t)}},{key:"setContext",value:function value(t){m.setUploadContext(this.fileKey,t,this.config.enableCache),this.param.ctx=t}},{key:"setComplete",value:function value(){m.setComplete(this.fileKey,this.config.enableCache),this.setUploadState("ended")}}])&&v(e.prototype,t),e}(),S={maxFileCache:1/0,expireTime:864e5,getFileUploadInformation:function getFileUploadInformation(t){var a=m.getFileKey(t),u=m.getFileInfo(a);return null===u?null:Zn()-u.modifyAt>S.expireTime?(m.removeFileInfo(a),null):{uploadInfo:bt({bucketName:u.bucket,objectName:u.obj,token:u.token,ctx:u.ctx},u.payload?{payload:u.payload}:{}),complete:u.end}},setMaxFileCache:function setMaxFileCache(t){S.maxFileCache=t},setExpireTime:function setExpireTime(t){S.expireTime=t},printCaches:function printCaches(){if("undefined"!=typeof localStorage)for(var t=0,a=qa(localStorage);t<a.length;t++){var u=a[t],_=m.getFileInfo(u);_&&console.log(_,"modifiedAt",new Date(_.modifyAt).toTimeString())}},createConfig:function createConfig(t){return new u(t)},createTask:function createTask(t,a,u){return new I(t,a,u)}},M=S;return a.default}()})),Wd={debug:function debug(){},log:function log(){},warn:function warn(){},error:function error(){}};function setLogger(t){Wd=t}function isMobile(){if(!navigator||!navigator.userAgent)return!1;var t=[/Android/i,/webOS/i,/iPhone/i,/iPad/i,/iPod/i,/BlackBerry/i,/Windows Phone/i];return some(t).call(t,(function(t){return navigator.userAgent.match(t)}))}function isElectron(){var t;return!(!navigator||!navigator.userAgent)&&("object"==typeof navigator&&"string"==typeof navigator.userAgent&&indexOf(t=navigator.userAgent).call(t,"Electron")>=0)}function isBrowser(){return navigator&&navigator.userAgent}function uploadFileFn(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var a,u,_,h,m,E,g;return Kl.wrap((function _callee$(T){for(;;)switch(T.prev=T.next){case 0:if(h=Wd,t.fileInput||t.file){T.next=3;break}throw new Error("File not exist");case 3:if(!t.file){T.next=7;break}m=t.file,T.next=21;break;case 7:if("string"!=typeof t.fileInput){T.next=16;break}if(!((E=document.getElementById(t.fileInput))&&E.files&&E.files[0])){T.next=13;break}m=E.files[0],T.next=14;break;case 13:throw new Error("Can not get file from fileInput");case 14:T.next=21;break;case 16:if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0])){T.next=20;break}m=t.fileInput.files[0],T.next=21;break;case 20:throw new Error("Can not get file from fileInput "+t.fileInput);case 21:if(!(t.maxSize&&m.size>t.maxSize)){T.next=23;break}throw new Error("The file exceeds maxSize limit. maxSize: "+t.maxSize+", get "+m.size);case 23:return T.next=25,new Jn((function(a,u){var _,E=Kd.getFileUploadInformation(m),g=Kd.createConfig({enableCache:!0,retryCount:0,directUploadAddr:t.chunkUploadHost,onError:function onError(t){u(t)},onUploadProgress:t.onUploadProgress||function(){},onComplete:function onComplete(t){a(t)}});if(E)if(E.complete)t.onUploadProgress&&t.onUploadProgress({total:m.size,loaded:m.size,percentage:1,percentageText:"100%"}),a(E.uploadInfo);else{_=Kd.createTask(m,E.uploadInfo,g);try{t.onUploadStart&&t.onUploadStart(_)}catch(t){h.error("Adapter uploadFile: options.onUploadStart error",t&&t.message),_.abort(),u(t)}}else{_=Kd.createTask(m,bt(bt({bucketName:t.nosToken.bucket,objectName:decodeURIComponent(t.nosToken.objectName),token:t.nosToken.token},t.md5?{md5:t.md5}:{}),t.payload?{payload:t.payload}:{}),g);try{t.onUploadStart&&t.onUploadStart(_)}catch(t){h.error("Adapter uploadFile: options.onUploadStart error",t&&t.message),_.abort(),u(t)}}}));case 25:return(g=T.sent).name=m.name,g.size=m.size,g.type=m.type,g.ext=lastIndexOf(a=g.name).call(a,".")>-1?slice(u=g.name).call(u,lastIndexOf(_=g.name).call(_,".")+1).toLowerCase():"",T.abrupt("return",g);case 31:case"end":return T.stop()}}),_callee)})))}function getFileUploadInformationFn(t){var a;if(t.file)a=t.file;else if("string"==typeof t.fileInput){var u=document.getElementById(t.fileInput);if(!(u&&u.files&&u.files[0]))throw new Error("Can not get file from fileInput");a=u.files[0]}else{if(!(t.fileInput&&t.fileInput.files&&t.fileInput.files[0]))throw new Error("Can not get file from fileInput "+t.fileInput);a=t.fileInput.files[0]}return Kd.getFileUploadInformation(a)}
/*!
	 * Platform.js v1.3.6
	 * Copyright 2014-2020 Benjamin Tan
	 * Copyright 2011-2013 John-David Dalton
	 * Available under MIT license
	 */var qd=createCommonjsModule((function(a,u){(function(){var _={function:!0,object:!0}[typeof window]&&window||this,h=u,m=a&&!a.nodeType&&a,E=h&&m&&"object"==typeof t&&t;!E||E.global!==E&&E.window!==E&&E.self!==E||(_=E);var g=Math.pow(2,53)-1,T=/\bOpera/,I=Object.prototype,S=I.hasOwnProperty,M=I.toString;function capitalize(t){return(t=String(t)).charAt(0).toUpperCase()+t.slice(1)}function format(t){return t=trim(t),/^(?:webOS|i(?:OS|P))/.test(t)?t:capitalize(t)}function forOwn(t,a){for(var u in t)S.call(t,u)&&a(t[u],u,t)}function getClassOf(t){return null==t?capitalize(t):M.call(t).slice(8,-1)}function qualify(t){return String(t).replace(/([ -])(?!$)/g,"$1?")}function reduce(t,a){var u=null;return function each(t,a){var u=-1,_=t?t.length:0;if("number"==typeof _&&_>-1&&_<=g)for(;++u<_;)a(t[u],u,t);else forOwn(t,a)}(t,(function(_,h){u=a(u,_,h,t)})),u}function trim(t){return String(t).replace(/^ +| +$/g,"")}var N=function parse(t){var a=_,u=t&&"object"==typeof t&&"String"!=getClassOf(t);u&&(a=t,t=null);var h=a.navigator||{},m=h.userAgent||"";t||(t=m);var E,g,I=u?!!h.likeChrome:/\bChrome\b/.test(t)&&!/internal|\n/i.test(M.toString()),S="Object",N=u?S:"ScriptBridgingProxyObject",A=u?S:"Environment",O=u&&a.java?"JavaPackage":getClassOf(a.java),R=u?S:"RuntimeObject",C=/\bJava/.test(O)&&a.java,b=C&&getClassOf(a.environment)==A,P=C?"a":"α",x=C?"b":"β",k=a.document||{},w=a.operamini||a.opera,D=T.test(D=u&&w?w["[[Class]]"]:getClassOf(w))?D:w=null,L=t,V=[],U=null,G=t==m,B=G&&w&&"function"==typeof w.version&&w.version(),Y=function getLayout(a){return reduce(a,(function(a,u){return a||RegExp("\\b"+(u.pattern||qualify(u))+"\\b","i").exec(t)&&(u.label||u)}))}([{label:"EdgeHTML",pattern:"Edge"},"Trident",{label:"WebKit",pattern:"AppleWebKit"},"iCab","Presto","NetFront","Tasman","KHTML","Gecko"]),j=function getName(a){return reduce(a,(function(a,u){return a||RegExp("\\b"+(u.pattern||qualify(u))+"\\b","i").exec(t)&&(u.label||u)}))}(["Adobe AIR","Arora","Avant Browser","Breach","Camino","Electron","Epiphany","Fennec","Flock","Galeon","GreenBrowser","iCab","Iceweasel","K-Meleon","Konqueror","Lunascape","Maxthon",{label:"Microsoft Edge",pattern:"(?:Edge|Edg|EdgA|EdgiOS)"},"Midori","Nook Browser","PaleMoon","PhantomJS","Raven","Rekonq","RockMelt",{label:"Samsung Internet",pattern:"SamsungBrowser"},"SeaMonkey",{label:"Silk",pattern:"(?:Cloud9|Silk-Accelerated)"},"Sleipnir","SlimBrowser",{label:"SRWare Iron",pattern:"Iron"},"Sunrise","Swiftfox","Vivaldi","Waterfox","WebPositive",{label:"Yandex Browser",pattern:"YaBrowser"},{label:"UC Browser",pattern:"UCBrowser"},"Opera Mini",{label:"Opera Mini",pattern:"OPiOS"},"Opera",{label:"Opera",pattern:"OPR"},"Chromium","Chrome",{label:"Chrome",pattern:"(?:HeadlessChrome)"},{label:"Chrome Mobile",pattern:"(?:CriOS|CrMo)"},{label:"Firefox",pattern:"(?:Firefox|Minefield)"},{label:"Firefox for iOS",pattern:"FxiOS"},{label:"IE",pattern:"IEMobile"},{label:"IE",pattern:"MSIE"},"Safari"]),H=getProduct([{label:"BlackBerry",pattern:"BB10"},"BlackBerry",{label:"Galaxy S",pattern:"GT-I9000"},{label:"Galaxy S2",pattern:"GT-I9100"},{label:"Galaxy S3",pattern:"GT-I9300"},{label:"Galaxy S4",pattern:"GT-I9500"},{label:"Galaxy S5",pattern:"SM-G900"},{label:"Galaxy S6",pattern:"SM-G920"},{label:"Galaxy S6 Edge",pattern:"SM-G925"},{label:"Galaxy S7",pattern:"SM-G930"},{label:"Galaxy S7 Edge",pattern:"SM-G935"},"Google TV","Lumia","iPad","iPod","iPhone","Kindle",{label:"Kindle Fire",pattern:"(?:Cloud9|Silk-Accelerated)"},"Nexus","Nook","PlayBook","PlayStation Vita","PlayStation","TouchPad","Transformer",{label:"Wii U",pattern:"WiiU"},"Wii","Xbox One",{label:"Xbox 360",pattern:"Xbox"},"Xoom"]),K=function getManufacturer(a){return reduce(a,(function(a,u,_){return a||(u[H]||u[/^[a-z]+(?: +[a-z]+\b)*/i.exec(H)]||RegExp("\\b"+qualify(_)+"(?:\\b|\\w*\\d)","i").exec(t))&&_}))}({Apple:{iPad:1,iPhone:1,iPod:1},Alcatel:{},Archos:{},Amazon:{Kindle:1,"Kindle Fire":1},Asus:{Transformer:1},"Barnes & Noble":{Nook:1},BlackBerry:{PlayBook:1},Google:{"Google TV":1,Nexus:1},HP:{TouchPad:1},HTC:{},Huawei:{},Lenovo:{},LG:{},Microsoft:{Xbox:1,"Xbox One":1},Motorola:{Xoom:1},Nintendo:{"Wii U":1,Wii:1},Nokia:{Lumia:1},Oppo:{},Samsung:{"Galaxy S":1,"Galaxy S2":1,"Galaxy S3":1,"Galaxy S4":1},Sony:{PlayStation:1,"PlayStation Vita":1},Xiaomi:{Mi:1,Redmi:1}}),W=function getOS(a){return reduce(a,(function(a,u){var _=u.pattern||qualify(u);return!a&&(a=RegExp("\\b"+_+"(?:/[\\d.]+|[ \\w.]*)","i").exec(t))&&(a=function cleanupOS(t,a,u){var _={"10.0":"10",6.4:"10 Technical Preview",6.3:"8.1",6.2:"8",6.1:"Server 2008 R2 / 7","6.0":"Server 2008 / Vista",5.2:"Server 2003 / XP 64-bit",5.1:"XP",5.01:"2000 SP1","5.0":"2000","4.0":"NT","4.90":"ME"};return a&&u&&/^Win/i.test(t)&&!/^Windows Phone /i.test(t)&&(_=_[/[\d.]+$/.exec(t)])&&(t="Windows "+_),t=String(t),a&&u&&(t=t.replace(RegExp(a,"i"),u)),format(t.replace(/ ce$/i," CE").replace(/\bhpw/i,"web").replace(/\bMacintosh\b/,"Mac OS").replace(/_PowerPC\b/i," OS").replace(/\b(OS X) [^ \d]+/i,"$1").replace(/\bMac (OS X)\b/,"$1").replace(/\/(\d)/," $1").replace(/_/g,".").replace(/(?: BePC|[ .]*fc[ \d.]+)$/i,"").replace(/\bx86\.64\b/gi,"x86_64").replace(/\b(Windows Phone) OS\b/,"$1").replace(/\b(Chrome OS \w+) [\d.]+\b/,"$1").split(" on ")[0])}(a,_,u.label||u)),a}))}(["Windows Phone","KaiOS","Android","CentOS",{label:"Chrome OS",pattern:"CrOS"},"Debian",{label:"DragonFly BSD",pattern:"DragonFly"},"Fedora","FreeBSD","Gentoo","Haiku","Kubuntu","Linux Mint","OpenBSD","Red Hat","SuSE","Ubuntu","Xubuntu","Cygwin","Symbian OS","hpwOS","webOS ","webOS","Tablet OS","Tizen","Linux","Mac OS X","Macintosh","Mac","Windows 98;","Windows "]);function getProduct(a){return reduce(a,(function(a,u){var _=u.pattern||qualify(u);return!a&&(a=RegExp("\\b"+_+" *\\d+[.\\w_]*","i").exec(t)||RegExp("\\b"+_+" *\\w+-[\\w]*","i").exec(t)||RegExp("\\b"+_+"(?:; *(?:[a-z]+[_-])?[a-z]+\\d+|[^ ();-]*)","i").exec(t))&&((a=String(u.label&&!RegExp(_,"i").test(u.label)?u.label:a).split("/"))[1]&&!/[\d.]+/.test(a[0])&&(a[0]+=" "+a[1]),u=u.label||u,a=format(a[0].replace(RegExp(_,"i"),u).replace(RegExp("; *(?:"+u+"[_-])?","i")," ").replace(RegExp("("+u+")[-_.]?(\\w)","i"),"$1 $2"))),a}))}function getVersion(a){return reduce(a,(function(a,u){return a||(RegExp(u+"(?:-[\\d.]+/|(?: for [\\w-]+)?[ /-])([\\d.]+[^ ();/_-]*)","i").exec(t)||0)[1]||null}))}if(Y&&(Y=[Y]),/\bAndroid\b/.test(W)&&!H&&(E=/\bAndroid[^;]*;(.*?)(?:Build|\) AppleWebKit)\b/i.exec(t))&&(H=trim(E[1]).replace(/^[a-z]{2}-[a-z]{2};\s*/i,"")||null),K&&!H?H=getProduct([K]):K&&H&&(H=H.replace(RegExp("^("+qualify(K)+")[-_.\\s]","i"),K+" ").replace(RegExp("^("+qualify(K)+")[-_.]?(\\w)","i"),K+" $2")),(E=/\bGoogle TV\b/.exec(H))&&(H=E[0]),/\bSimulator\b/i.test(t)&&(H=(H?H+" ":"")+"Simulator"),"Opera Mini"==j&&/\bOPiOS\b/.test(t)&&V.push("running in Turbo/Uncompressed mode"),"IE"==j&&/\blike iPhone OS\b/.test(t)?(K=(E=parse(t.replace(/like iPhone OS/,""))).manufacturer,H=E.product):/^iP/.test(H)?(j||(j="Safari"),W="iOS"+((E=/ OS ([\d_]+)/i.exec(t))?" "+E[1].replace(/_/g,"."):"")):"Konqueror"==j&&/^Linux\b/i.test(W)?W="Kubuntu":K&&"Google"!=K&&(/Chrome/.test(j)&&!/\bMobile Safari\b/i.test(t)||/\bVita\b/.test(H))||/\bAndroid\b/.test(W)&&/^Chrome/.test(j)&&/\bVersion\//i.test(t)?(j="Android Browser",W=/\bAndroid\b/.test(W)?W:"Android"):"Silk"==j?(/\bMobi/i.test(t)||(W="Android",V.unshift("desktop mode")),/Accelerated *= *true/i.test(t)&&V.unshift("accelerated")):"UC Browser"==j&&/\bUCWEB\b/.test(t)?V.push("speed mode"):"PaleMoon"==j&&(E=/\bFirefox\/([\d.]+)\b/.exec(t))?V.push("identifying as Firefox "+E[1]):"Firefox"==j&&(E=/\b(Mobile|Tablet|TV)\b/i.exec(t))?(W||(W="Firefox OS"),H||(H=E[1])):!j||(E=!/\bMinefield\b/i.test(t)&&/\b(?:Firefox|Safari)\b/.exec(j))?(j&&!H&&/[\/,]|^[^(]+?\)/.test(t.slice(t.indexOf(E+"/")+8))&&(j=null),(E=H||K||W)&&(H||K||/\b(?:Android|Symbian OS|Tablet OS|webOS)\b/.test(W))&&(j=/[a-z]+(?: Hat)?/i.exec(/\bAndroid\b/.test(W)?W:E)+" Browser")):"Electron"==j&&(E=(/\bChrome\/([\d.]+)\b/.exec(t)||0)[1])&&V.push("Chromium "+E),B||(B=getVersion(["(?:Cloud9|CriOS|CrMo|Edge|Edg|EdgA|EdgiOS|FxiOS|HeadlessChrome|IEMobile|Iron|Opera ?Mini|OPiOS|OPR|Raven|SamsungBrowser|Silk(?!/[\\d.]+$)|UCBrowser|YaBrowser)","Version",qualify(j),"(?:Firefox|Minefield|NetFront)"])),(E=("iCab"==Y&&parseFloat(B)>3?"WebKit":/\bOpera\b/.test(j)&&(/\bOPR\b/.test(t)?"Blink":"Presto"))||/\b(?:Midori|Nook|Safari)\b/i.test(t)&&!/^(?:Trident|EdgeHTML)$/.test(Y)&&"WebKit"||!Y&&/\bMSIE\b/i.test(t)&&("Mac OS"==W?"Tasman":"Trident")||"WebKit"==Y&&/\bPlayStation\b(?! Vita\b)/i.test(j)&&"NetFront")&&(Y=[E]),"IE"==j&&(E=(/; *(?:XBLWP|ZuneWP)(\d+)/i.exec(t)||0)[1])?(j+=" Mobile",W="Windows Phone "+(/\+$/.test(E)?E:E+".x"),V.unshift("desktop mode")):/\bWPDesktop\b/i.test(t)?(j="IE Mobile",W="Windows Phone 8.x",V.unshift("desktop mode"),B||(B=(/\brv:([\d.]+)/.exec(t)||0)[1])):"IE"!=j&&"Trident"==Y&&(E=/\brv:([\d.]+)/.exec(t))&&(j&&V.push("identifying as "+j+(B?" "+B:"")),j="IE",B=E[1]),G){if(function isHostType(t,a){var u=null!=t?typeof t[a]:"number";return!(/^(?:boolean|number|string|undefined)$/.test(u)||"object"==u&&!t[a])}(a,"global"))if(C&&(L=(E=C.lang.System).getProperty("os.arch"),W=W||E.getProperty("os.name")+" "+E.getProperty("os.version")),b){try{B=a.require("ringo/engine").version.join("."),j="RingoJS"}catch(t){(E=a.system)&&E.global.system==a.system&&(j="Narwhal",W||(W=E[0].os||null))}j||(j="Rhino")}else"object"==typeof a.process&&!a.process.browser&&(E=a.process)&&("object"==typeof E.versions&&("string"==typeof E.versions.electron?(V.push("Node "+E.versions.node),j="Electron",B=E.versions.electron):"string"==typeof E.versions.nw&&(V.push("Chromium "+B,"Node "+E.versions.node),j="NW.js",B=E.versions.nw)),j||(j="Node.js",L=E.arch,W=E.platform,B=(B=/[\d.]+/.exec(E.version))?B[0]:null));else getClassOf(E=a.runtime)==N?(j="Adobe AIR",W=E.flash.system.Capabilities.os):getClassOf(E=a.phantom)==R?(j="PhantomJS",B=(E=E.version||null)&&E.major+"."+E.minor+"."+E.patch):"number"==typeof k.documentMode&&(E=/\bTrident\/(\d+)/i.exec(t))?(B=[B,k.documentMode],(E=+E[1]+4)!=B[1]&&(V.push("IE "+B[1]+" mode"),Y&&(Y[1]=""),B[1]=E),B="IE"==j?String(B[1].toFixed(1)):B[0]):"number"==typeof k.documentMode&&/^(?:Chrome|Firefox)\b/.test(j)&&(V.push("masking as "+j+" "+B),j="IE",B="11.0",Y=["Trident"],W="Windows");W=W&&format(W)}if(B&&(E=/(?:[ab]|dp|pre|[ab]\d+pre)(?:\d+\+?)?$/i.exec(B)||/(?:alpha|beta)(?: ?\d)?/i.exec(t+";"+(G&&h.appMinorVersion))||/\bMinefield\b/i.test(t)&&"a")&&(U=/b/i.test(E)?"beta":"alpha",B=B.replace(RegExp(E+"\\+?$"),"")+("beta"==U?x:P)+(/\d+\+?/.exec(E)||"")),"Fennec"==j||"Firefox"==j&&/\b(?:Android|Firefox OS|KaiOS)\b/.test(W))j="Firefox Mobile";else if("Maxthon"==j&&B)B=B.replace(/\.[\d.]+/,".x");else if(/\bXbox\b/i.test(H))"Xbox 360"==H&&(W=null),"Xbox 360"==H&&/\bIEMobile\b/.test(t)&&V.unshift("mobile mode");else if(!/^(?:Chrome|IE|Opera)$/.test(j)&&(!j||H||/Browser|Mobi/.test(j))||"Windows CE"!=W&&!/Mobi/i.test(t))if("IE"==j&&G)try{null===a.external&&V.unshift("platform preview")}catch(t){V.unshift("embedded")}else(/\bBlackBerry\b/.test(H)||/\bBB10\b/.test(t))&&(E=(RegExp(H.replace(/ +/g," *")+"/([.\\d]+)","i").exec(t)||0)[1]||B)?(W=((E=[E,/BB10/.test(t)])[1]?(H=null,K="BlackBerry"):"Device Software")+" "+E[0],B=null):this!=forOwn&&"Wii"!=H&&(G&&w||/Opera/.test(j)&&/\b(?:MSIE|Firefox)\b/i.test(t)||"Firefox"==j&&/\bOS X (?:\d+\.){2,}/.test(W)||"IE"==j&&(W&&!/^Win/.test(W)&&B>5.5||/\bWindows XP\b/.test(W)&&B>8||8==B&&!/\bTrident\b/.test(t)))&&!T.test(E=parse.call(forOwn,t.replace(T,"")+";"))&&E.name&&(E="ing as "+E.name+((E=E.version)?" "+E:""),T.test(j)?(/\bIE\b/.test(E)&&"Mac OS"==W&&(W=null),E="identify"+E):(E="mask"+E,j=D?format(D.replace(/([a-z])([A-Z])/g,"$1 $2")):"Opera",/\bIE\b/.test(E)&&(W=null),G||(B=null)),Y=["Presto"],V.push(E));else j+=" Mobile";(E=(/\bAppleWebKit\/([\d.]+\+?)/i.exec(t)||0)[1])&&(E=[parseFloat(E.replace(/\.(\d)$/,".0$1")),E],"Safari"==j&&"+"==E[1].slice(-1)?(j="WebKit Nightly",U="alpha",B=E[1].slice(0,-1)):B!=E[1]&&B!=(E[2]=(/\bSafari\/([\d.]+\+?)/i.exec(t)||0)[1])||(B=null),E[1]=(/\b(?:Headless)?Chrome\/([\d.]+)/i.exec(t)||0)[1],537.36==E[0]&&537.36==E[2]&&parseFloat(E[1])>=28&&"WebKit"==Y&&(Y=["Blink"]),G&&(I||E[1])?(Y&&(Y[1]="like Chrome"),E=E[1]||((E=E[0])<530?1:E<532?2:E<532.05?3:E<533?4:E<534.03?5:E<534.07?6:E<534.1?7:E<534.13?8:E<534.16?9:E<534.24?10:E<534.3?11:E<535.01?12:E<535.02?"13+":E<535.07?15:E<535.11?16:E<535.19?17:E<536.05?18:E<536.1?19:E<537.01?20:E<537.11?"21+":E<537.13?23:E<537.18?24:E<537.24?25:E<537.36?26:"Blink"!=Y?"27":"28")):(Y&&(Y[1]="like Safari"),E=(E=E[0])<400?1:E<500?2:E<526?3:E<533?4:E<534?"4+":E<535?5:E<537?6:E<538?7:E<601?8:E<602?9:E<604?10:E<606?11:E<608?12:"12"),Y&&(Y[1]+=" "+(E+="number"==typeof E?".x":/[.+]/.test(E)?"":"+")),"Safari"==j&&(!B||parseInt(B)>45)?B=E:"Chrome"==j&&/\bHeadlessChrome/i.test(t)&&V.unshift("headless")),"Opera"==j&&(E=/\bzbov|zvav$/.exec(W))?(j+=" ",V.unshift("desktop mode"),"zvav"==E?(j+="Mini",B=null):j+="Mobile",W=W.replace(RegExp(" *"+E+"$"),"")):"Safari"==j&&/\bChrome\b/.exec(Y&&Y[1])?(V.unshift("desktop mode"),j="Chrome Mobile",B=null,/\bOS X\b/.test(W)?(K="Apple",W="iOS 4.3+"):W=null):/\bSRWare Iron\b/.test(j)&&!B&&(B=getVersion("Chrome")),B&&0==B.indexOf(E=/[\d.]+$/.exec(W))&&t.indexOf("/"+E+"-")>-1&&(W=trim(W.replace(E,""))),W&&-1!=W.indexOf(j)&&!RegExp(j+" OS").test(W)&&(W=W.replace(RegExp(" *"+qualify(j)+" *"),"")),Y&&!/\b(?:Avant|Nook)\b/.test(j)&&(/Browser|Lunascape|Maxthon/.test(j)||"Safari"!=j&&/^iOS/.test(W)&&/\bSafari\b/.test(Y[1])||/^(?:Adobe|Arora|Breach|Midori|Opera|Phantom|Rekonq|Rock|Samsung Internet|Sleipnir|SRWare Iron|Vivaldi|Web)/.test(j)&&Y[1])&&(E=Y[Y.length-1])&&V.push(E),V.length&&(V=["("+V.join("; ")+")"]),K&&H&&H.indexOf(K)<0&&V.push("on "+K),H&&V.push((/^on /.test(V[V.length-1])?"":"on ")+H),W&&(E=/ ([\d.+]+)$/.exec(W),g=E&&"/"==W.charAt(W.length-E[0].length-1),W={architecture:32,family:E&&!g?W.replace(E[0],""):W,version:E?E[1]:null,toString:function(){var t=this.version;return this.family+(t&&!g?" "+t:"")+(64==this.architecture?" 64-bit":"")}}),(E=/\b(?:AMD|IA|Win|WOW|x86_|x)64\b/i.exec(L))&&!/\bi686\b/i.test(L)?(W&&(W.architecture=64,W.family=W.family.replace(RegExp(" *"+E),"")),j&&(/\bWOW64\b/i.test(t)||G&&/\w(?:86|32)$/.test(h.cpuClass||h.platform)&&!/\bWin64; x64\b/i.test(t))&&V.unshift("32-bit")):W&&/^OS X/.test(W.family)&&"Chrome"==j&&parseFloat(B)>=39&&(W.architecture=64),t||(t=null);var q={};return q.description=t,q.layout=Y&&Y[0],q.manufacturer=K,q.name=j,q.prerelease=U,q.product=H,q.ua=t,q.version=j&&B,q.os=W||{architecture:null,family:null,version:null,toString:function(){return"null"}},q.parse=parse,q.toString=function toStringPlatform(){return this.description||""},q.version&&V.unshift(B),q.name&&V.unshift(j),W&&j&&(W!=String(W).split(" ")[0]||W!=j.split(" ")[0]&&!H)&&V.push(H?"("+W+")":"on "+W),V.length&&(q.description=V.join(" ")),q}();h&&m?forOwn(N,(function(t,a){h[a]=t})):_.platform=N}).call(t)}));function getSystemInfoFn(){var t,a,u=qd.version||"";if(isElectron())try{var _=navigator.userAgent.match(/Electron\/([\d.]+\d+)/);_&&_[1]&&"string"==typeof _[1]&&(u=_[1])}catch(t){}return{os:(null===(t=qd.os)||void 0===t?void 0:t.family)||"",osVer:(null===(a=qd.os)||void 0===a?void 0:a.version)||"",browser:qd.name||"",browserVer:qd.version||"",libEnv:"BROWSER",hostEnv:isElectron()?"Electron":isMobile()?"H5":isBrowser()?"BROWSER":"Unset",hostEnvEnum:isElectron()?5:isMobile()?101:isBrowser()?100:0,hostEnvVer:u,userAgent:navigator&&navigator.userAgent,model:u,manufactor:qd.name||""}}var $d=null,zd=null,Jd={getNetworkStatus:function getNetworkStatus(){return Jn.resolve({net_type:0,net_connect:"undefined"==typeof navigator||"boolean"!=typeof navigator.onLine||navigator.onLine})},onNetworkStatusChange:function onNetworkStatusChange(t){$d=function onlineListener(){t({isConnected:!0,networkType:0})},zd=function offlineListener(){t({isConnected:!1,networkType:0})},window.addEventListener("online",$d),window.addEventListener("offline",zd)},offNetworkStatusChange:function offNetworkStatusChange(){$d&&window.removeEventListener("online",$d),zd&&window.removeEventListener("offline",zd),$d=null,zd=null}},Qd=Ni.find,Xd="find",Zd=!0;Xd in[]&&Array(1).find((function(){Zd=!1})),_export({target:"Array",proto:!0,forced:Zd},{find:function find(t){return Qd(this,t,arguments.length>1?arguments[1]:void 0)}});var ep=entryVirtual("Array").find,tp=Array.prototype,find=function(t){var a=t.find;return t===tp||H(tp,t)&&a===tp.find?ep:a},rp="log",op=function(){function IDB(t,a){this.db=null,this.stores=[],this.name=t,this.version=a}var t=IDB.prototype;return t.setName=function setName(t){this.name=t},t.getDB=function getDB(){if(!this.db)throw new Error("DB not ready");return this.db},t.getStore=function getStore(t){var a,u=find(a=this.stores).call(a,(function(a){return a.storeName===t}));if(!u)throw new Error("LogStorage: store not found. "+t);return u},t.open=function open(){var t=this,a=window.indexedDB.open(this.name,this.version);return new Jn((function(u,_){a.onerror=function(t){var a=t.target;_(a.error)},a.onsuccess=function(a){var _,h,m=a.target;t.db=m.result,t.db.removeEventListener("close",bind$1(_=t.triggerDBCloseEvt).call(_,t)),t.db.addEventListener("close",bind$1(h=t.triggerDBCloseEvt).call(h,t)),t.stores.push(new np(rp,t)),u()},a.onupgradeneeded=function(a){var u=a.target;t.upgradeDBBySchema(u)}}))},t.triggerDBCloseEvt=function triggerDBCloseEvt(){try{this.db&&this.db.close(),this.db=null}catch(t){}this.open()},t.upgradeDBBySchema=function upgradeDBBySchema(t){var a=t.result,u=t.transaction&&a.objectStoreNames.contains(rp)?t.transaction.objectStore(rp):a.createObjectStore(rp,{keyPath:"id",autoIncrement:!0});try{u.index("time")}catch(t){u.createIndex("time","time",{unique:!1})}},t.close=function close(){var t;this.db&&(this.db.removeEventListener("close",bind$1(t=this.triggerDBCloseEvt).call(t,this)),this.db.close(),this.stores=[],this.db=null)},IDB}(),np=function(){function IDBStore(t,a){this.idb=null,this.storeName=t,this.idb=a}var t=IDBStore.prototype;return t.getDB=function getDB(){if(!this.idb)throw new Error("DB not ready");return this.idb.getDB()},t.getStoreName=function getStoreName(){return this.storeName},t.bulkCreate=function bulkCreate(t){var a=this.getDB(),u=this.getStoreName(),_=a.transaction(u,"readwrite"),h=_.objectStore(u);return forEach$1(t).call(t,(function(t){h.add(t)})),new Jn((function(t,a){_.oncomplete=function(){t()},_.onerror=function(t){var u=t.target;a(u.error)},_.onabort=function(t){var u=t.target;u.error instanceof Error?a(u.error):a(new Error("TransactionAborted"))}}))},t.bulkDelete=function bulkDelete(t){var a=t.keyName,u=t.lower,_=t.upper,h=t.lowerOpen,m=void 0!==h&&h,E=t.upperOpen,g=void 0!==E&&E,T=IDBKeyRange.bound(u,_,m,g),I=this.getDB(),S=this.getStoreName(),M=I.transaction(S,"readwrite"),N=M.objectStore(S).index(a).openCursor(T),A=0;return N.onsuccess=function(t){var a=t.target.result;a&&(a.delete(),A++,a.continue())},new Jn((function(t,a){M.oncomplete=function(){t(A)},M.onerror=function(t){var u=t.target;a(u.error)},M.onabort=function(t){var u=t.target;u.error instanceof Error?a(u.error):a(new Error("TransactionAborted"))}}))},t.readAllAndClear=function readAllAndClear(){var t=this.getDB(),a=this.getStoreName(),u=t.transaction(a,"readwrite").objectStore(a);if(!u.getAll)throw new Error("IDBExtract not support");var _=u.getAll();return new Jn((function(t,a){_.onsuccess=function(a){var _=a.target;u.clear(),t(_.result)},_.onerror=function(t){var u=t.target;a(u.error)}}))},IDBStore}(),ip=function(){function LogStorageImpl(t){void 0===t&&(t="nim-logs"),this.idb=new op(t,1)}var t=LogStorageImpl.prototype;return t.open=function open(t){return __awaiter(this,void 0,void 0,Kl.mark((function _callee(){var a;return Kl.wrap((function _callee$(u){for(;;)switch(u.prev=u.next){case 0:return t&&this.idb.setName(t),u.next=3,this.idb.open();case 3:return a=this.idb.getStore(rp),u.prev=4,u.next=7,a.bulkDelete({keyName:"time",lower:0,upper:Zn()-2592e5});case 7:u.next=11;break;case 9:u.prev=9,u.t0=u.catch(4);case 11:case"end":return u.stop()}}),_callee,this,[[4,9]])})))},t.close=function close(){this.idb.close()},t.addLogs=function addLogs(t){return this.idb.getStore(rp).bulkCreate(t)},t.extractLogs=function extractLogs(){return __awaiter(this,void 0,void 0,Kl.mark((function _callee2(){var t,a,u,_,h;return Kl.wrap((function _callee2$(m){for(;;)switch(m.prev=m.next){case 0:return a=this.idb.getStore(rp),m.next=3,a.readAllAndClear();case 3:if(0!==(u=m.sent).length){m.next=6;break}return m.abrupt("return","");case 6:return _=reduce(u).call(u,(function(t,a){var u=a.iid;return t[u]||(t[u]=[]),t[u].push(a),t}),{}),h=map$6(t=qa(_)).call(t,(function(t){var a=_[t];return"==========iid:"+t+"==========\n "+map$6(a).call(a,(function(t){return t.text})).join("\n")})).join("\n"),m.abrupt("return",new File([h],"nim-logs.txt",{type:"text/plain"}));case 9:case"end":return m.stop()}}),_callee2,this)})))},LogStorageImpl}();return function setAdapters(t){merge$1(Wl,t())}((function getAdapter(){return{setLogger:setLogger,platform:"BROWSER",localStorage:window.localStorage,request:Rd,WebSocket:window.WebSocket,uploadFile:uploadFileFn,getFileUploadInformation:getFileUploadInformationFn,getSystemInfo:getSystemInfoFn,net:Jd,logStorage:ip}})),Od}));
