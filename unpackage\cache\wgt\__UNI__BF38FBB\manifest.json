{"@platforms": ["android", "iPhone", "iPad"], "id": "__UNI__BF38FBB", "name": "铁福来钻机", "version": {"name": "1.0.4", "code": 104}, "description": "", "developer": {"name": "", "email": "", "url": ""}, "permissions": {"Push": {}, "UniNView": {"description": "UniNView原生渲染"}}, "plus": {"useragent": {"value": "uni-app", "concatenate": true}, "splashscreen": {"autoclose": true, "delay": 0, "target": "id:1", "waiting": true}, "popGesture": "close", "launchwebview": {"id": "1", "kernel": "WKWebview"}, "usingComponents": true, "nvueStyleCompiler": "uni-app", "nvueCompiler": "uni-app", "compilerVersion": 3, "distribute": {"splashscreen": {"androidStyle": "common", "android": {"hdpi": "static/splash/splash.png", "xhdpi": "static/splash/splash.png", "xxhdpi": "static/splash/splash.png"}, "ios": {"iphone": {"portrait-896h@3x": "static/splash/splash.png", "landscape-896h@3x": "static/splash/splash.png", "portrait-896h@2x": "static/splash/splash.png", "landscape-896h@2x": "static/splash/splash.png", "iphonex": "static/splash/splash.png", "iphonexl": "static/splash/splash.png", "retina55": "static/splash/splash.png", "retina55l": "static/splash/splash.png", "retina47": "static/splash/splash.png", "retina47l": "static/splash/splash.png", "retina40": "static/splash/splash.png", "retina40l": "static/splash/splash.png", "retina35": "static/splash/splash.png"}}}, "icons": {"android": {"hdpi": "icon-android-hdpi.png", "xhdpi": "icon-android-xhdpi.png", "xxhdpi": "icon-android-xxhdpi.png", "xxxhdpi": "icon-android-xxxhdpi.png"}, "ios": {"appstore": "unpackage/res/icons/1024x1024.png", "ipad": {"app": "unpackage/res/icons/76x76.png", "app@2x": "unpackage/res/icons/152x152.png", "notification": "unpackage/res/icons/20x20.png", "notification@2x": "unpackage/res/icons/40x40.png", "proapp@2x": "unpackage/res/icons/167x167.png", "settings": "unpackage/res/icons/29x29.png", "settings@2x": "unpackage/res/icons/58x58.png", "spotlight": "unpackage/res/icons/40x40.png", "spotlight@2x": "unpackage/res/icons/80x80.png"}, "iphone": {"app@2x": "unpackage/res/icons/120x120.png", "app@3x": "unpackage/res/icons/180x180.png", "notification@2x": "unpackage/res/icons/40x40.png", "notification@3x": "unpackage/res/icons/60x60.png", "settings@2x": "unpackage/res/icons/58x58.png", "settings@3x": "unpackage/res/icons/87x87.png", "spotlight@2x": "unpackage/res/icons/80x80.png", "spotlight@3x": "unpackage/res/icons/120x120.png"}, "prerendered": "false"}}, "google": {"abiFilters": ["armeabi-v7a", "arm64-v8a", "x86"], "backgroundColor": "#1f1f2b", "icon": {"background": {"height": "100", "resize": "center", "width": "100"}, "hdpi": "unpackage/res/icons/72x72.png", "ldpi": "unpackage/res/icons/58x58.png", "xhdpi": "unpackage/res/icons/96x96.png", "xxhdpi": "unpackage/res/icons/144x144.png", "xxxhdpi": "unpackage/res/icons/192x192.png"}, "permissions": ["<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>", "<uses-permission android:name=\"android.permission.VIBRATE\"/>", "<uses-permission android:name=\"android.permission.READ_LOGS\"/>", "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>", "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>", "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>", "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.INTERNET\"/>", "<uses-permission android:name=\"android.permission.READ_EXTERNAL_STORAGE\"/>", "<uses-permission android:name=\"android.permission.BADGE_ICON\" />", "<uses-permission android:name=\"com.android.launcher.permission.READ_SETTINGS\" />", "<uses-permission android:name=\"com.android.launcher.permission.WRITE_SETTINGS\" />"], "schemes": [], "packagename": "com.demo", "custompermissions": true}, "apple": {"backgroundColor": "#1f1f2b", "dSYMs": false, "icon": {"appstore": "unpackage/res/icons/1024x1024.png", "background": "unpackage/res/icons/1024x1024.png", "ipad-152": "unpackage/res/icons/152x152.png", "ipad-76": "unpackage/res/icons/76x76.png", "iphone-120": "unpackage/res/icons/120x120.png", "iphone-180": "unpackage/res/icons/180x180.png", "notification-40": "unpackage/res/icons/40x40.png", "notification-60": "unpackage/res/icons/60x60.png", "settings-58": "unpackage/res/icons/58x58.png", "spotlight-40": "unpackage/res/icons/40x40.png", "spotlight-80": "unpackage/res/icons/80x80.png"}, "schemes": [], "storyboard": "AppIcon", "plistcmds": ["Add :UIFileSharingEnabled bool true"], "devices": "universal"}, "plugins": {"ad": {}, "audio": {"mp3": {"description": "Android平台录音支持MP3格式文件"}}, "push": {"description": "管理消息推送能力"}}, "debug": true, "syncDebug": true, "orientation": "portrait-primary"}, "nativePlugins": {"JG-JCore": {"JPUSH_APPKEY_ANDROID": "8c0639c0bb9c2cd2828533d1", "JPUSH_APPKEY_IOS": "", "JPUSH_CHANNEL_ANDROID": "", "JPUSH_CHANNEL_IOS": ""}, "JG-JPush": {"JPUSH_ADVERTISINGID_IOS": "", "JPUSH_DEFAULTINITJPUSH_IOS": "", "JPUSH_GOOGLE_API_KEY": "", "JPUSH_GOOGLE_APP_ID": "", "JPUSH_GOOGLE_PROJECT_ID": "", "JPUSH_GOOGLE_PROJECT_NUMBER": "", "JPUSH_GOOGLE_STORAGE_BUCKET": "", "JPUSH_HONOR_APPID": "", "JPUSH_HUAWEI_APPID": "", "JPUSH_ISPRODUCTION_IOS": "", "JPUSH_MEIZU_APPID": "", "JPUSH_MEIZU_APPKEY": "", "JPUSH_NIO_APPID": "", "JPUSH_OPPO_APPID": "", "JPUSH_OPPO_APPKEY": "", "JPUSH_OPPO_APPSECRET": "", "JPUSH_VIVO_APPID": "", "JPUSH_VIVO_APPKEY": "", "JPUSH_XIAOMI_APPID": "", "JPUSH_XIAOMI_APPKEY": ""}, "udp-client": {}, "NERTCUniPluginSDK": {}}, "statusbar": {"immersed": "supportedDevice", "style": "dark", "background": "#000000"}, "allowsInlineMediaPlayback": true, "safearea": {"background": "#1f1f2b", "bottom": {"offset": "auto"}}, "uni-app": {"control": "uni-v3", "vueVersion": "3", "compilerVersion": "4.36", "nvueCompiler": "uni-app", "renderer": "auto", "nvue": {"flex-direction": "column"}, "nvueLaunchMode": "fast", "webView": {"minUserAgentVersion": "49.0"}}, "tabBar": {"position": "bottom", "color": "rgba(255, 255, 255, 0.5)", "selectedColor": "rgba(255, 255, 255, 0.85)", "borderStyle": "#1f1f2b", "blurEffect": "none", "fontSize": "10px", "iconWidth": "24px", "spacing": "3px", "height": "70px", "list": [{"pagePath": "pages/project/index", "text": "首页", "selectedIconPath": "/static/image/tabbar/index-ed.png", "iconPath": "/static/image/tabbar/index.png"}, {"pagePath": "pages/home/<USER>", "text": "看板", "selectedIconPath": "/static/image/tabbar/board-ed.png", "iconPath": "/static/image/tabbar/board.png"}, {"pagePath": "pages/commission/commission", "text": "代办", "selectedIconPath": "/static/image/tabbar/commission-ed.png", "iconPath": "/static/image/tabbar/commission.png"}, {"pagePath": "pages/apply/apply", "text": "应用", "selectedIconPath": "/static/image/tabbar/use-ed.png", "iconPath": "/static/image/tabbar/use.png"}, {"pagePath": "pages/user/user", "text": "我的", "selectedIconPath": "/static/image/tabbar/home-ed.png", "iconPath": "/static/image/tabbar/home-st.png"}], "backgroundColor": "#1f1f2b", "selectedIndex": 0, "shown": true}, "adid": "124189250109"}, "app-harmony": {"useragent": {"value": "uni-app", "concatenate": true}, "safearea": {"background": "#1f1f2b", "bottom": {"offset": "auto"}}}, "launch_path": "__uniappview.html"}