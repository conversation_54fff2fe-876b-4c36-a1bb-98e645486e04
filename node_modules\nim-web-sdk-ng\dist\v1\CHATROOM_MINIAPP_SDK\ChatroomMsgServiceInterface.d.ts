import { IBaseSendFileOptions, ChatroomMessage, TMsgType, ISendCustomMsgOptions, ISendGeoLocationMsgOptions, ISendTextMsgOptions, ISendTipMsgOptions } from './types';
/**
 * 调用方式:
 * ```js
 * chatroom.chatroomMsg.sendTextMsg(options)
 * ```
 */
export interface ChatroomMsgServiceInterface {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送文本消息
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendTextMsg({
     *  body: 'hello'
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send a text message
     * @locale
     */
    sendTextMsg(options: ISendTextMsgOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送提醒消息
     *
     * 提醒消息用于会话内的状态提醒，如进入会话时出现的欢迎消息，或者会话命中敏感词后的提示消息等等.
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendTipMessage({
     *  body: 'tip'
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send an alert
     *
     * Notification messages refer to status notifications in conversations, such as the welcome message when a person joins a conversation and the prompt messages about sensitive words detection.
     * @locale
     */
    sendTipMsg(options: ISendTipMsgOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送地理位置消息
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendGeoLocationMsg({
     *  body: {
     *    lng: 120,
     *    lat: 30.2,
     *    title: 'location title',
     *  }
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send an location message
     * @locale
     */
    sendGeoLocationMsg(options: ISendGeoLocationMsgOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送自定义消息
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendCustomMsg({
     *  "body": "custom"
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send a custom message
     * @locale
     */
    sendCustomMsg(options: ISendCustomMsgOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送图片消息（包含上传文件的功能）
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendImageMsg({
     *  "file": imgFile
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send an image messages (uploading images supported)
     * @locale
     */
    sendImageMsg(options: IBaseSendFileOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送音频消息（包含上传文件的功能）
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendAudioMsg({
     *  "file": audioFile
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send an audio message (uploading audios supported)
     * @locale
     */
    sendAudioMsg(options: IBaseSendFileOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送视频消息（包含上传文件的功能）
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendVideoMsg({
     *  "file": videoFile
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send a video message (uploading videos supported)
     * @locale
     */
    sendVideoMsg(options: IBaseSendFileOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 发送其他文件消息（包含上传文件的功能）
     *
     * #### 影响范围
     * - 聊天室在线用户收到 {@link ChatroomEventInterface.chatroomMsg | chatroomMsg} 事件
     *
     * @example
     * ```js
     * chatroom.chatroomMsg.sendFileMsg({
     *  "file": file
     * })
     * ```
     * @locale
     *
     * @locale en
     * Send a file messages (uploading files supported)
     * @locale
     */
    sendFileMsg(options: IBaseSendFileOptions): Promise<ChatroomMessage>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 查询历史消息
     *
     * @example
     * ```js
     * const msgs = chatroom.chatroomMsg.queryMessageHistory({
     *    // 单次最多查询 100 条消息
     *    limit: 100,
     *    // 升序查询。从最早的消息开始查询
     *    reverse: true
     * })
     * ```
     * @locale
     *
     * @locale en
     * Query the message history
     * @locale
     */
    queryMessageHistory(options: QueryMessageHistoryOptions): Promise<ChatroomMessage[]>;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 根据标签查询历史消息
     *
     * @example
     * ```js
     * const msgs = chatroom.chatroomMsg.getHistoryMsgsByTags({
     *    tags: ['tag1', 'tag2'],
     *    types: ['text', 'image', 'audio', 'video'],
     *    limit: 100,
     *    // 从较晚的消息开始查找
     *    reverse: 1
     * })
     * ```
     * @locale
     *
     * @locale en
     * Query message history by tag
     * @locale
     */
    getHistoryMsgsByTags(options: GetHistoryMsgsByTagsOptions): Promise<ChatroomMessage[]>;
}
export interface QueryMessageHistoryOptions {
    /**
     * 若为 0，则当 reverse = true时，从最早的消息开始查询；当 reverse = false时，从最晚的消息开始查询
     */
    timetag?: number;
    /**
     * 一次查询多少条记录
     */
    limit?: number;
    /**
     * - true：升序查找，以 timetag 记为最早的时间戳，寻找时间大于 timetag 的消息
     * - false：降序查找，以 timetag 记为最晚的时间戳，寻找时间小于 timetag 的消息
     */
    reverse?: boolean;
    msgTypes?: TMsgType[];
}
export interface GetHistoryMsgsByTagsOptions {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 标签
     * @locale
     *
     * @locale en
     * Tag
     * @locale
     */
    tags: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 消息类型
     * @locale
     *
     * @locale en
     * Message type
     * @locale
     */
    types?: TMsgType[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 开始时间 默认 0
     * @locale
     *
     * @locale en
     * Start time, the default value is 0.
     * @locale
     */
    fromTime?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 结束时间。若不填，或者为0，则默认服务器当前时间
     * @locale
     *
     * @locale en
     * The end time, the current time on the server is used by default
     * @locale
     */
    toTime?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 数量限制，默认 100
     * @locale
     *
     * @locale en
     * The limit, a maximum of 100 messages are allowed.
     * @locale
     */
    limit?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * - 0: 升序查找，优先返回时间较早的消息
     * - 1: 降序查找，优先返回时间较晚的消息
     * @locale
     *
     * @locale en
     * Whether a reverse query is allowed. 0(default): direct, 1: reverse
     * @locale
     */
    reverse?: 0 | 1;
}
