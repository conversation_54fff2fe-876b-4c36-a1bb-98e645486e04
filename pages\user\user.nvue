<template>
	<view class="container">
		<image 
			class="background-image" 
			src="/static/image/BG.png" 
			mode="aspectFill" 
			:style="{ width: screenWidth + 'px', height: screenHeight + 'px' }"
		/>
		<view class="content-wrapper">
			<view 
				 class="header"
				background="transparent"
				style="color: #fff;"
			> <text style="font-size: 34rpx;color: rgba(255, 255, 255, 0.85);"></text></view>
			<scroll-view class="content" scroll-y="true">
				<!-- 用户信息区域 -->
				<view class="user-info-card" @click="navigateToUserDetail">
					<view class="user-info">
						<view class="avatar-content">
							<image class="avatar" :src="userInfo.profilePic || '/static/logo.png'" mode="aspectFill" />
						</view>
						<view class="info-content">
							<view class="content-name">
								<text class="username">{{userInfo.name || '未登录'}}</text>
								<text v-if="userInfo.authentication==1" class="surealname">已实名</text>
								<text v-else class="realname">未实名</text>
							</view>
							<template v-if="userInfo.phoneNumber == ''">
								<text class="phone" style="color: rgba(255,255,255,0.65);">未绑定手机号</text>
							</template>
							<template v-else>
								<text class="phone">{{formatPhone(userInfo.phoneNumber)}}</text>
							</template>
							
						</view>
					</view>
					<image class="arrow-right" src="/static/image/user/right.png" mode="aspectFit" />
				</view>

				<!-- 操作菜单区域 -->
				<view class="menu-list">
					<view v-for="(item, index) in menuItems" :key="index" class="menu-item" @click="handleMenuClick(item)">
						<view class="menu-item-left">
							<image class="menu-icon" :src="item.icon" mode="aspectFit" />
							<text class="menu-text">{{item.text}}</text>
						</view>
						<image class="arrow-right" src="/static/image/user/right.png" mode="aspectFit" />
					</view>
				</view>

				<!-- 设置 -->
				<view class="menu-list">
					<view class="menu-item" @click="setting">
						<view class="menu-item-left">
							<image class="menu-icon" src="/static/image/user/setting.png" mode="aspectFit" />
							<text class="menu-text">设置</text>
						</view>
						<image class="arrow-right" src="/static/image/user/right.png" mode="aspectFit" />
					</view>
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import Quene from '../../components/utils/queue'
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue';
	export default {
		components: {
			customHeader
		},
		data() {
			return {
				screenWidth: 0,
				screenHeight: 0,
				userInfo: {},
				menuItems: [
					// {
					// 	icon: '/static/image/user/notification.png',
					// 	text: '通知',
					// 	path: '/pages/user/components/notification'
					// },
					{
						icon: '/static/image/user/security.png',
						text: '安全中心',
						path: '/pages/user/components/security'
					},
					// {
					// 	icon: '/static/image/user/product.png',
					// 	text: '产品咨询',
					// 	path: '/pages/user/components/product'
					// },
					// {
					// 	icon: '/static/image/user/feedback.png',
					// 	text: '意见反馈',
					// 	path: '/pages/user/components/feedback'
					// }
				]
			}
		},
		created() {
			// 获取屏幕尺寸
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.windowWidth
			this.screenHeight = systemInfo.windowHeight
		},
		async onLoad() {
			await this.loadUserData()
		},
		onShow() {
			this.userInfo = Quene.getData('userinfo')
		},
		methods: {
			async loadUserData() {
				try {
					const res = await Request.get('/personal/get_info')
					if (res.status === 0) {
						Quene.setData('userinfo', res.data)
						this.userInfo = Quene.getData('userinfo')
					} else {
						uni.showToast({
							title: '用户信息获取失败',
							icon: 'none',
							duration: 2000
						})
					}
				} catch (error) {
					console.error('Error fetching user info:', error)
					uni.showToast({
						title: '网络不稳定，请稍后再试',
						icon: 'none',
						duration: 2000
					})
				}
			},
			formatPhone(phone) {
				if (!phone) return ''
				return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
			},
			navigateToUserDetail() {
				uni.navigateTo({
					url: '/pages/user/components/detail'
				})
			},
			handleMenuClick(item) {
				uni.navigateTo({
					url: item.path
				})
			},
			setting() {
				uni.navigateTo({
					url: '/pages/user/components/setting'
				})
			}
		}
	}
</script>

<style scoped lang="scss">
.container {
	flex: 1;
	position: relative;
}

.background-image {
	position: absolute;
	top: 0;
	left: 0;
}

.content-wrapper {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	flex: 1;
}

.content {
	padding-top: 52rpx;
	padding-left: 26rpx;
	padding-right: 26rpx;

}

.header {
	padding-top: 80rpx;
	// height: 160rpx;
	font-size: 34rpx;
	font-weight: 600;
    text-align: center;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #f60 !important;
	// z-index: 1;
	// margin-bottom: 32rpx;
	// background-color: rgba(255,255,255,0.03);
}

.user-info-card {
	padding: 32rpx;
	background-color: rgba(255, 255, 255, 0.0362);
	border-width: 1rpx;
	border-style: solid;
	border-color: rgba(255, 255, 255, 0.0972);
	border-radius: 12rpx;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
}

.user-info {
	flex-direction: row;
	align-items: center;
}

.avatar-content {
	width: 104rpx;
	height: 104rpx;
	padding: 4rpx;
	background-color: rgba(84, 146, 247, 0.6);
	border-radius: 16rpx;
}

.avatar {
	width: 96rpx;
	height: 96rpx;
	border-radius: 16rpx;
}

.info-content {
	margin-left: 24rpx;
}

.content-name {
	flex-direction: row;
	align-items: center;
	margin-bottom: 24rpx;
}

.username {
	font-size: 40rpx;
	margin-right: 28rpx;
	color: rgba(255,255,255,0.85);
}

.surealname {
	padding: 8rpx;
	font-size: 24rpx;
	border-radius: 12rpx;
	background-color: rgba(22, 119, 255, 0.12);
	color: #1677FF;
}

.realname {
	padding: 8rpx;
	font-size: 24rpx;
	border-radius: 12rpx;
	background-color: rgba(255, 255, 255, 0.12);
	color: rgba(255, 255, 255, 0.85);
}

.phone {
	font-size: 28rpx;
	color: rgba(255, 255, 255, 0.65);
}

.menu-list {
	margin-top: 44rpx;
	background-color: rgba(255, 255, 255, 0.0362);
	border-width: 1rpx;
	border-style: solid;
	border-color: rgba(255, 255, 255, 0.0972);
	border-radius: 12rpx;
}

.menu-item {
	height: 116rpx;
	flex-direction: row;
	align-items: center;
	justify-content: space-between;
	padding-left: 32rpx;
	padding-right: 32rpx;
	border-bottom-width: 1rpx;
	border-bottom-style: solid;
	border-bottom-color: rgba(167, 169, 172, 0.15);
}

.menu-item:last-child {
	border-bottom-width: 0;
}

.menu-item-left {
	flex-direction: row;
	align-items: center;
}

.menu-icon {
	width: 40rpx;
	height: 40rpx;
	margin-right: 32rpx;
}

.menu-text {
	font-size: 34rpx;
	color: rgba(255, 255, 255, 0.85);
}

.arrow-right {
	width: 60rpx;
	height: 60rpx;
}
</style>