import { NIMEModuleParamCloudStorageConfig } from './CloudStorageServiceInterface';
import { NIMEQChatChannelConfig } from './QChatChannelServiceInterface';
import { NIMEQChatMediaConfig } from './QChatMediaServiceInterface';
export interface StrAnyObj {
    [key: string]: any;
}
export interface StrObj {
    [key: string]: string;
}
export interface QChat {
    id: string;
    name: string;
    announcement: string;
    broadcastUrl: string;
    custom: string;
    createTime: number;
    updateTime: number;
    creator: string;
    mute: boolean;
    onlineMemberNum: number;
}
/**
 * 实例初始化参数
 *
 * eg. new sdk({ ...QChatInitializeOptions })
 */
export interface QChatInitializeOptions {
    /**
     * 圈组账号
     */
    account: string;
    appkey: string;
    /**
     * 圈组登录 token。
     *
     * 注意，若 authType = 1，即鉴权方式为动态 token，则需要在重连时，重新设置 token，防止 token 过期导致鉴权失败
     *
     * ```js
     * nim.on('willReconnect', () => {
     *   nim.setOptions({
     *      token: 'new token'
     *   })
     * })
     * ```
     */
    token: string;
    /**
     * 连接地址列表
     *
     * 推荐使用服务器 API 获取。还可通过 NIM 连接实例获取，形式如 `await nim.plugin.getQChatAddress({ipType: 0})`
     */
    linkAddresses: string[];
    /**
     * 登录 IM 的鉴权方式（默认为 0）：
     *
     * - 0：通过传入静态 token 进行鉴权。静态 token 恒定不变，且默认永久有效，除非主动调用[云信服务端 API](https://doc.yunxin.163.com/messaging/docs/DUxNDQ3NjA?platform=server)刷新token
     * - 1：通过传入动态 token 进行鉴权。动态 token 可设置有效期，因此具备时效性。采用该鉴权方式可有效提升token 破解难度，降低密码泄露风险。动态鉴权生成方式请参考: [登录鉴权](https://doc.yunxin.163.com/messaging/docs/zE2NzA3Mjc?platform=server#%E5%8A%A8%E6%80%81token%E9%89%B4%E6%9D%83)
     * - 2：过云信的第三方回调功能进行鉴权。云信服务端不做 IM 登录鉴权，鉴权工作需由指定的第三方服务器（可以是应用服务器）进行
     *
     * 采用动态 token 鉴权时，需要在重连时，重新设置 token，否则重连时会鉴权失败
     * ```js
     * qchat.on('willReconnect', () => {
     *  qchat.setOptions({
     *    token: 'new token'
     *  })
     * })
     * ```
     */
    authType?: number;
    /**
     * 登录自定义字段，用于提交给用户的第三方回调服务进行登录检测
     */
    loginExt?: string;
    /**
     * 日志级别，默认为 off，即不输出任何日志
     *
     * - off: 不输出任何日志
     * - debug: 输出所有日志
     * - log: 输出 log、warn、 error 级别的日志
     * - warn: 输出 warn 和 error 级别的日志
     * - error: 输出 error 级别的日志
     */
    debugLevel?: string;
    /**
     * 是否需要自动重连，默认为 true
     */
    needReconnect?: boolean;
    /**
     * 自动重连尝试次数。默认为不限次数
     */
    reconnectionAttempts?: number;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * 是否 deviceId 需要固定下来。默认 false。
     *
     * true：sdk 随机对设备生成一个设备标识并存入 localstorage 缓存起来，也就是说一个浏览器来说所有 SDK 实例连接都被认为是共同的设备。
     *
     * false：每一个 sdk 实例连接时，使用随机的字符串作为设备标识，相当于每个实例采用的不同的设备连接上来的。
     *
     * #### 影响范围
     * - 这个参数会影响多端互踢的策略。有关于多端互踢策略的配置可以参见服务器文档。
     * - 这个参数影响离线推送(uniapp)
     * @locale
     *
     * @locale en
     * Whether deviceId is fixed, the default value is false.
     *
     * true：SDK randomly generates a device identifier and stores into the LocalStorage cache, all SDK instances are considered to be common devices for a browser.
     *
     * false：When each SDK instance is connected, the random string is used as a device identifier and different devices are used in each instance.
     *
     * Note: This parameter will affect the strategy of multi-device forced logout. For configurations about multi-device forced logout strategies, see the server documentation.
     * @locale
     */
    isFixedDeviceId?: boolean;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * Abtest 是否开启，默认 true 开启
     *
     * 注: 打开这个开关，在 sdk 内部会试探某些新功能的开启，建议开发者不要轻易设置它。
     * @locale
     *
     * @locale en
     * Whether Abtest is enabled. The default value is true
     * Note: If you turn on the switch, the SDK attempts to enable certain new features. You are recommended not turn on it.
     * @locale
     */
    isAbtestEnable?: boolean;
    /**
     * AB测试服务器下发地址。一般用户无需关注
     */
    abtestUrl?: string;
    /**
     * 建立连接时的 xhr 请求的超时时间。默认为 8000 ms。
     *
     * xhr 请求是 websocket 连接建立前的步骤。它主要用于和服务器协商连接ID
     */
    xhrConnectTimeout?: number;
    /**
     * 建立 socket 长连接的超时时间。默认为 8000 ms
     */
    socketConnectTimeout?: number;
}
export interface QChatOtherOptions {
    /**
     * cloud storage 模块配置
     */
    cloudStorageConfig: NIMEModuleParamCloudStorageConfig;
    /**
     * @deprecated 请使用 {@link QChatOtherOptions.qchatMediaConfig | QChatOtherOptions.qchatMediaConfig}
     *
     * qchatMedia 的模块配置
     */
    QChatMedia?: NIMEQChatMediaConfig;
    /**
     * qchatMedia 的模块配置
     *
     * 注: 自 1.0.0 开始支持
     */
    qchatMediaConfig?: NIMEQChatMediaConfig;
    /**
     * qchatChannel 的模块配置
     */
    qchatChannelConfig?: NIMEQChatChannelConfig;
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * SDK 上报收集数据的配置
     * @locale
     *
     * @locale en
     * Data reporting config
     * @locale
     */
    reporterConfig?: {
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 指南针是否开启，默认是 true
         * @locale
         *
         * @locale en
         * Whether compass is enabled. The default value is true
         * @locale
         */
        enableCompass?: boolean;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * 指南针数据默认端点
         * @locale
         *
         * @locale en
         * Default endpoint for compass data
         * @locale
         */
        compassDataEndpoint?: string;
        /**
         * @Multi_Lang_Tag
         * @locale cn
         * @deprecated
         * 是否开启数据上报，默认是true
         * @locale
         *
         * @locale en
         * @deprecated
         * Whether data reporting is enabled. The default value is true
         * @locale
         */
        isDataReportEnable?: boolean;
    };
    /**
     * 日志模块的配置
     */
    loggerConfig?: {
        /**
         * 日志等级. 默认 off 关闭日志打印.
         *
         * 注: 分别是关闭日志打印 | 打印 error 日志 | 打印 warn 级别及以上 | 打印 log 级别及以上 | 打印 debug 级别及以上
         */
        debugLevel?: 'off' | 'error' | 'warn' | 'log' | 'debug';
        /**
         * 日志代理函数.
         *
         * 注: 拥有四个等级的日志输出方法. 所有日志经过这些方法代理.
         *
         * 注2: 代理函数的入参包含一个或者多个参数, 参数的类型为基础类型
         */
        logFunc?: {
            debug?: (...args: any) => void;
            log?: (...args: any) => void;
            warn?: (...args: any) => void;
            error?: (...args: any) => void;
        };
    };
}
export interface WillReconnectEventResult {
    /**
     * 重试次数
     */
    retryCount: number;
    /**
     * 重试间隔
     */
    duration: number;
}
export declare enum EClientType {
    Android = 1,
    iOS = 2,
    PC = 4,
    WindowsPhone = 8,
    Web = 16,
    Server = 32,
    Mac = 64,
    HarmonyOS = 65
}
export declare type TClientType = keyof typeof EClientType;
export interface LoginResult {
    appkey: string;
    /**
     * 端测的IP
     */
    clientIP: string;
    /**
     * 端测的端口
     */
    clientPort: number;
    /**
     * 端类型
     */
    clientType: TClientType;
    /**
     * 连接 id
     */
    consid: string;
    /**
     * 自定义的端类型
     */
    customClientType: number;
    /**
     * 设备 id
     */
    deviceId: string;
    /**
     * 是否为自动连接（断线重连）
     */
    isAutoReconnect: boolean;
    /**
     * 登录时间
     */
    loginTime: number;
    /**
     * 是否在线
     */
    online: boolean;
    /**
     * 端的系统信息
     */
    os: string;
    /**
     * 推送类型
     */
    pushType: number;
    /**
     * 1 表示之前有推送
     */
    hasTokenPreviously: boolean;
}
export interface MultiSpotLoginResult {
    /**
     * account id
     */
    account: string;
    /**
     * socket 连接 id
     */
    connectionId: string;
    /**
     * 设备id
     */
    deviceId: string;
    /**
     * ip 地址
     */
    ip: string;
    /**
     * mac 地址
     */
    mac: string;
    /**
     * 是否在线
     */
    online: boolean;
    /**
     * 系统版本
     */
    os: string;
    /**
     * 登录时间
     */
    time: number;
    /**
     * 客户端类型
     */
    clientType: TClientType;
}
export interface TKickedReason {
    /**
     * 未知 | 互斥类型的客户端互踢-不允许同一个帐号在多个地方同时登录 | 服务器端发起踢客户端指令-被服务器踢了 | 被自己账号所在的其他端踢掉 | 被悄悄踢掉, 表示这个链接已经废掉了
     */
    reason: 'unknow' | 'samePlatformKick' | 'serverKick' | 'otherPlatformKick' | 'silentlyKick';
    /**
     * 原因的详细描述
     */
    message: string;
    /**
     * 踢了本链接的那个客户端的类型
     */
    clientType: TClientType;
    /**
     * 踢了本链接的那个客户端的自定义端类型
     */
    customClientType: number;
    /**
     * 扩展字段
     */
    ext?: string;
}
export interface QChatMessageUnread {
    /**
     * 消息所属的服务器的id
     */
    serverId: string;
    /**
     * 消息所属的频道的id
     */
    channelId: number;
    /**
     * SDK端测生成的消息id
     */
    idClient: string;
}
