export declare type NIMEInstanceStatus = 'unconnected' | 'connecting' | 'connected' | 'logined' | 'waitReconnect' | 'destroyed';
export declare type V2NIMReconnectDelayProvider = (delay: number) => number;
export declare type V2NIMLoginOption = {
    /**
     * API 登录的总超时时间。单位 ms, 默认 60000.
     *
     * 注: 这意味着这次调用 API 来发起登录，必须在默认 60000ms 内得到响应
     */
    timeout: number;
    /**
     * 强制登录模式, 默认为 false
     *
     * 注: 为 true 时, 登录阶段若达到账号的设备连接上限，则本端登录成功，挤掉其他设备登录端
     *
     * 注2: 为 false 时, 登录阶段若达到账号的设备连接上限，则本端登录响应 417 错误码而不是挤掉其他设备的。
     *
     * 注3: 在 sdk 进入断网自动重连的逻辑时, 本字段一直会被当作 false。
     */
    forceMode: boolean;
};
export declare type V2NIMTokenProvider = (accountId: string) => Promise<string> | string;
export declare type V2NIMLoginExtensionProvider = (accountId: string) => Promise<string> | string;
/**
 * 登录鉴权模式.
 */
export declare const enum V2NIMLoginAuthType {
    /**
     * 静态 token. 默认方式
     */
    V2NIM_LOGIN_AUTH_TYPE_DEFAULT = 0,
    /**
     * 动态 token
     */
    V2NIM_LOGIN_AUTH_TYPE_DYNAMIC_TOKEN = 1,
    /**
     * 需要第三方回调鉴权
     */
    V2NIM_LOGIN_AUTH_TYPE_THIRD_PARTY = 2
}
export declare const enum V2NIMLoginStatus {
    /**
     * 未登录
     */
    V2NIM_LOGIN_STATUS_LOGOUT = 0,
    /**
     * 已登录
     */
    V2NIM_LOGIN_STATUS_LOGINED = 1,
    /**
     * 登录中
     */
    V2NIM_LOGIN_STATUS_LOGINING = 2,
    /**
     * 处在退避间隔中
     *
     * 注: 这是一个中间状态, SDK 将会在这个状态下等待一段时间后再次尝试登录
     */
    V2NIM_LOGIN_STATUS_UNLOGIN = 3
}
/**
 * 登录的端的信息定义
 */
export declare type V2NIMLoginClient = {
    /**
     * 类型
     */
    type: V2NIMLoginClientType;
    /**
     * 操作系统
     */
    os: string;
    /**
     * 登录时间
     */
    timestamp: number;
    /**
     * 自定义信息，最大 32 个字符
     */
    customTag?: string;
    /**
     * 自定义登录端类型
     *
     * 注: 小程序, uniapp, 浏览器, h5 等更为细致的环境区分, 可以通过该字段区分, 开发者请自行做个映射
     */
    customClientType?: number;
    /**
     * 端 id
     */
    clientId: string;
};
export declare const enum V2NIMLoginClientType {
    /**
     * 未知类型
     */
    V2NIM_LOGIN_CLIENT_TYPE_UNKNOWN = 0,
    /**
     * Android
     */
    V2NIM_LOGIN_CLIENT_TYPE_ANDROID = 1,
    /**
     * iOS
     */
    V2NIM_LOGIN_CLIENT_TYPE_IOS = 2,
    /**
     * PC
     */
    V2NIM_LOGIN_CLIENT_TYPE_PC = 4,
    /**
     * Windows phone
     */
    V2NIM_LOGIN_CLIENT_TYPE_WP = 8,
    /**
     * WEB
     */
    V2NIM_LOGIN_CLIENT_TYPE_WEB = 16,
    /**
     * REST API
     */
    V2NIM_LOGIN_CLIENT_TYPE_RESTFUL = 32,
    /**
     * macOS
     */
    V2NIM_LOGIN_CLIENT_TYPE_MAC_OS = 64,
    /**
     * HarmonyOS
     */
    V2NIM_LOGIN_CLIENT_TYPE_HARMONY_OS = 65
}
export declare const enum V2NIMLoginClientChange {
    /**
     * 端列表刷新
     */
    V2NIM_LOGIN_CLIENT_CHANGE_LIST = 1,
    /**
     * 端登录
     */
    V2NIM_LOGIN_CLIENT_CHANGE_LOGIN = 2,
    /**
     * 端登出
     */
    V2NIM_LOGIN_CLIENT_CHANGE_LOGOUT = 3
}
export declare const enum V2NIMConnectStatus {
    /**
     * 未连接
     */
    V2NIM_CONNECT_STATUS_DISCONNECTED = 0,
    /**
     * 已连接
     */
    V2NIM_CONNECT_STATUS_CONNECTED = 1,
    /**
     * 连接中
     */
    V2NIM_CONNECT_STATUS_CONNECTING = 2,
    /**
     * 等待重连中
     */
    V2NIM_CONNECT_STATUS_WAITING = 3
}
export declare type V2NIMWaitingToConnectEvent = {
    /**
     * 重试间隔
     */
    duration: number;
};
export declare type NIMEModuleParamV2Login = {
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * lbs 地址，默认为云信公网提供的链接。SDK 连接时会向 lbs 地址请求得到 socket 连接地址。
     *
     * 注：为了防止 lbs 链接被网络运营商劫持，开发者可以传入自己代理的地址做备份，['公网地址', '代理地址']
     * @locale
     *
     * @locale en
     * Location Based Services (LBS) address. Its default value is the link provided by the CommsEase public network. When the SDK connects, SDK will request the socket address from the LBS address.
     *
     * Note: To prevent the LBS link from being hijacked by the network operator, the developer can pass in their proxy address for backup, ['public network address', 'proxy address']
     * @locale
     */
    lbsUrls?: string[];
    /**
     * @Multi_Lang_Tag
     * @locale cn
     * socket 备用地址，当 lbs 请求失败时，尝试直接连接 socket 备用地址。
     *
     * 注：优先级最高的是 lbs 地址下发的 socket 连接地址，
     * 次为开发者在此填的 socket 备用地址（如果不填这个字段， SDK 会有内部默认的备用地址）
     * @locale
     *
     * @locale en
     * Alternative socket address, used when the request for LBS address fails.
     *
     * Note: The socket address sent from the LBS address is of the highest priority.
     * The alternative socket address entered by the developer is of the second-highest priority. If it is not entered, the default alternative address will be adopted.
     * @locale
     */
    linkUrl?: string;
    /**
     * 是否 deviceId 需要固定下来。默认 false。
     *
     * true：sdk 随机对设备生成一个设备标识并存入 localstorage 缓存起来，也就是说一个浏览器来说所有 SDK 实例连接都被认为是共同的设备。
     *
     * false：每一个 sdk 实例连接时，使用随机的字符串作为设备标识，相当于每个实例采用的不同的设备连接上来的。
     *
     * 注意：这个参数会影响多端互踢的策略。有关于多端互踢策略的配置可以参见服务器文档。
     */
    isFixedDeviceId?: boolean;
    /**
     * 自定义客户端类型，大于0
     */
    customClientType?: number;
    /**
     * 客户端自定义 tag, 登录时多端同步该字段，最大32个字符
     */
    customTag?: string;
};
/**
 * 被踢出聊天室的原因
 */
export declare const enum V2NIMChatroomKickedReason {
    /**
     * 未知原因
     */
    V2NIM_CHATROOM_KICKED_REASON_UNKNOWN = -1,
    /**
     * 聊天室解散
     */
    V2NIM_CHATROOM_KICKED_REASON_CHATROOM_INVALID = 1,
    /**
     * 被管理员移除
     */
    V2NIM_CHATROOM_KICKED_REASON_BY_MANAGER = 2,
    /**
     * 被多端踢出
     */
    V2NIM_CHATROOM_KICKED_REASON_BY_CONFLICT_LOGIN = 3,
    /**
     * 静默被踢。表示这个链接已经废弃了n
     */
    V2NIM_CHATROOM_KICKED_REASON_SILENTLY = 4,
    /**
     * 被加入黑名单
     */
    V2NIM_CHATROOM_KICKED_REASON_BE_BLOCKED = 5
}
