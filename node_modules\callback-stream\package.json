{"name": "callback-stream", "version": "1.1.0", "description": "A pipeable stream that calls your callback", "main": "index.js", "scripts": {"lint": "standard", "test": "./node_modules/.bin/tap test.js"}, "precommit": ["lint", "test"], "website": "https://github.com/mcollina/callback-stream", "repository": {"type": "git", "url": "https://github.com/mcollina/callback-stream.git"}, "bugs": {"url": "http://github.com/mcollina/callback-stream/issues"}, "keywords": ["callback", "stream"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "> 1.0.0 < 3.0.0"}, "devDependencies": {"pre-commit": "^1.1.1", "standard": "^5.0.0", "tap": "~0.4.2", "tape": "^4.0.2"}}