## 0.5.0

圈组 QChat SDK：

- feature: 圈组接入安全通（易盾）能力，涉及发送消息和创建服务器/频道/身份组场景
- feature: 圈组追加快捷评论功能
- feature: 圈组追加消息回复功能
- feature: 圈组补充消息抄送能力
- feature: 圈组身份证新增服务器封禁权限和封禁名单查询接口
- feature: 圈组身份证新增查询自己是否拥有某个权限接口
- feature: 增加对微信小程序，支付宝小程序，百度智能小程序，字节跳动小程序的适配

聊天室 Chatroom SDK：

- feature: 聊天室补充管理队列能力

## 0.4.2

- chore: 优化了 TS 定义
- fix: 修复了 nim 发送消息的 needPushNick 参数设置为 true，不能真的让移动端收到推送里的昵称的问题。

连接相关的优化或修复有：

- chore: 缩短了 nim sdk 对于 lbs 请求的超时时间的默认值，降至 10 s。
- chore: 优化了 qchat，nim，chatroom 三个 sdk 的重连表现。sdk 目前针对初始化的连接失败都会断开并且抛出异常，重连阶段尝试连接失败，只会记录日志而不会向上抛出异常。

## 0.4.0

- feature: 提供圈组 QChatSDK，参见 `QChat_BROWSER_SDK.js`。相关功能介绍和开发集成请分别参见[什么是圈组](https://doc.yunxin.163.com/docs/TM5MzM5Njk/jQ5MjIxNzE)和[圈组开发流程](https://doc.yunxin.163.com/docs/TM5MzM5Njk/Tc4ODUzODk)。
- chore: 优化建立连接的一些表现，如允许传入多个 lbsUrls，遍历找到一个可用值。

## 0.3.2

- fix: 修复调用 resetAllSessionsUnreadCount api 无响应的问题

## 0.3.0

- feature: 追加 deleteSelfMsgs 单向删除接口。
- feature: 推送插件以及适配代码，推送功能的使用参见文章：[uniapp 推送相关](/docs/TM5MzM5Njk/jIyMTE5NDk?platformId=60179)
- chore: uniapp 平台适配优化，能够支持编译去支付宝小程序，微信小程序
- chore: 优化撤回消息逻辑，使撤回消息后 session 的 unread 数等参数能得到正确的更新。
- chore: 聊天室追加鉴权方式
- fix: 修复登出协议的一些问题

## 0.2.0

- feature: 追加 superTeam 超级群逻辑
- fix: 修复同步阶段错误的把 syncRelations 当作是同步回包的 bug
- fix: 修复同步包超时问题
- fix: 修复被拉黑的情况下消息漫游下来的状态显示成功
- chore: 优化，追加 esm/index 供开发者上层使用 esm 打包的模块，便于做 tree shaking 节省体积
- chore: 使用 lodash 替代内部一些函数，打包体积增大 30kb

## 0.1.1

- 初始化参数支持传入 lbsUrls 和 linkUrl 来指定默认的连接地址
- 修复 NIM 建立连接没有区别出自动重连的情况

## 0.1.0

初版上线，拥有好友，用户，群，消息，会话，透传协议，系统消息，聊天室能力。
