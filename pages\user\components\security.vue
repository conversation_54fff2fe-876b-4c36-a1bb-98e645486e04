<template>
	<custom-header  style="height: 88rpx;" title="安全中心" showBack3 />
	<view class="content">
		<view class="menu-list">

			<view v-if="userInfo.phoneNumber == ''" class="menu-item" @click="handleNoPhoneClick">
				<view class="menu-item-right">
					<text class="menu-text">手机号</text>
					<view class="menu-item-content">
						
							<text class="menu-item-text">去绑定手机号</text>
						
						
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view v-else class="menu-item" @click="handlePhoneClick">
				<view class="menu-item-right">
					<text class="menu-text">手机号</text>
					<view class="menu-item-content">																		
							<text class="menu-item-text">{{formatPhone(userInfo.phoneNumber)}}</text>						
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="handlePswClick">
				<view class="menu-item-right">
					<text class="menu-text">账号密码</text>
					<view class="menu-item-content">
						<text v-if="userInfo.passwordStrength==0" class="menu-item-text">当前密码强度:低</text>
						<text v-if="userInfo.passwordStrength==1" class="menu-item-text">当前密码强度:中</text>
						<text v-if="userInfo.passwordStrength==2" class="menu-item-text">当前密码强度:高</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
			<view class="menu-item" @click="handleNameClick">
				<view class="menu-item-right">
					<text class="menu-text">实名认证</text>
					<view class="menu-item-content">
						<text v-if="userInfo.authentication == 1" class="menu-item-text">{{ formatName(userInfo.name) }}</text>
						<text v-else class="menu-item-text">未实名</text>
						<image class="arrow-right" src="/static/image/user/right.png" />
					</view>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
	import Quene from '../../../components/utils/queue';
	import customHeader  from '@/components/page/header.vue';
	export default {
		components:{
			customHeader 
		},
		data() {
			return {
				userInfo: {
					// phone: '15044463323',
					// password: '当前密码强度：高',
					// name: '徐子辰'
				}
			}
		},
		async onShow() {
			this.userInfo= Quene.getData('userinfo');
			
			// console.log(this.userInfo);
		},
		methods: {
			//格式化名字
			 formatName(name) {
			            if (!name) return '';
			            const len = name.length;
			            if (len === 2) {
			                return name.charAt(0) + '*';
			            } else if (len === 3) {
			                return name.charAt(0) + '*' + name.charAt(2);
			            } else if (len > 3) {
			                return name.charAt(0) + '*'.repeat(len-2) + name.charAt(len-1);
			            }
			            return name;
			        },
			 // 格式化手机号
			 formatPhone(phone) {
			   if (!phone) return ''
			   return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
			 },
			 // 处理各项点击事件
			    handlePhoneClick() {
			      uni.navigateTo({
			        url: `/pages/user/components/safe/phone?phone=${this.userInfo.phoneNumber}`
			      });
			    },
				handleNoPhoneClick() {
				  uni.navigateTo({
				    url: `/pages/user/components/safe/updatephone`
				  });
				},
			    handlePswClick() {
			      uni.navigateTo({
			        url: '/pages/user/components/safe/updatepsw'
			      });
			    },
			    // 处理实名认证点击事件
			    			handleNameClick() {
			    				if (this.userInfo.authentication == 1) {
			    					// 已实名，跳转到 surealname 页面
			    					uni.navigateTo({
			    						url: '/pages/user/components/safe/rnsuccess'
			    					});
			    				} else {
			    					// 未实名，跳转到 realname 页面
			    					uni.navigateTo({
			    						url: '/pages/user/components/safe/realname'
			    					});
			    				}
			    			}
		}
	}
</script>
<style>
	page {
			background: #16171b;
		}
</style>
<style lang="scss" scoped>
	.content {
		padding: 0 32rpx;
		padding-top: 188rpx;
	}

	.menu-list {
		background: rgba(255, 255, 255, 0.0362);
		box-sizing: border-box;
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		border-radius: 12rpx;
	}

	.menu-item {
		display: flex;
		height: 116rpx;
		flex-direction: row;
		align-items: center;
		padding-left: 32rpx;
	}

.avatar{
	width: 56rpx;
	height: 56rpx;
	border-radius: 8rpx;
}
	.menu-item-left {
		display: flex;
		align-items: center;
	}

	.menu-item-right {
		flex: 1;
		height: 100%;
		align-items: center;
		padding-right: 32rpx;
		flex-direction: row;
		text-align: center;
		// padding-top: 32rpx;
		display: flex;
		border-bottom: 1rpx solid rgba(167, 169, 172, 0.15);
		justify-content: space-between;
	}

	.menu-item:last-child .menu-item-right {
		border-bottom: none;
	}

	.menu-item-content {
		display: flex;
		flex-direction: row;
		height: 100%;
		align-items: center;
		text {
			color: rgba(255, 255, 255, 0.65);
			align-items: center;
			text-align: center;
			font-size: 30rpx;
			line-height: 58rpx;
		}
	}

	.menu-icon {
		width: 40rpx;
		height: 40rpx;
		margin-right: 32rpx;
	}

	.menu-text {
		font-family: PingFang SC;
		font-size: 34rpx;
		letter-spacing: 0px;
		font-weight: normal;
		color: #FFFFFF;
	}

	.arrow-right {
		width: 60rpx;
		height: 60rpx;
	}
</style>