<template>
	<!-- 年月选择面板 -->
	<view class="uni-calendar" @mouseleave="leaveCale">
		<view v-if="!insert && show" class="uni-calendar__mask" :class="{'uni-calendar--mask-show':aniMaskShow}"
			@click="maskClick"></view>
		<view v-if="insert || show" class="uni-calendar__content"
			:class="{'uni-calendar--fixed':!insert,'uni-calendar--ani-show':aniMaskShow, 'uni-calendar__content-mobile': aniMaskShow}">
			<view class="uni-calendar__header" :class="{'uni-calendar__header-mobile' :!insert}">
				<!-- 左边按钮 -->
				<view class="uni-calendar__header-btn-box" @click.stop="changeYears('pre')">
					<view class="uni-calendar__header-btn uni-calendar--left"></view>
				</view>
				<!-- 年份选择（范围显示2020年-2029年） -->
				<text v-if="type=='year'||type=='years'||monthElectionYear"
					class="uni-calendar__header-text">{{ (nowDate.startYear||'') + yearText  +"-"+(nowDate.endYear||'') + yearText  }}</text>
				<!-- 当前年显示 -->
				<text v-if="type=='yearMonth'||type=='yearMonths'||type=='yearMonthRange'" v-show="!monthElectionYear"
					@click="bindYearChange"
					class="uni-calendar__header-text">{{ (nowDate.year||'') + yearText    }}</text>
				<!-- 右边按钮 -->
				<view class="uni-calendar__header-btn-box" @click.stop="changeYears('next')">
					<view class="uni-calendar__header-btn uni-calendar--right"></view>
				</view>
				<!-- 关闭按钮 -->
				<view v-if="!insert" class="dialog-close" @click="close">
					<view class="dialog-close-plus" data-id="close"></view>
					<view class="dialog-close-plus dialog-close-rotate" data-id="close"></view>
				</view>
			</view>
			<!-- 年月显示面板 -->
			<view class="uni-calendar__box">
				<!-- 年份选择面板 -->
				<view v-if="type=='year'||type=='years'||monthElectionYear" class=""
					style="display: flex;flex-direction: row;flex-wrap: wrap;padding: 20px 0;">
					<view :class="{'uni-year-month-item-checked-text':checkedYearList.includes(year.value),
					 'uni-calendar-item-this-year':year.value==nowDate.thisYear,}"
						style="width: 25%;display: flex;justify-content: center;align-items: center;height: 50px;font-size: 28rpx;"
						v-for="(year,yearIndex) in yearList" :key="yearIndex" @click="changeYear(year.value)">
						{{year.title}}
					</view>
				</view>
				<!-- 月份显示面板 -->
				<view v-if="type=='yearMonth'||type=='yearMonths'||type=='yearMonthRange' " v-show="!monthElectionYear"
					class="" style="display: flex;flex-direction: row;flex-wrap: wrap;padding: 20px 0;">
					<view :class="{'uni-year-month-item-checked-text':checkedYearMonth(nowDate.year,month.value),'uni-calendar-item--multiple':isYearMonthRangeBg(month.value),'uni-calendar-item--multiple-left':isYearMonthRangeLR(month.value,'left'),'uni-calendar-item--multiple-right':isYearMonthRangeLR(month.value,'right'),
					 'uni-calendar-item-this-year':nowDate.year==nowDate.thisYear&&month.value==nowDate.thisMonth}"
						style="width: 25%;display: flex;justify-content: center;align-items: center;height: 50px;font-size: 28rpx;color:rgba(0,0,0,0.45)"
						v-for="(month,monthIndex) in monthList" :key="monthIndex" @click="changeYearMonth(month.value)">
						{{month.title}}
					</view>
				</view>
			</view>
			<!-- 确认按钮 -->
			<view v-if="!insert" class="uni-date-changed uni-date-btn--ok">
				<view class="uni-datetime-picker--btn" v-show="showConfirm" @click="confirm">{{confirmText}}</view>
				<view class="" style="height: 40px;" v-show="!showConfirm">

				</view>
			</view>
		</view>
	</view>
</template>

<script>
	function getYearRange(year) {
		if (isNaN(year)) {
			return;
		}
		const startYear = Math.floor(year / 10) * 10; // 计算所在区间的起始年份
		const endYear = startYear + 9; // 计算所在区间的结束年份

		return [startYear, endYear];
	}
	import {
		initVueI18n
	} from '@dcloudio/uni-i18n'
	import i18nMessages from './i18n/index.js'
	const {
		t
	} = initVueI18n(i18nMessages)

	/**
	 * Calendar 日历
	 * @description 日历组件可以查看日期，选择任意范围内的日期，打点操作。常用场景如：酒店日期预订、火车机票选择购买日期、上下班打卡等
	 * @tutorial https://ext.dcloud.net.cn/plugin?id=56
	 * @property {String} date 自定义当前时间，默认为今天
	 * @property {String} startDate 日期选择范围-开始日期
	 * @property {String} endDate 日期选择范围-结束日期
	 * @property {Boolean} range 范围选择
	 * @property {Boolean} insert = [true|false] 插入模式,默认为false
	 * 	@value true 弹窗模式
	 * 	@value false 插入模式
	 * @property {Boolean} clearDate = [true|false] 弹窗模式是否清空上次选择内容
	 * @property {Array} selected 打点，期待格式[{date: '2019-06-27', info: '签到', data: { custom: '自定义信息', name: '自定义消息头',xxx:xxx... }}]
	 * @property {Boolean} showMonth 是否选择月份为背景
	 * @property {[String} defaultValue 选择器打开时默认显示的时间
	 * @event {Function} change 日期改变，`insert :ture` 时生效
	 * @event {Function} confirm 确认选择`insert :false` 时生效
	 * @event {Function} monthSwitch 切换月份时触发
	 * @example <uni-calendar :insert="true" :start-date="'2019-3-2'":end-date="'2019-5-20'"@change="change" />
	 */
	export default {
		props: {
			type: {
				type: String,
				default: 'datetime'
			},
			date: {
				type: [String, Array, Number],
				default: ''
			},
			defTime: {
				type: [String, Object],
				default: ''
			},
			selectableTimes: {
				type: [Object],
				default () {
					return {}
				}
			},
			selected: {
				type: Array,
				default () {
					return []
				}
			},
			startDate: {
				type: String,
				default: ''
			},
			endDate: {
				type: String,
				default: ''
			},
			startPlaceholder: {
				type: String,
				default: ''
			},
			endPlaceholder: {
				type: String,
				default: ''
			},
			range: {
				type: Boolean,
				default: false
			},
			hasTime: {
				type: Boolean,
				default: false
			},
			insert: {
				type: Boolean,
				default: true
			},
			showMonth: {
				type: Boolean,
				default: true
			},
			clearDate: {
				type: Boolean,
				default: true
			},
			checkHover: {
				type: Boolean,
				default: true
			},
			hideSecond: {
				type: [Boolean],
				default: false
			},
			pleStatus: {
				type: Object,
				default () {
					return {
						before: '',
						after: '',
						data: [],
						fulldate: ''
					}
				}
			},
			defaultValue: {
				type: [String, Object, Array],
				default: ''
			}
		},
		data() {
			return {
				checkedYearList: [],
				checkedYearMonthList: [],
				yearList: [],
				monthList: [{
					title: '一月',
					value: '01'
				}, {
					title: '二月',
					value: '02'
				}, {
					title: '三月',
					value: '03'
				}, {
					title: '四月',
					value: '04'
				}, {
					title: '五月',
					value: '05'
				}, {
					title: '六月',
					value: '06'
				}, {
					title: '七月',
					value: '07'
				}, {
					title: '八月',
					value: '08'
				}, {
					title: '九月',
					value: '09'
				}, {
					title: '十月',
					value: '10'
				}, {
					title: '十一月',
					value: '11'
				}, {
					title: '十二月',
					value: '12'
				}],
				show: false,
				weeks: [],
				calendar: {},
				calendarList: [], //多选日期
				nowDate: {}, //当前日期
				aniMaskShow: false,
				firstEnter: true,
				time: '',
				timeRange: {
					startTime: '',
					endTime: ''
				},
				tempSingleDate: '',
				tempRange: {
					before: '',
					after: ''
				},
				showConfirm: true,
				monthElectionYear: false
			}
		},
		watch: {
			date: {
				deep: true,
				immediate: true,
				handler(newVal) {
					console.log('newVal', newVal);
					if (!this.range) {
						this.tempSingleDate = newVal
						setTimeout(() => {
							this.initYearMonth(newVal)
						}, 100)
					}
				}
			},
			defTime: {
				immediate: true,
				handler(newVal) {
					if (!this.range) {
						this.time = newVal
					} else {
						this.timeRange.startTime = newVal.start
						this.timeRange.endTime = newVal.end
					}
				}
			},
		},
		computed: {
			isYearMonthRangeBg() {
				return function(month) {
					if (this.type != 'yearMonthRange') {
						return false
					}
					if (this.checkedYearMonthList.length != 2) {
						return false
					}

					function isDuringDate(beginDateStr, endDateStr, newDateStr) {
						// console.log(beginDateStr, endDateStr, newDateStr);
						var curDate = new Date(newDateStr),
							beginDate = new Date(beginDateStr),
							endDate = new Date(endDateStr);
						if (curDate >= beginDate && curDate <= endDate) {
							return true;
						}
						return false;
					}
					let beginDateStr = this.checkedYearMonthList[0].year + '-' + this.checkedYearMonthList[0].month
					let endDateStr = this.checkedYearMonthList[1].year + '-' + this.checkedYearMonthList[1].month
					let newDateStr = this.nowDate.year + '-' + month
					if (isDuringDate(beginDateStr, endDateStr, newDateStr)) {
						return true
					}
					return false
				}
			},
			isYearMonthRangeLR() {
				return function(month, type) {
					if (this.type != 'yearMonthRange') {
						return false
					}

					function isEqualDate(beginDateStr, endDateStr) {
						if (beginDateStr == endDateStr) {
							return true;
						}
						return false;
					}
					if (type == 'left' && this.checkedYearMonthList.length > 0) {
						let beginDateStr = this.checkedYearMonthList[0].year + '-' + this.checkedYearMonthList[0].month
						if (isEqualDate(beginDateStr, this.nowDate.year + '-' + month)) {
							return true;
						}
						return false;
					} else if (type == 'right' && this.checkedYearMonthList.length > 1) {
						let endDateStr = this.checkedYearMonthList[1].year + '-' + this.checkedYearMonthList[1].month
						if (isEqualDate(endDateStr, this.nowDate.year + '-' + month)) {
							return true;
						}
						return false;
					}
				}
			},
			timepickerStartTime() {
				const activeDate = this.range ? this.tempRange.before : this.calendar.fullDate
				return activeDate === this.startDate ? this.selectableTimes.start : ''
			},
			timepickerEndTime() {
				const activeDate = this.range ? this.tempRange.after : this.calendar.fullDate
				return activeDate === this.endDate ? this.selectableTimes.end : ''
			},
			/**
			 * for i18n
			 */
			selectDateText() {
				return t("uni-datetime-picker.selectDate")
			},
			startDateText() {
				return this.startPlaceholder || t("uni-datetime-picker.startDate")
			},
			endDateText() {
				return this.endPlaceholder || t("uni-datetime-picker.endDate")
			},
			okText() {
				return t("uni-datetime-picker.ok")
			},
			yearText() {
				return t("uni-datetime-picker.year")
			},
			monthText() {
				return t("uni-datetime-picker.month")
			},
			MONText() {
				return t("uni-calender.MON")
			},
			TUEText() {
				return t("uni-calender.TUE")
			},
			WEDText() {
				return t("uni-calender.WED")
			},
			THUText() {
				return t("uni-calender.THU")
			},
			FRIText() {
				return t("uni-calender.FRI")
			},
			SATText() {
				return t("uni-calender.SAT")
			},
			SUNText() {
				return t("uni-calender.SUN")
			},
			confirmText() {
				return t("uni-calender.confirm")
			},
		},
		created() {
			// 选中某一天
			this.initYearMonth(this.date)
		},
		methods: {
			bindYearChange() {
				this.monthElectionYear = true
				this.showConfirm = false
				this.checkedYearList = [this.nowDate.year]
				this.initYearList(this.nowDate.year)

			},
			checkedYearMonth(year, month) {
				return this.checkedYearMonthList.filter(e => e.year == year && e.month == month).length
			},
			leaveCale() {
				this.firstEnter = true
			},
			// 蒙版点击事件
			maskClick() {
				this.close()
				this.$emit('maskClose')
			},

			clearCalender() {
				this.calendarList = []
				this.checkedYearList = []
				this.checkedYearMonthList = []
			},
			initYearList(year) {
				const startYear = getYearRange(year)[0]
				const endYear = getYearRange(year)[1]
				this.nowDate.startYear = startYear
				this.nowDate.endYear = endYear
				let yearList = []
				for (var i = startYear; i <= endYear; i++) {
					yearList.push({
						title: i + '年',
						value: i
					})
					if (endYear == i) {
						this.yearList = yearList
					}
				}
			},
			/**
			 * 初始化日期显示
			 * @param {Object} date
			 */
			initYearMonth(date) {
				// 字节小程序 watch 早于 created
				console.log("date", date);
				let yearMonthDay = new Date()
				if ((this.type == 'yearMonth' || this.type == 'year') && date) {

					if (this.type == 'year') {
						yearMonthDay = new Date(date + '-01')
					} else {
						yearMonthDay = new Date(date)
					}
				} else if ((this.type == 'yearMonths' || this.type == 'years' || this.type == 'yearMonthRange') &&
					date &&
					date.length > 0) {

					if (this.type == 'years') {
						yearMonthDay = new Date(date[0] + '-01')
					} else {
						yearMonthDay = new Date(date[0])
					}
				}
				// 获取默认显示年份
				this.nowDate.year = yearMonthDay.getFullYear()
				// 获取默认显示月份
				this.nowDate.month = Number(yearMonthDay.getMonth()) + 1
				// 获取当前年份
				this.nowDate.thisYear = new Date().getFullYear()
				// 获取当前月份
				this.nowDate.thisMonth = Number(new Date().getMonth()) + 1

				// 月份选择
				if (this.type == 'yearMonth' || this.type == 'yearMonths' || this.type == 'yearMonthRange') {

					// 年月默认值赋值
					if (this.type == 'yearMonth' && date) {
						let arr = date.match(/\d+(\.\d+)?/g);
						this.checkedYearMonthList = [{
							year: arr[0],
							month: arr[1]
						}]

					}
					// 年月(多选)默认值赋值
					else if ((this.type == 'yearMonths' || this.type == 'yearMonthRange') && date) {
						// this.checkedYearList = date
						let list = []
						for (var i = 0; i < date.length; i++) {
							let arr = date[i].match(/\d+(\.\d+)?/g);
							list.push({
								year: arr[0],
								month: arr[1]
							})
							if (date.length - 1 == i) {
								this.checkedYearMonthList = list
							}
						}
					}
					return
				}
				// 年份选择
				if ((this.type == 'year' && date) || (this.type == 'years' && date)) {
					this.initYearList(this.nowDate.year)
					if (this.type == 'year') {
						this.checkedYearList = [date]
					} else if (this.type == 'years') {
						this.checkedYearList = date
					}
					return
				}
			},
			/**
			 * 打开日历弹窗
			 */
			open() {
				this.initYearMonth(this.date)
				this.show = true
				this.$nextTick(() => {
					setTimeout(() => {
						this.aniMaskShow = true
					}, 50)
				})
			},
			/**
			 * 关闭日历弹窗
			 */
			close() {
				this.showConfirm = true
				this.monthElectionYear = false
				this.aniMaskShow = false
				this.$nextTick(() => {
					setTimeout(() => {
						this.show = false
						this.$emit('close')
					}, 300)
				})
			},
			/**
			 * 确认按钮
			 */
			confirm() {
				this.setEmit('confirm')
				this.close()
			},
			/**
			 * 变化触发
			 */
			change() {
				if (!this.insert) return
				this.setEmit('change')
			},

			/**
			 * 派发事件
			 * @param {Object} name
			 */
			setEmit(name) {
				// 月份选择(单选)
				if (this.type == 'yearMonth') {
					this.$emit(name, this.checkedYearMonthList.length ? this.checkedYearMonthList.map(e => e.year +
						"-" + e
						.month)[0] : this.nowDate.thisYear + "-" + this.nowDate.thisMonth)
					if (this.checkedYearMonthList.length < 1) {
						this.checkedYearMonthList = [{
							year: this.nowDate.thisYear,
							month: this.nowDate.thisMonth
						}]
					}
					return
				}
				// 月份选择(多选)
				if (this.type == 'yearMonths') {
					this.$emit(name, this.checkedYearMonthList.length ? this.checkedYearMonthList.map(e => e.year +
						"-" + e
						.month) : [this.nowDate.thisYear + "-" + this.nowDate.thisMonth])
					if (this.checkedYearMonthList.length < 1) {
						this.checkedYearMonthList = [{
							year: this.nowDate.thisYear,
							month: this.nowDate.thisMonth
						}]
					}
					return
				}
				// 月份范围选择 
				if (this.type == 'yearMonthRange') {
					if (this.checkedYearMonthList.length == 2) {
						this.$emit(name, this.checkedYearMonthList.length == 2 ? this.checkedYearMonthList.map(e => e
							.year +
							"-" + e
							.month) : [this.nowDate.thisYear + "-" + this.nowDate.thisMonth])
					}
					return
				}
				// 年份选择(单选)
				if (this.type == 'year') {
					this.$emit(name, this.checkedYearList.length ? this.checkedYearList[0] : this.nowDate.thisYear)
					if (this.checkedYearList.length < 1) {
						this.checkedYearList = [this.nowDate.thisYear]
					}
					return
				}
				// 年份选择(多选)
				if (this.type == 'years') {
					this.$emit(name, this.checkedYearList.length ? this.checkedYearList : [this.nowDate.thisYear])
					if (this.checkedYearList.length < 1) {
						this.checkedYearList = [this.nowDate.thisYear]
					}
					return
				}
				let {
					year,
					month,
					date,
					fullDate,
					extraInfo
				} = this.calendar
				this.$emit(name, {
					// range: this.cale.multipleStatus,
					year,
					month,
					date,
					time: this.time,
					week: this.week,
					timeRange: this.timeRange,
					fulldate: fullDate,
					extraInfo: extraInfo || {}
				})
			},
			// 选择年份
			changeYears(type) {
				if (this.type == 'year' || this.type == 'years') {
					if (type === 'pre') {
						this.nowDate.year = Number(this.nowDate.year) - 10
					} else if (type === 'next') {
						this.nowDate.year = Number(this.nowDate.year) + 10
					}
					this.initYearList(this.nowDate.year)
				} else if (this.monthElectionYear) {
					if (type === 'pre') {
						this.nowDate.startYear = Number(this.nowDate.startYear) - 10
						this.nowDate.endYear = Number(this.nowDate.endYear) - 10
						this.initYearList(this.nowDate.startYear)
					} else if (type === 'next') {
						this.nowDate.startYear = Number(this.nowDate.startYear) + 10
						this.nowDate.endYear = Number(this.nowDate.endYear) + 10
						this.initYearList(this.nowDate.startYear)
					}
					// 年份判断
				} else if (this.type == 'yearMonth' || this.type == 'yearMonths' || this.type == 'yearMonthRange') {
					if (type === 'pre') {
						this.nowDate.year = Number(this.nowDate.year) - 1
					} else if (type === 'next') {
						this.nowDate.year = Number(this.nowDate.year) + 1
					}
				}
			},
			changeYear(year) {
				if (this.type == 'year') {
					this.checkedYearList = [year]
				} else if (this.type == 'years') {
					if (this.checkedYearList.includes(year)) {
						const index = this.checkedYearList.findIndex(e => e == year)
						this.checkedYearList.splice(index, 1)
					} else {
						this.checkedYearList.push(year)
					}
				} else if (this.monthElectionYear) {
					this.checkedYearList = [year]
					this.nowDate.year = year
					// this.nowDate.fullDate = year + "-" + this.nowDate.month + "-" + this.nowDate.date
					this.monthElectionYear = false
					this.showConfirm = true
				}
			},
			changeYearMonth(month) {
				if (this.type == 'yearMonth') {
					this.checkedYearMonthList = [{
						year: this.nowDate.year,
						month: month
					}]
				} else if (this.type == 'yearMonths') {
					if (this.checkedYearMonthList.filter(e => e.year == this.nowDate.year && e.month == month)
						.length) {
						const index = this.checkedYearMonthList.findIndex(e => e.year == this.nowDate.year && e
							.month ==
							month)
						this.checkedYearMonthList.splice(index, 1)
					} else {
						this.checkedYearMonthList.push({
							year: this.nowDate.year,
							month: month
						})
					}
				} else if (this.type == 'yearMonthRange') {
					if (this.checkedYearMonthList.length == 2) {
						this.checkedYearMonthList = []
						this.checkedYearMonthList.push({
							year: this.nowDate.year,
							month: month
						})
					} else {
						if (this.checkedYearMonthList.length == 1) {
							let beginDateStr = this.checkedYearMonthList[0].year + '-' + this.checkedYearMonthList[0].month
							if (beginDateStr < this.nowDate.year + '-' + month) {
								this.checkedYearMonthList.push({
									year: this.nowDate.year,
									month: month
								})
							} else if (beginDateStr > this.nowDate.year + '-' + month) {
								this.checkedYearMonthList.unshift({
									year: this.nowDate.year,
									month: month
								})
							}
						} else {
							this.checkedYearMonthList.push({
								year: this.nowDate.year,
								month: month
							})
						}
					}
				}
			},
		}
	}
</script>

<style lang="scss">
	$uni-primary: #007aff !default;

	.uni-calendar {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
	}

	.uni-calendar__mask {
		position: fixed;
		bottom: 0;
		top: 0;
		left: 0;
		right: 0;
		background-color: rgba(0, 0, 0, 0.4);
		transition-property: opacity;
		transition-duration: 0.3s;
		opacity: 0;
		/* #ifndef APP-NVUE */
		z-index: 99;
		/* #endif */
	}

	.uni-calendar--mask-show {
		opacity: 1
	}

	.uni-calendar--fixed {
		position: fixed;
		bottom: calc(var(--window-bottom));
		left: 0;
		right: 0;
		transition-property: transform;
		transition-duration: 0.3s;
		transform: translateY(460px);
		/* #ifndef APP-NVUE */
		z-index: 99;
		/* #endif */
	}

	.uni-calendar--ani-show {
		transform: translateY(0);
	}

	.uni-calendar__content {
		background-color: #fff;
	}

	.uni-calendar__content-mobile {
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
		box-shadow: 0px 0px 5px 3px rgba(0, 0, 0, 0.1);
	}

	.uni-calendar__header {
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: center;
		align-items: center;
		height: 50px;
	}

	.uni-calendar__header-mobile {
		padding: 10px;
		padding-bottom: 0;
	}

	.uni-calendar--fixed-top {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		justify-content: space-between;
		border-top-color: rgba(0, 0, 0, 0.4);
		border-top-style: solid;
		border-top-width: 1px;
	}

	.uni-calendar--fixed-width {
		width: 50px;
	}

	.uni-calendar__backtoday {
		position: absolute;
		right: 0;
		top: 25rpx;
		padding: 0 5px;
		padding-left: 10px;
		height: 25px;
		line-height: 25px;
		font-size: 12px;
		border-top-left-radius: 25px;
		border-bottom-left-radius: 25px;
		color: #fff;
		background-color: #f1f1f1;
	}

	.uni-calendar__header-text {
		text-align: center;
		max-width: 150px;
		font-size: 15px;
		color: #666;
	}

	.uni-calendar__button-text {
		text-align: center;
		width: 100px;
		font-size: 14px;
		color: $uni-primary;
		/* #ifndef APP-NVUE */
		letter-spacing: 3px;
		/* #endif */
	}

	.uni-calendar__header-btn-box {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		justify-content: center;
		width: 50px;
		height: 50px;
	}

	.uni-calendar__header-btn {
		width: 9px;
		height: 9px;
		border-left-color: #808080;
		border-left-style: solid;
		border-left-width: 1px;
		border-top-color: #555555;
		border-top-style: solid;
		border-top-width: 1px;
	}

	.uni-calendar--left {
		transform: rotate(-45deg);
	}

	.uni-calendar--right {
		transform: rotate(135deg);
	}


	.uni-calendar__weeks {
		position: relative;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
	}

	.uni-calendar__weeks-item {
		flex: 1;
	}

	.uni-calendar__weeks-day {
		flex: 1;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: column;
		justify-content: center;
		align-items: center;
		height: 40px;
		border-bottom-color: #F5F5F5;
		border-bottom-style: solid;
		border-bottom-width: 1px;
	}

	.uni-calendar__weeks-day-text {
		font-size: 12px;
		color: #B2B2B2;
	}

	.uni-calendar__box {
		position: relative;
		// padding: 0 10px;
		padding-bottom: 7px;
	}

	.uni-calendar__box-bg {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
	}

	.uni-calendar__box-bg-text {
		font-size: 200px;
		font-weight: bold;
		color: #999;
		opacity: 0.1;
		text-align: center;
		/* #ifndef APP-NVUE */
		line-height: 1;
		/* #endif */
	}

	.uni-date-changed {
		padding: 0 10px;
		// line-height: 50px;
		text-align: center;
		color: #333;
		border-top-color: #DCDCDC;
		;
		border-top-style: solid;
		border-top-width: 1px;
		flex: 1;
	}

	.uni-date-btn--ok {
		padding: 20px 15px;
	}

	.uni-date-changed--time-start {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
	}

	.uni-date-changed--time-end {
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		align-items: center;
	}

	.uni-date-changed--time-date {
		color: #999;
		line-height: 50px;
		/* #ifdef MP-TOUTIAO */
		font-size: 16px;
		/* #endif */
		margin-right: 5px;
		// opacity: 0.6;
	}

	.time-picker-style {
		// width: 62px;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		justify-content: center;
		align-items: center
	}

	.mr-10 {
		margin-right: 10px;
	}

	.dialog-close {
		position: absolute;
		top: 0;
		right: 0;
		bottom: 0;
		/* #ifndef APP-NVUE */
		display: flex;
		/* #endif */
		flex-direction: row;
		align-items: center;
		padding: 0 25px;
		margin-top: 10px;
	}

	.dialog-close-plus {
		width: 16px;
		height: 2px;
		background-color: #737987;
		border-radius: 2px;
		transform: rotate(45deg);
	}

	.dialog-close-rotate {
		position: absolute;
		transform: rotate(-45deg);
	}

	.uni-datetime-picker--btn {
		border-radius: 100px;
		height: 40px;
		line-height: 40px;
		background-color: $uni-primary;
		color: #fff;
		font-size: 16px;
		letter-spacing: 2px;
	}

	/* #ifndef APP-NVUE */
	.uni-datetime-picker--btn:active {
		opacity: 0.7;
	}

	/* #endif */
	.uni-year-month-item-checked-text {
		color: #0076f5 !important;
	}

	.uni-calendar-item-this-year {
		color: #007aff4d;
	}

	.uni-calendar-item--multiple {
		background-color: #F6F7FC;
	}

	.uni-calendar-item--multiple-left {
		border-radius: 50px 0px 0px 50px;
		background-color: rgba(0, 122, 255, 0.5);
		color: #fff !important;
	}

	.uni-calendar-item--multiple-right {
		border-radius: 0px 50px 50px 0px;
		background-color: rgba(0, 122, 255, 0.5);
		color: #fff !important;
	}
</style>