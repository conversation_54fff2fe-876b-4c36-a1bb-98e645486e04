
## 注意事项

该插件目录下关于IOS屏幕共享的产物包括：NERTCUniPluginSDK/ios/Plugins目录、mobileprovision文件、ios-extension.josn

这些产物都是都是云信sdk测试demo使用的，放在这里仅仅是为了给开发者参考，并不能直接使用（配置内容都已经修改，无法直接使用）

1. IOS屏幕共享的扩展存放路径为：NERTCUniPluginSDK/ios/Plugins中，目前存放的扩展为云信SDK自己测试demo的，需要开发者按照云信官方文档的步骤换成自己的
2. IOS屏幕共享扩展对应的描述文件（即当前目录下mobileprovision文件）以及ios-extension.josn配置，都需要换成开发自己的，不能直接使用
3. 注意需要更换package.json中的app Group的值（com.apple.security.application-groups）