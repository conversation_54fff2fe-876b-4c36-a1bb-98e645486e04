<template>
	<custom-header style="height: 88rpx;" title="报表" showBack />
	<view class="equip_content">

		<view class="tab_cont">	
		<view class="tab_border">		
    <up-tabs
        ref="upTabs"
        :list="list4"
        lineWidth="0"
        :current="currentTab"
        lineColor="#177DDC"
        :activeStyle="{
            color: '#177DDC',
            // fontWeight: 'bold',
            transform: 'scale(1.05)'
        }"
        :inactiveStyle="{
            color: 'rgba(255,255,255,0.45)',
            transform: 'scale(1)'
        }"
		@change="handleTabChange"
        itemStyle="padding:10rpx 15px; padding-right: 15px; height: 34px;"
    >
    </up-tabs>
	</view>
	<view class="de_con">
		<template v-if="currentTab == 0">
			<dayreport-vue></dayreport-vue>
		</template>
		<template v-if="currentTab == 1">
			<weekreport-vue></weekreport-vue>
		</template>
		<template v-if="currentTab == 2">
			<monthreport-vue></monthreport-vue>
		</template>
	</view>
	</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request'
	import customHeader from '@/components/page/header.vue'
	import dayreportVue from './report/dayreport.vue'
	import weekreportVue from './report/weekreport.vue'
	import monthreportVue from './report/monthreport.vue'
	// import siteCont from './site.vue'
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
			dayreportVue,
			weekreportVue,
			monthreportVue
			// siteCont
		},
		data(){
			return{
				currentTab:0,
				id: null,
			list4:[  
			    // { name: '其他信息' },  
			    { name: '日报表' },  
			    { name: '周报表' },  
			    { name: '月报表' },  
			    // { name: '设备寿命跟踪' },  
			    // { name: '设备转移记录' }
			],
			prolist:{
				// taskCode: "CAIMIAN001",
				// taskName: "1号采面",				
				// status: 1,				
				// createdAt: '2023-07-01',				
				// curatorName: "张三",				
			}
			}
		},
		
		onShow(){
			if (this.id) {
				// this.handelDetail()
			}
		},
		methods:{
			handleTabChange(index) {
				this.currentTab = index.index;
				// 如果切换到钻场标签，加载全部数据
				if (this.currentTab === 1) {
					this.$nextTick(() => {
						if (this.$refs.siteCont) {
							this.$refs.siteCont.isFromRoadway = false;
							this.$refs.siteCont.id = null;
							this.$refs.siteCont.handelDrill();
						}
					});
				}
			},
			// 切换到钻场标签并加载数据
			// switchToSite(id) {
			// 	console.log('切换到钻场标签，id:', id);
			// 	this.currentTab = 1; // 切换到钻场标签
			// 	// 等待DOM更新后再调用子组件方法
			// 	this.$nextTick(() => {
			// 		console.log('开始调用siteCont的initWithId方法');
			// 		if (this.$refs.siteCont) {
			// 			console.log('siteCont组件存在，调用initWithId');
			// 			this.$refs.siteCont.initWithId(id);
			// 		} else {
			// 			console.error('siteCont组件不存在');
			// 		}
			// 	});
			// },
			
					
		}
	}
</script>
<style>
	page {
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page {
		background: #16171b;
	}
	.equip_content{
		// padding:0 32rpx ;
		padding-top: 156rpx;
	}
	.equip_header{
		border: 1rpx solid rgba(255, 255, 255, 0.0972);
		margin-top: 30rpx;
		// padding: 32rpx;
		border-radius: 12rpx;
		background: rgba(255, 255, 255, 0.04);
	}
	.header_text{
		font-family: PingFang SC;
		font-size: 32rpx;
		font-weight: 500;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		color: rgba(255, 255, 255, 0.85);
		margin-bottom: 12rpx;
	}
	:deep(.u-tabs__wrapper__nav){
		justify-content: space-around !important;
	}
.u-tabs__wrapper__nav__line{
	transform: translate(100px) !important;
}
	.tab_cont{
		border-radius: 12rpx;
		// padding: 0 32rpx;
		// margin-top: 32rpx;
		// height: 680rpx;
		// background: rgba(255, 255, 255, 0.04);
	}
	.tab_border{
		border-radius: 12rpx  12rpx 0 0;
		// padding:0 32rpx;
		// border: 1rpx solid rgba(255, 255, 255, 0.0972);
	}
	.de_con{
		padding: 0;
		
	}
	.content{
		border-radius:  0 0 12rpx  12rpx;
		// border: 1rpx solid rgba(255, 255, 255, 0.0972);
		// padding: 32rpx;
		// height: 365rpx;
	}
	.headerfl{
		display: flex;
	}
	.header_flex{
		flex:1;
		// margin-right: 138rpx;
		// display: flex;
		// justify-content: space-between;
		// view{
		// 	flex:1;
		// }
	}
	.flex_label{
		margin-top: 32rpx;
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.45);
	}
	.flex_value{
		font-family: PingFang SC;
		font-size: 28rpx;
		font-weight: normal;
		line-height: 44rpx;
		letter-spacing: 0px;
		font-variation-settings: "opsz" auto;
		/* White/45% */
		color: rgba(255, 255, 255, 0.85);
	}
	.cont_flex{
		display: flex;
		margin-bottom: 30rpx;
		justify-content: space-between;
	}
	.label {
			font-size: 28rpx;
			color: rgba(255, 255, 255, 0.8);
		}
	
		.value {
			font-size: 26rpx;
			color: rgba(255, 255, 255, 0.8);
			// font-weight: bold;
		}
</style>