<template>
	<view class="container">
		<image class="background-image" src="/static/image/BG.png" mode="aspectFill"
			:style="{ width: screenWidth + 'px', height: screenHeight + 'px' }" />
		<view class="content-wrapper">
			<view class="header" background="transparent" style="color: #fff;"> <text
					style=" font-size: 34rpx;color: rgba(255, 255, 255, 0.85);"></text></view>
			<scroll-view class="content" scroll-y="true" @scrolltolower="loadMore" @refresherrefresh="onRefresh"
				@refresherpulling="onPulling" :refresher-enabled="true" :refresher-triggered="isRefreshing"
				refresher-background="rgba(0,0,0,0)" :refresher-threshold="30" refresher-default-style="black">

				<!-- 筛选栏 -->
				<view class="filter-wrapper">
					<up-input class="sear_inp" placeholder="搜索代办名称" prefixIcon="search" v-model="searchText"
						:clearable="true" @change="handleSearchInput" @clear="handleClear" color="rgba(255,255,255,0.8)"
						prefixIconStyle="font-size: 22px;color: #909399"></up-input>
				</view>

				<view v-if="todoList.length != 0">
					<view class="todo-item" v-for="(item, index) in todoList" :key="index">
						<template v-if="item.type == 2">
							<view class="item-wrapper">
								<!-- 上部分 -->
								<view class="item-header">
									<view class="header-left">
										<image class="item-icon" style="background: #CAE4FF;"
											src="/static/image/ding.png" mode="aspectFit"></image>
									</view>
									<view class="header-middle">
										<text class="item-title">{{ item.title }}</text>
									</view>
									<view class="header-right">
										<text class="item-time">{{ formatTime(item.createdAt) }}</text>
									</view>
								</view>

								<!-- 中间部分 -->
								<view class="item-content">
									<view class="content-row">
										<!-- <text class="label">任务名称：</text> -->
										<text class="value">{{ formatDynamicFieldss(item.dynamicFields) }}</text>
									</view>
									<view class="content-row">
										<text class="label">通知时间：{{ item.createdAt }}</text>
										<text class="value"></text>
									</view>
								</view>

								<!-- 底部操作栏 -->
								<view v-if="item.title == '云平台交接班提醒'" class="item-footer" >
									<template v-if="item.status === 0">
										<text @click="handleTourClick(item)" class="btn-approve">去处理</text>
									</template>
									<template v-else>
										<text class="btn-processed">已处理</text>
									</template>
								</view>
								<view v-else-if="item.title == '云平台终孔提醒'" class="item-footer" >
									<template v-if="item.status === 0">
										<text @click="handleEndCheck(item)" class="btn-approve">确认查收</text>
									</template>
									<template v-else>
										<text class="btn-processed">已查收</text>
									</template>
								</view>
								<view v-else class="item-footer" >
									<template v-if="item.status === 0">
										<text class="btn-approve" @click="handleCheck(item)">确认查收</text>
									</template>
									<template v-else>
										<text class="btn-processed">已查收</text>
									</template>
								</view>
							</view>
						</template>
						<template v-else>
							<view class="item-wrapper" >
								<view  @click="handleItemClick(item)">
								<!-- 上部分 -->
								<view class="item-header">
									<view class="header-left">
										<image class="item-icon" src="/static/image/commission.png" mode="aspectFit">
										</image>
									</view>
									<view class="header-middle">
										<text class="item-title">{{ item.title }}</text>
									</view>
									<view class="header-right">
										<text class="item-time">{{ formatTime(item.createdAt) }}</text>
									</view>
								</view>

								<!-- 中间部分 -->
								<view class="item-content">
									<view class="content-row">
										<!-- <text class="label">任务名称：</text> -->
										<text class="value">{{ formatDynamicFields(item.dynamicFields) }}</text>
									</view>
									<view class="content-row">
										<text class="label">任务下发时间：{{ item.createdAt }}</text>
										<text class="value"></text>
									</view>
								</view>
								</view>
								<!-- 底部操作栏 -->
								<view class="item-footer">
									<template v-if="item.status === 1">
										<text class="btn-processed">已同意</text>
									</template>
									<template v-if="item.status === 2">
										<text class="btn-processed">已拒绝</text>
									</template>
									<template v-if="item.status === 0">
										<text class="btn-reject" @tap.stop="showRejectDialog(item)">拒绝</text>
										<text class="divider">|</text>
										<text class="btn-approve" @tap.stop="handleApprove(item)">同意</text>
									</template>
								</view>
							</view>
						</template>
						
						<!-- 拒绝原因弹框 -->
						<u-modal :show="showRefusePopup" :title="'拒绝原因'" :content="''" :showCancelButton="true"
							:closeOnClickOverlay="true" @close="dialogClose" @cancel="dialogClose" @confirm="dialogConfirm(item)">
							<view class="modal-content">
								<u-textarea v-model="refuseReason" placeholder="输入拒绝原因" :maxlength="200" :autoHeight="true"
									:customStyle="{
									borderRadius: '8rpx',
								}"></u-textarea>
							</view>
						</u-modal>
					</view>
				</view>

				<!-- 空状态 -->
				<view v-else class="empty-state">
					<u-empty mode="data" text="暂无待办事项"></u-empty>
				</view>
				<!-- 加载更多 -->
				<view class="load-more" v-if="todoList.length > 0">
					<u-loadmore bgColor="transparent" :status="loadMoreStatus" />
				</view>
			</scroll-view>
		</view>
	</view>
</template>

<script>
	import Request from '@/components/utils/request.js'
	import customHeader from '@/components/page/header.vue'
	import { 
		connectPrivateServer,
		disconnectMqtt, 
		publishMessage, 
		onMessageReceived, 
		isConnected 
	} from '@/utils/mqtt-helper.js'
	
	export default {
		inheritAttrs: false,
		components: {
			customHeader,
		},
		data() {
			return {
				value1: 0,
				value2: 1,
				title1: '全部',
				title2: '待处理',
				options1: [{
						name: '全部',
						id: 0
					},
					{
						name: '审批',
						id: 1
					},
					{
						name: '通知',
						id: 2
					},
				],
				options2: [{
						label: '待处理',
						value: 1
					},
					{
						label: '已处理',
						value: 2
					},
				],
				screenWidth: 0,
				screenHeight: 0,
				todoList: [],
				searchText: '',
				currentPage: 1,
				pageSize: 10,
				total: 2,
				isRefreshing: false,
				loadMoreStatus: 'nomore',
				isPulling: false,
				showRefusePopup: false,
				refuseReason: '',
				currentItem: null,
				originalList: [], // 用于存储原始数据
				dropdownStyle: {
					backgroundColor: '#000',
					color: '#fff',
					boxShadow: 'none'
				},
				// 轮询相关配置 - 移除
				// pollingInterval: null, // 轮询定时器
				// pollingTime: 5000, // 轮询间隔，30秒
				userInfo: null,
				// MQTT相关配置
				mqttClient: null,
				activeTopic: '/sys/todo/update',
			}
		},
		onShow() {
			// 页面显示时获取列表
			this.currentPage = 1;
			this.getTodoList();
			
			// 获取用户信息
			this.getUserInfo();
			
			// 开始连接MQTT
			this.connectMQTT();
		},

		onHide() {
			// 断开MQTT连接
			this.disconnectMQTT();
		},

		onUnload() {
			// 页面卸载时断开MQTT连接
			this.disconnectMQTT();
		},

		created() {
			// 获取屏幕尺寸
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.windowWidth
			this.screenHeight = systemInfo.windowHeight
		},
		methods: {
			// 连接MQTT
			connectMQTT() {
				console.log('开始连接MQTT...');
				
				// 连接到MQTT服务器
				this.mqttClient = connectPrivateServer(this.activeTopic);
				
				if (this.mqttClient) {
					this._setupMqttEventHandlers();
				}
			},
			
			// 设置MQTT事件处理器
			_setupMqttEventHandlers() {
				if (!this.mqttClient) return;
				
				// 连接成功回调
				this.mqttClient.on('connect', () => {
					console.log('MQTT连接成功，已订阅主题:', this.activeTopic);
				});
				
				// 消息接收回调
				onMessageReceived((topic, message) => {
					console.log('收到MQTT消息:', topic);
					try {
						// 尝试解析消息
						const msgData = JSON.parse(message.toString());
						console.log('解析后的消息数据:', msgData);
						
						// 判断是否为数组,并且本地用户账号是否在数组中
						if (Array.isArray(msgData) && this.userInfo && this.userInfo.account) {
							const userAccount = this.userInfo.account;
							console.log('当前用户账号:', userAccount);
							
							// 判断用户账号是否在推送的数组中
							if (msgData.includes(userAccount)) {
								console.log('待办页面：当前用户在推送列表中，刷新待办列表');
								// 刷新待办列表
								this.currentPage = 1;
								this.getTodoList();
							}
						}
					} catch (e) {
						console.error('MQTT消息解析错误:', e);
					}
				});
				
				// 错误回调
				this.mqttClient.on('error', (error) => {
					console.error('MQTT连接错误:', error.message || '未知错误');
				});
				
				// 断开连接回调
				this.mqttClient.on('close', () => {
					console.log('MQTT连接已断开');
				});
			},
			
			// 断开MQTT连接
			disconnectMQTT() {
				console.log('断开MQTT连接');
				disconnectMqtt();
				this.mqttClient = null;
			},
			
			// 获取用户信息
			getUserInfo() {
				try {
					const userInfoStr = uni.getStorageSync('user');
					if (userInfoStr) {
						// 判断是否已经是对象
						if (typeof userInfoStr === 'object') {
							this.userInfo = userInfoStr;
						} else {
							// 尝试解析JSON字符串
							this.userInfo = JSON.parse(userInfoStr);
						}
						console.log('获取到用户信息:', this.userInfo);
					} else {
						console.log('未找到用户信息');
					}
				} catch (e) {
					console.error('获取用户信息失败:', e);
					// 如果解析失败，可能本地存储了一个对象而非字符串，直接使用
					try {
						const rawUserInfo = uni.getStorageSync('user');
						if (rawUserInfo && typeof rawUserInfo === 'object') {
							this.userInfo = rawUserInfo;
							console.log('直接使用对象类型的用户信息');
						}
					} catch (e2) {
						console.error('无法获取用户信息:', e2);
					}
				}
			},
			
			// 获取待办事项列表
			async getTodoList() {
				try {
					const params = {
						page: this.currentPage,
						perPage: this.pageSize,
						status: this.value2,
						type: this.value1,
						keyWord: this.searchText
					};
					const res = await Request.post('/todo/get_ls', params);
					console.log('获取待办列表响应:', res.data.total);
					
					if (res.status === 0) {
						if (this.currentPage === 1) {
							this.todoList = res.data.items;
						} else {
							this.todoList = [...this.todoList, ...res.data.items];
						}
						this.total = res.data.total;
						
						// 更新角标 - 获取未处理的数量
						const unhandledCount = res.data.items.filter(item => item.status === 0).length;
						console.log('待办页面未处理数量:', unhandledCount);
						
						if (unhandledCount > 0) {
							uni.setTabBarBadge({
								index: 2,
								text: unhandledCount.toString(),
								complete: (res) => {
									console.log('设置角标结果:', res);
								}
							});
						} else {
							uni.removeTabBarBadge({
								index: 2,
								complete: (res) => {
									console.log('移除角标结果:', res);
								}
							});
						}
						
						this.loadMoreStatus = this.todoList.length >= this.total ? 'nomore' : 'loadmore';
					} else {
						uni.showToast({
							title: res.message || '获取数据失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('获取待办列表失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				} finally {
					this.isRefreshing = false;
				}
			},
			
			// 以下方法保持不变
			formatDynamicFields(dynamicFields) {
				try {
					const fields = JSON.parse(dynamicFields);
					const operatorField = fields.find(field => field.title === '操作员');
					return operatorField ? `${operatorField.title}：${operatorField.value}` : '无操作员信息';
				} catch (error) {
					console.error('解析 dynamicFields 失败:', error);
					return '动态信息解析失败';
				}
			},
			
			formatDynamicFieldss(dynamicFields) {
				try {
					const fields = JSON.parse(dynamicFields);
					return fields.map(field => `${field.title}：${field.value}`).join('，');
				} catch (error) {
					console.error('解析 dynamicFields 失败:', error);
					return '动态信息解析失败';
				}
			},
			
			handleSearchInput(e) {
				console.log('选择的值:', e);
				if (this.searchTimer) {
					clearTimeout(this.searchTimer);
				}

				this.searchTimer = setTimeout(() => {
					this.searchText = e;
					this.currentPage = 1;
					this.getTodoList();
				}, 300);
			},

			handleClear() {
				this.searchText = '';
				this.currentPage = 1;
				this.getTodoList();
			},
			
			handleTourClick(item) {
				uni.navigateTo({
					url: `/pages/commission/components/tour?id=${item.id}&&submitter=${item.submitter}`
				});
			},
			
			handleItemClick(item) {
				uni.navigateTo({
					url: `/pages/commission/components/detail?id=${item.id}`
				});
			},

			showRejectDialog(item) {
				this.currentItem = item;
				this.showRefusePopup = true;
			},

			async handleApprove(item) {
				uni.showModal({
					title: '确认',
					content: '确定同意该申请吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								const params = {
									id: item.id,
									action: 1
								};
								const res = await Request.post('/todo/post_modify', params);
								if (res.status === 0) {
									uni.showToast({
										title: '处理成功',
										icon: 'none'
									});
									this.getTodoList();
								} else {
									uni.showToast({
										title: res.msg || '处理失败',
										icon: 'none'
									});
								}
							} catch (error) {
								console.error('处理同意操作失败:', error);
								uni.showToast({
									title: '网络错误，请重试',
									icon: 'none'
								});
							}
						}
					}
				});
			},
			
			async handleEndCheck(item) {
				uni.showModal({
					title: '确认',
					content: '请前往pc端查看终孔报告单',
					success: async (res) => {
						if (res.confirm) {
							try {
								const params = {
									id: item.id,
									action: 1
								};
								const res = await Request.post('/todo/post_modify', params);
								if (res.status === 0) {
									uni.showToast({
										title: '已查收',
										icon: 'none'
									});
									this.getTodoList();
								} else {
									uni.showToast({
										title: res.message || '处理失败',
										icon: 'none'
									});
								}
							} catch (error) {
								console.error('处理同意操作失败:', error);
								uni.showToast({
									title: '网络错误，请重试',
									icon: 'none'
								});
							}
						}
					}
				});
			},
			
			async handleCheck(item) {
				uni.showModal({
					title: '确认',
					content: '确定查收该通知吗？',
					success: async (res) => {
						if (res.confirm) {
							try {
								const params = {
									id: item.id,
									action: 1
								};
								const res = await Request.post('/todo/post_modify', params);
								if (res.status === 0) {
									uni.showToast({
										title: '已查收',
										icon: 'none'
									});
									this.getTodoList();
								} else {
									uni.showToast({
										title: res.message || '处理失败',
										icon: 'none'
									});
								}
							} catch (error) {
								console.error('处理同意操作失败:', error);
								uni.showToast({
									title: '网络错误，请重试',
									icon: 'none'
								});
							}
						}
					}
				});
			},
			
			dialogClose() {
				this.showRefusePopup = false;
				this.refuseReason = '';
				this.currentItem = null;
			},

			async dialogConfirm(item) {
				try {
					if (!this.refuseReason.trim()) {
						uni.showToast({
							title: '请输入拒绝原因',
							icon: 'none'
						});
						return;
					}
					const params = {
						id: item.id,
						refuse: this.refuseReason,
						action: 2
					};
					const res = await Request.post('/todo/post_modify', params);
					if (res.status === 0) {
						uni.showToast({
							title: '已处理',
							icon: 'none'
						});
						this.getTodoList();
					} else {
						uni.showToast({
							title: res.message || '处理失败',
							icon: 'none'
						});
					}
				} catch (error) {
					console.error('处理拒绝操作失败:', error);
					uni.showToast({
						title: '网络错误，请重试',
						icon: 'none'
					});
				}
				
				if (item) {
					item.status = 2;
				}

				this.dialogClose();
			},

			formatTime(timestamp) {
				if (!timestamp) return '';
				const time = new Date(timestamp).getTime();
				if (isNaN(time)) return '';

				const now = Date.now();
				const diff = now - time;

				if (diff < 60 * 1000) {
					return '刚刚';
				}
				if (diff < 60 * 60 * 1000) {
					return Math.floor(diff / (60 * 1000)) + '分钟前';
				}
				if (diff < 24 * 60 * 60 * 1000) {
					return Math.floor(diff / (60 * 60 * 1000)) + '小时前';
				}
				return Math.floor(diff / (24 * 60 * 60 * 1000)) + '天前';
			},

			loadMore() {
				if (this.loadMoreStatus === 'nomore') return;
				this.currentPage++;
				this.getTodoList();
			},

			onRefresh() {
				this.isRefreshing = true;
				this.currentPage = 1;
				this.getTodoList();
				setTimeout(() => {
					this.isRefreshing = false;
				}, 1000);
			},

			onPulling(e) {
				this.isPulling = true;
			},

			filterData() {
				// 重置为原始数据
				this.todoList = [...this.originalList];

				// 进行筛选
				const filteredList = this.todoList.filter(item => {
					const typeMatch = this.value1 === '' || item.type === this.value1;
					const statusMatch = this.value2 === '' || item.status === this.value2;
					const searchMatch = !this.searchText ||
						item.title.includes(this.searchText) ||
						item.submitter.includes(this.searchText) ||
						item.content.includes(this.searchText);
					return typeMatch && statusMatch && searchMatch;
				});

				this.todoList = filteredList;
				this.total = filteredList.length;
				this.loadMoreStatus = 'nomore';
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		flex: 1;
		position: relative;
	}

	.background-image {
		position: absolute;
		top: 0;
		left: 0;
	}

	.content-wrapper {
		padding-left: 26rpx;
		
		padding-right: 26rpx;
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		flex: 1;
	}

	.header {
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		padding-top: 50rpx;
		// height: 160rpx;
		font-size: 34rpx;
		font-weight: 600;
		text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;

		// background-color: rgba(255, 255, 255, 0.03);
	}


	.content {
		// padding-top: 32rpx;

		// padding-left: 26rpx;

		// padding-right: 26rpx;
	}

	.status-bar {
		height: 44px;
	}

	.sear_inp {
		// flex:1;
		/* margin-right: 10rpx; */
		border: 0px solid #fff !important;
		background: rgba(255, 255, 255, 0.08);
		color: rgba(255, 255, 255, 0.8);
	}

	.u-border {
		border: none;
	}

	.filter_icon {
		height: 35px;
		width: 28px;
		display: flex;
		justify-content: center;
		align-items: center;
		/* padding: 25rpx 14rpx;*/
		border-radius: 10rpx;
		background: rgba(255, 255, 255, 0.08);
	}

	.filter_icon image {

		width: 28rpx;
		height: 28rpx;

	}

	.nav-bar {
		height: 44px;
		justify-content: center;
		align-items: center;
	}

	.nav-title {
		font-size: 34rpx;
		color: rgba(255, 255, 255, 0.85);
	}

	.filter-wrapper {
		// position: fixed; 
		/* top: 88px; */
		padding: 0 10rpx;

		/* height: 100rpx; */
		// width: 100%;
		// background-color: #fff;
		z-index: 9999;
	}

	/* dropdown样式 */
	.u-dropdown {
		flex: 1;
	}
:deep(.u-select__label){
	flex-direction: row;
	height: 100%;
}
	:deep(.u-dropdown__content) {
		border-radius: 20rpx !important;

		// background-color: none;
		.u-dropdown__content__mask {
			background: none;
		}

		.u-dropdown__content__popup {
			background: #000;
		}
	}

	:deep(.uni-modal) {
		background: #16171b;
		color: rgba(255, 255, 255, 0.85);
	}

	:deep(.u-dropdown-item__scroll) {
		background: #28292e;
		color: rgba(255, 255, 255, 0.85);
	}

	:deep(.u-dropdown__content__popup) {
		margin-top: 5rpx;
		border-radius: 10rpx !important;
		// color: rgba(255, 255, 255, 0.85) !important;
	}

	:deep(.u-dropdown__menu__item__text) {

		color: rgba(255, 255, 255, 0.85) !important;
	}

	:deep(.u-line) {
		border: none !important;
		// border-bottom:1rpx solid rgb(214, 215, 217,0.5) !important;
	}

	:deep(.u-cell) {
		border-bottom: 1rpx solid rgb(214, 215, 217, 0.2) !important;
		// border-bottom:none !important;

	}

	:deep(.u-cell:last-child) {
		border-bottom: 0rpx solid rgb(214, 215, 217, 0.5) !important;
		// border-bottom:none !important;
	}

	.u-dropdown__menu {
		height: 100rpx;
		background-color: rgba(240, 240, 240, 0.08);
	}

	.u-dropdown__menu__item {
		height: 100rpx;
		justify-content: center;
		align-items: center;
	}

	.u-dropdown__menu__item__text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.85);
	}

	.u-dropdown__content {
		background-color: rgba(22, 23, 27, 0.95);
	}

	.u-select__list__item {
		padding: 24rpx 32rpx;
		background-color: rgba(0, 0, 0, 0.75);
		border-bottom-width: 1rpx;
		border-bottom-style: solid;
		border-bottom-color: rgba(255, 255, 255, 0.08);
	}

	:deep(.u-popup__content) {
		//background-color: #000;
	}

	:deep(.u-modal) {
		//background-color: #16171b;
		border: 1rpx solid rgba(255, 255, 255, 0.1) !important;

		.u-modal__title {
			padding: 32rpx;
			font-size: 32rpx;
			font-weight: 500;
			color: rgba(255, 255, 255, 0.75) !important;
		}

		.u-modal__content {
			// padding: 24rpx;
			// padding-right: 0;
			display: block;

			.modal-content {
				.u-textarea {
					//background: rgba(0, 0, 0, 0.15);
					border-radius: 8rpx;
					border: 1rpx solid rgba(255, 255, 255, 0.1) !important;

					// padding: 16rpx;
					// width: calc(100% - 60rpx);
					min-height: 200rpx !important;

					&__field {
						font-size: 28rpx;
						// color: rgba(255, 255, 255, 0.8) !important;
						line-height: 1.5;
					}
				}
			}

		}

		.u-line {
			border-color: rgba(255, 255, 255, 0.25) !important;
		}

		.u-modal__button-group__wrapper {
			//background-color: #16171b;
		}
	}

	.u-select__list__item:last-child {
		border-bottom-width: 0;
	}

	.u-select__list__item__text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.85);
	}

	.u-select__list__item--active {
		background-color: rgba(255, 255, 255, 0.08);
	}

	.u-select__list__item--active .u-select__list__item__text {
		color: #177DDC;
	}

	/* 内容区域调整上边距，避免被筛选栏遮挡 */
	.content {
		 padding-top: 150rpx;
		 z-index: 500; 
		 flex: 1; 
	}

	.todo-item {
		margin: 0 10rpx;
		margin-top: 32rpx;
		border-radius: 12rpx;
		background-color: rgba(255, 255, 255, 0.0362);
		border-width: 1rpx;
		border-style: solid;
		border-color: rgba(255, 255, 255, 0.0972);
	}

	.item-wrapper {
		padding: 32rpx;
		padding-bottom: 0;
	}

	.item-header {
		flex-direction: row;
		align-items: center;
		margin-bottom: 24rpx;
	}

	.header-left {
		margin-right: 16rpx;
	}

	.item-icon {
		width: 48rpx;
		height: 48rpx;
		border-radius: 8rpx;
		background-color: #F8CF8D;
		padding: 8rpx;
	}

	.header-middle {
		flex: 1;
	}

	.item-title {
		font-size: 32rpx;
		color: rgba(255, 255, 255, 0.85);
		lines: 2;
		text-overflow: ellipsis;
	}

	.item-time {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.45);
	}

	.item-content {
		// margin-bottom: 24rpx;
	}

	.content-row {
		// flex-direction: row;
		margin-bottom: 16rpx;
	}

	.label {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.65);
	}

	.value {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.65);
		// flex: 1;
	}

	.item-footer {
		flex-direction: row;
		justify-content: center;
		align-items: center;
		margin-top: 24rpx;
		border-top-width: 1rpx;
		border-top-style: solid;
		border-top-color: rgba(255, 255, 255, 0.08);
		// padding-top: 24rpx;
	}

	.btn-reject {
		flex: 1;
		text-align: center;
		padding:24rpx 0;
		font-size: 28rpx;
		justify-content: center;
		align-items: center;
		color: rgba(255, 255, 255, 0.75);
	}

	.divider {
		color: rgba(255, 255, 255, 0.08);
		margin: 0 32rpx;
	}

	.btn-approve {
		flex: 1;
		justify-content: center;
		padding:24rpx 0;
		// padding-bottom: 32rpx;
		align-items: center;
		text-align: center;
		font-size: 28rpx;
		color: #177DDC;
	}

	.btn-processed {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.45);
	}

	.empty-state {
		padding: 120rpx 0;
		align-items: center;
		justify-content: center;
	}

	.load-more {
		padding: 32rpx 0;
		align-items: center;
	}

	.modal-content {
		/* padding: 32rpx; */
		width: 100%;
	}

	.filter-container {
		display: flex;
		flex-direction: row;
		align-items: center;
		margin-top: 20rpx;
	}

	.filter-container .up-dropdown {
		flex: 1;
		margin-right: 20rpx;
	}

	.up-dropdown-item {
		background: rgba(255, 255, 255, 0.08);
		border-radius: 12rpx;
		height: 56rpx;
		line-height: 56rpx;
		padding: 0 20rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.up-dropdown-item__title {
		color: rgba(255, 255, 255, 0.8);
		font-size: 28rpx;
	}

	.up-dropdown-item__options {
		background: rgba(22, 23, 27, 0.95);
		border-radius: 12rpx;
		margin-top: 10rpx;
	}

	.up-dropdown-item__option {
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 20rpx;
		color: rgba(255, 255, 255, 0.8);
		font-size: 28rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.08);
	}

	.up-dropdown-item__option--active {
		color: #177DDC;
		background: rgba(255, 255, 255, 0.08);
	}

	.select-item {
		flex: 1;
		padding-right: 10rpx;
		border-radius: 12rpx;
		margin-right: 15rpx;
		color: rgba(255, 255, 255, 0.8);
		background: rgba(255, 255, 255, 0.08);
		display: flex;
		align-items: center;

		:deep(.uni-data-select) {
			flex: 1;

			.uni-select {
				border: none !important;
				background: transparent !important;

				.uni-select__input-box {
					height: 70rpx;
					border: none !important;
					padding: 0 32rpx;
					background: transparent !important;

					.uni-select__input-text {
						color: rgba(255, 255, 255, 0.85) !important;
						font-size: 28rpx;
					}

					.uni-select__input-placeholder {
						color: rgba(255, 255, 255, 0.45) !important;
						font-size: 28rpx;
					}
				}
			}

			.uni-select__selector {
				background-color: rgba(0, 0, 0, 0.85) !important;
				border: none !important;
				padding: 0;
				margin-top: 2px;
				border-radius: 0 !important;

				&::before {
					display: none !important;
				}

				.uni-select__selector-item {
					padding: 20rpx 32rpx;
					color: rgba(255, 255, 255, 0.85) !important;
					font-size: 28rpx;
					border: none !important;
					background: transparent !important;

					&.active {
						color: #177DDC !important;
					}

					&:hover {
						background-color: rgba(255, 255, 255, 0.08) !important;
					}
				}
			}
		}
	}

	.select-item {
		flex: 1;
		margin-right: 20rpx;
	}

	.select-item:last-child {
		margin-right: 10rpx;
	}

	.zxz-uni-data-select {
		background: rgba(255, 255, 255, 0.08);
		border-radius: 12rpx;
		height: 56rpx;
		line-height: 56rpx;
		padding: 0 20rpx;
		color: rgba(255, 255, 255, 0.8);
	}

	.zxz-uni-data-select__input {
		color: rgba(255, 255, 255, 0.8);
		font-size: 28rpx;
	}

	.zxz-uni-data-select__list {
		background: rgba(22, 23, 27, 0.95);
		border-radius: 12rpx;
		margin-top: 10rpx;
	}

	.zxz-uni-data-select__item {
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 20rpx;
		color: rgba(255, 255, 255, 0.8);
		font-size: 28rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.08);
	}

	.zxz-uni-data-select__item--active {
		color: #177DDC;
		background: rgba(255, 255, 255, 0.08);
	}

	.custom-select {
		background: rgba(255, 255, 255, 0.08);
		border-radius: 12rpx;
		color: rgba(255, 255, 255, 0.85);
		font-size: 28rpx;
		height: 70rpx;
		line-height: 70rpx;
		padding: 0 32rpx;
	}

	.custom-select ::v-deep .uni-select__input-box {
		background: transparent !important;
		border: none !important;
		color: rgba(255, 255, 255, 0.85) !important;
	}

	.custom-select ::v-deep .uni-select__input-placeholder {
		color: rgba(255, 255, 255, 0.45) !important;
	}

	.custom-select ::v-deep .uni-select__selector {
		background-color: rgba(0, 0, 0, 0.85) !important;
		border: none !important;
	}

	.custom-select ::v-deep .uni-select__selector-item {
		color: rgba(255, 255, 255, 0.85) !important;
		font-size: 28rpx;
		padding: 20rpx 32rpx;
	}

	.custom-select ::v-deep .uni-select__selector-item.active {
		color: #177DDC !important;
	}

	.picker {
		height: 70rpx;
		line-height: 70rpx;
		padding: 0 32rpx;
		color: rgba(255, 255, 255, 0.85);
		font-size: 28rpx;
		background: rgba(255, 255, 255, 0.08);
		border-radius: 12rpx;
	}

	.custom-picker {
		height: 70rpx;
		line-height: 70rpx;
		padding: 0 32rpx;
		color: rgba(255, 255, 255, 0.85);
		font-size: 28rpx;
		background: rgba(255, 255, 255, 0.08);
		border-radius: 12rpx;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.arrow-icon {
		width: 24rpx;
		height: 24rpx;
	}

	.picker-options-container {
		position: absolute;
		top: 80rpx;
		left: 0;
		// width: 100%;
		z-index: 999;
		color: rgba(255, 255, 255, 0.85);
		background: #fff;
	}

	.picker-options {
		width: 100%;
		max-height: 300rpx;
		background: rgba(22, 23, 27, 0.95);
		border-radius: 12rpx;
		box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
	}

	.custom-picker-option {
		height: 80rpx;
		line-height: 80rpx;
		padding: 0 32rpx;
		color: rgba(255, 255, 255, 0.85);
		font-size: 28rpx;
		border-bottom: 1rpx solid rgba(255, 255, 255, 0.08);
	}

	.custom-picker-option:last-child {
		border-bottom: none;
	}

	.custom-picker-option:hover {
		background: rgba(255, 255, 255, 0.08);
	}

	.mask {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background: rgba(0, 0, 0, 0.5);
		z-index: 998;
	}
</style>