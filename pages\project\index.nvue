<template>
		<view class="container">
			<image 
				class="background-image" 
				src="/static/image/BG.png" 
				mode="aspectFill" 
				:style="{ width: screenWidth + 'px', height: screenHeight + 'px' }"
			/>
			<view class="content-wrapper">
				<view class="header" :style="{ paddingTop: statusBarHeight + 'px' }">
					<view class="header-left">
						<text class="hi-text">Hi, </text>
						<text class="name-text">{{ userNameSuffix }}</text>
					</view>
					<view class="header-right">
						<view v-if="userAvatar" class="avatar-container">
							<image 
								class="avatar" 
								:src="userAvatar"
								mode="aspectFill"
							/>
						</view>
						<view v-else class="avatar-placeholder">
							<text class="avatar-text">{{ userNameSuffix }}</text>
						</view>
					</view>
				</view>

				<view class="welcome-container">
					<text class="welcome-text">欢迎瓦斯治理信息化系统</text>
				</view>

				<view class="tabs-container">
					<u-tabs
						v-if="tabsList.length > 0"
						:list="tabsList"
						:current="currentTab"
						@click="handleTabClick"
						itemStyle="height: 80rpx; padding: 0 32rpx;"
						lineColor="rgba(255, 255, 255, 0.85)"
						:activeStyle="{
							color: 'rgba(255, 255, 255, 0.85)',
							fontWeight: 'bold'
						}"
						:inactiveStyle="{
							color: 'rgba(255, 255, 255, 0.65)'
						}"
						
						:scrollable="true"
					></u-tabs>
				</view>

				<view class="null_device" v-if="deviceData==''">
					<text class="null_device">暂无设备</text>
				</view>
				<scroll-view 
					v-else
					class="device-list"
					scroll-y="true"
					:refresher-enabled="true"
					@refresherrefresh="onRefresh"
					:refresher-triggered="isRefreshing"
				>
					<view class="device-grid">
						<view 
							v-for="(device, index) in deviceData" 
							:key="index"
							class="device-item"
							@click="handleDevice(device)"
						>
						    <view style="display: flex;flex-direction:row;justify-content: end;margin-bottom: 8rpx;">
						    	<text style="text-align: right;" class="info-value">{{device.deviceModel}}</text>
						    </view>
							<image class="device-image" src="@/static/image/apply/element.png" mode="aspectFit"/>
							<text class="device-name">{{device.deviceName}}</text>
							<view class="device-info">
								<text class="info-label">运行动态：</text>
								<text class="info-value">{{ device.workState === 1 ? '开机' : '关机' }}</text>
							</view>
							<view class="device-info">
								<text class="info-label">工作模式：</text>
								<text class="info-value">{{ device.workMode === 0 ? '空闲' : '工作中' }}</text>
							</view>
						</view>
					</view>
				</scroll-view>
			</view>
		</view>
	
</template>

<script>
	//import customHeader from '@/components/page/header.vue'
	import Request from '@/components/utils/request';
	export default {
		inheritAttrs: false,
		

		data() {
          
			return {
				deviceData: [],
				page: 1,
				perPage: 5,
				screenWidth: 0,
				screenHeight: 0,
				isRefreshing: false,
				loading: false,
				hasMore: true,
				userNameSuffix: '',
				userAvatar: '',
				currentTab: 0,
				tabsList: [
					
				],
				statusBarHeight: 20,
				modelData: [],
			}
		},
		async created() {
			// Get screen dimensions
			const systemInfo = uni.getSystemInfoSync()
			this.screenWidth = systemInfo.windowWidth
			this.screenHeight = systemInfo.windowHeight
			this.statusBarHeight = systemInfo.statusBarHeight
			
			// Get user info from storage
			this.getUserInfo()
			// Get model data
			await this.getModelList()
		},
		onLoad() {
			// this.getDeviceList();
		},
		onShow() {
			this.getDeviceList()
		},
		methods: {
			handleDevice(device){
				uni.navigateTo({
					url: `/pages/project/detail/index?deviceCode=${device.deviceCode}`
				});
			},
			async initData() {
				await this.getModelList()
				this.getDeviceList()
			},
			async getDeviceList(params = {}) {
				if (this.loading) return;
				this.loading = true;
				
				try {
					const defaultParams = {
						page: this.page,
						perPage: this.perPage,
						offlineState: 1
					}

					const requestParams = { ...defaultParams, ...params }
					console.log('最终请求参数:', requestParams)
					
					const res = await Request.post('/device/get_ls', requestParams)
					if (res.status === 0) {
						this.deviceData = res.data.items || []
						this.hasMore = res.data.hasMore || false
					} else {
						console.error('获取设备列表失败:', res.message)
					}
				} catch (error) {
					console.error('请求出错:', error)
				} finally {
					this.loading = false
				}
			},
			// 添加下拉刷新方法
					async onRefresh() {
						this.isRefreshing = true;
						
						try {
							await Promise.all([
								this.getModelList(),
								this.getDeviceList()
							]);
							
							// 提示刷新成功
							uni.showToast({
								title: '刷新成功',
								icon: 'none',
								duration: 1000
							});
						} catch (error) {
							console.error('刷新失败:', error);
							uni.showToast({
								title: '刷新失败',
								icon: 'none',
								duration: 1000
							});
						} finally {
							// 停止刷新动画
							this.isRefreshing = false;
						}
					},
			async getModelList() {
				try {
					const res = await Request.get('/model/attribute/get_all_model')
					console.log('model数据:', res.data) // 打印接口返回数据

					if (res.status === 0 && Array.isArray(res.data)) {
						// 初始化全部设备选项
						const allDevices = { name: '全部设备', id:0 }
						
						// 处理模型数据
						const modelTabs = res.data.map(item => {
							if (typeof item === 'object') {
								return {
									name:  item.model || '',
									id: item.id || ''
								}
							}
							return {
								name: item,
								id: item
							}
						}).filter(item => item.name) // 过滤掉无效数据

						// 设置tabs数据
						this.tabsList = [allDevices, ...modelTabs]
						console.log('处理后的tabs数据:', this.tabsList)
					}
				} catch (error) {
					console.error('获取设备型号列表失败:', error)
					uni.showToast({
						title: '获取设备型号失败',
						icon: 'none'
					})
				}
			},
			handleTabClick(item) {
				console.log('点击的tab项:', item)
				const index = this.tabsList.findIndex(tab => tab.name === item.name)
				if (index > -1) {
					this.currentTab = index
					
					// 构建请求参数
					const params = {
						page: 1,
						perPage: this.perPage,
						offlineState: 1
					}

					// 如果不是全部设备，添加deviceModelID
					if (index > 0 && item.id) {
						params.deviceModelID = item.id
					}

					console.log('请求参数:', params)
					this.getDeviceList(params)
				}
			},
			getUserInfo() {
				try {
					const userInfo = uni.getStorageSync('user')
					if (userInfo) {
						// 获取用户名的最后两个字符
						this.userNameSuffix = userInfo.name.slice(-2)
						this.userAvatar = userInfo.profilePic
					}
				} catch (error) {
					console.error('获取用户信息失败:', error)
				}
			}
		},
		
	
	}
</script>

<style>
	page{
		background: #16171b;
	}
</style>
<style lang="scss" scoped>
	page{
		background: #16171b;
	}
	.container {
		flex: 1;
		position: relative;
		background: #16171b;
	}
	.background-image {
		position: absolute;
		top: 0;
		left: 0;
	}
	
	.content-wrapper {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		flex: 1;
	}
	
	.cont {
		display: flex;
		// align-items: center;
		justify-content: center;
	}

	.de_con {
		padding: 0 32rpx;
		padding-top: 16rpx;
		// background: rgba(255, 255, 255, 0.04);
	}
    .header {
		margin-top: 88rpx;
    	padding-left: 32rpx;
    	padding-right: 32rpx;
    	padding-bottom: 32rpx;
    	flex-direction: row;
    	justify-content: space-between;
    	align-items: center;
    	// background-color: rgba(255, 255, 255, 0.03);
    }
	.header-left {
		flex-direction: row;
		align-items: center;
	}
	.header-right{
		padding: 4rpx;
		height: 80rpx;
		width: 80rpx;
		border-radius: 12rpx;
		background: #5492F7;
	}
	.hi-text {
		font-size: 36rpx;
		color: rgba(255, 255, 255, 0.46);
	}
	.name-text {
		font-size: 36rpx;
		color: rgba(255, 255, 255, 0.46);
		margin-left: 8rpx;
	}
	.avatar {
		width: 72rpx;
		height: 72rpx;
		border-radius: 12rpx;
	}
	:deep(.u-tabs__wrapper) {
		flex: 1;
		width: 100%;
	}
	:deep(.u-tabs){
		width: 100vw;
	}
	.tabs-container {
		width: 100vw;
		// background-color: rgba(255, 255, 255, 0.03);
		
		:deep(.u-tabs__wrapper) {
			// flex: 1;
			width: 100%;
		}
		
		:deep(.u-tabs__wrapper__nav__item) {
			flex: none;
			padding: 0 32rpx;
		}
	}
	.tab-list {
		flex-direction: row;
		padding: 16rpx 32rpx;
	}
	.tab-content {
		flex-direction: row;
	}
	.tab-item {
		padding: 16rpx 32rpx;
		margin-right: 16rpx;
		border-radius: 8rpx;
		background-color: rgba(255, 255, 255, 0.04);
	}
	.tab-item-active {
		background-color: rgba(255, 255, 255, 0.1);
	}
	.tab-text {
		font-size: 28rpx;
		color: rgba(255, 255, 255, 0.6);
	}
	.tab-text-active {
		color: rgba(255, 255, 255, 0.85);
	}
	.content {
		// padding: 0 32rpx;
		// height: calc(100vh - 188rpx);
		padding-top: 32rpx;
			padding-left: 32rpx;
			padding-right: 32rpx;

		/* 自定义下拉刷新样式 */
		:deep(.uni-scroll-view-refresher) {
			width: 100% !important;
			height: 20px;
			background: #16171b;
			display: flex;
			justify-content: center;
			align-items: center;

			/* 隐藏默认图标 */
			.uni-scroll-view-refresher__indicator-box {
				display: none;
			}

			/* 自定义文本 */
			.uni-scroll-view-refresher__indicator {
				&::before {
					content: '加载中';
					color: rgba(255, 255, 255, 0.8);
					font-size: 14px;
				}
			}
		}

		/* 自定义刷新文本样式 */
		.refresh-text {
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			height: 30px;
			display: flex;
			align-items: center;
			justify-content: center;
			color: rgba(255, 255, 255, 0.8);
			font-size: 14px;
			background: #16171b;
			z-index: 100;
		}

		/* 隐藏默认的刷新图标 */
		:deep(.uni-scroll-view-refresher) {
			.uni-scroll-view-refresher__indicator-box {
				opacity: 0;
			}
		}
	}

	.phone_cont{
		padding:32rpx;
		// margin-top: 32rpx;
		border-radius:12rpx;
		background: rgba(255, 255, 255, 0.04);
		border: 0.5px solid rgba(255, 255, 255, 0.0972);
	}
	.phone_bet{
		display: flex;
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
	}
	.phone_title{
		color: rgba(255, 255, 255, 0.85);
		font-size: 34rpx;
		line-height: 44rpx;
	}
	.phone_value{
		color: rgba(255, 255, 255, 0.85);
		font-size: 30rpx;
		line-height: 44rpx;
	}
	.phone_btn{
		margin-top: 32rpx;
		border: 1px solid rgba(255, 255, 255, 0.2);
		background: rgba(255, 255, 255, 0);
		color: rgba(255, 255, 255, 0.85);
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 28rpx;
		padding: 16rpx;
	}
	.phone_btn_image{
	
				margin-right: 10rpx;
				width: 40rpx;
				height: 40rpx;
		
	}
	.phone_green {
		font-size: 28rpx;
		border-radius: 12rpx;
		padding: 12rpx 20rpx;
		&.normal {
			color: #00B042;
			background: rgba(0, 176, 66, 0.12);
		}
		&.abnormal {
			color: #FF4D4F;
			background: rgba(255, 77, 79, 0.12);
		}
	}

	.no-data {
		text-align: center;
		color: rgba(255, 255, 255, 0.6);
		font-size: 32rpx;
		padding: 64rpx 0;
	}

	.no-more {
		text-align: center;
		color: rgba(255, 255, 255, 0.6);
		font-size: 28rpx;
		padding: 32rpx 0;
	}

	.welcome-container {
		padding: 0rpx 32rpx;
		padding-bottom: 60rpx;
	}

	.welcome-text {
		font-size: 52rpx;
		color: rgba(255, 255, 255, 0.85);
		// font-weight: bold;
	}
    .null_device{
		margin-top: 80rpx;
		color: rgba(255,255,255,0.65);
		font-size: 32rpx;
		text-align: center;
	}
	.device-list {
		flex: 1;
		// padding: 32rpx;
	}

	.device-grid {
		flex-direction: row;
		flex-wrap: wrap;
		justify-content: space-between;
	}

	.device-item {
		width: 340rpx;
		// height: 280rpx;
		
		background-color: rgba(255, 255, 255, 0.04);
		box-shadow: 0px 4px 10px 0px rgba(0, 0, 0, 0.1);
		border-radius: 20rpx;
		margin: 20rpx 32rpx;
		margin-bottom: 20rpx;
		padding: 20rpx;
	}

	.device-image {
		width: 200rpx;
		height: 92rpx;
		// margin-bottom: 24rpx;
	}

	.device-name {
		font-size: 34rpx;
		color: rgba(255,255,255,0.85);
		margin: 28rpx 0;
	}

	.device-info {
		flex-direction: row;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 8rpx;
	}

	.info-label {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.45);
		
	}

	.info-value {
		font-size: 24rpx;
		color: rgba(255, 255, 255, 0.65);
	}

	.avatar-container {
		width: 100%;
		height: 100%;
	}

	.avatar {
		width: 72rpx;
		height: 72rpx;
		border-radius: 12rpx;
	}

	.avatar-placeholder {
		width: 100%;
		height: 100%;
		display: flex;
		align-items: center;
		justify-content: center;
	}

	.avatar-text {
		color: rgba(255,255,255,0.85);
		font-size: 28rpx;
	}
</style>