<template>
	<view style="height: 1200rpx; ">
		<view style="">
			<l-echart ref="chartUncompleted"></l-echart>
		</view>
		<view style="">
			<l-echart ref="chartCompleted"></l-echart>
		</view>
	</view>
</template>

<script>
	import * as echarts from 'echarts';
	export default {
		props: {
			chartData: {
				type: Array,
				default: () => []
			}
		},
		data() {
			return {}
		},
		mounted() {
			this.initUncompletedChart();
			this.initCompletedChart();
			console.log('111111111',this.chartData);
		},
		methods: {
			initUncompletedChart() {
				this.$refs.chartUncompleted.init(echarts, chart => {
					// 获取未完成的数量
					const uncompletedValue = this.chartData[1].value;
					let option = {
						title: {
							text: `未完成\n${uncompletedValue}个`,
							left: "center",
							top: "43%",
							textStyle: {
							           color: 'rgba(255,255,255,0.85)'
							         }
						},
						series: [{
							type: 'pie',
							avoidLabelOverlap: false,
							radius: ['40%', '70%'],
							data: this.chartData,
							color: ['#c2c2c2', '#eab308'],
							labelLine: {
								show: false
							}
						}]
					}
					chart.setOption(option);
				});
			},
			initCompletedChart() {
				this.$refs.chartCompleted.init(echarts, chart => {
					// 获取已完成的数量
					const completedValue = this.chartData[0].value;
					let option = {
						title: {
							text: `已完成\n${completedValue}个`,
							left: "center",
							top: "43%",
							 textStyle: {
							            color: 'rgba(255,255,255,0.85)'
							          }
						},
						series: [{
							type: 'pie',
							avoidLabelOverlap: false,
							radius: ['40%', '70%'],
							data: this.chartData,
							color: ['#22c55e','#c2c2c2'],
							labelLine: {
								show: false
							}
						}]
					}
					chart.setOption(option);
				});
			}
		}
	}
</script>