<template>
	<view class="container">
		<image 
			class="background-image" 
			src="/static/image/BG.png" 
			mode="aspectFill" 
			:style="{ width: screenWidth + 'px', height: screenHeight + 'px' }"
		/>
		<view class="content-wrapper">
			<view 
				class="header"
				background="transparent"
				style="color: #fff;"
			> 
				<text style="font-size: 34rpx;color: rgba(255, 255, 255, 0.85);">云平台</text>
			</view>
			<scroll-view
				class="content" 
				id="scrollView"
				scroll-y="true"
				@scrolltolower="loadMore"
				@refresherrefresh="onRefresh"
				@refresherpulling="onPulling"
				@scroll="onScroll"
				:refresher-enabled="enableRefresh"
				:refresher-triggered="isRefreshing"
				refresher-background="rgba(0,0,0,0)"
				:refresher-threshold="30"
				refresher-default-style="black"
			>
				<!-- 最上方数据显示 -->			
				<view class="data_header">
					<view class="data-items" style="margin-right: 32rpx;">
						<text class="data-title">总进尺</text>
						<text class="data-value">{{ footage }}米</text>
					</view>
					<view class="data-items">
						<text class="data-title">运行钻机数</text>
						<text class="data-value">{{ drillNum }}台</text>
					</view>
				</view>
				<!-- 圆环饼图 -->
				<view class="echarts-container">
					<view class="e_title">
						钻机部件健康状态
					</view>
					<view style="width: 100%; height: 408px;">
					    <l-echart ref="chartRef" @finished="init"></l-echart>
					</view>
					<!-- <pie-chart :chartData="chartData" /> -->
					<!-- <npie-chart :chartData="chartData" /> -->
					 <!-- <canvas id="myCanvas" canvas-id="myCanvas" style="width: 300px; height: 300px;"></canvas> -->
				</view>
				<!-- 中间数据 -->
				<view class="data_header">
					<view class="data-items" style="margin-right: 32rpx;">
						<text class="data-title">总任务数</text>
						<text class="data-value">{{ planNum }}</text>
					</view>
					<view class="data-items">
						<text class="data-title">环境异常</text>
						<text class="data-value" style="color: #ff6d37">{{ environmental }}</text>
					</view>
				</view>
				<view class="data_header" >
					<view class="data-items" style="margin-right: 32rpx;">
						<text class="data-title">事件异常</text>
						<text class="data-value" style="color: #ff9100;">{{ exception }}</text>
					</view>
					<view class="data-items">
						<text class="data-title">环境异常</text>
						<text class="data-value" style="color: #00b142">{{ task }}</text>
					</view>
				</view>
				<!-- <view  class="empty-state">
					<u-empty mode="data" text="暂无数据"></u-empty>
				</view> -->
			</scroll-view>
		</view>
	</view>
</template>

<script>
	// import * as echarts from 'echarts';
	// import * as echarts from 'echarts/dist/echarts.esm.js'
	// import uCharts from '@qiun/ucharts';
	// import PieChart from './components/pie.vue';
	// import NpieChart from './components/npie.nvue';
	export default {
		components: {
			// PieChart,
			// NpieChart
		},
		
		data() {
			return {
				screenWidth: 0,
				screenHeight: 0,
				isRefreshing: false,
				isAtTop: true,
				scrollTop: 0,
				footage:'2458.2',
				drillNum:'12',
				planNum:'166',
				environmental:'12',
				exception:'20',
				task:'82',
				chart: null,
				dataList: [],
				enableRefresh: true,

				chartData: [
					{ label: '正常', value: 40 },
					{ label: '良好', value: 20 },
					{ label: '异常', value: 20 },
					
				]
			};
		},
		mounted() {
			const info = uni.getSystemInfoSync();
			this.screenWidth = info.windowWidth;
			this.screenHeight = info.windowHeight;
		},
		methods: {
			onScroll(e) {
				this.scrollTop = e.detail.scrollTop;
				this.isAtTop = this.scrollTop <= 5;
				this.enableRefresh = this.isAtTop;
			},
			
			onPulling(e) {
				if (!this.isAtTop) {
					this.stopPullDownRefresh();
				}
			},
			
			onRefresh() {
				if (!this.isAtTop) {
					this.stopPullDownRefresh();
					return;
				}
				
				console.log('执行刷新操作');
				setTimeout(() => {
					this.isRefreshing = false;
				}, 1500);
			},
			
			stopPullDownRefresh() {
				this.isRefreshing = false;
			},
			
			loadMore() {
				console.log('加载更多数据');
			},
			
			init() {
			}
		}
	};
</script>

<style lang="scss" scoped>
.container {
		flex: 1;
		position: relative;
	}
.background-image {
	position: absolute;
	top: 0;
	left: 0;
}
.content-wrapper {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		flex: 1;
	}
	
	
	.header {
		padding-top: 80rpx;
		height: 160rpx;
		font-size: 34rpx;
		font-weight: 600;
	    text-align: center;
		display: flex;
		justify-content: center;
		align-items: center;
		
		background-color: rgba(255,255,255,0.03);
	}
.content {
	padding: 32rpx;
}
.data_header{
	display: flex;
	flex-flow: row;
	margin-top: 32rpx;
	justify-content: space-between;
  
}


.data-items {
	flex:1;
	padding: 32rpx;
	// color: #fff;
	border-radius: 12rpx;
	background: rgba(255, 255, 255, 0.04);
	border: 0.5px solid rgba(255, 255, 255, 0.0972);
}

.data-title{
	color: rgba(255, 255, 255, 0.45);
	font-size: 30rpx;
	font-weight: normal;
	line-height: 44rpx;
	margin-bottom: 10rpx;
}
.data-value{
	color: rgba(255, 255, 255, 0.85);
	font-size: 40rpx;
	font-weight: 500;
	line-height: 44rpx;
}
.empty-state {
	padding: 120rpx 0;
	align-items: center;
	justify-content: center;
}
.echarts-container {
	margin-top: 32rpx ;
	padding: 32rpx;
	
	background: rgba(255, 255, 255, 0.04);
	border-radius: 12rpx;
	border: 0.5px solid rgba(255, 255, 255, 0.0972);
}
.e_title{
	font-size: 32rpx;
	font-weight: normal;
	line-height: 44rpx;
	letter-spacing: 0px;
	margin-bottom: 32rpx;
	color: #9E9E9E;
}
.chart-data {
	display: flex;
	justify-content: space-around;
	margin-top: 32rpx;
}
.data-item {
	display: flex;
	align-items: center;
}
.color-block {
	width: 16rpx;
	height: 16rpx;
	border-radius: 4rpx;
	margin-right: 8rpx;
}
.data-label {
	color: rgba(255, 255, 255, 0.85);
	font-size: 24rpx;
}
</style>