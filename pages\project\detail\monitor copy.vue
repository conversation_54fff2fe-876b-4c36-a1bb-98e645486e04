<template>
  <view class="monitor-container">
    <!-- <view class="header">
      <view class="back-btn">
        <text class="back-icon">←</text>
      </view>
      <text class="title">视频监控</text>
    </view> -->
    
    <view class="video-wrapper">
      <!-- <video 
        id="videoPlayer"
        class="video-player"
        :src="videoUrl"
        :poster="posterUrl"
        controls
        :autoplay="false"
        object-fit="contain"
        codec="hardware"
      ></video> -->
       <yingbing-video 
	   autoplay
	   src="http://121.40.164.65:10000/proxy/sms/local/hls/ts/00000000000000012345_34020000001320005555/ts-playlist.m3u8?expired=20250727101530"
	   
	   >
          </yingbing-video>
     <!-- <view v-if="!isPlaying && !isLoading" class="play-overlay" @click="startPlay">
        <view class="play-button">
          <text class="play-icon">▶</text>
        </view>
        <text class="play-text">点击播放视频</text>
      </view> -->
    </view>

    <view class="loading-overlay" v-if="isLoading">
      <view class="spinner"></view>
      <text class="loading-text">视频加载中...</text>
    </view>

    <view class="video-info">
      <text class="video-title">监控视频</text>
      <text class="video-desc">实时监控画面</text>
    </view>
  </view>
</template>

<script>
export default {
  data() {
    return {
      videoUrl: 'https://open.ys7.com/v3/openlive/33010538992687326136:33010215991327468047_1_2.m3u8?expire=1775182511&id=829660643245166592&t=d042112bfe9f95b6f7a4190adbfa0f719f379c208c9421c805dce0a22de08e2a&ev=100&devProto=gb28181',
      posterUrl: '', // 可以设置一个封面图片
      isLoading: false,
      isPlaying: false,
      videoContext: null
    }
  },
  onReady() {
    
  },
  methods: {
   
    }
  }
</script>

<style scoped>
	page{
		background: #16171b;
	}
.monitor-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  /* background-color: #f5f5f5; */
  position: relative;
}

.header {
  display: flex;
  align-items: center;
  height: 90rpx;
  padding: 0 20rpx;
  /* background-color: #ffffff; */
  position: relative;
  padding-top: var(--status-bar-height, 20px);
}

.back-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-icon {
  font-size: 40rpx;
  color: #333;
}

.title {
  flex: 1;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.video-wrapper {
  margin: 20rpx;
  /* background-color: #000; */
  border-radius: 12rpx;
  overflow: hidden;
  height: 420rpx;
  position: relative;
}

.video-player {
  width: 100%;
  height: 100%;
}

.play-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.play-button {
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  /* background-color: rgba(255, 255, 255, 0.2); */
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20rpx;
  border: 4rpx solid #fff;
}

.play-icon {
  color: #fff;
  font-size: 50rpx;
  line-height: 1;
  margin-left: 10rpx; /* 调整播放图标位置 */
}

.play-text {
  color: #ffffff;
  font-size: 28rpx;
}

.loading-overlay {
  position: absolute;
  top: 110rpx; /* 调整位置以覆盖视频区域 */
  left: 20rpx;
  right: 20rpx;
  height: 420rpx;
  background-color: rgba(0, 0, 0, 0.7);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  z-index: 10;
}

.spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #ffffff;
  animation: spin 1s ease-in-out infinite;
}


.loading-text {
  color: #ffffff;
  font-size: 28rpx;
  margin-top: 20rpx;
}

.video-info {
  padding: 20rpx;
  background-color: rgba(255,255,255,0.04);
  margin: 0 20rpx;
  border-radius: 12rpx;
}

.video-title {
  font-size: 32rpx;
  font-weight: bold;
  color: rgba(255,255,255,0.65);
  margin-bottom: 10rpx;
  display: block;
}

.video-desc {
  font-size: 28rpx;
   color: rgba(255,255,255,0.45);
  display: block;
}
</style>